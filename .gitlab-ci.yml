stages:
  - test

build_and_test:
  stage: test
  only:
    - master
  tags:
    - win10
    - "64"
    - 教师
  script:
    # 初始清理阶段 - 改进版
    - cmd /c "if exist C:\plt-test\plt-teacher-server (taskkill /F /IM plt-teacher-server.exe >nul 2>&1 || echo 进程未找到-继续) & ping 127.0.0.1 -n 6 > nul & if exist C:\plt-test\plt-teacher-server (robocopy C:\plt-test\plt-teacher-server\course C:\plt-test\course_temp /MIR >nul & if exist C:\plt-test\plt-teacher-server\hxr.db (copy C:\plt-test\plt-teacher-server\hxr.db C:\plt-test\hxr.db.bak >nul) & rmdir /S /Q C:\plt-test\plt-teacher-server & mkdir C:\plt-test\plt-teacher-server & robocopy C:\plt-test\course_temp C:\plt-test\plt-teacher-server\course /MIR >nul & if exist C:\plt-test\hxr.db.bak (move /Y C:\plt-test\hxr.db.bak C:\plt-test\plt-teacher-server\hxr.db >nul) & rmdir /S /Q C:\plt-test\course_temp)"

    # 1. 复制文件到测试目录
    - cmd /c "xcopy /E /I /Y . C:\plt-test\plt-teacher-server || exit 1"
    
    # 2. 构建项目
    - cmd /c "cd C:\plt-test\plt-teacher-server && cargo build --release || exit 1"
    
    # 3. 确保旧进程已停止
    - cmd /c "cd C:\plt-test\plt-teacher-server && taskkill /F /IM plt-teacher-server.exe >nul 2>&1 || echo 进程未找到-继续"
    - cmd /c "cd C:\plt-test\plt-teacher-server && ping 127.0.0.1 -n 6 > nul"
    
    # 4. 复制可执行文件
    - cmd /c "cd C:\plt-test\plt-teacher-server && if exist target\release\plt-teacher-server.exe (copy /Y target\release\plt-teacher-server.exe .) || exit 1"

  # 5. 启动服务并检查是否成功
    - cmd /c "cd C:\plt-test\plt-teacher-server && start plt-teacher-server.exe || exit 1"
    - cmd /c "cd C:\plt-test\plt-teacher-server && ping 127.0.0.1 -n 6 > nul"
    
    # # 6. 运行k6测试
    # - powershell -Command "& { Set-Location 'C:\plt-test\plt-teacher-server'; k6 run --vus 1 --iterations 1 k6_teacher_api.js; if ($LASTEXITCODE -ne 0) { Write-Host '测试失败，立即终止流水线'; Stop-Process -Name 'plt-teacher-server' -Force -ErrorAction SilentlyContinue; exit 1 } }"
    
    # 7. 最终清理阶段
    - cmd /c "cd C:\plt-test\plt-teacher-server && (taskkill /F /IM plt-teacher-server.exe /T >nul 2>&1 || ( ping 127.0.0.1 -n 3 > nul && taskkill /F /IM plt-teacher-server.exe /T >nul 2>&1 || wmic process where name='plt-teacher-server.exe' call terminate >nul 2>&1 || echo 进程终止跳过) )"
    - cmd /c "cd C:\plt-test\plt-teacher-server && if exist C:\plt-test\plt-teacher-server (robocopy C:\plt-test\plt-teacher-server\course C:\plt-test\course_temp /MIR >nul & if exist C:\plt-test\plt-teacher-server\hxr.db (copy C:\plt-test\plt-teacher-server\hxr.db C:\plt-test\hxr.db.bak >nul) & rmdir /S /Q C:\plt-test\plt-teacher-server & mkdir C:\plt-test\plt-teacher-server & robocopy C:\plt-test\course_temp C:\plt-test\plt-teacher-server\course /MIR >nul & if exist C:\plt-test\hxr.db.bak (move /Y C:\plt-test\hxr.db.bak C:\plt-test\plt-teacher-server\hxr.db >nul) & rmdir /S /Q C:\plt-test\course_temp)"
