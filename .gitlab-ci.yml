# 首先并行的完成所有build任务，然后进行depoly任务
stages:
  - build_docker
  - deploy

api_build_docker:

  # 仅限master, prod分支 
  only:
    - master

  # 阶段为docker镜像制作
  stage: build_docker

  # 使用docker:stable镜像
  image: docker:stable

  # 编译docker镜像
  script:
    - docker build -t registry.i51cy.com:443/hxr-api:$CI_PIPELINE_ID .
    - docker push registry.i51cy.com:443/hxr-api:$CI_PIPELINE_ID

api_build_docker_faye:

  # 仅限faye分支 
  only:
    - faye

  # 阶段为docker镜像制作
  stage: build_docker

  # 使用docker:stable镜像
  image: docker:stable

  # 编译docker镜像
  script:
    - docker build -t registry.i51cy.com:443/hxr-api-faye:$CI_PIPELINE_ID .
    - docker push registry.i51cy.com:443/hxr-api-faye:$CI_PIPELINE_ID
    
api_build_docker_prod:

  # 仅限master, prod分支 
  only:
    - prod

  # 阶段为docker镜像制作
  stage: build_docker

  # 使用docker:stable镜像
  image: docker:stable

  # 编译docker镜像
  script:
    - docker login --username=<EMAIL> --password=yopu1234 registry.cn-hangzhou.aliyuncs.com
    - docker build -t registry.cn-hangzhou.aliyuncs.com/haixr/api:$CI_PIPELINE_ID .
    - docker push registry.cn-hangzhou.aliyuncs.com/haixr/api:$CI_PIPELINE_ID
    
api_deploy_debug:

  # 仅限master分支 
  only:
    - master

  # 阶段为docker镜像制作
  stage: deploy
 
  # 本阶段无需拉取源码
  variables:
    GIT_STRATEGY: none

  # 使用docker:stable镜像
  image: registry.i51cy.com:443/yopu-cluster2-kubectl:latest

  # 编译docker镜像
  script:
    - kubectl -n hxr-dev set image deployment/api api=registry.i51cy.com:443/hxr-api:$CI_PIPELINE_ID

api_deploy_debug_faye:

  # 仅限faye分支 
  only:
    - faye

  # 阶段为docker镜像制作
  stage: deploy
 
  # 本阶段无需拉取源码
  variables:
    GIT_STRATEGY: none

  # 使用docker:stable镜像
  image: registry.i51cy.com:443/yopu-cluster2-kubectl:latest

  # 编译docker镜像
  script:
    - kubectl -n hxr-faye set image deployment/api api=registry.i51cy.com:443/hxr-api-faye:$CI_PIPELINE_ID
