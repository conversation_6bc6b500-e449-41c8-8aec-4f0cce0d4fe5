use std::fs;
use std::path::PathBuf;

fn main() {
  // 自动复制 server.env 到开发目录
  let server_env_source = PathBuf::from("../../plt-teacher-server/server.env");
  let profile = std::env::var("PROFILE").unwrap_or_else(|_| "debug".to_string());
  let server_env_dest = PathBuf::from(format!("target/{}/server.env", profile));
  
  if server_env_source.exists() {
    if let Err(e) = fs::copy(&server_env_source, &server_env_dest) {
      println!("cargo:warning=无法复制 server.env: {}", e);
    } else {
      println!("cargo:warning=已复制 server.env 到 {}", server_env_dest.display());
    }
  } else {
    println!("cargo:warning=找不到 server.env 源文件: {}", server_env_source.display());
  }
  
  tauri_build::build()
}
