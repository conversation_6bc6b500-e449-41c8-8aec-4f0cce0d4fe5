[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "lib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2", features = [] }
tauri-plugin-http = "2"
tauri-plugin-fs = "2"
tauri-plugin-os = "2"
tauri-plugin-process = "2"
tauri-plugin-dialog = "2"
tauri-plugin-persisted-scope = "2"
tauri-plugin-shell = "2"
dotenvy = "0.15"
single-instance = "0.3"
port_check = "0.1.5"
network-interface = "0.1.6"
chrono = "0.4.19"
which = "4.4.2"
winreg = "0.51.0"
url = "2.2.2"
tokio-tungstenite =  "0.18.0"
futures-channel = "0.3"
futures="0.3.25"
futures-util = { version = "0.3", default-features = false, features = ["sink", "std"] }
reqwest = { version = "0.11.27", features = ["blocking", "json", "cookies", "gzip", "brotli", "deflate", "stream"] }

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = [ "custom-protocol" ]
# this feature is used for production builds where `devPath` points to the filesystem
# DO NOT remove this
custom-protocol = [ "tauri/custom-protocol" ]
