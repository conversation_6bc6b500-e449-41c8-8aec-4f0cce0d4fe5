// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

fn main() {
  // 检测操作系统版本
  let is_win7 = is_windows_7();

  // 检测是否存在固定版本的WebView2运行时
  let fixed_webview2_path = "./Microsoft.WebView2.FixedVersionRuntime.109.0.1518.78.x64";

  // 只有在Win7系统且固定版本存在时才设置环境变量
  if is_win7 && std::path::Path::new(fixed_webview2_path).exists() {
    std::env::set_var(
      "WEBVIEW2_BROWSER_EXECUTABLE_FOLDER",
      fixed_webview2_path
    );
  }
  // Win10/11系统即使有固定版本也不设置环境变量，让系统使用默认的WebView2

  app_lib::run();
}

// 检测是否为Windows 7
fn is_windows_7() -> bool {
  #[cfg(target_os = "windows")]
  {
    use winreg::{enums::HKEY_LOCAL_MACHINE, RegKey};

    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    if let Ok(key) = hklm.open_subkey("SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion") {
      if let Ok(version) = key.get_value::<String, _>("CurrentVersion") {
        // Windows 7 的版本号是 6.1
        return version == "6.1";
      }
    }
  }
  false
}
