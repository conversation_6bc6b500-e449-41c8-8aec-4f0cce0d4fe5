#![cfg_attr(
  all(not(debug_assertions), target_os = "windows"),
  windows_subsystem = "windows"
)]

use std::{env, net::SocketAddr, process::{Command, self}, thread::sleep, time::Duration, collections::HashMap, path::PathBuf};
use single_instance::SingleInstance;
use network_interface::{NetworkInterface, NetworkInterfaceConfig};
use winreg::{enums::HKEY_CURRENT_USER, RegKey};

use std::fs;
use std::io;
use std::path::Path;
use std::io::Write;
use chrono::Utc;
use std::fs::OpenOptions;
use dotenvy;

use which::which;

use tauri::State;
use tauri::async_runtime::Mutex;
use tokio_tungstenite::connect_async;
use tokio_tungstenite::tungstenite::{Message};
use tauri::Emitter;
use futures::{StreamExt, SinkExt};

#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;

#[tauri::command]
fn get_server_url() -> String {

  // 获取 PORT 环境变量的值
  let port = match env::var("PORT") {
      Ok(val) => val.parse::<u16>().unwrap_or(46048), // 如果解析失败，使用默认端口
      Err(_) => 46048, // 如果环境变量不存在，使用默认端口
  };

  let server_url = format!("http://127.0.0.1:{}", port);
  server_url.to_owned()
}

#[tauri::command]
fn get_env_var(environment_name: String) -> String {
  // 获取指定环境变量的值
  let env_var = env::var(environment_name);
  if env_var.is_ok() {
    return env_var.unwrap();
  }

  "".to_string()
}

#[tauri::command]
fn get_network_cards_info() -> String {
  // 获取工作网卡信息，用于获取MAC地址和对外展示IP地址
  let nis = match NetworkInterface::show() {
    Ok(r) => r,
    Err(e) => panic!("无法获得网络配置信息，请检查您的系统配置 {}", e.to_string())
  };

  // 寻找正常网卡
  let normal_nis: Vec<HashMap<&str, String>> = nis.into_iter().filter(|n| {
    let addr = n.addr;
    if addr.is_none() {
        return false;
    }

    if n.mac_addr.is_none() {
      return false;
    }

    // 非环回，不是IPV6且有广播地址的网卡
    let addr = addr.unwrap();
    if addr.ip().is_loopback() ||
       addr.ip().is_ipv6() ||
       addr.broadcast().is_none() {
       return false;
    }

    // 判定是否为本地链接地址
    // 将addr转换为IPV4地址
    let ipv4_addr = addr.ip().to_string();
    if ipv4_addr.starts_with("169.254.") {
      return false;
    }

    return true;
  })
  .map(|t| {
    let mut nis_info: HashMap<&str, String> = HashMap::new();
    nis_info.insert("ip", t.addr.unwrap().ip().to_string());
    nis_info.insert("mac", t.mac_addr.unwrap());
    nis_info.insert("name", t.name);

    nis_info
  })
  .collect();

  serde_json::to_string(&normal_nis).unwrap()
}

// 获取EXE路径
pub fn get_path_in_exe_dir(sub_path: &str) -> PathBuf {
  let exe_dir = std::env::current_exe().unwrap();
  let current_path = exe_dir.parent().unwrap();
  current_path.join(sub_path)
}

#[tauri::command]
fn upgrade() -> String {
  if cfg!(debug_assertions) {
    return "调试环境下不执行升级操作".to_string();
  }

  // 执行升级
  let bat_file_path = get_path_in_exe_dir("upgrade.bat");
  let bat_file_path = replace_path_with_blanks(bat_file_path.to_str().unwrap());
  match Command::new("cmd").arg(format!("/c start {}", bat_file_path.as_str())).output() {
      Ok(r) => r,
      Err(e) => {
          return format!("无法执行升级脚本upgrade.bat, {}", e);
      }
  };

  process::exit(0);
}

#[tauri::command]
fn is_software_installed(software_name: &str) -> String {
  if let Ok(_) = which(software_name) {
    return "成功".to_string();
  }

  "失败".to_string()
}

#[tauri::command]
fn get_software_installed(subkey_path: &str) -> String {
  // 打开注册表键
  let hklm = RegKey::predef(HKEY_CURRENT_USER);
  // let subkey_path = r"SOFTWARE\iyopu\模拟训练系统(学生端)";
  let key = hklm.open_subkey(subkey_path);

  match key {
      Ok(regkey) => {
          // 读取安装目录
          match regkey.get_value::<String, _>("InstallDir") {
              Ok(install_dir) => {
                  println!("学生端安装目录：{}", install_dir);
                  write_log(format!("学生端安装目录：{}", install_dir));

                  // 返回安装路径
                  install_dir
              }
              Err(e) => {
                  eprintln!("无法读取安装目录：{}", e);
                  write_log(format!("无法读取安装目录：{}", e));
                  "无法读取安装目录".to_string()
              }
          }
      }
      Err(e) => {
          eprintln!("无法打开注册表键：{}", e);
          write_log(format!("无法打开注册表键：{}", e));
          "无法打开注册表键".to_string()
      }
  }
}

#[tauri::command]
fn open_software(executable_path: String) -> String {

  // 使用 spawn 启动子线程来执行脚本
  std::thread::spawn(move || {
    match std::process::Command::new("cmd")
        .arg(format!("/c start {}", executable_path.as_str()))
        .stdout(std::process::Stdio::null())
        .stderr(std::process::Stdio::null())
        .spawn()
    {
        Ok(mut child) => {
            // 在需要的情况下可以等待子进程退出
            let _ = child.wait();
            write_log("成功启动学生端软件".to_string());
            return "成功".to_string();
        }
        Err(e) => {
            write_log(format!("无法启动学生端软件, {}", e));
            return "启动失败".to_string();
        }
    };
  });

  "成功".to_string()
}

#[tauri::command]
fn get_resource_dir() -> String {
    write_log("获取资源目录开始".to_string());

    let base_dir = env::current_exe().unwrap().parent().unwrap().to_str().unwrap().to_string();

    let canonical_base_dir = fs::canonicalize(base_dir).unwrap();
    let mut canonical_base_dir_str = canonical_base_dir.to_string_lossy().to_string();

    if cfg!(target_os = "windows") {
        if canonical_base_dir_str.starts_with(r"\\?\") {
            canonical_base_dir_str = canonical_base_dir_str[4..].to_string();
        }

        write_log("获取资源目录完毕".to_string());
        return canonical_base_dir_str + "\\";
    }

    write_log("获取资源目录完毕".to_string());

    canonical_base_dir_str + "/"
}

// Copy files from source to destination recursively.
pub fn copy_recursively(source: impl AsRef<Path>, destination: impl AsRef<Path>) -> io::Result<()> {
  fs::create_dir_all(&destination)?;
  for entry in fs::read_dir(source)? {
      let entry = entry?;
      let filetype = entry.file_type()?;
      if filetype.is_dir() {
          copy_recursively(entry.path(), destination.as_ref().join(entry.file_name()))?;
      } else {
          fs::copy(entry.path(), destination.as_ref().join(entry.file_name()))?;
      }
  }
  Ok(())
}


#[tauri::command]
async fn upload_file(file_path: String, file_name: String) -> String {

  // 复制文件，将path对应的文件复制到 static/train 目录下
  let train_dir = get_path_in_exe_dir("static\\train");
  let train_dir = train_dir.to_str().unwrap();
  write_log(format!("train_dir: {}", train_dir));

  let target_path = format!("{}\\{}", train_dir, file_name);

  // 打印文件路径
  println!("源路径：{}", file_path);
  println!("目标路径：{}", target_path);

  write_log("准备复制文件".to_string());
  write_log(format!("源路径：{}", file_path));
  write_log(format!("目标路径：{}", target_path));

  // 复制文件
  // 捕获错误异常
  let _copy_result = match fs::copy(file_path, &target_path) {
    Ok(r) => r,
    Err(e) => {
      write_log(format!("复制文件失败：{}", e));
      return "上传失败".to_string();
    }
  };
  // fs::copy(file_path, &target_path).unwrap();

  write_log("复制文件完成".to_string());

  // 返回复制文件结果
  let file_exist = std::path::Path::new(&target_path).exists();
  if file_exist {
    "上传成功".to_string()
  } else {
    "上传失败".to_string()
  }


    // ^We expect the frontend to send a file path.

    // let client = reqwest::Client::new();

    // let file_name = file.file_name().unwrap().to_string_lossy().to_string();
    // let file_content = tokio::fs::read(file).await.map_err(|err| err.to_string()).unwrap();
    // let part = reqwest::multipart::Part::bytes(file_content).file_name(file_name);
    // let form = reqwest::multipart::Form::new().part("file", part);

    // let server_url = get_server_url();
    // let url = format!("{}/{}", server_url, path);

    // let response = client
    //     .post(&url)
    //     .multipart(form)
    //     .send()
    //     .await.unwrap();

    // let status = response.status();
    // if !status.is_success() {
    //     return format!("上传文件失败，{}", status);
    // }

    // let text = response.text().await.unwrap();
    // return "上传成功".to_string();
}

#[tauri::command]
fn write_log(content: String) -> String {
  // 检查环境变量
  let env_var = env::var("APP_LOG");
  if env_var.is_err() || env_var.unwrap() != "1" {
      return "成功".to_owned();
  }

  // 获取当前可执行文件所在目录
  let exe_dir = match std::env::current_exe() {
      Ok(exe_path) => exe_path.parent().unwrap().to_path_buf(),
      Err(e) => {
          eprintln!("无法获取可执行文件路径: {}", e);
          return "失败".to_owned();
      }
  };

  // 构建日志文件路径
  let log_path = exe_dir.join("app.log");

  // 打开或创建日志文件
  let mut file = match OpenOptions::new()
      .create(true)
      .append(true)
      .open(&log_path)
  {
      Ok(file) => file,
      Err(e) => {
          eprintln!("无法打开日志文件: {}", e);
          return "失败".to_owned();
      }
  };

  // 写入日志内容
  let now = Utc::now();
  let fmt = "%Y-%m-%d %H:%M:%S";
  let log_content = format!("{} {}\n", now.format(fmt), content);

  if let Err(e) = file.write_all(log_content.as_bytes()) {
      eprintln!("无法写入日志文件: {}", e);
      return "失败".to_owned();
  }

  "成功".to_owned()
}

// 将路径按照分隔符划开，然后为空格加上修饰符
fn replace_path_with_blanks(path: &str) -> String {
  // 仅限Windows试用
  if cfg!(target_os = "windows") {
    let parts: Vec<String> = path.split("\\").into_iter().map(|part| {
      if part.contains(" ") || part.contains("(") || part.contains(")") {
        return format!("\"{}\"", part);
      }

      part.to_string()
    }).collect();

    return parts.join("\\");
  }

  return String::from(path);
}

#[derive(Clone, serde::Serialize)]
struct Payload {
    code: u32,
    message: String,
}

// 以下代码为建立教师机监听本地Websocket广播客户端实现
// 只能在登录后调用一次，此时可能出现配置文件加载异常和连接异常，可用catch抓到；
// 然后在所有窗口可以监听全局事件plt-ws-server-notice：
    // 当code = 0时，为收到服务器通知，此时message为JSON.stringify过的对象，内有type字段
    // 当code = 500为，为错误信息，例如服务器连接断开，无法和服务器维护心跳等。
#[tauri::command(async)]
async fn ws_client(app_handle: tauri::AppHandle, running: State<'_, Mutex<bool>>) -> Result<String, String> {
    write_log("初始化WS_CLIENT".to_string());

    {
        let mut is_run = running.lock().await;
        if *is_run {
            return Ok("已经执行过了".to_owned());
        }
        *is_run = true;
    }

    write_log("WS_CLIENT互斥锁OK".to_string());

    let server_url = String::from("127.0.0.1:46048");
    let connect_addr = ("http://".to_owned() + &server_url + "/ws/local").replace("http", "ws");
    let url = url::Url::parse(&connect_addr).unwrap();
    let (mut server_stream, _) = match connect_async(url).await {
        Ok(r) => r,
        Err(e) => {
            // 连接失败时重置running状态
            {
                let mut is_run = running.lock().await;
                *is_run = false;
            }
            return Err(format!("连接错误：{}", e));
        }
    };

    if cfg!(debug_assertions) {
        println!("Websocket客户端与{}连接成功建立。", connect_addr);
    }

    write_log("WS_CLIENT连接服务器OK".to_string());

    while let Some(server_msg) = server_stream.next().await {
        let server_msg = match server_msg {
            Ok(r) => r,
            Err(e) => {
                match app_handle.emit("plt-ws-server-notice", Payload { code: 500, message: format!("接受服务器消息时出错, {}", e) }) {
                    Ok(r) => r,
                    Err(e) => {
                        println!("向应用发送消息时出错 {}", e);
                        break;
                    }
                }
               // 连接断开时重置running状态
               {
                   let mut is_run = running.lock().await;
                   *is_run = false;
               }
                break;
            }
        };

        if server_msg.is_ping() {
            if cfg!(debug_assertions) {
                println!("服务器发送给客户端：PING");
                println!("客户端回复给服务器：PONG");
            }

            match server_stream.send(Message::Pong(server_msg.into())).await {
                Ok(r) => r,
                Err(e) => {
                    match app_handle.emit("plt-ws-server-notice", Payload { code: 500, message: format!("将服务器回复PONG消息出错, {}", e) }) {
                        Ok(r) => r,
                        Err(e) => {
                            println!("向应用发送消息时出错 {}", e);
                            break;
                        }
                    }
                    break;
                }
            };
        }
        else if server_msg.is_pong() {
            if cfg!(debug_assertions) {
                println!("服务器发送给客户端：PONG");
            }
        }
        else if server_msg.is_empty() {
            if cfg!(debug_assertions) {
                println!("服务器发送给客户端：EMPTY");
            }
        }
        else if server_msg.is_close() {
            if cfg!(debug_assertions) {
                println!("服务器发送给客户端：CLOSE");
            }
            // 服务器主动关闭连接时重置running状态
            {
                let mut is_run = running.lock().await;
                *is_run = false;
            }
            break;
        }
        else if server_msg.is_binary() {
            if cfg!(debug_assertions) {
                println!("服务器发送给客户端：BINARY");
            }
        }
        else if server_msg.is_text() {
            if cfg!(debug_assertions) {
                println!("服务器发送给客户端：{}", server_msg);
            }

            let server_msg_text = match server_msg.into_text() {
                Ok(r) => r,
                Err(e) => {
                    match app_handle.emit("plt-ws-server-notice", Payload { code: 500, message: format!("将服务器消息转换为文本出错, {}", e) }) {
                        Ok(r) => r,
                        Err(e) => {
                            println!("向应用发送消息时出错 {}", e);
                            break;
                        }
                    }
                    break;
                }
            };

            match app_handle.emit("plt-ws-server-notice", Payload { code: 0, message: server_msg_text }) {
                Ok(r) => r,
                Err(e) => {
                    println!("向应用发送消息时出错 {}", e);
                     break;
                }
            }
        }
    }

    // 函数结束时重置running状态为false
    {
        let mut is_run = running.lock().await;
        *is_run = false;
    }

    Ok("与服务器连接已关闭".to_owned())
}

pub fn run() {

    write_log("切换路径完成".to_string());
    let running = Mutex::new(false);
    write_log("互斥锁创建完成".to_string());

    // 禁止多开
    let instance = SingleInstance::new("plt-teacher-app").unwrap();
    if !instance.is_single() {return;}
    // 获取可执行文件所在目录
    let exe_dir = std::env::current_exe()
        .expect("无法获取可执行文件路径")
        .parent()
        .expect("无法获取父目录")
        .to_path_buf();

    // 构建server.env的完整路径
    let env_path = exe_dir.join("server.env");
    dotenvy::from_path(env_path).expect("server.env不存在!");
    // 监测教师机端口是否已经开放，没有开放尝试帮助启动同目录下的教师机应用
    let port = env::var("PORT").expect("配置文件server.env不存在PORT监听地址配置，您可以配置为PORT=46048");
    let port_u16:u16 = match port.parse::<u16>() {Ok(r) => r,Err(_e) => {
        println!("端口号配置异常{}", port);
        return;
        }
    };
    // 如果端口没有被占用,要主动启动当前路径下的服务器应用
    if !port_check::is_port_reachable( SocketAddr::from(([127, 0, 0, 1], port_u16))) {
        let server_exe_file_path = get_path_in_exe_dir("plt-teacher-server.exe");
        // Windows且非调试版本，尝试隐藏服务器端命令行窗口
        if !cfg!(debug_assertions) {#[cfg(target_os = "windows")]
        match Command::new(server_exe_file_path).creation_flags(0x08000000).spawn() {Ok(r) => r,Err(e) => {
            panic!("无法启动氦星人教师机服务器plt-teacher-server.exe, {}", e);
            }
        };
        } else {
            let server_exe_file_path = replace_path_with_blanks(server_exe_file_path.to_str().unwrap());
            match Command::new("cmd").arg(format!("/c start {}", server_exe_file_path.as_str())).spawn() {Ok(r) => r,Err(e) => {
                panic!("无法启动氦星人教师机服务器plt-teacher-server.exe, {}", e);
                }
            };
        }
        // 延时5秒等plt-teacher-server.exe进程启动了
        sleep(Duration::from_secs(5));
    }

#[tauri::command]
fn stop_server() {
    // 终止 plt-teacher-server.exe 进程
    Command::new("taskkill")
        .args(&["/F", "/IM", "plt-teacher-server.exe"])
        .creation_flags(0x08000000)
        .spawn()
        .expect("Failed to stop plt-teacher-server.exe");

    // 终止所有 Python 进程
    Command::new("taskkill")
        .args(&["/F", "/IM", "python.exe"])
        .creation_flags(0x08000000)
        .spawn()
        .expect("Failed to stop Python processes");
}

  tauri::Builder::default()
    .manage(running)
    .plugin(tauri_plugin_http::init())
    .plugin(tauri_plugin_dialog::init())
    .plugin(tauri_plugin_fs::init())
    .plugin(tauri_plugin_os::init())
    .plugin(tauri_plugin_process::init())
    .plugin(tauri_plugin_persisted_scope::init())
    .invoke_handler(tauri::generate_handler![get_server_url, get_env_var, get_network_cards_info, upgrade, upload_file, write_log, is_software_installed, open_software, get_software_installed, get_resource_dir, stop_server, ws_client])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
