{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "enables the default permissions", "windows": ["main", "login", "startUpWizard", "offlineSync", "register", "downloadCourse", "<PERSON><PERSON><PERSON><PERSON>"], "permissions": ["core:window:default", "core:default", "core:window:allow-close", "core:window:allow-destroy", "core:window:allow-start-dragging", "core:window:allow-set-fullscreen", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "process:default", "process:allow-exit", "core:webview:allow-create-webview-window", "core:window:allow-set-shadow", "core:window:allow-set-title", "dialog:default", "dialog:allow-save", "shell:allow-open", {"identifier": "http:default", "allow": [{"url": "http://**"}, {"url": "https://**"}, {"url": "http://*:*"}, {"url": "https://*:*"}]}, {"identifier": "fs:allow-exists", "allow": [{"path": "$APP/**"}, {"path": "$RESOURCE/**"}, {"path": "server.env"}, {"path": "start_ping.bat"}]}, {"identifier": "fs:allow-write-file", "allow": [{"path": "$APP/**"}, {"path": "$RESOURCE/**"}, {"path": "server.env"}, {"path": "start_ping.bat"}]}, {"identifier": "fs:allow-write-text-file", "allow": [{"path": "$APP/**"}, {"path": "$RESOURCE/**"}, {"path": "server.env"}, {"path": "start_ping.bat"}]}, {"identifier": "fs:allow-read-file", "allow": [{"path": "$APP/**"}, {"path": "$RESOURCE/**"}, {"path": "server.env"}, {"path": "start_ping.bat"}]}, {"identifier": "fs:allow-read-text-file", "allow": [{"path": "$APP/**"}, {"path": "$RESOURCE/**"}, {"path": "server.env"}, {"path": "start_ping.bat"}]}]}