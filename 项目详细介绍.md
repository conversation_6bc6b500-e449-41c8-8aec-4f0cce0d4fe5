# 氦星人教师机系统 - 项目详细介绍

## 📋 项目概述

氦星人教师机系统是一个多组件的教育平台，由三个主要部分组成：桌面前端应用、本地Rust后端服务和Node.js API服务。系统从原来的直连远程云平台架构，成功迁移为本地化架构，实现了更好的可控性和稳定性。

### 🏗️ 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  plt-teacher-app│───▶│plt-teacher-server│───▶│   api (local)   │───▶│  远程云平台      │
│   (Tauri+React) │    │   (Rust+Actix)   │    │ (Node.js+Egg.js)│    │ (hxr.iyopu.com) │
│   Port: 8080    │    │   Port: 46048    │    │   Port: 7001    │    │                │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   WebSocket      │
                       │   Port: 46046    │
                       │  (进度推送服务)   │
                       └──────────────────┘
```

## 🔧 技术栈详情

### 1. plt-teacher-app (前端桌面应用)
- **框架**: Tauri + React + Umi.js
- **端口**: 8080
- **功能**: 
  - 教师端用户界面
  - 课程管理和班级管理
  - 学生终端管理
  - 实时课程同步状态显示
- **启动命令**: `npx tauri dev`

### 2. plt-teacher-server (本地Rust后端)
- **框架**: Rust + Actix-web + SeaORM
- **端口**: 46048 (主服务) + 46046 (WebSocket进度推送)
- **数据库**: SQLite (hxr.db)
- **功能**:
  - 本地API服务
  - 课程数据管理和状态对比
  - 与本地Node.js API通信的中介
  - WebSocket连接管理
  - 课程同步进度推送
- **启动命令**: `cargo run`

### 3. api (本地Node.js API服务)
- **框架**: Node.js + Egg.js
- **端口**: 7001
- **功能**:
  - 本地API服务器
  - 与远程云平台通信
  - 队列任务处理
  - 课程内容下载和管理
- **启动命令**: `pnpm run dev`

## 🚀 核心功能实现

### 课程同步系统

#### 1. 状态对比机制
系统实现了智能的课程状态对比功能，通过 `compare_and_mark_sections()` 函数对比本地和远程课程：

- **删除**: 本地有但远程没有的section
- **无变化**: 本地和远程都有且内容相同
- **新增**: 远程有但本地没有的section
- **更新**: 本地和远程都有但内容不同

#### 2. 实时进度推送
- **WebSocket服务**: 46046端口专用进度推送
- **进度格式**: 直接发送0-100数字表示同步进度
- **自动关闭**: 同步完成后自动关闭连接

#### 3. 队列架构适配
系统成功适配了Node.js API的队列架构：
- 移除了原有的URL提取逻辑
- 实现了基于WebSocket的进度跟踪
- 支持异步任务处理

### 用户界面功能

#### 1. 自动同步检测
- 定时检查课程状态
- 智能显示同步按钮
- 条件性UI渲染

#### 2. Section禁用功能
- 同步期间禁用section点击
- 防止用户误操作
- 同步完成后自动恢复

#### 3. 进度显示
- 实时进度条显示
- 详细状态信息
- 错误处理和提示

## 📡 API接口详情

### 新增接口

#### 1. 课程同步接口
```rust
PUT /api/web/course/{slug}
```
- **功能**: 触发课程内容更新和同步
- **参数**: `slug` (路径参数) - 课程标识符
- **返回**: 启动状态和WebSocket连接信息

#### 2. WebSocket测试接口
```rust
GET /api/test/websocket
```
- **功能**: 测试46046端口WebSocket服务

### 修改接口

#### 1. 课程目录接口 (重大修改)
```rust
GET /api/web/course/{course_slug}/indices
```
- **新增功能**: 
  - 调用本地API获取远程课程信息
  - 对比本地和远程课程数据
  - 为每个section添加 `modify_status` 字段

### 前端服务函数
```javascript
// 获取远程课程信息（用于对比）
export async function getRemoteCourseInfo(courseSlug)

// 更新课程内容（触发后端下载和更新）
export async function updateCourseContent(courseSlug)
```

## 🔄 数据流程

### 课程同步流程
```
1. 前端点击"同步线上课程"
   ↓
2. 调用 PUT /api/web/course/{slug}
   ↓
3. Rust后端获取CSRF token
   ↓
4. 调用本地API: POST /admin/file/updateCourseContentForPLT
   ↓
5. 启动WebSocket服务 (46046端口)
   ↓
6. 前端连接WebSocket接收进度
   ↓
7. 实时显示下载进度
   ↓
8. 同步完成，自动关闭连接
```

### 状态检查流程
```
1. 前端调用 getRemoteCourseInfo()
   ↓
2. 后端调用 GET /admin/course/getCourseAndSectionBycourseSlug/{slug}
   ↓
3. 执行 compare_and_mark_sections() 对比
   ↓
4. 返回带有 modify_status 的课程数据
   ↓
5. 前端根据状态显示对应UI
```

## 🛠️ 关键技术实现

### WebSocket双服务架构
- **主WebSocket** (46048): 一般通信
- **课程同步WebSocket** (46046): 专用进度推送

### 异步任务处理
```rust
// 异步启动WebSocket服务
tokio::spawn(async move {
    let listener = tokio::net::TcpListener::bind("127.0.0.1:46046").await;
    // 处理进度推送逻辑
});
```

### 状态管理系统
- 复杂的前端状态管理
- 同步状态跟踪
- 错误处理和恢复

### 认证流程
- CSRF token获取和验证
- Session管理
- 安全的API调用

## 📁 重要文件结构

### Rust后端核心文件
- `api/src/course.rs` - 课程同步核心逻辑
- `src/main.rs` - 应用入口和配置
- `websocket/src/server.rs` - WebSocket服务实现

### 前端核心文件
- `src/pages/course/courseDetail.jsx` - 课程详情页面
- `src/utils/courseUpdateWebSocket.js` - 课程同步WebSocket客户端
- `src/services/course.js` - API服务层

### 配置文件
- `Cargo.toml` - Rust依赖管理
- `server.env` - 服务器配置
- `package.json` - 前端依赖管理

## 🚦 启动顺序

### 正确的启动顺序
1. **启动Node.js API服务**
   ```bash
   cd api
   pnpm run dev
   ```

2. **启动Rust后端服务**
   ```bash
   cd plt-teacher-server
   cargo run
   ```

3. **等待10秒后启动前端应用**
   ```bash
   cd plt-teacher-app
   npx tauri dev
   ```

### 端口占用检查
- 系统会自动检查46048端口是否被占用
- 如有冲突会提示修改配置

## 🔧 配置要求

### 环境变量
- `COOKIE_KEY`: 64位字母数字组合
- `PORT`: 服务器监听端口 (默认46048)

### 依赖要求
- Rust 1.70+
- Node.js 16+
- pnpm
- Tauri CLI

## 🎯 核心优势

### 1. 架构优化
- 从直连远程API改为本地中转
- 更好的错误处理和重试机制
- 支持离线模式

### 2. 用户体验
- 实时进度显示
- 智能状态检测
- 自动同步功能

### 3. 技术特性
- 异步任务处理
- WebSocket实时通信
- 队列架构支持

## 🔍 故障排除

### 常见问题
1. **端口冲突**: 检查46048和46046端口
2. **认证失败**: 验证CSRF token配置
3. **WebSocket连接失败**: 确认46046端口可用
4. **API调用超时**: 检查本地API服务状态

### 日志查看
- Rust后端: 控制台输出
- Node.js API: 开发模式日志
- 前端: 浏览器开发者工具

## 📈 未来扩展

### 可能的改进方向
1. 支持多课程并发同步
2. 增加同步历史记录
3. 优化WebSocket重连机制
4. 添加更多状态检查点

---

**最后更新**: 2025-11-04
**版本**: v0.1.0
**维护者**: 氦星人教育团队
