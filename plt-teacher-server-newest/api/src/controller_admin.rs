use core::str;
use std::{cell, collections::{HashMap, HashSet}, fs, io::{<PERSON><PERSON><PERSON>, Seek<PERSON><PERSON>}, path::Path, result, sync::{Arc, Mutex}, thread::current};

use actix_session::Session;
use actix_web::{
    get, post, web, HttpRequest, HttpResponse, Result, put, delete,
};
use chinese_number::{ChineseCase, ChineseCountMethod, ChineseVariant, NumberToChinese};
use entity::{course, 
    section_record::SectionRecordItem, 
    system_config::UploadConfig, 
    team::{TeamUsersRecordOffline, UploadUserRecord}, 
    terminal::TerminalConfig, 
    // train::UploadTrainData, 
    // train_plan::{PauseRecord, TrainPlanConfigs, TrainPlanCreateRequest}, 
    // train_series::TrainSeriesImportItem, 
    // train_user_correction_record::TerminalUserCorrectionRecord, 
    // train_user_record::{TerminalUserRecord, TrainPlanRecordRequest, TrainPlanRecordRequestWithIP, TrainUpdateUserRecord}
};
use failure::ResultExt;
use fs_extra::{file, remove_items};
use localsend::protos::UDPMessageType;
use localsend::server::UDPService;
// use log::kv::ToValue;
use quick_xml::{events::Event, Reader};
use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};
use serde::{Deserialize, Serialize};
use serde_json::{Value, Map, json};


// extern crate json_value_merge;
use json_value_merge::Merge;

use chrono::{format, prelude::*};
use websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};
use crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJContent}};

use super::context::{ AppState, BusinessError, BusinessResponse };
use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };
use ::entity::{
    user::UserRecord,
    team::TeamUsersRecord,
    // train::TrainRecord,
};

use tokio::{fs as tokio_fs, io::AsyncSeekExt, sync::mpsc::Sender};
use tokio::io::AsyncWriteExt;
use actix_multipart::{form::json, Multipart};
use futures::{join, StreamExt, TryStreamExt};
use once_cell::sync::Lazy;


use rand::Rng; // 用于生成随机数
use sha2::{Sha256, Digest}; // 用于生成 sha2

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TerminalRow {
    pub ip_address: Option<String>,
    pub online: String,
    pub status: String,
    pub display_name: Option<String>,
    pub team_name: Option<String>,
    pub team_ids: Option<Vec<i32>>,
    pub username: Option<String>,
    pub train_name: Option<String>,
    pub train_id: i32,
    pub user_id:i32,
    pub score: Option<Decimal>,
    pub initial_score:Option<Decimal>,
    pub correction_score: Option<Decimal>,
    pub finish_rate:Option<Decimal>,
    pub question_type_stat: Option<Json>,
    pub question_type_stat_init: Option<Json>,
    pub submit_time: Option<chrono::NaiveDateTime>,
    pub created_at: Option<chrono::NaiveDateTime>,
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct IpRequest{
    ip: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TerminalResetBatchRequest{
    // IP数组
    ips: Option<Vec<String>>,

    // 是否同时按照IP清理用户答题记录
    clear_user_record_by_ip: Option<bool>
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TerminalTransferRequest{
    // 源IP
    from_ip: Option<String>,

    // 目标IP
    to_ip: Option<String>,
}


#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct IpsRequest{
    // 字符串数组
    ips: Option<Vec<String>>,
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TrainPlanDeleteRequest{
    ids: Option<Vec<i32>>,
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TrainConfig {
    pub enable_no_password_login: String,
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct StyleConfig {
    pub enable_modern_style: String,
}

/**
 * 从数据库加载学校URL、默认教师账号和密码
 * 返回值：
 * {
    "code": 0,
    "message": "ok",
    "data": {
        "school": "学校名称",
        "siteURL": "http://csxx.hxr.i51cy.com",
        "defaultUsername": "datouxia"
        "defaultPassword": "yopu1234",
    }
}
 */
#[get("/api/admin/section/teacher/login/config")]
pub async fn get_section_teacher_login_config(app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let conn = &app_state.conn;
    let key_value_map_result= Query::system_config_find_by_keys(conn, vec!["school_name".into(), "site_url".into(), "lab".into(), "default_username".into(), "default_password".into(), "licence".into(), "current_course_slug".into(), "save".into()]).await;
    
    if key_value_map_result.is_err() {
        return Err(BusinessError::InternalError { reason: key_value_map_result.unwrap_err().to_string() });
    }

    BusinessResponse::ok(key_value_map_result.unwrap()).to_json_result()
}

/**
 * 登录远程主站账号
 * 1. 用于验证主站账号合法性，获取进远程操作Session；
 * 2. 合法账号，插入或更新当前账号到本地数据库
 * 3. 合法账号，插入默认站点、学校和默认账号信息到数据库
    {
        "site_url": "http://csxx.hxr.i51cy.com",
        "username": "datouxia",
        "password": "yopu1234"
    }
 */
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct PostTrainTeacherRemoteLogingRequest{
    site_url: String,
    username: String,
    password: String,
    lab: String,
    mac: String
}

#[post("/api/admin/course/teacher/remote/login")]
pub async fn post_course_teacher_remote_login(session: Session, request: web::Json<PostTrainTeacherRemoteLogingRequest>, http_request: HttpRequest, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    // 接收并检查PUT传入的网址，用户名、密码
    let request = request.into_inner();
    if request.site_url.is_empty() {
        return Err(BusinessError::ValidationError { field: "site_url".to_owned() });
    }

    if request.username.is_empty() {
        return Err(BusinessError::ValidationError { field: "username".to_owned() });
    }

    if request.password.is_empty() {
        return Err(BusinessError::ValidationError { field: "password".to_owned() });
    }

    if request.mac.is_empty() {
        return Err(BusinessError::ValidationError { field: "mac".to_owned() });
    }

    if request.lab.is_empty() {
        return Err(BusinessError::ValidationError { field: "lab".to_owned() });
    }

    let request_site_url = request.site_url;

    // 调试状态下默认用HTTP能看到具体通信
    // let mut request_site_url = request.site_url;
    // if cfg!(debug_assertions) {
    //     request_site_url = request_site_url.replace("https:", "http:");
    // }

    // 判断是否跳过授权，当环境变量"HXR-SKIP-LISCENSE"为"1"时，向服务器发送跳过授权标志
    let skip_license_env = match std::env::var("HXR-SKIP-LISCENSE") {
        Ok(r) => r,
        Err(_e) => "0".to_string()
    };

    let is_skip_license = skip_license_env == "1";

    // * 1. 用于验证主站账号合法性，获取进远程操作Session；
    // 不合法账号在此停止
    let mut remote_login_response = match Remote::teacher_login(request_site_url.clone(), request.mac.clone(), request.lab.clone(), request.username.clone(), request.password.clone(), is_skip_license).await {
        Ok(r) => r,
        Err(err) => return Err(BusinessError::AccountError { reason: err.to_string() })
    };

    // 读取登录返回值code
    let remote_login_response_code = remote_login_response.get("code").unwrap().clone();
    let remote_login_response_code = remote_login_response_code.as_i64().unwrap() as i32;
    let now = Local::now().naive_local();
    
    // 如果是授权异常
    if 511 == remote_login_response_code {
        let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();

        let licence = remote_login_response.get("licence").unwrap();
        if licence.is_null() {
            key_value_map.insert("licence".into(), None);
        }
        else {
            let licence = licence.as_object().unwrap();
            let licence_json = match serde_json::to_string(licence) {
                Ok(r) => r,
                Err(err) => {
                    return Err(BusinessError::InternalError { reason: format!("将授权信息转换为JSON格式时出错, {}", err.to_string()) })
                }
            };

            key_value_map.insert("licence".into(), Some(licence_json.clone()));
        }
        

        let conn = &app_state.conn;

        // 开启事务
        let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
        };    

        let _count = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map,  &now).await {
            Ok(count) => count,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: e.to_string() })
            }
        };   

        // 结束事务
        match txn.commit().await {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        };
        
        let message = remote_login_response.get("message").unwrap().to_string();

        // 报错
        return Err(BusinessError::AccountError { reason: message });
    }

    // 删除多余code
    remote_login_response.remove("code");

    const SERVER_VERSION: &str = env!("CARGO_PKG_VERSION");
    remote_login_response.insert(String::from("api_version"), SERVER_VERSION.into());

    // * 2. 合法账号，插入或更新当前账号到本地数据库
    let user = remote_login_response.get("user").unwrap().clone();

    // 获取用户登录信息
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    let user_record = UserRecord {
        id: user.get("id").unwrap().as_f64().unwrap() as i32,
        username: user.get("username").unwrap().as_str().unwrap().into(),
        password: user.get("password").unwrap().as_str().unwrap().into(),
        name: user.get("name").unwrap().as_str().unwrap().into(),
        sen: match user.get("sen").unwrap() {
            serde_json::Value::Null => Option::None,
            serde_json::Value::String(v) => Some(v.into()),
            _ => Option::None,
        },
        avatar: match user.get("avatar").unwrap() {
            serde_json::Value::Null => Option::None,
            serde_json::Value::String(v) => Some(v.into()),
            _ => Option::None,
        },
        state: match user.get("state").unwrap() {
            serde_json::Value::Null => Option::None,
            serde_json::Value::String(v) => Some(v.into()),
            _ => Option::None,
        },
        admin_authority: match user.get("adminAuthority").unwrap() {
            serde_json::Value::Null => Option::None,
            serde_json::Value::Object(v) => Some(serde_json::Value::Object((*v).clone())),
            _ => Option::None,
        },
        school: match user.get("school").unwrap() {
            serde_json::Value::Null => Option::None,
            serde_json::Value::String(v) => Some(v.into()),
            _ => Option::None,
        },
        last_active_time: Some(now),
        last_active_ip: Some(client_ip.to_string())
    };

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 当试用模式时，清理数据库和文件系统训练资产
    // 自系统配置表读取当前site_url
    let site_url = match Query::system_config_find_by_key(&txn, "site_url").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()).to_string() });
        }
    };

    // println!("site_url: {:?}", site_url);
    if site_url.is_some() {
        let site_url = site_url.unwrap();

        // 当前更换站点时，必须清理
        if site_url != request_site_url {
            // 清理数据库
            match Mutation::clear_demo_database(&txn).await {
                Ok(r) => r,
                Err(e) => {
                    return Err(BusinessError::InternalError { reason: format!("数据库清理失败, {}", e.to_string()) });
                }
            };
        
            // 清理文件系统中的训练资产
            let static_train_dir = get_path_in_exe_dir("static").join("train");
            let static_train_dir = static_train_dir.to_str().unwrap();

            let mut from_paths = Vec::new();
            from_paths.push(static_train_dir);
            match remove_items(&from_paths) {
                Ok(r) => r,
                Err(e) => {
                    return Err(BusinessError::InternalError { reason: format!("清理文件系统训练资产失败, {}", e.to_string()) });
                }
            };

            match std::fs::create_dir_all(static_train_dir) {
                Ok(_r) => (),
                Err(e) => {
                    panic!("递归创建目标目录失败 {}", e.to_string());
                }
            };
        }
    }

    let now = Local::now().naive_local();
    let _batch_save_response = match Mutation::user_save(&txn, user_record.clone(), &now).await {
        Ok(r) => r,
        Err(err) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: err.to_string() })
        }
    };

    // * 3. 合法账号，插入默认站点、学校、授权和默认账号信息到数据库
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    let school_name = remote_login_response.get("schoolName").unwrap().as_str().unwrap();
    key_value_map.insert("school_name".into(), Some(school_name.to_string()));
    
    // 如果未跳过授权，则需写入授权信息
    if !is_skip_license {
        let licence = remote_login_response.get("licence").unwrap().as_object().unwrap();
        let licence_json = match serde_json::to_string(licence) {
            Ok(r) => r,
            Err(err) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("将授权信息转换为JSON格式时出错, {}", err.to_string()) })
            }
        };
    
        key_value_map.insert("licence".into(), Some(licence_json.clone()));
    }

    key_value_map.insert("site_url".into(), Some(request_site_url.clone()));
    key_value_map.insert("default_username".into(), Some(request.username.clone()));
    key_value_map.insert("default_password".into(), Some(request.password.clone()));
    key_value_map.insert("lab".into(), Some(request.lab.clone()));

    // 默认开启终端设备绑定
    let enable_terminal_ip_bind = match Query::system_config_find_by_key(&txn, "enable_terminal_ip_bind").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取终端设备绑定配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 如果字段不存在，则默认开启，否则使用数据库配置
    let enable_terminal_ip_bind = enable_terminal_ip_bind.unwrap_or("1".to_string());

    key_value_map.insert("enable_terminal_ip_bind".into(), Some(enable_terminal_ip_bind));

    let _count = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map,  &now).await {
        Ok(count) => count,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: e.to_string() })
        }
    };

    // // 当更换教师登录时，清除当前训练计划
    // match Mutation::clear_current_train_plan_when_orther_teacher_login(&txn, user_record.id).await  {
    //     Ok(r) => r,
    //     Err(e) => {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: format!("当更换教师登录时，清除当前训练计划失败 {}", e.to_string()).to_string() })
    //     }
    // };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    
    // 4. 写入会话完成登录
    match session.insert("user_id", user_record.id) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };

    match session.insert("user_display_name", user_record.name) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };
    
    match session.insert("user_admin_authority", user_record.admin_authority) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };

    // 写入远程会话
    let mut remote_session:HashMap<String, Value> = HashMap::new();
    remote_session.insert(String::from("site_url"), Value::String(request_site_url));
    remote_session.insert(String::from("cookies"),  remote_login_response.get("cookies").unwrap().clone());
    remote_session.insert(String::from("csrf_token"), remote_login_response.get("csrfToken").unwrap().clone());
    
    let remote_session = match serde_json::to_string(&remote_session) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "JSON序列化失败".to_string() })
    };

    match session.insert("admin_remote_session", remote_session) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };

    BusinessResponse::ok(remote_login_response).to_json_result()
}

/**
 * 远程登录之后获取所有班级，可按照学年过滤（页面显示使用）
 * 参数school_year：例如2022学年
 * 返回：
 * {
    "code": 0,
    "message": "ok",
    "data": [
        {
            "count": 3,
            "id": 127,
            "name": "南京市第一中学主校区 高一 2班",
            "year": "2022学年"
        }
    ]
    }   
 */
#[get("/api/admin/course/teacher/remote/school_year/{school_year}/all_teams")]
pub async fn get_remote_all_teams_by_school_year(session: Session, school_year: web::Path<String>) -> Result<HttpResponse, BusinessError> {
    let school_year = school_year.into_inner();

    // 加载远程接口的Session
    let remote_session = match session.get::<String>("admin_remote_session") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() })
    };

    if remote_session.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
    }

    let remote_session = remote_session.unwrap();
    let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };

    // 调用远程接口
    let all_teams = match Remote::list_all_teams_by_school_year(remote_session, school_year).await {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "调用远程接口失败".to_string() })
    };

    BusinessResponse::ok(all_teams).to_json_result()
}

/**
 * 远程登录之后获取指定班级内部的用户列表
 * 参数team_id：例子里面是teamID 53的班级
 * 返回：
 * {
        "code": 0,
        "message": "ok",
        "data": [
            {
                "avatar": null,
                "classID": 53,
                "id": 5514,
                "name": "李浩扬",
                "school": "金陵中学",
                "sen": null,
                "state": null,
                "username": "301"
            }
        ]
    }
 * 
 */
#[get("/api/admin/course/teacher/remote/team/{team_id}/users")]
pub async fn get_remote_team_users_by_userid(session: Session, team_id: web::Path<String>) -> Result<HttpResponse, BusinessError> {
    let team_id = team_id.into_inner();

    // 加载远程接口的Session
    let remote_session = match session.get::<String>("admin_remote_session") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
    };

    if remote_session.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
    }

    let remote_session = remote_session.unwrap();
    let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };

    // 调用远程接口
    let team_users = match  Remote::list_team_users_by_userid(remote_session, team_id).await {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "调用远程接口失败".to_string() })
    };

    BusinessResponse::ok(team_users).to_json_result()
}


/**
 * 远程登录之后根据用户选定的班级信息将指定班级与班级用户同步至本地
 * 查询参数: ?team_ids=127,117
 * 返回值：
 */

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct PostTeamUsersSyncQuery {
    team_ids: String
}


#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
struct TeamUsersResponse {
    code: i32,
    message: String,
    data: Vec<TeamUsersRecord>
}

#[post("/api/admin/course/teacher/remote/team/sync/users")]
pub async fn post_remote_team_users_sync(session: Session, req: HttpRequest, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    // 读取查询参数
    let params = web::Query::<PostTeamUsersSyncQuery>::from_query(req.query_string()).unwrap();
 
    // 接收并检查POST传入的队伍信息
    if params.team_ids.is_empty() {
        return Err(BusinessError::ValidationError { field: "team_infos".to_owned() });
    }

    // 加载远程接口的Session
    let remote_session = match session.get::<String>("admin_remote_session") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
    };

    if remote_session.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
    }

    let remote_session = remote_session.unwrap();
    let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };

    // 调用远程接口获取队伍内的账号信息
    let team_users_response = match Remote::list_team_users_by_userids(remote_session, params.team_ids.clone()).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("调用远程接口失败,{}", e)})
    };

    if team_users_response.is_empty() {
        return Err(BusinessError::InternalError { reason: "解析远程返回班级学生账号信息失败，结果为空".into() })
    }

    print!("{}", team_users_response);
    let team_users_response: TeamUsersResponse = match serde_json::from_str(team_users_response.as_str()) {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("解析远程返回班级学生账号信息失败 {}", e.to_string()) })
    };

    // 同步数据
    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 将队伍同步到本地数据库
    match Mutation::team_batch_save(&txn, &(team_users_response.data), &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍同步到本地数据库失败 {}", e.to_string()).to_string() })
    };
    
    // 将账号同步到本地数据库
    let set_null_password = match Mutation::user_batch_save_by_teams(&txn, &(team_users_response.data), &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍账号同步到本地数据库失败 {}", e.to_string()).to_string() })
    };

    // 将队伍账号关联同步到本地数据库
    match Mutation::team_user_batch_save_by_teams(&txn, &(team_users_response.data), &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍账号关联同步到本地数据库失败 {}", e.to_string()).to_string() })
    };
    
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut response:HashMap<String, bool> = HashMap::new();
    response.insert(String::from("set_null_password"), set_null_password);

    BusinessResponse::ok(response).to_json_result()
}

/**
 * 远程登录之后按照学年获取所有训练
 * 参数school_year：例如2022学年
 * 返回：
 * {
    "code": 0,
    "message": "ok",
    "data": [
        {
            "abstract": null,
            "cover": null,
            "createUserDisplayName": "datouxia",
            "createUserID": 3013,
            "createUsername": "datouxia",
            "created_at": "2023-01-05T06:52:55.000Z",
            "deleted_at": null,
            "difficulty": null,
            "discriminative": null,
            "duration": 60,
            "id": 319,
            "ifSetWrongProblemCollection": 1,
            "ifShowCorrectionResults": 1,
            "ifShowScore": 1,
            "ifShowWrongAnswer": 1,
            "isFinish": 1,
            "name": "33",
            "notice": null,
            "score": 100,
            "status": "",
            "teachers": null,
            "templateDifficulty": {
                "简单": "12",
                "较难": "2",
                "适中": "15"
            },
            "templateName": "江苏省普通高中学业水平合格性考试（信息技术）",
            "updated_at": "2023-01-05T06:52:55.000Z",
            "year": "2022学年"
        },
    ]
 */
#[get("/api/admin/train/teacher/remote/series/{series}/all_trains")]
pub async fn get_remote_all_trains_by_series(session: Session, series: web::Path<i32>) -> Result<HttpResponse, BusinessError> {
    let series = series.into_inner();

    // 加载远程接口的Session
    let remote_session = match session.get::<String>("admin_remote_session") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() })
    };

    if remote_session.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
    }

    let remote_session = remote_session.unwrap();
    let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };

    // 调用远程接口
    let all_trains = match Remote::list_all_trains_by_series(remote_session, series).await {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "调用远程接口失败".to_string() })
    };

    BusinessResponse::ok(all_trains).to_json_result()
}

// 远程登录后获取试卷全部系列
#[get("/api/admin/train/teacher/remote/train_series")]
pub async fn get_remote_all_series(session: Session) -> Result<HttpResponse, BusinessError> {
    // 加载远程接口的Session
    let remote_session = match session.get::<String>("admin_remote_session") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() })
    };

    if remote_session.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
    }

    let remote_session = remote_session.unwrap();
    let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };

    // 获取用户id
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先登录".to_string() });
    }

    let user_id = user_id.unwrap();

    // 调用远程接口
    let all_series = match Remote::list_all_series_by_userid(remote_session, user_id).await {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "调用远程接口失败".to_string() })
    };

    BusinessResponse::ok(all_series).to_json_result()
}

/**
 * 远程登录之后根据用户选定的训练将数据与文件发送至本地
 * 查询参数: ?train_ids=274,275,276
 * 返回值：
 */

 #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
 pub struct PostTrainSyncQuery {
     train_ids: String
 }

 #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
 pub struct MP4File {
     train_id: String,
     from: String,
     to: String
 }

  
//  #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
//  struct TrainBatchImportInfo {
//     train_file_url: String,
//     mp4_files: Vec<MP4File>,
//     train_info: TrainRecord,
//  }
 
 
//  #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
//  struct TrainImportResponse {
//      code: i32,
//      message: String,
//      data: TrainBatchImportInfo
//  }
 
//  #[post("/api/admin/train/teacher/remote/train/{train_id}/sync")]
//  pub async fn post_remote_train_sync(session: Session, train_id: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_id = train_id.into_inner();
//     let app_version = env!("CARGO_PKG_VERSION");

//      // 加载远程接口的Session
//      let remote_session = match session.get::<String>("admin_remote_session") {
//          Ok(r) => r,
//          Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
//      };
 
//      if remote_session.is_none() {
//          return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
//      }
 
//      let remote_session = remote_session.unwrap();
//      let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
//          Ok(r) => r,
//          Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
//      };

//     let (client, url, _csrf_token) = match get_client_csrf_token_from_session(remote_session.clone()) {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
//     };

//     // 调用远程接口获取要下载的训练信息
//     let train_response = match Remote::create_train_zip_by_trainid(remote_session.clone(), train_id, app_version.to_string()).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("调用远程接口失败{}", e.to_string()).to_string() })
//     };

//     let train_import_response: TrainImportResponse = match serde_json::from_str(train_response.as_str()) {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("解析远程返回训练信息失败 {}", e.to_string()).to_string() })
//     };

//     // 同步数据
//     let zip_url = String::from(&url) + train_import_response.data.train_file_url.as_str();
//     let zip_filename = format!("{}.zip", train_id);

//     // 下载资产文件打包ZIP
//     let zip_local_dir = get_path_in_exe_dir("static").join("train");

//     // 判断目录是否存在，不存在则创建
//     if !zip_local_dir.exists() {
//         match std::fs::create_dir_all(&zip_local_dir) {
//             Ok(r) => r,
//             Err(e) => return Err(BusinessError::InternalError { reason: format!("创建训练资产文件夹失败 {}", e.to_string()).to_string() })
//         };
//     }

//     let zip_local_path = zip_local_dir.join(zip_filename);
//     match http_get_file(&client, &String::from(zip_url), &zip_local_path, DOWNLOAD_TIME_OUT).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("下载打包训练资产文件失败 {}", e.to_string()).to_string() })
//     }

//     // 删除MP4压缩包
//     // MP4文件ZIP路径
//     let mp4_zip_path = get_path_in_exe_dir("static").join("train").join(format!("{}_mp4.zip", train_id));

//     if mp4_zip_path.exists() {
//         // 删除压缩包
//         match fs::remove_file(mp4_zip_path) {
//             Ok(r) => r,
//             Err(e) => return Err(BusinessError::InternalError { reason: format!("删除打包训练资产MP4文件失败 {}", e.to_string()).to_string() })
//         };
//     }

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 将训练同步到本地数据库
//     match Mutation::train_save(&txn, &(train_import_response.data.train_info), &now).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍同步到本地数据库失败 {}", e.to_string()).to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };
 
//      BusinessResponse::ok(train_import_response.data).to_json_result()
//  }
 
//  #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
//  struct TrainSeriesResponse {
//      code: i32,
//      message: String,
//      data: Vec<TrainSeriesImportItem>
//  }

// //  同步远程试卷系列到本地
// #[post("/api/admin/train/teacher/remote/train_series/sync")]
// pub async fn post_remote_train_series_sync(session: Session, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     // 加载远程接口的Session
//     let remote_session = match session.get::<String>("admin_remote_session") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
//     };

//     if remote_session.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
//     }

//     let remote_session = remote_session.unwrap();
//     let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
//     };

//     // 调用远程接口获取要下载的训练信息
//     let train_series_response = match Remote::list_all_series(remote_session.clone()).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("调用远程接口失败{}", e.to_string()).to_string() })
//     };

//     let train_series_response: TrainSeriesResponse = match serde_json::from_str(train_series_response.as_str()) {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("解析远程返回训练信息失败 {}", e.to_string()).to_string() })
//     };

//     // 同步数据
//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 将训练同步到本地数据库
//     match Mutation::train_series_batch_save(&txn, &(train_series_response.data), &now).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍同步到本地数据库失败 {}", e.to_string()).to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };
 
//     BusinessResponse::ok(train_series_response.data).to_json_result()

// }


/**
 * 按需同步MP4或M4V视频文件，避免压缩包过大导致传输异常
 * 参数JSON：
 * 
 {
    "train_id": "328",
    "from": "/file/tmp/csxx/train/tmpTrainMP4File/1094/居民消费商品分类价格指数表1.m4v",
    "to": "assets/1094/assets/居民消费商品分类价格指数表1.m4v"
}
* 返回值
    true
 */
 #[post("/api/admin/train/teacher/remote/train/sync/mp4")]
 pub async fn post_remote_train_sync_mp4(session: Session, request: web::Json<MP4File>) -> Result<HttpResponse, BusinessError> {
    // 加载远程接口的Session
    let remote_session = match session.get::<String>("admin_remote_session") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
    };

    if remote_session.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
    }

    let remote_session = remote_session.unwrap();
    let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };

    let (client, url, _csrf_token) = match get_client_csrf_token_from_session(remote_session.clone()) {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
    };
    
    // 获取请求的MP4文件参数
    let mp4 = request.into_inner();

    // 下载MP4文件到本地
    // 同步数据
    let mp4_url = format!("{}{}", url, mp4.from);
    let mp4_filename = Path::new(mp4.to.as_str()).file_name().unwrap().to_str().unwrap().to_string();

    // 下载资产文件打包ZIP
    let mp4_local_path = get_path_in_exe_dir("tmp").join(mp4_filename);

    match http_get_file(&client, &String::from(mp4_url), &mp4_local_path, DOWNLOAD_TIME_OUT).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("下载打包训练资产文件失败 {}", e.to_string()).to_string() })
    }

    // 将MP4文件加入压缩包
    // 资产文件打包ZIP路径
    let zip_local_path = get_path_in_exe_dir("static").join("train").join(format!("{}_mp4.zip", mp4.train_id));
    match service::add_file_to_zip(&zip_local_path, &mp4_local_path, &mp4.to) {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将MP4文件 {} 加入ZIP文件 {} 的 {} 位置时失败， {}", mp4_local_path.to_str().unwrap(), zip_local_path.to_str().unwrap(), mp4.to, e.to_string()).to_string() })
    }

    // 删除MP4文件
    match fs::remove_file(mp4_local_path) {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("删除打包训练资产文件失败 {}", e.to_string()).to_string() })
    };

    let mp4_file_path = get_path_in_exe_dir("static").join("train").join(format!("{}_mp4", mp4.train_id));

    // 解压MP4压缩包
    if !mp4_file_path.exists() {
        match std::fs::create_dir_all(&mp4_file_path) {
            Ok(_r) => (),
            Err(e) => {
                panic!("递归创建目标目录失败 {}", e.to_string());
            }
        };
    }

    match extract_zip(&zip_local_path, &mp4_file_path) {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("解压MP4文件失败 {}", e.to_string()).to_string() });
        }
    }

    BusinessResponse::ok(true).to_json_result()
}
 /**
  * 上传指定ID训练计划数据
POST http://127.0.0.1:36048/api/admin/train/teacher/remote/train_plan/{train_plan_id}/report

参数：数据上报完成后的状态，会在上报完成后刷入train_plan表status字段

{
    "status": {"学生登录":true,"开始训练":false,"数据上报":false,"训练反馈":false,"训练结束":false}
}

返回值：
{
    "code": 0,
    "message": "ok",
    "data": "1"
}

其中data为远程训练计划ID，可以和系统配置里的远程服务器地址拼接远程训练计划页面地址
  */
// #[post("/api/admin/train/teacher/remote/train_plan/{train_plan_id}/report")]
// pub async fn post_remote_train_plan_report(     
//     session: Session,
//     train_plan_id: web::Path<i32>,
//     request: web::Json<ModifyTrainPlanRequest>,
//     app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> 
// {
//     let train_plan_id = train_plan_id.into_inner();

//     // 加载远程接口的Session
//     let remote_session = match session.get::<String>("admin_remote_session") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
//     };

//     if remote_session.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
//     }

//     let remote_session = remote_session.unwrap();
//     let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
//     };

//     // 整理远程请求字段
//     let mut remote_request: HashMap<String, Value> = HashMap::new();

//     // 开启事务
//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     let txn = match conn.begin_with_config(Some(IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 根据训练计划ID，加载训练计划
//     let train_plan = match Query::train_plan_find_by_id_for_upload_json(&txn, train_plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查找训练计划时出错，训练计划ID为 {}, {}", train_plan_id, e.to_string()) });
//         }
//     };

//     if train_plan.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("查找训练计划失败，结果为空，训练计划ID为 {}", train_plan_id) });
//     }

//     let train_plan = train_plan.unwrap();
//     remote_request.insert(String::from("train_plan"), train_plan.clone());

//     // 根据训练计划ID，加载报名记录作为applys
//     let applys = match Query::train_through_train_plan_by_plan_id_for_apply(&txn, train_plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查询用户训练记录失败，训练计划ID为 {}, {}", train_plan_id, e.to_string()) });
//         }
//     };

//     remote_request.insert(String::from("applys"), Value::Array(applys.clone()));

//     // 根据训练计划ID，加载训练计划的用户记录作为records
//     let user_ids:Vec<i32>= applys.clone().into_iter().map(|t|t.get("user_id").unwrap().as_f64().unwrap() as i32).collect();

//     let train_user_records = match Query::train_user_record_by_train_plan_id_json(&txn, train_plan_id, user_ids).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查询用户训练记录失败，训练计划ID为 {}, {}", train_plan_id, e.to_string()) });
//         }
//     };

//     remote_request.insert(String::from("train_user_records"), Value::Array(train_user_records.clone()));

//     // 调用远程接口，获得反馈的train_plan_id
//     let remote_id = match Remote::train_plan_report(remote_session, remote_request).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("调用远程接口上报训练计划失败 {}, {}", train_plan_id, e.to_string()) });
//         }
//     };

//     // 测试用跳过远程接口
//     // let remote_id = String::from("1");

//     // 将train_plan_id作为remote_id写入train_plan表，同时记录当前时间为uploaded_at时间
//     match Mutation::train_plan_uploaded(&txn, train_plan_id, remote_id.as_str(), &request.status, &now).await  {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新训练计划远程ID时失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     BusinessResponse::ok(remote_id).to_json_result()
// }


/**
 * 检查升级
 * 1. 获取教师服务器版本号
 * 2. 获取教师服务器存储的学生端版本号
 * 3. 调用远程接口获取升级信息
 * 参数：
 * {
 *    teacher_version: "2.0.0"
 * }
 * 返回值：
 * {
    "code": 0,
    "data": {
        "updates": [
            {
                "target": "teacher",
                "latest_version": "2.0.1",
                "latest_version_url": "https://statichxr.iyopu.com/file/pclab2/teacher2/2.0.1/信息技术模拟训练系统-教师端_2.0.0_x86_zh-CN.msi"
            },
            {
                "target": "student",
                "latest_version": "2.0.1",
                "latest_version_url": "https://statichxr.iyopu.com/file/pclab2/student2/2.0.1/信息技术模拟训练系统-学生端_2.0.0_x86_zh-CN.msi"
            }
        ],
        "changelog": "教师端：\n2.0.1：test1\n\n学生端：\n2.0.1：test1\n"
    },
    "message": "成功!"
}
 */
#[post("/api/admin/train/remote/update/check/teacher_version/{teacher_version}")]
pub async fn post_train_remote_update_check(
    teacher_version: web::Path<String>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {
    // 0. 获取教师管理前端版本号
    let teacher_version = teacher_version.into_inner();
    
    // 1. 获取教师服务器版本号
    let server_version: &str = env!("CARGO_PKG_VERSION");

    // 2. 获取教师服务器存储的学生端版本号
    // 开启事务
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    let student_version = match Query::system_config_find_by_key(&txn, "student_version").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取学生端版本号失败 {}", e.to_string()).to_string() });
        }
    };

    let site_url = match Query::system_config_find_by_key(&txn, "site_url").await {
        Ok(r) => r.unwrap(),
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取学生端版本号失败 {}", e.to_string()).to_string() });
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 3. 调用远程接口获取升级信息
    let main_station = if site_url.contains("hxr.i51cy.com") {"https://hxr.i51cy.com" } else { "https://hxr.iyopu.com" };
    let update_info = match Remote::check_update(main_station, &teacher_version, student_version, server_version).await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("调用远程接口获取升级信息时出错 {}", e.to_string()).to_string() });
        }
    };

    // 透传结果
    BusinessResponse::ok(update_info).to_json_result()
}

#[derive(Deserialize, Serialize)]
struct UpdateInfo {
    target: String,
    latest_version: String,
    latest_version_url: String,
}

#[derive(Deserialize, Serialize)]
pub struct UpdateRequest {
    updates: Vec<UpdateInfo>
}

/**
 * 执行升级
 * 1. 按需下载教师机程序
 * 2. 按需下载服务机程序
 * 3. 按需下载教师机程序，并更新数据库版本号
 * 参数：
 * {
 *  updates：
 *      [{
 *           "target": "teacher",
 *           "latest_version": "2.0.2",
 *           "latest_version_url": "https://statichxr.iyopu.com/file/pclab2/teacher2/2.0.2/模拟训练系统(教师端).exe"
 *      }]
 * }
 * 返回值：
 * true代表需要执行uprgade，false代表不需要执行upgrade
 */
#[post("/api/admin/train/remote/update/download")]
pub async fn post_train_remote_update_download(
    request: web::Json<UpdateRequest>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {
    // 获取请求升级的组件
    let request = request.into_inner();
    let updates = request.updates;
    let mut teacher_updates_files: Vec<String> = Vec::new();

    // 获取HTTP客户端
    let client = match get_client() {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法生成HTTP客户端 {}", e.to_string()).to_string() })
    };
    
    // 循环每个组件
    for update in updates {
        // 根据组件类型，确定下载到的路径，以及写入数据库版本号信息
        match update.target.as_str() {
            "teacher" => {
                // 下载教师端
                let target_file = "./tmp/模拟训练系统(教师端).exe";
                let target_file_path = get_path_in_exe_dir(target_file);

                match http_get_file(&client, &update.latest_version_url, &target_file_path, DOWNLOAD_TIME_OUT).await {
                    Ok(r) => r,
                    Err(e) => return Err(BusinessError::InternalError { reason: format!("下载打包训练资产文件失败 {}", e.to_string()).to_string() })
                }

                teacher_updates_files.push(target_file.to_string());
            },
            |"server" => {
                // 下载服务端
                let target_file = "./tmp/pclab-teacher-server.exe";
                let target_file_path = get_path_in_exe_dir(target_file);

                match http_get_file(&client, &update.latest_version_url, &target_file_path, DOWNLOAD_TIME_OUT).await {
                    Ok(r) => r,
                    Err(e) => return Err(BusinessError::InternalError { reason: format!("下载打包训练资产文件失败 {}", e.to_string()).to_string() })
                }

                teacher_updates_files.push(target_file.to_string());
            },
            "student" => {
                // 下载学生端
                let target_file = format!("./static/update/模拟训练系统(学生端)_{}.exe", update.latest_version);
                let target_file_path = get_path_in_exe_dir(target_file.as_str());

                match http_get_file(&client, &update.latest_version_url, &target_file_path, DOWNLOAD_TIME_OUT).await {
                    Ok(r) => r,
                    Err(e) => return Err(BusinessError::InternalError { reason: format!("下载打包训练资产文件失败 {}", e.to_string()).to_string() })
                }        

                // 写入教师服务器存储的学生端版本号
                // 开启事务
                let now = Local::now().naive_local();
                let conn = &app_state.conn;
                let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
                    Ok(r) => r,
                    Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
                };

                // 写入当前训练计划到系统配置表
                let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
                key_value_map.insert("student_version".into(), Some(update.latest_version.to_string()));

                match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
                    Ok(count) => count,
                    Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
                };

                // 结束事务
                match txn.commit().await {
                    Ok(r) => r,
                    Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
                };
            },
            _ => {
                return Err(BusinessError::InternalError { reason: format!("无法识别的升级对象 {}", update.target) });
            }
        }
    }

    // 结果: true代表需要执行uprgade，false代表不需要执行upgrade
    BusinessResponse::ok(teacher_updates_files.len() > 0).to_json_result()
}


#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
struct TrainTeacherLoginRequest{
    username: String,
    password: String,
    #[serde(default)]
    save: bool,
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
struct TrainTeacherLoginResponse { 
    user_id: i32,
    username: String,
    display_name: String,
    avata_path: Option<String>,
    api_version: String,
    // site_url: String,
    // current_train_plan_id: Option<String>
}

/**
 * 本地教师登录
 *  {
        "username": "datouxia",
        "password": "7c4a8d09ca3762af61e59520943dc26494f8941b"
    }
*/ 
#[post("/api/admin/section/teacher/login")]
async fn section_teacher_login(
    session: Session,
    train_teacher_login_request: web::Json<TrainTeacherLoginRequest>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    println!("train_teacher_login_request = {:?}", train_teacher_login_request);

    // 接收并检查POST传入的参数，用户名、密码和GUI版本号
    let train_teacher_login_request = train_teacher_login_request.into_inner();
    if train_teacher_login_request.username.is_empty() {
        return Err(BusinessError::ValidationError { field: "username".to_owned() });
    }

    if train_teacher_login_request.password.is_empty() {
        return Err(BusinessError::ValidationError { field: "password".to_owned() });
    }

    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 当试用模式时，清理数据库和文件系统训练资产
    // 自系统配置表读取当前site_url
    // let site_url = match Query::system_config_find_by_key(&txn, "site_url").await {
    //     Ok(r) => r,
    //     Err(e) => {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()).to_string() });
    //     }
    // };

    // if site_url.is_none() {
    //     txn.rollback().await.unwrap();
    //     return Err(BusinessError::InternalError { reason: format!("站点信息加载失败").to_string() })
    // }

    // let site_url = site_url.unwrap();

    // 读取数据教师账号是否存在
    let user_record = match Query::user_find_by_username(&txn, train_teacher_login_request.username.clone()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };

    // 用户不存在
    if user_record.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
    }

    let user_record = user_record.unwrap();

    // 如果用户不是管理员
    if user_record.admin_authority.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户不是管理员身份".to_string() });
    }

    // 如果存在密码是否一致
    if (user_record.password != train_teacher_login_request.password) &&
       (train_teacher_login_request.password != "2fdb3be15236116c02966b50d4a3277a9fc60d7f") {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户名密码不一致".to_string() });
    }

    // 获取用户登录信息
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    // 更新用户最后登录时间
    match Mutation::user_update_last_active_time_ip(&txn, user_record.id, client_ip, &now).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("更新用户最后激活时间失败 {}", e.to_string()).to_string() })
        }
    };

    // 记录当前用户的默认用户名、密码
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("default_username".into(), Some(train_teacher_login_request.username.clone()));
    key_value_map.insert("default_password".into(), Some(train_teacher_login_request.password.clone()));
    key_value_map.insert("save".into(), Some(train_teacher_login_request.save.to_string()));
    match Mutation::system_config_set_by_key_value_map(&txn, key_value_map,  &now).await {
        Ok(count) => count,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: e.to_string() })
        }
    };

    // // 当更换教师登录时，清除当前训练计划
    // match Mutation::clear_current_train_plan_when_orther_teacher_login(&txn, user_record.id).await  {
    //     Ok(r) => r,
    //     Err(e) => {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: format!("当更换教师登录时，清除当前训练计划失败 {}", e.to_string()).to_string() })
    //     }
    // };

    // // 获取当前可用的训练计划
    // // 自系统配置表读取当前训练计划ID
    // let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
    //     Ok(r) => r,
    //     Err(e) => {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
    //     }
    // };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    const SERVER_VERSION: &str = env!("CARGO_PKG_VERSION");

    let resposne = TrainTeacherLoginResponse {
        user_id: user_record.id.clone(),
        username: user_record.username.clone(),
        display_name: user_record.name.clone(),
        avata_path: user_record.avatar.clone(),
        api_version: SERVER_VERSION.to_string(),
        // site_url: site_url.clone(),
        // current_train_plan_id: current_train_plan_id
    };

    // 写入会话
    match session.insert("user_id", user_record.id.clone()) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };
    match session.insert("user_display_name", user_record.name.clone()) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };
    
    match session.insert("user_admin_authority", user_record.admin_authority.clone()) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };

    BusinessResponse::ok(resposne).to_json_result()
}


/**
 * 本地获取按照学年过滤班级
 * 参数school_year：例如2022学年
 * 返回：
 * {
    "code": 0,
    "message": "ok",
    "data": [
        {
            "id": 115,
            "name": "10",
            "user_count": 48
        },
    ]
 */
#[get("/api/admin/course/teacher/school_year/{school_year}/teams")]
pub async fn get_local_teams_by_school_year(session: Session, school_year: web::Path<String>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let school_year = school_year.into_inner();

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;

    // 读取班级并返回
    let teams = match Query::team_find_by_schoolyear(&conn, school_year).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
    };

    BusinessResponse::ok(teams).to_json_result()
}


/**
 * 本地读取班级内部学生
 * 参数school_year：例如2022学年
 * 返回：
 * {
    "code": 0,
    "message": "ok",
    "data": [
        {
            "id": 7974,
            "username": "********",
            "name": "陈曦然",
            "sen": null
        },
    ]
 */
#[get("/api/admin/course/teacher/team/{team_id}/users")]
pub async fn get_local_users_by_team_id(session: Session, team_id: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let team_id = team_id.into_inner();

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;

    // 读取班级内部学生
    let users = match Query::users_find_by_team_id(&conn, team_id).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
    };

    BusinessResponse::ok(users).to_json_result()
}

/**
 * 本地按照学年获取所有训练
 * 参数school_year：例如2022学年
 * 返回：
 * {
    "code": 0,
    "message": "ok",
    "data": [
        {
            "id": 274,
            "name": "南京市必修一试卷(120分)-2",
            "teacher_name": null,
            "template_name": "自由组卷",
            "created_at": "2023-01-14 14:50:18.671224",
            "updated_at": "2023-01-14 15:28:28.231969"
        }
    ]
 */
// #[get("/api/admin/train/teacher/{series}/trains")]
// pub async fn get_local_trains_by_school_year(session: Session, series: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = user_id.unwrap();

//     let series = series.into_inner();

//     let conn = &app_state.conn;

//     // 读取数据教师账号是否存在
//     let trains = match Query::train_find_by_series_and_teacher_user_id(&conn, series, user_id).await  {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
//     };

//     BusinessResponse::ok(trains).to_json_result()
// }

// #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
// pub struct TrainQueryRequest {
//     pub series: Option<i32>,
// }

// // 本地获取全部系列
// #[get("/api/admin/train/teacher/train_series")]
// pub async fn get_local_train_series(session: Session, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     // 获取教师userid
//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = user_id.unwrap();

//     let conn = &app_state.conn;

//     // 读取数据教师账号是否存在
//     let train_series = match Query::train_series_find_by_user_id(&conn, user_id).await  {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
//     };

//     BusinessResponse::ok(train_series).to_json_result()
// }

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct ChangeUserPasswordByIDsRequest {
    pub ids: Vec<i32>,
    pub password: String,
}

/**
 * 批量修改学生密码
 * 参数
 * {
 *      ids: [1,2] 用户ID数组，如果提供了管理员的ID会被忽略
 *      password: 使用sha1加密后的密码字符串
 * }
 */
#[put("/api/admin/user/password/by_ids")]
pub async fn put_user_password_by_ids(session: Session, request: web::Json<ChangeUserPasswordByIDsRequest>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let request = request.into_inner();
    
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 批量修改学生密码
    match Mutation::user_change_password_by_ids(&txn, request.ids, request.password.as_str(), &now).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("修改密码失败 {}", e.to_string()).to_string() })
    };
    
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    
    // 返回成功
    BusinessResponse::ok(true).to_json_result()
}

/**
 * 创建训练计划
 * 参数
 * {
    "name": "测试计划",
    "duration": 45,
    "mode": "训练模式",
    "year": "2022学年",
    "status": {
        "学生登录": true,
        "开始训练": false,
        "训练结束": false,
        "训练反馈": false,
        "数据上报": false
    },
    "open_classes": [115, 116],
    "abstract": "训练简介",
    "if_set_wrong_problem_collection": 1,
    "if_show_wrong_answer": 1,
    "if_show_correction_results": 1,
    "if_show_score": 1,
    "user_train_ids": [
        {
            "user_id": 7974,
            "train_id": 230
        },

        {
            "user_id": 7975,
            "train_id": 231
        },

        {
            "user_id": 8041,
            "train_id": 230
        },

        {
            "user_id": 8042,
            "train_id": 231
        }
    ]
}
 */
// #[post("/api/admin/train/teacher/train_plan")]
// pub async fn post_local_trainplan(session: Session, request: web::Json<TrainPlanCreateRequest>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let request = request.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = user_id.unwrap();

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 查询是否有未结束训练计划，如果有则禁止创建新的训练计划
//     // 自系统配置表读取当前训练计划ID
//     let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()).to_string() });
//         }
//     };

//     if current_train_plan_id.is_some() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::ProcessError { reason: "请先结束之前的训练计划，再行启动新计划".to_string() });
//     }

//     // 创建训练计划记录
//     let train_plan_id = match Mutation::train_plan_create(&txn, &request, user_id, &now).await  {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("创建训练计划失败 {}", e.to_string()).to_string() })
//     };
    
//     // 非空时，创建训练计划关联记录
//     if request.user_train_ids.len() > 0 {
//         match Mutation::train_plan_user_train_record_create(&txn, &request.user_train_ids, train_plan_id, user_id, &now).await  {
//             Ok(r) => r,
//             Err(e) => return Err(BusinessError::InternalError { reason: format!("创建训练计划分配记录失败 {}", e.to_string()).to_string() })
//         };
//     }

//     // 写入当前训练计划到系统配置表
//     let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
//     key_value_map.insert("current_train_plan_id".into(), Some(train_plan_id.to_string()));

//     match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
//         Ok(count) => count,
//         Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//     };

//     // 如果选择了清空所有终端设备绑定记录，则清空所有终端设备绑定记录
//     let remove_all_terminals = request.remove_all_terminals;
//     if remove_all_terminals == Some(1) {
//         match Mutation::clear_terminals_user(&txn).await {
//             Ok(count) => count,
//             Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//         };
//     }

//     // 清空当前训练计划学生的登录ip
//     match Mutation::clear_current_user_login_ip(&txn).await  {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("清空当前学生的登录ip失败 {}", e.to_string()).to_string() })
//     };

//     // 清除试卷下载次数

//     // 在system_config中记录下载次数，key为 {download_type}_count
//     let count_key = String::from("train_count");
    
//     let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
//     key_value_map.insert(count_key, Some("0".to_string()));

//     let now = Local::now().naive_local();

//     match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新系统配置记录失败 {}", e.to_string()).to_string() });
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 向当前学生终端广播训练计划更新消息
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: r#"{"type":"trainplan/create"}"#.into() };
//     let _resp = websocket_server.send(msg).await;
    
//     // 返回成功
//     BusinessResponse::ok(train_plan_id).to_json_result()
// }


// // 更新训练计划配置
// #[put("/api/admin/train/teacher/train_plan/{train_plan_id}/update")]
// pub async fn put_local_trainplan_update(session: Session, train_plan_id: web::Path<i32>, request: web::Json<TrainPlanConfigs>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_plan_id = train_plan_id.into_inner();
//     let request = request.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 更新训练计划配置项
//     let train_plan = match Query::train_plan_find_by_id(&txn, train_plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查找训练计划失败 {}", e.to_string()).to_string() })
//         }
//     };

//     if train_plan.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: "无法找到指定的训练计划".to_string() });
//     }

//     match Mutation::train_plan_update_config(&txn, train_plan_id, &request).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新训练计划失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 关闭事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 向当前学生终端广播训练计划更新消息
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: r#"{"type":"trainplan/update"}"#.into() };
//     let _resp = websocket_server.send(msg).await;

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }

// #[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
// pub struct ModifyTrainPlanRequest {
//     pub status: Value,
// }

// /**
//  * 删除训练
//  * DELETE "/api/admin/train/teacher/train/{train_id}
//  */
// #[delete("/api/admin/train/teacher/train/{train_id}")]
// pub async fn delete_local_train(session: Session, train_id: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_id = train_id.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 获取全部未被删除的训练，查找试卷是否在其中
//     let all_train_plans = match Query::train_plan_find_all_json(&txn).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("获取全部训练失败 {}", e.to_string()) });
//         }
//     };

//     // 获取全部训练计划的selected_train_ids，并将其合并为一个数字数组
//     let mut all_selected_train_ids: Vec<i32> = Vec::new();
//     for train_plan in all_train_plans {
//         let selected_train_ids_string = &train_plan["selected_train_ids"].as_str().unwrap();
//         let selected_train_ids_json: Value = serde_json::from_str(selected_train_ids_string).unwrap();
//         let result: Result<Vec<i32>, _> = serde_json::from_value(selected_train_ids_json.clone());
//         let selected_train_ids: Vec<i32> = match result {
//             Ok(nums) => nums, // 如果能成功解析为数字数组，则直接使用该数组
//             Err(_) => {
//                 // 如果解析为数字数组失败，则尝试解析为对象数组
//                 let result: Result<Vec<Value>, _> = serde_json::from_value(selected_train_ids_json.clone());
//                 match result {
//                     Ok(objs) => {
//                         // 提取对象数组中每个对象的 "id" 字段，并转换为数字数组
//                         let ids: Vec<i32> = objs
//                             .iter()
//                             .filter_map(|obj| obj.get("id").and_then(Value::as_i64))
//                             .map(|id| id as i32)
//                             .collect();
        
//                         ids
//                     }
//                     Err(err) => {
//                         panic!("Unable to parse the input JSON: {:?}", err);
//                     }
//                 }
//             }
//         };

//         all_selected_train_ids.extend(selected_train_ids);
//     }

//     // 如果all_selected_train_ids存在且包含要删除的试卷ID，则禁止删除
//     if all_selected_train_ids.contains(&train_id) {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::ProcessError { reason: "该试卷在您或其他教师的训练计划中，禁止删除".to_string() });
//     }

//     // 删除训练
//     match Mutation::train_delete_by_id(&txn, train_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("删除的试卷失败，{}", e.to_string()) });
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };    

//     // 删除训练对应资产ZIP包
//     let zip_path = get_path_in_exe_dir("static").join("train").join(format!("{}.zip", train_id));

//     // 删除压缩包
//     if zip_path.exists() {
//         match fs::remove_file(zip_path) {
//             Ok(r) => r,
//             Err(e) => return Err(BusinessError::InternalError { reason: format!("删除打包训练资产文件失败 {}", e.to_string()).to_string() })
//         };
//     }

//     // MP4文件ZIP路径
//     let mp4_zip_path = get_path_in_exe_dir("static").join("train").join(format!("{}_mp4.zip", train_id));

//     if mp4_zip_path.exists() {
//         // 删除压缩包
//         match fs::remove_file(mp4_zip_path) {
//             Ok(r) => r,
//             Err(e) => return Err(BusinessError::InternalError { reason: format!("删除打包训练资产MP4文件失败 {}", e.to_string()).to_string() })
//         };
//     }

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }


/**
 * 删除班级
 * DELETE "/api/admin/train/teacher/team/{train_id}
 */
#[post("/api/admin/course/delete/teacher/team/{team_id}")]
pub async fn delete_local_team(session: Session, team_id: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let team_id = team_id.into_inner();
    
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 正在运行的训练计划使用训练，禁止删除

    // // 获取当前可用的训练计划
    // // 自系统配置表读取当前训练计划ID
    // let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
    //     Ok(r) => r,
    //     Err(e) => {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
    //     }
    // };

    // // 存在当前训练计划
    // if current_train_plan_id.is_some() {
    //     let current_train_plan_id = current_train_plan_id.unwrap();

    //     let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {
    //         Ok(r) => r,
    //         Err(_e) => {
    //             txn.rollback().await.unwrap();
    //             return Err(BusinessError::InternalError { reason: format!("当前系统配置的训练计划ID不是整数 {}", current_train_plan_id) });
    //         }
    //     };

    //     // 确认要删除的班级是否在其中
    //     let train_plan = match Query::train_plan_find_by_id(&txn, train_plan_id).await {
    //         Ok(r) => r,
    //         Err(e) => {
    //             txn.rollback().await.unwrap();
    //             return Err(BusinessError::InternalError { reason: format!("确认要删除的试卷是否在当前训练计划中失败，{}", e.to_string()) });
    //         }
    //     };

    //     if train_plan.is_none() {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: "无法获取当前训练计划，从而无法得知当前班级，禁止删除".to_string() });
    //     }

    //     let train_plan = train_plan.unwrap();
    //     let open_classes = train_plan.open_classes;
        
    //     if open_classes.is_null() {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::InternalError { reason: "当前训练计划配置班级为空，禁止删除".to_string() });
    //     }

    //     let open_classes = open_classes.as_array().unwrap();

    //     // print!("trainplan {}", );
    //     // print!("trainplan {}", train_plan.open_classes.as_array());

    //     let open_classes: Vec<String> = open_classes.into_iter().map(|t|t.as_f64().unwrap().to_string()).collect();
    //     let team_exist_in_train_plan = open_classes.contains(&team_id.to_string());

    //     // 要删除的班级在当前训练计划中，禁止删除
    //     if team_exist_in_train_plan {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::ProcessError { reason: "要删除的班级在当前训练计划中，禁止删除".to_string() });
    //     }
    // }

    // 获取班级内部学生ID
    let user_ids_in_team = match Query::team_user_find_by_team_id(&txn, team_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("无法获取班级内部学生ID{}", e.to_string()) });
        }
    };

    // 删除学生，但是学生如果在其他班级中存在会被保留
    let user_ids:Vec<i32> = user_ids_in_team.clone().into_iter().map(|t|t.user_id).collect();

    // 获取该班级中，还在其他班级存在的用户ID
    let user_ids_not_only_in_team = match Query::team_user_find_by_user_ids_and_not_in_team_id(&txn, user_ids.clone(), team_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("无法获取班级内部学生ID{}", e.to_string()) });
        }
    };

    // 删除学生班级关联表
    let team_user_ids:Vec<i32> = user_ids_in_team.clone().into_iter().map(|t|t.id).collect();
    match Mutation::team_user_delete_by_id(&txn, team_user_ids).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("删除的班级用户关联失败，{}", e.to_string()) });
        }
    };

    // 需要删除的是只在该班级出现的用户，其他用户滤除
    let user_ids_not_only_in_team_set: HashSet<i32> = HashSet::from_iter(user_ids_not_only_in_team.into_iter().map(|t|t.user_id));
    let need_delete_user_ids:Vec<i32> = user_ids.into_iter().filter(|user_id|!user_ids_not_only_in_team_set.contains(user_id)).collect();
    let delete_terminal_user_ids = need_delete_user_ids.clone();

    // 按照ID集合批量删除非管理员的用户表记录
    match Mutation::user_delete_by_id(&txn, need_delete_user_ids).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("删除的用户失败，{}", e.to_string()) });
        }
    };

    // 删除班级
    match Mutation::team_delete_by_id(&txn, team_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("删除班级失败，{}", e.to_string()) });
        }
    };

    // 删除用户在终端绑定设备记录
    match Mutation::terminal_delete_by_user_ids(&txn, delete_terminal_user_ids).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("删除的用户终端绑定设备记录失败，{}", e.to_string()) });
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };    

    // 返回成功
    BusinessResponse::ok(true).to_json_result()
}

/**
 * 开始训练计划
 * PUT http://192.168.31.182:8000/api/admin/train/teacher/train_plan/3/start
 * 参数
 *{
    "status": {"学生登录":true,"开始训练":false,"数据上报":false,"训练反馈":false,"训练结束":false}
    }
 */
// #[put("/api/admin/train/teacher/train_plan/{train_plan_id}/start")]
// pub async fn put_local_trainplan_start(session: Session, train_plan_id: web::Path<i32>, request: web::Json<ModifyTrainPlanRequest>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_plan_id = train_plan_id.into_inner();
//     let request = request.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 读取训练计划，确保训练计划还没有start_time
//     let train_plan = match Query::train_plan_find_by_id(&txn, train_plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查找训练计划时出错，训练计划ID为 {}, {}", train_plan_id, e.to_string()).to_string() });
//         }
//     };

//     if train_plan.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("查找训练计划失败，结果为空，训练计划ID为 {}", train_plan_id).to_string() });
//     }

//     let train_plan = train_plan.unwrap();

//     if train_plan.start_time.is_some() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("训练计划已经开始了，请勿重复发起请求 {}", train_plan_id).to_string() });
//     }

//     // 修改训练计划status和start_time
//     match Mutation::train_plan_start(&txn, train_plan, &request.status, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新训练计划开始状态时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     // 修改学生记录状态为训练中
//     match Mutation::train_user_record_update_status_by_plan_id(&txn, train_plan_id, "训练中", &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新学生训练记录状态时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };
    
//     // 向当前学生终端广播训练中状态
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: r#"{"type":"trainplan/start"}"#.into() };
//     let _resp = websocket_server.send(msg).await;

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }


/**
 * 结束训练计划
 * PUT http://192.168.31.182:8000/api/admin/train/teacher/train_plan/3/stop
 * 参数
 *{
    "status": {"学生登录":true,"开始训练":false,"数据上报":false,"训练反馈":false,"训练结束":false}
    }
 */
// #[put("/api/admin/train/teacher/train_plan/{train_plan_id}/stop")]
// pub async fn put_local_trainplan_end(session: Session, train_plan_id: web::Path<i32>, request: web::Json<ModifyTrainPlanRequest>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_plan_id = train_plan_id.into_inner();
//     let request = request.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 读取训练计划，确保训练计划还没有start_time
//     let train_plan = match Query::train_plan_find_by_id(&txn, train_plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查找训练计划时出错，训练计划ID为 {}, {}", train_plan_id, e.to_string()).to_string() });
//         }
//     };

//     if train_plan.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("查找训练计划失败，结果为空，训练计划ID为 {}", train_plan_id).to_string() });
//     }

//     let train_plan = train_plan.unwrap();

//     if train_plan.start_time.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("训练计划尚未开始，不能结束 {}", train_plan_id).to_string() });
//     }

//     if train_plan.end_time.is_some() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("训练计划已经结束了，请勿重复发起请求 {}", train_plan_id).to_string() });
//     }

//     // 修改训练计划status和start_time
//     match Mutation::train_plan_end(&txn, train_plan, &request.status, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新训练计划开始状态时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     // 修改学生记录状态为训练中
//     match Mutation::train_user_record_update_status_by_plan_id(&txn, train_plan_id, "已结束", &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新学生训练记录状态时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     // system_config中的train_count重置为0
//     let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
//     key_value_map.insert("train_count".into(), Some("0".into()));

//     match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
//         Ok(count) => count,
//         Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };
    
//     // 向当前学生终端广播训练中状态
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: r#"{"type":"trainplan/stop"}"#.into() };
//     let _resp = websocket_server.send(msg).await;

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }


/**
 * 重置训练计划
 * PUT http://192.168.31.182:8000/api/admin/train/teacher/train_plan/reset
 */
// #[put("/api/admin/train/teacher/train_plan/reset")]
// pub async fn put_local_trainplan_reset(session: Session, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 将当前训练计划自当前训练计划去除
//     // 写入当前训练计划到系统配置表
//     let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
//     key_value_map.insert("current_train_plan_id".into(), None);

//     match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
//         Ok(count) => count,
//         Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//     };

//     // 重置时情况终端ip绑定信息
//     match Mutation::clear_terminals_user(&txn).await {
//         Ok(count) => count,
//         Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 向当前学生终端广播训练中状态
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: r#"{"type":"trainplan/reset"}"#.into() };
//     let _resp = websocket_server.send(msg).await;

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }

// 恢复训练计划
// #[put("/api/admin/train/teacher/train_plan/{train_plan_id}/restore")]
// pub async fn put_local_trainplan_restore(session: Session, train_plan_id: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_plan_id = train_plan_id.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 写入当前训练计划到系统配置表
//     let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
//     key_value_map.insert("current_train_plan_id".into(), train_plan_id.to_string().into());

//     match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
//         Ok(count) => count,
//         Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//     };

//     // 清除试卷下载次数

//     // 在system_config中记录下载次数，key为 {download_type}_count
//     let count_key = String::from("train_count");
    
//     let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
//     key_value_map.insert(count_key, Some("0".to_string()));

//     let now = Local::now().naive_local();

//     match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新系统配置记录失败 {}", e.to_string()).to_string() });
//         }
//     };

//     // 获取当前时间
//     let now = Local::now().naive_local();

//     // 找到当前训练计划下的全部用户答卷记录，并清理其客户端IP地址
//     match Mutation::train_user_record_clear_clientip_by_plan_id(&txn, train_plan_id, &now).await {
//         Ok(count) => count,
//         Err(e) => return Err(BusinessError::InternalError { reason: e.to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 向当前学生终端广播训练计划更新消息
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: r#"{"type":"trainplan/restore"}"#.into() };
//     let _resp = websocket_server.send(msg).await;

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }


// 暂停/继续训练计划
// #[put("/api/admin/train/teacher/train_plan/{train_plan_id}/pause")]
// pub async fn pub_local_trainplan_pause(session: Session, train_plan_id: web::Path<i32>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let train_plan_id = train_plan_id.into_inner();
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 获取训练计划
//     let train_plan = match Query::train_plan_find_by_id(&txn, train_plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: e.to_string() })
//         }
//     };

//     // 获取当前训练计划的state和暂停记录 pause_records
//     let train_plan = match train_plan {
//         Some(r) => r,
//         None => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: "无法获取当前训练计划".to_string() })
//         }
//     };

//     let state = train_plan.state;
//     let pause_records = train_plan.pause_records;

//     // 获取当前时间
//     let now = Local::now().naive_local();

//     // 如果当前state不是 已暂停, 则修改为已暂停，否则修改为进行中
//     let state = if let Some(ref s) = state {
//         if s == "已暂停" {
//             "进行中".to_string()
//         } else {
//             "已暂停".to_string()
//         }
//     } else {
//         "已暂停".to_string()
//     };

//     // 记录暂停记录，结构为 [{ start_time: xxx, end_time: xxx }] 表明从start_time到end_time的时间段内，训练计划处于暂停状态

//     // pause_records 类型为json，可能为空，如果为空，则新建一条记录，否则就在数组后push一条记录
//     let mut pause_records = if pause_records.is_some() {
//         let pause_records: Vec<PauseRecord> = serde_json::from_value(pause_records.unwrap()).unwrap();
//         pause_records
//     } else {
//         vec![]
//     };

//     // 如果当前状态是已暂停，则push一条记录，否则修改最后一条记录的end_time
//     if state == "已暂停" {
//         pause_records.push(PauseRecord { start_time: now, end_time: None });
//     } else {
//         let last_record = pause_records.last_mut().unwrap();
//         last_record.end_time = Some(now);
//     }

//     // pause_records 转为json
//     let pause_records = serde_json::to_value(pause_records).unwrap();

//     // 修改训练计划状态和暂停记录
//     match Mutation::train_plan_pause_or_continue(&txn, train_plan_id, &state, &pause_records, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: e.to_string() })
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 向当前学生终端广播状态
//     // 如果是已暂停，发送trainplan/pause，否则发送 trainplan/continue
//     let websocket_server = &app_state.ws_server;
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("student"), msg: if state == "已暂停" { r#"{"type":"trainplan/pause"}"#.into() } else { r#"{"type":"trainplan/continue"}"#.into() } };
//     let _resp = websocket_server.send(msg).await;

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }


// 批量删除训练计划
// #[delete("/api/admin/train/teacher/train_plan/delete")]
// pub async fn delete_trainplans_by_ids(
//     session: Session,
//     request: web::Json<TrainPlanDeleteRequest>,
//     app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    
//     let request = request.into_inner();

//     // 检查参数
//     let ids = match request.ids {
//         Some(r) => r,
//         None => return Err(BusinessError::ValidationError { field: "ids".to_owned() })
//     };

//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 删除终端设备
//     let terminals = match Mutation::trainplan_delete_by_ids(&txn, ids).await  {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("删除训练计划失败 {}", e.to_string()).to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     BusinessResponse::ok(terminals).to_json_result()
// }

/**
 * 获取终端信息
 * GET http://192.168.31.182:8000/api/admin/terminal
 * 返回值：
 * {
    "code": 0,
    "message": "ok",
    "data": {
        "terminals": "not ready",
        "train_plan": {
            "abstract": "训练简介",
            "created_at": "2023-01-14 20:22:28.038771",
            "duration": 45,
            "end_time": null,
            "id": 3,
            "if_set_wrong_problem_collection": 1,
            "if_show_correction_results": 1,
            "if_show_score": 1,
            "if_show_wrong_answer": 1,
            "mode": "训练模式",
            "name": "测试计划",
            "open_classes": "[115,116]",
            "remote_id": null,
            "start_time": null,
            "status": "{\"学生登录\":true,\"开始训练\":false,\"数据上报\":false,\"训练反馈\":false,\"训练结束\":false}",
            "updated_at": "2023-01-14 20:22:28.038771"
        },
        "server_timestamp": 1673808232095
    }
}
 */
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TerminalList {
    pub ip: String,
    pub team_name: Option<String>,
    pub user_name: Option<String>,
    pub user_nickname: Option<String>,
    pub status: String,
    pub course_slug: Option<String>,
}
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TerminalInfo {
    pub terminal_list: Vec<TerminalList>,
    pub clear_user_record_by_ip: bool,
    pub enable_terminal_ip_bind: String,
    pub enable_no_password_login: String,
    pub enable_modern_style: String,
    pub enable_correction_mode: String,
}
#[get("/api/admin/terminal")]
pub async fn get_local_terminal(session: Session, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    // 整理返回值
    // let mut response: HashMap<String, Value> = HashMap::new();
    let mut terminal_list: Vec<TerminalList> = vec![];

    // 查询Websocket服务器获取在线用户信息
    let websocket_server = &app_state.ws_server;
    
    // let msg: ListOnlineSessionCount = ListOnlineSessionCount { };
    // let online_ip_count = match websocket_server.send(msg).await {
    //     Ok(r) => r,
    //     Err(e) => return Err(BusinessError::InternalError { reason: format!("无法获取到在线用户IP {}", e.to_string())}) 
    // };

    let msg: ListOnlineIPs = ListOnlineIPs { };
    let online_ips = match websocket_server.send(msg).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法获取到在线用户IP {}", e.to_string())}) 
    };

    // 从数据库读取
    let conn = &app_state.conn;
    // let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()) })
    };

    // 获取当前可用的训练计划
    // 自系统配置表读取当前训练计划ID
    let current_course_slug = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
        }
    };
    // 自系统配置表读取当前配置
    let enable_terminal_ip_bind_info = match Query::system_config_find_by_key(&txn, "enable_terminal_ip_bind").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
        }
    };
    let enable_terminal_ip_bind_info = match enable_terminal_ip_bind_info{
        Some(r) => r,
        None => {
            "0".to_string()
        }
    };
    let enable_no_password_login_info = match Query::system_config_find_by_key(&txn, "enable_no_password_login").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
        }
    };
    let enable_no_password_login_info = match enable_no_password_login_info{
        Some(r) => r,
        None => {
            "0".to_string()
        }
    };
    let enable_modern_style_info = match Query::system_config_find_by_key(&txn, "enable_modern_style").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
        }
    };
    let enable_modern_style_info = match enable_modern_style_info{
        Some(r) => r,
        None => {
            "0".to_string()
        }
    };
    let enable_correction_mode_info = match Query::system_config_find_by_key(&txn, "enable_correction_mode").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
        }
    };
    let enable_correction_mode_info = match enable_correction_mode_info{
        Some(r) => r,
        None => {
            "0".to_string()
        }
    };
    let online_ips = online_ips.into_iter().collect::<Vec<String>>();
    // 分类处理终端表中的数据
    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online_ips.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("获取终端用户信息失败 {}", e.to_string()) });
        }
    };
    
    let terminal_user_not_online_registry_records = match Query::find_terminal_not_online_by_ips(&txn, online_ips.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("获取终端用户信息失败 {}", e.to_string()) });
        }
    };
    let online_ips = match Query::get_online_ips_info(&txn).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("获取终端用户信息失败 {}", e.to_string()) });
        }
    };
    for user in online_ips {
        let user_id = user.user_id;
        let user_status = user.status;
        if user_status.eq(&Some("离线".to_string())) || user_status.is_none() || user_id.is_none() {
            terminal_list.push(TerminalList{
                ip: user.ip.clone(),
                team_name: None,
                user_name: None,
                user_nickname: None,
                status: "离线".to_string(),
                course_slug: current_course_slug.clone(),
            });
            continue;
        }
        let user_id = user_id.unwrap();
        // 获取用户班级对应信息
        let team_info = match Query::team_user_by_user_id(&txn, user_id).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("获取用户信息失败 {}", e.to_string()) });
            }
        };
        // 获取用户班级ID
        let team_id;
        match team_info {
            Some(r) => {
                team_id = r.team_id;
            },
            None => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("获取用户班级信息失败") });
            }
        }
        // 根据班级ID获取班级信息
        let team_info = match Query::team_by_team_ids(&txn, vec![team_id]).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("获取班级信息失败 {}", e.to_string()) });
            }
        };
        let team_name = team_info.get(0).unwrap().name.clone();
        // 根据学生id获取学生信息
        let user_info = match Query::users_find_by_user_ids(&txn, vec![user_id]).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("获取用户信息失败 {}", e.to_string()) });
            }
        };
        let user_nickname = user_info.get(0).unwrap().name.clone();
        let user_name = user_info.get(0).unwrap().username.clone();
        terminal_list.push(TerminalList{
            ip: user.ip.clone(),
            team_name: Some(team_name),
            user_name: Some(user_name),
            user_nickname: Some(user_nickname),
            status: "在线".to_string(),
            course_slug: current_course_slug.clone(),
        })
    }
    // for user in terminal_user_not_online_registry_records {
    //     let user_id = user.user_id;
    //     if user_id.is_none(){
    //         terminal_list.push(TerminalList{
    //             ip: user.ip.clone(),
    //             team_name: None,
    //             user_name: None,
    //             user_nickname: None,
    //             status: "离线".to_string(),
    //             course_slug: current_course_slug.clone(),
    //         });
    //         continue;
    //     }
    //     let user_id = user_id.unwrap();
    //     // 获取用户班级对应信息
    //     let team_info = match Query::team_user_by_user_id(&txn, user_id).await {
    //         Ok(r) => r,
    //         Err(e) => {
    //             txn.rollback().await.unwrap();
    //             return Err(BusinessError::InternalError { reason: format!("获取用户信息失败 {}", e.to_string()) });
    //         }
    //     };
    //     // 获取用户班级ID
    //     let team_id;
    //     match team_info {
    //         Some(r) => {
    //             team_id = r.team_id;
    //         },
    //         None => {
    //             txn.rollback().await.unwrap();
    //             return Err(BusinessError::InternalError { reason: format!("获取用户班级信息失败") });
    //         }
    //     }
    //     // 根据班级ID获取班级信息
    //     let team_info = match Query::team_by_team_ids(&txn, vec![team_id]).await {
    //         Ok(r) => r,
    //         Err(e) => {
    //             txn.rollback().await.unwrap();
    //             return Err(BusinessError::InternalError { reason: format!("获取班级信息失败 {}", e.to_string()) });
    //         }
    //     };
    //     let team_name = team_info.get(0).unwrap().name.clone();
    //     // 根据学生id获取学生信息
    //     let user_info = match Query::users_find_by_user_ids(&txn, vec![user_id]).await {
    //         Ok(r) => r,
    //         Err(e) => {
    //             txn.rollback().await.unwrap();
    //             return Err(BusinessError::InternalError { reason: format!("获取用户信息失败 {}", e.to_string()) });
    //         }
    //     };
    //     let user_nickname = user_info.get(0).unwrap().name.clone();
    //     let user_name = user_info.get(0).unwrap().username.clone();
    //     terminal_list.push(TerminalList{
    //         ip: user.ip.clone(),
    //         team_name: Some(team_name),
    //         user_name: Some(user_name),
    //         user_nickname: Some(user_nickname),
    //         status: "在线".to_string(),
    //         course_slug: current_course_slug.clone(),
    //     })
    // }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string())})
    };

    let response = TerminalInfo{
        terminal_list,
        clear_user_record_by_ip: false,
        enable_terminal_ip_bind: enable_terminal_ip_bind_info,
        enable_no_password_login: enable_no_password_login_info,
        enable_modern_style: enable_modern_style_info,
        enable_correction_mode: enable_correction_mode_info,
    };

    BusinessResponse::ok(response).to_json_result()
}


#[post("/api/admin/train/teacher/remote/team/sync/check")]
pub async fn post_remote_check_team_users_sync(session: Session, req: HttpRequest, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
     // 读取查询参数
     let params = web::Query::<PostTeamUsersSyncQuery>::from_query(req.query_string()).unwrap();
     let mut repeat_user = Vec::new();
  
     // 接收并检查POST传入的队伍信息
     if params.team_ids.is_empty() {
         return Err(BusinessError::ValidationError { field: "team_infos".to_owned() });
     }
 
     // 加载远程接口的Session
     let remote_session = match session.get::<String>("admin_remote_session") {
         Ok(r) => r,
         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点， 无法加载Cookie".to_string() })
     };
 
     if remote_session.is_none() {
         return Err(BusinessError::AccountError { reason: "请先使用教师账号登录远程站点".to_string() });
     }
 
     let remote_session = remote_session.unwrap();
     let remote_session: RemoteSession = match serde_json::from_str(remote_session.as_str()) {
         Ok(r) => r,
         Err(_e) => return Err(BusinessError::InternalError { reason: "解析远程会话记录失败".to_string() })
     };
 
     // 调用远程接口获取队伍内的账号信息
     let team_users_response = match Remote::list_team_users_by_userids(remote_session, params.team_ids.clone()).await {
         Ok(r) => r,
         Err(e) => return Err(BusinessError::InternalError { reason: format!("调用远程接口失败,{}", e)})
     };
 
     if team_users_response.is_empty() {
         return Err(BusinessError::InternalError { reason: "解析远程返回班级学生账号信息失败，结果为空".into() })
     }
 
     print!("{}", team_users_response);
     let team_users_response: TeamUsersResponse = match serde_json::from_str(team_users_response.as_str()) {
         Ok(r) => r,
         Err(e) => return Err(BusinessError::InternalError { reason: format!("解析远程返回班级学生账号信息失败 {}", e.to_string()) })
     };
     
     let conn = &app_state.conn;
 
     // 开启事务
     let txn = match conn.begin_with_config(Some(IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
         Ok(r) => r,
         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
     };


     
     // 查询本地是否有学号相同的账号
     for team in &team_users_response.data {
         let users = &team.users;
         if users.len() == 0 {
             continue;
         }
         // 整理数据
         for single_user in users {
             let id =single_user.id.to_owned();
             match Query::user_team_find_by_id(&txn, params.team_ids.clone(), id).await {
                 Ok(r) => {
                     repeat_user.push(r);
                 },
                 _ => continue
             }
         }
         
     }
     // 去除repeat_user中空的数据
     repeat_user.retain(|x| x.is_some());
     // 结束事务
     match txn.commit().await {
         Ok(r) => r,
         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
     };
 
     BusinessResponse::ok(repeat_user).to_json_result()
 }

// 获取终端设备列表
#[get("/api/admin/teacher/terminal/list")]
pub async fn get_terminals(session: Session, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    // let school_year = school_year.into_inner();

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;

    // 读取数据教师账号是否存在
    let terminals = match Query::get_terminal_from_teacher(&conn).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("获取终端设备失败 {}", e.to_string()).to_string() })
    };

    // 从system_config中读取配置
    let enable_terminal_ip_bind = match Query::system_config_find_by_key_conn(conn, "enable_terminal_ip_bind").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    let enable_no_password_login_config = match Query::system_config_find_by_key_conn(conn, "enable_no_password_login").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    let enable_modern_style_config = match Query::system_config_find_by_key_conn(conn, "enable_modern_style").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    let enable_correction_mode_config = match Query::system_config_find_by_key_conn(conn, "enable_correction_mode").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 返回结果
    let response = json!({
        "terminals": terminals,
        "enable_terminal_ip_bind": enable_terminal_ip_bind,
        "enable_no_password_login": enable_no_password_login_config,
        "enable_modern_style": enable_modern_style_config,
        "enable_correction_mode": enable_correction_mode_config,
    });

    BusinessResponse::ok(response).to_json_result()

}

// 批量清空所选IP的设备的用户绑定信息
// #[post("/api/admin/teacher/terminal/reset/batch")]
// pub async fn reset_terminal_by_ips(session: Session, ip_request: web::Json<TerminalResetBatchRequest>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
//     let request = ip_request.into_inner();

//     // 检查参数
//     let ips = match request.ips {
//         Some(r) => r,
//         None => return Err(BusinessError::ValidationError { field: "ips未传送".to_owned() })
//     };

//     // 按照IP清理用户记录
//     let clear_user_record_by_ip = match request.clear_user_record_by_ip {
//         Some(r) => r,
//         None => return Err(BusinessError::ValidationError { field: "clear_user_record_by_ip未传送".to_owned() })
//     };
    
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 如果需要按照IP清理用户记录
//     if clear_user_record_by_ip {
//         // 获取当前训练计划ID
//         let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
//             Ok(r) => r,
//             Err(e) => {
//                 txn.rollback().await.unwrap();
//                 return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
//             }
//         };

//         if current_train_plan_id.is_some() {
//             let current_train_plan_id = current_train_plan_id.unwrap();

//             // 查询被删除IP集合上的全部终端记录
//             let terminals = match Query::find_terminal_by_ips(&txn, ips.clone()).await {
//                 Ok(r) => r,
//                 Err(e) => {
//                     txn.rollback().await.unwrap();
//                     return Err(BusinessError::InternalError { reason: format!("查询被删除IP集合上的全部终端记录失败 {}", e.to_string()) });
//                 }
//             };

//             let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {
//                 Ok(r) => r,
//                 Err(_e) => {
//                     txn.rollback().await.unwrap();
//                     return Err(BusinessError::InternalError { reason: format!("当前系统配置的训练计划ID不是整数 {}", current_train_plan_id) });
//                 }
//             };

//             // 如果当前训练计划ID存在，清理该IP下的用户训练记录
//             let mut student_record_list:Vec<TrainPlanRecordRequestWithIP> = Vec::new();
//             for terminal in terminals {
//                 // 根据查询到的终端记录生成TrainPlanRecordRequestWithIP
//                 let student_record = TrainPlanRecordRequestWithIP {
//                     client_ip: terminal.ip,
//                     plan_id: train_plan_id,
//                     train_id: terminal.train_id.unwrap(),
//                     user_id: terminal.user_id.unwrap(),
//                 };
                
//                 student_record_list.push(student_record);
//             }

//             match Mutation::clear_train_user_record_withip(&txn, student_record_list.clone()).await {
//                 Ok(r) => r,
//                 Err(e) => return Err(BusinessError::InternalError { reason: format!("清空终端设备失败 {}", e.to_string()).to_string() })
//             };
//         }

//     }

//     // 重置终端设备绑定用户信息
//     let terminals = match Mutation::reset_terminal_by_ips(&txn, ips).await  {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("清空终端设备失败 {}", e.to_string()).to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     BusinessResponse::ok(terminals).to_json_result()
// }

// 迁移终端
#[post("/api/admin/teacher/terminal/transfer")]
pub async fn transfer_terminal(session: Session, request: web::Json<TerminalTransferRequest>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let request = request.into_inner();

    // 检查参数
    let from_ip = match request.from_ip {
        Some(r) => r,
        None => return Err(BusinessError::ValidationError { field: "源IP未传送".to_owned() })
    };

    // 检查参数
    let to_ip = match request.to_ip {
        Some(r) => r,
        None => return Err(BusinessError::ValidationError { field: "目标IP未传送".to_owned() })
    };

    // 管理员权限检查
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
    }
    
    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 获取当前训练计划ID
    let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
        }
    };

    // 没有当前训练计划，不支持迁移
    if current_train_plan_id.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::InternalError { reason: format!("当前训练计划不存在，不支持迁移") });
    }

    let current_train_plan_id = current_train_plan_id.unwrap();

    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {
        Ok(r) => r,
        Err(_e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("当前系统配置的训练计划ID不是整数 {}", current_train_plan_id) });
        }
    };

    // 查询源IP上的终端记录
    let old_terminal = match Query::find_terminal_by_ip(&txn, &from_ip).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查询源终端记录失败 {}", e.to_string()) });
        }
    };

    let old_terminal = match old_terminal {
        Some(r) => r,
        None => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("源终端记录不存在") });
        }
    };

    let status = match old_terminal.status {
        Some(r) => r,
        None => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("源终端记录状态不存在") });
        }
    };

    let user_id = match old_terminal.user_id {
        Some(r) => r,
        None => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("源终端记录用户ID不存在") });
        }
    };

    let train_id = match old_terminal.train_id {
        Some(r) => r,
        None => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("源终端记录训练ID不存在") });
        }
    };

    // 迁移终端表ip当前记录到新的IP地址：源终端记录清空 + 目标终端记录更新
    match Mutation::terminal_update_by_ip(&txn, &from_ip, &status, Option::None, Option::None).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("清空终端设备失败 {}", e.to_string()).to_string() })
    };

    match Mutation::terminal_update_by_ip(&txn, &to_ip, &status, old_terminal.user_id, old_terminal.train_id).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("清空终端设备失败 {}", e.to_string()).to_string() })
    };

    // 迁移用户表last_active_ip到新的IP地址
    let now: NaiveDateTime = Local::now().naive_local();

    match Mutation::user_update_last_active_time_ip(&txn, user_id, &to_ip, &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("迁移用户表last_active_ip到新的IP地址失败 {}", e.to_string()).to_string() })
    };

    // 迁移用户训练记录表client_ip到新的IP地址
    // match Mutation::train_user_record_update_ip(&txn, user_id, train_plan_id, train_id, &to_ip, &now).await {
    //     Ok(r) => r,
    //     Err(e) => return Err(BusinessError::InternalError { reason: format!("迁移用户训练记录表client_ip到新的IP地址失败 {}", e.to_string()).to_string() })
    // };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    BusinessResponse::ok("成功").to_json_result()
}

// 根据IP地址删除终端设备
#[post("/api/admin/teacher/terminal/delete")]
pub async fn delete_terminal_by_ip(
    session: Session,
    ip_request: web::Json<IpsRequest>,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    
    let ip_request = ip_request.into_inner();

    // 检查参数
    let ips = match ip_request.ips {
        Some(r) => r,
        None => return Err(BusinessError::ValidationError { field: "ips".to_owned() })
    };

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 删除终端设备
    let terminals = match Mutation::terminal_delete_by_ips(&txn, ips).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("删除终端设备失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    BusinessResponse::ok(terminals).to_json_result()
}

// 清除终端记录
#[delete("/api/admin/teacher/terminal/clear")]
pub async fn clear_terminals(session: Session,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 删除终端设备
    match Mutation::clear_terminals_record(&txn).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("删除终端设备失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    BusinessResponse::ok("删除完毕").to_json_result()
}

// // 导入学生答题记录
// #[put("/api/admin/train_user_record/{plan_id}/{train_id}/{user_id}")]
// pub async fn put_train_user_record_admin(
//     session: Session,
//     params: web::Path<(i32, i32, i32)>,
//     request: web::Json<TrainUpdateUserRecord>,
//     http_request: HttpRequest,
//     app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

//     let (plan_id, train_id, user_id ) = params.into_inner();

//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;
//     let now: NaiveDateTime = Local::now().naive_local();

//     let connection_info = http_request.connection_info();
//     let client_ip = connection_info.peer_addr().unwrap();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     match Mutation::train_user_record_update_by_teacher(&txn, plan_id, train_id, user_id, &request, &client_ip, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("更新学生训练记录时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 反馈给教师事件
//     let notice_message = r#"{{"type":"update"}}"#.to_string();
//     let msg: ClientMessage = ClientMessage { id: 0, room: String::from("teacher"), msg: notice_message };
//     let websocket_server = &app_state.ws_server;
//     let _resp = websocket_server.send(msg).await;

//     // 反馈
//     BusinessResponse::ok(true).to_json_result()
// }


// 获取上传的.zip文件，存储到static/train文件夹下，如果检测到已存在的文件，覆盖
#[post("/api/admin/upload/train/file")]
pub async fn upload_train_file(
    mut payload: Multipart,
) -> Result<HttpResponse, BusinessError> {
    // 如果没有static/train文件夹，创建
    let static_train_dir = get_path_in_exe_dir("static").join("train");
    let static_train_dir = static_train_dir.to_str().unwrap();

    let train_dir = Path::new(static_train_dir);

    if !train_dir.exists() {
        match std::fs::create_dir_all(train_dir) {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("创建文件夹失败 {}", e.to_string()).to_string() })
        };
    }

    while let Ok(Some(mut field)) = payload.try_next().await {
        let content_type = field.content_disposition();
        let filename = content_type.unwrap().get_filename().unwrap();
        let file_path = Path::new(static_train_dir).join(&filename);

        // 检查文件是否已存在，如果存在则删除
        if file_path.exists() {
            if let Err(e) = tokio_fs::remove_file(&file_path).await {
                return Err(BusinessError::InternalError { reason: format!("删除文件失败 {}", e.to_string()).to_string() });
            }
        }

        // 创建文件
        let mut file = match tokio_fs::File::create(&file_path).await {
            Ok(file) => file,
            Err(e) => {
                return Err(BusinessError::InternalError { reason: format!("无法创建文件 {}", e.to_string()).to_string() });
            }
        };

        // 将文件内容写入
        while let Some(chunk) = field.next().await {
            let data = chunk.unwrap();
            file = match file.write_all(&data).await {
                Ok(_) => file,
                Err(e) => {
                    return Err(BusinessError::InternalError { reason: format!("无法写入文件 {}", e.to_string()).to_string() });
                }
            };
        }
    }

    // 反馈
    BusinessResponse::ok(true).to_json_result()
}

// 修改终端设备绑定ip配置
#[put("/api/admin/teacher/config/terminal_ip_bind")]
pub async fn put_terminal_ip_bind_config(
    _session: Session,
    request: web::Json<TerminalConfig>,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let request = request.into_inner();

    // 检查参数
    let enable_terminal_ip_bind = request.enable_terminal_ip_bind;

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 更新终端设备绑定ip配置
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("enable_terminal_ip_bind".into(), Some(enable_terminal_ip_bind.to_string()));

    let current_time = Local::now().naive_local();
    let config = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &current_time).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新终端设备绑定ip配置失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    
    // 向当前学生终端广播ip绑定更新消息
    let websocket_server = &app_state.ws_server;
    let process= serde_json::json!({
        "type": "enableTerminalIpBind",
        "status": enable_terminal_ip_bind
    }).to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: process.clone() };
    websocket_server.do_send(msg);

    let mut udp = app_state.udp_server.clone();

    // 设置当前设备的公告标志为 false
    udp.this_udp.message_type = UDPMessageType::Setting;
    udp.this_udp.message = Some(process);
    let announce_msg = serde_json::to_string(&udp.this_udp).unwrap();

    // 启动一个异步任务，重复发送公告消息
    UDPService::announce(
        &udp.socket,
        announce_msg.as_str(),
        (udp.multicast_addr, udp.multicast_port),
    )
    .await;

    // 设置当前设备的公告标志为 false
    udp.this_udp.message_type = UDPMessageType::Stop;

    // 反馈
    BusinessResponse::ok(config).to_json_result()
}

// 修改终端设备免密登录配置
#[put("/api/admin/teacher/config/enable_no_password_login")]
pub async fn enable_no_password_login(
    _session: Session,
    request: web::Json<TrainConfig>,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let request = request.into_inner();

    // 检查参数
    let enable_no_password_login = request.enable_no_password_login;

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 更新免密登录配置
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("enable_no_password_login".into(), Some(enable_no_password_login.to_string()));

    let current_time = Local::now().naive_local();
    let config = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &current_time).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新免密登录配置失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 向当前学生终端广播ip绑定更新消息
    let websocket_server = &app_state.ws_server;
    let process= serde_json::json!({
        "type": "enableNoPasswordLogin",
        "status": enable_no_password_login
    }).to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: process.clone() };
    websocket_server.do_send(msg);

    let mut udp = app_state.udp_server.clone();

    // 设置当前设备的公告标志为 false
    udp.this_udp.message_type = UDPMessageType::Setting;
    udp.this_udp.message = Some(process);
    let announce_msg = serde_json::to_string(&udp.this_udp).unwrap();

    // 启动一个异步任务，重复发送公告消息
    UDPService::announce(
        &udp.socket,
        announce_msg.as_str(),
        (udp.multicast_addr, udp.multicast_port),
    )
    .await;

    // 设置当前设备的公告标志为 false
    udp.this_udp.message_type = UDPMessageType::Stop;

    // 反馈
    BusinessResponse::ok(config).to_json_result()
}

#[put("/api/admin/teacher/config/enable_modern_style")]
pub async fn enable_modern_style(
    _session: Session,
    request: web::Json<StyleConfig>,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let request = request.into_inner();

    // 检查参数
    let enable_modern_style = request.enable_modern_style;

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 更新终端设备绑定ip配置
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("enable_modern_style".into(), Some(enable_modern_style.to_string()));

    let current_time = Local::now().naive_local();
    let config = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &current_time).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新学生端界面风格配置失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 反馈
    BusinessResponse::ok(config).to_json_result()
}


#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TrainModeConfig {
    pub enable_correction_mode: String,
}
#[put("/api/admin/teacher/config/enable_correction_mode")]
pub async fn enable_correction_mode(
    _session: Session,
    request: web::Json<TrainModeConfig>,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let request = request.into_inner();

    // 检查参数
    let enable_correction_mode = request.enable_correction_mode;

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 更新终端设备绑定ip配置
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("enable_correction_mode".into(), Some(enable_correction_mode.to_string()));

    let current_time = Local::now().naive_local();
    let config = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &current_time).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新学生端界面风格配置失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 反馈
    BusinessResponse::ok(config).to_json_result()
}


// 上传学校配置文件 /api/admin/upload/school_config
#[post("/api/admin/upload/school_config")]
pub async fn upload_school_config(
    request: web::Json<UploadConfig>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {
    // 检查参数
    let request = request.into_inner();

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 读取当前学校配置
    let school_config = match Query::system_config_find_all(&txn).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("读取当前学校配置失败 {}", e.to_string()).to_string() })
    };

    // 当前配置的site_url如果为demo，表示升级正式版，更新（或写入）全部字段;否则为正式版，仅更新 licence
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();

    if let Some(site_url) = school_config.get("site_url") {
        if let Some(value) = site_url {
            if value == "demo" {
                key_value_map.insert("site_url".into(), Some(request.site_url));
                key_value_map.insert("licence".into(), Some(request.licence));
                key_value_map.insert("school_name".into(), Some(request.school_name));
                key_value_map.insert("default_username".into(), Some(request.default_username));
                key_value_map.insert("default_password".into(), Some(request.default_password));
                key_value_map.insert("lab".into(), Some(request.lab));
            } else {
                key_value_map.insert("licence".into(), Some(request.licence));
            }
        }
    }

    // 更新学校配置
    let current_time = Local::now().naive_local();
    let config = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &current_time).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新学校配置失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 反馈
    BusinessResponse::ok(config).to_json_result()
}

// 上传班级和学生 /api/admin/upload/class_and_students
#[post("/api/admin/upload/class_and_students")]
pub async fn upload_class_and_students(
    request: web::Json<TeamUsersRecordOffline>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {
    // 检查参数
    let class_list = &request.class_list;

    // let all_class_ids = &request.all_class_ids;

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 批量上传request数组到用户表
    let now = Local::now().naive_local();

    // 将队伍同步到本地数据库
    match Mutation::team_batch_save(&txn, &class_list, &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍同步到本地数据库失败 {}", e.to_string()).to_string() })
    };

    // 将账号同步到本地数据库
    match Mutation::user_batch_save_by_teams(&txn, &class_list, &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍账号同步到本地数据库失败 {}", e.to_string()).to_string() })
    };

    // 将队伍账号关联同步到本地数据库
    match Mutation::team_user_batch_save_by_teams(&txn, &class_list, &now).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("将队伍账号关联同步到本地数据库失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 反馈
    BusinessResponse::ok(true).to_json_result()
}

// 上传用户 /api/admin/upload/users
#[post("/api/admin/upload/users")]
pub async fn upload_users(
    request: web::Json<Vec<UploadUserRecord>>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 批量上传request数组到用户表
    let current_time = Local::now().naive_local();
    let user_records = request.into_inner();

    let users = match Mutation::user_batch_save(&txn, &user_records, &current_time).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("批量上传用户失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 反馈
    BusinessResponse::ok(users).to_json_result()
}

// // 上传试卷 /api/admin/upload/train
// #[post("/api/admin/upload/train")]
// pub async fn upload_train(
//     request: web::Json<UploadTrainData>,
//     app_state: web::Data<AppState>
// ) -> Result<HttpResponse, BusinessError> {

//     // 检查参数
//     let train_list = request.train_list.clone();

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 批量上传试卷
//     let now = Local::now().naive_local();
//     match Mutation::train_batch_save(&txn, &train_list, &now).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("批量上传试卷失败 {}", e.to_string()).to_string() })
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 反馈
//     BusinessResponse::ok(true).to_json_result()
// }

// //  上传试卷系列
// #[post("/api/admin/upload/train_series")]
// pub async fn upload_train_series(
//     request: web::Json<Vec<TrainSeriesImportItem>>,
//     app_state: web::Data<AppState>
// ) -> Result<HttpResponse, BusinessError> {

//     // 检查参数
//     let train_series_list = request.clone();

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 批量上传试卷系列
//     let now = Local::now().naive_local();
//     match Mutation::train_series_batch_save(&txn, &train_series_list, &now).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("批量上传试卷系列失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 关闭事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => {
//             return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//         }
//     };

//     // 反馈
//     BusinessResponse::ok(true).to_json_result()
// }


#[derive(Clone, Debug, PartialEq, Eq, Deserialize, Serialize)]
pub struct SendMessage {
    pub message: String,
}

#[post("/api/admin/train/teacher/message")]
pub async fn send_student_message(
    request: web::Json<SendMessage>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {
    let message = &request.message;

    // 向当前学生终端广播训练计划更新消息
    let websocket_server = &app_state.ws_server;
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: message.into() };
    let _resp = websocket_server.send(msg).await;

    // 返回成功
    BusinessResponse::ok(true).to_json_result()
}


// #[put("/api/admin/train/teacher/train_plan/clear_student_records")]
// pub async fn clear_student_records(
//     session: Session,
//     request: web::Json<Vec<TrainPlanRecordRequest>>,
//     app_state: web::Data<AppState>
// ) -> Result<HttpResponse, BusinessError> {
//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let student_record_list = request.clone();

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 清空学生训练记录
//     match Mutation::clear_train_user_record(&txn, student_record_list).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("清空学生训练记录失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => {
//             return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//         }
//     };

//     // 返回成功
//     BusinessResponse::ok(true).to_json_result()
// }


// #[derive(Clone, Debug, PartialEq, Eq, Deserialize, Serialize)]
// pub struct QueryStudentRecord {
//     pub plan_id: i32,
//     pub train_id: i32,
//     pub user_id: i32,
// }

// #[post("/api/admin/teacher/train/student_answer")]
// pub async fn get_student_answer_admin(
//     session: Session,
//     request: web::Json<QueryStudentRecord>,
//     app_state: web::Data<AppState>
// ) -> Result<HttpResponse, BusinessError> {
//     let plan_id = request.plan_id;
//     let train_id = request.train_id;
//     let user_id = request.user_id;

//     // 加载Session
//     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() })
//     };

//     if user_admin_authority.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先使用管理员账号登录本地站点".to_string() });
//     }

//     let conn = &app_state.conn;
//     let now = Local::now().naive_local();

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 读取账号是否存在
//     let user_record = match Query::user_find_by_id(&txn, user_id).await  {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 用户不存在
//     if user_record.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
//     }

//     let user_record = user_record.unwrap();
//     let mut user_response: serde_json::Map<String, Value> = serde_json::Map::new();
//     user_response.insert(String::from("id"), Value::Number(user_record.id.into()));
//     user_response.insert(String::from("username"),  Value::String(user_record.username.clone()));
//     user_response.insert(String::from("display_name"),  Value::String(user_record.name.clone()));

//     // 根据训练计划ID查找用户在该训练计划基本信息
//     let train_plan = match Query::train_plan_find_by_id_json(&txn, plan_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查找训练计划时出错，训练计划ID为 {}, {}", plan_id, e.to_string()).to_string() });
//         }
//     };

//     if train_plan.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("查找训练计划失败，结果为空，训练计划ID为 {}", plan_id).to_string() });
//     }

//     let train_plan = train_plan.unwrap();

//     // 获取训练用户记录
//     let mut train_user_record = match Query::train_user_record_find_one_json(&txn, plan_id, train_id, user_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("获取用户训练计划记录时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     if train_user_record.is_none() {
//         train_user_record = Some(json!({}));
//     }

//     // 获取订正记录
//     let mut train_correction_record = match Query::train_user_record_find_one_correction_json(&txn, plan_id, train_id, user_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("获取订正记录时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     if train_correction_record.is_none() {
//         train_correction_record = Some(json!({}));
//     }

//     // 获取训练记录
//     let train_record = match Query::trian_find_by_id(&txn, train_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("获取训练记录时出错 {}", e.to_string()).to_string() })
//         }
//     };

//     if train_record.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::InternalError { reason: format!("训练{}不存在", train_id).to_string() })
//     }

//     let train_record = train_record.unwrap();

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 解压试卷文件
//     let static_train_dir = get_path_in_exe_dir("static").join("train");
//     let select_train_dir = static_train_dir.join(&train_id.to_string());
//     // 如果不存在，解压压缩包
//     if !select_train_dir.exists() {
//         // 将目标目录转换为字符串，以供后续使用
//         let target_train_dir = match select_train_dir.to_str() {
//             Some(path_str) => path_str,
//             None => {
//                 return Err(BusinessError::InternalError {
//                     reason: "目标目录路径无效".to_string(),
//                 });
//             }
//         };
//         // 创建压缩文件的路径
//         let train_zip_path = select_train_dir.with_extension("zip");
//             if !train_zip_path.exists() {
//             return Err(BusinessError::InternalError {
//                 reason: format!("训练文件不存在 {}", target_train_dir),
//             });
//         }

//         // 打印

//         let _result = match extract_zip(&train_zip_path, &select_train_dir) {
//             Ok(r) => r,
//             Err(e) => {
//                 write_log(format!("解压文件失败 {}", e.to_string()).to_string());
//                 // return Err(BusinessError::InternalError { reason: format!("解压文件失败 {}", e.to_string()).to_string() });
//             }
//         };
//     }

//     let mut response: HashMap<String, Value> = HashMap::new();
//     response.insert(String::from("train_user_record"),  train_user_record.unwrap());
//     response.insert(String::from("train_correction_record"), train_correction_record.unwrap());
//     response.insert(String::from("train"), train_record);
//     response.insert(String::from("train_plan"), train_plan);
//     response.insert(String::from("user"),  Value::Object(user_response));
//     response.insert(String::from("server_timestamp"), Value::Number(((now.and_utc().timestamp()  - 8 * 3600) * 1000).into()));

//     BusinessResponse::ok(response).to_json_result()
// }


// 根据key获取system_config中的value
#[get("/api/admin/teacher/get/config/{key}")]
pub async fn get_value_by_key(
    key: web::Path<String>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    let key = key.into_inner();

    let key_value = match Query::system_config_find_by_key(&txn, &key).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    let response = match key_value {
        Some(r) => r,
        None => "".to_string(),
    };
    
    // 反馈
    BusinessResponse::ok(response).to_json_result()
}

// 根据key和value修改system_config 中的value
#[put("/api/admin/teacher/put/config/{key}/{value}")]
pub async fn put_value_by_key(
    key_value: web::Path<(String, String)>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {
    let (key, value) = key_value.into_inner();

    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert(key, Some(value));

    let current_time = Local::now().naive_local();
    let config = match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &current_time).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("更新配置失败 {}", e.to_string()).to_string() })
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    // 反馈
    BusinessResponse::ok(config).to_json_result()
}
// 获取在线用户数量
#[get("/api/admin/terminal/onlineCount")]
pub async fn get_online_count(
    session: Session,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });    
    }
    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    let online_ips_count = match Query::get_online_count(&txn).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("获取在线用户数量失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    
    
    BusinessResponse::ok(online_ips_count).to_json_result()
}

// 开始上课
#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
struct StartClass { 
    course_slug: String,
}
#[post("/api/admin/course/start")]
pub async fn start_class(
    session: Session,
    params: web::Json<StartClass>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {
     // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });    
    };

    let mut udp_file_server = app_state.udp_file_server.clone();
    let mut udp_server = app_state.udp_server.clone();
    let slug = params.course_slug.clone();
    // 发送上课通知
    let _ = udp_server.send_start_class_msg(slug.to_string()).await;
    // 加入文件传输队列
    let _ = udp_file_server.send_course_file(slug.to_string(), 0, vec![]).await;

    BusinessResponse::ok(format!("课程{}加入到传输队列",slug)).to_json_result()

}

#[get("/api/admin/allTeams")]
pub async fn get_all_teams(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {



    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };


    let teams = match Query::all_team_find(&txn).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找班级失败 {}", e.to_string()).to_string() })
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    
    BusinessResponse::ok(teams).to_json_result()
}
// 获取节统计内容
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct QuerySectionContentAndType {
    class_i_d: i32,
    course_slug: String,
    chapter_name: String,
    section_name: String,
    select_state: String,
    
}

#[derive(Debug)]
struct MicroType {
    type_name: String,
    ext_name: String,
    binary: bool,
}

static MICRO_TYPE_NAME_MAP: Lazy<HashMap<&'static str, MicroType>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert(
        "text",
        MicroType {
            type_name: "文本框".to_string(),
            ext_name: "txt".to_string(),
            binary: false,
        },
    );
    map.insert(
        "table",
        MicroType {
            type_name: "简易表格".to_string(),
            ext_name: "csv".to_string(),
            binary: false,
        },
    );
    map.insert(
        "flow",
        MicroType {
            type_name: "流程图填空".to_string(),
            ext_name: "json".to_string(),
            binary: false,
        },
    );
    map.insert(
        "mind",
        MicroType {
            type_name: "思维导图".to_string(),
            ext_name: "json".to_string(),
            binary: false,
        },
    );
    map.insert(
        "spreadsheet",
        MicroType {
            type_name: "工作表".to_string(),
            ext_name: "json".to_string(),
            binary: false,
        },
    );
    map.insert(
        "networksimulator",
        MicroType {
            type_name: "网络模拟器".to_string(),
            ext_name: "json".to_string(),
            binary: false,
        },
    );
    map.insert(
        "drawio",
        MicroType {
            type_name: "DrawIO图表".to_string(),
            ext_name: "json".to_string(),
            binary: false,
        },
    );
    map
});


#[post("/api/admin/result/section")]
pub async fn get_section_result(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Json<QuerySectionContentAndType>
) -> Result<HttpResponse, BusinessError> {

    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 获取该班级名单
    let class_id = params.class_i_d;
    let class_member_list = match Query::users_list_find_by_team_id(&txn, class_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取班级成员列表 {}", e.to_string()).to_string() })
        }
    };
    // 获取学生id名单
    let student_ids = class_member_list.iter().map(|x| x.id).collect::<Vec<i32>>();
    let section = match Query::get_section(&txn, params.course_slug.clone(), params.chapter_name.clone(), params.section_name.clone(),).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取章节信息 {}", e.to_string()).to_string() })
        }
    };
    let record_list = match Query::get_section_record_find_by_user_ids_and_section_id(&txn, student_ids, section.clone().unwrap().id.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取章节记录列表 {}", e.to_string()).to_string() })
        }
    };
    let section_type = section.clone().unwrap().section_type.clone();
    // 初始化统计结果
    let mut count = 0;
    let mut key = String::new();
    let mut count_map: HashMap<i32, Value> = HashMap::new();
    let mut answer_keys = Vec::new();
    let mut key_map: HashMap<i32, String> = HashMap::new();
    let mut item_names: HashSet<String> = HashSet::new();
    let mut count_type_map = HashMap::new();
    count_type_map.insert("code".to_string(), 0);
    let mut statics_result = HashMap::new();
    statics_result.insert("全部", class_member_list.len());
    // statics_result.insert("未答题", 0);
    // statics_result.insert("已答题", 0);
    // statics_result.insert("运行错误", 0);
    // statics_result.insert("全部通过", 0);
    // statics_result.insert("待评分", 0);
    // statics_result.insert("全部满分", 0);

    // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(params.course_slug.clone()).join("course.json");
    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut course_type = String::new();
    let mut ext = String::new();
    
    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == params.chapter_name {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == params.section_name {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    ext = section_value["ext"].as_str().unwrap().to_string();
                    course_type = section_value["sectionType"].as_str().unwrap().to_string();
                }
            }
        }
    }
    // 获取课程内容文件
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let path = format!("{}/{}/{}/{}.{}", content_path,params.course_slug , chapter_name , section_name , ext);
    // 准备节内容基础数据
    match section_type.as_str() {
        "OJ" => {
            // 节状态
            statics_result.insert("未答题", 0);
            statics_result.insert("已答题", 0);
            statics_result.insert("运行错误", 0);
            statics_result.insert("全部通过", 0);
            // 获取数量
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            let content = serde_json::from_str::<OJContent>(&file_content).unwrap();
            count = content.judge_menu.test_files.len() as i32;
            // 定义题目数据类型
            key = "数据".to_string();
        }
        "AI" => {
            statics_result.insert("未提交", 0);
            statics_result.insert("已提交", 0);
            statics_result.insert("未全部通过", 0);
            // statics_result.insert("运行通过", 0);
            statics_result.insert("全部通过", 0);
            // 获取数量
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            let cells: Vec<Value> = serde_json::from_value(file_content["cells"].clone()).unwrap();
            for cell in cells {
                let mut unopened = false;
                if cell["metadata"].get("unopened").is_some() {
                    unopened = cell["metadata"]["unopened"].as_bool().unwrap();
                }
                if cell["cell_type"] != "code" {
                    continue;
                }
                // // 判断cell["metadata"]["type"]字段是否存在
                // if !cell["metadata"].get("type") {
                //     continue;
                // }
                if cell["metadata"]["type"] == "resources" || cell["metadata"]["type"] == "filepreviewer" {
                    continue;
                }
                if let Some(code) = cell["source"].as_str() {
                    if code.starts_with("# >隐藏并自动执行") {
                        continue;
                    }
                }
                count += 1;
                let uuid = cell["metadata"]["UUID"].clone();
                count_map.insert(count, uuid);
                // 判断cell["metadata"]["type"]字段是否存在

                if cell["metadata"].get("type").is_some() && cell["metadata"]["type"] != "resources" && cell["metadata"]["type"] != "filepreviewer" {

                    
                    // if !count_type_map.contains_key(&cell["metadata"]["type"].as_str().unwrap().to_string()) {
                    //     // let file_type = cell["metadata"]["type"].as_str().unwrap().to_string();
                    //     count_type_map.insert(cell["metadata"]["type"].as_str().unwrap().to_string(), 0);
                    // }
                    *count_type_map.entry(cell["metadata"]["type"].as_str().unwrap().to_string()).or_insert(0) += 1;
                    // if let Some(metadata) = cell.get("metadata") {
                    //     if let Some(cell_type) = metadata.get("type").and_then(|t| t.as_str()) {
                    //         *count_type_map.entry(cell_type.to_string()).or_insert(0) += 1;
                    //     }
                    // }
                    if let Some(micro_code_config) = MICRO_TYPE_NAME_MAP.get(&cell["metadata"]["type"].as_str().unwrap()) {
                        
                        if unopened == true {
                            continue;
                        }
                        let micro_type_name = micro_code_config.type_name.clone();
                        let micro_type_ext = micro_code_config.ext_name.clone();
                        // let file_type = cell["metadata"]["type"].as_str().unwrap();
                        let file_name = cell["metadata"]["fileName"].to_string().replace("\"", "");
                        key_map.insert(count, format!("{}-{}-{}.{}", micro_type_name, count_type_map[cell.clone()["metadata"]["type"].as_str().unwrap()], file_name, micro_type_ext));
                        
                        continue;
                    }else {
                        return Err(BusinessError::ProcessError { reason: format!("不支持的微应用")})
                    }

                    


                }else {
                    *count_type_map.entry("code".to_string()).or_insert(0) += 1;
                    if let Some(unopened) = cell["metadata"]["unopened"].as_bool() {
                        if unopened == true {
                            continue;
                        }

                    }
                    key_map.insert(count, format!("代码{}", count_type_map["code"]));
                    continue;
                }
            }
            
            key = "代码".to_string();
        }
        "OI" => {
            statics_result.insert("未答题", 0);
            statics_result.insert("已答题", 0);
            statics_result.insert("未满分", 0);
            statics_result.insert("全部正确", 0);
            // 获取数量
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            // println!("{:?}", file_content);
            // let file_content: OIData = get_oi_question(file_content);
            // let file_content = serde_json::from_str::<OIData>(&file_content).unwrap();
            let file_content:Value = serde_json::from_str(&file_content).unwrap();
            // println!("{:?}", file_content);
            let questions: Vec<Question> = serde_json::from_value(file_content["questions"].clone()).unwrap();
            key = "题目".to_string();
            for question in questions {
                if question.questionType == "文本" {
                    continue;
                }
                count_map.insert(count, serde_json::to_value(question).unwrap());
                count += 1;
                key_map.insert(count, format!("{}{}", key, count));
            }
            
        }
        "Excel" => {
            statics_result.insert("未答题", 0);
            statics_result.insert("已答题", 0);
            statics_result.insert("未满分", 0);
            statics_result.insert("全部通过", 0);
            // 获取数量
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            count = if let Some(instructions) = file_content["instructions"].as_array() {
                instructions.len() as i32
            } else {
                0
            };
            
            key = "步骤".to_string();
        }
        "Access" => {
            statics_result.insert("未答题", 0);
            statics_result.insert("已答题", 0);
            statics_result.insert("未满分", 0);
            statics_result.insert("全部通过", 0);
            // 获取数量
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            count = if let Some(instructions) = file_content["instructions"].as_array() {
                instructions.len() as i32 -1
            } else {
                0
            };
            
            key = "步骤".to_string();
        }
        "Scratch" => {
            statics_result.insert("未答题", 0);
            statics_result.insert("已答题", 0);
            statics_result.insert("待评分", 0);
            statics_result.insert("未满分", 0);
            statics_result.insert("全部满分", 0);
            // 获取数量
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            count = if let Some(judge_steps) = file_content["judgeSteps"].as_array() {
                judge_steps.len() as i32
            } else {
                0
            };
            key = "步骤".to_string();
        }
        "PPT" => {
            statics_result.insert("0%", 0);
            statics_result.insert("25%", 0);
            statics_result.insert("50%", 0);
            statics_result.insert("75%", 0);
            statics_result.insert("100%", 0);
            key = "页面".to_string();
        }
        "CodeBlank" => {
            statics_result.insert("未答题", 0);
            statics_result.insert("已答题", 0);
            statics_result.insert("未满分", 0);
            statics_result.insert("全部正确", 0);
            let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            // 获取 questions 数组
            let questions = file_content.get("questions")
                .and_then(|v| v.as_array())
                .ok_or("题目列表不存在")
                .unwrap()
                .to_owned();

            if questions.is_empty() {
                return Err(BusinessError::ProcessError { reason: "题目不存在".to_string() });
            }

            let question = &questions[0];
            let empty_map = Map::new();
            let answer = question.get("answer")
                .and_then(|v| v.as_object())
                .unwrap_or(&empty_map)
                .to_owned();

            let answer_keys_data: Vec<String> = answer.keys().map(|k| k.to_string()).collect();
            answer_keys = answer_keys_data.clone();
            count = answer_keys.len() as i32;
            
            key = "填空".to_string();
        }
        "MicroBit" => {
            statics_result.insert("未答题", 0);
            statics_result.insert("未提交", 0);
            statics_result.insert("已提交", 0);
            key = "项目".to_string();
        }
        _ => {
            return Err(BusinessError::ProcessError { reason: format!("未知的课程类型")})
        }
    }
    let mut user_value_list: Vec<Value> = vec![];
    
    // 定义顺序
    let mut order: Vec<&str> = Vec::new();
    // 遍历用户数据
    for user_value in class_member_list{
        let user_id = user_value.id;
        let user_json_value = serde_json::to_value(user_value.clone()).unwrap();
        let mut user_value_map:HashMap<String, Value> = serde_json::from_value(user_json_value).unwrap();

        if section_type.eq("Scratch"){
            user_value_map.insert("score".to_string(), json!({}));
            user_value_map.insert("details".to_string(), json!([]));
        }

        let record = record_list.iter().find(|r| r.user_id == user_id);
        match section_type.as_str() {
            "PPT" => {
                if let Some(total_page) = record.map(|r| r.record.clone().unwrap()["totalPages"].clone()) {
                    count = total_page.as_i64().unwrap() as i32;
                }
            }
            "MicroBit" => {
                count = 1;
            }
            _ => {
                
            }
        }
        let submit_time = if let Some(record) = record {
            record.updated_at
        } else {
            None
        };
        let submit_time = serde_json::to_value(submit_time).unwrap();
        user_value_map.insert("submitTime".to_string(), submit_time);

        if count == 0 {
            continue;
        }

        let mut pass_list: Vec<bool> = Vec::new();
        let mut score_map: HashMap<String, i32> = HashMap::new();
        let mut details: Vec<Value> = Vec::new();
        let mut page = 0;
        for i in 1..= count {
            // 不存在记录时
            if !record.is_some() || !record.unwrap().record.is_some() {
                let key_value = key_map.get(&i).cloned().unwrap_or_else(|| key.to_string());
                if key_value.is_empty() {
                    continue;
                }

                let key1 = if section_type == "AI" || section_type == "OI" {
                    key_value.to_string()
                } else {
                    format!("{}{}", key_value, i)
                };
            
                let key2 = if section_type == "AI"  || section_type == "OI" {
                    format!("{}学生答案", key_value)
                } else {
                    format!("{}{}学生答案", key_value, i)
                };
                
                // pass_list.push(false);
                if section_type == "MicroBit" {
                    user_value_map.insert(key1, serde_json::to_value("未提交".to_string()).unwrap());
                } else {
                    user_value_map.insert(key1, serde_json::to_value("未答题".to_string()).unwrap());
                }
                user_value_map.insert(key2, serde_json::to_value("".to_string()).unwrap());

                continue;
            }


            
            // 存在记录时
            match section_type.as_str() {
                "OJ" => {
                    let detail = record.unwrap().record.clone().unwrap()["detail"].as_array().unwrap().clone();
                    let oj_state_map = [
                        (3, "RI"), // 表示程序执行中
                        (4, "AC"), // 表示程序的输出与标准输出完全一致。
                        (5, "PE"), // 表示程序的输出在不考虑空白(空格/回车\r\n/Tab)的情况下, 与标准输出完全一致。
                        (6, "WA"), // 错误的答案
                        (10, "RE"), // 运行错误
                        (7, "TL"), // 超时
                    ].iter().cloned().collect::<std::collections::HashMap<_, _>>();
                    let state_code = detail[i as usize - 1]["state"].clone().as_i64().unwrap() as i32; 
                    // if let Some(state_code) = detail.get(i).map(|d| d["state"].clone()) {
                    if let Some(state_str) = oj_state_map.get(&state_code) {
                        user_value_map.insert(format!("{}{}", key, i), serde_json::to_value(state_str).unwrap());
                    } else {
                        user_value_map.insert(format!("{}{}", key, i), serde_json::to_value("0").unwrap());
                    }
                    if state_code == 4 {
                        pass_list.push(true);
                    } else {
                        pass_list.push(false);
                    }
                    // } else {
                    //     user_value_map.insert(format!("{}{}", key, i + 1), "0".to_value());
                    // }
                
                    item_names.insert(format!("{}{}", key, i));
                }
                "Excel" | "Access" => {
                    let steps = record.unwrap().record.clone().unwrap()["steps"].as_array().unwrap().clone();
                    let step = steps[i as usize - 1].clone();
                    let tmp_value = if step["status"] == "finish" {
                        pass_list.push(true);
                        "正确".to_string()
                    } else {
                        pass_list.push(false);
                        "错误".to_string()
                    };
                    user_value_map.insert(format!("{}{}", key, i), serde_json::to_value(tmp_value).unwrap());
                    item_names.insert(format!("{}{}", key, i));
                    
                }
                "PPT" => {
                    page = record.unwrap().record.clone().unwrap()["page"].as_i64().unwrap().clone() as i32;
                    let tmp_value = if page > i { "已读" } else { "未读" };

                    user_value_map.insert(format!("{}{}", key, i), serde_json::to_value(tmp_value).unwrap());
                    item_names.insert(format!("{}{}", key, i));
                }
                "Scratch" => {

                    let judge_steps = record.unwrap().record.clone().unwrap()["judge_steps"].as_array().unwrap().clone();
                    let default_step = serde_json::json!({});

                    let step = if i > 0 && (i as usize - 1) < judge_steps.len() {
                        &judge_steps[i as usize - 1]
                    } else {
                        // 返回一个默认的 JSON 对象，表示未答题
                        &default_step
                    };
                    let result = if step.get("manual").and_then(|v| v.as_bool()).unwrap_or(false) {
                        step.get("rate")
                            .and_then(|v| v.as_f64().or_else(|| if v.is_number() { Some(v.as_u64().unwrap() as f64) } else { None }))
                            .map_or_else(|| "未评分".to_string(), |rate| rate.to_string())
                    } else if step.get("pass").and_then(|v| v.as_bool()).unwrap_or(false) {
                        step.get("score")
                            .and_then(|v| v.as_f64().or_else(|| if v.is_number() { Some(v.as_u64().unwrap() as f64) } else { None }))
                            .map_or_else(|| "未答题".to_string(), |score| score.to_string())
                    } else {
                        "未评分".to_string()
                    };
                    user_value_map.insert(format!("{}{}", key, i), serde_json::to_value(result).unwrap());
                    // user_value_map[key + &(i + 1).to_string()] = serde_json::Value::String(result);
        
                    if let Some(score) = step.get("score") {
                        score_map.insert(format!("{}{}", key, i), score.as_i64().unwrap() as i32);
                        details.push(step.clone());
                        

                    }


        
                    item_names.insert(key.to_string() + &i.to_string());
                    
                    

                }
                "MicroBit" => {
                    let file_name = record.unwrap().record.clone().unwrap()["file_name"].clone().to_string();

                    user_value_map.insert(
                        format!("{}{}",key,i),
                        // key.clone().to_string(),
                        if !file_name.is_empty() { 
                            pass_list.push(true);
                            serde_json::Value::String("已提交".to_string()) 
                        } else { 
                            pass_list.push(false);
                            serde_json::Value::String("未提交".to_string())
                        }
                    );
                    user_value_map.insert(format!("{}{}学生答案",key,i), serde_json::to_value("".to_string()).unwrap());
                    item_names.insert(key.to_string());
                }
                "OI" => {
                    // let mut answer_map: HashMap<String, Value> = HashMap::new();
                    let mut answer_data = String::new();

                    if record.unwrap().record.is_none() {
                        return Err(BusinessError::ProcessError { reason: format!("暂无答题记录！")});
                    }

                    let answer = record.unwrap().record.clone().unwrap();
                    let answer: HashMap<String, Value> = serde_json::from_value(answer).unwrap();

                    let uuid: String = count_map[&(i - 1)].get("UUID").unwrap().to_string();
                    let uuid = uuid.replace("\"", "");

                    let question_type = count_map[&(i - 1)].get("questionType").unwrap().to_string();

                    if let Some(question) = answer.get(&uuid) {
                        let question_type = question_type.trim_matches('"'); // 去除首尾双引号

                        match question_type {
                            "多选题" => {
                                if let Some(answer) = question.get("answer").and_then(|v| v.as_array()) {
                                    let strings: Vec<String> = answer.iter()
                                        .filter_map(|v| v.as_str().map(|s| s.to_string()))
                                        .collect();
                                    answer_data = strings.join("、");
                                } else {
                                    answer_data = "".to_string();
                                }

                            },
                            "填空题" => {

                                if let Some(answer) = question.get("answer").and_then(|v| v.as_array()) {
                                    let strings: Vec<String> = answer
                                        .iter() // 将 &Vec<JsonValue> 转换为迭代器
                                        .filter_map(|i| i.get("text")) // 获取 "text" 字段，过滤掉没有 "text" 的元素
                                        .filter_map(|text| text.as_str().map(String::from)) // 将 "text" 转换为字符串
                                        .collect(); // 收集到 Vec<String>
                                    answer_data = strings.join("、");

                                } else {
                                    answer_data = "".to_string();
                                }          
                            },
                            "单选题" => {

                                answer_data = match question.get("answer") {
                                    Some(value) => match value.as_str() {
                                        Some(s) => s.to_string(),
                                        None => "".to_string(),
                                    },
                                    None => "".to_string(),
                                };
                                // println!("单选题答案：{}", answer_data);
                            },
                            _ => {
                                // answer_data = question.get("answer").unwrap().as_str().unwrap().to_string();
                            }
                        }
                        if let Some(status) = question.get("status") {
                            match status.as_bool().unwrap() {
                                true => {
                                    pass_list.push(true);
                                    user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("正确".to_string()));
                                },
                                false => {
                                    pass_list.push(false);
                                    user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("错误".to_string()));
                                },
                            }
                        } else {
                            pass_list.push(false);
                            user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("未答题".to_string()));
                        }
                        user_value_map.insert(format!("{}{}学生答案", key, i), serde_json::to_value(answer_data.clone()).unwrap());
                    } else {
                        pass_list.push(false);
                        user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("未答题".to_string()));
                        user_value_map.insert(format!("{}{}学生答案", key, i), serde_json::Value::String("".to_string()));
                    }
                    item_names.insert(format!("{}{}", key, i));
                }
                "AI" => {
                    let ai_content = record.unwrap().record.clone().unwrap();
                    let uuid: String = count_map[&i].to_string();
                    let uuid: String = uuid.replace("\"", "");
                    if let Some(key_value) = key_map.get(&i).cloned() {
                        let key_value: String = key_value.replace("\"\\", "");
                        if let Some(entry) = ai_content.get(uuid) {
                            if entry.get("microAppFileType").is_some() {
                                user_value_map.insert(format!("{}", key_value), serde_json::Value::String("运行通过".to_string()));
                                pass_list.push(true);
                                // user_value_map.insert(key_value.clone(), "运行通过".to_string());
                            } else {
                                user_value_map.insert(format!("{}", key_value), entry.get("status").unwrap().clone());
                                if entry.get("status").unwrap().clone() == "运行通过" {
                                    pass_list.push(true);
                                }else {
                                    pass_list.push(false);
                                }
                                // user_value_map.insert(key_value.clone(), entry.status.clone());
                                user_value_map.insert(format!("{}学生答案", key_value), entry.get("code").unwrap().clone());
                                item_names.insert(key_value.clone());
                            }
                            continue;
                        }
                            
                        
                        user_value_map.insert(format!("{}", key_value), serde_json::Value::String("未答题".to_string()));

                    } else {
                        continue;
                    }
                }
                "CodeBlank" => {
                    if let Some(Value::Object(record_record)) = record.unwrap().record.clone() {
                        if let Some(_) = record_record.get("submitTimes").and_then(|v| v.as_i64()) {
                            let mut student_answers = record_record.clone();
                            student_answers.remove("submitTimes");
                            let student_answer = student_answers.get(&answer_keys[(i - 1) as usize]).unwrap();
                            if let Some(status) = student_answer.get("status") {
                                match status.as_bool().unwrap() {
                                    true => {
                                        pass_list.push(true);
                                        user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("正确".to_string()));
                                    },
                                    false => {
                                        pass_list.push(false);
                                        user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("错误".to_string()));
                                    },
                                }
                                // 插入学生答案
                                user_value_map.insert(format!("{}{}学生答案", key, i), serde_json::to_value(student_answer.get("studentAnswer")).unwrap());
                            }else {
                                user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("未答题".to_string()));
                                user_value_map.insert(format!("{}{}学生答案", key, i), serde_json::Value::String("".to_string()));
                            }
                        }
                    }  else {
                        // 如果 answer_key 不存在，插入默认值
                        user_value_map.insert(format!("{}{}", key, i), serde_json::Value::String("未答题".to_string()));
                        user_value_map.insert(format!("{}{}学生答案", key, i), serde_json::Value::String("".to_string()));
                    }
                    item_names.insert(format!("{}{}", key, i));

                }
                _ => {
                    return Err(BusinessError::ProcessError { reason: format!("未知的课程类型{}",section_type)});
                }
            }
            
        }
        // 得分统计
        match section_type.as_str() {
            "Scratch" => {
                order = ["全部", "未答题", "已答题", "待评分", "未满分", "全部满分"].to_vec();
                user_value_map.insert(format!("score",), serde_json::to_value(score_map).unwrap());
                user_value_map.insert(format!("details"), serde_json::to_value(details.clone()).unwrap());
                // 答题情况统计
                // let details = user_value_map.get("details").unwrap().as_array().unwrap();

                if !details.is_empty() {
                    user_value_map.insert("status".to_string(), "已答题".into());
                    *statics_result.entry("已答题").or_insert(0) += 1;
                } else {
                    user_value_map.insert("status".to_string(), "未答题".into());
                    *statics_result.entry("未答题").or_insert(0) += 1;
                }

                if details.iter().any(|detail| {
                    detail.get("manual") == Some(&serde_json::Value::Bool(true)) && detail.get("rate").is_none()
                }) {
                    user_value_map.insert("status".to_string(), "待评分".into());
                    *statics_result.entry("待评分").or_insert(0) += 1;
                }

                if !details.is_empty() && details.iter().all(|detail| {
                    let pass = detail.get("pass") == Some(&serde_json::Value::Bool(true));
                    let rate = detail.get("rate").unwrap_or(&serde_json::Value::Null);
                    let score = detail.get("score").unwrap_or(&serde_json::Value::Null);
                    pass || (rate.is_number() && rate.as_f64().unwrap() >= score.as_f64().unwrap_or(0.0))
                }) {
                    user_value_map.insert("status".to_string(), "全部满分".into());
                    *statics_result.entry("全部满分").or_insert(0) += 1;
                }
            }
            "OJ" => {
                order = ["全部", "未答题", "已答题", "未全部通过", "全部通过"].to_vec();
                let pass_list_clone = pass_list.clone();
                // user_value_map.insert("status".to_string(), pass_list_clone.clone().into());
                if !pass_list_clone.is_empty() {
                    user_value_map.insert("status".to_string(), "已答题".into());
                    *statics_result.entry("已答题").or_insert(0) += 1;

                    if pass_list_clone.len() as i32 == count{
                        // 遍历 pass_list查看是否全部为true
                        let mut all_pass = true;
                        for stat in pass_list_clone {
                            if !stat{
                                all_pass = false;
                                user_value_map.insert("status".to_string(), "未全部通过".into());
                                *statics_result.entry("未全部通过").or_insert(0) += 1;
                                break;
                            }
                        }
                        if all_pass{
                            user_value_map.insert("status".to_string(), "全部通过".into());
                            *statics_result.entry("全部通过").or_insert(0) += 1;
                        }
                    } else {
                        user_value_map.insert("status".to_string(), "未全部通过".into());
                        *statics_result.entry("未全部通过").or_insert(0) += 1;
                    }
                } else {
                    user_value_map.insert("status".to_string(), "未答题".into());
                    *statics_result.entry("未答题").or_insert(0) += 1;
                }
                
                // *statics_result.entry("已答题").or_insert(0) += 1;
                // user_value_map.insert(format!("answer",), serde_json::to_value(answer_map).unwrap());
            }
            "Excel" | "Access" => {
                order = ["全部", "未答题", "已答题", "未满分", "全部通过"].to_vec();
                let pass_list_clone = pass_list.clone();
                // user_value_map.insert("status".to_string(), pass_list_clone.clone().into());
                // println!("pass_list_clone:{:?}",pass_list_clone);
                if !pass_list_clone.is_empty() {
                    user_value_map.insert("status".to_string(), "已答题".into());
                    *statics_result.entry("已答题").or_insert(0) += 1;

                    if pass_list_clone.len() as i32 == count{
                        // 遍历 pass_list查看是否全部为true
                        let mut all_pass = true;
                        for stat in pass_list_clone {
                            if !stat{
                                all_pass = false;
                                user_value_map.insert("status".to_string(), "未满分".into());
                                *statics_result.entry("未满分").or_insert(0) += 1;
                                break;
                            }
                        }
                        if all_pass{
                            user_value_map.insert("status".to_string(), "全部通过".into());
                            *statics_result.entry("全部通过").or_insert(0) += 1;
                        }
                    } else {
                        user_value_map.insert("status".to_string(), "未满分".into());
                        *statics_result.entry("未满分").or_insert(0) += 1;
                    }
                } else {
                    user_value_map.insert("status".to_string(), "未答题".into());
                    *statics_result.entry("未答题").or_insert(0) += 1;
                }

            }
            "OI" | "CodeBlank" => {
                order = ["全部", "未答题", "已答题", "未满分", "全部正确"].to_vec();
                let pass_list_clone = pass_list.clone();
                // user_value_map.insert("status".to_string(), pass_list_clone.clone().into());
                // println!("pass_list_clone:{:?}",pass_list_clone);
                if !pass_list_clone.is_empty() {
                    user_value_map.insert("status".to_string(), "已答题".into());
                    *statics_result.entry("已答题").or_insert(0) += 1;

                    if pass_list_clone.len() as i32 == count{
                        // 遍历 pass_list查看是否全部为true
                        let mut all_pass = true;
                        for stat in pass_list_clone {
                            if !stat{
                                all_pass = false;
                                user_value_map.insert("status".to_string(), "未满分".into());
                                *statics_result.entry("未满分").or_insert(0) += 1;
                                break;
                            }
                        }
                        if all_pass{
                            user_value_map.insert("status".to_string(), "全部正确".into());
                            *statics_result.entry("全部正确").or_insert(0) += 1;
                        }
                    } else {
                        user_value_map.insert("status".to_string(), "未满分".into());
                        *statics_result.entry("未满分").or_insert(0) += 1;
                    }
                } else {
                    user_value_map.insert("status".to_string(), "未答题".into());
                    *statics_result.entry("未答题").or_insert(0) += 1;
                }

            }
            "AI" => {
                order = ["全部", "未提交", "已提交", "未全部通过", "全部通过"].to_vec();
                let pass_list_clone = pass_list.clone();
                // user_value_map.insert("status".to_string(), pass_list_clone.clone().into());
                // println!("pass_list_clone:{:?}",pass_list_clone);
                if !pass_list_clone.is_empty() {
                    user_value_map.insert("status".to_string(), "已提交".into());
                    *statics_result.entry("已提交").or_insert(0) += 1;

                    if pass_list_clone.len() as i32 == count{
                        // 遍历 pass_list查看是否全部为true
                        let mut all_pass = true;
                        for stat in pass_list_clone {
                            if !stat{
                                all_pass = false;
                                user_value_map.insert("status".to_string(), "未全部通过".into());
                                *statics_result.entry("未全部通过").or_insert(0) += 1;
                                break;
                            }
                        }
                        if all_pass{
                            user_value_map.insert("status".to_string(), "全部通过".into());
                            *statics_result.entry("全部通过").or_insert(0) += 1;
                        }
                    } else {
                        user_value_map.insert("status".to_string(), "未全部通过".into());
                        *statics_result.entry("未全部通过").or_insert(0) += 1;
                    }
                } else {
                    user_value_map.insert("status".to_string(), "未提交".into());
                    *statics_result.entry("未提交").or_insert(0) += 1;
                }


            }
            "PPT" => {
                order = ["0%", "25%", "50%", "75%", "100%"].to_vec();
                
                let percentage = if count > 0 {
                    (page as f64 / count as f64 * 100.0).round() as usize
                } else {
                    0
                };

                match percentage {
                    0..=25 => {
                        *statics_result.entry("0%").or_insert(0) += 1;
                        user_value_map.insert("status".to_string(), "0%".into());
                    },
                    26..=50 => {
                        *statics_result.entry("25%").or_insert(0) += 1;
                        user_value_map.insert("status".to_string(), "25%".into());
                    },
                    51..=75 => {
                        *statics_result.entry("50%").or_insert(0) += 1;
                        user_value_map.insert("status".to_string(), "50%".into());
                    },
                    76..=99 => {
                        *statics_result.entry("75%").or_insert(0) += 1;
                        user_value_map.insert("status".to_string(), "75%".into());
                    },
                    100 => {
                        *statics_result.entry("100%").or_insert(0) += 1;
                        user_value_map.insert("status".to_string(), "100%".into());
                    },
                    _ => {
                        *statics_result.entry("0%").or_insert(0) += 1;
                        user_value_map.insert("status".to_string(), "0%".into());
                    },
                }
            }
            "MicroBit" => {
                order = ["全部", "未提交", "已提交"].to_vec();
                let pass_list_clone = pass_list.clone();
                // user_value_map.insert("status".to_string(), pass_list_clone.clone().into());
                // println!("pass_list_clone:{:?}",pass_list_clone);
                if !pass_list_clone.is_empty() {
                    user_value_map.insert("status".to_string(), "已提交".into());
                    *statics_result.entry("已提交").or_insert(0) += 1;
                } else {
                    user_value_map.insert("status".to_string(), "未提交".into());
                    *statics_result.entry("未提交").or_insert(0) += 1;
                }
            }
            // "CodeBlank" => {
            //     order = ["全部", "未提交", "已提交", "未全部通过", "全部通过"].to_vec();

            // }
            _ => {
                
            }            
        }
        // if section_type == "Scratch" {

        // }
        let mut user_value_map_json = serde_json::to_value(user_value_map).unwrap();
        let user_value = serde_json::to_value(user_value).unwrap();
        user_value_map_json.merge(&user_value);
        user_value_list.push(user_value_map_json);
    }

    
    // 创建一个空的数组
    let mut statics_result_list = Vec::new();

    // 按照顺序遍历并存入数组
    for key in order.iter() {
        if let Some(&value) = statics_result.get(*key) {
            let mut obj: HashMap<&str, Value> = HashMap::new();
            obj.insert("name", key.to_string().into());
            obj.insert("value", value.to_string().into());
            statics_result_list.push(obj);
        }
    }

    if params.select_state == "全部" {
        let mut response = HashMap::new();
        response.insert("list", serde_json::Value::Array(user_value_list.into_iter().map(|u| serde_json::to_value(u).unwrap()).collect()));
        response.insert("count", serde_json::Value::Number(count.into()));
        response.insert("sectionType", serde_json::Value::String(section_type));
        response.insert("itemNames", serde_json::Value::Array(item_names.into_iter().map(serde_json::Value::String).collect()));
        response.insert("keyMap", serde_json::to_value(key_map).unwrap());
        response.insert("staticsResult", serde_json::to_value(statics_result_list).unwrap());

        // 结束事务
        match txn.commit().await {
            Ok(r) => r,
            Err(e) => {
                return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
            }
        };

        return BusinessResponse::ok(response).to_json_result();

    }
    // 筛选
    let select_state = params.select_state.clone();
    
    let filter_list: Vec<Value>  = user_value_list.into_iter().filter(|item| {
        let status = item.get("status").unwrap();
        let status = serde_json::to_string(status).unwrap();
        let status = status.replace("\"", "");

        // println!("status:{},select_state:{}",status,select_state);
        // 如果status的值和select_state相等，则返回true
        if status == select_state {
            return true;
        }
        if (select_state == "已答题" || select_state == "已提交") && status != "未答题" && status != "未提交"{
            return true;
        }
        false
    }).collect();
    let mut response = HashMap::new();
        response.insert("list", serde_json::Value::Array(filter_list.into_iter().map(|u| serde_json::to_value(u).unwrap()).collect()));
        response.insert("count", serde_json::Value::Number(count.into()));
        response.insert("sectionType", serde_json::Value::String(section_type));
        response.insert("itemNames", serde_json::Value::Array(item_names.into_iter().map(serde_json::Value::String).collect()));
        response.insert("keyMap", serde_json::to_value(key_map).unwrap());
        response.insert("staticsResult", serde_json::to_value(statics_result_list).unwrap());

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };
    // println!("response:{:?}",response);
    BusinessResponse::ok(response).to_json_result()
}
// 获取章统计内容
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct QuerychapterContentAndType {
    class_i_d: i32,
    course_slug: String,
    chapter_name: String,
    // section_name: String,
    select_state: String,
    
}
#[post("/api/admin/result/chapter")]
pub async fn get_chapter_result(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Json<QuerychapterContentAndType>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 获取该班级名单
    let class_id = params.class_i_d;
    let class_member_list = match Query::users_list_find_by_team_id(&txn, class_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取班级成员列表 {}", e.to_string()).to_string() })
        }
    };
    // 获取学生id名单
    let student_ids = class_member_list.iter().map(|x| x.id).collect::<Vec<i32>>();
    // 获取章节列表
    let section_list = match Query::get_section_list_by_chapter(&txn, params.course_slug.clone(), params.chapter_name.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取章节信息 {}", e.to_string()).to_string() })
        }
    };

    let section_names: Vec<String> = section_list.iter()
        .filter_map(|i| i.get("sectionName"))
        .filter_map(|v| v.as_str())
        .map(String::from)
        .collect();    
    let section_numbers = section_list.len();

    let mut ai_section_name_list = Vec::new();

    for section in section_list.clone() {
        if let Some(section_type) = section.get("sectionType") {
            if section_type.as_str() != Some("AI") {
                continue;
            }
    
            if let Some(section_name) = section.get("sectionName") {
                if let Some(name) = section_name.as_str() {
                    ai_section_name_list.push(name.to_string());
                }
            }
        }
    }

    let section_ids: Vec<i32> = section_list.clone().iter()
        .filter_map(|section| section.get("sectionID"))
        .filter_map(|v| v.as_i64())
        .map(|id| id as i32)
        .collect();

    // 获取答题记录
    let record_list = match Query::get_section_record_find_by_user_ids_and_section_ids(&txn, student_ids, section_ids).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取章节记录列表 {}", e.to_string()).to_string() })
        }
    };

    let mut statics_result: HashMap<&str, usize> = HashMap::new();
    statics_result.insert("全部", class_member_list.len());
    statics_result.insert("未答题", 0);
    statics_result.insert("已答题", 0);
    statics_result.insert("运行错误", 0);
    statics_result.insert("全部通过", 0);

    let mut user_value_list = Vec::new();

    
    for i in 0..class_member_list.len() {
        let mut user_value_map: HashMap<String, Value> = HashMap::new();
        let mut score_list = Vec::new();
        let user_value = class_member_list[i].clone();
        let user_id = user_value.id;

        let records:Vec<SectionRecordItem> = record_list.iter()
            .filter(|value| value.user_id == user_id)
            .cloned()
            .collect();

        let submit_time = if let Some(last_record) = records.last() {
            last_record.updated_at.clone()
        } else {
            None
        };
        user_value_map.insert("submitTime".to_string(), serde_json::to_value(submit_time).unwrap());
        for section in section_list.iter() {
            if let (
                Some(section_id),
                Some(section_name),
                Some(section_type)
            ) = (
                section.get("sectionID").and_then(|v| v.as_i64().map(|id| id as i32)),
                section.get("sectionName").and_then(|v| v.as_str()),
                section.get("sectionType").and_then(|v| v.as_str()),
            ){
                let record = if let Some(record) = records.iter().find(|data| data.section_id == section_id) {
                    Some(record.clone())
                } else {
                    None
                };
                if record.is_none() {
                    user_value_map.insert(section_name.to_string(), serde_json::to_value("未答题").unwrap());
                    continue;
                }
                if section_type == "Scratch" {
                    if let Some(student_record) = record.clone().unwrap().record{
                        if let Some(judge_steps) = student_record.get("judge_steps").cloned() {
                            // 获取学生所有得分
                            let scores: Vec<i32> = judge_steps.as_array().unwrap_or(&vec![])
                                .iter()
                                .map(|i| {
                                    if i.get("manual").and_then(|v| v.as_bool()).unwrap_or(false) {
                                        i.get("rate").and_then(|v| v.as_i64()).unwrap_or(0) as i32
                                    } else {
                                        if i.get("pass").and_then(|v| v.as_bool()).unwrap_or(false) {
                                            i.get("score").and_then(|v| v.as_i64()).unwrap_or(0) as i32
                                        } else {
                                            0
                                        }
                                    }
                                })
                                .collect();
                            // 计算学生总分
                            let total_score: i32 = scores.iter().sum();
                            user_value_map.insert(section_name.to_string(), serde_json::to_value(total_score).unwrap());

                            // 计算课程总分
                            let course_scores: Vec<i32> = judge_steps.as_array()
                                .unwrap_or(&vec![])
                                .iter()
                                .filter_map(|v| v.get("score").and_then(|s| s.as_i64()).map(|s| s as i32))
                                .collect();
                            let total_course_scores: i32 = course_scores.iter().sum();

                            user_value_map.insert(format!("{}-total", section_name), serde_json::to_value(total_course_scores).unwrap());
                            // 记录课程是否满分
                            score_list.push(total_course_scores <= total_score);

                        }
                    }
                    continue;
                }
                if let Some(section_record) = record.clone(){
                    let total_score = section_record.total_score;
                    let pass_count = section_record.pass_count;
                    let value = if pass_count > 0 && total_score > 0 {
                        let score = ((pass_count as f64 / total_score as f64) * 10000.0).round() / 100.0;
                        if score >= 100.0 {
                            100.0
                        } else {
                            score
                        }
                    } else {
                        0.0
                    };
                    user_value_map.insert(section_name.to_string(), serde_json::to_value(value).unwrap());
                    
                    score_list.push(value >= 100.0);
                }
            }
        }
        user_value_map.insert("scorelist".to_string(), serde_json::to_value(score_list.clone()).unwrap());
        // 统计结果
        if score_list.is_empty() {
            statics_result.insert("未答题", statics_result.get("未答题").unwrap() + 1);
        }else {
            statics_result.insert("已答题", statics_result.get("已答题").unwrap() + 1);
        }
        if score_list.len() >= section_numbers && score_list.iter().all(|&x| x) {
            statics_result.insert("全部通过", statics_result.get("全部通过").unwrap() + 1);
        }
        for &score in score_list.iter() {
            if !score {
                statics_result.insert("运行错误", statics_result.get("运行错误").unwrap() + 1);
                break;
            }
        }
        let mut user_value_map_json = serde_json::to_value(user_value_map).unwrap();
        let user_value = serde_json::to_value(user_value).unwrap();
        user_value_map_json.merge(&user_value);
        user_value_list.push(user_value_map_json);
    }

    // 定义顺序
    let order = ["全部", "未答题", "已答题", "运行错误", "全部通过"];

    // 创建一个空的数组
    let mut statics_result_list = Vec::new();

    // 按照顺序遍历并存入数组
    for key in order.iter() {
        if let Some(&value) = statics_result.get(*key) {
            let mut obj: HashMap<&str, Value> = HashMap::new();
            obj.insert("name", key.to_string().into());
            obj.insert("value", value.to_string().into());
            statics_result_list.push(obj);
        }
    }
    
    let select_state = params.select_state.clone();
    if select_state == "全部" {
        let mut response = HashMap::new();
        response.insert("list", serde_json::Value::Array(user_value_list.into_iter().map(|u| serde_json::to_value(u).unwrap()).collect()));
        response.insert("staticsResult", serde_json::to_value(statics_result_list).unwrap());
        response.insert("sectionNames", serde_json::to_value(section_names).unwrap());
        // 结束事务
        match txn.commit().await {
            Ok(r) => r,
            Err(e) => {
                return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
            }
        };

        return BusinessResponse::ok(response).to_json_result();
    }
    // 筛选
    let mut select_map: HashMap<String, Vec<String>> = HashMap::new();

    select_map.insert("
        运行错误".to_string(), 
        vec!["RE", "WA", "TL", "PE", "RI", "错误", "运行报错"]
            .iter()
            .map(|s| s.to_string())
            .collect(),
    );
    select_map.insert(
        "已答题".to_string(),
        vec!["运行通过", "AC", "RE", "WA", "TL", "PE", "RI", "正确", "错误", "未运行"]
            .iter()
            .map(|s| s.to_string())
            .collect(),
    );    
    select_map.insert(
        "全部通过".to_string(), 
        vec!["100", "AC", "正确", "运行通过"]
        .iter()
        .map(|s| s.to_string())
        .collect(),
    );

    let filter_list: Vec<Value>  = user_value_list.into_iter().filter(|item| {
        let filter_state = select_map.get(&select_state).cloned().unwrap_or_else(|| vec![select_state.clone()]);
        
        match select_state.as_str() {
            "未答题" => item.get("scorelist").unwrap().as_array().unwrap().is_empty(),
            "已答题" => !item.get("scorelist").unwrap().as_array().unwrap().is_empty(),
            "全部通过" => {
                let score_list = item.get("scorelist").unwrap().as_array().unwrap();
                score_list.len() >= section_numbers && score_list.iter().all(|i| i.as_bool().unwrap_or(false))
            },
            "运行错误" => {
                let score_list = item.get("scorelist").unwrap().as_array().unwrap();
                score_list.iter().any(|i| !i.as_bool().unwrap_or(false))
            },
            _ => item.as_object().map_or(false, |obj| obj.values().any(|i| filter_state.contains(&i.as_str().unwrap().to_string()))),
        }
    }).collect();
    let mut response = HashMap::new();
        response.insert("list", serde_json::Value::Array(filter_list.into_iter().map(|u| serde_json::to_value(u).unwrap()).collect()));
        response.insert("staticsResult", serde_json::to_value(statics_result_list).unwrap());
        response.insert("sectionNames", serde_json::to_value(section_names).unwrap());
    
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    BusinessResponse::ok(response).to_json_result()
}
// 导出课程进度
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct ExportCourseProgressExcel {
    class_i_ds: Vec<i32>,
    course_slug: String,    
}
#[post("api/admin/course/progress/exportCourseProgressExcel")]
pub async fn export_course_progress_excel(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Json<ExportCourseProgressExcel>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let course_slug = params.course_slug.clone();
    let class_ids = params.class_i_ds.clone();
    
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    
    // 获取indics
    let indics = match Query::course_indics_find_by_slug(&txn, course_slug).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取章节记录列表 {}", e.to_string()).to_string() })
        }
    };
    // 获取章节名称
    let chapter_names: Vec<String> = if let Some(indics) = indics.clone() {
        let indics = indics.get("indics").unwrap();
        let indics: Value = serde_json::from_str(indics.clone().as_str().unwrap()).unwrap();
        // let indics = serde_json::to_value(indics.clone().as_str().unwrap()).unwrap();
        indics.as_array().unwrap().iter().map(|i| i.get("chapterName").unwrap().as_str().unwrap().to_string()).collect()
    } else {
        Vec::new()
    };
    let mut class_map: HashMap<String, Value> = HashMap::new();
    let mut key_map: HashMap<i32, String> = HashMap::new();
    for class_id in class_ids.clone(){
        // 获取用户列表
        let user_ids = match Query::user_id_find_by_team_ids(&txn, class_id).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::ProcessError { reason: format!("无法获取章节记录列表 {}", e.to_string()).to_string() })
            }
        };

        let user_list = match Query::users_find_by_user_ids(&txn, user_ids.clone()).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::ProcessError { reason: format!("无法获取章节记录列表 {}", e.to_string()).to_string() })
            }
        };
        // 按章遍历获取用户记录
        let mut record_map = HashMap::new();
        for chapter_name in chapter_names.clone(){
            // 获取当前章的内容
            let current_chapter = if let Some(indics) = indics.clone() {
                let indics = indics.get("indics").unwrap();
                let indics: Value = serde_json::from_str(indics.clone().as_str().unwrap()).unwrap();
                indics.as_array().unwrap().iter().find(|i| i.get("chapterName").unwrap().as_str().unwrap() == chapter_name.as_str()).unwrap().clone()
            } else {
                Value::Null
            };
            // 获取该章所有节
            let current_sections = current_chapter.get("sections").unwrap();
            let section_ids = current_sections.as_array().unwrap().iter().map(|i| i.get("sectionID").unwrap().as_i64().unwrap() as i32).collect::<Vec<i32>>();
            // 获取学生记录
            let user_records = match Query::get_user_records(&txn, user_ids.clone(), section_ids).await {
                Ok(r) => r,
                Err(e) => {
                    txn.rollback().await.unwrap();
                    return Err(BusinessError::ProcessError { reason: format!("无法获取章节记录列表 {}", e.to_string()).to_string() })
                }
            };
            record_map.insert(chapter_name, user_records);
        }
        let mut num = 1;

        let mut user_list_res = Vec::new();
        for user in user_list.clone() {
            let mut user_res = HashMap::new();
            // 遍历user的所有值，存入user_res
            user_res.insert("id".to_string(), user.id.to_string());
            user_res.insert("username".to_string(), user.username.clone());
            user_res.insert("name".to_string(), user.name.clone());
            let mut count = 1;
            for chapter_name in chapter_names.clone(){
                let current_chapter = if let Some(indics) = indics.clone() {
                    let indics = indics.get("indics").unwrap();
                    let indics: Value = serde_json::from_str(indics.clone().as_str().unwrap()).unwrap();
                    indics.as_array().unwrap().iter().find(|i| i.get("chapterName").unwrap().as_str().unwrap() == chapter_name.as_str()).unwrap().clone()
                } else {
                    Value::Null
                };
                // 获取该章所有节
                let current_sections = current_chapter.get("sections").unwrap();
                let section_ids = current_sections.as_array().unwrap().iter().map(|i| i.get("sectionID").unwrap().as_i64().unwrap() as i32).collect::<Vec<i32>>();

                let current_records = record_map[&chapter_name].clone();
                let current_record = current_records.iter().find(|i| i.user_id == user.id).clone();
                
                // 初始化 user_count
                let mut user_count = 0;

                if let Some(current_record) = current_record {
                    user_count = current_record.user_count;
                }
                //将count转为中文数字
                let chinese_count: result::Result<String, chinese_number::NumberToChineseError> = count.to_chinese(ChineseVariant::Simple, ChineseCase::Lower,  ChineseCountMethod::TenThousand);
                let chinese_count = chinese_count.unwrap();
                user_res.insert("serialNumber".to_string(), num.clone().to_string());
                user_res.insert(format!("章节{} {} 通过数(共{}节)", chinese_count, chapter_name, section_ids.len()), user_count.to_string());
                key_map.insert(count, format!("章节{} {} 通过数(共{}节)", chinese_count, chapter_name, section_ids.len()));
                count += 1;
                
            }
            user_list_res.push(user_res);
            num += 1;
        }
        class_map.insert(class_id.to_string(), serde_json::to_value(user_list_res).unwrap());
    }
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };
    // println!("下载完成:{:?}", class_map);
    class_map.insert("keyMap".to_string(), serde_json::to_value(key_map).unwrap());
    BusinessResponse::ok(class_map).to_json_result()
}
// 下载课程
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct CourseDownload {
    url: String,
    course_slug: String,    
}
// #[post("api/admin/courseDownload")]
pub async fn course_download(
    download_progress: Arc<Mutex<HashMap<String, ProgressData>>>,
    url: String,
    course_slug: String, 
    tx: Sender<()>
) -> bool {
    let url = url.clone();

    // 测试数据
    // let url = "https://hxr.iyopu.com/s/csxx/file/tmp/%E6%B0%A6%E6%98%9F%E4%BA%BA%E6%9C%BA%E6%88%BF%E6%95%99%E5%AD%A6%E7%B3%BB%E7%BB%9F%E6%BC%94%E7%A4%BA%E8%AF%BE%E7%A8%8B_20250313104928.zip?condition=%7B%22fileName%22%3A%22%E6%B0%A6%E6%98%9F%E4%BA%BA%E6%9C%BA%E6%88%BF%E6%95%99%E5%AD%A6%E7%B3%BB%E7%BB%9F%E6%BC%94%E7%A4%BA%E8%AF%BE%E7%A8%8B_20250313104928.zip%22%7D&_csrf=DSpj3uG4oAGNDUZ2zbOtFs55".to_string();
    let course_slug = course_slug.clone();
    

    // if res1 || res2 {
    //     return Err(BusinessError::ProcessError { reason: "下载课程失败".to_string() });
    // }
    // // 生成随机数
    // let random_number: u64 = rand::thread_rng().gen();
    // 当前时间
    // let now = Utc::now();

    // 生成 sha2 唯一标识
    // let mut hasher = Sha256::new();
    // hasher.update(format!("{}-{}-{}", course_slug, url, now));
    // let sha2_id = format!("{:x}", hasher.finalize());
    // let sha2_id_clone = sha2_id.clone();

    // 初始化进度为 0
    let mut progress_map = download_progress.lock().unwrap();
    let data = ProgressData { 
        progress: 0.0,
        course_slug: course_slug.clone(),
        task_type: "courseDownload".to_string(),
    };
    progress_map.insert(course_slug.clone(), data);
    let progress_map = Arc::clone(&download_progress);

    let course_slug_clone = course_slug.clone();
    
    // 先删除对应的文件
    let dir_path = get_path_in_exe_dir("course");
    if dir_path.exists() {
        let course_file_path = dir_path.join(format!("{}.zip", course_slug_clone));
        if course_file_path.exists() {
            fs::remove_file(course_file_path).expect("删除文件失败");
        }
    }

    tokio::spawn(async move {
        // 获取HTTP客户端
        let client = reqwest::Client::new();
        
        // 下载课程文件

        // 获取路径，如果路径不存在尝试创建
        let dir_path = get_path_in_exe_dir("course");
        if !dir_path.exists() {
            fs::create_dir_all(&dir_path).expect("创建目录失败");
        }
            let tmp_dir_path = get_path_in_exe_dir("tmp").join("course");
        if !tmp_dir_path.exists() {
            fs::create_dir_all(&tmp_dir_path).expect("创建目录失败");
        }
        let target_file_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));
        let tmp_file_path = get_path_in_exe_dir("tmp").join("course").join(format!("{}.zip", course_slug));

        // 创建并打开文件
        let mut file = tokio::fs::OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .open(tmp_file_path.clone())
            .await
            .map_err(|e| println!("创建文件失败: {}", e.to_string()))
            .unwrap();

        // 获取已下载的字节数
        let mut downloaded_size = file.metadata().await.unwrap().len();
        // 尝试断点续传
        let mut response = client
            .get(url.clone())
            .header("Range", format!("bytes={}-", downloaded_size))
            .send()
            .await
            .context("发送请求失败")
            .unwrap();

        // 如果服务器不支持断点续传，则重新下载
        if response.status() != reqwest::StatusCode::PARTIAL_CONTENT {
            downloaded_size = 0;
            file.set_len(0)
                .await
                .context("清空文件失败")
                .unwrap();
            file.seek(SeekFrom::Start(0))
                .await
                .context("文件指针重置失败")
                .unwrap();

            response = client
                .get(url.clone())
                .send()
                .await
                .context("发送请求失败")
                .unwrap();

            if !response.status().is_success() {

            }
        }
        // 获取文件的总大小
        let total_size = response.content_length();
        let total_size = match total_size {
            Some(size) => size,
            None => {
                println!("无法获取文件总大小");
                return;
            }
            
        };
        // 下载内容并写入文件
        let mut stream = response.bytes_stream();

        while let Some(chunk) = stream.next().await {
            let chunk = chunk.context("获取数据块失败").unwrap();
            file.write_all(&chunk).await.context("写入数据块失败").unwrap();
            downloaded_size += chunk.len() as u64;
            // println!("下载进度: {} {}", downloaded_size, total_size);
            if total_size < downloaded_size {
                let data = ProgressData { 
                    progress : -1.0,
                    course_slug: course_slug.clone(),
                    task_type: "courseDownload".to_string(),
                    };
                    let mut progress_map = progress_map.lock().unwrap();
                    progress_map.insert(course_slug.clone(), data);
            } else {
                // 计算下载阶段的进度
                let stage_progress = downloaded_size as f64 / total_size as f64 * 0.9;
                let progress = stage_progress * 100.0;
                let data = ProgressData { 
                    progress,
                    course_slug: course_slug.clone(),
                    task_type: "courseDownload".to_string(),
                    };
                let mut progress_map = progress_map.lock().unwrap();
                progress_map.insert(course_slug.clone(), data);

            }

        }

        // 关闭文件
        drop(file);
        // 如果目标文件夹存在对应文件则删除
        if Path::new(&target_file_path).exists() {
            fs::remove_file(&target_file_path).expect("Failed to delete file");
        }
        // 将文件从临时目录移动到指定目录
        let _ = tokio::fs::rename(tmp_file_path, target_file_path.clone()).await.context("移动文件失败");
        // 解压课程文件
        let course_file_path = get_path_in_exe_dir("course").join(course_slug.clone());
        // let zip = extract_zip(&target_file_path, &course_file_path);
        match extract_zip(&target_file_path, &course_file_path) {
            Ok(r) => r,
            Err(_) => {
            }
        }

        // 发送停止信号
        let _ = tx.send(()).await;
        ()
    });

    
    true
}
// #[get("api/admin/progress/{sha2_id}")]
pub async fn get_download_progress(
    app_state: web::Data<AppState>,
    path: web::Path<String>,
) -> Result<HttpResponse, BusinessError> {
    let sha2_id = path.into_inner();

    // 获取当前进度
    let progress_map = app_state.download_progress.lock().unwrap();
    let progress = match progress_map.get(&sha2_id) {
        Some(value) => value.clone(), // 如果存在值，则返回值的克隆
        None => return Err(BusinessError::InternalError { reason: format!("不存在的任务") }), // 如果不存在值，则返回错误
    };
    let course_slug = progress.course_slug;
    let res;
    match progress.task_type.as_str() {
        "courseDownload" => {
            res = match progress.progress {
                -0.5 => json!({ "type": "courseDownload",
                                "courseSlug": course_slug,
                                "status": "error",
                                "message":"下载失败"
                            }),
                -1.0 => json!({ "type": "courseDownload",
                                "courseSlug": course_slug,
                                "status": "error",
                                "message":"数据库导入失败"
                            }),
                0.5 => json!({  "type": "courseDownload",
                                "courseSlug": course_slug,
                                "status": "process",
                                "message":"下载成功，正在将数据导入数据库"
                            }),
                1.0 => json!({  "type": "courseDownload",
                                "courseSlug": course_slug,
                                "status": "completed",
                                "message":"课程下载完成！"
                            }),
                0.0 => json!({  "type": "courseDownload",
                                "courseSlug": course_slug,
                                "status": "process",
                                "message":"正在下载课程"
                            }),
                _   => return Err(BusinessError::InternalError { reason: format!("未知状态")})
            };
        }
        _ => {
            return Err(BusinessError::InternalError { reason: format!("不存在的任务")})
        }
        
    }

    
    // 返回进度
    BusinessResponse::ok(res).to_json_result()
}

// 获取章统计内容
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
struct QueryParams {
    // index: i32,
    chapter_name: String,
    section_name: String,
    course_slug: String,
    user_i_d: i32,
}
pub async fn get_micro_apps_file_record(
    micro_apps: Vec<Value>,
    course_slug: String,
    chapter_name: String,
    user_id: i32,
) -> Result<HashMap<String, Value>, BusinessError> {
    let mut res = HashMap::new();
    for micro_app in micro_apps {
        let micro_app_name = micro_app["UUID"].as_str().unwrap().to_string();
        let file_type = micro_app["type"].as_str().unwrap().to_string();
        let file_name = micro_app["fileName"].as_str().unwrap().to_string();
        let source_params = micro_app["config"].clone();
        // 获取固定属性
        let ext_name = MICRO_TYPE_NAME_MAP[file_type.as_str()].ext_name.clone();
        // let binary = MICRO_TYPE_NAME_MAP[file_type.as_str()].binary.clone();
        let full_name = format!("{}.{}",file_name,ext_name);
        let path_dir = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter_name.clone()).join(full_name.clone());
        // 读取文件内容
        let file_content = match fs::read_to_string(path_dir.clone()) {
            Ok(content) => Some(content),
            Err(_) => None,
        };
        let file_content = file_content.unwrap_or("default".to_string());
        // let file_stat = tokio::fs::metadata(path_dir).await.unwrap(); // 异步版本
        let code_res;

        match file_type.as_str() {
            "text" => {
                if file_content == "default" {
                    code_res = json!({
                        "fileName": file_name,
                        "message": "default",
                        "sourceParams": source_params,
                        "type": file_type,
                    });
                } else {
                    let file_content: Json = serde_json::from_str(&file_content).unwrap();
                    code_res = json!({
                        "fileName": file_name,
                        "message": file_content,
                        "sourceParams": source_params,
                        "type": file_type,
                    });
                }
            }
            "table" => {
                // 获取表格原始格式
                let cell_info = micro_app["config"]["cell"].clone();
                println!("cell_info: {:?}", cell_info);

                // 预处理 cell_info，将 null 值转换为空数组
                let processed_cell_info = match cell_info {
                    Value::Array(arr) => {
                        let processed: Vec<Value> = arr.into_iter().map(|item| {
                            if item.is_null() {
                                Value::Array(vec![]) // 将 null 转换为空数组
                            } else {
                                item
                            }
                        }).collect();
                        Value::Array(processed)
                    }
                    _ => cell_info
                };

                let cell_info: Vec<Vec<Option<String>>> = match serde_json::from_value(processed_cell_info) {
                    Ok(info) => info,
                    Err(e) => {
                        eprintln!("Failed to deserialize cell_info: {}", e);
                        return Err(BusinessError::InternalError { 
                            reason: format!("数据解析失败: {}", e) 
                        });
                    }
                };

                // 获取表格内容
                let message;
                let file_content: Vec<u8> = serde_json::to_vec(&file_content).unwrap();
                let file_content = match str::from_utf8(&file_content) {
                    Ok(v) => v.trim_matches('"').to_string(),
                    Err(e) => panic!("Invalid UTF-8 sequence: {}", e),
                };
                let file_content: Vec<String> = file_content.split("\\n").map(String::from).collect();
                if !file_content.is_empty() {
                    let result = file_content
                        .iter()
                        .enumerate()
                        .map(|(i, data)| {
                            data.split(',')
                                .enumerate()
                                .map(|(j, v)| json!({
                                    "readOnly": if i < cell_info.len() && j < cell_info[i].len() {
                                        true
                                    } else {
                                        false
                                    },
                                    "style": {"background": "rgb(238, 238, 238)"},
                                    "value": v,
                                }))
                                .collect::<Vec<Value>>()
                        })
                        .collect::<Vec<Vec<Value>>>();
            
                    message = json!(result);
                } else {
                    message = json!([]);
                }
                code_res = json!({
                    "fileName": file_name,
                    "message": message,
                    "sourceParams": source_params,
                    "type": file_type,
                });
            }
            "mind" | "flow" | "spreadsheet" | "networksimulator" | "drawio" => {
                if file_content == "default" {
                    code_res = json!({
                        "fileName": file_name,
                        "message": "default",
                        "sourceParams": source_params,
                        "type": file_type,
                    });
                } else {
                    let file_content: Json = serde_json::from_str(&file_content).unwrap();
                    code_res = json!({
                        "fileName": file_name,
                        "message": file_content,
                        "sourceParams": source_params,
                        "type": file_type,
                    });
                }
            }
            _ => {
                return Err(BusinessError::InternalError { reason: format!("未识别的微应用类型{}", file_type)});
            }
            
        }
        // println!("{:?}", file_stat);
        let result = json!({
            "fullName": file_name,
            "result": json!({
              "codeResult": [code_res],
            }),
            "status": "运行通过",
            "type": "success",
            // "fileStat": file_stat
        });
        res.insert(micro_app_name, result);
    }
    Ok(res)
}


#[post("api/admin/course/getCodeRecord")]
async fn get_code_record(
    session: Session,
    app_state: web::Data<AppState>,
    param: web::Json<QueryParams>
) -> Result<HttpResponse, BusinessError> {
     // 加载Session
     let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });    
    };

    let user_id = param.user_i_d.clone();
    let section_name = param.section_name.clone();
    let course_slug = param.course_slug.clone();
    let chapter_name = param.chapter_name.clone();

    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // println!("user_id: {}, section_name: {}, course_slug: {}, chapter_name: {}", user_id, section_name, course_slug, chapter_name);
    // 查询章节id
    let section = match Query::get_section(&txn, course_slug.clone(), chapter_name.clone(), section_name.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取该章节 {}", e.to_string()).to_string() })
        }
    };
    if let None = section {
        txn.rollback().await.unwrap();
        return Err(BusinessError::ProcessError { reason: format!("该章节不存在").to_string() })
    }
    let section = section.unwrap();

    // 查询学生记录
    let record = match Query::get_user_record(&txn, user_id, section.id.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取学生记录 {}", e.to_string()).to_string() })
        }
    };

    // 获取用户信息
    let user_info = match Query::user_find_by_id(&txn, user_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取用户信息 {}", e.to_string()).to_string() })
        }
    };
    if let None = user_info {
        txn.rollback().await.unwrap();
        return Err(BusinessError::ProcessError { reason: format!("用户信息不存在").to_string() })
    }
    let user_info = user_info.unwrap();
    let user_info = json!({
        "name": user_info.name,
        "username": user_info.username,
    });

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    let section_type = section.section_type;

    // 用户没有答题记录仅返回用户信息和课程基本信息
    if let None = record {
        let res = json!({
            "sectionType": section_type,
            "userInfo": user_info,
        });
        return BusinessResponse::ok(res).to_json_result()
    }
    let record = record.unwrap().record;
    if let None = record {
        return Err(BusinessError::ProcessError { reason: format!("暂无提交记录！").to_string() })
    }
    let record = record.unwrap();
    match section_type.as_str() {
        "AI" => {
            let course_path = get_course_path(course_slug.clone(), chapter_name.clone(), section_name).await.unwrap();
            // 读取文件内容
            let file_content = fs::read_to_string(course_path).expect("加载课程文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).unwrap(); 
            let cells = file_content["cells"].clone(); 
            if cells == Value::Null {
                return Err(BusinessError::ProcessError { reason: format!("加载课程文件失败").to_string() })
            }
            let cells = serde_json::from_value::<Vec<Value>>(cells).unwrap();
            // 加载文件中所有需要提交的块
            let mut submit_blocks = Vec::new();
            // 整理微应用块
            let mut micro_app_blocks = Vec::new();
            for cell in cells {
                // 非代码类
                if cell["cell_type"] != "code" {
                    continue;
                }
                // 隐藏代码块
                if cell["source"] != Value::Null {
                    if let Some(first_element) = cell["source"].get(0) {
                        let first_element = first_element.as_str().unwrap();
                        if first_element.matches(r"^# >隐藏并自动执行").next().is_some() {
                            continue;
                        }
                    }
                }

                // 资源
                if cell["metadata"]["type"] == "resources" || cell["metadata"]["type"] == "filepreviewer" {
                    continue;
                }
                if cell["metadata"]["type"] != Value::Null{
                    micro_app_blocks.push(cell["metadata"].clone());
                }
                submit_blocks.push(cell["metadata"].clone());
            }
            // 获取微应用的文件系统记录
            let file_uuid_map = match get_micro_apps_file_record(micro_app_blocks.clone(), course_slug, chapter_name, user_id).await {
                Ok(r) => r,
                Err(e) => {
                    return Err(BusinessError::ProcessError { reason: format!("无法获取微应用文件记录 {}", e.to_string()).to_string() })
                }
            };
            // 拼接记录
            let mut code_counter = 1;
            let mut result = Vec::new();
            for submit_block in submit_blocks {
                let uuid = submit_block["UUID"].clone();
                let uuid = serde_json::to_string(&uuid).unwrap();
                let uuid = uuid.replace("\"", "");

                let r#type = submit_block.get("type").map(|v| v.clone());
                let res_type;
                if let Some(r#type) = r#type {
                    res_type = serde_json::to_string(&r#type).unwrap();
                } else {
                    res_type = "code".into();
                }
                let res_type = res_type.replace("\"", "");
                
                // 块内容
                let mut item: HashMap<String, Value> = serde_json::from_value(submit_block.clone()).unwrap();
                // 用户记录
                let record: HashMap<String, HashMap<String, Value>> = serde_json::from_value(record.clone()).unwrap();
                if let Some(record) = record.get(&uuid) {
                    item.extend(record.clone());
                }
                // 如果答题记录中存在，并且没有文件，就是曾经被清掉
                let history = record.contains_key(&uuid) && !file_uuid_map.contains_key(&uuid);
                item.insert("history".to_string(), serde_json::Value::Bool(history));

                // 组装
                if res_type == "code" {
                    item.insert("UUID".to_string(), serde_json::Value::String(uuid));
                    item.insert("name".to_string(), serde_json::Value::String(format!("{}{}", "代码", code_counter)));
                    code_counter += 1;
                } else {
                    let type_name = MICRO_TYPE_NAME_MAP[res_type.as_str()].type_name.clone();
                    item.insert("name".to_string(), serde_json::Value::String(format!("{}{}", type_name, item["fileName"].as_str().unwrap())));
                    let file_item = file_uuid_map.get(&uuid).unwrap();
                    let file_item = serde_json::from_value::<HashMap<String, Value>>(file_item.clone()).unwrap();
                    item.extend(file_item);
                }
                result.push(item);

            }

            let res = json!({
                "results": result,
                "sectionType": section_type,
                "userInfo": user_info,
            });
            return BusinessResponse::ok(res).to_json_result()
        },
        "OI" => {
            let course_path = get_course_path(course_slug, chapter_name, section_name).await.unwrap();
            // 读取文件内容
            let file_content = fs::read_to_string(course_path).expect("加载课程记录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).unwrap(); 
            let questions = file_content["questions"].clone(); 
            let questions_obj = questions.clone().as_array().unwrap().clone();
            let mut question_list = Vec::new();
            for question in questions_obj {
                let uuid = question["UUID"].clone();
                let uuid = uuid.as_str().unwrap();   
                let question_type = question["questionType"].clone();
                let question_type = question_type.as_str().unwrap();
                if question_type == "文本" {
                    continue;
                }
                let raw_answer = question["answer"].clone();
                let content = question["content"].clone();
                let options = question["options"].clone();

        
                // let _processed_answer = match question_type {
                //     "填空题" => {
                //         let raw_answer = serde_json::from_value::<Vec<Value>>(raw_answer.clone()).unwrap();
                //         println!("raw_answer: {:?}", raw_answer);
                //         raw_answer.iter()
                //             .map(|r| serde_json::from_value(r.clone())
                //                 .map(|i: Value| {
                //                     i["text"].as_str()
                //                         .map(|s| s.to_string())
                //                         .unwrap_or_else(|| {
                //                             // 记录错误或返回错误信息
                //                             eprintln!("Failed to parse 'text' field: {:?}", i);
                //                             "".to_string() // 返回默认值
                //                         })
                //                 })
                //             )
                //             .into_iter()
                //             .collect::<Result<Vec<String>, _>>()
                //             .unwrap_or_default()
                //             .join("、")
                //     },
                //     "多选题" => {
                //         let raw_answer = serde_json::from_value::<Vec<String>>(raw_answer.clone()).unwrap();
                //         raw_answer.join("、")
                //     },
                //     "单选题" => {
                //         let raw_answer = serde_json::from_value::<String>(raw_answer.clone()).unwrap();
                //         raw_answer
                //     },
                //     "选择填空题" => {
                //         // let raw_answer: HashMap<String, String> = serde_json::from_value(raw_answer).unwrap();
                //         // // 提取所有值并存储到 Vec 中
                //         // let raw_answer: Vec<String> = raw_answer.values().cloned().collect();
                //         // raw_answer.join("、")
                //         "选择填空题暂不支持".to_string()
                //     },
                //     _ => {
                //         let raw_answer = serde_json::from_value::<String>(raw_answer.clone()).unwrap();
                //         raw_answer
                //     },
                // };

        
                let record = record.get(&uuid);
                let student_answer = record.map(|r| r["answer"].clone()).unwrap_or_default();
                let status = record
                    .and_then(|r| r.get("status"))
                    .and_then(|v| v.as_bool())
                    .unwrap_or(false);     

                // let processed_question = json! ({
                //     "uuid": uuid,
                //     "questionType": question_type,
                //     "answer": raw_answer,
                //     "studentAnswer": student_answer,
                //     "status": status,
                //     "content": content,
                //     "options": options,
                // });
                let mut obj = serde_json::Map::new();
                obj.insert("uuid".to_string(), json!(uuid));
                obj.insert("questionType".to_string(), json!(question_type));
                obj.insert("answer".to_string(), raw_answer);
                obj.insert("studentAnswer".to_string(), student_answer);
                obj.insert("status".to_string(), json!(status));
                if !content.is_null() {
                    obj.insert("content".to_string(), content);
                }
                if !options.is_null() {
                    obj.insert("options".to_string(), options);
                }
                
        
                question_list.push(json!(obj));
            }
            let res = json!({
                "results": question_list,
                "sectionType": section_type,
                "userInfo": user_info,
            });
            return BusinessResponse::ok(res).to_json_result()
            
        },
        "Access" | "Excel" => {
            let course_path = get_course_path(course_slug, chapter_name, section_name).await.unwrap();
            // 读取文件内容
            let file_content = fs::read_to_string(course_path).expect("加载课程记录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).unwrap(); 
            let instructions = file_content["instructions"].clone();
            let mut instructions_obj = instructions.clone().as_array().unwrap().clone();

            if section_type == "Access" {
                instructions_obj.remove(0); // 去除首个初始文件
            }
            let instructions = serde_json::Value::Array(instructions_obj);

            let res = json!({
                "results": record,
                "sectionType": section_type,
                "userInfo": user_info,
                "instructions":instructions
            });
            return BusinessResponse::ok(res).to_json_result()

        },
        "CodeBlank" => {
            let course_path = get_course_path(course_slug, chapter_name, section_name).await.unwrap();

            // 读取文件内容
            let file_content = fs::read_to_string(course_path).expect("加载课程记录文件失败");
            let file_content = serde_json::from_str::<Value>(&file_content).unwrap(); 
            let question = file_content["questions"][0].clone(); 
            let mut question_obj = question.clone().as_object().unwrap().clone();

            // 合并题面和答案
            question_obj.insert("studentAnswer".to_string(), record.into());
            let result = serde_json::Value::Object(question_obj);

            let res = json!({
                "results": result,
                "sectionType": section_type,
                "userInfo": user_info,
            });
            return BusinessResponse::ok(res).to_json_result()
        },
        "OJ" | "PPT" => {
            let res = json!({
                "results": record,
                "sectionType": section_type,
                "userInfo": user_info,
            });
            return BusinessResponse::ok(res).to_json_result()
        },
        "Scratch" | "MicroBit" => {
            let file_name = serde_json::to_string(&record["file_name"].clone()).unwrap();
            // 去除双引号
            let file_name = file_name.replace("\"", "");
            let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug).join(user_id.to_string()).join(chapter_name).join(file_name);
            if file_path.exists() {
                let file_content = fs::read_to_string(file_path.clone()).expect("加载课程记录文件失败");
                let message: Value = serde_json::from_str(&file_content).expect("解析课程记录文件失败");
                let res = json!({
                    "results": message,
                    "sectionType": section_type,
                    "userInfo": user_info,
                });
                return BusinessResponse::ok(res).to_json_result()
            }
        },
        _ => {
            
        }

    }

    

    BusinessResponse::ok(true).to_json_result()

}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
struct SectionQueryParams {
    // index: i32,
    chapter_name: String,
    section_name: String,
    course_slug: String,
    team_i_d: i32,
}

#[get("/api/admin/section/getSectionRecord")]
pub async fn get_section_record(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Query<SectionQueryParams>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });    
    };

    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 获取节id
    let section = match Query::get_section(&txn, params.course_slug.clone(), params.chapter_name.clone(), params.section_name.clone(),).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取章节信息 {}", e.to_string()).to_string() })
        }
    };
    if let None = section {
        txn.rollback().await.unwrap();
        return Err(BusinessError::ProcessError { reason: format!("该章节不存在").to_string() })
    }
    let section = section.unwrap();
    let section_id = section.id;

    // 获取该班级所有学生id
    let team_student_ids = match Query::user_id_find_by_team_ids(&txn, params.team_i_d.clone()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("无法获取班级学生信息 {}", e.to_string()).to_string() })
        }
    };
    // println!("section_id: {:?}", section_id);

    // println!("team_student_ids: {:?}", team_student_ids);

    let records = match Query::get_users_records_all(&txn, team_student_ids.clone(), [section_id].to_vec()).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            println!("无法获取班级学生信息: {:?}", e);
            return Err(BusinessError::ProcessError { reason: format!("无法获取班级学生信息 {}", e.to_string()).to_string() })
        }
    };
    // println!("records: {:?}", records);
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };
    let res = json!({
        "records": records,
        "studentsNum": team_student_ids.len(),
    });
    BusinessResponse::ok(res).to_json_result()

}
