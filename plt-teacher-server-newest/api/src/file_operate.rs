use std::fmt::format;
use std::fs::File;
use std::io::Write;
use std::os::windows::ffi::OsStrExt;
use std::os::windows::process::CommandExt;
use std::time::Duration;
use std::thread;
use std::{collections::HashMap, fs, process::Command};
use std::env::set_var;
use actix_session::Session;
use chrono::Utc;
use log::{error, info};
use serde::{Deserialize, Serialize};
use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};
use fs_extra::dir::remove;
use serde_json::Value;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::process::Command as AsyncCommand;

// use winapi::um::winbase::{CREATE_NO_WINDOW};
// use winapi::um::handleapi::CloseHandle;
// use winapi::um::winnt::HANDLE;

use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};


use actix_web::{
    get, post, web, HttpRequest, HttpResponse, Result
};
use tokio::{io, task, time};
use winapi::um::wincon::GetConsoleWindow;
use super::context::{ BusinessError, BusinessResponse };
use winapi::um::winuser::{SW_HIDE, ShowWindow};
use winapi::um::winbase::CREATE_NO_WINDOW;


// IPython配置文件
#[derive(Deserialize,Debug)]
struct IpythonConfig {
    // 操作系统
    _os:String,
    // 操作系统架构
    _arch:String,
    // python版本控制
    _python:String,
    // 包管理
    _packages:Vec<String>,
    // 创建对应配置文件夹
    create_file:String,
    // 解压Python到对应文件
    un_zip_python:String,
    // 解压缓存文件到对应文件
    un_zip_cache:String,
    // 安装jupyter lab
    install_jupyter_lab:String,
    // 启动jupyter lab    
    set_jupyter_dir:String,
    run_jupyter_lab:String,
    // 检查Ipython环境
    check_ipython: String,
    ipython_version:String,
    // 关闭Ipython环境
    kill_ipython:String
}

use std::process::{Output, Stdio};
use std::path::{Path, PathBuf};

// 异步检查路径是否存在的辅助函数
async fn path_exists_async(path: &Path) -> bool {
    tokio::fs::metadata(path).await.is_ok()
}
// 在对应位置执行命令行
fn run_command(command: &str , dir:&str ) -> (bool,String) {
    
    //判断路径是否存在
    
    let path = Path::new(dir);
    if !(path.exists() && path.is_dir()) {
        return (false,format!("路径不存在：{}",dir));
    } 
    let error :String = format!("执行命令：{}失败",command);
    // 隐藏控制台窗口
    unsafe {
        let console_handle = GetConsoleWindow();
        ShowWindow(console_handle, SW_HIDE);
    }
    // 执行命令
    let output: Output = Command::new("cmd")
        .args(&["/C", command])
        .current_dir(dir)
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .expect(&error);
    let stdout = String::from_utf8_lossy(&output.stdout);
    (true,stdout.to_string())
}

// 初始化IPython环境
#[post("/api/web/course/block/ipython/init/{config_name}")]
pub async fn init_ipython(name: web::Path<String>) -> Result<HttpResponse, BusinessError>  {
    // 获取配置
    let config_name = name;
    let course_path = get_path_in_exe_dir("");
    // 将路径返回父级目录
    let course_path = course_path.parent().unwrap().join("ipython");
    let config_path =  course_path.join("config").join(config_name.to_string()).join("amd64-win-py38-jupyterlab.toml");

    let config_path = config_path.to_str().unwrap();
    //解析配置相关的toml文件
    let file_content = fs::read_to_string(config_path).expect("加载toml文件失败");
    let mut config:IpythonConfig = toml::from_str(&file_content).unwrap();
    // 创建对应操作系统的版本目录
    {
        let path =  course_path.join("tmp").join("ipython");
        // 验证该路径是否存在，不存在则创建
        if !path.exists() {
            fs::create_dir_all(&path).expect("创建目录失败");
        }
        let path = path.to_str().unwrap();
        let install_output = run_command(&config.create_file,&path);
        if !install_output.0 {
            return Err(BusinessError::InternalError { reason: install_output.1 });
        }
    }
    {  
        let path =  course_path.join("ipython");
        // 验证该路径是否存在，不存在则创建
        if !path.exists() {
            fs::create_dir_all(&path).expect("创建目录失败");
        }
        let path = path.to_str().unwrap();
        let install_output = run_command(&config.create_file,&path);
        if !install_output.0 {
            return Err(BusinessError::InternalError { reason: install_output.1 });
        }
    }       
    // 解压Python到对应版本目录
    {    
        let path =  course_path.join("tmp");
        // 验证该路径是否存在，不存在则创建
        if !path.exists() {
            fs::create_dir_all(&path).expect("创建目录失败");
        }
        let path = path.to_str().unwrap();
        let install_output = run_command(&config.un_zip_python,&path);
        if !install_output.0 {
            return Err(BusinessError::InternalError { reason: install_output.1 });
        }
    }
    // 解压对应版本的jupyter lab缓存文件
    {    
        // let cache = format!("cache_{}",config_name);
        let path =  course_path.join("tmp");
        // 验证该路径是否存在，不存在则创建
        if !path.exists() {
            fs::create_dir_all(&path).expect("创建目录失败");
        }
        let path = path.to_str().unwrap();
        let install_output = run_command(&config.un_zip_cache,&path);
        if !install_output.0 {
            return Err(BusinessError::InternalError { reason: install_output.1 });
        }
    }
    // 安装jupyter lab
    {    
        let path =  course_path.clone();
        let path = path.to_str().unwrap();
        let install_output = run_command(&config.install_jupyter_lab,&path);
        if !install_output.0 {
            return Err(BusinessError::InternalError { reason: install_output.1 });
        }

    }
    // 设置jupyter lab环境变量
    {
        let path =  course_path.clone().join("ipython").join(config_name.to_string()).join("python-3.8.20-embed-amd64");
        let path = path.to_str().unwrap();
        
        let dir_path = course_path.join("ipython").join("jupyter");
        if !dir_path.exists() {
            fs::create_dir_all(&dir_path).expect("创建目录失败");
        }
        let dir_path = dir_path.to_str().unwrap();
        let dir_config = config.set_jupyter_dir.clone();
        // 将python_path中的斜杠替换为反斜杠
        let dir_path = dir_path.replace("/", "\\"); 
    
        config.set_jupyter_dir = format!("{} {}",dir_config,dir_path);
        let set_output = run_command(&config.set_jupyter_dir,&path);
        if !set_output.0 {
            return Err(BusinessError::InternalError { reason: set_output.1 });
        }
    }
    // // 初始化pyo3的路径
    // {
    //     // 指定 32 位 Python 解释器的路径
    //     let python_path =  get_path_in_exe_dir("ipython").join(config_name.to_string()).join("python-3.8.10-embed-amd64").join("python.exe");
    //     // 设置 PYO3_PYTHON 环境变量
    //     env::set_var("PYO3_PYTHON", python_path);
    // }
    let mut response: HashMap<String, String> = HashMap::new();
    response.insert("configName".to_string(), "amd64-win-py38-jupyterlab".to_string());
    response.insert("status".to_string(), "ready".to_string());
    BusinessResponse::ok(response).to_json_result()
}


// 定义启动Ipython的单例结构体
use lazy_static::lazy_static;
use std::sync::Mutex;
static mut PID : Option<u32> = None;
// 单例创建jupyter lab
lazy_static! {
    static ref RUN_COMMAND_INSTANCE: Mutex<Option<String>> = Mutex::new(None);
}
async fn singleten_run_ipython(command:String, path:&str) -> (bool,Option<u32>) {
    // 获取锁
    let mut instance = RUN_COMMAND_INSTANCE.lock().unwrap();
    // 如果没有创建过该函数，创建该函数
    if instance.is_none() {
        *instance = Some(String::from(""));
        let path = Path::new(path);

        if !(path.exists() && path.is_dir()) {
            unsafe{
                return(false,PID);
            }
        }
        let jupyter_runtime_dir = path.join("jupyter-runtime");
        let mut child = AsyncCommand::new("cmd")
            .args(&["/C", &command])
            .env("JUPYTER_RUNTIME_DIR", &jupyter_runtime_dir)
            // .env("PYTHONSTARTUP", &startup_path)
            .current_dir(path)
            .stdout(Stdio::piped()) // 捕获标准输出
            .stderr(Stdio::piped()) // 捕获标准错误
            .creation_flags(CREATE_NO_WINDOW)
            .spawn()
            .expect("Failed to start command");
        // 获取子进程的 PID
        let pid = child.id();
        unsafe {
            PID = pid;
        }
        // 启动异步任务来读取标准输出和标准错误
        // let stdout = child.stdout.take().unwrap();
        let stderr = child.stderr.take().unwrap();

        // task::spawn(async move {
        // let mut stdout_reader = BufReader::new(stdout).lines();
        let mut stderr_reader = BufReader::new(stderr).lines();

        loop {
            tokio::select! {
                // Ok(Some(line)) = stdout_reader.next_line() => {
                //     println!("{}", line);
                //     info!("Jupyter Lab stdout: {}", line); // 将标准输出写入日志
                //     if line.contains("Skipped non-installed server") {
                //         let url = line.split(" ").last().unwrap();
                //         println!("JupyterLab URL: {}", url);
                //         break;
                //     }
                //
                // }
                Ok(Some(line)) = stderr_reader.next_line() => {
                    println!("stderr: {}", line);
                    error!("Jupyter Lab stderr: {}", line); // 将标准错误写入日志
                    // if line.contains("Skipped non-installed server") {
                    //     let url = line.split(" ").last().unwrap();
                    //     println!("JupyterLab URL: {}", url);
                    //     break;
                    // }
                    if line.contains("Use Control-C to stop this server") {
                        // let url = line.split(" ").last().unwrap();
                        // println!("JupyterLab URL: {}", url);
                        println!("break");
                        break;
                    }
                }
                else => break, // 如果读取结束，退出循环
            }
        }
        // });

        // 创建成功，返回 true 和 PID
        (true, pid)
    } else {
        // 如果创建过该函数，返回false和PID
        unsafe{
            return (false,PID);
        }
    }
}
// 结束单例
fn end_singleten_ipython() -> bool {
    let mut instance = RUN_COMMAND_INSTANCE.lock().unwrap();
    if let Some(_) = instance.take() {
        // 清除状态
        true
    } else {
        false
    }
}
// 启动IPython环境
#[post("/api/web/course/block/ipython/start/{config_name}/{course_slug}")]
pub async fn run_ipython(
    // session: Session,
    param : web::Path<(String, String)>
) -> Result<HttpResponse, BusinessError>  {

    // 获取配置
    let (_config_name, course_slug) = param.into_inner();
    let course_path = get_path_in_exe_dir("");
    // 将路径返回父级目录
    let course_path = course_path.parent().unwrap().join("ipython");

    // Command::new("setx")
    //     .args(&["JUPYTER_TOKEN", "123456"])
    //     .creation_flags(CREATE_NO_WINDOW)
    //     .status()
    //     .expect("Failed to set environment variable");
    set_var("JUPYTER_TOKEN", "123456");

    // 获取jupyter lab环境路径
    // 检查系统
    // let os_version = match get_system_version(){
    //     Ok(os_version) => os_version,
    //     Err(e) => return Err(BusinessError::ProcessError { reason: format!("不支持的操作系统版本{} ", e)}),
    // };

    // 检查jupyter lab
    // let mut env_path = course_path.join("python-3.9.22-amd64-env").join("Scripts");

    // if os_version == "win7"{
    //     env_path = course_path.join("python-3.8.20-amd64-env").join("Scripts");
    // }
    // let amd_path = course_path.join("python-3.8.20-embed-amd64").join("Scripts");
    let amd_path = course_path.join("python-3.8.20-embed-amd64");

    let path: &str = amd_path.to_str().unwrap();

    // let startup_path = get_path_in_exe_dir("ipython").join("tmp").join("startup");
    // if !startup_path.exists() {
    //     tokio::fs::create_dir_all(startup_path.clone()).await.unwrap();
    // }
    //
    // let startup_path = format!("{}\\__tmp_startup_{}.py", startup_path.to_str().unwrap(), course_slug);
    // if Path::new(&startup_path).exists() {
    //     // 尝试多次删除，处理文件被锁定的情况
    //     let mut attempts = 0;
    //     loop {
    //         match tokio::fs::remove_file(&startup_path).await {
    //             Ok(_) => break,
    //             Err(e) => {
    //                 attempts += 1;
    //                 if attempts >= 3 {
    //                     return Err(BusinessError::ProcessError {
    //                         reason: format!("无法删除startup文件，可能被占用: {}", e.to_string())
    //                     });
    //                 }
    //                 // 短暂等待后重试
    //                 tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    //             }
    //         }
    //     }
    // }

    // 等待z盘完全写入完成
    // thread::sleep(Duration::from_secs(5));
    // let ini_disk_path_str = format!("Z:{}", course_slug);
    // let ini_disk_path = Path::new(&ini_disk_path_str);
    // while !path_exists_async(&ini_disk_path).await {
    //     println!("等待Z盘写入完成...");
    //     tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    // }
    
    // let temp_file_path = ini_disk_path.join("temp_test_file.tmp");
    // loop {
    //     // 尝试创建一个临时文件
    //     if let Ok(_) = tokio::fs::write(&temp_file_path, b"test").await {
    //         // 尝试删除文件
    //         if let Ok(_) = tokio::fs::remove_file(&temp_file_path).await {
    //             println!("虚拟盘已成功挂载并可读写。");
    //             break;
    //         } else {
    //             // 如果无法删除，说明权限或文件系统有问题，继续等待
    //             println!("成功写入但无法删除，继续等待...");
    //         }
    //     }
    //     println!("虚拟盘路径已存在，但文件系统未就绪，继续等待...");
    //     tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    // }
    let target_path = get_path_in_exe_dir("student").join(course_slug.clone());

    // 确保目标目录在写入startup.py前已完全准备好
    if !target_path.exists() {
        return Err(BusinessError::ProcessError { 
            reason: format!("课程目录不存在，请先调用prepare_fs: {:?}", target_path) 
        });
    }

    // // 写入 startup.py
    // let startup_code = format!(
    //     "import os\nos.chdir(r\"{}\")\nprint(\"[startup.py] 当前目录:\", os.getcwd())",
    //     target_path.to_str().unwrap().to_string()
    // );
    let startup_code = format!(
        r#"
# 在服务器启动时切换目录
import os

# runtime_dir = os.path.expanduser("~/jupyter_runtime")
# os.makedirs(runtime_dir, exist_ok=True)
# c.NotebookApp.runtime_dir = runtime_dir

os.chdir(r"{}")
print("[startup.py] 当前目录:", os.getcwd())
"#,
        target_path.to_str().unwrap().to_string()
    );

    let jupyter_config_path = course_path.join("python-3.8.20-embed-amd64").join("jupyter_lab_config.py");
    let jupyter_config_path_str = jupyter_config_path.to_str().unwrap();

    let Ok(is_replace) = replace_between_markers(jupyter_config_path_str, "#===========================================", &startup_code).await else {
        return Err(BusinessError::ProcessError { reason: "匹配失败".to_string() });
    };
    if !is_replace {
        let mut file = tokio::fs::OpenOptions::new()
            .append(true)
            .read(true)
            .open(jupyter_config_path_str)
            .await.unwrap();
        let content = format!(r#"
#===========================================
{}
#===========================================
        "#, startup_code);
        file.write_all(content.as_bytes()).await.unwrap();
    }

//     let startup_code = format!(
//         r#"
// # 在服务器启动时切换目录
// import os
// os.chdir("{}")
// print("[startup.py] 当前目录:", os.getcwd())
// "#,
//         target_path.to_str().unwrap().to_string()
//     );

    // let jupyter_config_path = course_path.join("python-3.8.20-embed-amd64").join("jupyter_lab_config.py");
    // let jupyter_config_path_str = jupyter_config_path.to_str().unwrap();

    // let mut file = tokio::fs::OpenOptions::new()
    //     .append(true)
    //     .read(true)
    //     .open(jupyter_config_path_str)
    //     .await.unwrap();
    //
    // file.write(startup_code.as_bytes()).await.unwrap();
    
    // 确保 jupyter_lab_config.py 写入(追加)成功
    // match tokio::fs::write(&startup_path, startup_code).await {
    //     Ok(_) => {},
    //     Err(e) => {
    //         return Err(BusinessError::ProcessError {
    //             reason: format!("写入startup.py失败: {}", e.to_string())
    //         });
    //     }
    // }
    // let command = format!("{} && {}", "activate.bat", "jupyter lab --port=9999 --no-browser --config=jupyter_lab_config.py");
    let python_exe_path = course_path.join("python-3.8.20-embed-amd64").join("python.exe");
    let python_exe_path_str = python_exe_path.to_str().unwrap();
    let jupyter_fix_path = course_path.join("python-3.8.20-embed-amd64").join("jupyter_fix.py");
    let jupyter_fix_path_str = jupyter_fix_path.to_str().unwrap();
    // let command = format!("{} -m {}{}", python_exe_path_str, "jupyter lab --port=9999 --no-browser --config=", jupyter_config_path_str);
    let command = format!("{} {} {}{}", python_exe_path_str, jupyter_fix_path_str, " --port=9999 --no-browser --config=", jupyter_config_path_str);
    info!("command = {:?}", command);

    // let command = format!("{} && {}{}", "activate.bat", "jupyter lab --port=9999 --no-browser --config=jupyter_lab_config.py --notebook-dir=Z:/", course_slug);
    // 开启对应版本的jupyter lab
    let (is_success,pid) = singleten_run_ipython(command, path).await;
    if !is_success {
        //若pid不为空，说明已经启动，返回已启动
        if pid.is_some() {
            return Err(BusinessError::InternalError { reason: format!("已启动,PID为{}",pid.unwrap()) });

        }
        return Err(BusinessError::InternalError { reason: format!("启动失败") });
    }

    // 8s延时
    // tokio::time::sleep(tokio::time::Duration::from_secs(8)).await;
    
    BusinessResponse::ok(format!("启动成功，PID为{}",pid.unwrap())).to_json_result()
}


// 检查IPython环境
#[get("/api/web/course/block/ipython/check/{config_name}")]
pub async fn check_ipython(name: web::Path<String>) -> Result<HttpResponse, BusinessError>  {
    // 获取配置
    let _config_name = name;
    let course_path = get_path_in_exe_dir("");
    // 将路径返回父级目录
    let course_path = course_path.parent().unwrap().join("ipython");

    // 检查系统
    let os_version = match get_system_version(){
        Ok(os_version) => os_version,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("不支持的操作系统版本{} ", e)}),
    };
    
    // println!("系统版本:{}", os_version);
    // 检查jupyter lab
    // let mut env_path = course_path.join("python-3.9.22-amd64-env").join("Scripts");
    let amd_path = course_path.join("python-3.8.20-embed-amd64").join("Scripts");

    // if os_version == "win7"{
    //     env_path = course_path.join("python-3.8.20-amd64-env").join("Scripts");
    //     amd_path = course_path.join("python-3.8.20-embed-amd64");
    // }

    // 判断文件路径是否存在
    // if !env_path.exists() || !amd_path.exists() {
    //     return Err(BusinessError::ProcessError { reason: format!("未安装正确的IPython环境") });
    // }
    if !amd_path.exists() {
        return Err(BusinessError::ProcessError { reason: format!("未安装正确的IPython环境") });
    }
    // return Err(BusinessError::ProcessError { reason: format!("未安装正确的IPython环境,当前Jupyter Lab 版本为：{}，所需版本为{}") });
    let mut response: HashMap<String, String> = HashMap::new();
    response.insert("configName".to_string(), "amd64-win-py38-jupyterlab".to_string());
    response.insert("status".to_string(), "installed".to_string());
    return BusinessResponse::ok(response).to_json_result();
}
// 检查是否启动Ipython环境
#[get("/api/web/course/block/ipython/status/check")]
pub async fn check_ipython_status() -> Result<HttpResponse, BusinessError>  {
    unsafe{
        if PID == None{
            return BusinessResponse::ok(false).to_json_result()
        }
        BusinessResponse::ok(true).to_json_result()}
}
//关闭Ipython环境
#[post("/api/web/course/block/ipython/stop/{config_name}")]
pub async fn stop_ipython(name: web::Path<String>) -> Result<HttpResponse, BusinessError>  {
    // 获取配置
    let _config_name = name;
    // let course_path = get_path_in_exe_dir("");
    // 将路径返回父级目录
    // let course_path = course_path.parent().unwrap().join("ipython");
    // let config_path =  course_path.join("config").join(config_name.to_string()).join("amd64-win-py38-jupyterlab.toml");
    // let config_path = config_path.to_str().unwrap(); 
    // // 解析相关的toml文件
    // let file_content = fs::read_to_string(config_path).expect("加载toml文件失败");
    // let config:IpythonConfig = toml::from_str(&file_content).unwrap();
    unsafe {    
        if PID == None{
            return BusinessResponse::ok("未开启IPthon环境").to_json_result()
        }
        let pid_string = match PID {
            Some(pid) => pid.to_string(),
            None => String::from(""), // 或者选择其他默认值
        };
        let command = format!("taskkill /f /t /im {}", &pid_string);
        // let command = config.kill_ipython.clone().replace("$pid", &pid_string);
        // 关闭jupyter lab
        let path =   get_path_in_exe_dir("");
        let kill_output = run_command(&command,&path.to_str().unwrap());
        if !kill_output.0 {
            return Err(BusinessError::ProcessError { reason:kill_output.1 });
        }
        // 初始化PID为None
        PID = None;
        // 5s延时
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;  
        // 释放开启ipython函数及其相关进程和锁，以便之后可重新调用
        end_singleten_ipython();
        BusinessResponse::ok("关闭成功").to_json_result()
    } 
}
// 准备课程文件系统
#[post("/api/web/course/prepareFS/{courseSlug}/{userId}")]
pub async fn prepare_fs(
    session: Session,
    param : web::Path<(String, i32)>,
) -> Result<HttpResponse, BusinessError>  {

    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let _user_id = user_id.unwrap();

    let (course_slug, user_id) = param.into_inner();
    match init_virtual_disk(course_slug, user_id).await {
        Ok(_) => {},
        Err(e) => {return Err(BusinessError::ProcessError { reason: e.to_string() })}
    }

    // unsafe {
    //     let hwnd = FindWindowW(std::ptr::null(), std::ffi::OsStr::new("Z:\\assets").encode_wide().chain(Some(0)).collect::<Vec<u16>>().as_ptr());
    //     if hwnd.is_null() {
    //         eprintln!("Failed to find window handle");
            
    //     } else {
    //         SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
    //     }
    // }
    BusinessResponse::ok("课程文件系统准备成功").to_json_result()
}
// 打开虚拟盘
#[post("/api/web/course/openFS/{courseSlug}")]
pub async fn open_fs(
    param: web::Path<String>,
) -> Result<HttpResponse, BusinessError>  {

    let course_slug = param.into_inner();
    // // 查看虚拟盘是否已经打开
    // if !Path::new(format!("Z:\\{}", course_slug).as_str()).exists() {
    //     return Err(BusinessError::ProcessError { reason: "虚拟盘不存在".to_string() });
    // }
    //     // 打开虚拟盘
    // let path = format!("Z:\\{}", course_slug);
    let path = get_path_in_exe_dir("student").join(course_slug.clone());

    // 检查目录是否存在
    if !path.exists() {
        return Err(BusinessError::ProcessError { reason: format!("目录不存在: {:?}", path) });
    }

    Command::new("explorer")
        .arg(&path)
        .spawn()
        .expect("Failed to open file explorer");
    
    // 等待explorer窗口创建
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    
    // unsafe {
    //     let hwnd = FindWindowW(std::ptr::null(), std::ffi::OsStr::new(format!("Z:\\{}", course_slug).as_str()).encode_wide().chain(Some(0)).collect::<Vec<u16>>().as_ptr());
    //     if hwnd.is_null() {
    //         eprintln!("Failed to find window handle");
    //     } else {
    //         SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
    //     }
    // }
    BusinessResponse::ok("课程文件系统准备成功").to_json_result()
}
// 关闭虚拟盘
#[post("/api/web/course/closeVirtualDisk/{user_id}")]
pub async fn close_virtual_disk(
    // session: Session,
    user_id: web::Path<String>,
) -> Result<HttpResponse, BusinessError>  {

    // // 查看虚拟盘是否已经打开
    // if !Path::new(format!("Z:").as_str()).exists() {
    //     return Err( BusinessError::ProcessError { reason: "虚拟盘不存在".to_string() })
    // }
    // 删除缓存文件
    let source_dir = get_path_in_exe_dir("student");
    if source_dir.exists() {
        fs::remove_dir_all(source_dir).expect("删除缓存文件失败");
    }
    // if cfg!(target_os = "windows") {
    //     unmount_virtual_disk("Z:").await;
    // }
    BusinessResponse::ok("课程文件系统关闭成功").to_json_result()
}
// 初始化虚拟盘
pub async fn init_virtual_disk(course_slug: String, _user_id: i32) -> Result<(), String> {

    // // 空虚拟盘目录
    // let empty_virtual_disk_dir = get_path_in_exe_dir("tmp").join("virtual_disk");
    
    // 资源目录
    let source_path = get_path_in_exe_dir("course").join(course_slug.clone());
    // let source_dir = get_path_in_exe_dir("student").join(user_id.to_string());
    let target_path = get_path_in_exe_dir("student").join(course_slug.clone());
    let target_dir = ["assets", "output", "input"];
    
    // // 清理之前的虚拟盘
    // if cfg!(target_os = "windows") {
    //     unmount_virtual_disk("Z").await;
    // }

    // // 挂载虚拟盘
    // if cfg!(target_os = "windows") {
    //     mount_virtual_disk("Z", empty_virtual_disk_dir.clone().to_str().unwrap()).await;
    // }

    // // 将学生目录下的课程资源拷贝到虚拟盘
    // let virtual_course_path = Path::new("Z:").join(course_slug.clone());
    
    // // 确保虚拟盘中的课程目录存在
    // if !path_exists_async(&virtual_course_path).await {
    //     match tokio::fs::create_dir_all(&virtual_course_path).await {
    //         Ok(_) => (),
    //         Err(e) => {
    //             return Err(format!("在虚拟盘中创建课程目录失败 {}", e));
    //         }
    //     };
    // }
    
    // 确保target_path存在
    if !target_path.exists() {
        match tokio::fs::create_dir_all(&target_path).await {
            Ok(_) => (),
            Err(e) => {
                return Err(format!("创建目录失败 {}: {:?}", e, target_path));
            }
        };
    }
    
    // 将资源移入资源目录中
    for dir in target_dir.iter() {
        let target_path = target_path.join(dir);

        if !path_exists_async(&source_path.join(dir)).await { 
            continue;
        }

        if !path_exists_async(&target_path).await {    
            match tokio::fs::create_dir_all(target_path.clone()).await {
                Ok(_r) => (),
                Err(e) => {
                    return Err(format!("创建目录失败 {}", e));
                }
            };    
        }
        match copy_recursively(&source_path.join(dir), &target_path).await {
            Ok(_r) => (),
            Err(e) => {
                return Err(format!("拷贝目录失败 {} {:?} {:?}", e.to_string(), source_path, target_path).to_string());
            }
        };
    }

    // // 等待虚拟盘挂载完成
    // tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
    
    // // 资源目录中的资源拷贝至虚拟盘
    // let student_course_dir = get_path_in_exe_dir("student").join(user_id.to_string()).join(course_slug.clone());
    // if path_exists_async(&student_course_dir).await {

        
    //     // 将学生目录下的课程资源拷贝到虚拟盘
    //     let virtual_course_path = Path::new("Z:").join(course_slug.clone());
        
    //     // 确保虚拟盘中的课程目录存在
    //     if !path_exists_async(&virtual_course_path).await {
    //         match tokio::fs::create_dir_all(&virtual_course_path).await {
    //             Ok(_) => (),
    //             Err(e) => {
    //                 return Err(format!("在虚拟盘中创建课程目录失败 {}", e));
    //             }
    //         };
    //     }
        
    //     // 拷贝每个子目录到虚拟盘
    //     for dir in target_dir.iter() {
    //         let source_dir_path = student_course_dir.join(dir);
    //         let virtual_target_path = virtual_course_path.join(dir);
            
    //         if path_exists_async(&source_dir_path).await {
    //             match copy_recursively(&source_dir_path, &virtual_target_path).await {
    //                 Ok(_) => (),
    //                 Err(e) => {
    //                     return Err(format!("拷贝{}目录到虚拟盘失败 {}", dir, e.to_string()));
    //                 }
    //             };
    //         }
    //     }
    // }

    // tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
    Ok(())
}

// 学生端下载课程文件
#[get("/api/web/course/download/{courseSlug}")]
pub async fn get_download_course_student(
    session: Session,
    http_request: HttpRequest,
    course_slug: web::Path<String>
) -> Result<HttpResponse, BusinessError> {
    let start_time = Utc::now();

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => Some(0)
    };
    let user_id = user_id.unwrap_or(0);

    // 检查course目录是否存在
    let static_course_dir = get_path_in_exe_dir("course");
    let course_dir = static_course_dir.join(format!("{}", course_slug));
    // let course_zip = static_course_dir.join(format!("{}.zip",course_slug));



    let file_path = course_dir.clone();
    let select_train_dir = static_course_dir;


    // 获取文件类型
    let content_type = "application/zip";

    // 获取文件路径
    let file_path = select_train_dir.join(&file_path);

    // 打印文件路径
    write_log(format!("download_mp4_file_path: {:?}", &file_path));


    // 如果不存在，解压压缩包
    if !select_train_dir.exists() || !file_path.exists() {
        // 创建文件夹
        match std::fs::create_dir_all(&select_train_dir) {
            Ok(_r) => (),
            Err(e) => {
                panic!("递归创建目标目录失败 {}", e.to_string());
            }
        };
        
        // 将目标目录转换为字符串，以供后续使用
        let target_train_dir = match select_train_dir.to_str() {
            Some(path_str) => path_str,
            None => {
                return Err(BusinessError::InternalError {
                    reason: "目标目录路径无效".to_string(),
                });
            }
        };
        // 创建压缩文件的路径
        let train_zip_path = select_train_dir.with_extension("zip");

         if !train_zip_path.exists() {
            return Err(BusinessError::InternalError {
                reason: format!("训练文件不存在 {}", target_train_dir),
            });
        }

        let _result = match extract_zip(&train_zip_path, &select_train_dir) {
            Ok(r) => r,
            Err(e) => {
                return Err(BusinessError::InternalError { reason: format!("解压文件失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 检查文件是否存在
    if !file_path.exists() {
        return Err(BusinessError::InternalError { reason: format!("文件不存在 {}", file_path.to_str().unwrap()).to_string() });
    }

    // 获取文件大小
    let file_size = match fs::metadata(&file_path) {
        Ok(r) => r.len(),
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("获取文件大小失败 {}", e.to_string()).to_string() });
        }
    };

    // 获取文件
    let file = match fs::read(&file_path) {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("获取文件失败 {}", e.to_string()).to_string() });
        }
    };

    // 返回文件
    let mut response = HttpResponse::Ok();
    response.content_type(content_type);
    // response.append_header(("Content-Disposition", format!("attachment; filename={}", file_name)));
    response.append_header(("Content-Length", file_size.to_string()));

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/train/download/mp4", user_id, start_time);

    Ok(response.body(file))
}
// 站点下载课程到教师端

// src/api.rs

// use actix::prelude::*;

// #[derive(Message, Debug, Serialize, Deserialize)]
// #[rtype(result = "Response")]
// pub struct Request {
//     pub data: String,
// }

// #[derive(Debug, Serialize, Deserialize)]
// pub struct Response {
//     pub result: String,
// }

// pub struct ApiActor;

// impl Actor for ApiActor {
//     type Context = Context<Self>;
// }

// impl Handler<Request> for ApiActor {
//     type Result = Response;

//     fn handle(&mut self, msg: Request, _ctx: &mut Self::Context) -> Self::Result {
//         let result = format!("Processed: {}", msg.data);
//         Response { result }
//     }
// }

// #[derive(Deserialize, Serialize)]
// pub struct remoteCourseDownload{
//     url: String,
//     course_slug: String
// }
// // #[post("/api/admin/course/downloadCourseZIP")]
// pub async fn post_course_download_course_zip(
//     request: web::Json<remoteCourseDownload>,
// ) -> Result<HttpResponse, BusinessError> {
//     println!("3");
//     let request = request.into_inner();

//     let course_slug = request.course_slug.clone();
//     // 获取HTTP客户端
//     let client = match get_client() {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法生成HTTP客户端 {}", e.to_string()).to_string() })
//     };
    
//     // 下载课程文件
    
//     let target_file_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));

//     match http_get_file(&client, &request.url, &target_file_path, DOWNLOAD_TIME_OUT).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("下载课程文件失败 {}", e.to_string()).to_string() })
//     }
    
//     // 解压课程文件
//     let course_file_path = get_path_in_exe_dir("course").join(course_slug.clone());
//     println!("{:?}",target_file_path);
//     println!("{:?}",course_file_path);
//     match extract_zip(&target_file_path, &course_file_path) {
//         Ok(r) => r,
//         Err(e) => {
//             return Err(BusinessError::InternalError { reason: format!("解压课程文件失败 {}", e.to_string()).to_string() });
//         }
//     }

// BusinessResponse::ok(format!("课程{}下载完成",course_slug)).to_json_result()

// }

pub async fn post_course_download_course_zip(
    request: String,
    
) -> bool {
    let request = serde_json::from_str::<HashMap<String, Value>>(&request).unwrap();
    // let url = request.get("url");
    let mut url: String = "".to_string();
    let mut course_slug: String = "".to_string();
    if let Some(Value::String(res)) = request.get("url") {
        url = res.to_string();
    }
    if let Some(Value::String(res)) = request.get("courseSlug") {
        course_slug = res.to_string();
    }
    // 获取HTTP客户端
    let client = match get_client() {
        Ok(r) => r,
        Err(_) => {
            return false
        }
    };
    
    // 下载课程文件
    let target_file_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));

    // let get_file = http_get_file(&client, &url, &target_file_path, DOWNLOAD_TIME_OUT);
    match http_get_file(&client, &url, &target_file_path, DOWNLOAD_TIME_OUT).await {
        Ok(r) => r,
        Err(_) => {
            // let websocket_server = &app_state.ws_server;
            // let msg: ClientMessage = ClientMessage { id: 0, room: String::from("download"), msg: r#"{"type":"courseDownload/fail"}"#.into() };
            // let _resp = websocket_server.send(msg).await;
            return false
        }
    }
    // 等待下载完成
    
    // 解压课程文件
    let course_file_path = get_path_in_exe_dir("course").join(course_slug.clone());
    // let zip = extract_zip(&target_file_path, &course_file_path);
    match extract_zip(&target_file_path, &course_file_path) {
        Ok(r) => r,
        Err(_) => {
            // let websocket_server = &app_state.ws_server;
            // let msg: ClientMessage = ClientMessage { id: 0, room: String::from("download"), msg: r#"{"type":"courseDownload/fail"}"#.into() };
            // let _resp = websocket_server.send(msg).await;
            return false;
        }
    }

    
    
    // let websocket_server = &app_state.ws_server;
    // let msg: ClientMessage = ClientMessage { id: 0, room: String::from("download"), msg: r#"{"type":"courseDownload/complete"}"#.into() };
    // let _resp = websocket_server.send(msg).await;
    true

}
// #[derive(Deserialize, Serialize)]
// pub struct remoteCourseDownload{
//     url: String,
//     course_slug: String
// }
// // #[post("/api/admin/course/downloadCourseZIP")]
// pub async fn post_course_download_course_zip(
//     request: web::Json<remoteCourseDownload>,
// ) -> Result<HttpResponse, BusinessError> {
//     println!("3");
//     let request = request.into_inner();

//     let course_slug = request.course_slug.clone();
//     // 获取HTTP客户端
//     let client = match get_client() {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法生成HTTP客户端 {}", e.to_string()).to_string() })
//     };
    
//     // 下载课程文件
    
//     let target_file_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));

//     match http_get_file(&client, &request.url, &target_file_path, DOWNLOAD_TIME_OUT).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("下载课程文件失败 {}", e.to_string()).to_string() })
//     }
    
//     // 解压课程文件
//     let course_file_path = get_path_in_exe_dir("course").join(course_slug.clone());
//     println!("{:?}",target_file_path);
//     println!("{:?}",course_file_path);
//     match extract_zip(&target_file_path, &course_file_path) {
//         Ok(r) => r,
//         Err(e) => {
//             return Err(BusinessError::InternalError { reason: format!("解压课程文件失败 {}", e.to_string()).to_string() });
//         }
//     }

// BusinessResponse::ok(format!("课程{}下载完成",course_slug)).to_json_result()

// }

// 获取文件内容
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct GetFileContentByPath {
    path: String,
}
#[post("api/course/file/content")]
pub async fn get_file_content_by_path(
    // path: web::Path<String>,
    param: web::Json<GetFileContentByPath>
) -> Result<HttpResponse, BusinessError> {
    // 将string转换为Pathbuf
    let path = PathBuf::from(param.path.clone());
    // 获取文件内容
    if path.exists() {
        let content = match fs::read_to_string(path) {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("无法读取文件 {}", e.to_string()).to_string() })
        };
        return BusinessResponse::ok(content).to_json_result();
    }
    return Err(BusinessError::InternalError { reason: "未找到文件".to_string() });
    
}
#[post("api/admin/course/broadcast/{broadcast_series}")]
pub async fn post_course_broadcast(
    broadcast_series: web::Path<String>,
) -> Result<HttpResponse, BusinessError> {
    match  broadcast_series.as_str(){
        "serverIP" => {
            // 
        }
        _ => {
            return Err(BusinessError::InternalError { reason: "暂未实现".to_string() });
        }
    }
    BusinessResponse::ok(1).to_json_result()
    
}



use regex::Regex;

/// 替换文件中两个特定标记之间的内容
///
/// # 参数
/// * `file_path` - 要处理的文件路径
/// * `marker` - 标记字符串（如 "#==========================================")
/// * `new_content` - 要插入的新内容
///
/// # 返回
/// * `Ok(true)` - 替换成功
/// * `Ok(false)` - 标记未找到
/// * `Err` - I/O错误
pub async fn replace_between_markers(
    file_path: &str,
    marker: &str,
    new_content: &str,
) -> io::Result<bool> {
    // 1. 读取整个文件到字符串
    let content = tokio::fs::read_to_string(file_path).await?;

    // 2. 转义标记字符串中的正则特殊字符
    let escaped_marker = regex::escape(marker);

    // 3. 构建正则表达式：匹配两个标记之间的内容（包括标记本身）
    // (?s) - 启用 . 匹配换行符
    // (?m) - 启用多行模式，^ 和 $ 匹配行首行尾
    let pattern = format!(
        r"(?sm)^{}\s*\n.*?\n^{}",
        escaped_marker,
        escaped_marker
    );

    let re = Regex::new(&pattern).map_err(|e| {
        io::Error::new(io::ErrorKind::InvalidData, format!("正则错误: {}", e))
    })?;

    // 4. 检查是否存在匹配
    if !re.is_match(&content) {
        println!("未找到标记: {}", marker);
        return Ok(false);
    }

    // 5. 构建替换内容（包含标记）
    let replacement = format!("{}\n{}\n{}", marker, new_content.trim(), marker);

    // 6. 执行替换（只替换第一个匹配块）
    let new_content_str = re.replace(&content, replacement);

    // 7. 写回文件
    tokio::fs::write(file_path, new_content_str.as_ref()).await?;

    println!("成功替换标记之间的内容");
    Ok(true)
}