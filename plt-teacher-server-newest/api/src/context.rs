use std::{collections::HashMap, sync::{Arc, Mutex}, time::SystemTime};

use actix_web::{ HttpResponse, error };
use localsend::server::UDPService;
use sea_orm::DatabaseConnection;
use websocket::server::WSServer;
use actix::prelude::Addr;
use serde::{Deserialize, Serialize};
use failure::Fail;

// HTTP下载文件缓存结构体，用于缓存下载的文件内容
#[derive(Debug, Clone)]
pub struct HttpFileCache {
    // MIME类型
    pub mime: String,

    // 文件内容
    pub content: Vec<u8>,

    // 文件修改时间
    pub modified: SystemTime,
}

// 每个请求处理的上下文，包含数据库连接和WebSocket服务器
#[derive(Debug, Clone)]
pub struct AppState {
    // WebSocket服务器
    pub ws_server: Addr<WSServer>,
    
    // UDP公告服务器
    pub udp_server: UDPService,
    // UDP文件传输服务器
    pub udp_file_server: UDPService,
    // 存储下载进度
    pub download_progress: Arc<Mutex<HashMap<String, ProgressData>>>,
    
    // 数据库连接
    pub conn: DatabaseConnection,
}
#[derive(Debug, Clone)]
pub struct ProgressData {
    // 进度
    pub progress: f64,
    pub course_slug: String,
    pub task_type: String,
}

// 业务错误定义，用于显示业务错误和内部错误
#[derive(Fail, Debug)]
pub enum BusinessError {
    #[fail(display = "字段出现了校验错误: {}", field)]
    ValidationError { field: String },
    #[fail(display = "出现了内部错误 {}", reason)]
    InternalError { reason: String },
    #[fail(display = "出现了流程错误 {}", reason)]
    ProcessError { reason: String },
    #[fail(display = "出现了账号错误 {}", reason)]
    AccountError { reason: String },
}

impl error::ResponseError for BusinessError {
    fn error_response(&self) -> HttpResponse {
        match *self {
            BusinessError::AccountError { .. } => {
                let resp = BusinessResponse::err(401, &self.to_string());
                HttpResponse::BadRequest().json(resp)
            }
            BusinessError::ProcessError { .. } => {
                let resp = BusinessResponse::err(403, &self.to_string());
                HttpResponse::BadRequest().json(resp)
            }
            BusinessError::ValidationError { .. } => {
                let resp = BusinessResponse::err(400, &self.to_string());
                HttpResponse::BadRequest().json(resp)
            }
            _ => {
                let resp = BusinessResponse::err(402, &self.to_string());
                HttpResponse::InternalServerError().json(resp)
            }
        }
    }
}

#[derive(Deserialize, Serialize)]
pub struct BusinessResponse<T> where T: Serialize {
    code: i32,
    message: String,
    data: Option<T>,
}

impl<T: Serialize > BusinessResponse<T> {
    pub fn ok(data: T) -> Self {
        BusinessResponse { code: 0, message: "ok".to_owned(), data: Some(data) }
    }

    pub fn to_json_result(&self) -> Result<HttpResponse, BusinessError> {
        Ok(HttpResponse::Ok().json(self))
    }
}


impl BusinessResponse<()> {
    pub fn err(error: i32, message: &str) -> Self {
        BusinessResponse { code: error, message: message.to_owned(), data: None }
    }
}
