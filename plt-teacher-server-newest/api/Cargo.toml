[package]
name = "api"
version = "0.1.0"
authors = ["张未波(<EMAIL>)"]
edition = "2021"
publish = false

[dependencies]
actix = "0.13.0"
actix-files = "0.6"
actix-http = "3"
actix-rt = "2.7"
actix-service = "2"
actix-web = "4"
actix-web-actors = "4.1.0"
actix-session = { version="0.10.1", features = ["cookie-session"] }
actix-cors = "0.7.0"
chrono = "0.4.6"

Boa = "0.13.1"
# rquickjs = "0.6.2"
# deno_core = "0.319.0"

# pyo3 = { version = "0.22.6", features = ["extension-module"] }
# pyo3-build-config = "0.22.6"
# pyo3 = { version = "0.22.6", features = ["abi3"] }

tera = "1.15.0"
listenfd = "1.0.1"
serde = "1"
serde_json = "1.0.83"
json_value_merge  = "2.0.0"
fs_extra = "1.2.0"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
failure = "0.1.5"
rand = "0.8.5"
ring = "0.17.8"
hex = "0.4.3"
log = "0.4.14"
log4rs = "1.0.0"
tokio = { version = "1.0", features = ["full"] }
actix-multipart = "0.7.2"
futures = "0.3.15"
mime_guess = "2.0.0"
toml = "0.5.11"
lazy_static = "1.4.0"
encoding_rs = "0.8"
service = { path = "../service" }
entity = { path = "../entity" }
migration = { path = "../migration" }
websocket = { path = "../websocket"}
reqwest = { version = "0.12.12", features = ["blocking", "json", "stream", "cookies"] }

localsend = { path = "../localsend"}
indicatif = "0.17"
tracing = "0.1"
console = "0.15"
dialoguer = "0.11"
once_cell = "1.8"
chinese-number = "0.7.7"
winapi = { version = "0.3", features = ["winuser", "errhandlingapi"] }

quick-xml = "0.23"
xml = "0.8.20"
sha2 = "0.10.8"
urlencoding = "2.1.3"
regex = "1"
futures-util = "0.3.31"
tokio-tungstenite = { version = "0.28", features = ["native-tls"] }
url = "2.5.7"
uuid = "1.18.1"
zip = "2.4.2"
walkdir = "2.5.0"
[dependencies.sea-orm]
version = "=1.0.1" # sea-orm version
features = [
    "debug-print",
    "runtime-async-std-native-tls",
    #"sqlx-mysql",
    # "sqlx-postgres",
    "sqlx-sqlite",
]
