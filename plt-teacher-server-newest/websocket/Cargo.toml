[package]
name = "websocket"
version = "1.0.0"
edition = "2021"
publish = false

[lib]
name = "websocket"
path = "src/lib.rs"


[dependencies]
actix = "0.13.0"
actix-web = "4"
actix-web-actors = "4.1.0"
service = { path = "../service" }
env_logger = "0.10"
log = "0.4"
rand = "0.8"
serde = "1"
serde_json = "1"
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1", features = ["full"] }  # reqwest 0.11 需要 tokio 作为异步运行时