use std::{
    collections::HashMap, fmt, fs::{self, File}, io::Read, net::{IpAddr, Ipv4Addr}, sync::{atomic::{AtomicBool, Ordering}, Arc, RwLock}, time::{Duration, SystemTime}
};

use log::info;
use network_interface::Addr;
use ring::digest;
use service::{get_path_in_exe_dir, sea_orm::{self, sqlx::types::chrono::Local, Database, TransactionTrait}, Mutation, Query};
use tokio::{ net::UdpSocket, sync::{mpsc, Mutex}};
use tracing::debug;
use uuid::Uuid;
use websocket::server::{ClientMessage, WSServer};
// use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};

use crate::{
    protos::{  FileTransmitPacket, Task, TaskState, UDPMessageInfo, UDPMessageType, UDPResponse}, utils::get_device_ip_addr, CHUNK_SIZE, FILE_MESSAGE_BUFFER_SIZE, HASH_SIZE, MAX_PACKET_SIZE, MAX_RETRANSMIT, NUM_REPEAT
};

use crate::{BUFFER_SIZE, DEVICE_MODEL, DEVICE_TYPE};

// UDPService 结构体用于扫描和管理设备信息
pub struct UDPService {
    pub socket: Arc<UdpSocket>, // 用于发送和接收UDP数据包的套接字
    pub this_udp: UDPResponse, // 当前设备的信息
    pub devices: Vec<UDPMessageInfo>, // 已发现的设备信息列表
    pub interface_addr: Ipv4Addr, // 网络接口的IP地址
    pub multicast_addr: Ipv4Addr, // 多播组的IP地址
    pub multicast_port: u16, // 多播组的端口号
    pub sender:mpsc::UnboundedSender<FileTransmitPacket>,
    pub receiver:Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>,
    pub tasks: Arc<Mutex<Vec<Task>>>,
    pub file_is_sending: Arc<AtomicBool>,
}

impl UDPService {
    // 创建一个新的 UDPService 实例
    // 参数:
    // - device_alias: 设备的别名
    // - interface_addr: 网络接口的IP地址
    // - multicast_addr: 多播组的IP地址
    // - multicast_port: 多播组的端口号
    pub async fn new(
        device_alias: String,
        interface_addr: Ipv4Addr,
        multicast_addr: Ipv4Addr,
        multicast_port: u16,
        sender:mpsc::UnboundedSender<FileTransmitPacket>,
        receiver:Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>,
        
    ) -> Self {
        // 创建并绑定UDP套接字到指定的网络接口和端口
        let socket = Arc::new(
            UdpSocket::bind((interface_addr, multicast_port))
                .await
                .expect("couldn't bind to address"),
        ); 

        // 生成设备的唯一标识符
        let fingerprint = Uuid::new_v4();

        // 获取设备的IPv4地址，如果获取失败则使用默认的IP地址
        let ip_addr = get_device_ip_addr().unwrap_or(IpAddr::V4([0, 0, 0, 0].into()));

        // 构建设备信息
        let udp_message_info: UDPMessageInfo = UDPMessageInfo {
            alias: device_alias,
            device_type: DEVICE_TYPE.to_string(),
            device_model: Some(DEVICE_MODEL.to_string()),
            ip: ip_addr.to_string(),
            port: multicast_port,
            last_refresh_time: SystemTime::now(),
        };

        // 构建当前设备的信息
        let this_udp = UDPResponse {
            udp_message_info,
            fingerprint: fingerprint.to_string(),
            file_size: 0,
            ..Default::default()
        };

        // 返回 DeviceScanner 实例
        Self {
            socket,
            this_udp,
            devices: vec![],
            interface_addr,
            multicast_addr,
            multicast_port,
            sender,
            receiver, 
            tasks: Arc::new(Mutex::new(vec![])),
            file_is_sending: Arc::new(AtomicBool::new(false)),
        }
    }

    // 发送设备公告消息
    // 参数:
    // - send_socket: 用于发送消息的UDP套接字
    // - announcement_msg: 公告消息
    // - addr: 目标地址 (IP地址和端口号)
    pub async fn announce(
        send_socket: &Arc<UdpSocket>,
        announcement_msg: &str,
        addr: (Ipv4Addr, u16),
    ) {
        // 发送公告消息到指定的地址
        if let Err(e) = send_socket
        .send_to(announcement_msg.as_bytes(), addr)
        .await
        {
            println!("发送udp公告失败: {}", e);
            log::error!("发送udp公告失败: {}", e);
        }
    }
    pub async fn announce_for_bytes(
        send_socket: &Arc<UdpSocket>,
        announcement_msg: &[u8],
        addr: (Ipv4Addr, u16),
    ) {

        // 发送公告消息到指定的地址
        if let Err(e) = send_socket
        .send_to(announcement_msg, addr)
        .await
        {
            println!("发送udp公告失败: {}", e);
            log::error!("发送udp公告失败: {}", e);
        }
        
    }

    // 重复发送设备公告消息
    // 参数:
    // - send_socket: 用于发送消息的UDP套接字
    // - announcement_msg: 公告消息
    // - addr: 目标地址 (IP地址和端口号)
    pub async fn announce_repeat(
        send_socket: Arc<UdpSocket>,
        announcement_msg: String,
        addr: (Ipv4Addr, u16),
    ) {
        // 无限循环，每隔5秒重复发送公告消息
        loop {
            
            for _ in 0..NUM_REPEAT {
                Self::announce(&send_socket, announcement_msg.as_str(), addr).await;
            }
            tokio::time::sleep(Duration::from_secs(5)).await;
        }
    }

    // 监听多播消息并发送设备公告
    pub async fn listen_and_announce_multicast(
        &mut self, 
        receiver: Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>,
        mut udp_file_server: UDPService,
        ws_server: actix::Addr<WSServer>,
        conn: sea_orm::DatabaseConnection,

    ) {
        // 加入多播组
        self.socket
            .join_multicast_v4(self.multicast_addr, self.interface_addr)
            .expect("failed to join multicast");

        // 设置当前设备的公告标志为 true
        self.this_udp.message_type = UDPMessageType::Announcement;

        // 克隆套接字用于发送公告消息
        let send_socket = self.socket.clone();

        // 将当前设备的信息序列化为JSON字符串
        let announce_msg = serde_json::to_string(&self.this_udp).unwrap();

        // 启动一个异步任务，重复发送公告消息
        tokio::spawn(Self::announce_repeat(
            send_socket,
            announce_msg,
            (self.multicast_addr, self.multicast_port),
        ));

        // 设置当前设备的公告标志为 false
        self.this_udp.message_type = UDPMessageType::Stop;

        // 将当前设备的信息序列化为JSON字符串，用于回复公告
        let reply_announce_msg = serde_json::to_string(&self.this_udp).unwrap();

        // 创建一个缓冲区用于接收数据
        let mut buf = [0u8; BUFFER_SIZE as usize];

        // 无限循环，监听多播消息
        loop { 
            // 更新自己的时间
            self.this_udp.udp_message_info.last_refresh_time = SystemTime::now();
            // 接收数据并解析为 DeviceResponse 结构体
            // println!("正在监听多播消息...");
            if let Ok((amt, src)) = self.socket.recv_from(&mut buf).await {
                // println!("接收到多播消息: ");
                let mut udp_response: UDPResponse =
                    serde_json::from_slice(&buf[..amt]).unwrap();// TODO

                // 更新设备的IP地址和端口号
                (
                    udp_response.udp_message_info.ip,
                    udp_response.udp_message_info.port,
                    udp_response.udp_message_info.last_refresh_time,
                ) = (src.ip().to_string(), src.port(), SystemTime::now());
                
                // // 如果接收到的消息是当前设备发送的，则跳过处理
                // if udp_response.udp_message_info.ip == self.this_udp.udp_message_info.ip {
                //     continue;
                // }

                // 如果接收到的消息是公告消息，则发送回复公告
                if udp_response.message_type == UDPMessageType::Announcement {
                    Self::announce(
                        &self.socket,
                        reply_announce_msg.as_str(),
                        (self.multicast_addr, self.multicast_port),
                    )
                    .await;
                }
                // 处理文件传输任务
                // let (course_slug , file_id ) = Self::select_task(self).await; 
                if let (Some(course_slug) , Some(file_id)) = udp_file_server.select_task().await {
                    
                    // 发送消息告知要传输的任务文件
                    self.this_udp.message_type = UDPMessageType::Public;
                    self.this_udp.message = Some(course_slug.clone());
                    self.this_udp.file_is_end = false;


                    // 获取文件大小
                    let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug.clone()));
                    let file_size = std::fs::metadata(dir_path.clone()).unwrap().len();
                    self.this_udp.file_size = file_size;

                    // 获取文件hash作为文件id传入
                    let file_content = fs::read(&dir_path).unwrap();
                    // 读取文件id（sha256哈希值）
                    let hash = digest::digest(&digest::SHA256, &file_content);
                    let hash_bytes: &[u8] = hash.as_ref();
                    self.this_udp.file_id = hash_bytes.to_vec();
                    // println!("文件hash: {:?}", hash_bytes);

                    let announce_msg = serde_json::to_string(&self.this_udp).unwrap();
                    
                    Self::announce(
                        &self.socket,
                        announce_msg.as_str(),
                        (self.multicast_addr, self.multicast_port),
                    )
                    .await;

                    // 开始传输文件
                    let mut udp_file_server_clone = udp_file_server.clone();
                    let mut udp_server_clone = self.clone();

                    tokio::spawn(async move {
                        println!("开始传输文件");
                        // 使用 compare_exchange 原子性地检查并设置标志位
                        match udp_file_server_clone.file_is_sending.compare_exchange(
                            false,    // 期望当前值是 false（未在发送）
                            true,     // 如果是 false，则设置为 true（标记为正在发送）
                            Ordering::SeqCst,  // 成功时的内存顺序
                            Ordering::SeqCst   // 失败时的内存顺序
                        ) {
                            Ok(_) => {
                                // 成功获取到发送权
                                println!("开始传输文件: {}", course_slug);
                                let mut send_result;
                                let mut task_complete = false;
                                let mut task_reset = false;
                                let mut part_ids = vec![];
                                loop {
                                    
                                    // let mut server = udp_file_server_clone.clone();
                                    {
                                        let mut tasks_guard = udp_file_server_clone.tasks.lock().await;

                                        for task in &mut *tasks_guard {
                                            if task.course_slug == course_slug{
                                                part_ids = task.part_ids.clone();
                                                println!("Resetting task {:?}", task.part_ids);
                                                println!("Resetting {:?}", task.reset);
                                                task.part_ids = vec![];
                                                task_reset = task.reset.clone();
                                            }
                                        } 
                                    }
                                    println!("开始发送文件: {:?} {}", part_ids, task_reset);
                                    // 执行文件发送任务
                                    send_result = udp_file_server_clone.send_task_file(course_slug.clone(), file_id.clone(), task_reset, part_ids.clone()).await;
                                    
                                    let mut tasks_guard = udp_file_server_clone.tasks.lock().await;
                                    let mut found_task = false;

                                    for task in &mut *tasks_guard {
                                        if task.course_slug == course_slug{
                                            // println!("重传任务: {:?}", task.download_count);
                                            // println!("重传任务: {:?}", task.part_ids);
                                            // 此处很诡异，记得调查
                                            found_task = true;
                                            if task.download_count > 0 || task.part_ids.len() > 0{
                                                task.download_count = 0;
                                                task.reset = true;
                                                // part_ids = task.part_ids.clone();
                                                // println!("Resetting task {:?}", task.part_ids);
                                                // task.part_ids = vec![];
                                            } else {
                                                task_complete = true;
                                                task.download_count = 0;
                                                task.part_ids = vec![];
                                            }
                                            break;
                                        }
                                    }
                                    // 查询数据
                                    let db_path = get_path_in_exe_dir("hxr.db");
                                    let db_url = format!("sqlite://{}?mode=rwc", db_path.to_str().unwrap());
                                    let conn: sea_orm::prelude::DatabaseConnection = Database::connect(&db_url).await.unwrap();

                                    // 开启事务
                                    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
                                        Ok(txn) => txn,
                                        Err(e) => {
                                            eprintln!("开启事务失败: {}", e);
                                            continue;
                                        }
                                    };
                                                                
                                    let current_course_slug = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
                                        Ok(r) => r,
                                        Err(e) => {
                                            eprintln!("查找失败: {}", e);
                                            continue;
                                        }
                                    };

                                    if current_course_slug != Some(course_slug.clone()) {
                                        tasks_guard.retain(|task| task.course_slug != course_slug);
                                        drop(tasks_guard);  // 显式释放锁（非必需，但更清晰）
                                        break;
                                    }
                                    if !found_task || task_complete{ break;}

                                }

                                
                                // 清除任务（无论发送成功与否）
                                // let mut tasks_guard = udp_file_server_clone.tasks.lock().await;
                                // tasks_guard.retain(|task| task.course_slug != course_slug);
                                // drop(tasks_guard);  // 显式释放锁（非必需，但更清晰）
                                
                                // 重置发送标志
                                udp_file_server_clone.file_is_sending.store(false, Ordering::SeqCst);
                                
                                if send_result {
                                    println!("任务 {} 发送完成", course_slug);
                                    // 再次发送开始上课消息确保后加入的学生可以收到课程文件
                                    let mut current_self = udp_server_clone;
                                    println!("再次发送开始上课消息");
        
                                    current_self.this_udp.message_type = UDPMessageType::StartClass;
                                    current_self.this_udp.message = Some(course_slug.clone());

                                    // 检查对应目录是否存在
                                    let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug.clone()));
                                    // 将文件作为二进制流读出
                                    let file_content = fs::read(&dir_path).unwrap();
                                    // 读取文件id（sha256哈希值）
                                    let hash = digest::digest(&digest::SHA256, &file_content);
                                    let hash_bytes: &[u8] = hash.as_ref();
                                    current_self.this_udp.file_id = hash_bytes.to_vec();

                                    let announce_msg = serde_json::to_string(&current_self.this_udp).unwrap();
                                    Self::announce( 
                                        &current_self.socket,
                                        announce_msg.as_str(),
                                        (current_self.multicast_addr, current_self.multicast_port),
                                    )
                                    .await;    
                                    
                                    current_self.this_udp.message_type = UDPMessageType::Stop;
                                } else {
                                    println!("任务 {} 发送失败", course_slug);
                                }
                            },
                            Err(current) => {
                                // 已经有文件在发送中
                                println!("文件 {} 正在传输中，无法传输新任务（当前状态: {}）", course_slug, current);
                            }
                        }
                        
                    });

                }
                // 延迟
                // tokio::time::sleep(Duration::from_secs(3)).await;



                // 如果设备信息不在已发现的设备列表中，则添加到列表中
                if !self.devices.contains(&udp_response.udp_message_info) && udp_response.udp_message_info.ip != self.this_udp.udp_message_info.ip {
                    let clone_device = udp_response.udp_message_info.clone();
                    self.devices.push(udp_response.udp_message_info);
                    println!("New device found: {:?}", clone_device);
                } else {
                    self.devices
                        .iter_mut()
                        .find(|device| device.ip == udp_response.udp_message_info.ip)
                        .map(|device| device.last_refresh_time = SystemTime::now());
                }
                
                // 遍历已发现的设备列表，长时间没有消息通知离线
                let mut devices_to_remove = Vec::new();
                for device in self.devices.iter() {
                    // 获取 elapsed 时间
                    let elapsed_time = match device.last_refresh_time.elapsed() {
                        Ok(elapsed) => elapsed,
                        Err(e) => {
                            println!("Error calculating elapsed time: {}", e);
                            continue
                        }, 
                    };

                    if elapsed_time > Duration::from_secs(45) {
                        // 记录需要移除的设备
                        devices_to_remove.push(device.clone());
                        println!("Device offline: {:?}", device);
                    }
                
                }
                
                // 移除超时的设备
                self.devices.retain(|d| !devices_to_remove.contains(d));
                // 获取所有在线设备的ip
                let devices_ips: Vec<String> = self.devices.iter().map(|d| d.ip.clone()).collect();
                // 设置所有超时设备为离线状态
                // 开启事务
                let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
                    Ok(r) => r,
                    Err(_) => continue
                };
                let ips_data = match Query::get_online_ips(&txn).await {
                    Ok(r) => r,
                    Err(_) => continue
                };

                // 获取所有ips_data中不存在于devices_ips的ip
                let ips_to_update: Vec<String> = ips_data.iter().filter(|ip| !devices_ips.contains(ip)).map(|ip| ip.to_string()).collect();

                if ips_to_update.len() > 0 {
                    // 更新设备状态为离线
                    let status = "离线".to_string();
                    match Mutation::terminal_update_status_by_ips(&txn, ips_to_update, &status).await  {
                        Ok(r) => r,
                        Err(_) => continue
                    };

                    // 通知前端刷新页面
                    let process = serde_json::json!({
                        "type": "terminalRefresh",
                    }).to_string();
                    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: process };
                    ws_server.do_send(msg);
                }

                // 结束事务
                match txn.commit().await {
                    Ok(r) => r,
                    Err(_) => continue
                };


            
                
                
            }
        }
    }
    // 文件发送通道数据解析与接收
    pub async fn file_message_recive(&mut self , sender:mpsc::UnboundedSender<FileTransmitPacket>){
        
        

        
        loop {
            // 接收缓存
            let mut file_message_buf = [0u8; FILE_MESSAGE_BUFFER_SIZE];
            // 接收文件传输数据
            let (amt, _) = match self.socket.recv_from(&mut file_message_buf).await {
                Ok(amt) => amt,
                Err(e) => {
                    println!("文件数据接收失败: {}", e);
                    continue;
                }
            };         
            // let mut data_lave_hander: usize = 0;
            loop {
                // 找出udp数据包头部
                let mut data_lave_hander: usize = 0;
                println!("数据包长度: {}", amt);
                let (header_index, _) = match file_message_buf[data_lave_hander..amt]
                    .windows(3)
                    .enumerate()
                    .find(|(_, window)| window == b"hxr") {
                        Some(header_index) => header_index,
                        None => {
                            println!("文件数据接收失败: 未找到头部");
                            continue;
                        }
                    };
                    println!("数据包头部: {:?}", &file_message_buf[header_index..header_index + 3]);
                // 更新偏移量
                let data_lave_len = amt - header_index;
                data_lave_hander = header_index + 3;
                println!("数据包剩余长度: {}", data_lave_len);
                // 信息类型
                let message_type: [u8;1] = file_message_buf[header_index .. header_index + 1].try_into().unwrap();
                if &message_type == b"3" {
                    println!("文件数据接收成功: {:?}", &file_message_buf[data_lave_hander ..data_lave_hander + 101]);
                    if data_lave_len < 101 {
                        break;
                    }
                    let data = file_message_buf[data_lave_hander + 1 ..data_lave_hander + 101].to_vec();
                    let current_packet = FileTransmitPacket{
                        header: message_type,
                        data,
                    };
                    sender.send(current_packet).unwrap();
                }        
            }
        }
    }

    // 查询当前下载任务是否存在在任务传输列表中，并返回结果
    pub async fn send_course_file(&mut self,course_slug: String, download_count: u32, part_ids: Vec<u16>)-> (TaskState,Vec<u8>){
        let mut tasks =self.tasks.lock().await;
        // 查找tasks中的slug是否存在当前course_slug
        for task in tasks.iter_mut(){
            if task.course_slug == course_slug{
                
                // 检查对应目录是否存在
                let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));
                if !dir_path.exists() {
                    return (TaskState::Failed,Vec::new());
                }
                let file_size = fs::metadata(&dir_path);
                // 将文件作为二进制流读出
                let file_content = fs::read(&dir_path).unwrap();
                // 创建文件id（sha256哈希值）
                let hash = digest::digest(&digest::SHA256, &file_content);
                let hash_bytes: &[u8] = hash.as_ref();
                // 更新task.file_id
                task.file_id = hash_bytes.try_into().unwrap();

                if task.download_count == 0{ 
                    task.download_count = 1;
                }
                task.download_count += 1;

                if part_ids.len() > 0{ 
                    // 将 task.part_ids 转换为 HashSet 进行去重操作
                    let mut unique_part_ids: std::collections::HashSet<u16> = task.part_ids.drain(..).collect();
                    // 将新的 part_ids 插入 HashSet（自动去重）
                    for id in part_ids {
                        unique_part_ids.insert(id);
                    }
                    // 将去重后的结果转回 Vec
                    task.part_ids.extend(unique_part_ids);
                } else {
                    task.part_ids = vec![];
                    task.reset = false;
                }
                return (TaskState::Exist,task.file_id.to_vec());
            }
        }
        if part_ids.len() > 0 { 
            return (TaskState::Failed,Vec::new());
        }
        // 检查对应目录是否存在
        let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));
        if !dir_path.exists() {
            return (TaskState::Failed,Vec::new());
        }
        let file_size = fs::metadata(&dir_path);
        // 将文件作为二进制流读出
        let file_content = fs::read(&dir_path).unwrap();
        // 创建文件id（sha256哈希值）
        let hash = digest::digest(&digest::SHA256, &file_content);
        let hash_bytes: &[u8] = hash.as_ref();
        tasks.push(Task::new(course_slug.clone() , file_size.unwrap().len() , hash_bytes, download_count));

        (TaskState::Create,hash_bytes.to_vec())
    }
    // 选择合适的任务
    pub async fn select_task(&mut self) -> (Option<String>, Option<Vec<u8>>) {
        let mut tasks = self.tasks.lock().await;
        let mut course_slug_score = 0;
        let mut course_slug: Option<String> = None ;
        let mut file_id: Option<Vec<u8>> = None;
        for task in &*tasks {
            let interval = task.first_request_time.elapsed().unwrap();
            let interval_seconds: i32 = interval.as_secs() as i32;
            let file_size = task.file_size as i32;
            let score = (interval_seconds as i128 + (task.download_count as i128)*10) * file_size as i128;

            if score > course_slug_score && task.download_count != 0{
                course_slug_score = score;
                course_slug = Some(task.course_slug.clone());
                file_id = Some(task.file_id.to_vec());
            }
        }
        if course_slug.is_some() && file_id.is_some(){ 
            for task in &mut *tasks {
                if task.course_slug == course_slug.clone().unwrap(){
                    task.download_count = 0;
                }
            }
        }

        (course_slug,file_id)
    }
    // 发送任务文件
    pub async fn send_task_file(
        &mut self,
        course_slug: String, 
        file_id: Vec<u8>, 
        task_reset: bool, 
        part_ids: Vec<u16>,
    )-> bool {
        // 设置当前设备的公告标志为 Public
        let current_self = self.clone();
        
        let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));
        let mut file = match File::open(&dir_path) {
            Ok(f) => f,
            Err(_) => return false,
        };
        // 获取文件大小
        let file_size = match file.metadata() {
            Ok(metadata) => metadata.len(),
            Err(_) => return false,
        };
        let file_size = file_size as usize;
        // 初始化数据
        let mut buffer = vec![0; CHUNK_SIZE];
        let mut part_offset = 0;

            
        let mut package_count: u16 = 0;
        // 数据包顺序
        let mut part_id: u16 = 0;
        
        // 对文件分片
        while file_size > part_offset {
            // 获取任务
            let tasks_guard = self.tasks.lock().await;
            let task = tasks_guard.iter().find(|task| task.course_slug == course_slug).cloned();        
            drop(tasks_guard);
            //检查任务是否存在
            if let None = task {
                println!("任务不存在");
                // 没有找到匹配的任务
                return false;
            }
            let part_buffer_index = 0;
            // 读取文件分片
            let bytes_read = match file.read(&mut buffer) {
                Ok(bytes_read) => bytes_read,
                Err(_) => return false,
            };

            // 计算当前分片的SHA-256散列值
            let remaining_read_bytes = bytes_read - part_buffer_index;
            let read_bytes_to_send = std::cmp::min(remaining_read_bytes, CHUNK_SIZE);
            let hash = digest::digest(&digest::SHA256, &buffer[part_buffer_index..read_bytes_to_send]);
            let hash_bytes = hash.as_ref();

            // 创建一个数据包，包含散列值和分片数据
            let mut packet = Vec::with_capacity(MAX_PACKET_SIZE);
            // packet.extend_from_slice(&hash_bytes);
            packet.extend_from_slice(&buffer[..bytes_read]);
            
            // 如果数据包大小超过最大包大小，则分包发送
            let mut packet_offset = 0;
            while packet_offset < packet.len() {
                    
                // 计算本次发送分包数据
                let remaining_bytes = packet.len() - packet_offset;
                let bytes_to_send = std::cmp::min(remaining_bytes, MAX_PACKET_SIZE - HASH_SIZE);
                
                    
                // 将文件内容存入消息中
                let mut file_buf = Vec::new();
                // 消息头部
                file_buf.extend_from_slice(b"hxr1");        // 4byte
                // 文件ID
                file_buf.extend_from_slice(&file_id);       // 32byte

                // 数据偏移量
                file_buf.extend_from_slice(&package_count.to_be_bytes());   // 2byte
                // 数据包顺序
                file_buf.extend_from_slice(&part_id.to_be_bytes());       // 2byte

                if !task_reset || (task_reset && part_ids.contains(&part_id)){ 
                    // println!("part_id: {}, part_ids: {:?}", part_id, part_ids);
                    // 文件大小
                    file_buf.extend_from_slice(&(bytes_to_send as u64).to_be_bytes());     // 8byte
                    
                    // 文件内容
                    file_buf.extend_from_slice(&packet[packet_offset..(packet_offset + bytes_to_send.clone())]);    
                    // 文件内容长度对齐
                    file_buf.extend_from_slice(&vec![0; (MAX_PACKET_SIZE - HASH_SIZE - bytes_to_send) as usize]);   // 60kb
                    
                    // 加入对该条消息总体的验证
                    let message_hash = digest::digest(&digest::SHA256, &file_buf);
                    let message_hash_bytes: &[u8] = message_hash.as_ref();                  
                    file_buf.extend_from_slice(message_hash_bytes);     // 32byte
                    // 增加发送间隔以减少网络拥塞和数据包乱序
                    // if task_reset && !part_ids.is_empty(){
                    //     tokio::time::sleep(std::time::Duration::from_millis(50)).await;
                    // }
                    // tokio::time::sleep(std::time::Duration::from_millis(10)).await;
                    // 发送消息
                    Self::announce_for_bytes(
                        &current_self.socket,
                        file_buf.as_ref(), 
                        (current_self.multicast_addr, current_self.multicast_port),
                    )
                    .await;
                    tokio::time::sleep(std::time::Duration::from_millis(5)).await;
                }


                // 记录该分片发送了多少
                part_id += 1;
                packet_offset += bytes_to_send;


                // 判断是否为该分片的最后一个数据包
                if bytes_to_send == remaining_bytes {

                    // 发送分片校验报文
                    // 缓存清理
                    file_buf.clear();
                    // 消息头部
                    file_buf.extend_from_slice(b"hxr2");        // 4byte
                    // 文件ID
                    file_buf.extend_from_slice(&file_id);       // 32byte
                    // 数据偏移量
                    file_buf.extend_from_slice(&package_count.to_be_bytes());   // 2byte
                    // 数据包顺序
                    file_buf.extend_from_slice(&part_id.to_be_bytes());       // 2byte
                    
                    // 分片校验信息
                    file_buf.extend_from_slice(&hash_bytes);    // 32byte

                    // if !task_reset || (task_reset && part_ids.contains(&part_id)){ 
                        // println!("part_id: {}, part_ids: {:?}", part_id, part_ids);
                        // 加入对该条消息总体的验证
                        let message_hash = digest::digest(&digest::SHA256, &file_buf);
                        let message_hash_bytes: &[u8] = message_hash.as_ref();                  
                        file_buf.extend_from_slice(message_hash_bytes);     // 32byte
                        // let announce_msg = serde_json::to_string(&file_buf).unwrap();
                        Self::announce_for_bytes(
                            &current_self.socket,
                            file_buf.as_ref(),
                            (current_self.multicast_addr, current_self.multicast_port),
                        )
                        .await;
                        // 延迟10ms
                        tokio::time::sleep(std::time::Duration::from_millis(10)).await;
                    // }

                    part_id += 1;
                }
            }
            // 记录分片偏移
            part_offset += packet.len();
            package_count += 1;
        }

        // 文件发送完成
        // 等待100ms以确保该包不会丢失
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let mut end_buf = Vec::new();
        end_buf.extend_from_slice(b"hxr4");     // 4byte
        end_buf.extend_from_slice(&file_id);    // 32byte
        // 数据包顺序
        end_buf.extend_from_slice(&part_id.to_be_bytes());       // 2byte
        // part_id += 1;
        
        // 加入对该条消息总体的验证
        let message_hash = digest::digest(&digest::SHA256, &end_buf);
        let message_hash_bytes: &[u8] = message_hash.as_ref();                  
        end_buf.extend_from_slice(message_hash_bytes);     // 32byte
            
        // let announce_msg = serde_json::to_string(&end_buf).unwrap();
        Self::announce_for_bytes(
            &current_self.socket,
            end_buf.as_ref(),
            (current_self.multicast_addr, current_self.multicast_port),
        )
        .await;
        // 等待100ms以确保该包不会丢失
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
        // }
        true
    }
    // 等待分片校验结果
    pub async fn wait_for_hash_result(
        &self, 
        course_slug: String, 
        receiver:Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>, 
        silce_hash:Vec<u8>,
        send_count:i32
    ) -> bool {
        // 依据send_count等待一定时长
        tokio::time::sleep(Duration::from_secs((send_count + 1) as u64)).await;

        // 获取请求次数
        let mut tasks =self.tasks.lock().await;

        let mut download_task  = 0;
        for task in tasks.iter_mut(){
            if task.course_slug == course_slug{
                download_task = task.download_count;
            }
        }
        // 计数器
        let mut empty_count = 0;
        // 接收校验确认报文
        let mut receiver_lock = receiver.lock().await;
        loop {
            match receiver_lock.try_recv(){
                Ok(received) => {
                    println!("received: {:?}", received);
                    // 寻找校验报文
                    if &received.header == b"3" {
                        // 判断该报文是否完整
                        let mut check_buf = Vec::new();
                        check_buf.extend_from_slice(b"hxr");
                        check_buf.extend_from_slice(&received.header);
                        check_buf.extend_from_slice(&received.data);
                        let hash = digest::digest(&digest::SHA256, &check_buf);
                        let hash_bytes = hash.as_ref().to_vec();
                        let hash_result = received.data[68..100].to_vec();
                        if hash_bytes == hash_result {
                            // 校验是否为当前文件
                            let file_id = received.data[..32].to_vec();
                            // 判断是否为当前分片
                            let package_hash = received.data[32 ..64].to_vec();
                            
                            if file_id == self.this_udp.file_id && package_hash == silce_hash {
                                // 获取校验位信息
                                let result = received.data[64 ..65].to_vec();
                                if &result == b"1" {
                                    download_task -= 1;
                                }
                            }
                        }
                    }
                    if download_task == 0 {
                        return true;
                    }
                },
                Err(mpsc::error::TryRecvError::Empty) => {
                    empty_count += 1;
                    if empty_count == 5 {
                        return false;
                    }
                },
                _ => {
                    return false;
                }
            }
        }
        
    }
    // 发送开始上课消息
    pub async fn send_start_class_msg(&mut self , course_slug: String) -> bool {
        
        let mut current_self = self.clone();
        
        current_self.this_udp.message_type = UDPMessageType::StartClass;
        current_self.this_udp.message = Some(course_slug.clone());

        // 检查对应目录是否存在
        let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug.clone()));
        if !dir_path.exists() {
            return false;
        }
        // 将文件作为二进制流读出
        let file_content = fs::read(&dir_path).unwrap();
        // 读取文件id（sha256哈希值）
        let hash = digest::digest(&digest::SHA256, &file_content);
        let hash_bytes: &[u8] = hash.as_ref();
        current_self.this_udp.file_id = hash_bytes.to_vec();

        let announce_msg = serde_json::to_string(&current_self.this_udp).unwrap();
        Self::announce( 
            &current_self.socket,
            announce_msg.as_str(),
            (current_self.multicast_addr, current_self.multicast_port),
        )
        .await;    
        current_self.this_udp.message_type = UDPMessageType::Stop;

        let db_path = get_path_in_exe_dir("hxr.db");
        let db_url = format!("sqlite://{}?mode=rwc", db_path.to_str().unwrap());
        let conn: sea_orm::prelude::DatabaseConnection = Database::connect(&db_url).await.unwrap();
        let now = Local::now().naive_local();
        
        // 开启事务
        let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
            Ok(r) => r,
            Err(_) => return false
        };
    
    
        // 查询是否有未结束课程计划，如果有则禁止创建新的课程计划
        // 自系统配置表读取当前课程ID
        let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
            Ok(r) => r,
            Err(_) => {
                txn.rollback().await.unwrap();
                return false
            }
        };
    
    
        if current_train_plan_id.is_some() {
            txn.rollback().await.unwrap();
            return false
        }
    
    
        // 写入当前课程到系统配置表
        let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
        key_value_map.insert("current_course_slug".into(), Some(course_slug.to_string()));
    
    
        match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
            Ok(count) => count,
            Err(_) => return false
        };
    
    
        // 清空当前训练计划学生的登录ip
        match Mutation::clear_current_user_login_ip(&txn).await  {
            Ok(r) => r,
            Err(_) => return false
        };
    
    
        // 结束事务
        match txn.commit().await {
            Ok(r) => r,
            Err(_) => return false
        };
        true
    }
    // 获取当前设备列表
    pub fn get_devices(&self) -> Vec<UDPMessageInfo> {
        self.devices.clone()
    }
    
}
impl fmt::Debug for UDPService {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("UDPService")
            .field("addr", &self.devices)
            .finish()
    }
}

impl Clone for UDPService {
    fn clone(&self) -> Self {
        
        UDPService {
            devices: self.devices.clone(),
            socket: self.socket.clone(),
            this_udp:self.this_udp.clone(),
            interface_addr:self.interface_addr.clone(),
            multicast_addr: self.multicast_addr,
            multicast_port: self.multicast_port,
            sender: self.sender.clone(),
            receiver: self.receiver.clone(),
            tasks: self.tasks.clone(),
            file_is_sending: self.file_is_sending.clone(),
        }
    }
}