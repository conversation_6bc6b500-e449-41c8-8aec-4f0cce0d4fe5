use sea_orm::{entity::prelude::*, From<PERSON><PERSON>y<PERSON><PERSON>ult};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct SectionRecordItem {
  pub user_id: i32,
  pub section_id: i32,
  // pub stay_time:i32,
  pub record: Option<Json>,
  pub total_score: i32,
  pub pass_count: i32,  
  pub created_at: DateTime,
  pub updated_at: Option<DateTime>,

}
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct OnlySectionRecordItem {
  // pub stay_time:i32,
  pub record: Option<Json>,
}
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct SectionRecordUpdateItem {
  pub user_id: i32,
  pub section_id: i32,
  pub record: Option<Json>,
  pub total_score: i32,
  pub pass_count: i32,  
  pub updated_at: DateTime,
}
#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Deserialize, Serialize)]
pub struct SectionRecordCreateItem {
  pub user_id: i32,
  pub section_id: i32,
  pub record: Option<Json>,
  pub total_score: i32,
  pub pass_count: i32,  
  pub created_at: DateTime,
}
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "section_record")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub user_id: i32,
    pub section_id: i32,
    pub stay_time:Option<i32>,
    pub record: Json,
    pub total_score: i32,
    pub pass_count: i32,  
    pub created_at: Option<DateTime>,
    pub updated_at: Option<DateTime>,
    pub deleted_at: Option<DateTime>,
}
#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
  #[sea_orm(
      belongs_to = "super::section::Entity",
      from = "Column::SectionId",
      to = "super::section::Column::Id",
      on_update = "Cascade",
      on_delete = "Cascade"
  )]
  Section,
}

impl Related<super::section::Entity> for Entity {
  fn to() -> RelationDef {
      Relation::Section.def()
  }
}
impl ActiveModelBehavior for ActiveModel {}