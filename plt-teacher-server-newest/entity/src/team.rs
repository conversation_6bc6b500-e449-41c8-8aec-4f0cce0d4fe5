//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6
use serde::{Deserialize, Serialize};
use sea_orm::{entity::prelude::*, FromQueryResult};

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct TeamItem {
    pub id: i32,
    pub name: String,
}

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "team")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub name: String,
    pub year: String,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::team_user::Entity")]
    TeamUser,
}

impl Related<super::team_user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TeamUser.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

// 用来导入数据用的格式
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TeamUserRecord {
    pub id: i32,
    pub username: String,
    pub password: Option<String>,
    pub name: String,
    pub school: Option<String>,
    pub sen: Option<String>,
    pub state: Option<String>,
    pub avatar: Option<String>,
    #[serde(rename="adminAuthority")]
    pub admin_authority: Option<Json>,
    #[serde(rename="deleted_at")]
    pub deleted_at: Option<String>,
    #[serde(rename="team_user_deleted_at")]
    pub team_user_deleted_at: Option<String>,
    #[serde(rename="team_user_id")]
    pub team_user_id: i32,
}

// 用来导入数据用的格式
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct UploadUserRecord {
    pub id: i32,
    pub username: String,
    pub password: Option<String>,
    pub name: String,
    pub school: Option<String>,
    pub sen: Option<String>,
    pub state: Option<String>,
    pub avatar: Option<String>,
    #[serde(rename="adminAuthority")]
    pub admin_authority: Option<Json>,
    #[serde(rename="deleted_at")]
    pub deleted_at: Option<String>,
    #[serde(rename="team_user_deleted_at")]
    pub team_user_deleted_at: Option<String>,
    #[serde(rename="team_user_id")]
    pub team_user_id: Option<i32>,
}


#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TeamUsersRecord {
    #[serde(rename="classID")]
    pub team_id: i32,
    #[serde(rename="className")]
    pub team_name: String,
    #[serde(rename="year")]
    pub school_year: String,
    #[serde(rename="userList")]
    pub users: Vec<TeamUserRecord>
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TeamUsersRecordOffline {
    pub all_class_ids: Option<Vec<i32>>,
    pub class_list: Vec<TeamUsersRecord>
}

#[derive(Debug, Clone, PartialEq, Eq, FromQueryResult, Deserialize, Serialize)]
pub struct TeamUserShortRecord {
    pub id: i32,
    pub name: String,
    pub user_count: i32,
}