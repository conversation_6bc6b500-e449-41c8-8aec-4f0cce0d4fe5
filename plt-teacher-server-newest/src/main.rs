use std::{env, net::SocketAddr};
use single_instance::SingleInstance;
use port_check;
use service::get_path_in_exe_dir;

fn main() {
    // // 禁止多开
    // let instance = SingleInstance::new("hxr-train-server").unwrap();
    // if !instance.is_single() {
    //     println!("氦星人教师机服务器应用已启动，无需重复启动");
    //     return;
    // }

    // 加载配置文件
    let teacher_env_file_path = get_path_in_exe_dir("server.env");
    let teacher_env_file_path = teacher_env_file_path.to_str().unwrap();
    if !dotenvy::from_filename(teacher_env_file_path).is_ok() {
        println!("无法加载教师机配置文件server.env, 路径{}找不到", teacher_env_file_path);
        return;
    }

    let port = match env::var("PORT") {
        Ok(r) => r,
        Err(_e) => {
            println!("请在server.env中配置服务器监听端口号PORT字段");
            return;
        }
    };

    let port_u16:u16 = match port.parse::<u16>() {
        Ok(r) => r,
        Err(_e) => {
            println!("端口号配置异常{}", port);
            return;
        }
    };

    // 如果端口已经被占用，报错退出
    if port_check::is_port_reachable( SocketAddr::from(([127, 0, 0, 1], port_u16))) {
        println!("端口号{}已经其他应用被占用，请释放该端口号或者在server.env中配置服务器监听端口号PORT字段为其他可用端口号", port);
        return;
    }

    // 尝试自配置文件中读取COOKIE_KEY字段
    let cookie_key = match env::var("COOKIE_KEY") {
        Ok(r) => Option::Some(r),
        Err(_e) => Option::None
    };

    // 验证Key是一个64字节字母或者数字
    if let Some(key) = &cookie_key {
        if key.len() != 64 {
            println!("COOKIE_KEY长度不是64位，请检查配置文件server.env中COOKIE_KEY字段");
            return;
        }
        if !key.chars().all(|c| c.is_ascii_alphanumeric()) {
            println!("COOKIE_KEY包含非法字符，请检查配置文件server.env中COOKIE_KEY字段");
            return;
        }
    }

    api::main(port_u16, cookie_key);
}
