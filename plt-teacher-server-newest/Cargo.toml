[package]
name = "plt-teacher-server"
version = "0.1.0"
authors = ["张未波(<EMAIL>)"]
edition = "2021"
publish = false

[workspace]
members = [".", "api", "service", "entity", "migration", "websocket","localsend"]

[workspace.dependencies]
rayon-core = "1.12.1"
rayon = "1.10.0" 

[dependencies]
single-instance = "0.3"
dotenvy = "0.15"
port_check = "0.2.1"
api = { path = "api" }
service = { path = "service" }