use tokio::task;

#[cfg(target_os = "windows")]
use std::os::windows::prelude::OsStrExt;

#[cfg(target_os = "windows")]
use {
    winapi::um::fileapi::{DefineDosDeviceW, QueryDosDeviceW},
    winapi::um::errhandlingapi::GetLastError,
    std::ffi::OsStr,
    std::ptr::null_mut,
};

// 转换字符串为宽字符串
#[cfg(target_os = "windows")]
fn to_wchar(str : &str) -> Vec<u16> {
    OsStr::new(str).encode_wide().chain(Some(0).into_iter()).collect()
}

// 定义虚拟盘
#[cfg(target_os = "windows")]
pub async fn mount_virtual_disk(device_name: &str, target_path: &str) {
    let device_name_owned = format!("{}:", device_name);
    let target_path_owned = target_path.to_owned();

    let lp_device_name = to_wchar(&device_name_owned);
    let lp_target_path = to_wchar(&target_path_owned);

    println!("执行挂载命令: DefineDosDevice {} -> {}", device_name_owned, target_path_owned);

    let _ = task::spawn_blocking(move || unsafe {
        let result = DefineDosDeviceW(0, lp_device_name.as_ptr(), lp_target_path.as_ptr());
        if result != 0 {
            println!("虚拟盘挂载成功: {} -> {}", device_name_owned, target_path_owned);
        } else {
            let error = GetLastError();
            println!("虚拟盘挂载失败，错误代码: {}", error);
        }
    }).await;
}

// 卸载虚拟盘
#[cfg(target_os = "windows")]
pub async fn unmount_virtual_disk(device_name: &str) {
    let device_name_owned = format!("{}:", device_name);
    let lp_device_name = to_wchar(&device_name_owned);
    const DDD_REMOVE_DEFINITION: u32 = 0x00000002;

    println!("执行卸载命令: DefineDosDevice {} (移除)", device_name_owned);

    let _ = task::spawn_blocking(move || unsafe {
        let result = DefineDosDeviceW(DDD_REMOVE_DEFINITION, lp_device_name.as_ptr(), null_mut());
        if result != 0 {
            println!("虚拟盘卸载成功: {}", device_name_owned);
        } else {
            let error = GetLastError();
            println!("虚拟盘卸载失败，错误代码: {}", error);
        }
    }).await;
}

#[cfg(not(target_os = "windows"))]
pub fn mount_virtual_disk(_device_name: &str, _target_path: &str) {
    unimplemented!("只有Windows系统可以实现该函数")
}

#[cfg(not(target_os = "windows"))]
pub fn unmount_virtual_disk(_device_name: &str) {
    unimplemented!("只有Windows系统可以实现该函数")
}

// fn main() {
//     // mount_virtual_disk("z:", "D:\\迅雷下载");
//     // unmount_virtual_disk("z:");
// //     block_on(server_main());
// //     block_on(ui_main());
// }