use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::time::Duration;
use regex::Regex;
use reqwest::Client;
use reqwest::cookie::Jar;
use url::Url;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use reqwest;
use std::io::Cursor;
use std::sync::Arc;
use urlencoding::encode;
use tokio::time::sleep;

use crate::{extract_zip, get_path_in_exe_dir};

use super::fs::write_log;
use super::bussiness_err::HxrError;

// 普通超时
pub const NORMAL_TIME_OUT:u64 = 10;

// 重载超时
pub const HEAVY_TIME_OUT:u64 = 120;

// 文件下载超时
pub const DOWNLOAD_TIME_OUT:u64 = 120;

#[derive(Serialize, Deserialize, Clone)]
pub struct RemoteSession {
    site_url: String,
    cookies: Vec<String>,
    csrf_token: String,
}

async fn http_get_json_with_cookies(client: &reqwest::Client, url: &String, timeout: u64) -> Result<(HashMap<String, Value>, Vec<String>), Box<dyn std::error::Error>> {
    // 写入日志
    write_log(String::from("http_get_json_with_cookies ") + url);

    let res = match client.get(url)
        .timeout(Duration::from_secs(timeout))
        .send() 
        .await {
            Ok(r) => r,
            Err(e) => {
                let message = String::from("在请求") + url.as_str() + "网络出错:" + e.to_string().as_str();
                let hxr_err = HxrError {
                    message: message
                };

                return Err(Box::new(hxr_err));
            }
        };

    let res_err = res.error_for_status_ref();
    if let Err(err) = res_err {
        println!("{}", err);
        assert_eq!(err.status(), Some(reqwest::StatusCode::BAD_REQUEST));
    }

    let mut cookies = Vec::new();
    let header_cookies = res.headers().get_all("set-cookie");
    for header_cookie in header_cookies {
        cookies.push(String::from(header_cookie.to_str()?));
    }

    let resp = res.json::<HashMap<String, Value>>().await?;
    write_log(serde_json::to_string(&resp).unwrap());

    Ok((resp, cookies))
}

async fn http_post_json_value(client: &reqwest::Client, url: &String, data: &HashMap<String, Value>, timeout: u64) -> Result<HashMap<String, Value>, Box<dyn std::error::Error>> {
    // 写入日志
    write_log(String::from(" http_post_json_value ") + url + " " + serde_json::to_string(data).unwrap().as_str());

    let res = match client.post(url)
        .timeout(Duration::from_secs(timeout))
                .json(&data)
        .send() 
        .await {
            Ok(r) => r,
            Err(e) => {
                let message = String::from("在请求") + url.as_str() + "网络出错:" + e.to_string().as_str();
                let hxr_err = HxrError {
                    message
                };

                return Err(Box::new(hxr_err));
            }
        };

    let resp = res.json::<HashMap<String, Value>>().await?;
    println!("response = {:?}", resp);
    write_log(serde_json::to_string(&resp).unwrap());

    Ok(resp)
}

async fn http_get_json(client: &reqwest::Client, url: &String, timeout: u64) -> Result<HashMap<String, Value>, Box<dyn std::error::Error>> {
    // 写入日志
    write_log(String::from("http_get_json ") + url);

    let res = match client.get(url)
        .timeout(Duration::from_secs(timeout))
        .send() 
        .await {
            Ok(r) => r,
            Err(e) => {
                let message = String::from("在请求") + url.as_str() + "网络出错:" + e.to_string().as_str();
                let hxr_err = HxrError {
                    message: message
                };

                return Err(Box::new(hxr_err));
            }
        };

    let resp = res.json::<HashMap<String, Value>>().await?;
    write_log(serde_json::to_string(&resp).unwrap());

    Ok(resp)
}

async fn http_get_text(client: &reqwest::Client, url: &String, timeout: u64) -> Result<String, Box<dyn std::error::Error>> {
    // 写入日志
    write_log(String::from("http_get_text ") + url);

    let res = match client.get(url)
        .timeout(Duration::from_secs(timeout))
        .send()
        .await {
            Ok(r) => r,
            Err(e) => {
                let message = String::from("在请求") + url.as_str() + "网络出错:" + e.to_string().as_str();
                let hxr_err = HxrError {
                    message: message
                };

                return Err(Box::new(hxr_err));
            }
        };

    let resp = res.text().await?;
    write_log(resp.clone());

    Ok(resp)
}

pub async fn http_get_file(client: &reqwest::Client, url: &String, file_path: &PathBuf, timeout: u64) -> Result<(), Box<dyn std::error::Error>> {
    // 写入日志
    write_log(String::from(" http_get_file ") + url + " " + file_path.to_str().unwrap());

    let mut retry_count = 3; // 设置重试次数
    let mut res = None;

    while retry_count > 0 {
        match client.get(url)
            .timeout(Duration::from_secs(timeout))
            .send()
            .await {
                Ok(r) => {
                    res = Some(r);
                    break;
                }
                Err(e) => {
                    write_log(format!("在请求 {} 时网络出错: {}", url, e));
                    retry_count -= 1;
                    if retry_count == 0 {
                        return Err(Box::new(e));
                    }
                    // 等待一段时间后重试
                    sleep(Duration::from_secs(1)).await; // 等待1秒
                }
            };
    }

    if let Some(r) = res {
        let mut file = std::fs::File::create(file_path)?;
        let mut content = Cursor::new(r.bytes().await?);
        std::io::copy(&mut content, &mut file)?;
        Ok(())
    } else {
        unreachable!("Should not reach here as the loop should have been exited with a successful response.");
    }
}


pub async fn post_course_download_course_zip(
    url: String,
    course_slug: String,
) -> bool {
    // println!("3");
    // let request = serde_json::from_str::<HashMap<String, Value>>(&request).unwrap();
    // println!("{:?}", request);
    // // let url = request.get("url");
    // let mut url: String = "".to_string();
    // let mut course_slug: String = "".to_string();
    // if let Some(Value::String(res)) = request.get("url") {
    //     url = res.to_string();
    // }
    // if let Some(Value::String(res)) = request.get("courseSlug") {
    //     course_slug = res.to_string();
    // }
    // 获取HTTP客户端
    let client = match get_client() {
        Ok(r) => r,
        Err(e) => return false
    };
    
    // 下载课程文件
    let dir_path = get_path_in_exe_dir("course");
    if !dir_path.exists() {
        fs::create_dir_all(&dir_path).expect("创建目录失败");
    }

    let target_file_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));

    // let get_file = http_get_file(&client, &url, &target_file_path, DOWNLOAD_TIME_OUT);
    match http_get_file(&client, &url, &target_file_path, DOWNLOAD_TIME_OUT).await {
        Ok(r) => r,
        Err(e) => return false
    }
    // 等待下载完成
    
    // 解压课程文件
    let course_file_path = get_path_in_exe_dir("course").join(course_slug.clone());
    match extract_zip(&target_file_path, &course_file_path) {
        Ok(r) => r,
        Err(e) => {
            return false;
        }
    }
    true

}
pub async fn http_post_file(client: &reqwest::Client, body: &String, url: &String, file_path: &PathBuf, timeout: u64) -> Result<(), Box<dyn std::error::Error>> {
    // 写入日志
    write_log(String::from(" http_get_file ") + url + " " + file_path.to_str().unwrap());

    let mut retry_count = 3; // 设置重试次数
    let mut res = None;

    while retry_count > 0 {
        match client.post(url)
            .body(body.clone())
            .timeout(Duration::from_secs(timeout))
            .send()
            .await {
                Ok(r) => {
                    res = Some(r);
                    break;
                }
                Err(e) => {
                    write_log(format!("在请求 {} 时网络出错: {}", url, e));
                    retry_count -= 1;
                    if retry_count == 0 {
                        return Err(Box::new(e));
                    }
                    // 等待一段时间后重试
                    sleep(Duration::from_secs(1)).await; // 等待1秒
                }
            };
    }

    if let Some(r) = res {
        let mut file = std::fs::File::create(file_path)?;
        let mut content = Cursor::new(r.bytes().await?);
        std::io::copy(&mut content, &mut file)?;
        Ok(())
    } else {
        unreachable!("Should not reach here as the loop should have been exited with a successful response.");
    }
}

// 自Session解开URL、Cookie，CSRF Token，生成配置好的客户端
pub fn get_client_csrf_token_from_session(session: RemoteSession) -> Result<(Client, String, String), Box<dyn std::error::Error>> {
    // 自Session解开URL、Cookie，CSRF Token
    let url = session.site_url.parse::<Url>().unwrap();
    
    let jar = Jar::default();

    for cookie in session.cookies {
        jar.add_cookie_str(&cookie, &url);
    }

    // HTTP客户端，支持Session
    let client = reqwest::Client::builder()
        .user_agent("hxrdll/0.01")
        .cookie_store(true)
        .cookie_provider(Arc::from(jar))
        .build()?;
    
    return Ok((client, session.site_url, session.csrf_token))
}

// 生成客户端
pub fn get_client() -> Result<Client, Box<dyn std::error::Error>> {
    // HTTP客户端，支持Session
    let client = reqwest::Client::builder()
        .user_agent("hxrdll/0.01")
        .build()?;
    
    return Ok(client)
}


pub struct Remote;
impl Remote {
    // 登录远程教师账号、密码获取会话
    pub async fn teacher_login(url: String, mac: String, lab: String, username: String, password: String, is_skip_license: bool) -> Result<HashMap<String, Value>, Box<dyn std::error::Error>> {
        // HTTP客户端，支持Session
        let client = reqwest::Client::builder()
            .user_agent("hxrpclab/0.01")
            .cookie_store(true)
            .build()?;
        
        // 调用 GET /api/admin/user/adminSession 获取管理员会话，获取其中的csrfToken
        // let session_url = String::from(url.clone()) + "/api/admin/user/adminSession";
        let session_url = String::from(url.clone()) + "/admin/user/adminSession";

        // 取Cookie
        let (session_response, cookies) = http_get_json_with_cookies(&client, &session_url, NORMAL_TIME_OUT).await?;
        println!("session_response = {:?}", session_response);
        let csrf_token_value = match session_response.get("csrfToken") {
            Some(r) => r,
            None => {
                let hxr_err = HxrError {
                    message: "请检查网址是否正确，通信是否正常，当前无法获取服务器Token！".to_string()
                };
    
                return Err(Box::new(hxr_err));
            }
        };

        let csrf_token = csrf_token_value.as_str().unwrap();

        // 生成登录请求
        let mut requst_json: HashMap<String, Value> = HashMap::new();
        requst_json.insert(String::from("username"), Value::String(username));
        requst_json.insert(String::from("password"), Value::String(password));
        requst_json.insert(String::from("mac"), Value::String(mac));
        requst_json.insert(String::from("lab"), Value::String(lab));

        // 如果是开发版本
        if cfg!(debug_assertions) {
            // 允许跳过授权检查
            if is_skip_license {
                requst_json.insert(String::from("isSkipLicense"), Value::Bool(is_skip_license));
            }
        }

        requst_json.insert(String::from("_csrf"), Value::String(csrf_token.to_string()));

        // 调用 POST /api/admin/train/teacherLogin 完成登录，相比管理员增加检查机房授权逻辑，更新csrfToken
        // let login_url = String::from(url.clone()) + "/api/pclabTrain/teacherLoginForPLT";
        let login_url = String::from(url.clone()) + "/pclabTrain/teacherLoginForPLT";
        println!("login_url: {}, json = {:#?}", login_url, requst_json);
        let mut login_response = http_post_json_value(&client, &login_url, &requst_json, NORMAL_TIME_OUT).await?;

        let code = login_response.get("code");

        if code.is_none() {
            let hxr_err = HxrError {
                message: login_response.get("message").unwrap().to_string()
            };
    
            return Err(Box::new(hxr_err));
        }

        let code_value = login_response.get("code").unwrap();
        let code = code_value.as_f64().unwrap() as i32;

        // 511是授权错误，此时还需要后续处理
        if (code != 0) && (code != 511) {
            let hxr_err = HxrError {
                message: login_response.get("message").unwrap().to_string(),
            };

            return Err(Box::new(hxr_err));
        }

        // 请求串，包含Cookie, CSRF Token, URL
        login_response.insert(String::from("code"), serde_json::Value::from(code));
        login_response.insert(String::from("url"), serde_json::Value::from(String::from(url)));
        login_response.insert(String::from("cookies"), serde_json::Value::from(cookies));

        Ok(login_response)
    }

    // 获取全部班级
    pub async fn list_all_teams_by_school_year(session: RemoteSession, school_year: String) -> Result<Value, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用 POST /api/thirdPart/license 注册授权
        let school_year_encoded = encode(school_year.as_str()).into_owned();
        let classes_list_url = String::from(url) + "/api/pclabTrain/getClassListAll?year=" + school_year_encoded.as_str();

        let classes_list_response = http_get_json(&client, &classes_list_url, NORMAL_TIME_OUT).await?;
        let data = classes_list_response.get("data")
            .ok_or_else(|| HxrError {
                message: "无法获取班级列表".to_string()
            })?;

        return Ok((*data).clone());
    }

    // 根据班级ID获取指定班级用户账号
    pub async fn list_team_users_by_userid(session: RemoteSession, team_id: String) -> Result<Value, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用 POST /api/thirdPart/license 注册授权
        let team_id_encoded = encode(team_id.as_str()).into_owned();
        let classes_list_url = String::from(url) + "/api/pclabTrain/filterUserWithComputerRoomTrain?classID=" + team_id_encoded.as_str();

        let classes_list_response = http_get_json(&client, &classes_list_url, NORMAL_TIME_OUT).await?;
        let data = classes_list_response.get("data").unwrap();

        return Ok((*data).clone());
    }

    // 根据逗号分割的多个班级ID获取指定班级用户账号
    pub async fn list_team_users_by_userids(session: RemoteSession, team_ids: String) -> Result<String, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用 POST /api/thirdPart/license 注册授权
        let team_ids_encoded = encode(team_ids.as_str()).into_owned();
        let classes_list_url = String::from(url) + "/api/pclabTrain/filterBulkUserWithComputerRoomTrain?classIDs=" + team_ids_encoded.as_str();

        let classes_list_response = http_get_text(&client, &classes_list_url, NORMAL_TIME_OUT).await?;

        return Ok(classes_list_response);
    }

    // 获取指定学年的全部试卷
    pub async fn list_all_trains_by_series(session: RemoteSession, series: i32) -> Result<Value, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用 POST /api/thirdPart/license 注册授权
        let url = String::from(url) + "/api/pclabTrain/getTrainList?series=" + series.to_string().as_str();

        let list_response = http_get_json(&client, &url, NORMAL_TIME_OUT).await?;
        let data = list_response.get("data").ok_or_else(|| HxrError {
            message: "无法获取试卷列表".to_string()
        })?;

        return Ok((*data).clone());
    }

    // 获取在线全部试卷系列
    pub async fn list_all_series(session: RemoteSession) -> Result<String, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用 POST /api/thirdPart/license 注册授权
        let url = String::from(url) + "/api/pclabTrain/getTrainSeries";

        let list_response = http_get_text(&client, &url, HEAVY_TIME_OUT).await?;

        return Ok(list_response);
    }

    // 获取当前用户在线全部试卷系列
    pub async fn list_all_series_by_userid(session: RemoteSession, user_id: i32) -> Result<Value, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用 POST
        let url = String::from(url) + "/api/pclabTrain/getTrainSeries?userID=" + user_id.to_string().as_str();

        let list_response = http_get_json(&client, &url, NORMAL_TIME_OUT).await?;

        let data = list_response.get("data").ok_or_else(|| HxrError {
            message: "无法获取试卷系列".to_string()
        })?;


        return Ok((*data).clone());
    }

    // 根据逗号分割的多个训练ID获取指定训练配置与资产ZIP文件
    pub async fn create_train_zip_by_trainid(session: RemoteSession, train_id: i32, app_version: String) -> Result<String, Box<dyn std::error::Error>> {
        let (client, url, _csrf_token) = get_client_csrf_token_from_session(session)?;
        
        // 调用接口创建训练ZIP文件
        let train_id_encoded = encode(train_id.to_string().as_str()).into_owned();
        let url = String::from(url) + "/api/pclabTrain/createTrainFileZip?trainID=" + train_id_encoded.as_str() + "&appVersion=" + app_version.as_str();

        let list_response = http_get_text(&client, &url, HEAVY_TIME_OUT).await?;

        return Ok(list_response);
    }

    // 上报训练计划以及学生训练记录获得远程训练计划ID
    pub async fn train_plan_report(session: RemoteSession, data: HashMap<String, Value>) -> Result<String, Box<dyn std::error::Error>> {
        let (client, url, csrf_token) = get_client_csrf_token_from_session(session)?;

        // 整理发送数据，加入csrf token
        let mut request_json = data.clone();
        request_json.insert(String::from("_csrf"), Value::String(csrf_token));

        // 调用 POST/pclabTrain/postTrainPlanRecord 完成登录，相比管理员增加检查机房授权逻辑，更新csrfToken
        let remote_url = String::from(url.clone()) + "/api/pclabTrain/postTrainPlanRecord";
        let remote_response = http_post_json_value(&client, &remote_url, &request_json, HEAVY_TIME_OUT).await?;

        let code_value = remote_response.get("code").unwrap();
        let code = code_value.as_f64().unwrap() as i32;

        if code != 0 {
            let hxr_err = HxrError {
                message: remote_response.get("message").unwrap().to_string(),
            };

            return Err(Box::new(hxr_err));
        }

        let remote_id = remote_response.get("data").unwrap();
        let remote_id = remote_id.as_f64().unwrap();

        Ok((remote_id as i32).to_string())
    }

    // 查询升级信息
    pub async fn check_update(main_station:&str, teacher_version: &str, student_version: Option<String>, server_version: &str) -> Result<Value, Box<dyn std::error::Error>> {
        // 组装请求
        let mut request:HashMap<String, Value> = HashMap::new();
        request.insert(String::from("teacher"), Value::String(String::from(teacher_version)));
    
        if student_version.is_none() {
            request.insert(String::from("student"), Value::Null);
        }
        else {
            request.insert(String::from("student"), Value::String(student_version.unwrap()));
        }
    
        request.insert(String::from("server"), Value::String(String::from(server_version)));
    
        // HTTP客户端，支持Session
        let client = reqwest::Client::builder()
            .user_agent("hxrpclab/0.01")
            .cookie_store(true)
            .build()?;

        // 调用 GET /api/admin/user/adminSession 获取管理员会话，获取其中的csrfToken
        let session_url = String::from(format!("{}/api/main/getSession", main_station));

        // 取Cookie
        let (_session_response, cookies) = http_get_json_with_cookies(&client, &session_url, NORMAL_TIME_OUT).await?;
        let re = Regex::new(r"^csrfToken=([^;]+);").unwrap();
        let mut csrf_token_value: Option<String> = Option::None;
        for cookie in cookies {
            for captures in re.captures_iter(cookie.as_str()) {
                csrf_token_value = Some(captures[1].into());
            }
        }

        if csrf_token_value.is_none() {
            let hxr_err = HxrError {
                message: "无法自Cookie分析CSRF Token".into(),
            };

            return Err(Box::new(hxr_err));
        }

        let csrf_token = csrf_token_value.unwrap();

        request.insert(String::from("_csrf"), Value::String(csrf_token));

        // 执行请求，获取回复
        let url  = String::from(format!("{}/api/main/pclabTrain/check_update", main_station));
        let response = http_post_json_value(&client, &url, &request, NORMAL_TIME_OUT).await?;

        // 是否正常？
        let code_value = response.get("code").unwrap();
        let code = code_value.as_f64().unwrap() as i32;

        if code != 0 {
            let hxr_err = HxrError {
                message: response.get("message").unwrap().to_string(),
            };

            return Err(Box::new(hxr_err));
        }

        // 透传结果
        let data_value = response.get("data").unwrap();
    
        Ok((*data_value).clone())
    }
}