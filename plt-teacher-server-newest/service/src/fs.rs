use std::path::PathBuf;
use boa::property::Attribute;
use boa::{Context, JsValue};
use chrono::Utc;
use rand::distributions::Standard;

use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::path::Path;
use std::fs::{self, File};
use std::fs::OpenOptions;
use std::io::{self};
use std::io::Write;
use std::env;
use super::bussiness_err::HxrError;
use async_recursion::async_recursion;


// Windows调试可以用chcp 65001切换至UTF-8命令行，避免println!输出乱码
pub fn string_from_utf8_lossy(input: &[u8]) -> String {
    let mut string = String::new();
    utf8::LossyDecoder::new(|s| string.push_str(s)).feed(input);
    string
}

pub fn log_request(ip_address: &str, path: &str, user_id: i32, start_time: chrono::DateTime<chrono::Utc>) {
    // APP_LOG=1才有日志
    let env_var = env::var("APP_LOG");
    if !env_var.is_ok() {
        return;
    }

    if env_var.ok().unwrap() != "1" {
        return;
    }

    // 则写日志
    let mut file = OpenOptions::new().create(true).append(true).open("hxrdll.log").expect(
        "无法打开hxrdll.log，请尝试删除APP_LOG环境变量");

    // user_id转为字符串，如果不存在或未定义，则转字符串：无
    let user_id_str = user_id.to_string();

    let end_time = Utc::now();
    let fmt = "%Y-%m-%d %H:%M:%S%.3f%z";

    let end_time = end_time.format(fmt).to_string();

    let mut log_content = start_time.format(fmt).to_string();
    log_content = log_content + " " + ip_address + " " + path + " " + "user_id:" + user_id_str.as_str() + " " + end_time.as_str() + "\n";
    file.write_all(log_content.as_bytes()).expect("无法写入hxrdll.log，请尝试删除APP_LOG环境变量");
    
    return;
}

pub fn write_log(content: String) {
    // 配置环境变量APP_LOG=1才有日志
    let env_var = env::var("APP_LOG");
    if !env_var.is_ok() {
        return;
    }

    if env_var.ok().unwrap() != "1" {
        return;
    }

    // 则写日志
    let mut file = OpenOptions::new().create(true).append(true).open("hxrdll.log").expect(
        "无法打开hxrdll.log，请尝试删除HXR_PCLAB_TEST环境变量");
    // 自当前年度直到2020年
    let now = Utc::now();
    let fmt = "%Y-%m-%d %H:%M:%S";
    let mut log_content = now.format(fmt).to_string();
    log_content = log_content + " " + content.as_str() + "\n";
    file.write_all(log_content.as_bytes()).expect("无法写入hxrdll.log，请尝试删除HXR_PCLAB_TEST环境变量");
    
    return;
}

// 解压缩zip文件到指定目录
pub fn extract_zip(zip_path: &PathBuf, extract_path: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
    let file = fs::File::open(zip_path).unwrap();

    let mut archive = zip::ZipArchive::new(file).unwrap();

    for i in 0..archive.len() {
        let mut file = archive.by_index(i).unwrap();
        // let internal_path = decode(file.name_raw(), "utf-8")?;
        // let internal_path = file.name();
        let internal_path = string_from_utf8_lossy(file.name_raw());
        let outpath = Path::new(extract_path).join(internal_path.clone());

        // println!("internal_path: {}", &internal_path);
        // println!("extract_path: {}", extract_path.display());
        // println!("outpath: {}", outpath.display());

        // println!("{}", file.name());
        if (*file.name()).ends_with('/') {
            write_log(String::from("创建目录") + outpath.to_str().unwrap());
            fs::create_dir_all(&outpath).unwrap();
        } else {
            if let Some(p) = outpath.parent() {
                if !p.exists() {
                    write_log(String::from("创建目录") + p.to_str().unwrap());
                    fs::create_dir_all(&p).unwrap();
                }
            }
            write_log(String::from("创建文件") + outpath.to_str().unwrap());
            let mut outfile = fs::File::create(&outpath).unwrap();
            io::copy(&mut file, &mut outfile).unwrap();
        }

        // Get and Set permissions
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;

            if let Some(mode) = file.unix_mode() {
                fs::set_permissions(&outpath, fs::Permissions::from_mode(mode)).unwrap();
            }
        }
    }

    Ok(())
}

// 将指定路径的文件添加到压缩文件中的指定路径
pub fn add_file_to_zip(zip_path: &PathBuf, file_path: &PathBuf, file_in_zip_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 存在文件了，追加模式
    let append_mode = zip_path.exists();
    let zip_file = if append_mode {
        File::options().read(true).write(true).to_owned()
    }
    else {
        File::options().create(true).write(true).to_owned()
    };

    // 打开已经存在的ZIP文件
    let zip_file = match zip_file.open(zip_path) {
        Ok(r) => r,
        Err(e) => {
            let message = format!("尝试使用写入与添加模式打开ZIP文件 {} 时出错, {}", zip_path.to_str().unwrap(), e);
            let hxr_err = HxrError {
                message,
            };

            return Err(Box::new(hxr_err));
        }
    };
    
    // 打开已经存在的MP4文件
    let mut mp4_file = match File::open(file_path) {
        Ok(r) => r,
        Err(e) => {
            let message = format!("尝试使用只读模式打开MP4文件 {} 时出错, {}", zip_path.to_str().unwrap(), e);
            let hxr_err = HxrError {
                message: message
            };

            return Err(Box::new(hxr_err));
        }
    };

    // 将ZIP文件转换为写入器
    let mut zip = if append_mode {
        match zip::ZipWriter::new_append(zip_file)     {
            Ok(r) => r,
            Err(e) => {
                let message = format!("尝试使用ZIP写入工具添加模式打开ZIP文件 {} 时出错, {}", zip_path.to_str().unwrap(), e);
                let hxr_err = HxrError {
                    message: message
                };
    
                return Err(Box::new(hxr_err));
            }
        }
    } else {
        zip::ZipWriter::new(zip_file)
    };
    
    // MP4的话，就别进行实际的压缩了，文件存进去就好
    let options: zip::write::FileOptions<'_, _> = zip::write::SimpleFileOptions::default().compression_level(Some(0));

    // ZIP内部开启新文件
    match zip.start_file::<&str, _>(file_in_zip_path, options) {
        Ok(r) => r,
        Err(e) => {
            let message = format!("尝试在ZIP文件 {} 中添加文件 {} 时出错, {}", zip_path.to_str().unwrap(), file_in_zip_path, e);
            let hxr_err = HxrError {
                message: message
            };

            return Err(Box::new(hxr_err));
        }
    };

    // 将MP4文件写入ZIP新文件
    io::copy(&mut mp4_file, &mut zip).unwrap();

    // 完成ZIP文件
    match zip.finish() {
        Ok(r) => r,
        Err(e) => {
            let message = format!("完成ZIP文件时 {} 时出错, {}", zip_path.to_str().unwrap(), e);
            let hxr_err = HxrError {
                message: message
            };

            return Err(Box::new(hxr_err));
        }
    };

    Ok(())
}

// 获取EXE路径
pub fn get_path_in_exe_dir(sub_path: &str) -> PathBuf {
    let exe_dir = std::env::current_exe().unwrap();
    let current_path = exe_dir.parent().unwrap();
    current_path.join(sub_path)
}

// OI课程xml文件解析
pub fn oi_xml_analyze() {
    
}

// 文件内容处理
#[derive(Serialize,Debug,Clone)]
pub struct CodeResult {
    pub r#type: String,
    pub file_name: String,
    pub message: serde_json::Value,
}
pub async fn post_scratch_file(

    user_id: &i32,
    course_slug: &str,
    code_result: &CodeResult,
    chapter_name: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut content: Vec<u8> = Vec::new();
    let mut full_name: String = String::new();

    match code_result.r#type.as_str() {
        "text" => {
            content = code_result.message.to_string().into_bytes();
            full_name = format!("{}.txt", code_result.file_name);
        }
        "table" => {
            full_name = format!("{}.csv", code_result.file_name);
            if let Some(arr) = code_result.message.as_array() {
                let mut csv_list = vec![];
                for i_value in arr {
                    if let Some(inner_arr) = i_value.as_array() {
                        let grid: Vec<String> = inner_arr
                            .iter()
                            .filter_map(|j_value| j_value.get("value").and_then(|v| v.as_str()))
                            .map(|v| v.to_string())
                            .collect();
                        csv_list.push(grid.join(","));
                    }
                }
                content = format!("{}\n{}", "\u{FEFF}", csv_list.join("\n")).into_bytes();
            }
        }
        "mind" | "flow" | "networksimulator" | "spreadsheet" | "drawio" | "Scratch" | "MicroBit" => {
            content = serde_json::to_vec(&code_result.message)?;
            full_name = format!("{}.json", code_result.file_name);
        }
        _ => {}
    }

    // let path = if code_result.r#type == "Scratch" || code_result.r#type == "MicroBit" {
    //     format!("{}/{}/{}", course_slug, chapter_name, user_id)
    // } else {
    //     format!("{}/{}", course_slug, user_id)
    // };
    let path = format!("{}/{}/{}", course_slug, user_id, chapter_name);


    upload_student_file(&path, &full_name, &content).await?;

    Ok(())
}
// 文件内容处理
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct MicroAppCodeResult{
    pub file_name: String, 
    pub message: Value,
    pub source_params: Value,
    pub r#type: String,
}
// #[derive(Deserialize, Serialize,Debug,Clone)]
// #[serde(rename_all = "camelCase")]
// pub struct SourceParams{
//     pub uuid: String, 
//     pub file_name: String,
//     pub placeholder: String,
// }
pub async fn post_micro_app_file(

    user_id: &i32,
    course_slug: &str,
    code_result: &MicroAppCodeResult,
    chapter_name: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut content: Vec<u8> = Vec::new();
    // let mut table_content = Vec::new();
    let mut full_name: String = String::new();

    match code_result.r#type.as_str() {
        "text" => {
            content = code_result.message.to_string().into_bytes();
            full_name = format!("{}.txt", code_result.file_name);
        }
        "table" => {
            full_name = format!("{}.csv", code_result.file_name);
            
            let message = code_result.message.clone();

            let mut csv_list = Vec::new();

            let message: Option<Vec<Vec<Value>>> = serde_json::from_value(message).unwrap();
            // if let Some(message) = message {
            //     if !message.is_empty() {
            //         for i_value in message {
            //             if i_value.is_empty() {
            //                 continue;
            //             }
        
            //             let grid: Vec<String> = i_value.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect();
            //             csv_list.push(grid.join(","));
            //         }
            //     }
            // }
        
            // if !csv_list.is_empty() {
            //     table_content.push('\u{feff}'.to_string()); // BOM character
            //     table_content.push(csv_list.join("\n"));
            // }
            for i_value in message {
                if i_value.is_empty() {
                    continue;
                }
        
                for j_value in i_value {
                    let mut grid = Vec::new();

                    for value_object in j_value {
                        if let Some(value) = value_object.get("value") {
                            if let Some(value_str) = value.as_str() {
                                grid.push(value_str.to_string());
                            } else {
                                grid.push("".to_string());
                            }
                        } else {
                            grid.push("".to_string());
                        }
                    }
                    csv_list.push(grid.join(","));
                }
            
                
                println!("csv_list:{:?}", csv_list);
            }
        
            content = ("\u{feff}".to_string() + &csv_list.join("\n")).into();
            println!("res:{:?}", content);
        }
        "mind" | "flow" | "networksimulator" | "spreadsheet" | "drawio" | "Scratch" | "MicroBit" => {
            content = serde_json::to_vec(&code_result.message)?;
            full_name = format!("{}.json", code_result.file_name);
        }
        _ => {}
    }

    // let path = if code_result.r#type == "Scratch" || code_result.r#type == "MicroBit" {
    //     format!("{}/{}/{}", course_slug, user_id, chapter_name)
    // } else {
    //     format!("{}/{}", course_slug, user_id)
    // };
    let path = format!("{}/{}/{}", course_slug, user_id, chapter_name);

    // println!("table_content:{:?}", table_content);

    upload_student_file(&path, &full_name, &content).await?;

    Ok(())
}
// 上传文件内容
async fn upload_student_file(

    path: &str,
    file_name: &str,
    file_content: &[u8],
) -> std::io::Result<()> {
    let path_dir = get_path_in_exe_dir("static").join("student").join("course").join(path);

    if !path_dir.exists() {
        fs::create_dir_all(path_dir.clone())?;
    }
    tokio::fs::write(path_dir.join(file_name), file_content).await?;

    Ok(())
}

// 复制文件
#[async_recursion(?Send)]
pub async fn copy_recursively(source: &Path, destination: &Path) -> io::Result<()> {
    tokio::fs::create_dir_all(destination).await?;
    for entry in fs::read_dir(source)? {
        let entry = entry?;
        let filetype = entry.file_type()?;
        if filetype.is_dir() {
            copy_recursively(&entry.path(), &destination.join(entry.file_name())).await?;
        } else {
            tokio::fs::copy(entry.path(), destination.join(entry.file_name())).await?;
        }
    }
    Ok(())
}


