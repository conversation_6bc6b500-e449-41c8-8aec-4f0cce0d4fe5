use std::collections::{HashMap, HashSet};

use ::entity::{
    course::{self, CourseIndicsInfoItem, CourseInfoItem}, 
    section::{self, Entity as Section, SectionItem}, 
    section_record::{self, OnlySectionRecordItem, SectionRecordItem}, 
    system_config::{self, Entity as SystemConfig}, 
    team::{self, TeamItem, TeamUserShortRecord}, 
    team_user::{self, Entity as TeamUser, TeamToUserItem, TeamUserItem}, 
    terminal,
    user::{self, CourseUser, Entity as User, UserShortItem, UserShortRecord}
};
use prelude::Expr;
use sea_orm::*;
use serde::Serialize;
use serde_json::json;

use crate::HxrError;

pub struct Query;

#[derive(FromQueryResult)]
struct SystemConfigResult {
    key: String,
    value: Option<String>,
}

#[derive(Debug , Serialize)]
pub struct UserWithTeamInfo {
    pub user: user::Model,
    pub team_name: String,
    pub school_year: String,
}
#[derive(Debug, Serialize, FromQueryResult, Clone)]
pub struct RawUserRecord {
    pub user_id: i32,
    pub pass_count: i32,
    pub user_count: i64,
}
impl Query {
    // 根据索引键寻找系统配置
    pub async fn system_config_find_by_keys(db: &DbConn, keys: Vec<String>) -> Result<HashMap<String, Option<String>>, DbErr> {
        let key_value_models= SystemConfig::find()
                .select_only()
                .column(system_config::Column::Key)
                .column(system_config::Column::Value)
                .filter(Condition::all()
                    .add(system_config::Column::DeletedAt.is_null())
                    .add(system_config::Column::Key.is_in(keys))
                )
                .into_model::<SystemConfigResult>()
                .all(db)
                .await?;
        
        let mut key_value_map:HashMap<String, Option<String>> = HashMap::new();
        
        for key_value_model in key_value_models {
            key_value_map.insert( key_value_model.key, key_value_model.value);
        }

        Ok(key_value_map)
    }

    // 根据单一索引键寻找系统配置
    pub async fn system_config_find_by_key(txn: &DatabaseTransaction, key: &str) -> Result<Option<String>, DbErr> {
        let key_value_model = SystemConfig::find()
                .select_only()
                .column(system_config::Column::Key)
                .column(system_config::Column::Value)
                .filter(Condition::all()
                    .add(system_config::Column::DeletedAt.is_null())
                    .add(system_config::Column::Key.eq(key))
                )
                .into_model::<SystemConfigResult>()
                .one(txn)
                .await?;
        
        if key_value_model.is_none() {
            return Ok(Option::None);
        }

        Ok(key_value_model.unwrap().value)
    }

    // 根据单一索引键寻找系统配置
    pub async fn system_config_find_by_key_conn(txn: &DatabaseConnection, key: &str) -> Result<Option<String>, DbErr> {
        let key_value_model = SystemConfig::find()
                .select_only()
                .column(system_config::Column::Key)
                .column(system_config::Column::Value)
                .filter(Condition::all()
                    .add(system_config::Column::DeletedAt.is_null())
                    .add(system_config::Column::Key.eq(key))
                )
                .into_model::<SystemConfigResult>()
                .one(txn)
                .await?;
        
        if key_value_model.is_none() {
            return Ok(Option::None);
        }

        Ok(key_value_model.unwrap().value)
    }

    // 获取全部系统配置
    pub async fn system_config_find_all(txn: &DatabaseTransaction) -> Result<HashMap<String, Option<String>>, DbErr> {
        let key_value_models= SystemConfig::find()
                .select_only()
                .column(system_config::Column::Key)
                .column(system_config::Column::Value)
                .filter(Condition::all()
                    .add(system_config::Column::DeletedAt.is_null())
                )
                .into_model::<SystemConfigResult>()
                .all(txn)
                .await?;
        
        let mut key_value_map:HashMap<String, Option<String>> = HashMap::new();
        
        for key_value_model in key_value_models {
            key_value_map.insert( key_value_model.key, key_value_model.value);
        }

        Ok(key_value_map)
    }

    // 根据账号寻找用户
    pub async fn user_find_by_username(db: &DatabaseTransaction, username: String) -> Result<Option<user::Model>, DbErr> {
        let u = User::find()
            .filter(
                Condition::all()
                .add(
                    Condition::any()
                    .add(user::Column::State.is_null())
                    .add(user::Column::State.eq("open"))
                )
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::Username.eq(username))
            )
            .into_model::<user::Model>()
            .one(db)
            .await?;
        
        Ok(u)
    }

    // 根据ID寻找用户
    pub async fn user_find_by_id(db: &DatabaseTransaction, user_id: i32) -> Result<Option<user::Model>, DbErr> {
        let u = User::find_by_id(user_id)
            .filter(
                Condition::all()
                .add(
                    Condition::any()
                    .add(user::Column::State.is_null())
                    .add(user::Column::State.eq("open"))
                )
                .add(user::Column::DeletedAt.is_null())
            )
            .into_model::<user::Model>()
            .one(db)
            .await?;
        
        Ok(u)
    }

    // 根据学年寻找班级
    pub async fn team_find_by_schoolyear(db: &DbConn, school_year: String) -> Result<Vec<TeamUserShortRecord>, DbErr> {
        let results = team::Entity::find()
            .from_raw_sql(Statement::from_sql_and_values(
                DbBackend::Sqlite,
                r#"select id, name, (select count(user_id) from team_user tu where team_id = t.id and tu.deleted_at is null) as user_count from team t where t.deleted_at is null and t.year = $1"#,
                vec![school_year.into()],
            ))
            .into_model::<TeamUserShortRecord>()
            .all(db)
            .await?;

        Ok(results)
    }

    // 根据班级ID加载学生
    pub async fn users_find_by_team_id(db: &DbConn, team_id: i32) -> Result<Vec<UserShortRecord>, DbErr> {
        let results = team::Entity::find()
            .from_raw_sql(Statement::from_sql_and_values(
                DbBackend::Sqlite,
                r#"select id, username, name, sen from user where deleted_at is null and id in (select user_id from team_user where team_id = $1 and deleted_at is null)"#,
                vec![team_id.into()],
            ))
            .into_model::<UserShortRecord>()
            .all(db)
            .await?;

        Ok(results)
    }
    // 根据班级ID加载学生
    pub async fn users_list_find_by_team_id(txn: &DatabaseTransaction, team_id: i32) -> Result<Vec<UserShortRecord>, DbErr> {
        let user_ids: Vec<i32> = TeamUser::find()
        .select_only()
        .column(team_user::Column::UserId)
        .filter(team_user::Column::TeamId.eq(team_id))
        .into_tuple::<i32>()
        .all(txn)
        .await?;
        Ok(
            User::find()
            .select_only()
            .column(user::Column::Id)
            .column(user::Column::Username)
            .column(user::Column::Name)
            .column(user::Column::Sen)
            .filter(
                Condition::all()
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::Id.is_in(user_ids))
            )
            .into_model::<UserShortRecord>()
            .all(txn)
            .await?
        )
    }

    // 根据班级ID加载学生
    pub async fn team_user_find_by_team_id(txn: &DatabaseTransaction, team_id: i32) -> Result<Vec<TeamUserItem>, DbErr> {
        Ok(
            TeamUser::find()
            .select_only()
            .column(team_user::Column::Id)
            .column(team_user::Column::UserId)
            .column(team_user::Column::TeamId)
            .filter(
                Condition::all()
                .add(team_user::Column::DeletedAt.is_null())
                .add(team_user::Column::TeamId.eq(team_id))
            )
            .into_model::<TeamUserItem>()
            .all(txn)
            .await?
        )
    }
    // 根据班级ID加载学生ID
    pub async fn user_id_find_by_team_ids(txn: &DatabaseTransaction, team_id: i32) -> Result<Vec<i32>, DbErr> {
        Ok(
            TeamUser::find()
            .select_only()
            .column(team_user::Column::Id)
            .column(team_user::Column::UserId)
            .column(team_user::Column::TeamId)
            .filter(
                Condition::all()
                .add(team_user::Column::DeletedAt.is_null())
                .add(team_user::Column::TeamId.eq(team_id))
            )
            .into_model::<TeamUserItem>()
            .all(txn)
            .await?
            .into_iter()
            .map(|item| item.user_id)
            .collect()
        )
    }
    // 根据学生ID加载学生
    pub async fn users_find_by_user_ids(txn: &DatabaseTransaction, user_ids: Vec<i32>) -> Result<Vec<UserShortItem>, DbErr> {
        Ok(
            User::find()
            .select_only()
            .column(user::Column::Id)
            .column(user::Column::Username)
            .column(user::Column::Name)
            .filter(
                Condition::all()
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::Id.is_in(user_ids))
            )
            .into_model::<UserShortItem>()
            .all(txn)
            .await?
        )
    }
    // 批量获取用户章节记录

    pub async fn get_user_records(
        txn: &DatabaseTransaction,
        user_ids: Vec<i32>,
        section_ids: Vec<i32>
    ) -> Result<Vec<RawUserRecord>, DbErr> {
        let user_records = section_record::Entity::find()
            .filter(section_record::Column::UserId.is_in(user_ids))
            .filter(section_record::Column::SectionId.is_in(section_ids))
            .filter(
                section_record::Column::TotalScore
                    .ne(0)
                    .and(Expr::col(section_record::Column::TotalScore).eq(Expr::col(section_record::Column::PassCount))),
                )
            .select_only()
            .column(section_record::Column::UserId)
            .column(section_record::Column::PassCount)
            .column_as(Expr::col(section_record::Column::UserId).count(), "user_count")
            .group_by(section_record::Column::UserId)
            .into_model::<RawUserRecord>()
            .all(txn)
            .await?;

        Ok(user_records)
    }
    pub async fn get_users_records_all(
        txn: &DatabaseTransaction,
        user_ids: Vec<i32>,
        section_ids: Vec<i32>
    ) -> Result<Vec<SectionRecordItem>, DbErr> {
        let user_records = section_record::Entity::find()
            .filter(section_record::Column::UserId.is_in(user_ids))
            .filter(section_record::Column::SectionId.is_in(section_ids))
            .select_only()
            .columns([
                section_record::Column::UserId,
                section_record::Column::SectionId,
                section_record::Column::Record,
                section_record::Column::TotalScore,
                section_record::Column::PassCount,
                section_record::Column::CreatedAt,
                section_record::Column::UpdatedAt
            ])
            .into_model::<SectionRecordItem>()
            .all(txn)
            .await?;

        Ok(user_records)
    }
    // 获取单个用户指定章节记录
    pub async fn get_user_record(
        txn: &DatabaseTransaction,
        user_id: i32,
        section_id: i32
    ) -> Result<Option<OnlySectionRecordItem>, DbErr> {
        let user_record = section_record::Entity::find()
            .filter(section_record::Column::UserId.eq(user_id))
            .filter(section_record::Column::SectionId.eq(section_id))
            .select_only()
            .column(section_record::Column::Record)
            .into_model::<OnlySectionRecordItem>()
            .one(txn)
            .await?;

        Ok(user_record)
    }

    // 根据学生ID列表，查看不在当前班级中存在的学生ID
    pub async fn team_user_find_by_user_ids_and_not_in_team_id(txn: &DatabaseTransaction, user_ids: Vec<i32>, team_id: i32) -> Result<Vec<TeamUserItem>, DbErr> {
        Ok(
            TeamUser::find()
            .select_only()
            .column(team_user::Column::Id)
            .column(team_user::Column::UserId)
            .column(team_user::Column::TeamId)
            .filter(
                Condition::all()
                .add(team_user::Column::DeletedAt.is_null())
                .add(team_user::Column::TeamId.ne(team_id))
                .add(team_user::Column::UserId.is_in(user_ids))
            )
            .into_model::<TeamUserItem>()
            .all(txn)
            .await?
        )
    }
    // 获取节内容
    pub async fn get_section(txn: &DatabaseTransaction, course_slug: String, chapter_name: String, section_name: String) -> Result<Option<SectionItem>, DbErr> {
        Ok(
            Section::find()
            .select_only()
            .column(section::Column::Id)
            .column(section::Column::CourseId)
            .column(section::Column::ChapterName)
            .column(section::Column::SectionName)
            .column(section::Column::SectionType)
            .column(section::Column::Ext)
            .column(section::Column::CreatedAt)
            .filter(
                Condition::all()
                .add(section::Column::DeletedAt.is_null())
                .add(section::Column::CourseId.eq(course_slug))
                .add(section::Column::ChapterName.eq(chapter_name))
                .add(section::Column::SectionName.eq(section_name))
            )
            .into_model::<SectionItem>()
            .one(txn)
            .await?
        )
    }
    // 根据章获取所有节
    pub async fn get_section_list_by_chapter(txn: &DatabaseTransaction, course_slug: String, chapter_name: String) -> Result<Vec<serde_json::Value>, DbErr> {
        // 查询课程缓存
        let course:Option<CourseIndicsInfoItem> = course::Entity::find()
            .filter(course::Column::CourseSlug.eq(course_slug))
            .select_only()
            .columns([
                course::Column::Id,
                course::Column::Indics
            ])
            .into_model::<CourseIndicsInfoItem>()
            .one(txn)
            .await?;

        if let Some(course) = course {
            let indics: Vec<serde_json::Value> = serde_json::from_value(course.indics.unwrap()).unwrap();

            // 从课程缓存中查询当前课节信息
            if let Some(current_chapter) = indics.iter().find(|chapter| chapter["chapterName"].as_str() == Some(&chapter_name)) {
                // 获取当前章所有的节列表
                if let Some(sections) = current_chapter.get("sections") {
                    return Ok(sections.as_array().unwrap_or(&Vec::new()).to_vec());
                }
            }

            return Err(DbErr::Custom(format!("未查询到章节{}", chapter_name)));
        } else {
            return Err(DbErr::Custom("目前查找不到相关课程".to_string()));
        }
    }


    // 根据用户ID集合获取对应的班级ID关系
    pub async fn team_user_by_user_ids(txn: &DatabaseTransaction, user_ids: Vec<i32>) -> Result<Vec<TeamUserItem>, DbErr> {
        Ok(
            team_user::Entity::find()
            .select_only()
            .column(team_user::Column::Id)
            .column(team_user::Column::UserId)
            .column(team_user::Column::TeamId)
            .filter(Condition::all()
                .add(team_user::Column::DeletedAt.is_null())
                .add(team_user::Column::UserId.is_in(user_ids))
            )
            .into_model::<TeamUserItem>()
            .all(txn)
            .await?
        )
    }
        // 根据用户ID获取对应的班级ID关系
        pub async fn team_user_by_user_id(txn: &DatabaseTransaction, user_id: i32) -> Result<Option<TeamUserItem>, DbErr> {
            Ok(
                team_user::Entity::find()
                .select_only()
                .column(team_user::Column::Id)
                .column(team_user::Column::UserId)
                .column(team_user::Column::TeamId)
                .filter(Condition::all()
                    .add(team_user::Column::DeletedAt.is_null())
                    .add(team_user::Column::UserId.eq(user_id))
                )
                .into_model::<TeamUserItem>()
                .one(txn)
                .await?
            )
        }

    // 根据用户ID集合获取对应的班级ID关系
    pub async fn all_team_find(txn: &DatabaseTransaction) -> Result<Vec<TeamItem>, DbErr> {
        Ok(
            team::Entity::find()
            .select_only()
            .column(team::Column::Id)
            .column(team::Column::Name)
            .filter(Condition::all()
                .add(team::Column::DeletedAt.is_null())
            )
            .into_model::<TeamItem>()
            .all(txn)
            .await?
        )
    }
    // 根据班级ID和章节id获取对应的课程记录
    pub async fn section_record_find_by_course_id_and_section_id(txn: &DatabaseTransaction, team_id: i32, section_id: i32) -> Result<(Vec<SectionRecordItem>,Vec<i32>), DbErr>{
        let students = team_user::Entity::find()
            .select_only()
            .column(team_user::Column::UserId)
            .filter(Condition::all()
                .add(team_user::Column::DeletedAt.is_null())
                .add(team_user::Column::TeamId.eq(team_id))
            )
            .into_model::<TeamToUserItem>()
            .all(txn)
            .await?;
        let students_ids: Vec<i32> = students.into_iter().map(|student|student.user_id).collect();
        let results = section_record::Entity::find()
            .select_only()
            .column(section_record::Column::UserId)
            .column(section_record::Column::SectionId)
            .column(section_record::Column::Record)
            .column(section_record::Column::TotalScore)
            .column(section_record::Column::PassCount)
            .column(section_record::Column::CreatedAt)
            .column(section_record::Column::UpdatedAt)
            .filter(Condition::all()
                .add(section_record::Column::DeletedAt.is_null())
                .add(section_record::Column::UserId.is_in(students_ids.clone()))
                .add(section_record::Column::SectionId.eq(section_id))
            )
            .into_model::<SectionRecordItem>()
            .all(txn)
            .await?;
        Ok((results, students_ids))
    }

    
    // 根据班级ID和章节id获取对应的课程记录
    pub async fn user_record_find_by_section_id_and_user_id(txn: &DatabaseTransaction, user_id: i32, section_id: i32) -> Result<Option<SectionRecordItem>, DbErr>{

        let results = section_record::Entity::find()
            .select_only()
            .column(section_record::Column::UserId)
            .column(section_record::Column::SectionId)
            .column(section_record::Column::Record)
            .column(section_record::Column::TotalScore)
            .column(section_record::Column::PassCount)
            .column(section_record::Column::CreatedAt)
            .column(section_record::Column::UpdatedAt)
            .filter(Condition::all()
                .add(section_record::Column::DeletedAt.is_null())
                .add(section_record::Column::UserId.eq(user_id))
                .add(section_record::Column::SectionId.eq(section_id))
            )
            .into_model::<SectionRecordItem>()
            .one(txn)
            .await?;
        Ok(results)
    }
    // 根据用户id批量获取课程记录
    pub async fn get_section_record_find_by_user_ids_and_section_id(txn: &DatabaseTransaction, user_ids: Vec<i32>, section_id: i32) -> Result<Vec<SectionRecordItem>, DbErr> {
        let results = section_record::Entity::find()
            .select_only()
            .column(section_record::Column::UserId)
            .column(section_record::Column::SectionId)
            .column(section_record::Column::Record)
            .column(section_record::Column::TotalScore)
            .column(section_record::Column::PassCount)
            .column(section_record::Column::CreatedAt)
            .column(section_record::Column::UpdatedAt)
            .filter(Condition::all()
                .add(section_record::Column::DeletedAt.is_null())
                .add(section_record::Column::UserId.is_in(user_ids))
                .add(section_record::Column::SectionId.eq(section_id))
            )
            .into_model::<SectionRecordItem>()
            .all(txn)
            .await?;
        Ok(results)
    }
    // 根据用户id批量获取课程记录
    pub async fn get_section_record_find_by_user_ids_and_section_ids(txn: &DatabaseTransaction, user_ids: Vec<i32>, section_ids: Vec<i32>) -> Result<Vec<SectionRecordItem>, DbErr> {
        let results = section_record::Entity::find()
            .select_only()
            .column(section_record::Column::UserId)
            .column(section_record::Column::SectionId)
            .column(section_record::Column::Record)
            .column(section_record::Column::TotalScore)
            .column(section_record::Column::PassCount)
            .column(section_record::Column::CreatedAt)
            .column(section_record::Column::UpdatedAt)
            .filter(Condition::all()
                .add(section_record::Column::DeletedAt.is_null())
                .add(section_record::Column::UserId.is_in(user_ids))
                .add(section_record::Column::SectionId.is_in(section_ids))
            )
            .into_model::<SectionRecordItem>()
            .all(txn)
            .await?;
        Ok(results)
    }
    // 根据班级ID查询班级名称
    pub async fn team_by_team_ids(txn: &DatabaseTransaction, team_ids: Vec<i32>) -> Result<Vec<TeamItem>, DbErr> {
        Ok(
            team::Entity::find()
            .select_only()
            .column(team::Column::Id)
            .column(team::Column::Name)
            .filter(Condition::all()
                .add(team::Column::DeletedAt.is_null())
                .add(team::Column::Id.is_in(team_ids))
            )
            .into_model::<TeamItem>()
            .all(txn)
            .await?
        )
    }

    // 获取终端设备列表
    pub async fn get_terminal_from_teacher(db: &DbConn) -> Result<Vec<JsonValue>, DbErr> {
        // 查询终端设备、用户名、班级、试卷名称，班级通过班级用户bridge表查询

        // 查询终端设备、用户名、班级、试卷名称
        let results = terminal::Entity::find()
            .from_raw_sql(Statement::from_sql_and_values(
                DbBackend::Sqlite,
                r#"select terminal.id, terminal.ip, terminal.user_id, terminal.train_id, terminal.status, terminal.created_at, user.username as user_name, user.name as user_nickname, team.name as team_name, train.name as train_name from terminal left join user on terminal.user_id = user.id left join team_user on terminal.user_id = team_user.user_id left join team on team_user.team_id = team.id left join train on terminal.train_id = train.id where terminal.deleted_at is null"#,
                vec![],
            ))
            .into_json()
            .all(db)
            .await?;
      
        Ok(results)
    }

    // 根据IP地址获取终端设备
    pub async fn find_terminal_by_ip(db: &DatabaseTransaction, ip: &str) -> Result<Option<terminal::Model>, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Ip.eq(ip))
            )
            .one(db)
            .await?;
      
        Ok(result)
    }
    // 根据IP地址获取终端设备
    pub async fn find_terminal_by_ips(db: &DatabaseTransaction, ips: Vec<String>) -> Result<Vec<terminal::Model>, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Ip.is_in(ips))
            )
            .all(db)
            .await?;
      
        Ok(result)
    }
    // 获取在线设备数量
    pub async fn get_online_count(db: &DatabaseTransaction) -> Result<usize, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Status.eq("在线"))
            )
            .all(db)
            .await?;
        let count = result.len();
        Ok(count)
    }
    // 获取所有在线设备的ip
    pub async fn get_online_ips(db: &DatabaseTransaction) -> Result<Vec<String>, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Status.eq("在线"))
            )
            .all(db)
            .await?;
        let ips = result.iter().map(|t| t.ip.clone()).collect::<Vec<_>>();
        Ok(ips)
    }
    // 获取所有在线设备
    pub async fn get_online_ips_info(db: &DatabaseTransaction) -> Result<Vec<terminal::Model>, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                // .add(terminal::Column::Status.eq("在线"))
            )
            .all(db)
            .await?;
        Ok(result)
    }
    // 根据IP地址获取终端设备
    pub async fn find_terminal_not_online_by_ips(db: &DatabaseTransaction, ips: Vec<String>) -> Result<Vec<terminal::Model>, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Ip.is_not_in(ips))
            )
            .all(db)
            .await?;
      
        Ok(result)
    }
    // 通过用户id查找终端设备
    pub async fn find_terminal_by_user_id(db: &DatabaseTransaction, user_id: i32) -> Result<Option<terminal::Model>, DbErr> {
        let result = terminal::Entity::find()
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::UserId.eq(user_id))
            )
            .one(db)
            .await?;
      
        Ok(result)
    }
    // 获取section_id
    pub async fn get_section_id(db: &DatabaseTransaction, course_id: String, chapter_name: String, section_name: String,) -> Result<i32, DbErr> {
        let result = section::Entity::find()
            .filter(
                Condition::all()
                .add(section::Column::CourseId.eq(course_id))
                .add(section::Column::ChapterName.eq(chapter_name))
                .add(section::Column::SectionName.eq(section_name))
                .add(section::Column::DeletedAt.is_null())
            )
            .into_model::<SectionItem>()
            .one(db)
            .await?;
        let section_id = match result {
            Some(section_item) => section_item.id,
            None => return Err(DbErr::Custom("查找不到相关节".to_string())),
        };
        Ok(section_id)
    }
    // 获取课程进度
    pub async fn get_progress_by_user(txn: &DatabaseTransaction,user_id: i32, section_id: i32) -> Result<Option<serde_json::Value>, DbErr> {
        let result: Option<serde_json::Value> = section_record::Entity::find()
            .filter(
                Condition::all()
                .add(section_record::Column::UserId.eq(user_id))
                .add(section_record::Column::SectionId.eq(section_id))
                .add(section_record::Column::DeletedAt.is_null())
            )
            .select_only()
            .columns([
                section_record::Column::SectionId, 
                section_record::Column::Record, 
                section_record::Column::PassCount,
                section_record::Column::TotalScore,
            ])
            .into_json()
            .one(txn)
            .await?;


        // 解析Record字段为json Value
        let result = match result {
            Some(record) => {
                let record_str = record["record"].as_str().unwrap();
                let record_json: JsonValue = serde_json::from_str(record_str).unwrap();

                // 构造Value，放入SectionId，PassCount和解开的Record
                let record_full = json!({
                    "section_id": record["section_id"],
                    "pass_count": record["pass_count"],
                    "total_score": record["total_score"],
                    "record": record_json
                });

                Some(record_full)
            },
            None => None
        };

        Ok(result)
    }

    // pub async fn query_course_list(txn: &DatabaseTransaction,user_id: Option<i32>,is_teacher:bool) -> Result<Option<serde_json::Value>, DbErr> {
    //     let course_list = course::Entity::find()
    //         .from_raw_sql(
    //             Statement::from_sql_and_values(
    //             DbBackend::Sqlite,
    //             r#"SELECT course.*, user.name, avatar
    //             FROM course 
    //             LEFT JOIN user ON course.creater_id = user.id 
    //             WHERE course.publish = 1
	// 			AND course.deleted_at IS NULL"#,
    //             vec![user_id.into()],
    //         ))
    //         .into_model::<course::Model>()
    //         .all(txn)
    //         .await?;

    //     let user_teams = team_user::Entity::find()
    //         .filter(team_user::Column::UserId.eq(user_id))
    //         .select_only()
    //         .columns([
    //             team_user::Column::TeamId
    //         ])
    //         .into_json()
    //         .one(txn)
    //         .await?;

    //     // 遍历user_teams中的每个元素
    //     let mut team_res: Vec<JsonValue> = vec![];
    //     for team in user_teams.iter() {
    //         team_res.push(team["team_id"].clone());
    //     }

    //     println!("course: {:?}", course_list);
    //     println!("teams: {:?}", user_teams);
    //     println!("isteacher: {}", is_teacher);

    //     // 将user_id转为i32类型
    //     let user_id = user_id.unwrap_or(0);
    //     // 过滤符合条件的信息
    //     // let mut _res;
    //     // 打印user_id
    //     println!("user_id: {}", user_id.to_string());
    //     // _res = course_list.into_iter().filter(|e| 
    //     //     e.creater_id == user_id 
    //     //     || 
    //     //     (
    //     //         e.teams.as_ref()
    //     //         .and_then(|teams| {
    //     //             // 尝试将teachers JSON值解析为Vec<String>
    //     //             serde_json::from_value::<Vec<JsonValue>>(teams.clone()).ok()
    //     //         })
    //     //         .map_or(false, |teams| 
    //     //             team_res.clone().iter()
    //     //             .any(|team| teams.contains(&team))
    //     //     ) 
    //     //     ||
    //     //         (
    //     //             (e.teachers.is_none()||
    //     //             e.teachers.as_ref()
    //     //             .and_then(|teachers| {
    //     //                 // 尝试将teachers JSON值解析为Vec<String>
    //     //                 serde_json::from_value::<Vec<String>>(teachers.clone()).ok()
    //     //             }).map_or(false, |teachers| 
    //     //                 teachers.is_empty())
    //     //             )
    //     //             &&is_teacher
    //     //         )
    //     //     ||
    //     //     e.teachers.as_ref()
    //     //     .and_then(|teachers| {
    //     //         // 尝试将teachers JSON值解析为Vec<String>
    //     //         serde_json::from_value::<Vec<JsonValue>>(teachers.clone()).ok()
    //     //     })
    //     //     .map_or(false, |teachers| 

    //     //             teachers.contains(&JsonValue::from(user_id))
    //     //         ))
    //     // ).collect::<Vec<_>>(); 
        
    //     // 将res转换为json格式
    //     let res = serde_json::to_value(course_list).unwrap();
    //     println!("res: {}", res);

    //     // 返回查询到的slug字段
    //     Ok(Some(res))
    // }

    // 校验用户对于课节的权限，如果无权限，抛出异常告知原因，如果有权限给出课程配置
    pub async fn check_user_permission_get_config(txn: &DatabaseTransaction,user_id: i32, team_ids: Vec<JsonValue>, course_slug: String, chapter_name: String, section_name: String) -> Result<Option<serde_json::Value>,  Box<dyn std::error::Error>> {
        let course = course::Entity::find()
            .filter(course::Column::CourseSlug.eq(course_slug))
            .select_only()
            .into_json()
            .one(txn)
            .await?;
        if let Some(course) = course.clone() {
            // 获取当前章节
            if let Some(value) = course["indics"].get("chapter") {
                if value["chapter_name"] != chapter_name {
                    
                    let message = format!("本章 {} 不存在，可能老师已经修改，请尝试返回课程列表页面！", chapter_name);
                    let hxr_err = HxrError {
                        message: message
                    };
                    return Err(Box::new(hxr_err));
                
                 }
            }
            if let Some(value) = course["indics"].get("section") {
                if value["section_name"] != section_name {
                    let message = format!("本课程 {} 不存在，可能老师已经修改，请尝试返回课程列表页面！", section_name);
                    let hxr_err = HxrError {
                        message: message
                    };
                    return Err(Box::new(hxr_err));
                
                 }
            }
            
            if let Some(teachers) = serde_json::from_value::<Vec<JsonValue>>(course["teachers"].clone()).ok(){
                // 判断是否为本课程老师
                let mut vec_teachers = teachers;
                vec_teachers.push(course["creater_id"].clone());
                if user_id == -1 {
                    vec_teachers.push(JsonValue::from(user_id.clone()));                  
                }
                if !vec_teachers.contains(&JsonValue::from(user_id)) {
                    if let Some(publish) = course["publish"].as_bool(){
                        if !publish {
                            let message = format!("课程尚未开放，请联系老师！");
                            let hxr_err = HxrError {
                                message: message
                            };
                            return Err(Box::new(hxr_err));
                        }
                    }
                    // 课程尚未向班级开放，禁止提交
                    if let Some(teams) = serde_json::from_value::<Vec<JsonValue>>(course["teams"].clone()).ok() {
                        if !team_ids.iter().any(|team| teams.contains(&team)){
                            let message = format!("课程尚未向班级开放，请联系老师！");
                            let hxr_err = HxrError {
                                message: message
                            };
                            return Err(Box::new(hxr_err));
                       }
                    }
                    // 初始化章节数据
                    let indics_info = course["indics"].clone();
                    // 章尚未向班级开放
                    // 暂不实现
                    // 节尚未向班级开放
                    // 暂不实现
                }
                
                
            };
            

            
        }
        
        Ok(course)
    }
    // 根据slug读取课程列表
    pub async fn course_find_by_slug_json(txn: &DatabaseTransaction, course_slug: String) -> Result<Option<serde_json::Value>, DbErr> {
        let result = course::Entity::find()
            .select_only()
            .columns([
                course::Column::Creater,
                course::Column::CourseType,
            ])
            .filter(Condition::all()
            .add(course::Column::DeletedAt.is_null())
            .add(course::Column::CourseSlug.eq(course_slug)))
            .into_json()
            .one(txn)
            .await?;
    
        Ok(result)
    }
        // 根据slug读取indics
        pub async fn course_indics_find_by_slug(txn: &DatabaseTransaction, course_slug: String) -> Result<Option<serde_json::Value>, DbErr> {
            let result = course::Entity::find()
                .select_only()
                .columns([
                    course::Column::Indics,
                ])
                .filter(Condition::all()
                .add(course::Column::DeletedAt.is_null())
                .add(course::Column::CourseSlug.eq(course_slug)))
                .into_json()
                .one(txn)
                .await?;
                    
            Ok(result)
        }
    pub async fn query_course_list(txn: &DatabaseTransaction) -> Result<Vec<serde_json::Value>, DbErr> {
        let result = course::Entity::find()
            .filter(Condition::all()
            .add(course::Column::DeletedAt.is_null()))
            .into_json()
            .all(txn)
            .await?;
    
        Ok(result)
    }
    // 
    pub async fn find_course_list(txn: &DatabaseTransaction) -> Result<Vec<CourseInfoItem>, DbErr> {
        let result: Vec<CourseInfoItem> = course::Entity::find()
            .filter(Condition::all()
            .add(course::Column::DeletedAt.is_null()))
            .into_model::<CourseInfoItem>()
            .all(txn)
            .await?;
    
        Ok(result)
    }
    // // 根据slug读取container_info
    // pub async fn course_container_info_find_by_slug_json(txn: &DatabaseTransaction, course_slug: String) -> Result<Option<serde_json::Value>, DbErr> {
    //     let result = course::Entity::find()
    //         .select_only()
    //         .columns([
    //             course::Column::ContainerInfo,
    //         ])
    //         .filter(Condition::all()
    //         .add(course::Column::DeletedAt.is_null())
    //         .add(course::Column::CourseSlug.eq(course_slug)))
    //         .into_json()
    //         .one(txn)
    //         .await?;
    
    //     Ok(result)
    // }
    // 根据slug读取课程信息
    pub async fn course_info_find_by_slug_json(txn: &DatabaseTransaction, course_slug: String) -> Result<Option<CourseInfoItem>, DbErr> {
        let result = course::Entity::find()
            .select_only()
            .columns([
                course::Column::Id,
                course::Column::SaveCode,
                course::Column::SaveRunResult,
                course::Column::ContainerInfo,
                course::Column::CourseSlug,
            ])
            .filter(Condition::all()
            .add(course::Column::DeletedAt.is_null())
            .add(course::Column::CourseSlug.eq(course_slug)))
            .into_model::<CourseInfoItem>()
            .one(txn)
            .await?;
    
        Ok(result)
    }
    // 根据用户id读取用户名及头像
    // pub async fn query_user_by_id(txn: &DatabaseTransaction, creater_id: i32) -> Result<Option<serde_json::Value>, DbErr> {
    //     let result: Option<serde_json::Value> = user::Entity::find()
    //         .filter(
    //             Condition::all()
    //             .add(user::Column::Id.eq(creater_id))
    //             .add(user::Column::DeletedAt.is_null())
    //         )
    //         .select_only()
    //         .columns([
    //             user::Column::Name,  
    //             user::Column::Avatar
    //         ])
    //         .into_json()
    //         .one(txn)
    //         .await?;

    //     Ok(result)
    // }
    // 根据课程slug读取对应indics
    pub async fn query_indics_by_slug(txn: &DatabaseTransaction, course_slug:String) -> Result<Option<serde_json::Value>, DbErr>{
        let result: Option<serde_json::Value> = course::Entity::find()
            .filter(
                Condition::all()
                .add(course::Column::CourseSlug.eq(course_slug))
            )
            .select_only()
            .columns([
                course::Column::Indics,  
            ])
            .into_json()
            .one(txn)
            .await?;
        // 解析Indics字段为json Value
        let result = match result {
            Some(result) => {
                let result_str = result["indics"].as_str().unwrap();
                let result_json: JsonValue = serde_json::from_str(result_str).unwrap();

                // 构造Value，放入SectionId，PassCount和解开的result
                let result_full = json!({
                    "indics": result_json
                });

                Some(result_full)
            },
            None => None
        };
        Ok(result)
    }

    
    // 根据ID寻找用户及其班级学年
    pub async fn user_team_find_by_id(db: &DatabaseTransaction, check_team_ids: String,  user_id: i32) -> Result<Option<UserWithTeamInfo>, DbErr> {
        let check_team_ids: Vec<i32> = check_team_ids.split(",").map(|s| s.parse::<i32>().unwrap()).collect();
        let u = User::find_by_id(user_id)
            .filter(
                Condition::all()
                .add(
                    Condition::any()
                    .add(user::Column::State.is_null())
                    .add(user::Column::State.eq("open"))
                )
                .add(user::Column::DeletedAt.is_null())
            )
            .find_with_related(TeamUser)
            .all(db)
            .await?;
        
        let mut result = None;
        if let Some((user, team_users)) = u.into_iter().next() {
            let team_ids: Vec<i32> = team_users.iter().map(|tu| tu.team_id).collect();
    
            let teams = team::Entity::find()
                .filter(team::Column::Id.is_in(team_ids))
                .all(db)
                .await?;
    
            // if let Some(team) = teams.into_iter().next() {
            //     result = Some(UserWithTeamInfo {
            //         user,
            //         team_name: team.name,
            //         school_year: team.year,
            //     });
            // }
            for team in teams {
                if !check_team_ids.contains(&team.id) {
                    result = Some(UserWithTeamInfo {
                        user,
                        team_name: team.name,
                        school_year: team.year,
                    });
                    break;
                }
            }
        }
        
        Ok(result)
    }
}

