import tkinter
root=tkinter.Tk()
root.geometry('910x610')#设置窗口大小
root.resizable(width=True,height=True)#可否调整窗口的大小
root.title("欢迎进入选课系统")#窗口的标题
root.config(bg="red")

label1=tkinter.Label(root,text="账号：")
label1.grid(row=0,column=0)

entry1=tkinter.Entry(root)
entry1.grid(row=0,column=1)

def changelabeltext():
    s=entry1.get()
    label1.config(text=s)
    
btn1=tkinter.Button(root,text="确定",command=changelabeltext)
btn1.grid(row=1,columnspan=2)
root.mainloop()


