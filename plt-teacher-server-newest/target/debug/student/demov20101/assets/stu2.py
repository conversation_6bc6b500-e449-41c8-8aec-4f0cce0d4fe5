import tkinter ,pyodbc, os
from tkinter import *
from tkinter import messagebox

root=tkinter.Tk()
root.geometry('910x610')#设置窗口大小
root.resizable(width=False,height=False)#可否调整窗口的大小
root.title("欢迎进入选课系统")#窗口的标题

DBfile =DBfile = os.getcwd()+u'''\info.accdb''' # 数据库文件需要带路径
conn = pyodbc.connect(r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ="+ DBfile +";Uid=;Pwd=;") 
cursor = conn.cursor()

def student(userID):#学生功能
    pass
        
def teacher(userID):#教师功能
    pass 
    
def check():#核验身份
    userID=entry1.get()
    userpwd=entry2.get()
    params = (userID)
    SQL ='''SELECT * FROM 用户信息表 WHERE 用户信息表.userID=?;'''
    rs=cursor.execute(SQL,params)#需要用传递参数的方式进行
    user=rs.fetchone()#游标的fetch函数
    if user and user[2]==userpwd:#用户存在且密码正确
        if user[3]==0:#用户的角色是学生
            label3.config(text="欢迎"+user[1]+"同学!")
            student(userID)
        else:
            label3.config(text="欢迎"+user[1]+"老师!")
            teacher(userID)
    else:
        messagebox.showinfo(title='提示', message='用户密码错误，请重新输入')
        entry1.delete(0,len(userID))
        entry2.delete(0,len(userpwd))    

    
'''
创建左右2个框架frameleft、frameright
'''
frameleft=tkinter.Frame(width=280,height=600,bg="white",relief="groove",bd=5)
frameleft.grid(row=0,column=0,padx=5,pady=5)
frameleft.grid_propagate(0)

frameright=tkinter.Frame(width=620,height=600,bg="white",relief="groove",bd=5)
frameright.grid(row=0,column=1,padx=1,pady=5)
frameright.grid_propagate(0)

#frameleft框架下的内容
label1=tkinter.Label(frameleft,text="账号:")
label1.grid(row=0)
entry1=tkinter.Entry(frameleft)
entry1.grid(row=0,column=1,padx=10,pady=10,)

label2=tkinter.Label(frameleft,text="密码:")
label2.grid(row=1,padx=10,pady=10)
entry2=tkinter.Entry(frameleft)
entry2.grid(row=1,column=1,padx=10,pady=10)

cmd1=tkinter.Button(frameleft,text="确定",command=check)
cmd1.grid(row=2,columnspan=2)

#frameright框架下的内容
label3=tkinter.Label(frameright)
label3.grid(row=0)

root.mainloop()

