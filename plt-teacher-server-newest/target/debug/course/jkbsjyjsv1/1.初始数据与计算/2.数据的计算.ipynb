#%% md
# 鸡兔同笼
《孙子算经》记载：“今有雉（鸡）兔同笼，上有三十五头，下有九十四足，问雉兔各几何？”
## 人工方式解决
方程法、抬腿法、假设法……
## 借助电子表格软件解决
列表法

[tbp01101.xlsx](../assets/tbp01101.xlsx)
#%% md
“数据”在“运算符”的操作下，按“规则”进行的数据变换，如“3+3=6”，这是简单计算中的**算术运算**。

通过建立分析模型和设计有效的步骤、方法，利用自动计算工具来进行规律预测和发现，这是**计算机领域的计算**。

人类分析问题常采用的方法是计算，因此计算可以看做是一种获得信息的过程。
#%% md
## 排序
依据关键字对数据进行升序或降序排列，从而清晰地了解各种分类信息。

[tbp01201.xlsx](../assets/tbp01201.xlsx)
## 任务
1. 通过排序找出全班身高最高的同学

2. 通过多条件排序，把男女生中身高最高的同学找出来。
#%% md
## 筛选
筛选会依据条件显示需要的数据，其余的内容都会被隐藏起来。
[tbp01201.xlsx](../assets/tbp01201.xlsx)
## 任务
1.筛选出性别为男、爱好为篮球的数据，将数据标注为蓝色，然后恢复全表数据显示。
#%% md
## 常用函数
<table border=1 align="center">
<tr align="center"><td>函数</td><td>SUM</td><td>AVERAGE</td><td>COUNT</td><td>MAX</td><td>MIN</td></tr>
<tr align="center"><td>功能</td><td>求和</td><td>求平均值</td><td>计数</td><td>求最大值</td><td>求最小值</td></tr>
</table>


[tbp01301.xlsx](../assets/tbp01301.xlsx)
## 任务
1.计算各位同学的体重指数。计算公式：体重指数=体重÷（身高×身高）
#%% md
## 分类汇总
若要进行分类汇总，首先要按照分类字段排序，以便将要进行分类汇总的行排列在一起，然后依据汇总方式计算选定汇总项。
分类汇总结果可以分级显示，灵活选择显示或隐藏明细数据行。
[tbp01201.xlsx](../assets/tbp01201.xlsx)
## 任务
1. 使用分类汇总的方法统计各种爱好的人数。
2. 使用分类汇总的方法统计不同性别下各种爱好的人数。
#%% md
## 鸡兔同笼问题下的三种计算方式
1. 人工方式——方程、假设法、抬腿法……
2. 电子表格——使用电子表格，列举所有可能，从中选出符合条件的方案
3. 编程

#%%
print('这是一个有关鸡兔同笼问题的程序')
heads=float(input('请输入总的头数:'))
legs=float(input('请输入总的脚数:'))
tu=int((legs-heads*2)/(4-2))
print('兔子有:',tu,'头')
print('鸡有:',int(heads-tu),'头')
input("运行完毕，请按回车键退出...")
#%% md
## if函数
IF 函数是最常用的函数之一，它可以对值和期待值进行逻辑比较。

例如，=IF(C2=”Yes”,1,2) 表示 IF(C2 = Yes, 则返回 1, 否则返回 2)。

[if函数.xlsx](../assets/if函数.xlsx)
## 任务
1. 成绩>=60,等第记为“合格”，否则为“不合格”
2. 成绩>=90,等第记为“优秀”;60<=成绩<90,等第记为“合格”，否则为“不合格”