#%% md
# 一、项目引入
<img src="../assets/1.jpg" style="width: 600px" />

> 试试看这些问题？

<div style="display: flex; width: 100%;">
    <ul style="width:50%;">
        <li>废电池是什么垃圾？</li>
        <li>废弃的食用油属于什么垃圾？</li>
        <li>树叶属于什么垃圾？</li>
        <li>坏掉的电脑属于哪类垃圾？</li>
    </ul>
    <ul>
        <li>空的灭火器属于哪一类垃圾？</li>
        <li>撕掉了的旧照片,应该丢到哪个垃圾桶内？</li>
        <li>塑料玩具是什么垃圾？</li>
        <li>药丸是什么垃圾？</li>
    </ul>
</div>

<ul>
    <li><a target="_blank" href="../input/垃圾分类知识库.csv" download="垃圾分类知识库.csv">下载 垃圾分类知识库.csv</a></li>
</ul>
# 二、项目分析
## 活动1-体验垃圾分类智能问答机器人。（文字版的垃圾分类问答机器人）
<img src="../assets/2.jpg" style="width: 600px" />
<img src="../assets/6.jpg" style="width: 600px" />

> 试试看这些问题？
<div style="display: flex; width: 100%;">
    <ul style="width:50%;">
        <li>创可贴是什么垃圾？</li>
        <li>烟头烟灰是什么垃圾？</li>
        <li>废纸是什么垃圾？</li>
        <li>废书籍是什么垃圾？</li>
        <li>纸箱是什么垃圾？</li>
        <li>纸尿裤是什么垃圾？</li>
        <li>废纽扣电池是什么垃圾？</li>
        <li>螺丝刀是什么垃圾？</li>
    </ul>
    <ul>
        <li>尘土是什么垃圾？</li>
        <li>打火机是什么垃圾？</li>
        <li>纸袋是什么垃圾？</li>
        <li>信封是什么垃圾？</li>
        <li>食物残渣是什么垃圾？</li>
        <li>包是什么垃圾？</li>
        <li>过期药品及其包装是什么垃圾？</li>
        <li>日光灯管是什么垃圾？</li>
    </ul>
</div>



单击下方代码块右下脚运行按钮，就可以开始提问，结束提问请直接在文本框中输入回车。
#%%
import sys
sys.path.append("../")
from input.faqbot import chat
chat('../input/baidu_stopwords.txt', '../input/垃圾分类知识库.csv', 'gbk')  
#%% md
# 三、项目实施
## 活动2-学习分词和相似度计算方法，完成程序填空
<img src="../assets/4.jpg" style="width: 400px; height:200px" />

通过对句子进行中分分词（已经去除无意义词以增加匹配度），确定关键词：
运行以下代码试一试：
#%%
from input.faqbot import cut
print(cut('废弃药品是什么垃圾？'))
print(cut('过期药品及其包装是什么垃圾？'))
print(cut('废纽扣电池是什么垃圾？'))   
#%% md
> Jaccard index, 又称为Jaccard相似系数（Jaccard similarity coefficient）用于比较有限样本集之间的相似性与差异性。Jaccard系数值越大，样本相似度越高。

给定两个集合A,B，Jaccard 系数定义为A与B交集的大小与A与B并集的大小的比值，定义如下：

![image.png](attachment:a7d5aff7-4650-4df3-9287-c8d68a222321.png)

简单来说，jaccard相似度就是两个句子词汇的交集size除以两个句子词汇的并集size。举个例子来说：

句子1分词结果集合a为： ```{'药品', '废弃'}```，集合中元素数量为2

句子2分词结果集合b为： ```{'包装', '药品', '过期'}```，集合中元素数量为3

他们的交集i是：```{'药品'}```，集合中元素数量为1

那么根据公式 $jaccard(a, b) =  \frac{1}{2 + 3 - 1} = 0.25$

尝试将下列代码中的横线按照公式结合函数中定义的变量进行完善，并执行函数以测试jaccard系数计算。
#%%
# 计算jaccard系数
def jaccard(a, b):
    intersection = [value for value in a if value in b]  #求a和b的交集
    il = len(intersection)
    al = len(a)
    bl = len(b)
    return float(il) / (_______________)

a = {'药品', '废弃'}
b = {'包装', '药品', '过期'}
print(jaccard(a, b)) 
#%% md
![image.png](attachment:b8b39b14-7948-42ef-944a-d449a719929c.png)
#%%
a = {'药品', '废弃'}
b = {'包装', '药品', '过期'}
c = {'电池', '纽扣', '废'}
print('问题', a)
print('常见问题1:', b, jaccard(a, b))
print('常见问题2:', c, jaccard(a, c)) 
#%% md
## 活动3-探究文档库，添加不同领域的知识库，看看你的机器人回答问题有没有不同？
<img src="../assets/5.jpg" style="width: 400px;" />

<ul>
    <li><a target="_blank" href="../input/垃圾分类知识库.csv" download="垃圾分类知识库.csv">下载 垃圾分类知识库.csv</a></li>
    <li><a target="_blank" href="../input/科大讯飞学习机.csv" download="科大讯飞学习机.csv">下载 科大讯飞学习机.csv</a></li>
</ul>
#%%
chat('../input/baidu_stopwords.txt', '../input/垃圾分类知识库.csv', 'gbk')
#%% md
<img src="../assets/3.jpg" style="width: 400px" />

> 科大讯飞智能学习机X2 Pro 4G+128GB 儿童家教机早教机点读机 小学初中高中学生平板学习平板 个性化精准学习

我们在网上想咨询一下这个产品，会问哪些问题呢？

* 我有两个小孩，五年级和初一都能用吗？
* 真心求问，请真实回答，小学四年级开始是合适这款讯飞X2pro还是步步高S5pro。谢谢！
* 能学语文吗？
#%%
chat('../input/baidu_stopwords.txt', '../input/科大讯飞学习机.csv', 'gbk')
#%% md
# 四、总结
> 阅读代码和课本理解总结步骤，绘制思维导图

```python
    # 文档库：初始化停止词库和FAQ文档库

    # 加载停止词库
    load_stopwords(stop_words_path)

    # 加载FAQ文件，格式为[[['分词', '过', '的', '问题'], '答案']]
    faqs = load_faq_csv(faq_path, encoding)

    # 获得用户问题
    while True:
        ask = input('请输入问题：')
        if len(ask) == 0:
            break

        # 对于用户问题的中文分词
        ask_words = cut(ask)

        # 信息检索：将用户提出的问题与每个常见问题计算相关系数，找到相关系数最高的那个答案
        max_similarity = 0
        max_similarity_answer = None
        for faq in faqs:
            # 将分好词的常见问题和答案取出
            question_words, answer = faq

            # 计算 用户问题分词 和 常见问题分词的相关性
            similarity = jaccard(ask_words, question_words)

            # 问题理解：使用相似度排序，保留相关性最大的结果
            if similarity > max_similarity:
                max_similarity = similarity
                max_similarity_answer = answer

        # 答案抽取：抽取匹配度最大的AQ答案
        if max_similarity_answer:
            print(max_similarity_answer)
        else:
            print('哎呀，这个问题，我也不太清楚呢！')
            
```
#%%
