#%% md


## <strong>人工智能的诞生与发展</strong>

人工智能是通过智能机器延伸、增强人类改造自然和治理社会能力的新兴技术。怎样才能判断一台机器是否具备了思维能力呢？阿兰·图灵提出：在测试人与被测试者（一个人和一台机器）隔开的情况下，通过一些装置（如键盘）向被测试者随意提问。经过多次测试后，如果被测试者超过 70% 的答复不能使测试人确认出哪个是人、哪个是机器的回答，那么这台机器就通过了测试，并被认为具有人类智能。

<img src='../assets/图灵测试示意图.jpg'  style="width: 400px; height:200px">
<br><br>

<strong>人工智能发展历程大致分为三个阶段</strong>

第一阶段（20世纪50年代——80年代）。这一阶段人工智能刚诞生，基于抽象数学推理的可编程数字计算机已经出现，符号主义(Symbolism)快速发展，但由于很多事物不能形式化表达，建立的模型存在一定的局限性。此外，随着计算任务的复杂性不断加大，计算能力远远不能满足需求，人工智能发展一度遇到瓶颈。

第二阶段（20世纪80年代——90年代末）。在这一阶段，专家系统得到快速发展，数学模型有重大突破，但由于专家系统在知识获取、推理能力等方面的不足，以及开发成本高等原因，人工智能的发展又一次进入低谷期。

第三阶段（21世纪初至今）。大数据的积聚、理论算法的革新、计算能力的提升，为人工智能发展提供了丰富的数据资源，协助训练出更加智能化的算法模型。人工智能的发展模式也从过去追求“用计算机模拟人工智能”，逐步转向以机器与人结合而成的增强型混合智能系统，用机器、人、网络结合成新的群智系统，以及用机器、人、网络和物结合成的更加复杂的智能系统。人工智能在很多应用领域取得了突破性进展，迎来了又一个繁荣时期。

<img src='../assets/人工智能发展历程.jpg'  style="width: 900px; height:600px"><br><br>

#%% md


## <strong>体验1：机器翻译</strong>
以词组为基础的传统翻译系统往往将语言句子拆分成多个词块，然后进行词对词的翻译。这样的翻译输出远不及人工翻译来得流畅。机器翻译，即跨语言间的自动翻译，特别是结合神经网络的机器翻译能够捕捉语言中的词性和句法结构等要素，输出流利度更高的翻译结果。<br><br>

运行下面的程序，输入要翻译的中文。如果要退出，请输入“q”。
#%%
#体验机器翻译
import hashlib
import random
import json
import urllib.request
import sys

def translate(conent):
    appid = '20210421000792221'
    secretKey = 'Q3TCi9QqYiGcV_n2vrbH'
    myurl = 'https://api.fanyi.baidu.com/api/trans/vip/translate'
    q=content
    fromLang = 'zh' # 源语言
    toLang = 'en' # 翻译后的语言
    salt = random.randint(32768, 65536)
    sign = appid + q + str(salt) + secretKey
    sign = hashlib.md5(sign.encode()).hexdigest()
    query_words = urllib.request.quote(q)
    myurl = myurl + '?appid=' + appid + '&q=' + query_words + '&from=' + fromLang + '&to=' + toLang + '&salt=' + str(salt) + '&sign=' + sign
    
    try:
        # 获得返回的结果，结果为json格式
        jsonResponse = urllib.request.urlopen(myurl).read()
        # 将json格式的结果转换字典结构
        js = json.loads(jsonResponse)
        # 取得翻译后的文本结果
        dst = str(js["trans_result"][0]["dst"])
        print(dst) # 打印结果
    except Exception as e:
        print(e)

if __name__ == '__main__':
    while True:
        print("请输入要翻译的中文。如果要退出，请输入“q”。")
        content = input()
        if (content == 'q' or content == ''):
            break
        translate(content)
#%% md
整句翻译的准确率如何？翻译的速度如何？
#%%

#%% md
## <strong>体验2：人脸识别匹配程序</strong>
人脸识别是基于人的脸部特征信息进行身份识别的一种生物识别技术，是用摄像机或者摄像头采集含有人脸的图像或者视频流，并自动在图像中检测和跟踪人脸，进而对检测到的人脸进行脸部特征比对的一系列相关技术。<br>
<img src='../assets/Fig23.jpg' style="width: 1000px" ><br>
下面的四张图片的文件名依次是：6-1.jpg、6-2.jpg、6-3.jpg、6-4.jpg，运行程序后输入图片的文件名，查看对比结果。<br><br>
<img src='../assets/6-1.jpg'>
<img src='../assets/6-2.jpg'>
<img src='../assets/6-3.jpg'>
<img src='../assets/6-4.jpg'>
#%%
#体验人脸识别匹配程序
import requests
import base64
import json

def Get_API():

    # client_id 为官网获取的AK， client_secret 为官网获取的SK.
    client_id = 'E57oyZKpgX6TH6NUk5wdiLLU'    
    client_secret = 'b1joo8sCRjzzkubZAHmcouqh7OiN6zml'    
    host = 'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s'%(client_id,client_secret)

    response=requests.get(host)
    #返回访问令牌
    access_token=eval(response.text)['access_token']
    request_url='https://aip.baidubce.com/rest/2.0/face/v3/match'
    API = request_url + "?access_token=" + access_token
    #API = "https://aip.baidubce.com/rest/2.0/face/v3/match"+"?access_token="+access_token    

    return API 


#转换图片
#读取文件内容，转换为base64编码
#二进制方式打开图文件
def Image_coding(file1path,file2path):
    f=open(r'%s' % file1path,'rb') 
    pic1=base64.b64encode(f.read()) 
    f.close()
    f=open(r'%s' % file2path,'rb') 
    pic2=base64.b64encode(f.read())
    f.close()
    #将图片信息格式化为可提交信息，这里需要注意str参数设置
    params=json.dumps([
        {"image":str(pic1,"utf-8"),"image_type":'BASE64'},
        {"image":str(pic2,"utf-8"),"image_type":'BASE64'}])
    return params

#分析和比较图片
def Image_contrast(img1,img2):

    API=Get_API()
    params=Image_coding(img1,img2)
    #以POST方式得到结果
    content=requests.post(API,params).text
    #print(content)
    #计算得分
    score=eval(content)['result']['score']
    if score>=80:
        print('二人相似度得分为 %s, 是同一人的可能性极大'%str(score))
    else:
        print('二人相似度得分为 %s, 不是同一人的可能性极大'%str(score))

if __name__ == '__main__':
    print('输入第一张图片名：')
    file1=input()
    print('输入第二张图片名：')
    file2=input()
    print('开始对比...')
    result = Image_contrast('../assets/'+file1, '../assets/'+file2)
#%% md
人脸识别匹配的结果是？此应用可以用于什么场景？
#%%

#%%

#%% md
## <strong>体验3：OCR图片中的文字</strong>
<img src='../assets/鸡兔同笼.jpg'>
<img src='../assets/book1.jpg'>
#%%
from aip import AipOcr
APP_ID = '24040431'
API_KEY = '2W5FaGqIgGkIPCKNr1XKobIL'
SECRET_KEY = '1NzdP46pNORAdAKW2E1tL0jiNATw3KWZ'
client=AipOcr(APP_ID,API_KEY,SECRET_KEY)
i=open('../assets/鸡兔同笼.jpg','rb')
img=i.read()
message=client.basicGeneral(img)
for i in message.get('words_result'):
    print(i.get('words'))
print()
i=open('../assets/book1.jpg','rb')
img=i.read()
message=client.basicGeneral(img)
for i in message.get('words_result'):
    print(i.get('words'))
#%% md
程序对图片中文字的识别效果如何？想一想，哪些因素会影响识别率。
#%%

#%%

#%% md
## <strong>体验4：情感分析</strong>
情感分析是自然语言处理中的一项技术，也是文本挖掘常用的方法。在产品开发与维护中，情感分析技术经常被采用，如通过分析用户评论，调查用户对产品的需求；通过分析买家的产品评价，获得用户喜好，针对性地向用户推荐产品；通过分析网络新闻，调差社会舆论。
#%%
# 体验情感分析
# 导入aip库中的AipNlp方法
from aip import AipNlp
APP_ID = '22787102'
API_KEY = 'BjDaai8tuKgjfFEUkQI2UH9M'
SECRET_KEY = 'Gl5r3oq9akkVjcmEN3Cpiv0QyX04vlin'

client = AipNlp(APP_ID, API_KEY, SECRET_KEY)
text=input("请输入一句话表达你的心情")
result=client.sentimentClassify(text)
print("积极情绪的概率：",result['items'][0]['positive_prob'])
print("负面情绪的概率：",result['items'][0]['negative_prob'])
print("置信度：",result['items'][0]['confidence'])  
#%% md
上述结果显示你的心情属于什么类别？置信度是多少？
#%%

#%% md
## <strong>体验5：人工神经网络——多层感知机手写数字识别</strong>
人工神经网络在解决手写数字识别这一问题，其思想就是利用大量的手写数字（训练样本），开发出一套从训练样本中进行学习的系统。人工神经网络使用如下图所示的样本来自动推理出识别手写数字的规则，通过增加训练样本的规模，神经网络能学到手写数字的更多规则，从而提升它的识别精度。<br>
<img src='../assets/Fig24.jpg'>

导入第三方库
#%%
# Only use this if using iPython
%matplotlib inline

print(__doc__)

import matplotlib.pyplot as plt
import numpy as np
from sklearn.datasets import fetch_openml
from sklearn.neural_network import MLPClassifier
#%% md
下载训练集，大约需要一分钟时间
#%%
# Load data from https://www.openml.org/d/554
X, y = fetch_openml('mnist_784', version=1, return_X_y=True)
X = X / 255.

X_train, X_test = X[:60000], X[60000:]
y_train, y_test = y[:60000], y[60000:]
#%% md
查看前 9 个训练文件
#%%
import matplotlib.pyplot as plt
fig = plt.figure(figsize=(10,10))
plt.rcParams['font.family'] = 'Source Han Serif SC'
plt.rcParams['axes.unicode_minus'] = False
for i in range(9):
  plt.subplot(3,3,i+1)
  plt.tight_layout()
  data = X_train.loc[i]
  plt.imshow(data.values.reshape(28, 28), cmap='gray', interpolation='none')
  plt.title("Digit: {}".format(y_train[i]),fontsize=20)
  plt.xticks([])
  plt.yticks([])
fig        
#%% md
查看最后一个训练文件
#%%
image_index = 59999 # You may select anything up to 60,000
print("数字标签是：",y_train[image_index]) # The label is 8
plt.imshow(X_train.loc[image_index].values.reshape(28, 28), cmap='Greys')   
#%% md
初始化神经网络多层感知机分类器
#%%
# mlp = MLPClassifier(hidden_layer_sizes=(100, 100), max_iter=400, alpha=1e-4,
# solver='sgd', verbose=10, tol=1e-4, random_state=1)
mlp = MLPClassifier(hidden_layer_sizes=(50,), max_iter=10, alpha=1e-4, solver='sgd', 
                    verbose=10, tol=1e-4, random_state=1, learning_rate_init=.1)


mlp.fit(X_train, y_train)
print("Training set score: %f" % mlp.score(X_train, y_train))
print("Test set score: %f" % mlp.score(X_test, y_test))      
#%% md
### 可视化多层感知机神经元的权重

有时查看神经网络学习到的权重可以提供对学习行为有更深入的了解。例如，如果权重看起来结构凌乱，可能有些神经元根本没有起作用，或者学习时使用了较大的超参数，比如正则化太低或学习率太高。

下面的示例显示如何绘制在MNIST数据集上训练的MLPClassifier中的第一层权重。

输入数据由28x28个像素的手写数字组成，所以数据集中有784个特征。 因此，第一层权重矩阵大小为（784，hidden_layer_sizes [0]）。 因此，我们可以将权重矩阵中的一列可视化为28x28像素图像。

为了使示例运行得更快，我们使用了非常少的隐藏单元，并且仅在非常短的时间内进行训练。 训练时间更长会导致重量更加平滑的空间外观。你可以调整上面多层感知机分类器的隐藏层大小，然后重新训练再来看看权重系数的变化。
#%%
fig, axes = plt.subplots(4, 4)

vmin, vmax = mlp.coefs_[0].min(), mlp.coefs_[0].max()
for coef, ax in zip(mlp.coefs_[0].T, axes.ravel()):
    ax.matshow(coef.reshape(28, 28), cmap=plt.cm.gray, vmin=.5 * vmin,
               vmax=.5 * vmax)
    ax.set_xticks(())
    ax.set_yticks(())

plt.show()
#%% md
进行预测
#%%
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'Source Han Serif SC'
plt.rcParams['axes.unicode_minus'] = False
image_index = 60001  # X_test索引小于70000,大于60000
plt.imshow(X_test.loc[image_index].values.reshape(28, 28),cmap='Greys')
pred = mlp.predict(X_test)
print("预测标签是：", pred[image_index-60000])  
#%% md
对 10000 个样本进行预测的总体情况，并显示其中正确的9个预测
#%%
predicted_classes = mlp.predict(X_test)
y_test_list = y_test.values.tolist()
correct_indices = np.nonzero(predicted_classes == y_test_list)[0]
incorrect_indices = np.nonzero(predicted_classes != y_test_list)[0]
print()
print(len(correct_indices)," 分类正确")
print(len(incorrect_indices)," 分类错误")


import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'Source Han Serif SC'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (10,20)

figure_evaluation = plt.figure()

print("正确的 9 个预测")
for i, correct in enumerate(correct_indices[:9]):
    plt.subplot(6,3,i+1)
    plt.imshow(X_test.loc[60000+correct].values.reshape(28,28), cmap='gray', interpolation='none')
    plt.title(
      "预测: {}, 真实: {}".format(predicted_classes[correct],
                                        y_test[60000+correct]),fontsize=20)
    plt.xticks([])
    plt.yticks([])     
#%% md
显示不正确的 9 个预测
#%%
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'Source Han Serif SC'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (10,20)

figure_evaluation = plt.figure()
print("不正确的 9 个预测")
for i, incorrect in enumerate(incorrect_indices[:9]):
    plt.subplot(6,3,i+10)
    plt.imshow(X_test.loc[60000+incorrect].values.reshape(28,28), cmap='gray', interpolation='none')
    plt.title(
      "预测 {}, 真实: {}".format(predicted_classes[incorrect], 
                                       y_test[60000+incorrect]),fontsize=20)
    plt.xticks([])
    plt.yticks([])    
#%% md
试试看调整神经网络多层感知机分类器的参数，提升识别率
#%%

#%%
