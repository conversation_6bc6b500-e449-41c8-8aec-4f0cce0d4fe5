#%% md
# 导入
#%% md

 小明同学所在城市的地铁线路局部图，如下图所示。他计划从A站出发去B站附近的图书馆学习。假设地铁各线路每两站间行车用时相等，记为t1；换乘地铁的用时也相等，记为t2。
![城市地铁线路.jpg](../assets/城市地铁线路.jpg)
## 思考：

1. 列举出由A站出发到达B站的所有换乘次数最少的乘车路线。

2. 如果小明同学希望尽快赶到B站，试为他推荐一条最佳乘车路线，并说明理由。
### 什么是算法？
#%%

#%% md

苏州与京东联合开展“迎新春,年货节”摇号数字红包的活动，凡是报名参加的都有机会获得数字人名币的年货红包。

游戏体验：输入自己的手机号码，系统抽奖，根据系统公布的结果，观察自己是否“中奖”。

我们要参加这个活动，看看自己能否中奖，首先就要输入自己的手机号码，才能参加系统抽奖

## 体验一： 运行下面的程序，输入手机号码，看看能否中奖？
#%%
import math
import random
Price = []
Data = [1586231234,1586231233,1586231232,1586231231]
phone = int(input("'迎新春，年货节!'请输入你的手机号码："))
Data.append(phone)
Data = tuple(Data)
print("参与抽奖的号码是：",Data)
n = math.ceil(len(Data)*0.3)
print("一共有%d个号码中奖"%n)
while True:
    m = random.randint(1,len(Data)-1)
    if Data[m] in Price:
        continue
    Price.append(Data[m])
    if len(Price) == n:
        break
if phone in Price:
    print("你的号码%d中奖了！中奖的号码是："%phone)
    print(Price)
else:
    print("您的号码未中奖，谢谢！中奖的号码是：")
    print(Price)
  
#%% md

## 实验总结：

算法一定有输出吗？

算法的执行步骤是有限还是可以无限的？

算法一定要有程序本身以外的外部信息输入吗？
#%% md
### 李先生收到了中奖的提示，但是他却发现自己无法兑奖。
经过工作人员检查后发现，因为参与活动的中奖者人数比较庞大，服务器经过备份，就把数据按照不同的区县，分发给不同的管理员去处理了，在分发的过程中由于不小心删除了中奖者的手机号（唯一），导致无法确认中奖者身份。还好备份的数据库里有该条数据的手机号（手机号无序），请问该如何快速地找到这条中奖信息的ID号？


 请下载下面两个文件，观察两个文件的数据

[trouble.txt](../input/trouble.txt)(不完整的数据库)

[backup.txt](../input/backup.txt)(备份的数据库)

>讨论：可以从哪些角度思考去找出丢失的那个号码？

哪一种算法对程序设计者来说简单(速度快，减少人力成本)？


 计算机访问文件的方法：

 计算机如何读取文件中的数据？--open（）、readlines（）、close（）
#%%
#open("文件名.扩展名",'读取方式')，我们用open()函数来访问文件
#readlines()函数表示按行读取文件，读取每一行作为一个元素，最后把读取的所有元素以列表的形式返回
f=open("../input/backup.txt",'r')
# 让我们把列表中的元素一次性打印出来
print(f.readlines())
# 让我们把列表中的元素一个个打印出来
words = f.readlines()
for word in words:
    print(word,end="")    
#%%
#接下来我们读取两个文件（"back.txt"和"trouble.txt"）的数据
#分别求总sum1和sum2，让sum2-sum1相减，就能找到丢失的那个手机号码了
import datetime
d1 = datetime.datetime.now()
sum1=0					 						#设置初始值
sum2=0											#设置初始值
#打开文件“backup.txt”，并指向操作变量"f1"
f1=open("../input/backup.txt",'r') 						#打开备份文件
lines=f1.readlines() 							#读取每行数据
for line in lines:
       sum1= ？ + int(line) 					#将读取的数据做和运算
f1.close() 									#关闭备份文件

#打开文件“trouble.txt”，并指向操作变量"f2"
f2=open("../input/trouble.txt",？) 						#打开故障文件
lines=f2.readlines() 							#读取每行数据
for line in lines:  
    sum2= ？ +int(line) 						#将读取的数据做和运算 
？									#关闭故障文件
print("被删除的手机号是:",？)		#输出被删除的手机号
d2 = datetime.datetime.now()
print("一共需要的时间:",d2-d1)                                  
#%% md
 中签客户将获取一个程序安装兑换码，才能获得数字人民币。小李同学也被抽中了。他说了一个“谜语”：我的兑换码有4个数字，前2位数字一样，但不是0；后2位数字也一样，但也不是0，前两位和后两位的数字不一样，这个号码是一个整数的平方。你能猜到他的兑换码是多少吗？
#%%
# 兑换码.jpg
from IPython.display import Image
Image('../assets/兑换码.jpg')
#%% md
## 是否需要考虑每一位数字的可能性？
#%%

#%% md
## 先把AABB形式的四位数找出来
#%%
# AABB形式的数字我们需要确定的就是A和B的范围
# AA在前，所以A的范围是（1-10），B的范围是（1-10）
# 用range()函数可以循环A和B的范围，这里我们既要循环A的可能性，又要同时循环B的可能性
# 所以是循环中又有循环，叫做双重循环
for A in range(1,？):  #range()函数左闭右开
    for B in range(？):     #第二条语句用Tab键缩进表示上一个语句的循环体开始执行  
        if A!=B:
            k=A*？+A*？+B*？+B
                                                                                                                                           
#%%

#%% md
## 我们让计算机对AABB的四位数开根号，这个算数平方根不一定符合要求：
1. 这个平方根是一个小数？

2. 这个平方根一定是一个整数？

3. 验证开方的这个数n，符合条件（n*n==AABB）；
#%%
#枚举思想1：
# import math
# import datetime
# d1=datetime.datetime.now()
# for A in range(？):
#     for B in range (？):
#         if A != B:
#             k = ？
#             c = int(math.sqrt(k)) # 求票据中数字的平方根并取其整数部分
#             if ？: 		          # 若k是完全平方数，则找到该票据编号
#                 print(？)         # 输出票据 
# d2=datetime.datetime.now()
# print(d2-d1)
                
                                                                                                                                  
#%% md
## 枚举思路二：先考虑经过平方得到一个四位数的这个整数的所有可能性
#%%
# import math
# import datetime
# d1=datetime.datetime.now()
# for x in range(30,100) :              # 某个整数x可能的范围
#     x=x*x                             # x的平方--可能是AABB，接下来判断
#     if x>1000:                        # 首先这个数要满足大于1000
#         a1 = int(x // 1000)           # 整除取千位数
#         a2 = int((x % 1000) // 100)   # 用%取余数，过滤千位数，再整除取百位
#         b1 = int((x % 100) // 10)     # 同理，取十位数
#         b2 =int(x % 10)               # 余10的余数，得到个位数
#         if a1 == a2 and b1 == b2 and a1!=b1:  # 如果x的平方得到数是AABB
#             print(x)                  # 打印满足条件的这个经过平方的x，也就是AABB
#             break                     # 跳出循环，不用再继续了
# d2=datetime.datetime.now()                                                                                                                                       
# print(d2-d1)
#%% md
# 直译思维——枚举案例：“谁是冠军”
#%% md
### 问题描述：
![4.1.15.jpg](../assets/4.1.15.jpg)

#%% md
### 问题分析：
![4.1.16.jpg](../assets/4.1.16.jpg)
#%%

#%% md



![4.1.17.jpg](../assets/4.1.17.jpg)
#%%
#!='A'的运算结果是一个逻辑值，再数值计算中会自动转换成1（真）或0（假）
champion=['A','B','C','D'] #设置选手列表
for i in champion: #循环读取选手编号
    cond=(i!='A') +(i=='C') + (i=='D')+(i!='D') #查找符合条件的选手
    if cond==3: #说真话是否是3人
        print("冠军是:",i) #输出冠军                     
#%% md

上面这种算法是我们已经使用过许多次的“枚举”。

即：把所有可能的答案一一列举，合适的就保留，不合适就丢弃。

在问题规模大时，枚举法会造成时间代价的增大。

但是计算机运算速度快、精确度高，时间代价在计算机运算速度的比较下就显得完全可以接受。

所以，我们在设计算法时，经常会采用枚举法来直译我们的思维过程。


