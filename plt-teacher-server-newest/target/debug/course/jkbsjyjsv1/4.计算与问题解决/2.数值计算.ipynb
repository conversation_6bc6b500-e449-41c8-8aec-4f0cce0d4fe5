#%% md
# 导入
#%% md
## 一、观察以下三张图片，说一说数据在计算机中有哪些不同的形式？分别有什么作用？
![组合图1.jpg](../assets/组合图1.jpg)


 图形化是帮助我们分析数据，找出规律的重要方法



## 二、计算机绘制图像。

1.正弦三角函数：y = sin(x)，图像：
![图表 1.png](../assets/图表 1.png)


填写下面的表格，让我们与数学中的三角函数公式面对面



#%%

#%% md

相对地址:A2                              绝对地址: $$A$$2

## 三、下载文件，在WPS电子表格中，绘制函数图像




[三角函数绘图.xlsx](../input/三角函数绘图.xlsx)
#%% md
# 用python绘制正弦曲线
#%% md
如何提高图像的精度？--利用Python绘制图像
## 一、在Python中，绘制图像需要用到numpy和matplotlib两个模块

1. numpy是一个科学计算包；

2. matplotlib是出色的绘图库，其中的matplotlib.pyplot经常用来绘制坐标系函数图像
![Pyhton绘图1.jpg](../assets/Pyhton绘图1.jpg)

numpy导入数学公式，matplotlib.pyplot在numpy计算的基础上绘制图形
## 二、在Python中使用numpy和matplotlib.pyplot模块的方法：

要使用<u><b>外部函数</b></u>有两个关键步骤：

第一步<u><b>import ... as ...</b></u>导入数学numpy和绘图matplotlib.pyplot两个模块：

```python
### import numpy as np #导入numpy模块并取名为np
### import matplotlib.pyplot as plt #导入matplotlib.pyplot模块并取名为plt
```

第二步调用函数方法: <u><b>模块名.函数名()</b></u>

 例如:我们要使用numpy模块中的arange()函数

1. 只有函数名，没有模块名：<b>错误！ </b>          

2. 既有模块名又有函数名：<b>正确!</b>


#%% md
arange()函数可以创建一个等差数列：

 示例：arange(0，11，1) 取值为\[0,1,2,3,4,5,6,7,8,9,10\]
#%%
#导入numpy模块取名为np
import numpy as np
#在0-11之间，每隔1取一个数
x = np.arange(0,11,1) 
print(x)                                                                                               
#%%

#%% md
### 正弦三角函数：


#%%
# 描点法：利用Python绘制三角函数
import numpy as np               #导入numpy模块取名为np
import matplotlib.pyplot as plt         #导入matplotlib模块取名为plt
x = np.arange(初值,终值,步长)    #在0-2Π之间，每隔0.01取一个数
y = np.sin(x)                    #通过解析式计算列表x对应的列表y的值

# for i in x:                   # 打印坐标系内（x,y）所有坐标值
#     print(i,np.sin(i))            


plt.plot(x,y)                   # plot()函数绘制图像
plt.show()                      # show()显示图像

                                                                                                                                                                                                                    
#%% md
## 三、matplotlib.pyplot绘制图像
![Pyhton绘图1.jpg](../assets/Pyhton绘图1.jpg)
#%%
#导入numpy模块并取名为np
？
#导入matplotlib.pyplot模块并取名为plt
import matplotlib.pyplot as plt
x = ？.arange(？)       #数列在0-2Π之间，每隔0.01取一个点

y1 = ？                 #求sin(x)对应的列表y1的值：

y2 = np.sin(-x)         #求sin(-x)对应的y2的列表值

y3 = ？                 #求sin(2x)/2对应的y3的列表值：

plt.plot(x,y1)             # 1.画出sin(x)的图像
？                          # 2.画出sin(-x)的图像
？                          # 3.画出sin(2x)/2的图像

plt.title('y=sin(x)')    #绘制图像标题
plt.xlabel('X')        #绘制X轴标题
plt.ylabel('Y')        #绘制Y轴标题
plt.show()             #将绘制的函数图像窗口显示出来

                                                                                                                                                                                                                                                                                                                                                                      
#%% md
# 斐波那契数列
#%%
#下面这个例子是否使用了迭代法？
#sum的值是否实现了迭代？i的值是否实现了迭代？
#1-30的求和
sum = 0                       #求和变量：sum，初值为0
print("sum的初始值=",sum)
#用循环重复求和计算
#------以下是循环体语句-----
for i in range(31):
    sum = sum + i              #先右后左（右边初值用于计算；左边终值，通过迭代更新）
    print("加",i,"后sum的值更新为：",sum)
#------以上是循环体语句------
                     #循环结束，打印结果
    
                                                                                                                                                                                                                                                                                                                                        
#%% md

除了绘制图像，对于有规律的数值计算，Python也有很大的作用

# 斐波那契数列：

假设一对大兔子每个月可以生一对小兔子，小兔子花一个月长成大兔子，新的大兔子以后每个月又可以生一对小兔子。则一对小兔子七个月能繁殖成多少对？
#%%

#%% md
## 一、下载文件，用WPS求解第75月各月兔子数量总和
[用WPS求解斐波那契数列.xlsx](../input/用WPS求解斐波那契数列.xlsx)

观察第74和第75月的结果是否正确，思考用WPS求解斐波那契数列有什么问题？

## 二、用Python求解数列--简化参数

 WPS电子表格求解斐波那契数列的方法：A3 = A1 + A2,产生很多单元格的参数

程序思想求解：c = a+b

已知a与b，求第n项？

第一步：前项：a,后项：b，第三项:c = a+b,则第三项的结果就是c的值；

第二步：<strong>更新</strong>a和b的值：a=b,b=c；

重复执行第一步和第二步，求出第n项的值。


#%% md

这种过程重复，且计算的终值用作下一次初值的方法叫做<b>迭代法</b>。
#%% md

 迭代法的关键：<strong><u><u>通过迭代，接近并到达所需的目标或者结果</u></u></strong>

减少参数，斐波那契数列：

![迭代.png](../assets/迭代.png)
#%%
def fib(n):
    #利用迭代求斐波那契数列的第n个数
    f2 = f1 = 1                 #第1个月、第2个月初值设定
    for i in range(3,n+1):      #__________？______________
        #f1,f2 = f2,f1+f2       #下面的两条赋值语句也可以合并写成一条
        f = ？
        f1 = ？
        f2 = ？
    return f
n = int(input('输入需要计算的月份数：'))
print('兔子总对数为：',fib(n))                                                                                                     
#%% md

斐波那契数列在自然科学的其他分支，有许多应用。例如，树木的生长，由于新生的枝条，往往需要一段“休息”时间，供自身生长，而后才能萌发新枝。所以，一株树苗在一段间隔，例如一年，以后长出一条新枝；第二年新枝“休息”，老枝依旧萌发；此后，老枝与“休息”过一年的枝同时萌发，当年生的新枝则次年“休息”。这样，一株树木各个年份的枝桠数，便构成斐波那契数列。这个规律，就是生物学上著名的“鲁德维格定律”。

![斐波那契数列树.gif](../assets/斐波那契数列树.gif)



#%% md
## 一只猴子摘了若干桃子，每天吃现有桃子的一半多一个，到第十天就只有一个桃子了，问猴子最初共摘了多少桃子？

数学模型：

每天的桃子数为：a10=1,a9=(1+a10)*2,a8=(1+a9)*2,…

递推公式为：ai=(1+ai)*2, i=9,8,7,6,…,1
#%%
#求桃子的数量
peaches = 1                 #第十天剩余的桃子数量
for i in range(2,？):
    peaches = ？
    print("第",11-i,"天的桃子数量："？)             