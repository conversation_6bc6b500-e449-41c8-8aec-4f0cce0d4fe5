#%% md
# 导入
#%% md
## 一、游戏：猜数字。

请一位同学心里选一个100以内的非零整数作为答案，另一个同学去猜。如果猜测的数字大于答案，对方提示：大了；如果猜测的数字小于答案，对方提示：小了；如果猜测正确则提示：答案正确！

### 讨论：

1. 至少需要多少次能猜中答案？

2. 如果猜测的范围扩大10倍或者100倍，该如何去猜中这个数字？


    1. 如何描述这个算法？

    2. 程序如何实现

下载文件后将文件的<strong>扩展名由</strong>“txt”改成<strong>“fld”</strong>,在画程中完成流程图的绘制

[猜数字游戏.txt](../input/猜数字游戏.txt)
#%%
#思考：程序在执行的过程中，重复着什么规律？
#导入随机函数模块，用于生成一个随机数

import random
#生成一个1-100的随机数
n=random.randint(1,100)
x=int(input('请输入x='))
#开始循环
#如果猜中就中断循环
while (？):                 #第1处填空，当满足什么条件，进入循环     
  if x<n:
    print("小了")
  elif ？:                  #第2处填空
    print("？")             #第3处填空
  x=int(input('请输入x='))
print("恭喜你,猜中了！")
#结束                                                                                      
#%% md

分治策略：是将一个难以直接解决的大问题，分割成一些较小的同类问题，各个击破，最终达到解决问题的目的。

## 二、项目活动：谁是小偷？
 ![监控.jpg](../assets/监控.jpg)

警方接到报案，报警人声称自己的手机昨天晚上被盗。

报案人提供的证据1
![前天晚上.jpg](../assets/前天晚上.jpg)

报案人提供的证据2
![第二天白天.jpg](../assets/第二天白天.jpg)

经过现场勘察，警方决定查看监控，看看手机是在夜间什么时候丢失的？

监控一共有12个小时，全部看完要花费大量的时间。

该如何查看监控？


![监控需要时间.jpg](../assets/监控需要时间.jpg)
#%% md
# 二分查找法
#%% md
先从哪个位置开始看监控？
![开始查看监控.jpg](../assets/开始查看监控.jpg)
==========================================================

![查看监控.jpg](../assets/查看监控.jpg)
===========================================================
<table border=0><tr>
<td align=center>![查看监控11.jpg](../assets/查看监控11.jpg)</td>
<td align=center>![查看监控22.jpg](../assets/查看监控22.jpg)</td></tr></table>

### 二分法查看监控：
<video src="../assets/二分查找监控.mp4" style="width: 100%;" controls="controls"/>

### 一个1小时的监控，大约11次就能确定需要的信息。
### 我们来尝试完善下面的二分查找程序。输入指定的时间，看看计算机需要多少次能找到指定的监控画面。


#%%
#查找一小时以内的监控
import datetime
x = int(input("请输入要查找的监控时间是第几秒："))
step = 1
flag1 = 1
flag2 = 3600
d1 = datetime.datetime.now()
while(flag1<=flag2):
    print("左边界：{:-3d}，右边界：{:-4d}，当前第：{:2d}次查找".format(flag1,flag2,step))
    mid = int((flag1 + flag2)/2)
    step = step + 1
    if x>mid:                           # 如果x大于中间值
        flag1 = ？                      # 第1处填空，更新左边界
    elif ？:                            # 第2处填空，如果x小于中间值
        flag2 = ？                      # 更新右边界
    else:
        print("监控画面在第：",？,"秒")   # 第3处填空，x等于选取画面的中间值
        print("一共执行了：",step,"步")
        d2 = datetime.datetime.now()
        print("用时：",d2-d1,"秒")
        break                                                                                                                    
#%% md
### 二分查找又叫折半查找，该方法主要将数列有序排列，采用跳跃式的方式查找数据。

 注意：二分法查找的前提条件是被查找的数据必须是有序的。

#%%
# 有一个列表：nums = [1,2,3,4,6,7,8,8,9,10,12,12,14,15,20] 
# 请设计一个算法找出列表中大于等于某个数的第一个位置
nums = [1,2,3,4,6,7,8,8,9,10,12,12,14,15,20]
l = len(nums) - 1                              #求列表的长度
x = int(input("请输入要查找的数："))
flag1 = 0                                      #左边界
flag2 = l                                      #右边界
while(flag1 <= flag2):                         #左边界不超过有边界时循环
    mid = ？                                   #取整求列表的中间值 
    if x<=nums[mid]:                           #如果要找的数在中间值的左侧，包括中间值
        flag2 = ？
    elif x>nums[mid]:                          #如果要找的数在中间值的右侧 
        flag1 = ？
print(flag1)                                   #打印左边界 



 
#%% md
# 用递归法解决问题
#%% md
## 用递归法解决问题：
![算法及其特征.jpg](../assets/算法及其特征.jpg)

在一个队伍中，假设不能观察队伍的长度，也不能随意走动，如何确定自己排在第几个？
### 一、什么是递归？

 函数<strong>重复调用自身</strong>的方法称为递归。

在下面的例子中首先通过"def A()"定义了A()函数并等待程序调用这段代码，在程序在"2"中执行到A()就调用”1“中的代码，然后程序就会不断调用1的代码，如果程序有终止条件，则返回调用。



递归的条件：
1. 递推公式；
2. 回归条件。

用递归法求解：

1-10的平方和？

#%%
# 1-10平方求和的问题f(10)，分解成f(9) + 10**2（**2表示平方） 的问题，这样我们只需要求出f(9)的结果
# 同理，我们一直把问题分解成f(2) = f(1) + 2**2
# 分解到n==1 的时候 f(n) 得到返回值1，返回上一层求得f(2),再逐层返回，直至f(10)
#1.递推公式：f(n) = f(n-1) + n**2
#2.回归条件：f(1) = 1**2=1
# 递归法求解
def f(n):                   #定义f()函数
    if n>1:
        return f(n-1) + n**2
    elif n==1:
        return 1
print(f(10))         
#%%
# 求10的阶乘
# f(n) = f(n-1) * n
# n == 1 f(n) 返回值为1
def f(n):
    if ?:
        return n
    else:
        return ?
print(f(10))
#%% md

<video src="../assets/【一听就懂】什么是递归？.mp4" style="width: 100%;" controls="controls"/>
#%%

# 小李的教室在4楼，每天都要爬楼梯回教室。他走楼梯可以1步1个台阶，也可以1步2个台阶。
# 已知1楼到4楼一共有30个台阶，请问他从第1个台阶到第30个台阶共有几种走法？
# 用递归的方法，关键不在于马上求得结果，而是把复杂的问题分解成简单的问题。
# 例如：要求上到第30个台阶，我们从前往后想比较复杂。但是我们可以从后往前推。
# 第30个台阶是由哪个台阶跨上来上的呢？可以从第29个台阶（1步1个台阶），也可以从第28个台阶（1步2个台阶）
# 而第1个台阶和第2台阶的走法则是容易推算的
def f(n):
    if n == 1:
        ？                              #填空1：回归条件
    elif n==2:
        ？
    else:
        return ？                       #填空2：递推公式
                         
print("目标台阶的走法：",f(？))     #填空3：目标天数


#%% md
### 用递归法求斐波那契数列

斐波那契数列”1，1，2，3，5，8，13 ......“,可以递归定义为：


![斐波那契而数列通项公式.jpg](../assets/斐波那契而数列通项公式.jpg)
#%%
#求斐波那契数列第n项的值
#为了能快速求得结果，我们求20项以内的某项的值
#输入20以内的数
import datetime
def f(n):
    if n>2:
        return ?                                    #递推公式
    else:
        return ?                                    #边界条件
d1 = datetime.datetime.now()
n = int(input("要求的是斐波那契数列的第几项：")) 
if n<=20:
    print("第",n,"项的结果是：",?))                     #调用函数，求第n项值
    d2 = datetime.datetime.now()
    print(d2-d1)
print("输入超过了20")   
#%% md
# 汉诺塔游戏
#%% md

法国数学家爱德华·卢卡斯曾编写过一个印度的古老传说：在世界中心贝拿勒斯（在印度北部）的圣庙里，一块黄铜板上插着三根宝石针。印度教的主神梵天在创造世界的时候，在其中一根针上从下到上地穿好了由大到小的64片金片，这就是所谓的汉诺塔。不论白天黑夜，总有一个僧侣在按照下面的法则移动这些金片：一次只移动一片，不管在哪根针上，小片必须在大片上面。僧侣们预言，当所有的金片都从梵天穿好的那根针上移到另外一根针上时，世界就将在一声霹雳中消灭，而梵塔、庙宇和众生也都将同归于尽。


三个盘子的汉诺塔动图演示：

![汉诺塔动图.gif](../assets/汉诺塔动图.gif)

 四个盘子的汉诺塔：

![汉诺塔.jpg](../assets/汉诺塔.jpg)

 四个盘子的汉诺塔动图演示：


![汉诺塔动图2.gif](../assets/汉诺塔动图2.gif)
#%%
def hano(A,B,C,n):
    if n == 1:
        print(n,A,'---',C)
    else:
        hano(?)
        print(n,A,'---',C)
        hano(?)

n= int(input("盘子数量（不超过5个）："))
if n<=10:
    hano('A','B','C',n) 
else:
    print("请输入5以下的数字")           
#%%
#代码演示：
#n层汉诺塔
import datetime
step = 0
def f(A,B,C,n):
    global step
    if n==1:
        step = step + 1
        print("第",n,"个盘子",":",A,"-----",C)
    else:
        f(A,C,B,n-1)
        print("第",n,"个盘子",":",A,"-----",C)
        f(B,A,C,n-1)
n=int(input("请输入盘子的数量"))
if n<=5:
    d1 = datetime.datetime.now()
    step = step + 1
    f("A","B","C",n)
    d2 = datetime.datetime.now()
    print("一共用时：",d2-d1,"秒")
    print("总共搬运次数：",step)
else:
    print("请输入5以下的数字")

                                                                                                                              