{"id": 279, "courseSlug": "jkbsjyjsv1", "publish": 1, "statist": {"chapters": 5}, "indics": [{"collapse": false, "openTeam": [159], "sections": [{"ext": "ipynb", "index": false, "status": true, "sectionID": 8555, "sectionName": "我们身边的数据", "sectionType": "AI", "sectionIndex": 1, "sectionTitle": "我们身边的数据"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8556, "sectionName": "数据的计算", "sectionType": "AI", "sectionIndex": 2, "sectionTitle": "数据的计算"}], "chapterName": "初始数据与计算", "chapterIndex": 1, "chapterTitle": "初始数据与计算"}, {"collapse": false, "openTeam": [46, 44], "sections": [{"ext": "ipynb", "index": false, "status": true, "sectionID": 8557, "sectionName": "计算机解决问题的过程", "sectionType": "AI", "sectionIndex": 1, "sectionTitle": "计算机解决问题的过程"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8558, "sectionName": "做出判断的分支", "sectionType": "AI", "sectionIndex": 2, "sectionTitle": "做出判断的分支"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8559, "sectionName": "周而复始的循环", "sectionType": "AI", "sectionIndex": 3, "sectionTitle": "周而复始的循环"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8560, "sectionName": "可以复用的代码", "sectionType": "AI", "sectionIndex": 4, "sectionTitle": "可以复用的代码"}], "chapterName": "编程计算", "chapterIndex": 2, "chapterTitle": "编程计算"}, {"collapse": false, "openTeam": [46, 44], "sections": [{"ext": "ipynb", "index": false, "status": true, "sectionID": 8561, "sectionName": "数据编码", "sectionType": "AI", "sectionIndex": 1, "sectionTitle": "数据编码"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8562, "sectionName": "数据与结构", "sectionType": "AI", "sectionIndex": 2, "sectionTitle": "数据与结构"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8563, "sectionName": "数据与系统", "sectionType": "AI", "sectionIndex": 3, "sectionTitle": "数据与系统"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8564, "sectionName": "加密与解密", "sectionType": "AI", "sectionIndex": 4, "sectionTitle": "加密与解密"}], "chapterName": "认识数据", "chapterIndex": 3, "chapterTitle": "认识数据"}, {"collapse": false, "openTeam": [46, 44], "sections": [{"ext": "ipynb", "index": false, "status": true, "sectionID": 8565, "sectionName": "算法及其特征", "sectionType": "AI", "sectionIndex": 1, "sectionTitle": "算法及其特征"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8566, "sectionName": "数值计算", "sectionType": "AI", "sectionIndex": 2, "sectionTitle": "数值计算"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8567, "sectionName": "非数值计算", "sectionType": "AI", "sectionIndex": 3, "sectionTitle": "非数值计算"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8568, "sectionName": "综合问题的解决", "sectionType": "AI", "sectionIndex": 4, "sectionTitle": "综合问题的解决"}], "chapterName": "计算与问题解决", "chapterIndex": 4, "chapterTitle": "计算与问题解决"}, {"collapse": false, "openTeam": [46, 44], "sections": [{"ext": "ipynb", "status": true, "sectionID": 8569, "sectionName": "走近数据分析剖析垃圾分类智能问答机器人", "sectionType": "AI", "sectionIndex": 1, "sectionTitle": "走近数据分析：剖析垃圾分类智能问答机器人"}, {"ext": "ipynb", "index": false, "status": true, "sectionID": 8571, "sectionName": "探秘人工智能", "sectionType": "AI", "sectionIndex": 2, "sectionTitle": "探秘人工智能", "isProjectBook": false}], "chapterName": "数据分析与人工智能", "chapterIndex": 5, "chapterTitle": "数据分析与人工智能"}], "containerInfo": {"image": "registry.cn-hangzhou.aliyuncs.com/haixr/myjupyter", "cpuLimit": 2, "memoryRequest": "8Gi"}, "createrID": 1, "courseType": "必修课", "programLanguage": "Python", "questionAnswer": 1, "teams": [159], "historyTeams": [], "teachers": [1, 3013, 5256], "uploadCount": 0, "downloadCount": 58, "created_at": "2023-11-15T06:17:49.000Z", "updated_at": "2025-10-23T09:10:22.000Z", "deleted_at": null, "allowPaste": true, "allowCopy": true, "courseDescription": "高中信息技术教材配套课程", "courseName": "（教科版-必修1）数据与计算", "saveCode": true, "saveRunResult": true, "chapterNameMap": {"初始数据与计算": "初始数据与计算", "编程计算": "编程计算", "认识数据": "认识数据", "计算与问题解决": "计算与问题解决", "数据分析与人工智能": "数据分析与人工智能"}, "schoolSlug": "csxx", "creater": {"name": "氦星人", "username": "hxr", "avatar": null}}