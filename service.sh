#!/bin/bash

flag=1

prog_exit()
{
    echo '开始优雅退出应用...';
    
    # 优雅退出egg
    EGG_SERVER_ENV=${WORK_MODE} npm stop

    # 执行清理
    node grace_exit.js

    # 退出
    flag = 0
}

# 注册中断处理函数
trap "prog_exit" TERM

WORKERS=0

case $WORK_MODE in
    test)
        WORKERS=1
        ;;
    faye)
        WORKERS=4
        ;;
    dev)
        WORKERS=4
        ;;
    prod)
        # 备份数据库
        #mysqldump -hmysql-master -uroot -pyopu123 institutions|gzip -c|cat > /data/file/db/institutions_$(date '+%Y-%m-%d_%H-%M-%S').sql.tar.gz
        WORKERS=4
        ;;
    *)
        echo '没有设定环境变量WORK_MODE，其值必须为test、dev或prod中一个！'
        exit 9
        ;;
esac

# 启动egg，限制占据过多内存
nohup bash -c "EGG_SERVER_ENV=${WORK_MODE} npm start -- --workers=${WORKERS}" &

while [ $flag -ne 0 ];do
    sleep 3;
done;