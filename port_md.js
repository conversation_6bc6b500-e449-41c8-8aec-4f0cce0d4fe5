const fs = require( "mz/fs");

const controllerFileMap = {};

//控制器
async function getControllerMap(controllerName)
{
  //小写
  controllerName = controllerName.replace(/([A-Z])/g, ($1) => `_${$1.toLowerCase()}`);
  let controllerMap = controllerFileMap[controllerName];
  if(controllerMap)
  {
    return controllerMap;
  }
 
  const controllerFileName = `${__dirname}/app/controller/${controllerName}.js`;
  controllerText = await fs.readFile(controllerFileName, 'utf8');

  if(!controllerText)
  {
    return null;
  }

  //分析 控制器文件
  const rows = controllerText.split("\n");

  let functionSpace = null;
  let functionRows = null;
  controllerMap = {};

  for(const row of rows)
  {
    if(row.length === 0)
    {
      continue;
    }

    // 如果是命名行，增加一个数据，并给出记录
    const functionNameParts = row.match(/\*([\w\d]+)/);
    if(functionNameParts)
    {
      const functionName = functionNameParts[1];
      functionRows = [];

      controllerMap[functionName] = {
        functionName,
        functionRows
      };

      continue;
    }

    if(!functionRows)
    {
      continue;
    }
    
    // 非命名行
    functionRows.push(row);
  }

  controllerFileMap[controllerName] = controllerMap;
  return controllerMap;
}

function parseJSONLikeMap(str)
{
  // 参数描述数组 ["type: 'string'", "required: true"]
  const parameterParts = str.match(/\'*[\w\d_]+\'*\s*:\s*[^,]+/g);

  const parameterDescMap = {};
  if(!parameterParts)
  {
    return parameterDescMap;
  }

  parameterParts.forEach(parameterPart => {
    const parameterPartPairs = parameterPart.match(/\'*([\w\d_]+)\'*\s*:\s*([^\}]+)/);
    try
    {
      parameterDescMap[parameterPartPairs[1]] = JSON.parse(parameterPartPairs[2].replace(/'/g, '"'));
    }
    catch(e)
    {
      parameterDescMap[parameterPartPairs[1]] = parameterPartPairs[2];
    }
  });

  return parameterDescMap;
}

function parseParams(lastRow, paramParts, type, parameters) {
  if(!paramParts)
  {
    return;
  }

  const bodyParamText = paramParts[1];
  if(!bodyParamText)
  {
    return;
  }

  //说明
  let descriptions = [];
  if(lastRow)
  {
    descriptions = lastRow.replace('//', '').split(/[,，]/).map(description => description.trim());
  }
  
  const bodyParams = bodyParamText.split(",").map(bodyParam => bodyParam.trim());

  for(const index in bodyParams)
  {
    const bodyParam = bodyParams[index];
    parameters.push({
      name: bodyParam,
      description: index < descriptions.length ? descriptions[index]: null,
      type: type
    });
  } 
}

function putParameters(parameterSection, markdowns)
{
  markdowns.push(`>`);
  markdowns.push(`> ${parameterSection.type}`);

  if(parameterSection.type == 'BODY内文件上传')
  {
    return;
  }

  markdowns.push(`> 字段名|描述|类型|必要字段|`);
  markdowns.push(`> ---|---|---|---|`);
  for(const parameter of parameterSection.children)
  {
    markdowns.push(`> ${parameter.name}|${parameter.description}|${parameter.itemType ? parameter.type + '(' + parameter.itemType + ')': parameter.type}|${parameter.required ? '必要字段': ''}`)
  }
}

function getIO(funcName, rows){
  let lastRow = null;
  const parameterSections = [];
  const parameterSectionMap = {};
  const validateRuleMap = {};
  const parameterMap = {};
  const results = [];

  for(let row of rows)
  {
    row = row.trim();

    // 判断是否是规则
    const ruleParts = row.match(/([\w_\d]+)\s*:\s*(\{\s*type\s*:[^\}]+\})/);
    if(ruleParts)
    {
      // 参数名
      const parameterName = ruleParts[1];

      // 参数描述 {type: 'string', required: true}
      const parameterDesc = ruleParts[2];
      const parameterMap = parseJSONLikeMap(parameterDesc);

      let description = null;
      if(lastRow && lastRow[0] == '/')
      {
        description = lastRow.substr(2).trim();
      }

      validateRuleMap[parameterName] = {
        description,
        required: true,
        ...parameterMap
      };
    }
    else {
      // 判断参数
      let parameters = [];

      // 获取文件流
      if(row.match(/ctx\.getFileStream\(/))
      {
        parameters.push({
          type: "BODY内文件上传"
        });
      }

      // URL参数
      const urlParams = row.match(/\{[^\}]+\}\s*=\s*ctx.params/);
      parseParams(lastRow, urlParams, 'URL参数', parameters);

      // URL查询
      const queryParamParts = row.match(/\{([^\}]+)\}\s*=\s*ctx.query/);
      parseParams(lastRow, queryParamParts, 'URL查询', parameters);

      // BODY
      const bodyParamParts = row.match(/\{([^\}]+)\}\s*=\s*ctx.request.body/);
      parseParams(lastRow, bodyParamParts, 'BODY参数', parameters);

      if(parameters.length > 0)
      {
        // 加入参数
        for(const parameter of parameters)
        {
          let parameterSection = parameterSectionMap[parameter.type];
          if(!parameterSection)
          {
            parameterSection = {
              type: parameter.type,
              children: []
            };

            parameterSections.push(parameterSection);
            parameterSectionMap[parameterSection.type] = parameterSection;
          }

          parameterSection.children.push(parameter);

          if(parameterMap[parameter.name])
          {
            console.error(`函数${funcName}参数${parameter.name}出现重复，请消除！`);
            continue;
          }

          parameterMap[parameter.name] = parameter;
        }

        // 合并安全参数到参数上
        for(const parameterName in parameterMap)
        {
          const parameter = parameterMap[parameterName]
          const validateRule = validateRuleMap[parameterName];
          if(!validateRule)
          {
            continue;
          }

          for(const key in validateRule)
          {
            parameter[key] = validateRule[key];
          }
        }
      }

      // 判断返回值
      const resultParts = row.match(/ctx\.body\s*=\s*(\{.+\});*$/);
      if(resultParts)
      {
        const bodyContent = resultParts[1].trim().replace(/,?\s*data\s*:\s*[^\s}]+/, '');
        const resultMap = parseJSONLikeMap(bodyContent);
        if(!resultMap.code && resultMap.code !== 0)
        {
          console.error(`${row} 中返回码code字段没有定义`);
        }

        if(!resultMap.message)
        {
          console.error(`${row} 中返回值message字段没有定义`);
        }

        resultMap.bodyContent = bodyContent;
        if(lastRow[0] == '/')
        {
          try
          {
            resultMap.bodyContent = JSON.stringify({
              ...eval(`tmp = ` + bodyContent),
              data: eval(`tmp = ` + lastRow.substr(2).trim())
            });
          }
          catch(e)
          {
            console.error(`如果不返回数据，请将函数 ${funcName} 中 ${row} 上方的注释 ${lastRow}删除，否则请写为返回数据格式，并确定内容格式正确！`);
          }
        }

        results.push(resultMap);
      }
    }

    lastRow = row;
  }

  return {
    parameterSections,
    results
  };
}

async function parseFiles()
{
  // 读入文件
  const routerFilePath = __dirname + '/app/router.js';
  const routerText = await fs.readFile(routerFilePath, 'utf8');
  //获取大括号之内  
  const rowsText = routerText.match(/\{([^\}]+)\}/)[1];  
  //分割成行
  const rows = rowsText.split("\n");

  // 输出Markdown
  const title = '# 接口文档';
  const markdowns = [title];
  //  抽取title第一个字符 赋给paddingHash
  const paddingHash = title.substr(0, title.indexOf(' '));

  let portName = null;
  for(let row of rows)
  { 
    //移除前后空格
    row = row.trim();
    //空白行
    if(row.length == 0)
    {
      continue;
    }

    //描述行
    if(row[0] == '/')
    {
      // 描述行，增加记录
      //  删去/
      let subTitle = row.substr(3);
      if(subTitle[0] != ' ' && subTitle[0] != '#')
      {
        portName = subTitle;
      }
      // 接口行，增加表格
      else
      {
        subTitle = paddingHash + subTitle;
        markdowns.push("");
        markdowns.push(subTitle);
      }

      continue;
    }


    // 接口行。获取接口方法，URL，参数，输出，注释
    const request = row.match(/\.([\w]+)\(['"]([^'"]+)['"]\s*,\s*['"]([^'"]+)/);     
    if(request == null) {
      const ioMatchResult = row.match(/\.([\w]+)\.([\w]+)\(['"]([^'"]+)['"]\s*,\s*([\w\.]+)/);
      var method = ioMatchResult[2]; 
      var url = ioMatchResult[3];
      var linkTo = ioMatchResult[4]; 
    }
    
    else {
      var method = request[1];       //接口方法
      var url = request[2];          //接口URL
      var linkTo = request[3];       //控制器.函数
    }

    if(!portName)
    {
      console.error(`在接口${method} ${url}之前必须写注释！`);
      continue;
    }

    if(!linkTo)
    {
      console.error(`在接口${portName} ${method} ${url}上必须定义链接到的控制器和方法！`);
      continue;
    }

    const linkTos = linkTo.split(".");
    if(linkTos.length !== 2)
    {
      console.error(`在接口${portName} ${method} ${url}上必须定义链接到的控制器和方法！`);
      continue;
    }

    const controllerName = linkTos[0];
    const funcName = linkTos[1];

    // 加载控制器代码
    const controllerMap = await getControllerMap(controllerName);

    // 获取对应函数文本
    const controller = controllerMap[funcName];

    // 分析获取文本中的参数集合
    const { parameterSections, results } = getIO(controller.functionName, controller.functionRows);

    markdowns.push(`#### ${portName}`);
    markdowns.push(`> 方法 路径|控制器|函数|`);
    markdowns.push(`> ---|---|---|`);
    markdowns.push(`> ${method.toUpperCase()} ${url}|${controllerName}|${funcName}|`);
    
    for(const parameterSection of parameterSections)
    {
      putParameters(parameterSection, markdowns);
    }

    results.sort((a, b) => a.code - b.code);
    if(results && results.length > 0)
    {
      markdowns.push('> ');
      markdowns.push('> 结果：');
      markdowns.push('> 返回码|描述|示例数据');
      markdowns.push('> ---|---|---|');

      for(const result of results)
      {
        markdowns.push(`> ${result.code}|${result.message}|${result.bodyContent}`);
      }
    }
  }

  await fs.writeFile("port.md", markdowns.join("\r\n"));
}

parseFiles();