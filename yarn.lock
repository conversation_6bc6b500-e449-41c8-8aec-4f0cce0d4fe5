# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.1.0":
  version "2.2.0"
  resolved "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.2.0.tgz"
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@babel/code-frame@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/highlight" "7.0.0-beta.44"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz"
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/code-frame@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.10.4.tgz"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.20.0":
  version "7.20.5"
  resolved "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.20.5.tgz"

"@babel/core@^7.11.6", "@babel/core@^7.12.3":
  version "7.20.5"
  resolved "https://registry.npmmirror.com/@babel/core/-/core-7.20.5.tgz"
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-module-transforms" "^7.20.2"
    "@babel/helpers" "^7.20.5"
    "@babel/parser" "^7.20.5"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.5"
    "@babel/types" "^7.20.5"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.1"
    semver "^6.3.0"

"@babel/generator@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/generator/download/@babel/generator-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1604443729171&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/types" "7.0.0-beta.44"
    jsesc "^2.5.1"
    lodash "^4.2.0"
    source-map "^0.5.0"
    trim-right "^1.0.1"

"@babel/generator@^7.12.5", "@babel/generator@^7.20.5", "@babel/generator@^7.4.0", "@babel/generator@^7.7.2":
  version "7.20.5"
  resolved "https://registry.npmmirror.com/@babel/generator/-/generator-7.20.5.tgz"
  dependencies:
    "@babel/types" "^7.20.5"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/helper-compilation-targets@^7.20.0":
  version "7.20.0"
  resolved "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz"
  dependencies:
    "@babel/compat-data" "^7.20.0"
    "@babel/helper-validator-option" "^7.18.6"
    browserslist "^4.21.3"
    semver "^6.3.0"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz"

"@babel/helper-function-name@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/helper-get-function-arity" "7.0.0-beta.44"
    "@babel/template" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-function-name@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.10.4.tgz"
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-function-name@^7.19.0":
  version "7.19.0"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz"
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/types" "^7.19.0"

"@babel/helper-get-function-arity@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-get-function-arity@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.10.4.tgz"
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-imports@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.20.2":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.2"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.8.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz"

"@babel/helper-simple-access@^7.20.2":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz"
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-split-export-declaration@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-split-export-declaration@^7.11.0":
  version "7.11.0"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.11.0.tgz"
  dependencies:
    "@babel/types" "^7.11.0"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  version "7.19.4"
  resolved "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz"

"@babel/helper-validator-identifier@^7.10.4", "@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"

"@babel/helper-validator-option@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz"

"@babel/helpers@^7.20.5":
  version "7.20.6"
  resolved "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.20.6.tgz"
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.5"
    "@babel/types" "^7.20.5"

"@babel/highlight@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.0.0-beta.44.tgz"
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.10.4.tgz"
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz"
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.12.7", "@babel/parser@^7.14.7", "@babel/parser@^7.18.10", "@babel/parser@^7.20.5", "@babel/parser@^7.4.3":
  version "7.20.5"
  resolved "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.5.tgz"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.7.2":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.7.2":
  version "7.20.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.20.0.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/runtime-corejs3@^7.10.2":
  version "7.12.5"
  resolved "https://registry.npmmirror.com/@babel/runtime-corejs3/download/@babel/runtime-corejs3-7.12.5.tgz"
  dependencies:
    core-js-pure "^3.0.0"
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.10.2", "@babel/runtime@^7.11.2":
  version "7.12.5"
  resolved "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.12.5.tgz?cache=0&sync_timestamp=1604443606981&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.12.5.tgz"
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1605904887618&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftemplate%2Fdownload%2F%40babel%2Ftemplate-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    lodash "^4.2.0"

"@babel/template@^7.10.4", "@babel/template@^7.18.10", "@babel/template@^7.3.3", "@babel/template@^7.4.0":
  version "7.18.10"
  resolved "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/traverse@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1606251950708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.0.0-beta.44.tgz"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/generator" "7.0.0-beta.44"
    "@babel/helper-function-name" "7.0.0-beta.44"
    "@babel/helper-split-export-declaration" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    debug "^3.1.0"
    globals "^11.1.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

"@babel/traverse@^7.20.1", "@babel/traverse@^7.20.5", "@babel/traverse@^7.7.2":
  version "7.20.5"
  resolved "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.5.tgz"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.5"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.20.5"
    "@babel/types" "^7.20.5"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/traverse@^7.4.3":
  version "7.12.9"
  resolved "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.12.9.tgz?cache=0&sync_timestamp=1606251950708&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.12.9.tgz"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.12.5"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/parser" "^7.12.7"
    "@babel/types" "^7.12.7"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.19"

"@babel/types@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.0.0-beta.44.tgz?cache=0&sync_timestamp=1605904888635&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.0.0-beta.44.tgz"
  dependencies:
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^2.0.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.10", "@babel/types@^7.18.6", "@babel/types@^7.19.0", "@babel/types@^7.20.2", "@babel/types@^7.20.5", "@babel/types@^7.3.0", "@babel/types@^7.3.3":
  version "7.20.5"
  resolved "https://registry.npmmirror.com/@babel/types/-/types-7.20.5.tgz"
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@babel/types@^7.10.4", "@babel/types@^7.11.0", "@babel/types@^7.12.7", "@babel/types@^7.4.0":
  version "7.12.7"
  resolved "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.12.7.tgz?cache=0&sync_timestamp=1605904888635&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.12.7.tgz"
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    lodash "^4.17.19"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry.npmmirror.com/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"

"@eggjs/router@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmmirror.com/@eggjs/router/download/@eggjs/router-2.0.0.tgz"
  dependencies:
    co "^4.6.0"
    debug "^3.1.0"
    http-errors "^1.3.1"
    inflection "^1.12.0"
    is-type-of "^1.2.1"
    koa-compose "^3.0.0"
    koa-convert "^1.2.0"
    methods "^1.0.1"
    path-to-regexp "^1.1.1"
    urijs "^1.19.0"
    utility "^1.15.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmmirror.com/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "https://registry.npmmirror.com/@istanbuljs/schema/-/schema-0.1.3.tgz"

"@jest/console@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/console/-/console-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^29.3.1"
    jest-util "^29.3.1"
    slash "^3.0.0"

"@jest/core@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/core/-/core-29.3.1.tgz"
  dependencies:
    "@jest/console" "^29.3.1"
    "@jest/reporters" "^29.3.1"
    "@jest/test-result" "^29.3.1"
    "@jest/transform" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^29.2.0"
    jest-config "^29.3.1"
    jest-haste-map "^29.3.1"
    jest-message-util "^29.3.1"
    jest-regex-util "^29.2.0"
    jest-resolve "^29.3.1"
    jest-resolve-dependencies "^29.3.1"
    jest-runner "^29.3.1"
    jest-runtime "^29.3.1"
    jest-snapshot "^29.3.1"
    jest-util "^29.3.1"
    jest-validate "^29.3.1"
    jest-watcher "^29.3.1"
    micromatch "^4.0.4"
    pretty-format "^29.3.1"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/environment/-/environment-29.3.1.tgz"
  dependencies:
    "@jest/fake-timers" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    jest-mock "^29.3.1"

"@jest/expect-utils@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/expect-utils/-/expect-utils-29.3.1.tgz"
  dependencies:
    jest-get-type "^29.2.0"

"@jest/expect@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/expect/-/expect-29.3.1.tgz"
  dependencies:
    expect "^29.3.1"
    jest-snapshot "^29.3.1"

"@jest/fake-timers@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/fake-timers/-/fake-timers-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    "@sinonjs/fake-timers" "^9.1.2"
    "@types/node" "*"
    jest-message-util "^29.3.1"
    jest-mock "^29.3.1"
    jest-util "^29.3.1"

"@jest/globals@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/globals/-/globals-29.3.1.tgz"
  dependencies:
    "@jest/environment" "^29.3.1"
    "@jest/expect" "^29.3.1"
    "@jest/types" "^29.3.1"
    jest-mock "^29.3.1"

"@jest/reporters@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/reporters/-/reporters-29.3.1.tgz"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^29.3.1"
    "@jest/test-result" "^29.3.1"
    "@jest/transform" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@jridgewell/trace-mapping" "^0.3.15"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^5.1.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^29.3.1"
    jest-util "^29.3.1"
    jest-worker "^29.3.1"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^29.0.0":
  version "29.0.0"
  resolved "https://registry.npmmirror.com/@jest/schemas/-/schemas-29.0.0.tgz"
  dependencies:
    "@sinclair/typebox" "^0.24.1"

"@jest/source-map@^29.2.0":
  version "29.2.0"
  resolved "https://registry.npmmirror.com/@jest/source-map/-/source-map-29.2.0.tgz"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.15"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/test-result/-/test-result-29.3.1.tgz"
  dependencies:
    "@jest/console" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/test-sequencer/-/test-sequencer-29.3.1.tgz"
  dependencies:
    "@jest/test-result" "^29.3.1"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.3.1"
    slash "^3.0.0"

"@jest/transform@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/transform/-/transform-29.3.1.tgz"
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.3.1"
    "@jridgewell/trace-mapping" "^0.3.15"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.3.1"
    jest-regex-util "^29.2.0"
    jest-util "^29.3.1"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.1"

"@jest/types@^25.5.0":
  version "25.5.0"
  resolved "https://registry.npmmirror.com/@jest/types/-/types-25.5.0.tgz"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    chalk "^3.0.0"

"@jest/types@^29.3.1":
  version "29.3.1"
  resolved "https://registry.npmmirror.com/@jest/types/-/types-29.3.1.tgz"
  dependencies:
    "@jest/schemas" "^29.0.0"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.1.0":
  version "0.1.1"
  resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.2"
  resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz"

"@jridgewell/sourcemap-codec@1.4.14", "@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.14"
  resolved "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.15", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.17"
  resolved "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://registry.npmmirror.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.scandir@2.1.3":
  version "2.1.3"
  resolved "https://registry.npmmirror.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.3.tgz"
  dependencies:
    "@nodelib/fs.stat" "2.0.3"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.3", "@nodelib/fs.stat@^2.0.2":
  version "2.0.3"
  resolved "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.3.tgz"

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"

"@nodelib/fs.walk@^1.2.3":
  version "1.2.4"
  resolved "https://registry.npmmirror.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.4.tgz"
  dependencies:
    "@nodelib/fs.scandir" "2.1.3"
    fastq "^1.6.0"

"@sinclair/typebox@^0.24.1":
  version "0.24.51"
  resolved "https://registry.npmmirror.com/@sinclair/typebox/-/typebox-0.24.51.tgz"

"@sinonjs/commons@^1.7.0":
  version "1.8.6"
  resolved "https://registry.npmmirror.com/@sinonjs/commons/-/commons-1.8.6.tgz"
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^9.1.2":
  version "9.1.2"
  resolved "https://registry.npmmirror.com/@sinonjs/fake-timers/-/fake-timers-9.1.2.tgz"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@tokenizer/token@^0.1.1":
  version "0.1.1"
  resolved "https://registry.npmmirror.com/@tokenizer/token/-/token-0.1.1.tgz#f0d92c12f87079ddfd1b29f614758b9696bc29e3"

"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmmirror.com/@tokenizer/token/-/token-0.3.0.tgz#fe98a93fe789247e998c75e74e9c7c63217aa276"

"@types/accepts@*", "@types/accepts@^1.3.5":
  version "1.3.5"
  resolved "https://registry.npmmirror.com/@types/accepts/download/@types/accepts-1.3.5.tgz?cache=0&sync_timestamp=1605052791762&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Faccepts%2Fdownload%2F%40types%2Faccepts-1.3.5.tgz"
  dependencies:
    "@types/node" "*"

"@types/babel__core@^7.1.14":
  version "7.1.20"
  resolved "https://registry.npmmirror.com/@types/babel__core/-/babel__core-7.1.20.tgz"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.4"
  resolved "https://registry.npmmirror.com/@types/babel__generator/-/babel__generator-7.6.4.tgz"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  resolved "https://registry.npmmirror.com/@types/babel__template/-/babel__template-7.4.1.tgz"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.18.3"
  resolved "https://registry.npmmirror.com/@types/babel__traverse/-/babel__traverse-7.18.3.tgz"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/bluebird@*":
  version "3.5.33"
  resolved "https://registry.npmmirror.com/@types/bluebird/download/@types/bluebird-3.5.33.tgz"

"@types/body-parser@*":
  version "1.19.0"
  resolved "https://registry.npmmirror.com/@types/body-parser/download/@types/body-parser-1.19.0.tgz"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.33"
  resolved "https://registry.npmmirror.com/@types/connect/download/@types/connect-3.4.33.tgz?cache=0&sync_timestamp=1605052812480&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fconnect%2Fdownload%2F%40types%2Fconnect-3.4.33.tgz"
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.3"
  resolved "https://registry.npmmirror.com/@types/content-disposition/download/@types/content-disposition-0.5.3.tgz?cache=0&sync_timestamp=1605052778076&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fcontent-disposition%2Fdownload%2F%40types%2Fcontent-disposition-0.5.3.tgz"

"@types/continuation-local-storage@*":
  version "3.2.2"
  resolved "https://registry.npmmirror.com/@types/continuation-local-storage/download/@types/continuation-local-storage-3.2.2.tgz"
  dependencies:
    "@types/node" "*"

"@types/cookiejar@*":
  version "2.1.2"
  resolved "https://registry.npmmirror.com/@types/cookiejar/download/@types/cookiejar-2.1.2.tgz?cache=0&sync_timestamp=1605054312699&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fcookiejar%2Fdownload%2F%40types%2Fcookiejar-2.1.2.tgz"

"@types/cookies@*":
  version "0.7.5"
  resolved "https://registry.npmmirror.com/@types/cookies/download/@types/cookies-0.7.5.tgz?cache=0&sync_timestamp=1605052778766&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fcookies%2Fdownload%2F%40types%2Fcookies-0.7.5.tgz"
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/dargs@^5.1.0":
  version "5.1.0"
  resolved "https://registry.npmmirror.com/@types/dargs/download/@types/dargs-5.1.0.tgz"

"@types/debug@^4.1.7":
  version "4.1.7"
  resolved "https://registry.npmmirror.com/@types/debug/-/debug-4.1.7.tgz"
  dependencies:
    "@types/ms" "*"

"@types/depd@^1.1.32":
  version "1.1.32"
  resolved "https://registry.npmmirror.com/@types/depd/download/@types/depd-1.1.32.tgz"
  dependencies:
    "@types/node" "*"

"@types/empower@*":
  version "1.2.30"
  resolved "https://registry.npmmirror.com/@types/empower/download/@types/empower-1.2.30.tgz?cache=0&sync_timestamp=1605054079935&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fempower%2Fdownload%2F%40types%2Fempower-1.2.30.tgz"

"@types/eslint-visitor-keys@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmmirror.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz"

"@types/express-serve-static-core@*":
  version "4.17.14"
  resolved "https://registry.npmmirror.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.14.tgz?cache=0&sync_timestamp=1606163141518&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fexpress-serve-static-core%2Fdownload%2F%40types%2Fexpress-serve-static-core-4.17.14.tgz"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  version "4.17.9"
  resolved "https://registry.npmmirror.com/@types/express/download/@types/express-4.17.9.tgz?cache=0&sync_timestamp=1605057477768&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fexpress%2Fdownload%2F%40types%2Fexpress-4.17.9.tgz"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "*"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  version "7.1.3"
  resolved "https://registry.npmmirror.com/@types/glob/download/@types/glob-7.1.3.tgz?cache=0&sync_timestamp=1605053345113&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fglob%2Fdownload%2F%40types%2Fglob-7.1.3.tgz"
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/graceful-fs@^4.1.3":
  version "4.1.5"
  resolved "https://registry.npmmirror.com/@types/graceful-fs/-/graceful-fs-4.1.5.tgz"
  dependencies:
    "@types/node" "*"

"@types/http-assert@*":
  version "1.5.1"
  resolved "https://registry.npmmirror.com/@types/http-assert/download/@types/http-assert-1.5.1.tgz"

"@types/http-errors@*":
  version "1.8.0"
  resolved "https://registry.npmmirror.com/@types/http-errors/download/@types/http-errors-1.8.0.tgz"

"@types/ioredis@^4.0.10":
  version "4.17.8"
  resolved "https://registry.npmmirror.com/@types/ioredis/download/@types/ioredis-4.17.8.tgz"
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.4"
  resolved "https://registry.npmmirror.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz"

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://registry.npmmirror.com/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  version "1.1.2"
  resolved "https://registry.npmmirror.com/@types/istanbul-reports/-/istanbul-reports-1.1.2.tgz"
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "https://registry.npmmirror.com/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.3":
  version "7.0.6"
  resolved "https://registry.npmmirror.com/@types/json-schema/download/@types/json-schema-7.0.6.tgz?cache=0&sync_timestamp=1605053861867&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fjson-schema%2Fdownload%2F%40types%2Fjson-schema-7.0.6.tgz"

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmmirror.com/@types/json5/download/@types/json5-0.0.29.tgz"

"@types/keygrip@*":
  version "1.0.2"
  resolved "https://registry.npmmirror.com/@types/keygrip/download/@types/keygrip-1.0.2.tgz?cache=0&sync_timestamp=1605053947249&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fkeygrip%2Fdownload%2F%40types%2Fkeygrip-1.0.2.tgz"

"@types/koa-compose@*":
  version "3.2.5"
  resolved "https://registry.npmmirror.com/@types/koa-compose/download/@types/koa-compose-3.2.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fkoa-compose%2Fdownload%2F%40types%2Fkoa-compose-3.2.5.tgz"
  dependencies:
    "@types/koa" "*"

"@types/koa-router@^7.0.40":
  version "7.4.1"
  resolved "https://registry.npmmirror.com/@types/koa-router/download/@types/koa-router-7.4.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fkoa-router%2Fdownload%2F%40types%2Fkoa-router-7.4.1.tgz"
  dependencies:
    "@types/koa" "*"

"@types/koa@*", "@types/koa@^2.0.48":
  version "2.11.6"
  resolved "https://registry.npmmirror.com/@types/koa/download/@types/koa-2.11.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fkoa%2Fdownload%2F%40types%2Fkoa-2.11.6.tgz"
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/lodash@*":
  version "4.14.165"
  resolved "https://registry.npmmirror.com/@types/lodash/download/@types/lodash-4.14.165.tgz?cache=0&sync_timestamp=1604600563068&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Flodash%2Fdownload%2F%40types%2Flodash-4.14.165.tgz"

"@types/mime@*":
  version "2.0.3"
  resolved "https://registry.npmmirror.com/@types/mime/download/@types/mime-2.0.3.tgz"

"@types/minimatch@*":
  version "3.0.3"
  resolved "https://registry.npmmirror.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz"

"@types/ms@*":
  version "0.7.31"
  resolved "https://registry.npmmirror.com/@types/ms/-/ms-0.7.31.tgz"

"@types/node-fetch@^2.6.4":
  version "2.6.12"
  resolved "https://registry.npmmirror.com/@types/node-fetch/-/node-fetch-2.6.12.tgz#8ab5c3ef8330f13100a7479e2cd56d3386830a03"
  integrity sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node@*":
  version "14.14.10"
  resolved "https://registry.npmmirror.com/@types/node/download/@types/node-14.14.10.tgz"

"@types/node@^10.12.18":
  version "10.17.47"
  resolved "https://registry.npmmirror.com/@types/node/download/@types/node-10.17.47.tgz"

"@types/node@^18.11.18":
  version "18.19.103"
  resolved "https://registry.npmmirror.com/@types/node/-/node-18.19.103.tgz#9bbd31a54e240fc469cca409d7507ebc77536458"
  integrity sha512-hHTHp+sEz6SxFsp+SA+Tqrua3AbmlAw+Y//aEwdHrdZkYVRWdvWD3y5uPZ0flYOkgskaFWqZ/YGFm3FaFQ0pRw==
  dependencies:
    undici-types "~5.26.4"

"@types/power-assert-formatter@*":
  version "1.4.29"
  resolved "https://registry.npmmirror.com/@types/power-assert-formatter/download/@types/power-assert-formatter-1.4.29.tgz"

"@types/power-assert@^1.5.0":
  version "1.5.3"
  resolved "https://registry.npmmirror.com/@types/power-assert/download/@types/power-assert-1.5.3.tgz"
  dependencies:
    "@types/empower" "*"
    "@types/power-assert-formatter" "*"

"@types/prettier@^2.1.5":
  version "2.7.1"
  resolved "https://registry.npmmirror.com/@types/prettier/-/prettier-2.7.1.tgz"

"@types/qs@*":
  version "6.9.5"
  resolved "https://registry.npmmirror.com/@types/qs/download/@types/qs-6.9.5.tgz?cache=0&sync_timestamp=1605055106687&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.5.tgz"

"@types/range-parser@*":
  version "1.2.3"
  resolved "https://registry.npmmirror.com/@types/range-parser/download/@types/range-parser-1.2.3.tgz"

"@types/sequelize@^4.27.24":
  version "4.28.9"
  resolved "https://registry.npmmirror.com/@types/sequelize/download/@types/sequelize-4.28.9.tgz"
  dependencies:
    "@types/bluebird" "*"
    "@types/continuation-local-storage" "*"
    "@types/lodash" "*"
    "@types/validator" "*"

"@types/serve-static@*":
  version "1.13.8"
  resolved "https://registry.npmmirror.com/@types/serve-static/download/@types/serve-static-1.13.8.tgz?cache=0&sync_timestamp=1605657862811&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fserve-static%2Fdownload%2F%40types%2Fserve-static-1.13.8.tgz"
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "https://registry.npmmirror.com/@types/stack-utils/-/stack-utils-2.0.1.tgz"

"@types/superagent@*":
  version "4.1.10"
  resolved "https://registry.npmmirror.com/@types/superagent/download/@types/superagent-4.1.10.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fsuperagent%2Fdownload%2F%40types%2Fsuperagent-4.1.10.tgz"
  dependencies:
    "@types/cookiejar" "*"
    "@types/node" "*"

"@types/supertest@^2.0.7":
  version "2.0.10"
  resolved "https://registry.npmmirror.com/@types/supertest/download/@types/supertest-2.0.10.tgz"
  dependencies:
    "@types/superagent" "*"

"@types/validator@*", "@types/validator@^13.7.1":
  version "13.7.1"
  resolved "https://registry.npmmirror.com/@types/validator/-/validator-13.7.1.tgz"

"@types/yargs-parser@*":
  version "21.0.0"
  resolved "https://registry.npmmirror.com/@types/yargs-parser/-/yargs-parser-21.0.0.tgz"

"@types/yargs@^12.0.4":
  version "12.0.19"
  resolved "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-12.0.19.tgz?cache=0&sync_timestamp=1605657825304&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fyargs%2Fdownload%2F%40types%2Fyargs-12.0.19.tgz"

"@types/yargs@^15.0.0":
  version "15.0.14"
  resolved "https://registry.npmmirror.com/@types/yargs/-/yargs-15.0.14.tgz"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^17.0.8":
  version "17.0.17"
  resolved "https://registry.npmmirror.com/@types/yargs/-/yargs-17.0.17.tgz"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^2.0.0":
  version "2.34.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-2.34.0.tgz?cache=0&sync_timestamp=1606427805689&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Feslint-plugin%2Fdownload%2F%40typescript-eslint%2Feslint-plugin-2.34.0.tgz"
  dependencies:
    "@typescript-eslint/experimental-utils" "2.34.0"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.0.0"
    tsutils "^3.17.1"

"@typescript-eslint/experimental-utils@2.34.0":
  version "2.34.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-2.34.0.tgz?cache=0&sync_timestamp=1606427786779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Fexperimental-utils%2Fdownload%2F%40typescript-eslint%2Fexperimental-utils-2.34.0.tgz"
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/typescript-estree" "2.34.0"
    eslint-scope "^5.0.0"
    eslint-utils "^2.0.0"

"@typescript-eslint/parser@^2.0.0":
  version "2.34.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/parser/download/@typescript-eslint/parser-2.34.0.tgz?cache=0&sync_timestamp=1606427797902&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40typescript-eslint%2Fparser%2Fdownload%2F%40typescript-eslint%2Fparser-2.34.0.tgz"
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "2.34.0"
    "@typescript-eslint/typescript-estree" "2.34.0"
    eslint-visitor-keys "^1.1.0"

"@typescript-eslint/typescript-estree@2.34.0":
  version "2.34.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-2.34.0.tgz"
  dependencies:
    debug "^4.1.1"
    eslint-visitor-keys "^1.1.0"
    glob "^7.1.6"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^7.3.2"
    tsutils "^3.17.1"

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/abort-controller/-/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

accepts@^1.3.5:
  version "1.3.7"
  resolved "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz"
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-es7-plugin@^1.0.10, acorn-es7-plugin@^1.0.12:
  version "1.1.7"
  resolved "https://registry.npmmirror.com/acorn-es7-plugin/download/acorn-es7-plugin-1.1.7.tgz"

acorn-jsx@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/acorn-jsx/download/acorn-jsx-5.3.1.tgz"

acorn@^5.0.0:
  version "5.7.4"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-5.7.4.tgz?cache=0&sync_timestamp=1602534280466&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-5.7.4.tgz"

acorn@^6.0.7:
  version "6.4.2"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-6.4.2.tgz?cache=0&sync_timestamp=1602534280466&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-6.4.2.tgz"

address@>=0.0.1, address@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/address/download/address-1.1.2.tgz"

adm-zip@^0.5.5:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/adm-zip/download/adm-zip-0.5.5.tgz"

agent-base@4, agent-base@^4.2.0, agent-base@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/agent-base/download/agent-base-4.3.0.tgz?cache=0&sync_timestamp=1603479872755&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fagent-base%2Fdownload%2Fagent-base-4.3.0.tgz"
  dependencies:
    es6-promisify "^5.0.0"

agent-base@~4.2.1:
  version "4.2.1"
  resolved "https://registry.npmmirror.com/agent-base/download/agent-base-4.2.1.tgz?cache=0&sync_timestamp=1603479872755&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fagent-base%2Fdownload%2Fagent-base-4.2.1.tgz"
  dependencies:
    es6-promisify "^5.0.0"

agentkeepalive@^4.0.2:
  version "4.1.3"
  resolved "https://registry.npmmirror.com/agentkeepalive/download/agentkeepalive-4.1.3.tgz"
  dependencies:
    debug "^4.1.0"
    depd "^1.1.2"
    humanize-ms "^1.2.1"

agentkeepalive@^4.2.1:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/agentkeepalive/-/agentkeepalive-4.6.0.tgz#35f73e94b3f40bf65f105219c623ad19c136ea6a"
  integrity sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==
  dependencies:
    humanize-ms "^1.2.1"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/aggregate-error/download/aggregate-error-3.1.0.tgz"
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv@^6.10.2, ajv@^6.9.1:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1606043275341&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.6.tgz"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ali-rds@^3.0.1:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/ali-rds/-/ali-rds-3.4.0.tgz"
  dependencies:
    co-wrap-all "^1.0.0"
    debug "^2.2.0"
    mysql "^2.13.0"
    pify "^2.3.0"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/amdefine/-/amdefine-1.0.1.tgz"

ansi-colors@3.2.3:
  version "3.2.3"
  resolved "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-3.2.3.tgz"

ansi-colors@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-4.1.1.tgz"

ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-2.1.1.tgz"

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz"

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-4.1.1.tgz"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-2.2.1.tgz?cache=0&sync_timestamp=1601839226460&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-styles%2Fdownload%2Fansi-styles-2.2.1.tgz"

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1601839226460&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz"
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-5.2.0.tgz"

any-promise@^1.0.0, any-promise@^1.1.0, any-promise@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz"

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.2.tgz"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-transform@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/append-transform/download/append-transform-1.0.0.tgz"
  dependencies:
    default-require-extensions "^2.0.0"

archiver-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/archiver-utils/download/archiver-utils-2.1.0.tgz"
  dependencies:
    glob "^7.1.4"
    graceful-fs "^4.2.0"
    lazystream "^1.0.0"
    lodash.defaults "^4.2.0"
    lodash.difference "^4.5.0"
    lodash.flatten "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.union "^4.6.0"
    normalize-path "^3.0.0"
    readable-stream "^2.0.0"

archiver@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/archiver/download/archiver-5.3.0.tgz"
  dependencies:
    archiver-utils "^2.1.0"
    async "^3.2.0"
    buffer-crc32 "^0.2.1"
    readable-stream "^3.6.0"
    readdir-glob "^1.0.0"
    tar-stream "^2.2.0"
    zip-stream "^4.1.0"

archy@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/archy/download/archy-1.0.0.tgz"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz"
  dependencies:
    sprintf-js "~1.0.2"

aria-query@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmmirror.com/aria-query/download/aria-query-4.2.2.tgz"
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@babel/runtime-corejs3" "^7.10.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/arr-diff/download/arr-diff-4.0.0.tgz"

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/arr-flatten/download/arr-flatten-1.1.0.tgz"

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/arr-union/download/arr-union-3.1.0.tgz"

array-differ@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/array-differ/download/array-differ-1.0.0.tgz"

array-filter@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/array-filter/download/array-filter-1.0.0.tgz"

array-find@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/array-find/-/array-find-1.0.0.tgz"

array-includes@^3.1.1:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/array-includes/download/array-includes-3.1.2.tgz?cache=0&sync_timestamp=1606263198539&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray-includes%2Fdownload%2Farray-includes-3.1.2.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    get-intrinsic "^1.0.1"
    is-string "^1.0.5"

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/array-union/download/array-union-1.0.2.tgz"
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/array-union/download/array-union-2.1.0.tgz"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/array-uniq/download/array-uniq-1.0.3.tgz"

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npmmirror.com/array-unique/download/array-unique-0.3.2.tgz"

array.prototype.flat@^1.2.3:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/array.prototype.flat/download/array.prototype.flat-1.2.4.tgz?cache=0&sync_timestamp=1605688463641&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farray.prototype.flat%2Fdownload%2Farray.prototype.flat-1.2.4.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"

array.prototype.flatmap@^1.2.3:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/array.prototype.flatmap/download/array.prototype.flatmap-1.2.4.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    function-bind "^1.1.1"

arrify@^1.0.0, arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/arrify/download/arrify-1.0.1.tgz"

asap@*:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/asap/download/asap-2.0.6.tgz"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/assign-symbols/download/assign-symbols-1.0.0.tgz"

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "https://registry.npmmirror.com/ast-types-flow/download/ast-types-flow-0.0.7.tgz"

ast-types@0.x.x:
  version "0.14.2"
  resolved "https://registry.npmmirror.com/ast-types/download/ast-types-0.14.2.tgz"
  dependencies:
    tslib "^2.0.1"

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/astral-regex/download/astral-regex-1.0.0.tgz"

async@^3.2.0:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/async/download/async-3.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fasync%2Fdownload%2Fasync-3.2.1.tgz"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz"

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/atob/download/atob-2.1.2.tgz"

autod-egg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/autod-egg/download/autod-egg-1.1.0.tgz"

autod@^3.0.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/autod/download/autod-3.1.1.tgz"
  dependencies:
    babel-core "^6.26.0"
    babel-preset-env "^1.6.1"
    babel-preset-react "^6.24.1"
    babel-preset-stage-0 "^6.24.1"
    co "^4.6.0"
    colors "^1.1.2"
    commander "^2.11.0"
    crequire "^1.8.1"
    debug "^3.1.0"
    fs-readdir-recursive "^1.1.0"
    glob "^7.1.2"
    minimatch "^3.0.4"
    printable "^0.0.3"
    semver "^6.0.0"
    urllib "^2.25.1"

await-event@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/await-event/download/await-event-2.1.0.tgz"

await-first@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/await-first/download/await-first-1.0.0.tgz"
  dependencies:
    ee-first "^1.1.1"

aws-ssl-profiles@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/aws-ssl-profiles/-/aws-ssl-profiles-1.1.2.tgz#157dd77e9f19b1d123678e93f120e6f193022641"

axe-core@^4.0.2:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/axe-core/download/axe-core-4.1.1.tgz?cache=0&sync_timestamp=1606323489756&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faxe-core%2Fdownload%2Faxe-core-4.1.1.tgz"

axios@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/axios/-/axios-1.4.0.tgz#38a7bf1224cd308de271146038b551d725f0be1f"
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/axobject-query/download/axobject-query-2.2.0.tgz"

babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz"
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.26.0:
  version "6.26.3"
  resolved "https://registry.npmmirror.com/babel-core/download/babel-core-6.26.3.tgz"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-eslint@^8.2.6:
  version "8.2.6"
  resolved "https://registry.npmmirror.com/babel-eslint/download/babel-eslint-8.2.6.tgz?cache=0&sync_timestamp=1600349064486&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-eslint%2Fdownload%2Fbabel-eslint-8.2.6.tgz"
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/traverse" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    eslint-scope "3.7.1"
    eslint-visitor-keys "^1.0.0"

babel-generator@^6.26.0:
  version "6.26.1"
  resolved "https://registry.npmmirror.com/babel-generator/download/babel-generator-6.26.1.tgz"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-bindify-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz"
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-builder-react-jsx@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-helper-builder-react-jsx/download/babel-helper-builder-react-jsx-6.26.0.tgz"
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    esutils "^2.0.2"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-explode-class@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz"
  dependencies:
    babel-helper-bindify-decorators "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz"
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz"
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz"
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-helpers/download/babel-helpers-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-jest@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/babel-jest/-/babel-jest-29.3.1.tgz"
  dependencies:
    "@jest/transform" "^29.3.1"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.2.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmmirror.com/babel-messages/download/babel-messages-6.23.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://registry.npmmirror.com/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^29.2.0:
  version "29.2.0"
  resolved "https://registry.npmmirror.com/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.2.0.tgz"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz"

babel-plugin-syntax-async-generators@^6.5.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz"

babel-plugin-syntax-class-constructor-call@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-class-constructor-call/download/babel-plugin-syntax-class-constructor-call-6.18.0.tgz"

babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz"

babel-plugin-syntax-decorators@^6.13.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz"

babel-plugin-syntax-do-expressions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-do-expressions/download/babel-plugin-syntax-do-expressions-6.13.0.tgz"

babel-plugin-syntax-dynamic-import@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz"

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz"

babel-plugin-syntax-export-extensions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-export-extensions/download/babel-plugin-syntax-export-extensions-6.13.0.tgz"

babel-plugin-syntax-flow@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-flow/download/babel-plugin-syntax-flow-6.18.0.tgz"

babel-plugin-syntax-function-bind@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-function-bind/download/babel-plugin-syntax-function-bind-6.13.0.tgz"

babel-plugin-syntax-jsx@^6.3.13, babel-plugin-syntax-jsx@^6.8.0:
  version "6.18.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-jsx/download/babel-plugin-syntax-jsx-6.18.0.tgz?cache=0&sync_timestamp=1601268767997&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-plugin-syntax-jsx%2Fdownload%2Fbabel-plugin-syntax-jsx-6.18.0.tgz"

babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz"

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz"

babel-plugin-transform-async-generator-functions@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-generators "^6.5.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-async-to-generator@^6.22.0, babel-plugin-transform-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-constructor-call@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-class-constructor-call/download/babel-plugin-transform-class-constructor-call-6.24.1.tgz"
  dependencies:
    babel-plugin-syntax-class-constructor-call "^6.18.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-class-properties@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz"
  dependencies:
    babel-helper-explode-class "^6.24.1"
    babel-plugin-syntax-decorators "^6.13.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-do-expressions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-do-expressions/download/babel-plugin-transform-do-expressions-6.22.0.tgz"
  dependencies:
    babel-plugin-syntax-do-expressions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1:
  version "6.26.2"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.22.0, babel-plugin-transform-exponentiation-operator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz"
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-export-extensions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-export-extensions/download/babel-plugin-transform-export-extensions-6.22.0.tgz"
  dependencies:
    babel-plugin-syntax-export-extensions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-flow-strip-types@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-flow-strip-types/download/babel-plugin-transform-flow-strip-types-6.22.0.tgz"
  dependencies:
    babel-plugin-syntax-flow "^6.18.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-function-bind@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-function-bind/download/babel-plugin-transform-function-bind-6.22.0.tgz"
  dependencies:
    babel-plugin-syntax-function-bind "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.22.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz"
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.26.0"

babel-plugin-transform-react-display-name@^6.23.0:
  version "6.25.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-react-display-name/download/babel-plugin-transform-react-display-name-6.25.0.tgz"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx-self@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-react-jsx-self/download/babel-plugin-transform-react-jsx-self-6.22.0.tgz"
  dependencies:
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx-source@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-react-jsx-source/download/babel-plugin-transform-react-jsx-source-6.22.0.tgz"
  dependencies:
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-react-jsx/download/babel-plugin-transform-react-jsx-6.24.1.tgz"
  dependencies:
    babel-helper-builder-react-jsx "^6.24.1"
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@^6.22.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz"
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-env@^1.6.1:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/babel-preset-env/download/babel-preset-env-1.7.0.tgz"
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^3.2.6"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-preset-flow@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmmirror.com/babel-preset-flow/download/babel-preset-flow-6.23.0.tgz"
  dependencies:
    babel-plugin-transform-flow-strip-types "^6.22.0"

babel-preset-jest@^29.2.0:
  version "29.2.0"
  resolved "https://registry.npmmirror.com/babel-preset-jest/-/babel-preset-jest-29.2.0.tgz"
  dependencies:
    babel-plugin-jest-hoist "^29.2.0"
    babel-preset-current-node-syntax "^1.0.0"

babel-preset-react@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-preset-react/download/babel-preset-react-6.24.1.tgz"
  dependencies:
    babel-plugin-syntax-jsx "^6.3.13"
    babel-plugin-transform-react-display-name "^6.23.0"
    babel-plugin-transform-react-jsx "^6.24.1"
    babel-plugin-transform-react-jsx-self "^6.22.0"
    babel-plugin-transform-react-jsx-source "^6.22.0"
    babel-preset-flow "^6.23.0"

babel-preset-stage-0@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-preset-stage-0/download/babel-preset-stage-0-6.24.1.tgz"
  dependencies:
    babel-plugin-transform-do-expressions "^6.22.0"
    babel-plugin-transform-function-bind "^6.22.0"
    babel-preset-stage-1 "^6.24.1"

babel-preset-stage-1@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-preset-stage-1/download/babel-preset-stage-1-6.24.1.tgz"
  dependencies:
    babel-plugin-transform-class-constructor-call "^6.24.1"
    babel-plugin-transform-export-extensions "^6.22.0"
    babel-preset-stage-2 "^6.24.1"

babel-preset-stage-2@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz"
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-class-properties "^6.24.1"
    babel-plugin-transform-decorators "^6.24.1"
    babel-preset-stage-3 "^6.24.1"

babel-preset-stage-3@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmmirror.com/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz"
  dependencies:
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-generator-functions "^6.24.1"
    babel-plugin-transform-async-to-generator "^6.24.1"
    babel-plugin-transform-exponentiation-operator "^6.24.1"
    babel-plugin-transform-object-rest-spread "^6.22.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-register/download/babel-register-6.26.0.tgz"
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@^6.18.0, babel-runtime@^6.22.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-template/download/babel-template-6.26.0.tgz"
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-traverse/download/babel-traverse-6.26.0.tgz"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.19.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-types/download/babel-types-6.26.0.tgz"
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@7.0.0-beta.44:
  version "7.0.0-beta.44"
  resolved "https://registry.npmmirror.com/babylon/download/babylon-7.0.0-beta.44.tgz"

babylon@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npmmirror.com/babylon/download/babylon-6.18.0.tgz"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.0.tgz"

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/base64-js/download/base64-js-1.5.1.tgz"

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npmmirror.com/base/download/base-0.11.2.tgz"
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

big-integer@^1.6.17:
  version "1.6.48"
  resolved "https://registry.npmmirror.com/big-integer/download/big-integer-1.6.48.tgz"

bignumber.js@9.0.0:
  version "9.0.0"
  resolved "https://registry.npmmirror.com/bignumber.js/download/bignumber.js-9.0.0.tgz"

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz"

binary@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/binary/download/binary-0.3.0.tgz"
  dependencies:
    buffers "~0.1.1"
    chainsaw "~0.1.0"

bl@^4.0.3:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/bl/download/bl-4.1.0.tgz"
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

black-hole-stream@~0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/black-hole-stream/download/black-hole-stream-0.0.1.tgz"

bluebird@~3.4.1:
  version "3.4.7"
  resolved "https://registry.npmmirror.com/bluebird/download/bluebird-3.4.7.tgz"

boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/boolbase/download/boolbase-1.0.0.tgz"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz?cache=0&sync_timestamp=1601898189928&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbrace-expansion%2Fdownload%2Fbrace-expansion-1.1.11.tgz"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz"
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz"
  dependencies:
    fill-range "^7.0.1"

browser-stdout@1.3.1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/browser-stdout/download/browser-stdout-1.3.1.tgz"

browserslist@^3.2.6:
  version "3.2.8"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-3.2.8.tgz"
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

browserslist@^4.21.3:
  version "4.21.4"
  resolved "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.4.tgz"
  dependencies:
    caniuse-lite "^1.0.30001400"
    electron-to-chromium "^1.4.251"
    node-releases "^2.0.6"
    update-browserslist-db "^1.0.9"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/bser/-/bser-2.1.1.tgz"
  dependencies:
    node-int64 "^0.4.0"

buffer-crc32@^0.2.1, buffer-crc32@^0.2.13:
  version "0.2.13"
  resolved "https://registry.npmmirror.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz"

buffer-from@^1.0.0, buffer-from@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.1.tgz"

buffer-indexof-polyfill@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/buffer-indexof-polyfill/download/buffer-indexof-polyfill-1.0.2.tgz"

buffer@^5.1.0, buffer@^5.5.0:
  version "5.7.1"
  resolved "https://registry.npmmirror.com/buffer/download/buffer-5.7.1.tgz"
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffers@~0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/buffers/download/buffers-0.1.1.tgz"

bull@^3.22.7:
  version "3.22.7"
  resolved "https://registry.npmmirror.com/bull/download/bull-3.22.7.tgz?cache=0&sync_timestamp=1622458658855&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbull%2Fdownload%2Fbull-3.22.7.tgz"
  dependencies:
    cron-parser "^2.13.0"
    debuglog "^1.0.0"
    get-port "^5.1.1"
    ioredis "^4.22.0"
    lodash "^4.17.21"
    p-timeout "^3.2.0"
    promise.prototype.finally "^3.1.2"
    semver "^7.3.2"
    util.promisify "^1.0.1"
    uuid "^8.3.0"

busboy@^0.2.8:
  version "0.2.14"
  resolved "https://registry.npmmirror.com/busboy/download/busboy-0.2.14.tgz"
  dependencies:
    dicer "0.2.5"
    readable-stream "1.1.x"

byte@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/byte/download/byte-2.0.0.tgz"
  dependencies:
    debug "^3.1.0"
    long "^4.0.0"
    utility "^1.13.1"

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz"

bytes@~2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-2.2.0.tgz"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz"
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-content-type@^1.0.0, cache-content-type@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-content-type/download/cache-content-type-1.0.1.tgz"
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

cache-require-paths@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/cache-require-paths/-/cache-require-paths-0.3.0.tgz"

caching-transform@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/caching-transform/-/caching-transform-3.0.2.tgz#601d46b91eca87687a281e71cef99791b0efca70"
  dependencies:
    hasha "^3.0.0"
    make-dir "^2.0.0"
    package-hash "^3.0.0"
    write-file-atomic "^2.4.2"

call-bind@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.0.tgz?cache=0&sync_timestamp=1604117087261&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcall-bind%2Fdownload%2Fcall-bind-1.0.0.tgz"
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.0"

call-matcher@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/call-matcher/-/call-matcher-1.1.0.tgz"
  dependencies:
    core-js "^2.0.0"
    deep-equal "^1.0.0"
    espurify "^1.6.0"
    estraverse "^4.0.0"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz"

call-signature@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/call-signature/download/call-signature-0.0.2.tgz"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/callsites/download/callsites-3.1.0.tgz"

camel-case@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/camel-case/download/camel-case-3.0.0.tgz"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-3.0.0.tgz"

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz"

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz"

caniuse-lite@^1.0.30000844, caniuse-lite@^1.0.30001400:
  version "1.0.30001439"
  resolved "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001439.tgz"

cfork@^1.6.1, cfork@^1.7.1:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/cfork/download/cfork-1.8.0.tgz"
  dependencies:
    utility "^1.12.0"

chainsaw@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/chainsaw/download/chainsaw-0.1.0.tgz"
  dependencies:
    traverse ">=0.3.0 <0.4"

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-3.0.0.tgz"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.1:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chan@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/chan/download/chan-0.6.1.tgz"

change-case@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/change-case/download/change-case-3.1.0.tgz"
  dependencies:
    camel-case "^3.0.0"
    constant-case "^2.0.0"
    dot-case "^2.1.0"
    header-case "^1.0.0"
    is-lower-case "^1.1.0"
    is-upper-case "^1.1.0"
    lower-case "^1.1.1"
    lower-case-first "^1.0.0"
    no-case "^2.3.2"
    param-case "^2.1.0"
    pascal-case "^2.0.0"
    path-case "^2.1.0"
    sentence-case "^2.1.0"
    snake-case "^2.1.0"
    swap-case "^1.1.0"
    title-case "^2.1.0"
    upper-case "^1.1.1"
    upper-case-first "^1.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/char-regex/-/char-regex-1.0.2.tgz"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/chardet/download/chardet-0.7.0.tgz"

charenc@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/charenc/download/charenc-0.0.2.tgz"

cheerio@^1.0.0-rc.3:
  version "1.0.0-rc.3"
  resolved "https://registry.npmmirror.com/cheerio/download/cheerio-1.0.0-rc.3.tgz"
  dependencies:
    css-select "~1.2.0"
    dom-serializer "~0.1.1"
    entities "~1.1.1"
    htmlparser2 "^3.9.1"
    lodash "^4.15.0"
    parse5 "^3.0.1"

chokidar@^3.0.0:
  version "3.5.3"
  resolved "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

ci-info@^3.2.0:
  version "3.7.0"
  resolved "https://registry.npmmirror.com/ci-info/-/ci-info-3.7.0.tgz"

circular-json-for-egg@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/circular-json-for-egg/download/circular-json-for-egg-1.0.0.tgz"

cjs-module-lexer@^1.0.0:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npmmirror.com/class-utils/download/class-utils-0.3.6.tgz"
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/clean-stack/download/clean-stack-2.2.0.tgz?cache=0&sync_timestamp=1605702369164&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fclean-stack%2Fdownload%2Fclean-stack-2.2.0.tgz"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  dependencies:
    restore-cursor "^2.0.0"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/cli-width/download/cli-width-2.2.1.tgz"

cliui@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/cliui/download/cliui-3.2.0.tgz"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/cliui/download/cliui-4.1.0.tgz"
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/cliui/-/cliui-5.0.0.tgz"
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

cluster-client@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/cluster-client/download/cluster-client-3.0.1.tgz"
  dependencies:
    byte "^2.0.0"
    co "^4.6.0"
    debug "^4.1.1"
    egg-logger "^2.3.2"
    is-type-of "^1.2.1"
    json-stringify-safe "^5.0.1"
    long "^4.0.0"
    mz-modules "^2.1.0"
    sdk-base "^3.5.1"
    serialize-json "^1.0.3"
    tcp-base "^3.1.0"
    utility "^1.15.0"

cluster-key-slot@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/cluster-key-slot/download/cluster-key-slot-1.1.0.tgz"

cluster-reload@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/cluster-reload/download/cluster-reload-1.0.2.tgz"

cnchar-types@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/cnchar-types/-/cnchar-types-3.2.2.tgz"

cnchar@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/cnchar/-/cnchar-3.2.2.tgz"
  dependencies:
    cnchar-types "^3.2.2"

co-body@^6.0.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/co-body/download/co-body-6.1.0.tgz"
  dependencies:
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co-busboy@^1.4.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/co-busboy/download/co-busboy-1.4.1.tgz"
  dependencies:
    black-hole-stream "~0.0.1"
    busboy "^0.2.8"
    chan "^0.6.1"

co-mocha@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/co-mocha/download/co-mocha-1.2.2.tgz"
  dependencies:
    co "^4.0.0"
    is-generator "^1.0.1"

co-wrap-all@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/co-wrap-all/download/co-wrap-all-1.0.0.tgz"
  dependencies:
    co "^4.0.0"

co@^4.0.0, co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/co/download/co-4.6.0.tgz"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/code-point-at/download/code-point-at-1.1.0.tgz"

coffee@^5.2.1:
  version "5.4.0"
  resolved "https://registry.npmmirror.com/coffee/download/coffee-5.4.0.tgz"
  dependencies:
    cross-spawn "^6.0.5"
    debug "^4.1.0"
    is-type-of "^1.2.1"

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz"

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/collection-visit/download/collection-visit-1.0.0.tgz"
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

collections@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/collections/download/collections-3.0.0.tgz?cache=0&sync_timestamp=1601011324604&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcollections%2Fdownload%2Fcollections-3.0.0.tgz"
  dependencies:
    weak-map "~1.0.x"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz"
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"

colors@^1.1.2:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/colors/download/colors-1.4.0.tgz"

combined-stream@^1.0.6, combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz"
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.11.0, commander@^2.15.1, commander@^2.20.3:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1605992478790&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"

comment-parser@^0.5.4:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/comment-parser/download/comment-parser-0.5.5.tgz?cache=0&sync_timestamp=1605517273502&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcomment-parser%2Fdownload%2Fcomment-parser-0.5.5.tgz"

common-bin@^2.8.0, common-bin@^2.9.0:
  version "2.9.0"
  resolved "https://registry.npmmirror.com/common-bin/download/common-bin-2.9.0.tgz"
  dependencies:
    "@types/dargs" "^5.1.0"
    "@types/node" "^10.12.18"
    "@types/yargs" "^12.0.4"
    chalk "^2.4.1"
    change-case "^3.0.2"
    co "^4.6.0"
    dargs "^6.0.0"
    debug "^4.1.0"
    is-type-of "^1.2.1"
    semver "^5.5.1"
    yargs "^12.0.2"
    yargs-parser "^11.0.0"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/commondir/download/commondir-1.0.1.tgz"

component-emitter@^1.2.0, component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz"

compress-commons@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/compress-commons/download/compress-commons-4.1.1.tgz?cache=0&sync_timestamp=1622399636378&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcompress-commons%2Fdownload%2Fcompress-commons-4.1.1.tgz"
  dependencies:
    buffer-crc32 "^0.2.13"
    crc32-stream "^4.0.2"
    normalize-path "^3.0.0"
    readable-stream "^3.6.0"

compressible@^2.0.6:
  version "2.0.18"
  resolved "https://registry.npmmirror.com/compressible/download/compressible-2.0.18.tgz"
  dependencies:
    mime-db ">= 1.43.0 < 2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz"

constant-case@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/constant-case/download/constant-case-2.0.0.tgz"
  dependencies:
    snake-case "^2.1.0"
    upper-case "^1.1.1"

contains-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/contains-path/download/contains-path-0.1.0.tgz"

content-disposition@~0.5.2:
  version "0.5.3"
  resolved "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz"
  dependencies:
    safe-buffer "5.1.2"

content-type@^1.0.2, content-type@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz"

convert-source-map@^1.1.0, convert-source-map@^1.1.1, convert-source-map@^1.5.1, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.7.0.tgz"
  dependencies:
    safe-buffer "~5.1.1"

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz"

cookie@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmmirror.com/cookie/download/cookie-0.3.1.tgz"

cookiejar@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/cookiejar/download/cookiejar-2.1.2.tgz"

cookies@~0.8.0:
  version "0.8.0"
  resolved "https://registry.npmmirror.com/cookies/download/cookies-0.8.0.tgz"
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"

copy-to@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/copy-to/download/copy-to-2.0.1.tgz"

core-js-pure@^3.0.0:
  version "3.8.0"
  resolved "https://registry.npmmirror.com/core-js-pure/download/core-js-pure-3.8.0.tgz"

core-js@^2.0.0, core-js@^2.4.0, core-js@^2.5.0:
  version "2.6.12"
  resolved "https://registry.npmmirror.com/core-js/download/core-js-2.6.12.tgz"

core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.2.tgz"

crc-32@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/crc-32/download/crc-32-1.2.0.tgz"
  dependencies:
    exit-on-epipe "~1.0.1"
    printj "~1.1.0"

crc32-stream@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/crc32-stream/download/crc32-stream-4.0.2.tgz"
  dependencies:
    crc-32 "^1.2.0"
    readable-stream "^3.4.0"

crc@^3.4.4:
  version "3.8.0"
  resolved "https://registry.npmmirror.com/crc/download/crc-3.8.0.tgz"
  dependencies:
    buffer "^5.1.0"

crequire@^1.8.0, crequire@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/crequire/download/crequire-1.8.1.tgz"

cron-parser@^2.13.0, cron-parser@^2.16.3:
  version "2.18.0"
  resolved "https://registry.npmmirror.com/cron-parser/download/cron-parser-2.18.0.tgz?cache=0&sync_timestamp=1605774386755&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcron-parser%2Fdownload%2Fcron-parser-2.18.0.tgz"
  dependencies:
    is-nan "^1.3.0"
    moment-timezone "^0.5.31"

cross-spawn@^4:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-4.0.2.tgz"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/crypt/download/crypt-0.0.2.tgz"

csprng@*:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/csprng/download/csprng-0.1.2.tgz"
  dependencies:
    sequin "*"

csrf@^3.0.6:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/csrf/download/csrf-3.1.0.tgz"
  dependencies:
    rndm "1.2.0"
    tsscmp "1.0.6"
    uid-safe "2.1.5"

css-select@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/css-select/download/css-select-1.2.0.tgz?cache=0&sync_timestamp=1601658025267&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-select%2Fdownload%2Fcss-select-1.2.0.tgz"
  dependencies:
    boolbase "~1.0.0"
    css-what "2.1"
    domutils "1.5.1"
    nth-check "~1.0.1"

css-what@2.1:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-2.1.3.tgz?cache=0&sync_timestamp=1602570970779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-what%2Fdownload%2Fcss-what-2.1.3.tgz"

cssfilter@0.0.10:
  version "0.0.10"
  resolved "https://registry.npmmirror.com/cssfilter/download/cssfilter-0.0.10.tgz"

d@1, d@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/d/-/d-1.0.1.tgz"
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

damerau-levenshtein@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/damerau-levenshtein/download/damerau-levenshtein-1.0.6.tgz"

dargs@^6.0.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/dargs/download/dargs-6.1.0.tgz"

data-uri-to-buffer@1:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/data-uri-to-buffer/download/data-uri-to-buffer-1.2.0.tgz"

debounce@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/debounce/download/debounce-1.2.0.tgz"

debug@2, debug@^2.2.0, debug@^2.3.3, debug@^2.6.0, debug@^2.6.1, debug@^2.6.3, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1605791507452&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  dependencies:
    ms "2.0.0"

debug@3.1.0, debug@~3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/debug/download/debug-3.1.0.tgz?cache=0&sync_timestamp=1605791507452&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.1.0.tgz"
  dependencies:
    ms "2.0.0"

debug@3.2.6:
  version "3.2.6"
  resolved "https://registry.npmmirror.com/debug/-/debug-3.2.6.tgz"
  dependencies:
    ms "^2.1.1"

debug@4, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.3:
  version "4.3.4"
  resolved "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  dependencies:
    ms "2.1.2"

debug@^3.0.1, debug@^3.1.0, debug@^3.2.6:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1605791507452&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  dependencies:
    ms "^2.1.1"

debuglog@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/debuglog/download/debuglog-1.0.1.tgz"

decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz"

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"

dedent@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/dedent/-/dedent-0.7.0.tgz"

deep-equal@^1.0.0, deep-equal@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.0.1.tgz?cache=0&sync_timestamp=1601612573471&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.0.1.tgz"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.3.tgz"

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmmirror.com/deepmerge/-/deepmerge-4.2.2.tgz"

default-require-extensions@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/default-require-extensions/download/default-require-extensions-2.0.0.tgz"
  dependencies:
    strip-bom "^3.0.0"

default-user-agent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/default-user-agent/download/default-user-agent-1.0.0.tgz"
  dependencies:
    os-name "~1.0.3"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz"
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npmmirror.com/define-property/download/define-property-0.2.5.tgz"
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/define-property/download/define-property-1.0.0.tgz"
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/define-property/download/define-property-2.0.2.tgz"
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

degenerator@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/degenerator/download/degenerator-1.0.4.tgz"
  dependencies:
    ast-types "0.x.x"
    escodegen "1.x.x"
    esprima "3.x.x"

del@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/del/download/del-5.1.0.tgz?cache=0&sync_timestamp=1601076741536&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdel%2Fdownload%2Fdel-5.1.0.tgz"
  dependencies:
    globby "^10.0.1"
    graceful-fs "^4.2.2"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.1"
    p-map "^3.0.0"
    rimraf "^3.0.0"
    slash "^3.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz"

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/delegates/download/delegates-1.0.0.tgz"

denque@^1.1.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/denque/download/denque-1.4.1.tgz"

denque@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/denque/-/denque-2.1.0.tgz#e93e1a6569fb5e66f16a3c2a2964617d349d6ab1"

depd@^1.1.2, depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz"

depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/depd/download/depd-2.0.0.tgz"

destroy@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/detect-indent/download/detect-indent-4.0.0.tgz"
  dependencies:
    repeating "^2.0.0"

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/detect-newline/-/detect-newline-3.1.0.tgz"

detect-port@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/detect-port/download/detect-port-1.3.0.tgz"
  dependencies:
    address "^1.0.1"
    debug "^2.6.0"

dicer@0.2.5:
  version "0.2.5"
  resolved "https://registry.npmmirror.com/dicer/download/dicer-0.2.5.tgz"
  dependencies:
    readable-stream "1.1.x"
    streamsearch "0.1.2"

diff-match-patch@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/diff-match-patch/download/diff-match-patch-1.0.5.tgz?cache=0&sync_timestamp=1600349125335&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdiff-match-patch%2Fdownload%2Fdiff-match-patch-1.0.5.tgz"

diff-sequences@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/diff-sequences/-/diff-sequences-29.3.1.tgz"

diff@3.5.0, diff@^3.1.0:
  version "3.5.0"
  resolved "https://registry.npmmirror.com/diff/download/diff-3.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdiff%2Fdownload%2Fdiff-3.5.0.tgz"

digest-header@^0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/digest-header/download/digest-header-0.0.1.tgz"
  dependencies:
    utility "0.1.11"

dir-glob@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/dir-glob/download/dir-glob-2.0.0.tgz"
  dependencies:
    arrify "^1.0.1"
    path-type "^3.0.0"

dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmmirror.com/dir-glob/download/dir-glob-2.2.2.tgz"
  dependencies:
    path-type "^3.0.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/dir-glob/download/dir-glob-3.0.1.tgz"
  dependencies:
    path-type "^4.0.0"

doctrine@1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/doctrine/download/doctrine-1.5.0.tgz"
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/doctrine/download/doctrine-2.1.0.tgz"
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/doctrine/download/doctrine-3.0.0.tgz"
  dependencies:
    esutils "^2.0.2"

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1600026603144&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz"
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@~0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/dom-serializer/download/dom-serializer-0.1.1.tgz?cache=0&sync_timestamp=1600026603144&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.1.1.tgz"
  dependencies:
    domelementtype "^1.3.0"
    entities "^1.1.1"

domelementtype@1, domelementtype@^1.3.0, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/domelementtype/download/domelementtype-1.3.1.tgz"

domelementtype@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/domelementtype/download/domelementtype-2.0.2.tgz"

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/domhandler/download/domhandler-2.4.2.tgz?cache=0&sync_timestamp=1601760754653&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomhandler%2Fdownload%2Fdomhandler-2.4.2.tgz"
  dependencies:
    domelementtype "1"

domutils@1.5.1:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/domutils/download/domutils-1.5.1.tgz"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^1.5.1:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/domutils/download/domutils-1.7.0.tgz"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-case@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/dot-case/download/dot-case-2.1.1.tgz"
  dependencies:
    no-case "^2.2.0"

dot-prop@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmmirror.com/dot-prop/-/dot-prop-4.2.1.tgz"
  dependencies:
    is-obj "^1.0.0"

dottie@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/dottie/-/dottie-2.0.2.tgz"

duplexer2@~0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/duplexer2/download/duplexer2-0.1.4.tgz"
  dependencies:
    readable-stream "^2.0.2"

duplexer@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/duplexer/download/duplexer-0.1.2.tgz"

duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.npmmirror.com/duplexify/download/duplexify-3.7.1.tgz"
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz"

ee-first@1.1.1, ee-first@^1.1.1, ee-first@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz"

egg-bin@^4.18.1:
  version "4.18.1"
  resolved "https://registry.npmmirror.com/egg-bin/-/egg-bin-4.18.1.tgz"
  dependencies:
    chalk "^4.1.1"
    co-mocha "^1.2.2"
    common-bin "^2.9.0"
    debug "^4.3.1"
    detect-port "^1.3.0"
    egg-ts-helper "^1.25.9"
    egg-utils "^2.4.1"
    espower-source "^2.3.0"
    globby "^9.2.0"
    inspector-proxy "^1.2.1"
    intelli-espower-loader "^1.1.0"
    jest-changed-files "^25.5.0"
    minimatch "^3.0.4"
    mocha "^6.0.2"
    mz-modules "^2.1.0"
    nyc "^13.3.0"
    power-assert "^1.6.1"
    semver "^7.3.5"
    source-map-support "^0.5.19"
    test-exclude "^5.1.0"
    ts-node "^7"
    ypkgfiles "^1.6.0"

egg-cluster@^1.23.0:
  version "1.26.0"
  resolved "https://registry.npmmirror.com/egg-cluster/download/egg-cluster-1.26.0.tgz"
  dependencies:
    await-event "^2.1.0"
    cfork "^1.7.1"
    cluster-reload "^1.0.2"
    co "^4.6.0"
    debug "^4.1.1"
    depd "^2.0.0"
    detect-port "^1.3.0"
    egg-logger "^2.3.2"
    egg-utils "^2.4.1"
    get-ready "^2.0.1"
    graceful-process "^1.2.0"
    is-type-of "^1.2.1"
    mz-modules "^2.1.0"
    ps-tree "^1.2.0"
    semver "^5.6.0"
    sendmessage "^1.1.0"
    utility "^1.15.0"

egg-cookies@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/egg-cookies/download/egg-cookies-2.4.2.tgz"
  dependencies:
    debug "^3.1.0"
    scmp "^2.0.0"
    should-send-same-site-none "^2.0.2"
    utility "^1.14.0"

egg-core@^4.18.0:
  version "4.20.0"
  resolved "https://registry.npmmirror.com/egg-core/download/egg-core-4.20.0.tgz"
  dependencies:
    "@eggjs/router" "^2.0.0"
    "@types/depd" "^1.1.32"
    "@types/koa" "^2.0.48"
    co "^4.6.0"
    debug "^4.1.1"
    depd "^2.0.0"
    egg-logger "^2.4.1"
    egg-path-matching "^1.0.1"
    extend2 "^1.0.0"
    get-ready "^2.0.1"
    globby "^8.0.2"
    is-type-of "^1.2.1"
    koa "^2.7.0"
    koa-convert "^1.2.0"
    node-homedir "^1.1.1"
    ready-callback "^2.1.0"
    utility "^1.16.1"

egg-development@^2.4.2:
  version "2.7.0"
  resolved "https://registry.npmmirror.com/egg-development/download/egg-development-2.7.0.tgz"
  dependencies:
    debounce "^1.1.0"
    multimatch "^2.1.0"
    mz "^2.7.0"
    mz-modules "^2.1.0"
    utility "^1.13.1"

egg-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/egg-errors/-/egg-errors-2.3.1.tgz"

egg-i18n@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/egg-i18n/download/egg-i18n-2.1.1.tgz"
  dependencies:
    debug "^3.1.0"
    koa-locales "^1.11.0"

egg-jsonp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/egg-jsonp/download/egg-jsonp-2.0.0.tgz"
  dependencies:
    is-type-of "^1.2.0"
    jsonp-body "^1.0.0"

egg-logger@^2.3.2, egg-logger@^2.4.1:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/egg-logger/download/egg-logger-2.4.2.tgz"
  dependencies:
    chalk "^2.4.1"
    circular-json-for-egg "^1.0.0"
    debug "^2.6.9"
    depd "^2.0.0"
    iconv-lite "^0.4.24"
    mkdirp "^0.5.1"
    utility "^1.15.0"

egg-logrotator@^3.0.5:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/egg-logrotator/download/egg-logrotator-3.1.0.tgz"
  dependencies:
    debug "^4.1.1"
    moment "^2.24.0"
    mz "^2.7.0"

egg-mock@^3.20.1:
  version "3.25.1"
  resolved "https://registry.npmmirror.com/egg-mock/download/egg-mock-3.25.1.tgz"
  dependencies:
    "@types/power-assert" "^1.5.0"
    "@types/supertest" "^2.0.7"
    await-event "^2.1.0"
    co "^4.6.0"
    coffee "^5.2.1"
    debug "^4.1.1"
    detect-port "^1.3.0"
    egg-logger "^2.4.1"
    egg-utils "^2.4.1"
    extend2 "^1.0.0"
    get-ready "^2.0.1"
    globby "^9.2.0"
    is-type-of "^1.2.1"
    ko-sleep "^1.0.3"
    merge-descriptors "^1.0.1"
    methods "^1.1.2"
    mm "^2.5.0"
    mz-modules "^2.1.0"
    power-assert "^1.6.1"
    supertest "^4.0.2"
    urllib "^2.33.3"

egg-multipart@^2.4.0:
  version "2.10.3"
  resolved "https://registry.npmmirror.com/egg-multipart/download/egg-multipart-2.10.3.tgz"
  dependencies:
    co-busboy "^1.4.0"
    egg-path-matching "^1.0.1"
    humanize-bytes "^1.0.1"
    moment "^2.22.2"
    mz "^2.7.0"
    mz-modules "^2.1.0"
    stream-wormhole "^1.1.0"
    uuid "^3.3.2"

egg-mysql@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/egg-mysql/-/egg-mysql-3.1.0.tgz"
  dependencies:
    ali-rds "^3.0.1"

egg-onerror@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/egg-onerror/download/egg-onerror-2.1.0.tgz"
  dependencies:
    cookie "^0.3.1"
    koa-onerror "^4.0.0"
    mustache "^2.3.0"
    stack-trace "^0.0.10"

egg-path-matching@^1.0.0, egg-path-matching@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/egg-path-matching/download/egg-path-matching-1.0.1.tgz"
  dependencies:
    path-to-regexp "^1.7.0"

egg-redis@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/egg-redis/-/egg-redis-2.4.0.tgz"
  dependencies:
    "@types/ioredis" "^4.0.10"
    await-first "^1.0.0"
    ioredis "^4.9.0"

egg-schedule@^3.6.0, egg-schedule@^3.6.6:
  version "3.6.6"
  resolved "https://registry.npmmirror.com/egg-schedule/-/egg-schedule-3.6.6.tgz"
  dependencies:
    cron-parser "^2.16.3"
    humanize-ms "^1.2.1"
    is-type-of "^1.2.1"
    safe-timers "^1.1.0"
    utility "^1.16.3"

egg-scripts@^2.15.3:
  version "2.15.3"
  resolved "https://registry.npmmirror.com/egg-scripts/-/egg-scripts-2.15.3.tgz"
  dependencies:
    await-event "^2.1.0"
    common-bin "^2.8.0"
    debug "^4.1.0"
    egg-utils "^2.4.1"
    moment "^2.23.0"
    mz "^2.7.0"
    mz-modules "^2.1.0"
    node-homedir "^1.1.1"
    runscript "^1.3.0"
    source-map-support "^0.5.9"
    zlogger "^1.1.0"

egg-security@^2.4.3:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/egg-security/download/egg-security-2.8.0.tgz"
  dependencies:
    csrf "^3.0.6"
    debug "^4.1.1"
    delegates "^1.0.0"
    egg-path-matching "^1.0.0"
    escape-html "^1.0.3"
    extend "^3.0.1"
    ip "^1.1.5"
    koa-compose "^4.0.0"
    matcher "^1.1.1"
    methods "^1.1.2"
    nanoid "^2.0.1"
    platform "^1.3.4"
    statuses "^1.5.0"
    type-is "^1.6.15"
    xss "^1.0.3"

egg-sequelize@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/egg-sequelize/-/egg-sequelize-6.0.0.tgz"
  dependencies:
    "@types/sequelize" "^4.27.24"
    mz-modules "^2.1.0"
    sequelize "^6.0.0"

egg-session-redis@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/egg-session-redis/-/egg-session-redis-2.1.0.tgz"

egg-session@^3.1.0, egg-session@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/egg-session/-/egg-session-3.3.0.tgz"
  dependencies:
    koa-session "^6.0.0"

egg-static@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/egg-static/download/egg-static-2.2.0.tgz"
  dependencies:
    is-type-of "^1.2.1"
    koa-compose "^4.1.0"
    koa-range "^0.3.0"
    koa-static-cache "^5.1.2"
    mkdirp "^0.5.1"
    ylru "^1.2.1"

egg-ts-helper@^1.25.9:
  version "1.30.2"
  resolved "https://registry.npmmirror.com/egg-ts-helper/-/egg-ts-helper-1.30.2.tgz"
  dependencies:
    cache-require-paths "^0.3.0"
    chalk "^2.4.2"
    chokidar "^3.0.0"
    commander "^2.15.1"
    debug "^3.1.0"
    dot-prop "^4.2.0"
    enquirer "^2.3.0"
    globby "^8.0.2"
    json5 "^2.2.0"
    mkdirp "^0.5.1"
    ts-node "^7.0.0"
    tslib "^2.0.0"
    typescript "^4.0.0"
    yn "^3.0.0"

egg-utils@^2.4.1:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/egg-utils/download/egg-utils-2.4.1.tgz"
  dependencies:
    mkdirp "^0.5.1"
    utility "^1.13.1"

egg-validate@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/egg-validate/-/egg-validate-2.0.2.tgz"
  dependencies:
    parameter "^3.0.0"

egg-view@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/egg-view/download/egg-view-2.1.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fegg-view%2Fdownload%2Fegg-view-2.1.3.tgz"
  dependencies:
    mz "^2.7.0"

egg-watcher@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/egg-watcher/download/egg-watcher-3.1.1.tgz"
  dependencies:
    camelcase "^5.0.0"
    sdk-base "^3.5.0"
    wt "^1.2.0"

egg@^2.34.0:
  version "2.34.0"
  resolved "https://registry.npmmirror.com/egg/-/egg-2.34.0.tgz"
  dependencies:
    "@types/accepts" "^1.3.5"
    "@types/koa" "^2.0.48"
    "@types/koa-router" "^7.0.40"
    accepts "^1.3.5"
    agentkeepalive "^4.0.2"
    cache-content-type "^1.0.1"
    circular-json-for-egg "^1.0.0"
    cluster-client "^3.0.1"
    debug "^4.1.1"
    delegates "^1.0.0"
    egg-cluster "^1.23.0"
    egg-cookies "^2.3.0"
    egg-core "^4.18.0"
    egg-development "^2.4.2"
    egg-errors "^2.3.0"
    egg-i18n "^2.0.0"
    egg-jsonp "^2.0.0"
    egg-logger "^2.3.2"
    egg-logrotator "^3.0.5"
    egg-multipart "^2.4.0"
    egg-onerror "^2.1.0"
    egg-schedule "^3.6.0"
    egg-security "^2.4.3"
    egg-session "^3.1.0"
    egg-static "^2.2.0"
    egg-view "^2.1.2"
    egg-watcher "^3.1.0"
    extend2 "^1.0.0"
    graceful "^1.0.2"
    humanize-ms "^1.2.1"
    is-type-of "^1.2.1"
    koa-bodyparser "^4.2.1"
    koa-is-json "^1.0.0"
    koa-override "^3.0.0"
    ms "^2.1.1"
    mz "^2.7.0"
    on-finished "^2.3.0"
    semver "^7.3.2"
    sendmessage "^1.1.0"
    urllib "^2.33.0"
    utility "^1.15.0"
    ylru "^1.2.1"

electron-to-chromium@^1.3.47, electron-to-chromium@^1.4.251:
  version "1.4.284"
  resolved "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz"

emitter-component@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/emitter-component/download/emitter-component-1.1.1.tgz"

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://registry.npmmirror.com/emittery/-/emittery-0.13.1.tgz"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.3.tgz"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz"

emoji-regex@^9.0.0:
  version "9.2.0"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-9.2.0.tgz"

empower-assert@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/empower-assert/-/empower-assert-1.1.0.tgz"
  dependencies:
    estraverse "^4.2.0"

empower-core@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/empower-core/download/empower-core-1.2.0.tgz"
  dependencies:
    call-signature "0.0.2"
    core-js "^2.0.0"

empower@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/empower/download/empower-1.3.1.tgz"
  dependencies:
    core-js "^2.0.0"
    empower-core "^1.2.0"

encodeurl@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz"

end-of-stream@^1.0.0, end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  dependencies:
    once "^1.4.0"

enquirer@^2.3.0:
  version "2.3.6"
  resolved "https://registry.npmmirror.com/enquirer/-/enquirer-2.3.6.tgz"
  dependencies:
    ansi-colors "^4.1.1"

entities@^1.1.1, entities@~1.1.1:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/entities/download/entities-1.1.2.tgz?cache=0&sync_timestamp=1602898797807&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fentities%2Fdownload%2Fentities-1.1.2.tgz"

entities@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/entities/download/entities-2.1.0.tgz?cache=0&sync_timestamp=1602898797807&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fentities%2Fdownload%2Fentities-2.1.0.tgz"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz"
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.0-next.0, es-abstract@^1.17.0-next.1, es-abstract@^1.17.2:
  version "1.17.7"
  resolved "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.17.7.tgz"
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-regex "^1.1.1"
    object-inspect "^1.8.0"
    object-keys "^1.1.1"
    object.assign "^4.1.1"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-abstract@^1.18.0-next.0, es-abstract@^1.18.0-next.1:
  version "1.18.0-next.1"
  resolved "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.18.0-next.1.tgz"
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-negative-zero "^2.0.0"
    is-regex "^1.1.1"
    object-inspect "^1.8.0"
    object-keys "^1.1.1"
    object.assign "^4.1.1"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.46, es5-ext@^0.10.50, es5-ext@~0.10.14:
  version "0.10.59"
  resolved "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.59.tgz"
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    next-tick "^1.1.0"

es6-denodeify@^0.1.1:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/es6-denodeify/download/es6-denodeify-0.1.5.tgz"

es6-error@^4.0.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/es6-error/download/es6-error-4.1.1.tgz"

es6-iterator@^2.0.3, es6-iterator@~2.0.1:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/es6-iterator/-/es6-iterator-2.0.3.tgz"
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/es6-map/-/es6-map-0.1.5.tgz"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-promise@^4.0.3:
  version "4.2.8"
  resolved "https://registry.npmmirror.com/es6-promise/download/es6-promise-4.2.8.tgz"

es6-promisify@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/es6-promisify/download/es6-promisify-5.0.0.tgz"
  dependencies:
    es6-promise "^4.0.3"

es6-set@~0.1.5:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/es6-set/-/es6-set-0.1.5.tgz"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/es6-symbol/-/es6-symbol-3.1.1.tgz"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-symbol@^3.1.1, es6-symbol@^3.1.3, es6-symbol@~3.1.1:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/es6-symbol/-/es6-symbol-3.1.3.tgz"
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

es6-weak-map@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/es6-weak-map/-/es6-weak-map-2.0.3.tgz"
  dependencies:
    d "1"
    es5-ext "^0.10.46"
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.1"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz"

escallmatch@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/escallmatch/-/escallmatch-1.5.0.tgz"
  dependencies:
    call-matcher "^1.0.0"
    esprima "^2.0.0"

escape-html@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz"

escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.4, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"

escodegen@1.x.x, escodegen@^1.10.0, escodegen@^1.7.0:
  version "1.14.3"
  resolved "https://registry.npmmirror.com/escodegen/download/escodegen-1.14.3.tgz"
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

escope@^3.3.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/escope/-/escope-3.6.0.tgz"
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-config-egg@^7.1.0:
  version "7.5.1"
  resolved "https://registry.npmmirror.com/eslint-config-egg/download/eslint-config-egg-7.5.1.tgz"
  dependencies:
    "@typescript-eslint/eslint-plugin" "^2.0.0"
    "@typescript-eslint/parser" "^2.0.0"
    babel-eslint "^8.2.6"
    eslint-plugin-eggache "^1.0.0"
    eslint-plugin-import "^2.14.0"
    eslint-plugin-jsdoc "^4.1.1"
    eslint-plugin-jsx-a11y "^6.1.1"
    eslint-plugin-react "^7.11.1"

eslint-import-resolver-node@^0.3.4:
  version "0.3.4"
  resolved "https://registry.npmmirror.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.4.tgz"
  dependencies:
    debug "^2.6.9"
    resolve "^1.13.1"

eslint-module-utils@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmmirror.com/eslint-module-utils/download/eslint-module-utils-2.6.0.tgz"
  dependencies:
    debug "^2.6.9"
    pkg-dir "^2.0.0"

eslint-plugin-eggache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-eggache/download/eslint-plugin-eggache-1.0.0.tgz"

eslint-plugin-import@^2.14.0:
  version "2.22.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-import/download/eslint-plugin-import-2.22.1.tgz"
  dependencies:
    array-includes "^3.1.1"
    array.prototype.flat "^1.2.3"
    contains-path "^0.1.0"
    debug "^2.6.9"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.4"
    eslint-module-utils "^2.6.0"
    has "^1.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.1"
    read-pkg-up "^2.0.0"
    resolve "^1.17.0"
    tsconfig-paths "^3.9.0"

eslint-plugin-jsdoc@^4.1.1:
  version "4.8.4"
  resolved "https://registry.npmmirror.com/eslint-plugin-jsdoc/download/eslint-plugin-jsdoc-4.8.4.tgz"
  dependencies:
    comment-parser "^0.5.4"
    jsdoctypeparser "3.1.0"
    lodash "^4.17.11"

eslint-plugin-jsx-a11y@^6.1.1:
  version "6.4.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-jsx-a11y/download/eslint-plugin-jsx-a11y-6.4.1.tgz"
  dependencies:
    "@babel/runtime" "^7.11.2"
    aria-query "^4.2.2"
    array-includes "^3.1.1"
    ast-types-flow "^0.0.7"
    axe-core "^4.0.2"
    axobject-query "^2.2.0"
    damerau-levenshtein "^1.0.6"
    emoji-regex "^9.0.0"
    has "^1.0.3"
    jsx-ast-utils "^3.1.0"
    language-tags "^1.0.5"

eslint-plugin-react@^7.11.1:
  version "7.21.5"
  resolved "https://registry.npmmirror.com/eslint-plugin-react/download/eslint-plugin-react-7.21.5.tgz?cache=0&sync_timestamp=1603161620481&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-plugin-react%2Fdownload%2Feslint-plugin-react-7.21.5.tgz"
  dependencies:
    array-includes "^3.1.1"
    array.prototype.flatmap "^1.2.3"
    doctrine "^2.1.0"
    has "^1.0.3"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    object.entries "^1.1.2"
    object.fromentries "^2.0.2"
    object.values "^1.1.1"
    prop-types "^15.7.2"
    resolve "^1.18.1"
    string.prototype.matchall "^4.0.2"

eslint-scope@3.7.1:
  version "3.7.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-3.7.1.tgz?cache=0&sync_timestamp=1600070417656&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-scope%2Fdownload%2Feslint-scope-3.7.1.tgz"
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-4.0.3.tgz?cache=0&sync_timestamp=1600070417656&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-scope%2Fdownload%2Feslint-scope-4.0.3.tgz"
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz?cache=0&sync_timestamp=1600070417656&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-scope%2Fdownload%2Feslint-scope-5.1.1.tgz"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1:
  version "1.4.3"
  resolved "https://registry.npmmirror.com/eslint-utils/download/eslint-utils-1.4.3.tgz"
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1599829544231&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"

eslint@^5.8.0:
  version "5.16.0"
  resolved "https://registry.npmmirror.com/eslint/download/eslint-5.16.0.tgz"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.9.1"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^4.0.3"
    eslint-utils "^1.3.1"
    eslint-visitor-keys "^1.0.0"
    espree "^5.0.1"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.7.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^6.2.2"
    js-yaml "^3.13.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.11"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^5.5.1"
    strip-ansi "^4.0.0"
    strip-json-comments "^2.0.1"
    table "^5.2.3"
    text-table "^0.2.0"

espower-loader@^1.0.0:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/espower-loader/-/espower-loader-1.2.2.tgz"
  dependencies:
    convert-source-map "^1.1.0"
    espower-source "^2.0.0"
    minimatch "^3.0.0"
    source-map-support "^0.4.0"
    xtend "^4.0.0"

espower-location-detector@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/espower-location-detector/-/espower-location-detector-1.0.0.tgz"
  dependencies:
    is-url "^1.2.1"
    path-is-absolute "^1.0.0"
    source-map "^0.5.0"
    xtend "^4.0.0"

espower-source@^2.0.0, espower-source@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/espower-source/-/espower-source-2.3.0.tgz"
  dependencies:
    acorn "^5.0.0"
    acorn-es7-plugin "^1.0.10"
    convert-source-map "^1.1.1"
    empower-assert "^1.0.0"
    escodegen "^1.10.0"
    espower "^2.1.1"
    estraverse "^4.0.0"
    merge-estraverse-visitors "^1.0.0"
    multi-stage-sourcemap "^0.2.1"
    path-is-absolute "^1.0.0"
    xtend "^4.0.0"

espower@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/espower/-/espower-2.1.2.tgz"
  dependencies:
    array-find "^1.0.0"
    escallmatch "^1.5.0"
    escodegen "^1.7.0"
    escope "^3.3.0"
    espower-location-detector "^1.0.0"
    espurify "^1.3.0"
    estraverse "^4.1.0"
    source-map "^0.5.0"
    type-name "^2.0.0"

espree@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/espree/download/espree-5.0.1.tgz"
  dependencies:
    acorn "^6.0.7"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

esprima@3.x.x:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/esprima/download/esprima-3.1.3.tgz"

esprima@^2.0.0:
  version "2.7.3"
  resolved "https://registry.npmmirror.com/esprima/-/esprima-2.7.3.tgz"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz"

espurify@^1.3.0, espurify@^1.6.0:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/espurify/download/espurify-1.8.1.tgz"
  dependencies:
    core-js "^2.0.0"

esquery@^1.0.1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/esquery/download/esquery-1.3.1.tgz"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/esrecurse/download/esrecurse-4.3.0.tgz"
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.2.0.tgz"

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/esutils/download/esutils-2.0.3.tgz"

event-emitter@~0.3.5:
  version "0.3.5"
  resolved "https://registry.npmmirror.com/event-emitter/-/event-emitter-0.3.5.tgz"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

event-stream@=3.3.4:
  version "3.3.4"
  resolved "https://registry.npmmirror.com/event-stream/download/event-stream-3.3.4.tgz"
  dependencies:
    duplexer "~0.1.1"
    from "~0"
    map-stream "~0.1.0"
    pause-stream "0.0.11"
    split "0.3"
    stream-combiner "~0.0.4"
    through "~2.3.1"

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/event-target-shim/-/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/events/-/events-3.3.0.tgz"

excel-export@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmmirror.com/excel-export/download/excel-export-0.5.1.tgz"
  dependencies:
    collections "^3.0.0"
    node-zip "1.x"

excel-export@~0.3.11:
  version "0.3.11"
  resolved "https://registry.npmmirror.com/excel-export/-/excel-export-0.3.11.tgz"
  dependencies:
    node-zip "1.x"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1603882924514&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz"
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^3.2.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/execa/-/execa-3.4.0.tgz"
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit-on-epipe@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/exit-on-epipe/download/exit-on-epipe-1.0.1.tgz"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/exit/-/exit-0.1.2.tgz"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npmmirror.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/expect/-/expect-29.3.1.tgz"
  dependencies:
    "@jest/expect-utils" "^29.3.1"
    jest-get-type "^29.2.0"
    jest-matcher-utils "^29.3.1"
    jest-message-util "^29.3.1"
    jest-util "^29.3.1"

ext@^1.1.2:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/ext/-/ext-1.6.0.tgz"
  dependencies:
    type "^2.5.0"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend2@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/extend2/download/extend2-1.0.0.tgz"

extend@^3.0.0, extend@^3.0.1, extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/extend/download/extend-3.0.2.tgz"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/external-editor/download/external-editor-3.1.0.tgz"
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/extglob/download/extglob-2.0.4.tgz"
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"

fast-glob@^2.0.2, fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://registry.npmmirror.com/fast-glob/download/fast-glob-2.2.7.tgz"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.0.3:
  version "3.2.4"
  resolved "https://registry.npmmirror.com/fast-glob/download/fast-glob-3.2.4.tgz"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.0"
    merge2 "^1.3.0"
    micromatch "^4.0.2"
    picomatch "^2.2.1"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"

fastq@^1.6.0:
  version "1.9.0"
  resolved "https://registry.npmmirror.com/fastq/download/fastq-1.9.0.tgz?cache=0&sync_timestamp=1603877566746&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffastq%2Fdownload%2Ffastq-1.9.0.tgz"
  dependencies:
    reusify "^1.0.4"

faye-websocket@>=0.9.1:
  version "0.11.3"
  resolved "https://registry.npmmirror.com/faye-websocket/download/faye-websocket-0.11.3.tgz"
  dependencies:
    websocket-driver ">=0.5.1"

faye@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/faye/download/faye-1.4.0.tgz"
  dependencies:
    asap "*"
    csprng "*"
    faye-websocket ">=0.9.1"
    safe-buffer "*"
    tough-cookie "*"
    tunnel-agent "*"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/fb-watchman/-/fb-watchman-2.0.2.tgz"
  dependencies:
    bser "2.1.1"

fetch-cookie@^0.7.2:
  version "0.7.3"
  resolved "https://registry.npmmirror.com/fetch-cookie/download/fetch-cookie-0.7.3.tgz"
  dependencies:
    es6-denodeify "^0.1.1"
    tough-cookie "^2.3.3"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/figures/download/figures-2.0.0.tgz?cache=0&sync_timestamp=1600349107095&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffigures%2Fdownload%2Ffigures-2.0.0.tgz"
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz"
  dependencies:
    flat-cache "^2.0.1"

file-type@16.3.0:
  version "16.3.0"
  resolved "https://registry.npmmirror.com/file-type/-/file-type-16.3.0.tgz#f03af91db30f92cc9a0b335c0644c46101522f6d"
  dependencies:
    readable-web-to-node-stream "^3.0.0"
    strtok3 "^6.0.3"
    token-types "^2.0.0"
    typedarray-to-buffer "^3.1.5"

file-uri-to-path@1:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/fill-range/download/fill-range-4.0.0.tgz"
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
  dependencies:
    to-regex-range "^5.0.1"

find-cache-dir@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@3.0.0, find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1597169884679&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz"
  dependencies:
    locate-path "^3.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-1.1.2.tgz?cache=0&sync_timestamp=1597169884679&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-1.1.2.tgz"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-2.1.0.tgz?cache=0&sync_timestamp=1597169884679&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-2.1.0.tgz"
  dependencies:
    locate-path "^2.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/flat-cache/download/flat-cache-2.0.1.tgz"
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flat@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/flat/-/flat-4.1.1.tgz"
  dependencies:
    is-buffer "~2.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/flatted/download/flatted-2.0.2.tgz"

follow-redirects@^1.15.0:
  version "1.15.2"
  resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/for-in/download/for-in-1.0.2.tgz"

foreground-child@^1.5.6:
  version "1.5.6"
  resolved "https://registry.npmmirror.com/foreground-child/download/foreground-child-1.5.6.tgz"
  dependencies:
    cross-spawn "^4"
    signal-exit "^3.0.0"

form-data-encoder@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmmirror.com/form-data-encoder/-/form-data-encoder-1.7.2.tgz#1f1ae3dccf58ed4690b86d87e4f57c654fbab040"
  integrity sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==

form-data@^2.3.1:
  version "2.5.1"
  resolved "https://registry.npmmirror.com/form-data/download/form-data-2.5.1.tgz?cache=0&sync_timestamp=1600349122280&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.5.1.tgz"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formdata-node@^4.3.2:
  version "4.4.1"
  resolved "https://registry.npmmirror.com/formdata-node/-/formdata-node-4.4.1.tgz#23f6a5cb9cb55315912cbec4ff7b0f59bbd191e2"
  integrity sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==
  dependencies:
    node-domexception "1.0.0"
    web-streams-polyfill "4.0.0-beta.3"

formidable@^1.2.0:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/formidable/download/formidable-1.2.2.tgz"

formstream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/formstream/download/formstream-1.1.0.tgz"
  dependencies:
    destroy "^1.0.4"
    mime "^1.3.4"
    pause-stream "~0.0.11"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  dependencies:
    map-cache "^0.2.2"

fresh@~0.5.2:
  version "0.5.2"
  resolved "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz"

from@~0:
  version "0.1.7"
  resolved "https://registry.npmmirror.com/from/download/from-0.1.7.tgz"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs-constants/download/fs-constants-1.0.0.tgz"

fs-extra@^10.0.1:
  version "10.1.0"
  resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-readdir-recursive@^1.0.0, fs-readdir-recursive@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/fs-readdir-recursive/download/fs-readdir-recursive-1.1.0.tgz"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz"

fsevents@^2.3.2, fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"

fstream@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npmmirror.com/fstream/download/fstream-1.0.12.tgz"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

ftp@~0.3.10:
  version "0.3.10"
  resolved "https://registry.npmmirror.com/ftp/download/ftp-0.3.10.tgz"
  dependencies:
    readable-stream "1.1.x"
    xregexp "2.0.0"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz?cache=0&sync_timestamp=1599829540798&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffunctional-red-black-tree%2Fdownload%2Ffunctional-red-black-tree-1.0.1.tgz"

generate-function@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/generate-function/download/generate-function-2.3.1.tgz"
  dependencies:
    is-property "^1.0.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz"

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-1.0.3.tgz"

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"

get-intrinsic@^1.0.0, get-intrinsic@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.0.1.tgz?cache=0&sync_timestamp=1604120586779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-intrinsic%2Fdownload%2Fget-intrinsic-1.0.1.tgz"
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/get-package-type/-/get-package-type-0.1.0.tgz"

get-port@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/get-port/download/get-port-5.1.1.tgz"

get-ready@^2.0.0, get-ready@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/get-ready/download/get-ready-2.0.1.tgz"
  dependencies:
    is-type-of "^1.0.0"

get-ready@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/get-ready/download/get-ready-1.0.0.tgz"

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz"
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/get-stream/-/get-stream-5.2.0.tgz"
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"

get-uri@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/get-uri/download/get-uri-2.0.4.tgz"
  dependencies:
    data-uri-to-buffer "1"
    debug "2"
    extend "~3.0.2"
    file-uri-to-path "1"
    ftp "~0.3.10"
    readable-stream "2"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz"
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.1.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.1.tgz"
  dependencies:
    is-glob "^4.0.1"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"

glob@7.1.3:
  version "7.1.3"
  resolved "https://registry.npmmirror.com/glob/-/glob-7.1.3.tgz"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.1.6"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0, globals@^11.7.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz"

globals@^9.18.0:
  version "9.18.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-9.18.0.tgz"

globby@^10.0.1:
  version "10.0.2"
  resolved "https://registry.npmmirror.com/globby/download/globby-10.0.2.tgz?cache=0&sync_timestamp=1600349143804&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-10.0.2.tgz"
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.0.3"
    glob "^7.1.3"
    ignore "^5.1.1"
    merge2 "^1.2.3"
    slash "^3.0.0"

globby@^8.0.2:
  version "8.0.2"
  resolved "https://registry.npmmirror.com/globby/download/globby-8.0.2.tgz?cache=0&sync_timestamp=1600349143804&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-8.0.2.tgz"
  dependencies:
    array-union "^1.0.1"
    dir-glob "2.0.0"
    fast-glob "^2.0.2"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://registry.npmmirror.com/globby/download/globby-9.2.0.tgz?cache=0&sync_timestamp=1600349143804&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobby%2Fdownload%2Fglobby-9.2.0.tgz"
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.2, graceful-fs@^4.2.9:
  version "4.2.10"
  resolved "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.10.tgz"

graceful-process@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/graceful-process/download/graceful-process-1.2.0.tgz"
  dependencies:
    is-type-of "^1.2.0"
    once "^1.4.0"

graceful@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/graceful/download/graceful-1.0.2.tgz"
  dependencies:
    humanize-ms "^1.2.1"
    ps-tree "^1.1.0"

growl@1.10.5:
  version "1.10.5"
  resolved "https://registry.npmmirror.com/growl/download/growl-1.10.5.tgz"

growly@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/growly/-/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/has-ansi/download/has-ansi-2.0.0.tgz"
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz"

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"

has-symbols@^1.0.0, has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.1.tgz"

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmmirror.com/has-value/download/has-value-0.3.1.tgz"
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/has-value/download/has-value-1.0.0.tgz"
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/has-values/download/has-values-0.1.4.tgz"

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/has-values/download/has-values-1.0.0.tgz"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/has/download/has-1.0.3.tgz"
  dependencies:
    function-bind "^1.1.1"

hasha@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/hasha/download/hasha-3.0.0.tgz?cache=0&sync_timestamp=1602204231185&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhasha%2Fdownload%2Fhasha-3.0.0.tgz"
  dependencies:
    is-stream "^1.0.1"

he@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/he/-/he-1.2.0.tgz"

header-case@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/header-case/download/header-case-1.0.1.tgz"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.3"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/home-or-tmp/download/home-or-tmp-2.0.0.tgz"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.8.tgz"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/html-escaper/-/html-escaper-2.0.2.tgz"

htmlparser2@^3.9.1:
  version "3.10.1"
  resolved "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-3.10.1.tgz"
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-assert@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/http-assert/download/http-assert-1.4.1.tgz"
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.7.2"

http-errors@1.7.3, http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz"
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@^1.3.1, http-errors@^1.6.3:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.8.0.tgz"
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-parser-js@>=0.5.1:
  version "0.5.3"
  resolved "https://registry.npmmirror.com/http-parser-js/download/http-parser-js-0.5.3.tgz?cache=0&sync_timestamp=1609539829589&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-parser-js%2Fdownload%2Fhttp-parser-js-0.5.3.tgz"

http-proxy-agent@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/http-proxy-agent/download/http-proxy-agent-2.1.0.tgz"
  dependencies:
    agent-base "4"
    debug "3.1.0"

http@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.npmmirror.com/http/-/http-0.0.1-security.tgz"

https-proxy-agent@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/https-proxy-agent/download/https-proxy-agent-3.0.1.tgz"
  dependencies:
    agent-base "^4.3.0"
    debug "^3.1.0"

https@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/https/-/https-1.0.0.tgz"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/human-signals/-/human-signals-1.1.1.tgz"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz"

humanize-bytes@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/humanize-bytes/download/humanize-bytes-1.0.1.tgz"
  dependencies:
    bytes "~2.2.0"

humanize-ms@^1.2.0, humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/humanize-ms/download/humanize-ms-1.2.1.tgz"
  dependencies:
    ms "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.15, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/ieee754/download/ieee754-1.2.1.tgz?cache=0&sync_timestamp=1603838209136&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fieee754%2Fdownload%2Fieee754-1.2.1.tgz"

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz"

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz"

ignore@^5.1.1:
  version "5.1.8"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-5.1.8.tgz"

import-fresh@^3.0.0:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/import-fresh/download/import-fresh-3.2.2.tgz?cache=0&sync_timestamp=1604255567620&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.2.2.tgz"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/import-local/-/import-local-3.1.0.tgz"
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz"

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/indent-string/download/indent-string-4.0.0.tgz"

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/indexof/download/indexof-0.0.1.tgz"

inflation@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/inflation/download/inflation-2.0.0.tgz"

inflection@^1.12.0, inflection@^1.13.2:
  version "1.13.2"
  resolved "https://registry.npmmirror.com/inflection/-/inflection-1.13.2.tgz"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz"

ini@^1.3.4:
  version "1.3.5"
  resolved "https://registry.npmmirror.com/ini/download/ini-1.3.5.tgz"

inquirer@^6.2.2:
  version "6.5.2"
  resolved "https://registry.npmmirror.com/inquirer/download/inquirer-6.5.2.tgz"
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

inspector-proxy@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/inspector-proxy/download/inspector-proxy-1.2.1.tgz"
  dependencies:
    cfork "^1.6.1"
    debug "^3.0.1"
    tcp-proxy.js "^1.0.5"
    urllib "^2.24.0"

intelli-espower-loader@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/intelli-espower-loader/-/intelli-espower-loader-1.1.0.tgz"
  dependencies:
    espower-loader "^1.0.0"

internal-slot@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/internal-slot/download/internal-slot-1.0.2.tgz"
  dependencies:
    es-abstract "^1.17.0-next.1"
    has "^1.0.3"
    side-channel "^1.0.2"

invariant@^2.2.0, invariant@^2.2.2:
  version "2.2.4"
  resolved "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/invert-kv/download/invert-kv-1.0.0.tgz"

invert-kv@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/invert-kv/download/invert-kv-2.0.0.tgz"

ioredis@^4.22.0:
  version "4.27.3"
  resolved "https://registry.npmmirror.com/ioredis/download/ioredis-4.27.3.tgz"
  dependencies:
    cluster-key-slot "^1.1.0"
    debug "^4.3.1"
    denque "^1.1.0"
    lodash.defaults "^4.2.0"
    lodash.flatten "^4.4.0"
    p-map "^2.1.0"
    redis-commands "1.7.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^2.1.0"

ioredis@^4.9.0:
  version "4.19.2"
  resolved "https://registry.npmmirror.com/ioredis/download/ioredis-4.19.2.tgz"
  dependencies:
    cluster-key-slot "^1.1.0"
    debug "^4.1.1"
    denque "^1.1.0"
    lodash.defaults "^4.2.0"
    lodash.flatten "^4.4.0"
    p-map "^2.1.0"
    redis-commands "1.6.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^2.0.1"

ip@1.1.5, ip@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz"

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5, is-buffer@~1.1.6:
  version "1.1.6"
  resolved "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz?cache=0&sync_timestamp=1604429388528&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-1.1.6.tgz"

is-buffer@~2.0.3:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/is-buffer/-/is-buffer-2.0.5.tgz"

is-callable@^1.1.4, is-callable@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/is-callable/download/is-callable-1.2.2.tgz"

is-class-hotfix@~0.0.6:
  version "0.0.6"
  resolved "https://registry.npmmirror.com/is-class-hotfix/download/is-class-hotfix-0.0.6.tgz"

is-core-module@^2.9.0:
  version "2.11.0"
  resolved "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.11.0.tgz"
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-date-object/download/is-date-object-1.0.2.tgz"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-docker@^2.0.0:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/is-docker/-/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz"

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-extendable/download/is-extendable-1.0.1.tgz"
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz"

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-finite/download/is-finite-1.1.0.tgz"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-generator-fn/-/is-generator-fn-2.1.0.tgz"

is-generator-function@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/is-generator-function/download/is-generator-function-1.0.7.tgz"

is-generator@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/is-generator/download/is-generator-1.0.3.tgz"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz"
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.1.tgz"
  dependencies:
    is-extglob "^2.1.1"

is-lower-case@^1.1.0:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/is-lower-case/download/is-lower-case-1.1.3.tgz?cache=0&sync_timestamp=1575601673660&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-lower-case%2Fdownload%2Fis-lower-case-1.1.3.tgz"
  dependencies:
    lower-case "^1.1.0"

is-nan@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/is-nan/download/is-nan-1.3.0.tgz"
  dependencies:
    define-properties "^1.1.3"

is-negative-zero@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/is-negative-zero/download/is-negative-zero-2.0.0.tgz"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-number/download/is-number-3.0.0.tgz"
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"

is-obj@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-obj/-/is-obj-1.0.1.tgz"

is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz"

is-path-inside@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/is-path-inside/download/is-path-inside-3.0.2.tgz"

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz?cache=0&sync_timestamp=1600349132080&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-plain-object%2Fdownload%2Fis-plain-object-2.0.4.tgz"
  dependencies:
    isobject "^3.0.1"

is-property@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-property/download/is-property-1.0.2.tgz"

is-regex@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/is-regex/download/is-regex-1.1.1.tgz"
  dependencies:
    has-symbols "^1.0.1"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz"

is-string@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/is-string/download/is-string-1.0.5.tgz"

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/is-symbol/download/is-symbol-1.0.3.tgz"
  dependencies:
    has-symbols "^1.0.1"

is-type-of@^1.0.0, is-type-of@^1.1.0, is-type-of@^1.2.0, is-type-of@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/is-type-of/download/is-type-of-1.2.1.tgz"
  dependencies:
    core-util-is "^1.0.2"
    is-class-hotfix "~0.0.6"
    isstream "~0.1.2"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-upper-case@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/is-upper-case/download/is-upper-case-1.1.2.tgz"
  dependencies:
    upper-case "^1.1.0"

is-url@^1.2.1:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/is-url/-/is-url-1.2.4.tgz"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/is-utf8/download/is-utf8-0.2.1.tgz"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-windows/download/is-windows-1.0.2.tgz"

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/is-wsl/-/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz"

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz"
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/isstream/download/isstream-0.1.2.tgz"

istanbul-lib-coverage@^2.0.3, istanbul-lib-coverage@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-2.0.5.tgz"

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz"

istanbul-lib-hook@^2.0.3:
  version "2.0.7"
  resolved "https://registry.npmmirror.com/istanbul-lib-hook/-/istanbul-lib-hook-2.0.7.tgz#c95695f383d4f8f60df1f04252a9550e15b5b133"
  dependencies:
    append-transform "^1.0.0"

istanbul-lib-instrument@^3.1.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-3.3.0.tgz"
  dependencies:
    "@babel/generator" "^7.4.0"
    "@babel/parser" "^7.4.3"
    "@babel/template" "^7.4.0"
    "@babel/traverse" "^7.4.3"
    "@babel/types" "^7.4.0"
    istanbul-lib-coverage "^2.0.5"
    semver "^6.0.0"

istanbul-lib-instrument@^5.0.4, istanbul-lib-instrument@^5.1.0:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^2.0.4:
  version "2.0.8"
  resolved "https://registry.npmmirror.com/istanbul-lib-report/-/istanbul-lib-report-2.0.8.tgz#5a8113cd746d43c4889eba36ab10e7d50c9b4f33"
  dependencies:
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    supports-color "^6.1.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^3.0.2:
  version "3.0.6"
  resolved "https://registry.npmmirror.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.6.tgz#284997c48211752ec486253da97e3879defba8c8"
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    rimraf "^2.6.3"
    source-map "^0.6.1"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^2.1.1:
  version "2.2.7"
  resolved "https://registry.npmmirror.com/istanbul-reports/-/istanbul-reports-2.2.7.tgz#5d939f6237d7b48393cc0959eab40cd4fd056931"
  dependencies:
    html-escaper "^2.0.0"

istanbul-reports@^3.1.3:
  version "3.1.5"
  resolved "https://registry.npmmirror.com/istanbul-reports/-/istanbul-reports-3.1.5.tgz"
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jest-changed-files@^25.5.0:
  version "25.5.0"
  resolved "https://registry.npmmirror.com/jest-changed-files/-/jest-changed-files-25.5.0.tgz"
  dependencies:
    "@jest/types" "^25.5.0"
    execa "^3.2.0"
    throat "^5.0.0"

jest-changed-files@^29.2.0:
  version "29.2.0"
  resolved "https://registry.npmmirror.com/jest-changed-files/-/jest-changed-files-29.2.0.tgz"
  dependencies:
    execa "^5.0.0"
    p-limit "^3.1.0"

jest-circus@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-circus/-/jest-circus-29.3.1.tgz"
  dependencies:
    "@jest/environment" "^29.3.1"
    "@jest/expect" "^29.3.1"
    "@jest/test-result" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^0.7.0"
    is-generator-fn "^2.0.0"
    jest-each "^29.3.1"
    jest-matcher-utils "^29.3.1"
    jest-message-util "^29.3.1"
    jest-runtime "^29.3.1"
    jest-snapshot "^29.3.1"
    jest-util "^29.3.1"
    p-limit "^3.1.0"
    pretty-format "^29.3.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-cli/-/jest-cli-29.3.1.tgz"
  dependencies:
    "@jest/core" "^29.3.1"
    "@jest/test-result" "^29.3.1"
    "@jest/types" "^29.3.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    import-local "^3.0.2"
    jest-config "^29.3.1"
    jest-util "^29.3.1"
    jest-validate "^29.3.1"
    prompts "^2.0.1"
    yargs "^17.3.1"

jest-config@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-config/-/jest-config-29.3.1.tgz"
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^29.3.1"
    "@jest/types" "^29.3.1"
    babel-jest "^29.3.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^29.3.1"
    jest-environment-node "^29.3.1"
    jest-get-type "^29.2.0"
    jest-regex-util "^29.2.0"
    jest-resolve "^29.3.1"
    jest-runner "^29.3.1"
    jest-util "^29.3.1"
    jest-validate "^29.3.1"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^29.3.1"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-diff/-/jest-diff-29.3.1.tgz"
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.3.1"
    jest-get-type "^29.2.0"
    pretty-format "^29.3.1"

jest-docblock@^29.2.0:
  version "29.2.0"
  resolved "https://registry.npmmirror.com/jest-docblock/-/jest-docblock-29.2.0.tgz"
  dependencies:
    detect-newline "^3.0.0"

jest-each@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-each/-/jest-each-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    chalk "^4.0.0"
    jest-get-type "^29.2.0"
    jest-util "^29.3.1"
    pretty-format "^29.3.1"

jest-environment-node@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-environment-node/-/jest-environment-node-29.3.1.tgz"
  dependencies:
    "@jest/environment" "^29.3.1"
    "@jest/fake-timers" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    jest-mock "^29.3.1"
    jest-util "^29.3.1"

jest-get-type@^29.2.0:
  version "29.2.0"
  resolved "https://registry.npmmirror.com/jest-get-type/-/jest-get-type-29.2.0.tgz"

jest-haste-map@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-haste-map/-/jest-haste-map-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.2.0"
    jest-util "^29.3.1"
    jest-worker "^29.3.1"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-leak-detector/-/jest-leak-detector-29.3.1.tgz"
  dependencies:
    jest-get-type "^29.2.0"
    pretty-format "^29.3.1"

jest-matcher-utils@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-matcher-utils/-/jest-matcher-utils-29.3.1.tgz"
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.3.1"
    jest-get-type "^29.2.0"
    pretty-format "^29.3.1"

jest-message-util@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-message-util/-/jest-message-util-29.3.1.tgz"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^29.3.1"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^29.3.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-mock/-/jest-mock-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    jest-util "^29.3.1"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"

jest-regex-util@^29.2.0:
  version "29.2.0"
  resolved "https://registry.npmmirror.com/jest-regex-util/-/jest-regex-util-29.2.0.tgz"

jest-resolve-dependencies@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-resolve-dependencies/-/jest-resolve-dependencies-29.3.1.tgz"
  dependencies:
    jest-regex-util "^29.2.0"
    jest-snapshot "^29.3.1"

jest-resolve@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-resolve/-/jest-resolve-29.3.1.tgz"
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.3.1"
    jest-pnp-resolver "^1.2.2"
    jest-util "^29.3.1"
    jest-validate "^29.3.1"
    resolve "^1.20.0"
    resolve.exports "^1.1.0"
    slash "^3.0.0"

jest-runner@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-runner/-/jest-runner-29.3.1.tgz"
  dependencies:
    "@jest/console" "^29.3.1"
    "@jest/environment" "^29.3.1"
    "@jest/test-result" "^29.3.1"
    "@jest/transform" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.13.1"
    graceful-fs "^4.2.9"
    jest-docblock "^29.2.0"
    jest-environment-node "^29.3.1"
    jest-haste-map "^29.3.1"
    jest-leak-detector "^29.3.1"
    jest-message-util "^29.3.1"
    jest-resolve "^29.3.1"
    jest-runtime "^29.3.1"
    jest-util "^29.3.1"
    jest-watcher "^29.3.1"
    jest-worker "^29.3.1"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-runtime/-/jest-runtime-29.3.1.tgz"
  dependencies:
    "@jest/environment" "^29.3.1"
    "@jest/fake-timers" "^29.3.1"
    "@jest/globals" "^29.3.1"
    "@jest/source-map" "^29.2.0"
    "@jest/test-result" "^29.3.1"
    "@jest/transform" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.3.1"
    jest-message-util "^29.3.1"
    jest-mock "^29.3.1"
    jest-regex-util "^29.2.0"
    jest-resolve "^29.3.1"
    jest-snapshot "^29.3.1"
    jest-util "^29.3.1"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-snapshot/-/jest-snapshot-29.3.1.tgz"
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-jsx" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^29.3.1"
    "@jest/transform" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/babel__traverse" "^7.0.6"
    "@types/prettier" "^2.1.5"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^29.3.1"
    graceful-fs "^4.2.9"
    jest-diff "^29.3.1"
    jest-get-type "^29.2.0"
    jest-haste-map "^29.3.1"
    jest-matcher-utils "^29.3.1"
    jest-message-util "^29.3.1"
    jest-util "^29.3.1"
    natural-compare "^1.4.0"
    pretty-format "^29.3.1"
    semver "^7.3.5"

jest-util@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-util/-/jest-util-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-validate/-/jest-validate-29.3.1.tgz"
  dependencies:
    "@jest/types" "^29.3.1"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.2.0"
    leven "^3.1.0"
    pretty-format "^29.3.1"

jest-watcher@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-watcher/-/jest-watcher-29.3.1.tgz"
  dependencies:
    "@jest/test-result" "^29.3.1"
    "@jest/types" "^29.3.1"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.13.1"
    jest-util "^29.3.1"
    string-length "^4.0.1"

jest-worker@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest-worker/-/jest-worker-29.3.1.tgz"
  dependencies:
    "@types/node" "*"
    jest-util "^29.3.1"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/jest/-/jest-29.3.1.tgz#c130c0d551ae6b5459b8963747fed392ddbde122"
  dependencies:
    "@jest/core" "^29.3.1"
    "@jest/types" "^29.3.1"
    import-local "^3.0.2"
    jest-cli "^29.3.1"

js-levenshtein@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmmirror.com/js-levenshtein/-/js-levenshtein-1.1.6.tgz"

js-tokens@^3.0.0, js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/js-tokens/download/js-tokens-3.0.2.tgz"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"

js-yaml@3.13.1:
  version "3.13.1"
  resolved "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.13.1.tgz"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^3.13.0, js-yaml@^3.13.1:
  version "3.14.0"
  resolved "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.0.tgz"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsdoctypeparser@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/jsdoctypeparser/download/jsdoctypeparser-3.1.0.tgz"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/jsesc/download/jsesc-1.3.0.tgz?cache=0&sync_timestamp=1603891198638&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-1.3.0.tgz"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/jsesc/download/jsesc-2.5.2.tgz?cache=0&sync_timestamp=1603891198638&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-2.5.2.tgz"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npmmirror.com/jsesc/download/jsesc-0.5.0.tgz?cache=0&sync_timestamp=1603891198638&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-0.5.0.tgz"

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz?cache=0&sync_timestamp=1599333999343&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson-schema-traverse%2Fdownload%2Fjson-schema-traverse-0.4.1.tgz"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"

json-stringify-safe@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"

json2xls@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/json2xls/-/json2xls-0.1.2.tgz"
  dependencies:
    excel-export "~0.3.11"

json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmmirror.com/json5/download/json5-0.5.1.tgz"

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/json5/download/json5-1.0.1.tgz"
  dependencies:
    minimist "^1.2.0"

json5@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/json5/-/json5-2.2.1.tgz"

json5@^2.2.1:
  version "2.2.2"
  resolved "https://registry.npmmirror.com/json5/-/json5-2.2.2.tgz"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonp-body@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/jsonp-body/download/jsonp-body-1.0.0.tgz"

jssha@2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/jssha/download/jssha-2.3.1.tgz"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/jsx-ast-utils/download/jsx-ast-utils-3.1.0.tgz?cache=0&sync_timestamp=1602640057099&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsx-ast-utils%2Fdownload%2Fjsx-ast-utils-3.1.0.tgz"
  dependencies:
    array-includes "^3.1.1"
    object.assign "^4.1.1"

jszip@2.5.0:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/jszip/download/jszip-2.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjszip%2Fdownload%2Fjszip-2.5.0.tgz"
  dependencies:
    pako "~0.2.5"

keygrip@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/keygrip/download/keygrip-1.1.0.tgz"
  dependencies:
    tsscmp "1.0.6"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-4.0.0.tgz"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz"

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/kleur/-/kleur-3.0.3.tgz"

ko-sleep@^1.0.2, ko-sleep@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/ko-sleep/download/ko-sleep-1.0.3.tgz"
  dependencies:
    ms "^2.0.0"

koa-bodyparser@^4.2.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/koa-bodyparser/download/koa-bodyparser-4.3.0.tgz"
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/koa-compose/download/koa-compose-3.2.1.tgz"
  dependencies:
    any-promise "^1.1.0"

koa-compose@^4.0.0, koa-compose@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/koa-compose/download/koa-compose-4.1.0.tgz"

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/koa-convert/download/koa-convert-1.2.0.tgz"
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

koa-is-json@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/koa-is-json/download/koa-is-json-1.0.0.tgz"

koa-locales@^1.11.0:
  version "1.12.0"
  resolved "https://registry.npmmirror.com/koa-locales/download/koa-locales-1.12.0.tgz"
  dependencies:
    debug "^2.6.0"
    humanize-ms "^1.2.0"
    ini "^1.3.4"
    object-assign "^4.1.0"

koa-onerror@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/koa-onerror/download/koa-onerror-4.1.0.tgz"
  dependencies:
    escape-html "^1.0.3"
    stream-wormhole "^1.1.0"

koa-override@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/koa-override/download/koa-override-3.0.0.tgz"
  dependencies:
    methods "^1.1.2"

koa-range@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/koa-range/download/koa-range-0.3.0.tgz"
  dependencies:
    stream-slice "^0.1.2"

koa-session@^6.0.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/koa-session/download/koa-session-6.1.0.tgz"
  dependencies:
    crc "^3.4.4"
    debug "^3.1.0"
    is-type-of "^1.0.0"
    uuid "^3.3.2"

koa-static-cache@^5.1.2:
  version "5.1.4"
  resolved "https://registry.npmmirror.com/koa-static-cache/download/koa-static-cache-5.1.4.tgz"
  dependencies:
    compressible "^2.0.6"
    debug "^3.1.0"
    fs-readdir-recursive "^1.0.0"
    mime-types "^2.1.8"
    mz "^2.7.0"

koa@^2.7.0:
  version "2.13.0"
  resolved "https://registry.npmmirror.com/koa/download/koa-2.13.0.tgz"
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.8.0"
    debug "~3.1.0"
    delegates "^1.0.0"
    depd "^1.1.2"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^1.2.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

language-subtag-registry@~0.3.2:
  version "0.3.21"
  resolved "https://registry.npmmirror.com/language-subtag-registry/download/language-subtag-registry-0.3.21.tgz?cache=0&sync_timestamp=1603785598662&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flanguage-subtag-registry%2Fdownload%2Flanguage-subtag-registry-0.3.21.tgz"

language-tags@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/language-tags/download/language-tags-1.0.5.tgz"
  dependencies:
    language-subtag-registry "~0.3.2"

lazystream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/lazystream/download/lazystream-1.0.0.tgz"
  dependencies:
    readable-stream "^2.0.5"

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/lcid/download/lcid-1.0.0.tgz"
  dependencies:
    invert-kv "^1.0.0"

lcid@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/lcid/download/lcid-2.0.0.tgz"
  dependencies:
    invert-kv "^2.0.0"

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/leven/-/leven-3.1.0.tgz"

levenshtein-edit-distance@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/levenshtein-edit-distance/-/levenshtein-edit-distance-2.0.5.tgz#a066eca8afb350e4d9054aed9ffeef66e78ffc83"
  integrity sha512-Yuraz7QnMX/JENJU1HA6UtdsbhRzoSFnGpVGVryjQgHtl2s/YmVgmNYkVs5yzVZ9aAvQR9wPBUH3lG755ylxGA==

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/levn/download/levn-0.3.0.tgz"
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz"

listenercount@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/listenercount/download/listenercount-1.0.1.tgz"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/load-json-file/download/load-json-file-1.1.0.tgz"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/load-json-file/download/load-json-file-2.0.0.tgz"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/load-json-file/download/load-json-file-4.0.0.tgz"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/locate-path/download/locate-path-2.0.0.tgz"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz"
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz"
  dependencies:
    p-locate "^4.1.0"

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz"

lodash.difference@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/lodash.difference/download/lodash.difference-4.5.0.tgz"

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmmirror.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz"

lodash.flattendeep@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmmirror.com/lodash.flattendeep/download/lodash.flattendeep-4.4.0.tgz"

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz"

lodash.union@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/lodash.union/download/lodash.union-4.6.0.tgz"

lodash@^4.15.0, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.2, lodash@^4.17.21, lodash@^4.17.4, lodash@^4.2.0:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"

log-symbols@2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/log-symbols/-/log-symbols-2.2.0.tgz"
  dependencies:
    chalk "^2.0.1"

long@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/long/download/long-4.0.0.tgz"

long@^5.2.1:
  version "5.2.3"
  resolved "https://registry.npmmirror.com/long/-/long-5.2.3.tgz#a3ba97f3877cf1d778eccbcb048525ebb77499e1"

loose-envify@^1.0.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case-first@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/lower-case-first/download/lower-case-first-1.0.2.tgz"
  dependencies:
    lower-case "^1.1.2"

lower-case@^1.1.0, lower-case@^1.1.1, lower-case@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/lower-case/download/lower-case-1.1.4.tgz"

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/lru-cache/download/lru-cache-5.1.1.tgz"
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.14.1:
  version "7.18.3"
  resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-7.18.3.tgz#f793896e0fd0e954a59dfdd82f0773808df6aa89"

lru.min@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/lru.min/-/lru.min-1.1.1.tgz#146e01e3a183fa7ba51049175de04667d5701f0e"

make-dir@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/make-dir/download/make-dir-1.3.0.tgz"
  dependencies:
    pify "^3.0.0"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz"
  dependencies:
    semver "^6.0.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "https://registry.npmmirror.com/make-error/-/make-error-1.3.6.tgz"

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry.npmmirror.com/makeerror/-/makeerror-1.0.12.tgz"
  dependencies:
    tmpl "1.0.5"

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "https://registry.npmmirror.com/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz"
  dependencies:
    p-defer "^1.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/map-cache/download/map-cache-0.2.2.tgz"

map-stream@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/map-stream/download/map-stream-0.1.0.tgz"

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/map-visit/download/map-visit-1.0.0.tgz"
  dependencies:
    object-visit "^1.0.0"

matcher@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/matcher/download/matcher-1.1.1.tgz"
  dependencies:
    escape-string-regexp "^1.0.4"

md5@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/md5/download/md5-2.3.0.tgz?cache=0&sync_timestamp=1596365225537&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmd5%2Fdownload%2Fmd5-2.3.0.tgz"
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/media-typer/download/media-typer-0.3.0.tgz"

mem@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/mem/download/mem-4.3.0.tgz?cache=0&sync_timestamp=1602347248860&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmem%2Fdownload%2Fmem-4.3.0.tgz"
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

memorystream@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmmirror.com/memorystream/download/memorystream-0.3.1.tgz"

merge-descriptors@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"

merge-estraverse-visitors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/merge-estraverse-visitors/-/merge-estraverse-visitors-1.0.0.tgz"
  dependencies:
    estraverse "^4.0.0"

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz"

merge2@^1.2.3, merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/merge2/download/merge2-1.4.1.tgz"

methods@^1.0.1, methods@^1.1.1, methods@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz"

micromatch@^3.1.10:
  version "3.1.10"
  resolved "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/micromatch/download/micromatch-4.0.2.tgz"
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

micromatch@^4.0.4:
  version "4.0.5"
  resolved "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz"
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.44.0:
  version "1.44.0"
  resolved "https://registry.npmmirror.com/mime-db/download/mime-db-1.44.0.tgz?cache=0&sync_timestamp=1600831175828&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.44.0.tgz"

"mime-db@>= 1.43.0 < 2":
  version "1.45.0"
  resolved "https://registry.npmmirror.com/mime-db/download/mime-db-1.45.0.tgz?cache=0&sync_timestamp=1600831175828&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.45.0.tgz"

mime-types@^2.1.12, mime-types@^2.1.18, mime-types@^2.1.8, mime-types@~2.1.24:
  version "2.1.27"
  resolved "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.27.tgz"
  dependencies:
    mime-db "1.44.0"

mime@^1.3.4, mime@^1.4.1:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz?cache=0&sync_timestamp=1592843216793&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime%2Fdownload%2Fmime-1.6.0.tgz"

mime@^2.4.6:
  version "2.4.6"
  resolved "https://registry.npmmirror.com/mime/download/mime-2.4.6.tgz?cache=0&sync_timestamp=1592843216793&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime%2Fdownload%2Fmime-2.4.6.tgz"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz"

mimic-fn@^2.0.0, mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz"

minimatch@3.0.4, minimatch@^3.0.0, minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz"
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.0, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz"

minimist@^1.2.6:
  version "1.2.7"
  resolved "https://registry.npmmirror.com/minimist/-/minimist-1.2.7.tgz#daa1c4d91f507390437c6a8bc01078e7000c4d18"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.4.tgz"
  dependencies:
    minimist "^1.2.5"

"mkdirp@>=0.5 0", mkdirp@^0.5.1:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1600349118431&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz"
  dependencies:
    minimist "^1.2.5"

mkdirp@^0.5.0:
  version "0.5.6"
  resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  dependencies:
    minimist "^1.2.6"

mm@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/mm/download/mm-2.5.0.tgz"
  dependencies:
    is-type-of "^1.0.0"
    ko-sleep "^1.0.2"
    muk-prop "^1.0.0"
    thenify "^3.2.1"

mocha@^6.0.2:
  version "6.2.3"
  resolved "https://registry.npmmirror.com/mocha/-/mocha-6.2.3.tgz"
  dependencies:
    ansi-colors "3.2.3"
    browser-stdout "1.3.1"
    debug "3.2.6"
    diff "3.5.0"
    escape-string-regexp "1.0.5"
    find-up "3.0.0"
    glob "7.1.3"
    growl "1.10.5"
    he "1.2.0"
    js-yaml "3.13.1"
    log-symbols "2.2.0"
    minimatch "3.0.4"
    mkdirp "0.5.4"
    ms "2.1.1"
    node-environment-flags "1.0.5"
    object.assign "4.1.0"
    strip-json-comments "2.0.1"
    supports-color "6.0.0"
    which "1.3.1"
    wide-align "1.1.3"
    yargs "13.3.2"
    yargs-parser "13.1.2"
    yargs-unparser "1.6.0"

moment-timezone@^0.5.31, moment-timezone@^0.5.34:
  version "0.5.34"
  resolved "https://registry.npmmirror.com/moment-timezone/-/moment-timezone-0.5.34.tgz"
  dependencies:
    moment ">= 2.9.0"

"moment@>= 2.9.0", moment@^2.22.2, moment@^2.23.0, moment@^2.24.0, moment@^2.29.1:
  version "2.29.1"
  resolved "https://registry.npmmirror.com/moment/download/moment-2.29.1.tgz?cache=0&sync_timestamp=1601983320283&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmoment%2Fdownload%2Fmoment-2.29.1.tgz"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz"

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.1.1.tgz"

ms@2.1.2, ms@^2.0.0, ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz"

muk-prop@^1.0.0:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/muk-prop/download/muk-prop-1.2.1.tgz"

multi-stage-sourcemap@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/multi-stage-sourcemap/-/multi-stage-sourcemap-0.2.1.tgz"
  dependencies:
    source-map "^0.1.34"

multimatch@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/multimatch/download/multimatch-2.1.0.tgz?cache=0&sync_timestamp=1602519380282&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmultimatch%2Fdownload%2Fmultimatch-2.1.0.tgz"
  dependencies:
    array-differ "^1.0.0"
    array-union "^1.0.1"
    arrify "^1.0.0"
    minimatch "^3.0.0"

mustache@^2.3.0:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/mustache/download/mustache-2.3.2.tgz"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://registry.npmmirror.com/mute-stream/download/mute-stream-0.0.7.tgz"

mysql2@^3.11.5:
  version "3.11.5"
  resolved "https://registry.npmmirror.com/mysql2/-/mysql2-3.11.5.tgz#1a1cb9a61e78d636da10e122a3c4d6978ad08625"
  dependencies:
    aws-ssl-profiles "^1.1.1"
    denque "^2.1.0"
    generate-function "^2.3.1"
    iconv-lite "^0.6.3"
    long "^5.2.1"
    lru.min "^1.0.0"
    named-placeholders "^1.1.3"
    seq-queue "^0.0.5"
    sqlstring "^2.3.2"

mysql@^2.13.0:
  version "2.18.1"
  resolved "https://registry.npmmirror.com/mysql/download/mysql-2.18.1.tgz"
  dependencies:
    bignumber.js "9.0.0"
    readable-stream "2.3.7"
    safe-buffer "5.1.2"
    sqlstring "2.3.1"

mz-modules@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/mz-modules/download/mz-modules-2.1.0.tgz"
  dependencies:
    glob "^7.1.2"
    ko-sleep "^1.0.3"
    mkdirp "^0.5.1"
    pump "^3.0.0"
    rimraf "^2.6.1"

mz@^2.6.0, mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz"
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

named-placeholders@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/named-placeholders/-/named-placeholders-1.1.3.tgz#df595799a36654da55dda6152ba7a137ad1d9351"
  dependencies:
    lru-cache "^7.14.1"

nanoid@^2.0.1:
  version "2.1.11"
  resolved "https://registry.npmmirror.com/nanoid/download/nanoid-2.1.11.tgz?cache=0&sync_timestamp=1605907888580&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnanoid%2Fdownload%2Fnanoid-2.1.11.tgz"

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.npmmirror.com/nanomatch/download/nanomatch-1.2.13.tgz"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/natural-compare/download/natural-compare-1.4.0.tgz"

ndir@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/ndir/download/ndir-0.1.5.tgz"

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz"

netmask@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/netmask/download/netmask-1.0.6.tgz"

next-tick@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/next-tick/-/next-tick-1.1.0.tgz"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz"

no-case@^2.2.0, no-case@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/no-case/download/no-case-2.3.2.tgz"
  dependencies:
    lower-case "^1.1.1"

node-domexception@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/node-domexception/-/node-domexception-1.0.0.tgz#6888db46a1f71c0b76b3f7555016b63fe64766e5"
  integrity sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==

node-environment-flags@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/node-environment-flags/-/node-environment-flags-1.0.5.tgz"
  dependencies:
    object.getownpropertydescriptors "^2.0.3"
    semver "^5.7.0"

node-fetch@^2.6.1:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.1.tgz"

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.7.0.tgz#d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-homedir@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/node-homedir/download/node-homedir-1.1.1.tgz"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/node-int64/-/node-int64-0.4.0.tgz"

node-notifier@^10.0.1:
  version "10.0.1"
  resolved "https://registry.npmmirror.com/node-notifier/-/node-notifier-10.0.1.tgz#0e82014a15a8456c4cfcdb25858750399ae5f1c7"
  dependencies:
    growly "^1.3.0"
    is-wsl "^2.2.0"
    semver "^7.3.5"
    shellwords "^0.1.1"
    uuid "^8.3.2"
    which "^2.0.2"

node-releases@^2.0.6:
  version "2.0.8"
  resolved "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.8.tgz"

node-uuid@^1.4.8:
  version "1.4.8"
  resolved "https://registry.npmmirror.com/node-uuid/download/node-uuid-1.4.8.tgz"

node-zip@1.x:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/node-zip/download/node-zip-1.1.1.tgz"
  dependencies:
    jszip "2.5.0"

normalize-package-data@^2.3.2:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1602547447569&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz"
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  dependencies:
    path-key "^3.0.0"

nth-check@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/nth-check/download/nth-check-1.0.2.tgz"
  dependencies:
    boolbase "~1.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/number-is-nan/download/number-is-nan-1.0.1.tgz"

nyc@^13.3.0:
  version "13.3.0"
  resolved "https://registry.npmmirror.com/nyc/download/nyc-13.3.0.tgz?cache=0&sync_timestamp=1599832510085&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnyc%2Fdownload%2Fnyc-13.3.0.tgz"
  dependencies:
    archy "^1.0.0"
    arrify "^1.0.1"
    caching-transform "^3.0.1"
    convert-source-map "^1.6.0"
    find-cache-dir "^2.0.0"
    find-up "^3.0.0"
    foreground-child "^1.5.6"
    glob "^7.1.3"
    istanbul-lib-coverage "^2.0.3"
    istanbul-lib-hook "^2.0.3"
    istanbul-lib-instrument "^3.1.0"
    istanbul-lib-report "^2.0.4"
    istanbul-lib-source-maps "^3.0.2"
    istanbul-reports "^2.1.1"
    make-dir "^1.3.0"
    merge-source-map "^1.1.0"
    resolve-from "^4.0.0"
    rimraf "^2.6.3"
    signal-exit "^3.0.2"
    spawn-wrap "^1.4.2"
    test-exclude "^5.1.0"
    uuid "^3.3.2"
    yargs "^12.0.5"
    yargs-parser "^11.1.1"

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz"

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/object-copy/download/object-copy-0.1.0.tgz"
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.8.0:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.8.0.tgz"

object-keys@^1.0.0, object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz"

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/object-visit/download/object-visit-1.0.1.tgz"
  dependencies:
    isobject "^3.0.0"

object.assign@4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/object.assign/-/object.assign-4.1.0.tgz"
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.assign@^4.1.0, object.assign@^4.1.1:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.2.tgz?cache=0&sync_timestamp=1604115104654&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.assign%2Fdownload%2Fobject.assign-4.1.2.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.entries@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/object.entries/download/object.entries-1.1.3.tgz?cache=0&sync_timestamp=1606431063191&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.entries%2Fdownload%2Fobject.entries-1.1.3.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    has "^1.0.3"

object.fromentries@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/object.fromentries/download/object.fromentries-2.0.2.tgz"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"
    has "^1.0.3"

object.getownpropertydescriptors@^2.0.3, object.getownpropertydescriptors@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.0.tgz"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/object.pick/download/object.pick-1.3.0.tgz"
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/object.values/download/object.values-1.1.2.tgz?cache=0&sync_timestamp=1606429851964&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.values%2Fdownload%2Fobject.values-1.1.2.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    has "^1.0.3"

on-finished@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz"
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/once/download/once-1.4.0.tgz"
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/onetime/download/onetime-2.0.1.tgz"
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
  dependencies:
    mimic-fn "^2.1.0"

only@~0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/only/download/only-0.0.2.tgz"

openai@^4.103.0:
  version "4.103.0"
  resolved "https://registry.npmmirror.com/openai/-/openai-4.103.0.tgz#84ee52fec22f3486dcc107338b2eaf568ef4924b"
  integrity sha512-eWcz9kdurkGOFDtd5ySS5y251H2uBgq9+1a2lTBnjMMzlexJ40Am5t6Mu76SSE87VvitPa0dkIAp75F+dZVC0g==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

opentype.js@^0.7.3:
  version "0.7.3"
  resolved "https://registry.npmmirror.com/opentype.js/download/opentype.js-0.7.3.tgz"
  dependencies:
    tiny-inflate "^1.0.2"

optionator@^0.8.1, optionator@^0.8.2:
  version "0.8.3"
  resolved "https://registry.npmmirror.com/optionator/download/optionator-0.8.3.tgz"
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/os-homedir/download/os-homedir-1.0.2.tgz"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/os-locale/download/os-locale-1.4.0.tgz"
  dependencies:
    lcid "^1.0.0"

os-locale@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/os-locale/download/os-locale-3.1.0.tgz"
  dependencies:
    execa "^1.0.0"
    lcid "^2.0.0"
    mem "^4.0.0"

os-name@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/os-name/download/os-name-1.0.3.tgz"
  dependencies:
    osx-release "^1.0.0"
    win-release "^1.0.0"

os-tmpdir@^1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"

osx-release@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/osx-release/download/osx-release-1.1.0.tgz"
  dependencies:
    minimist "^1.1.0"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-defer/download/p-defer-1.0.0.tgz"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz"

p-finally@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/p-finally/-/p-finally-2.0.1.tgz"

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/p-is-promise/download/p-is-promise-2.1.0.tgz"

p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/p-limit/download/p-limit-1.3.0.tgz"
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz"
  dependencies:
    p-try "^2.0.0"

p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz"
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/p-locate/download/p-locate-2.0.0.tgz"
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz"
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz"
  dependencies:
    p-limit "^2.2.0"

p-map@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-2.1.0.tgz"

p-map@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-3.0.0.tgz"
  dependencies:
    aggregate-error "^3.0.0"

p-timeout@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/p-timeout/download/p-timeout-3.2.0.tgz"
  dependencies:
    p-finally "^1.0.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-1.0.0.tgz"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz"

pac-proxy-agent@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/pac-proxy-agent/download/pac-proxy-agent-3.0.1.tgz"
  dependencies:
    agent-base "^4.2.0"
    debug "^4.1.1"
    get-uri "^2.0.0"
    http-proxy-agent "^2.1.0"
    https-proxy-agent "^3.0.0"
    pac-resolver "^3.0.0"
    raw-body "^2.2.0"
    socks-proxy-agent "^4.0.1"

pac-resolver@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pac-resolver/download/pac-resolver-3.0.0.tgz"
  dependencies:
    co "^4.6.0"
    degenerator "^1.0.4"
    ip "^1.1.5"
    netmask "^1.0.6"
    thunkify "^2.1.2"

package-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/package-hash/download/package-hash-3.0.0.tgz"
  dependencies:
    graceful-fs "^4.1.15"
    hasha "^3.0.0"
    lodash.flattendeep "^4.4.0"
    release-zalgo "^1.0.0"

pako@~0.2.5:
  version "0.2.9"
  resolved "https://registry.npmmirror.com/pako/download/pako-0.2.9.tgz?cache=0&sync_timestamp=1605743773891&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpako%2Fdownload%2Fpako-0.2.9.tgz"

param-case@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/param-case/download/param-case-2.1.1.tgz"
  dependencies:
    no-case "^2.2.0"

parameter@^3.0.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/parameter/download/parameter-3.6.0.tgz"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz"
  dependencies:
    callsites "^3.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-2.2.0.tgz"
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz"
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5@^3.0.1:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/parse5/download/parse5-3.0.3.tgz"
  dependencies:
    "@types/node" "*"

parseurl@^1.3.2:
  version "1.3.3"
  resolved "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz"

pascal-case@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/pascal-case/download/pascal-case-2.0.1.tgz"
  dependencies:
    camel-case "^3.0.0"
    upper-case-first "^1.1.0"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz"

path-case@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/path-case/download/path-case-2.1.1.tgz"
  dependencies:
    no-case "^2.2.0"

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/path-dirname/download/path-dirname-1.0.2.tgz"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/path-exists/download/path-exists-2.1.0.tgz"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/path-is-inside/download/path-is-inside-1.0.2.tgz"

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"

path-to-regexp@^1.1.1, path-to-regexp@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz"
  dependencies:
    isarray "0.0.1"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/path-type/download/path-type-1.1.0.tgz"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/path-type/download/path-type-2.0.0.tgz"
  dependencies:
    pify "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/path-type/download/path-type-3.0.0.tgz"
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-type/download/path-type-4.0.0.tgz"

pause-stream@0.0.11, pause-stream@~0.0.11:
  version "0.0.11"
  resolved "https://registry.npmmirror.com/pause-stream/download/pause-stream-0.0.11.tgz"
  dependencies:
    through "~2.3"

peek-readable@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/peek-readable/-/peek-readable-4.1.0.tgz#4ece1111bf5c2ad8867c314c81356847e8a62e72"

pg-connection-string@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/pg-connection-string/-/pg-connection-string-2.5.0.tgz"

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz"

picomatch@^2.0.4, picomatch@^2.0.5, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"

pify@^2.0.0, pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz"

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pify/download/pify-3.0.0.tgz"

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/pify/download/pify-4.0.1.tgz"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/pinkie/download/pinkie-2.0.4.tgz"

pirates@^4.0.4:
  version "4.0.5"
  resolved "https://registry.npmmirror.com/pirates/-/pirates-4.0.5.tgz"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-2.0.0.tgz"
  dependencies:
    find-up "^2.1.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz"
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz"
  dependencies:
    find-up "^4.0.0"

platform@^1.3.4:
  version "1.3.6"
  resolved "https://registry.npmmirror.com/platform/download/platform-1.3.6.tgz"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"

power-assert-context-formatter@^1.0.7:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-context-formatter/download/power-assert-context-formatter-1.2.0.tgz"
  dependencies:
    core-js "^2.0.0"
    power-assert-context-traversal "^1.2.0"

power-assert-context-reducer-ast@^1.0.7:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-context-reducer-ast/download/power-assert-context-reducer-ast-1.2.0.tgz"
  dependencies:
    acorn "^5.0.0"
    acorn-es7-plugin "^1.0.12"
    core-js "^2.0.0"
    espurify "^1.6.0"
    estraverse "^4.2.0"

power-assert-context-traversal@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-context-traversal/download/power-assert-context-traversal-1.2.0.tgz"
  dependencies:
    core-js "^2.0.0"
    estraverse "^4.1.0"

power-assert-formatter@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/power-assert-formatter/download/power-assert-formatter-1.4.1.tgz"
  dependencies:
    core-js "^2.0.0"
    power-assert-context-formatter "^1.0.7"
    power-assert-context-reducer-ast "^1.0.7"
    power-assert-renderer-assertion "^1.0.7"
    power-assert-renderer-comparison "^1.0.7"
    power-assert-renderer-diagram "^1.0.7"
    power-assert-renderer-file "^1.0.7"

power-assert-renderer-assertion@^1.0.7:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-renderer-assertion/download/power-assert-renderer-assertion-1.2.0.tgz"
  dependencies:
    power-assert-renderer-base "^1.1.1"
    power-assert-util-string-width "^1.2.0"

power-assert-renderer-base@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/power-assert-renderer-base/download/power-assert-renderer-base-1.1.1.tgz"

power-assert-renderer-comparison@^1.0.7:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-renderer-comparison/download/power-assert-renderer-comparison-1.2.0.tgz"
  dependencies:
    core-js "^2.0.0"
    diff-match-patch "^1.0.0"
    power-assert-renderer-base "^1.1.1"
    stringifier "^1.3.0"
    type-name "^2.0.1"

power-assert-renderer-diagram@^1.0.7:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-renderer-diagram/download/power-assert-renderer-diagram-1.2.0.tgz"
  dependencies:
    core-js "^2.0.0"
    power-assert-renderer-base "^1.1.1"
    power-assert-util-string-width "^1.2.0"
    stringifier "^1.3.0"

power-assert-renderer-file@^1.0.7:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-renderer-file/download/power-assert-renderer-file-1.2.0.tgz"
  dependencies:
    power-assert-renderer-base "^1.1.1"

power-assert-util-string-width@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/power-assert-util-string-width/download/power-assert-util-string-width-1.2.0.tgz"
  dependencies:
    eastasianwidth "^0.2.0"

power-assert@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npmmirror.com/power-assert/download/power-assert-1.6.1.tgz"
  dependencies:
    define-properties "^1.1.2"
    empower "^1.3.1"
    power-assert-formatter "^1.4.1"
    universal-deep-strict-equal "^1.2.1"
    xtend "^4.0.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.1.2.tgz"

pretty-format@^29.3.1:
  version "29.3.1"
  resolved "https://registry.npmmirror.com/pretty-format/-/pretty-format-29.3.1.tgz"
  dependencies:
    "@jest/schemas" "^29.0.0"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

printable@^0.0.3:
  version "0.0.3"
  resolved "https://registry.npmmirror.com/printable/download/printable-0.0.3.tgz"

printj@~1.1.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/printj/download/printj-1.1.2.tgz"

private@^0.1.6, private@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmmirror.com/private/download/private-0.1.8.tgz"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npmmirror.com/process/-/process-0.11.10.tgz"

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/progress/download/progress-2.0.3.tgz"

promise.prototype.finally@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/promise.prototype.finally/download/promise.prototype.finally-3.1.2.tgz"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.0"
    function-bind "^1.1.1"

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/prompts/-/prompts-2.4.2.tgz"
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.7.2:
  version "15.7.2"
  resolved "https://registry.npmmirror.com/prop-types/download/prop-types-15.7.2.tgz"
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

proxy-agent@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/proxy-agent/download/proxy-agent-3.1.1.tgz"
  dependencies:
    agent-base "^4.2.0"
    debug "4"
    http-proxy-agent "^2.1.0"
    https-proxy-agent "^3.0.0"
    lru-cache "^5.1.1"
    pac-proxy-agent "^3.0.1"
    proxy-from-env "^1.0.0"
    socks-proxy-agent "^4.0.1"

proxy-from-env@^1.0.0, proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz"

ps-tree@^1.1.0, ps-tree@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/ps-tree/download/ps-tree-1.2.0.tgz"
  dependencies:
    event-stream "=3.3.4"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/pseudomap/download/pseudomap-1.0.2.tgz"

psl@^1.1.28, psl@^1.1.33:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/psl/download/psl-1.8.0.tgz"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/pump/download/pump-2.0.1.tgz"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.5:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/pumpify/download/pumpify-1.5.1.tgz"
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/punycode/-/punycode-1.3.2.tgz"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz"

qs@^6.4.0, qs@^6.5.1, qs@^6.5.2:
  version "6.9.4"
  resolved "https://registry.npmmirror.com/qs/download/qs-6.9.4.tgz"

querystring@0.2.0, querystring@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz"

random-bytes@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/random-bytes/download/random-bytes-1.0.0.tgz"

raw-body@^2.2.0, raw-body@^2.3.3:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.1.tgz"
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.3"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^16.8.1:
  version "16.13.1"
  resolved "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz"

react-is@^18.0.0:
  version "18.2.0"
  resolved "https://registry.npmmirror.com/react-is/-/react-is-18.2.0.tgz"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-2.0.0.tgz"
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg-up@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-4.0.0.tgz"
  dependencies:
    find-up "^3.0.0"
    read-pkg "^3.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/read-pkg/download/read-pkg-1.1.0.tgz"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/read-pkg/download/read-pkg-2.0.0.tgz"
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/read-pkg/download/read-pkg-3.0.0.tgz"
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

readable-stream@1.1.x:
  version "1.1.14"
  resolved "https://registry.npmmirror.com/readable-stream/download/readable-stream-1.1.14.tgz"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@2, readable-stream@2.3.7, readable-stream@^2.0.0, readable-stream@^2.0.2, readable-stream@^2.0.5, readable-stream@^2.3.5, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readable-stream/download/readable-stream-3.6.0.tgz"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-web-to-node-stream@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/readable-web-to-node-stream/-/readable-web-to-node-stream-3.0.2.tgz#5d52bb5df7b54861fd48d015e93a2cb87b3ee0bb"
  dependencies:
    readable-stream "^3.6.0"

readdir-glob@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/readdir-glob/download/readdir-glob-1.1.1.tgz"
  dependencies:
    minimatch "^3.0.4"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  dependencies:
    picomatch "^2.2.1"

ready-callback@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/ready-callback/download/ready-callback-2.1.0.tgz"
  dependencies:
    debug "^2.6.0"
    get-ready "^2.0.0"
    once "^1.4.0"
    uuid "^3.0.1"

redis-commands@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/redis-commands/download/redis-commands-1.6.0.tgz"

redis-commands@1.7.0:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/redis-commands/download/redis-commands-1.7.0.tgz?cache=0&sync_timestamp=1612586456630&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fredis-commands%2Fdownload%2Fredis-commands-1.7.0.tgz"

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/redis-errors/download/redis-errors-1.2.0.tgz"

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/redis-parser/download/redis-parser-3.0.0.tgz"
  dependencies:
    redis-errors "^1.0.0"

regenerate@^1.2.1:
  version "1.4.2"
  resolved "https://registry.npmmirror.com/regenerate/download/regenerate-1.4.2.tgz?cache=0&sync_timestamp=1604218439731&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate%2Fdownload%2Fregenerate-1.4.2.tgz"

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"

regenerator-runtime@^0.13.4:
  version "0.13.7"
  resolved "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz"

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "https://registry.npmmirror.com/regenerator-transform/download/regenerator-transform-0.10.1.tgz"
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz"
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.0.tgz"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/regexpp/download/regexpp-2.0.1.tgz"

regexpp@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/regexpp/download/regexpp-3.1.0.tgz"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/regexpu-core/download/regexpu-core-2.0.0.tgz?cache=0&sync_timestamp=1600413905865&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexpu-core%2Fdownload%2Fregexpu-core-2.0.0.tgz"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/regjsgen/download/regjsgen-0.2.0.tgz"

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/regjsparser/download/regjsparser-0.1.5.tgz"
  dependencies:
    jsesc "~0.5.0"

release-zalgo@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/release-zalgo/download/release-zalgo-1.0.0.tgz"
  dependencies:
    es6-error "^4.0.1"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/repeat-element/download/repeat-element-1.1.3.tgz"

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npmmirror.com/repeat-string/download/repeat-string-1.6.1.tgz"

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/repeating/download/repeating-2.0.1.tgz"
  dependencies:
    is-finite "^1.0.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-1.0.1.tgz"

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/require-main-filename/-/require-main-filename-2.0.0.tgz"

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  dependencies:
    resolve-from "^5.0.0"

resolve-files@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/resolve-files/download/resolve-files-1.0.2.tgz"
  dependencies:
    crequire "^1.8.0"
    debug "^2.6.3"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/download/resolve-from-4.0.0.tgz"

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz"

resolve.exports@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/resolve.exports/-/resolve.exports-1.1.0.tgz"

resolve@^1.10.0, resolve@^1.13.1, resolve@^1.17.0, resolve@^1.18.1, resolve@^1.20.0:
  version "1.22.1"
  resolved "https://registry.npmmirror.com/resolve/-/resolve-1.22.1.tgz"
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npmmirror.com/ret/download/ret-0.1.15.tgz"

retry-as-promised@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/retry-as-promised/-/retry-as-promised-5.0.0.tgz"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/reusify/download/reusify-1.0.4.tgz"

rimraf@2, rimraf@^2.6.1, rimraf@^2.6.2, rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz"
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-2.6.3.tgz"
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz"
  dependencies:
    glob "^7.1.3"

rndm@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/rndm/download/rndm-1.2.0.tgz"

run-async@^2.2.0:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/run-async/download/run-async-2.4.1.tgz"

run-parallel@^1.1.9:
  version "1.1.10"
  resolved "https://registry.npmmirror.com/run-parallel/download/run-parallel-1.1.10.tgz"

runscript@^1.3.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/runscript/download/runscript-1.5.0.tgz"
  dependencies:
    debug "^2.6.8"
    is-type-of "^1.1.0"

rxjs@^6.4.0:
  version "6.6.3"
  resolved "https://registry.npmmirror.com/rxjs/download/rxjs-6.6.3.tgz?cache=0&sync_timestamp=1602770934889&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frxjs%2Fdownload%2Frxjs-6.6.3.tgz"
  dependencies:
    tslib "^1.9.0"

safe-buffer@*, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/safe-regex/download/safe-regex-1.1.0.tgz"
  dependencies:
    ret "~0.1.10"

safe-timers@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/safe-timers/download/safe-timers-1.1.0.tgz"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz"

sax@=0.4.2:
  version "0.4.2"
  resolved "https://registry.npmmirror.com/sax/download/sax-0.4.2.tgz"

sax@>=0.6.0:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/sax/download/sax-1.2.4.tgz"

scmp@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/scmp/download/scmp-2.1.0.tgz"

sdk-base@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/sdk-base/download/sdk-base-2.0.1.tgz"
  dependencies:
    get-ready "~1.0.0"

sdk-base@^3.1.1, sdk-base@^3.5.0, sdk-base@^3.5.1:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/sdk-base/download/sdk-base-3.6.0.tgz"
  dependencies:
    await-event "^2.1.0"
    await-first "^1.0.0"
    co "^4.6.0"
    is-type-of "^1.2.1"

"semver@2 || 3 || 4 || 5", semver@^5.0.1, semver@^5.3.0, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0, semver@^5.7.0:
  version "5.7.1"
  resolved "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz"

semver@^6.0.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz"

semver@^7.3.2:
  version "7.3.2"
  resolved "https://registry.npmmirror.com/semver/download/semver-7.3.2.tgz"

semver@^7.3.5:
  version "7.3.5"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz"
  dependencies:
    lru-cache "^6.0.0"

sendmessage@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/sendmessage/download/sendmessage-1.1.0.tgz"

sentence-case@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/sentence-case/download/sentence-case-2.1.1.tgz"
  dependencies:
    no-case "^2.2.0"
    upper-case-first "^1.1.2"

seq-queue@^0.0.5:
  version "0.0.5"
  resolved "https://registry.npmmirror.com/seq-queue/download/seq-queue-0.0.5.tgz"

sequelize-pool@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npmmirror.com/sequelize-pool/-/sequelize-pool-7.1.0.tgz"

sequelize@^6.0.0:
  version "6.17.0"
  resolved "https://registry.npmmirror.com/sequelize/-/sequelize-6.17.0.tgz"
  dependencies:
    "@types/debug" "^4.1.7"
    "@types/validator" "^13.7.1"
    debug "^4.3.3"
    dottie "^2.0.2"
    inflection "^1.13.2"
    lodash "^4.17.21"
    moment "^2.29.1"
    moment-timezone "^0.5.34"
    pg-connection-string "^2.5.0"
    retry-as-promised "^5.0.0"
    semver "^7.3.5"
    sequelize-pool "^7.1.0"
    toposort-class "^1.0.1"
    uuid "^8.3.2"
    validator "^13.7.0"
    wkx "^0.5.0"

sequin@*:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/sequin/download/sequin-0.1.1.tgz"

serialize-json@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/serialize-json/download/serialize-json-1.0.3.tgz"
  dependencies:
    debug "^3.2.6"
    is-type-of "^1.2.1"
    utility "^1.15.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz"
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@~1.0.4:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/setimmediate/download/setimmediate-1.0.5.tgz"

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.2.0.tgz"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz"
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/shellwords/-/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"

should-send-same-site-none@^2.0.2:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/should-send-same-site-none/download/should-send-same-site-none-2.0.5.tgz"

side-channel@^1.0.2, side-channel@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/side-channel/download/side-channel-1.0.3.tgz"
  dependencies:
    es-abstract "^1.18.0-next.0"
    object-inspect "^1.8.0"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"

similarity@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/similarity/-/similarity-1.2.1.tgz#58eed40bc90742434abcf1b8e0b9af63241fb3dd"
  integrity sha512-lMOpWVaWrTnyL+tkvDs8oGV/KAUBQ3wfbZtdiwDcC+KYGlwO8kgtiyag1B6akAjALDMwn5rN5YHHei1hr4X7nw==
  dependencies:
    levenshtein-edit-distance "^2.0.0"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/sisteransi/-/sisteransi-1.0.5.tgz"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/slash/download/slash-1.0.0.tgz"

slash@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/slash/download/slash-2.0.0.tgz"

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/slice-ansi/download/slice-ansi-2.1.0.tgz"
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

smart-buffer@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/smart-buffer/download/smart-buffer-4.1.0.tgz"

snake-case@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/snake-case/download/snake-case-2.1.0.tgz"
  dependencies:
    no-case "^2.2.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npmmirror.com/snapdragon/download/snapdragon-0.8.2.tgz"
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socks-proxy-agent@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/socks-proxy-agent/download/socks-proxy-agent-4.0.2.tgz"
  dependencies:
    agent-base "~4.2.1"
    socks "~2.3.2"

socks@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npmmirror.com/socks/download/socks-2.3.3.tgz"
  dependencies:
    ip "1.1.5"
    smart-buffer "^4.1.0"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.13.tgz"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@^0.4.0, source-map-support@^0.4.15:
  version "0.4.18"
  resolved "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.4.18.tgz"
  dependencies:
    source-map "^0.5.6"

source-map-support@^0.5.19, source-map-support@^0.5.6, source-map-support@^0.5.9:
  version "0.5.19"
  resolved "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/source-map-url/download/source-map-url-0.4.0.tgz"

source-map@^0.1.34:
  version "0.1.43"
  resolved "https://registry.npmmirror.com/source-map/-/source-map-0.1.43.tgz"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.6, source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz"

spawn-wrap@^1.4.2:
  version "1.4.3"
  resolved "https://registry.npmmirror.com/spawn-wrap/-/spawn-wrap-1.4.3.tgz#81b7670e170cca247d80bf5faf0cfb713bdcf848"
  dependencies:
    foreground-child "^1.5.6"
    mkdirp "^0.5.0"
    os-homedir "^1.0.1"
    rimraf "^2.6.2"
    signal-exit "^3.0.2"
    which "^1.3.0"

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/spdx-correct/download/spdx-correct-3.1.1.tgz"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.6"
  resolved "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.6.tgz?cache=0&sync_timestamp=1600284758648&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.6.tgz"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz"
  dependencies:
    extend-shallow "^3.0.0"

split2@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/split2/download/split2-2.2.0.tgz"
  dependencies:
    through2 "^2.0.2"

split@0.3:
  version "0.3.3"
  resolved "https://registry.npmmirror.com/split/download/split-0.3.3.tgz"
  dependencies:
    through "2"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz"

sqlstring@2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/sqlstring/download/sqlstring-2.3.1.tgz"

sqlstring@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/sqlstring/download/sqlstring-2.3.2.tgz"

stack-trace@^0.0.10:
  version "0.0.10"
  resolved "https://registry.npmmirror.com/stack-trace/download/stack-trace-0.0.10.tgz"

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/stack-utils/-/stack-utils-2.0.6.tgz"
  dependencies:
    escape-string-regexp "^2.0.0"

standard-as-callback@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/standard-as-callback/download/standard-as-callback-2.0.1.tgz"

standard-as-callback@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/standard-as-callback/download/standard-as-callback-2.1.0.tgz"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/static-extend/download/static-extend-0.1.2.tgz"
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.5.0 < 2", statuses@^1.3.1, statuses@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz"

stream-combiner@~0.0.4:
  version "0.0.4"
  resolved "https://registry.npmmirror.com/stream-combiner/download/stream-combiner-0.0.4.tgz"
  dependencies:
    duplexer "~0.1.1"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/stream-shift/download/stream-shift-1.0.1.tgz?cache=0&sync_timestamp=1576147145118&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstream-shift%2Fdownload%2Fstream-shift-1.0.1.tgz"

stream-slice@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/stream-slice/download/stream-slice-0.1.2.tgz"

stream-wormhole@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/stream-wormhole/download/stream-wormhole-1.1.0.tgz"

stream@^0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/stream/download/stream-0.0.2.tgz"
  dependencies:
    emitter-component "^1.1.1"

streamsearch@0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/streamsearch/download/streamsearch-0.1.2.tgz"

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/string-length/-/string-length-4.0.2.tgz"
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-1.0.2.tgz"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/string-width/-/string-width-3.1.0.tgz"
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.matchall@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/string.prototype.matchall/download/string.prototype.matchall-4.0.3.tgz?cache=0&sync_timestamp=1605855732298&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.matchall%2Fdownload%2Fstring.prototype.matchall-4.0.3.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    has-symbols "^1.0.1"
    internal-slot "^1.0.2"
    regexp.prototype.flags "^1.3.0"
    side-channel "^1.0.3"

string.prototype.trimend@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/string.prototype.trimend/download/string.prototype.trimend-1.0.3.tgz?cache=0&sync_timestamp=1606007972119&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimend%2Fdownload%2Fstring.prototype.trimend-1.0.3.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.3.tgz?cache=0&sync_timestamp=1606007972027&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.3.tgz"
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.3.0.tgz"
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.npmmirror.com/string_decoder/download/string_decoder-0.10.31.tgz"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz"
  dependencies:
    safe-buffer "~5.1.0"

stringifier@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/stringifier/download/stringifier-1.4.0.tgz"
  dependencies:
    core-js "^2.0.0"
    traverse "^0.6.6"
    type-name "^2.0.1"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-5.2.0.tgz"
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-bom/download/strip-bom-2.0.0.tgz"
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/strip-bom/download/strip-bom-3.0.0.tgz"

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-bom/-/strip-bom-4.0.0.tgz"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz"

strip-json-comments@2.0.1, strip-json-comments@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz"

strtok3@^6.0.3:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/strtok3/-/strtok3-6.3.0.tgz#358b80ffe6d5d5620e19a073aa78ce947a90f9a0"
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^4.1.0"

superagent@^3.8.3:
  version "3.8.3"
  resolved "https://registry.npmmirror.com/superagent/download/superagent-3.8.3.tgz"
  dependencies:
    component-emitter "^1.2.0"
    cookiejar "^2.1.0"
    debug "^3.1.0"
    extend "^3.0.0"
    form-data "^2.3.1"
    formidable "^1.2.0"
    methods "^1.1.1"
    mime "^1.4.1"
    qs "^6.5.1"
    readable-stream "^2.3.5"

supertest@^3.4.2:
  version "3.4.2"
  resolved "https://registry.npmmirror.com/supertest/-/supertest-3.4.2.tgz"
  dependencies:
    methods "^1.1.2"
    superagent "^3.8.3"

supertest@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/supertest/download/supertest-4.0.2.tgz"
  dependencies:
    methods "^1.1.2"
    superagent "^3.8.3"

supports-color@6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-6.0.0.tgz"
  dependencies:
    has-flag "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-2.0.0.tgz?cache=0&sync_timestamp=1606205080621&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-2.0.0.tgz"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1606205080621&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz"
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz"
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"

svg-captcha@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/svg-captcha/download/svg-captcha-1.4.0.tgz"
  dependencies:
    opentype.js "^0.7.3"

swap-case@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/swap-case/download/swap-case-1.1.2.tgz?cache=0&sync_timestamp=1575601672807&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fswap-case%2Fdownload%2Fswap-case-1.1.2.tgz"
  dependencies:
    lower-case "^1.1.1"
    upper-case "^1.1.1"

table@^5.2.3:
  version "5.4.6"
  resolved "https://registry.npmmirror.com/table/download/table-5.4.6.tgz?cache=0&sync_timestamp=1605825584818&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftable%2Fdownload%2Ftable-5.4.6.tgz"
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tar-stream@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/tar-stream/download/tar-stream-2.2.0.tgz"
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

tcp-base@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/tcp-base/download/tcp-base-3.1.0.tgz"
  dependencies:
    is-type-of "^1.0.0"
    sdk-base "^3.1.1"

tcp-proxy.js@^1.0.5:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/tcp-proxy.js/download/tcp-proxy.js-1.3.0.tgz"
  dependencies:
    debug "^3.0.1"
    through2 "^2.0.3"

test-exclude@^5.1.0:
  version "5.2.3"
  resolved "https://registry.npmmirror.com/test-exclude/download/test-exclude-5.2.3.tgz"
  dependencies:
    glob "^7.1.3"
    minimatch "^3.0.4"
    read-pkg-up "^4.0.0"
    require-main-filename "^2.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/test-exclude/-/test-exclude-6.0.0.tgz"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/text-table/download/text-table-0.2.0.tgz"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/thenify-all/download/thenify-all-1.6.0.tgz"
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4", thenify@^3.2.1:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/thenify/download/thenify-3.3.1.tgz"
  dependencies:
    any-promise "^1.0.0"

throat@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/throat/-/throat-5.0.0.tgz"

through2@^2.0.1, through2@^2.0.2, through2@^2.0.3:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/through2/download/through2-2.0.5.tgz"
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@2, through@^2.3.6, through@~2.3, through@~2.3.1:
  version "2.3.8"
  resolved "https://registry.npmmirror.com/through/download/through-2.3.8.tgz"

thunkify@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/thunkify/download/thunkify-2.1.2.tgz"

tiny-inflate@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/tiny-inflate/download/tiny-inflate-1.0.3.tgz"

title-case@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/title-case/download/title-case-2.1.1.tgz"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.0.3"

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npmmirror.com/tmp/download/tmp-0.0.33.tgz"
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/tmpl/-/tmpl-1.0.5.tgz"

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-1.0.3.tgz"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/to-object-path/download/to-object-path-0.3.0.tgz"
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz"
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz"

token-types@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/token-types/-/token-types-2.1.1.tgz#bd585d64902aaf720b8979d257b4b850b4d45c45"
  dependencies:
    "@tokenizer/token" "^0.1.1"
    ieee754 "^1.2.1"

toposort-class@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/toposort-class/-/toposort-class-1.0.1.tgz"

tough-cookie@*:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-4.0.0.tgz"
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.1.2"

tough-cookie@^2.3.3:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmmirror.com/tr46/-/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

"traverse@>=0.3.0 <0.4":
  version "0.3.9"
  resolved "https://registry.npmmirror.com/traverse/download/traverse-0.3.9.tgz?cache=0&sync_timestamp=1602049904142&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftraverse%2Fdownload%2Ftraverse-0.3.9.tgz"

traverse@^0.6.6:
  version "0.6.6"
  resolved "https://registry.npmmirror.com/traverse/download/traverse-0.6.6.tgz?cache=0&sync_timestamp=1602049904142&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftraverse%2Fdownload%2Ftraverse-0.6.6.tgz"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/trim-right/download/trim-right-1.0.1.tgz"

ts-node@^7, ts-node@^7.0.0:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/ts-node/-/ts-node-7.0.1.tgz"
  dependencies:
    arrify "^1.0.0"
    buffer-from "^1.1.0"
    diff "^3.1.0"
    make-error "^1.1.1"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.5.6"
    yn "^2.0.0"

tsconfig-paths@^3.9.0:
  version "3.9.0"
  resolved "https://registry.npmmirror.com/tsconfig-paths/download/tsconfig-paths-3.9.0.tgz"
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.npmmirror.com/tslib/download/tslib-1.14.1.tgz"

tslib@^2.0.0:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/tslib/-/tslib-2.3.1.tgz"

tslib@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/tslib/download/tslib-2.0.3.tgz"

tsscmp@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/tsscmp/download/tsscmp-1.0.6.tgz"

tsutils@^3.17.1:
  version "3.17.1"
  resolved "https://registry.npmmirror.com/tsutils/download/tsutils-3.17.1.tgz?cache=0&sync_timestamp=1599833144072&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftsutils%2Fdownload%2Ftsutils-3.17.1.tgz"
  dependencies:
    tslib "^1.8.1"

tunnel-agent@*:
  version "0.6.0"
  resolved "https://registry.npmmirror.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  dependencies:
    safe-buffer "^5.0.1"

tunnel@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmmirror.com/tunnel/-/tunnel-0.0.6.tgz"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npmmirror.com/type-check/download/type-check-0.3.2.tgz"
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/type-detect/-/type-detect-4.0.8.tgz"

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz"

type-is@^1.6.15, type-is@^1.6.16:
  version "1.6.18"
  resolved "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type-name@^2.0.0, type-name@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/type-name/download/type-name-2.0.2.tgz"

type@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/type/-/type-1.2.0.tgz"

type@^2.5.0:
  version "2.6.0"
  resolved "https://registry.npmmirror.com/type/-/type-2.6.0.tgz"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npmmirror.com/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
  dependencies:
    is-typedarray "^1.0.0"

typescript@^4.0.0:
  version "4.6.2"
  resolved "https://registry.npmmirror.com/typescript/-/typescript-4.6.2.tgz"

uid-safe@2.1.5:
  version "2.1.5"
  resolved "https://registry.npmmirror.com/uid-safe/download/uid-safe-2.1.5.tgz"
  dependencies:
    random-bytes "~1.0.0"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.npmmirror.com/undici-types/-/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unescape@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/unescape/download/unescape-1.0.1.tgz"
  dependencies:
    extend-shallow "^2.0.1"

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/union-value/download/union-value-1.0.1.tgz"
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universal-deep-strict-equal@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/universal-deep-strict-equal/download/universal-deep-strict-equal-1.2.2.tgz"
  dependencies:
    array-filter "^1.0.0"
    indexof "0.0.1"
    object-keys "^1.0.0"

universalify@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz"

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz"

unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz"

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/unset-value/download/unset-value-1.0.0.tgz"
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

unzipper@^0.10.3:
  version "0.10.11"
  resolved "https://registry.npmmirror.com/unzipper/download/unzipper-0.10.11.tgz"
  dependencies:
    big-integer "^1.6.17"
    binary "~0.3.0"
    bluebird "~3.4.1"
    buffer-indexof-polyfill "~1.0.0"
    duplexer2 "~0.1.4"
    fstream "^1.0.12"
    graceful-fs "^4.2.2"
    listenercount "~1.0.1"
    readable-stream "~2.3.6"
    setimmediate "~1.0.4"

update-browserslist-db@^1.0.9:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz"
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

upper-case-first@^1.1.0, upper-case-first@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/upper-case-first/download/upper-case-first-1.1.2.tgz"
  dependencies:
    upper-case "^1.1.1"

upper-case@^1.0.3, upper-case@^1.1.0, upper-case@^1.1.1, upper-case@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/upper-case/download/upper-case-1.1.3.tgz"

uri-js@^4.2.2:
  version "4.4.0"
  resolved "https://registry.npmmirror.com/uri-js/download/uri-js-4.4.0.tgz"
  dependencies:
    punycode "^2.1.0"

urijs@^1.19.0:
  version "1.19.2"
  resolved "https://registry.npmmirror.com/urijs/download/urijs-1.19.2.tgz"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmmirror.com/url/-/url-0.11.0.tgz"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

urllib@^2.24.0, urllib@^2.25.1, urllib@^2.33.0, urllib@^2.33.3:
  version "2.36.1"
  resolved "https://registry.npmmirror.com/urllib/download/urllib-2.36.1.tgz"
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.9"
    default-user-agent "^1.0.0"
    digest-header "^0.0.1"
    ee-first "~1.1.1"
    formstream "^1.1.0"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    ip "^1.1.5"
    proxy-agent "^3.1.0"
    pump "^3.0.0"
    qs "^6.4.0"
    statuses "^1.3.1"
    utility "^1.16.1"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/use/download/use-3.1.1.tgz"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz"

util.promisify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/util.promisify/download/util.promisify-1.0.1.tgz"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

utility@0.1.11:
  version "0.1.11"
  resolved "https://registry.npmmirror.com/utility/download/utility-0.1.11.tgz"
  dependencies:
    address ">=0.0.1"

utility@^1.12.0, utility@^1.13.1, utility@^1.14.0, utility@^1.15.0, utility@^1.16.1, utility@^1.16.3:
  version "1.16.3"
  resolved "https://registry.npmmirror.com/utility/download/utility-1.16.3.tgz"
  dependencies:
    copy-to "^2.0.1"
    escape-html "^1.0.3"
    mkdirp "^0.5.1"
    mz "^2.7.0"
    unescape "^1.0.1"

uuid@^3.0.1, uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz?cache=0&sync_timestamp=1605964231143&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuuid%2Fdownload%2Fuuid-3.4.0.tgz"

uuid@^8.3.0:
  version "8.3.1"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-8.3.1.tgz?cache=0&sync_timestamp=1605964231143&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuuid%2Fdownload%2Fuuid-8.3.1.tgz"

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz"

v8-to-istanbul@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmmirror.com/v8-to-istanbul/-/v8-to-istanbul-9.0.1.tgz"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validator@^13.7.0:
  version "13.7.0"
  resolved "https://registry.npmmirror.com/validator/-/validator-13.7.0.tgz"

vary@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz"

walker@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmmirror.com/walker/-/walker-1.0.8.tgz"
  dependencies:
    makeerror "1.0.12"

weak-map@~1.0.x:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/weak-map/download/weak-map-1.0.5.tgz"

web-streams-polyfill@4.0.0-beta.3:
  version "4.0.0-beta.3"
  resolved "https://registry.npmmirror.com/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz#2898486b74f5156095e473efe989dcf185047a38"
  integrity sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://registry.npmmirror.com/websocket-driver/download/websocket-driver-0.7.4.tgz"
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"

webstorm-disable-index@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/webstorm-disable-index/download/webstorm-disable-index-1.2.0.tgz"
  dependencies:
    co "^4.6.0"
    lodash "^4.17.2"
    mkdirp "^0.5.1"
    mz "^2.6.0"
    xml-mapping "^1.7.1"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/which-module/download/which-module-1.0.0.tgz"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz"

which@1.3.1, which@^1.2.9, which@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/which/download/which-1.3.1.tgz?cache=0&sync_timestamp=1605134855909&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz"
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  dependencies:
    isexe "^2.0.0"

wide-align@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/wide-align/-/wide-align-1.1.3.tgz"
  dependencies:
    string-width "^1.0.2 || 2"

win-release@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/win-release/download/win-release-1.1.1.tgz"
  dependencies:
    semver "^5.0.1"

wkx@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmmirror.com/wkx/-/wkx-0.5.0.tgz"
  dependencies:
    "@types/node" "*"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-5.1.0.tgz"
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz"

write-file-atomic@^2.4.2:
  version "2.4.3"
  resolved "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.4.3.tgz#1fd2e9ae1df3e75b8d8c367443c692d4ca81f481"
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-file-atomic@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-4.0.2.tgz"
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

write@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/write/download/write-1.0.3.tgz"
  dependencies:
    mkdirp "^0.5.1"

wt@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/wt/download/wt-1.2.0.tgz"
  dependencies:
    debug "^2.2.0"
    ndir "^0.1.5"
    sdk-base "^2.0.1"

xml-mapping@^1.7.1:
  version "1.7.1"
  resolved "https://registry.npmmirror.com/xml-mapping/download/xml-mapping-1.7.1.tgz"
  dependencies:
    sax "=0.4.2"
    xml-writer ">=1.0.4"

xml-writer@>=1.0.4:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/xml-writer/download/xml-writer-1.7.0.tgz"

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.npmmirror.com/xml2js/download/xml2js-0.4.23.tgz"
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz"

xregexp@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/xregexp/download/xregexp-2.0.0.tgz"

xss@^1.0.3:
  version "1.0.8"
  resolved "https://registry.npmmirror.com/xss/download/xss-1.0.8.tgz"
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz"

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/y18n/download/y18n-3.2.1.tgz?cache=0&sync_timestamp=1603637404399&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-3.2.1.tgz"

"y18n@^3.2.1 || ^4.0.0", y18n@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/y18n/download/y18n-4.0.0.tgz?cache=0&sync_timestamp=1603637404399&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-4.0.0.tgz"

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/yallist/download/yallist-2.1.2.tgz"

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/yallist/download/yallist-3.1.1.tgz"

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"

yargs-parser@13.1.2, yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-13.1.2.tgz"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@5.0.0-security.0:
  version "5.0.0-security.0"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-5.0.0-security.0.tgz?cache=0&sync_timestamp=1604886709178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-5.0.0-security.0.tgz"
  dependencies:
    camelcase "^3.0.0"
    object.assign "^4.1.0"

yargs-parser@^11.0.0, yargs-parser@^11.1.1:
  version "11.1.1"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-11.1.1.tgz?cache=0&sync_timestamp=1604886709178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-11.1.1.tgz"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz"

yargs-unparser@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/yargs-unparser/-/yargs-unparser-1.6.0.tgz"
  dependencies:
    flat "^4.1.0"
    lodash "^4.17.15"
    yargs "^13.3.0"

yargs@13.3.2, yargs@^13.3.0:
  version "13.3.2"
  resolved "https://registry.npmmirror.com/yargs/-/yargs-13.3.2.tgz"
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^12.0.2, yargs@^12.0.5:
  version "12.0.5"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-12.0.5.tgz?cache=0&sync_timestamp=1605465967295&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-12.0.5.tgz"
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^1.0.1"
    os-locale "^3.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1 || ^4.0.0"
    yargs-parser "^11.1.1"

yargs@^17.3.1:
  version "17.6.2"
  resolved "https://registry.npmmirror.com/yargs/-/yargs-17.6.2.tgz"
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yargs@^7.0.1:
  version "7.1.1"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-7.1.1.tgz?cache=0&sync_timestamp=1605465967295&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-7.1.1.tgz"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "5.0.0-security.0"

ylru@^1.2.0, ylru@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/ylru/download/ylru-1.2.1.tgz"

yn@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/yn/-/yn-2.0.0.tgz"

yn@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/yn/-/yn-3.1.1.tgz"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz"

ypkgfiles@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/ypkgfiles/download/ypkgfiles-1.6.0.tgz"
  dependencies:
    debug "^2.6.1"
    glob "^7.1.1"
    is-type-of "^1.0.0"
    resolve-files "^1.0.0"
    yargs "^7.0.1"

zip-stream@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/zip-stream/download/zip-stream-4.1.0.tgz"
  dependencies:
    archiver-utils "^2.1.0"
    compress-commons "^4.1.0"
    readable-stream "^3.6.0"

zlib@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/zlib/-/zlib-1.0.5.tgz"

zlogger@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/zlogger/download/zlogger-1.1.0.tgz"
  dependencies:
    pumpify "^1.3.5"
    split2 "^2.1.0"
    through2 "^2.0.1"
