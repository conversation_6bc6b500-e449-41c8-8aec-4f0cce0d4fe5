const fs = require("fs.promised");

const STRING = (length) => {
  if(!length)
  {
    length = '32';
  }

  return `STRING(${length})`;
};

const TEXT = (length) => {
  if(!length)
  {
    length = '32';
  }

  return `TEXT(${length})`;
};

const DECIMAL = (integers, decimals) => {
  if(!integers)
  {
    integers = 10;
  }

  if(!decimals && decimals != 0)
  {
    decimals = 2;
  }

  return `DECIMAL(${integers}, ${decimals})`;
}

const ENUM = (...enums) => {
  return `ENUM(${enums.join(", ")})`;
}

const CHAR = (length) => {
  if(!length)
  {
    length = '32';
  }

  return `CHAR(${length})`;
}

const INTEGER = 'INTEGER';
const DATE = 'DATE';
const DATETIME = 'DATETIME';
const TIMESTAMP = 'TIMESTAMP';
const INT = 'INT';

const tableMap = {};

class Table
{
  constructor(tableName, modelName, fieldMap, options) {
    this.tableName = tableName;
    this.modelName = modelName;
    this.fieldMap = fieldMap;
    this.options = options;
  }

  belongsTo(targetTable, options) {
    const currentTable = this;
    let IDName = options.foreignKey;
    if(!IDName)
    {
      IDName = targetTable.modelName + "ID";
    }

    currentTable.fieldMap[IDName] = {
      comment: `外键，指向 ${targetTable.options.comment}表`,
      type: INTEGER,
    }
  }

  hasOne(targetTable, options) {
    const currentTable = this;
    let IDName = options.foreignKey;
    if(!IDName)
    {
      IDName = currentTable.modelName + "ID";
    }

    targetTable.fieldMap[IDName] = {
      comment: `外键，指向 ${currentTable.options.comment}表`,
      type: INTEGER,
    }
  }

  hasMany(targetTable, options) {
    const currentTable = this;
    let IDName = options.foreignKey;
    if(!IDName)
    {
      IDName = currentTable.modelName + "ID";
    }

    targetTable.fieldMap[IDName] = {
      comment: `外键，指向 ${currentTable.options.comment}表`,
      type: INTEGER,
    }
  }

  belongsToMany(targetTable, options) {
    const currentTable = this;
    let tableName = options.through;
    if(!tableName)
    {
      tableName = `${currentTable.tableName}_${targetTable.tableName}`;
    }

    let fieldMap = {};
    let tableOptions = {};
    let modelName = formatName(tableName);

    if(tableMap[tableName])
    {
      const table = tableMap[tableName];
      fieldMap = table.fieldMap;
      tableOptions = table.options;
      modelName = table.modelName;
    }

    let foreignKey = options.foreignKey;
    if(!foreignKey)
    {
      foreignKey = currentTable.tableName + "ID";
    }

    let otherKey = options.otherKey;
    if(!otherKey)
    {
      otherKey = targetTable.tableName + "ID";
    }

    if(!fieldMap[foreignKey])
    {
      fieldMap[foreignKey] = {
        comment: `外键，指向 ${currentTable.options.comment}表`,
        type: INTEGER,
      }
    }

    if(!fieldMap[otherKey])
    {
      fieldMap[otherKey] = {
        comment: `外键，指向 ${targetTable.options.comment}表`,
        type: INTEGER,
      }
    }

    const table = new Table(tableName, modelName, fieldMap, tableOptions);
    tableMap[tableName] = table;
    app.model[modelName] = table;
  }
}

function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function formatName(tableName)
{
  const parts = tableName.split('_');
  return parts.map(part => capitalizeFirstLetter(part)).join("");
}

let currentFileName = null;

const app = {
  model: {
    define: function(tableName, fieldMap, options)
    {
      if(options && options.classMethods && options.classMethods.freezeTableName)
      {
        tableName = currentFileName.match(/^([^\.]+)/)[1];
      }

      const modelName = formatName(tableName);
      const table = new Table(tableName, modelName, fieldMap, options);
      table.prototype = {};

      tableMap[tableName] = table;
      app.model[modelName] = table;

      return table;
    }
  },

  Sequelize: {
    STRING,
    INTEGER,
    DATETIME,
    DATE,
    TIMESTAMP,
    TEXT, 
    INT,
    DECIMAL,
    ENUM,
    CHAR
  }
}

function parseField(fieldName, field, markdowns)
{
  const more = [];
  if(field.primaryKey)
  {
    more.push("主键");
  }

  if(field.autoIncrement)
  {
    more.push("自动增长");
  }

  if(field.unique)
  {
    more.push("唯一索引");
  }

  if(typeof field.type == 'function')
  {
    field.type = field.type();
  }

  const row = [fieldName, field.comment, field.type, more.join("，")];
  markdowns.push(row.join("|"));
}

async function parseFiles()
{
  // 读入文件
  var currentPath = __dirname + '/app/model/';
  const files = await fs.readdir(currentPath);
  for(const file of files)
  {
    //console.log(file)
    const fileName = currentPath + file;
    try
    {
      currentFileName = file;
      const modelDefine = require(fileName);
      modelDefine(app);
    }
    catch(e)
    {
      console.error("文件" + fileName + "语法有错！");
      console.error(e);
    }

  }

  // 分析依赖树，按照从最小依赖到最大依赖进行输出
  for(const tableName in tableMap)
  {
    const table = tableMap[tableName];

    if(!table.prototype || !table.prototype.associate)
    {
      continue;
    }

    // 执行关系构建函数
    const associate = table.prototype.associate;

    try
    {
      associate();
    }
    catch(e)
    {
      console.error("在执行" + tableName + "关联表时出错！" + e.toString());
      console.error(e);
    }
  }

  // 输出Markdown
  const markdowns = [];
  markdowns.push('# 数据字典');
  for(const tableName in tableMap)
  {
    const table = tableMap[tableName];
    const fieldMap = table.fieldMap;

    const tableNameCN = table.options ? (table.options.comment ? table.options.comment: ''): '';

    markdowns.push('');
    markdowns.push(`## ${tableName} ${tableNameCN}`);
    markdowns.push('字段名 | 含义 | 数据类型 | 备注');
    markdowns.push('---|---|---|---');

    if(!fieldMap.id)
    {
      parseField("id", {
        comment: "主键",
        type: INTEGER,
        primaryKey: true,
        autoIncrement: true
      }, markdowns);
    }

    for(const filedName in fieldMap)
    {
      parseField(filedName, fieldMap[filedName], markdowns);
    }

    if(!fieldMap.created_at)
    {
      parseField("created_at", {
        comment: "创建时间",
        type: DATETIME
      }, markdowns);
    }

    if(!fieldMap.updated_at)
    {
      parseField("updated_at", {
        comment: "最后更新时间",
        type: DATETIME
      }, markdowns);
    }

    if(!fieldMap.deleted_at)
    {
      parseField("deleted_at", {
        comment: "删除时间",
        type: DATETIME
      }, markdowns);
    }
  }

  await fs.writeFile("db.md", markdowns.join("\r\n"));
}

parseFiles(); 