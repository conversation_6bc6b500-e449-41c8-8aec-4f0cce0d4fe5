const fs = require("mz/fs");
const { getQuestionResult, judgeQuestion, getNewFreeModeRecord, getNewTrainModeRecord } = require('../app/utils/judge.js');
const { testData } = require('../app/common/data.js');

describe("multiple choice Tests", () => {
    const questionData = { freeMode: true, questionID: 'test', questionType: '单选题', questionScore: 2, referAnswer:{ answer: 'D' }};
    test("no answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: null });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = null;

        expect(newAnswer).toBe(expected);
    });

    test("no answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer:{ answer: null, score: 0, result: undefined, initResult: true, initAnswer: 'D', initScore: 2 } });

        // assert
        expect(newInitScore).toBe(2);
        expect(newScore).toBe(0);

        const expected = {
            answer: null,
            result: undefined,
            score: 0,
            initAnswer: 'D',
            initResult: true,
            initScore: 2,
        };

        expect(newAnswer).toMatchObject(expected);
    });

    test("right answer first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ freeMode: true, questionID: 'test', questionType: '单选题', questionScore: 2, referAnswer:{ answer: 'D' }, studentAnswer: { answer: 'D' } });
        
        // assert
        expect(newInitScore).toBe(2);
        expect(newScore).toBe(2);

        const expected = {
            answer: 'D',
            result: true,
            score: 2,
            initAnswer: 'D',
            initResult: true,
            initScore: 2,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("wrong answer first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ freeMode: true, questionID: 'test', questionType: '单选题', questionScore: 2, referAnswer:{ answer: 'D' }, studentAnswer: { answer: 'C' } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = {
            answer: 'C',
            result: false,
            score: 0,
            initAnswer: 'C',
            initResult: false,
            initScore: 0,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("right answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ freeMode: true, questionID: 'test', questionType: '单选题', questionScore: 2, referAnswer:{ answer: 'D' }, studentAnswer: { answer: 'D', result: false, score: 0, initAnswer: 'C', initResult: false, initScore: 0 } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(2);

        const expected = {
            answer: 'D',
            result: true,
            score: 2,
            initAnswer: 'C',
            initResult: false,
            initScore: 0,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("wrong answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ freeMode: true, questionID: 'test', questionType: '单选题', questionScore: 2, referAnswer:{ answer: 'D' }, studentAnswer: { answer: 'C', result: true, score: 2, initAnswer: 'D', initResult: true, initScore: 2 } });
        
        // assert
        expect(newInitScore).toBe(2);
        expect(newScore).toBe(0);

        const expected = {
            answer: 'C',
            result: false,
            score: 0,
            initAnswer: 'D',
            initResult: true,
            initScore: 2,
        }

        expect(newAnswer).toMatchObject(expected);
    });
});

describe("multiple many choice Tests", () => {
    const questionData = {
        freeMode: true, questionID: 'test', questionType: '多选题', referAnswer: { answer: ['A', 'B'], score: 3 }
    };

    test("multiple many choice no answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: null });

        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);
        expect(newAnswer).toBe(null);
    });

    test("multiple many choice not submit", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: ['A', 'B'] } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = {
            answer: ['A', 'B'],
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("multiple many choice right answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: ['A', 'B'], result: true, score: 3, initAnswer: ['A'], initResult: false, initScore: 0, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(3);

        const expected = {
            answer: ['A', 'B'],
            result: true,
            score: 3,
            initAnswer: ['A'],
            initResult: false,
            initScore: 0,
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("multiple many choice wrong answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: ['A'], result: false, score: 0, initAnswer: ['A', 'B'], initResult: true, initScore: 3, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(3);
        expect(newScore).toBe(0);

        const expected = {
            answer: ['A'],
            result: false,
            score: 0,
            initAnswer: ['A', 'B'],
            initResult: true,
            initScore: 3,
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });
});

describe("JumbledSentence Tests", () => {
    const questionData = {
        freeMode: true, questionID: 'test', questionType: '选择填空题', referAnswer: { answer: { '__填空1__': [['A'], ['C']] }, score: { '__填空1__': 3 } }
    };

    test("JumbledSentence no answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: null });

        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);
        expect(newAnswer).toBe(null);
    });

    test("JumbledSentence not submit", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: { '__填空1__': 'A' } } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = {
            answer: { '__填空1__': 'A' },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("JumbledSentence right answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: { '__填空1__': 'A' }, result: true, score: 3, initAnswer: { '__填空1__': 'B' }, initResults: { '__填空1__': false }, initResult: false, initScore: 0, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(3);

        const expected = {
            answer: { '__填空1__': 'A' },
            result: true,
            results: { '__填空1__': true },
            score: 3,
            initAnswer: { '__填空1__': 'B' },
            initResult: false,
            initScore: 0,
            initResults: { '__填空1__': false },
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("JumbledSentence wrong answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: { '__填空1__': 'B' }, result: false, score: 0, initAnswer: { '__填空1__': 'A' }, initResult: true, initResults: { '__填空1__': true }, initScore: 3, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(3);
        expect(newScore).toBe(0);

        const expected = {
            answer: { '__填空1__': 'B' },
            result: false,
            results: { '__填空1__': false },
            score: 0,
            initAnswer: { '__填空1__': 'A' },
            initResult: true,
            initScore: 3,
            initResults: { '__填空1__': true },
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });
});

describe("code blank Tests", () => {
    const questionData = {
        freeMode: true, questionID: 'test', questionType: '编程填空题', referAnswer: { answer: { '__填空1__': ['hello', 'Hello'] }, score: { '__填空1__': 3 } }
    };

    test("no answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: null });

        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);
        expect(newAnswer).toBe(null);
    });

    test("not submit", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: { '__填空1__': ' hello ' } } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = {
            answer: { '__填空1__': ' hello ' },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("right answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: { '__填空1__': ' hello ' }, result: true, score: 3, initAnswer: { '__填空1__': ' helloo ' }, initResults: { '__填空1__': false }, initResult: false, initScore: 0, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(3);

        const expected = {
            answer: { '__填空1__': ' hello ' },
            result: true,
            results: { '__填空1__': true },
            score: 3,
            initAnswer: { '__填空1__': ' helloo ' },
            initResult: false,
            initScore: 0,
            initResults: { '__填空1__': false },
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("wrong answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: { '__填空1__': 'www' }, result: false, score: 0, initAnswer: { '__填空1__': 'hello' }, initResult: true, initResults: { '__填空1__': true }, initScore: 3, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(3);
        expect(newScore).toBe(0);

        const expected = {
            answer: { '__填空1__': 'www' },
            result: false,
            results: { '__填空1__': false },
            score: 0,
            initAnswer: { '__填空1__': 'hello' },
            initResult: true,
            initScore: 3,
            initResults: { '__填空1__': true },
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("right answer multiple blanks", () => {
        // arrange and act
        const studentAnswer =  {
            score: 10,
            answer: {
                '__[填空1]__': '2',
                '__[填空2]__': 'pi',
                '__[填空3]__': 'op',
                '__[填空4]__': 'print',
            },
            result: true,
            results: {
                '__[填空1]__': true,
                '__[填空2]__': true,
                '__[填空3]__': true,
                '__[填空4]__': true,
            },
            initScore: 10,
            initAnswer: {
                '__[填空1]__': '2',
                '__[填空2]__': 'pi',
                '__[填空3]__': 'op',
                '__[填空4]__': 'print',
            },
            initResult: true,
            initResults: {
                '__[填空1]__': true,
                '__[填空2]__': true,
                '__[填空3]__': true,
                '__[填空4]__': true,
            },
            submitJudge: true,
        };

        const questionAnswer = {
            "score": {
                "__[填空1]__": 2.5,
                "__[填空2]__": 2.5,
                "__[填空3]__": 2.5,
                "__[填空4]__": 2.5
            },
            "answer": {
                "__[填空1]__": [
                    "2"
                ],
                "__[填空2]__": [
                    "pi"
                ],
                "__[填空3]__": [
                    "op"
                ],
                "__[填空4]__": [
                    "print"
                ]
            }
        };

        const newQuestionData = {
            freeMode: true, questionID: 'test', questionType: '编程填空题', referAnswer: questionAnswer,
        }

        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...newQuestionData, studentAnswer });
        
        // assert
        expect(newInitScore).toBe(10);
        expect(newScore).toBe(10);

        const expected = {
            score: 10,
            answer: {
              '__[填空1]__': '2',
              '__[填空2]__': 'pi',
              '__[填空3]__': 'op',
              '__[填空4]__': 'print',
            },
            result: true,
            results: {
              '__[填空1]__': true,
              '__[填空2]__': true,
              '__[填空3]__': true,
              '__[填空4]__': true,
            },
            initScore: 10,
            initAnswer: {
              '__[填空1]__': '2',
              '__[填空2]__': 'pi',
              '__[填空3]__': 'op',
              '__[填空4]__': 'print',
            },
            initResult: true,
            initResults: {
              '__[填空1]__': true,
              '__[填空2]__': true,
              '__[填空3]__': true,
              '__[填空4]__': true,
            },
            submitJudge: true,
        };

        expect(newAnswer).toMatchObject(expected);
    });
});

describe("access wps Tests", () => {
    const questionData = {
        freeMode: true, questionID: 'test', questionType: 'Access操作题', 
        referAnswer: [
            {
                "guide": '',
                "score": 3
            },
            {
                "guide": '',
                "score": 3
            },
            {
                "guide": '',
                "score": 4
            }
        ],
    };

    test("no answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: null });

        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);
        expect(newAnswer).toBe(null);
    });

    test("not submit", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: [ { status: 'wait' } ] } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = {
            answer: [ { status: 'wait' } ],
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("wrong answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: [ { status: 'finish' } ], result: false, score: 3, initAnswer: [ { status: 'finish' }, { status: 'finish' }, { status: 'finish' } ], initResult: true, initScore: 10, submitJudge: true }});
        
        // assert
        expect(newInitScore).toBe(10);
        expect(newScore).toBe(3);

        const expected = {
            answer: [ { status: 'finish' } ],
            result: false,
            score: 3,
            initAnswer: [ { status: 'finish' }, { status: 'finish' }, { status: 'finish' } ],
            initResult: true,
            initScore: 10,
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("right answer not first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { answer: [ { status: 'finish' }, { status: 'finish' }, { status: 'finish' } ], result: true, score: 10, initAnswer: [ { status: 'wait' } ], initResult: false, initScore: 0, submitJudge: true } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(10);

        const expected = {
            answer: [ { status: 'finish' }, { status: 'finish' }, { status: 'finish' } ],
            result: true,
            score: 10,
            initAnswer: [ { status: 'wait' } ],
            initResult: false,
            initScore: 0,
            submitJudge: true,
        }

        expect(newAnswer).toMatchObject(expected);
    });
});

describe("complex Tests", () => {
    const questionData = {
        freeMode: true, questionID: 'test', questionType: '综合题',
        questionDetail: [
            {
                UUID: 11,
                questionType: '单选题',
            },
            {
                questionType: '文本',
            },
            {
                UUID: 22,
                questionType: '填空题',
            },
            {
                UUID: 33,
                questionType: '选择填空题',
            },
        ],
        referAnswer: {
            11: {
                answer: 'A',
                score: 3,
            },
            22: {
                answer: {
                    '__填空1__': ['1', '2']
                },
                score: {
                    '__填空1__': 3
                }, 
            },
            33: {
                answer: {
                    '__填空1__': [['A'], ['C']]
                },
                score: {
                    '__填空1__': 3
                }, 
            }
        },
    };

    test("no answer", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: null });

        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);
        expect(newAnswer).toMatchObject({});
    });

    test("not submit with multiple choice right answer first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { 11: { answer: 'A' } } });
        
        // assert
        expect(newInitScore).toBe(3);
        expect(newScore).toBe(3);

        const expected = {
            11: {
                answer: 'A',
                result: true,
                score: 3,
                initAnswer: 'A',
                result: true,
                initScore: 3,
            },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("not submit with multiple choice right answer first time", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { 11: { answer: 'B' } } });
        
        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(0);

        const expected = {
            11: {
                answer: 'B',
                result: false,
                score: 0,
                initAnswer: 'B',
                result: false,
                initScore: 0,
            },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("submit with answer judged", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { 
            11: {
                answer: 'B',
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '1'
                },
                initResult: true,
                initScore: 3,
                submitJudge: true,
            },
        } });

        // assert
        expect(newInitScore).toBe(3);
        expect(newScore).toBe(3);

        const expected = {
            11: {
                answer: 'B',
                result: false,
                score: 0,
                initAnswer: 'B',
                initResult: false,
                initScore: 0,
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '1'
                },
                initResult: true,
                initScore: 3,
                submitJudge: true,
            },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("two right answers with initial result", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { 
            11: {
                answer: 'A',
                score: 0,
                result: false,
                initAnswer: 'B',
                initResult: false,
                initScore: 0,
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '1'
                },
                initResult: true,
                initScore: 3,
                submitJudge: true,
            },
        } });

        // assert
        expect(newInitScore).toBe(3);
        expect(newScore).toBe(6);

        const expected = {
            11: {
                answer: 'A',
                result: true,
                score: 3,
                initAnswer: 'B',
                initResult: false,
                initScore: 0,
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '1'
                },
                initResult: true,
                initScore: 3,
                submitJudge: true,
            },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("two right answers with two wrong initial result", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { 
            11: {
                answer: 'A',
                score: 0,
                result: false,
                initAnswer: 'B',
                initResult: false,
                initScore: 0,
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '34344'
                },
                initResult: false,
                initScore: 0,
                submitJudge: true,
            },
        } });

        // assert
        expect(newInitScore).toBe(0);
        expect(newScore).toBe(6);

        const expected = {
            11: {
                answer: 'A',
                result: true,
                score: 3,
                initAnswer: 'B',
                initResult: false,
                initScore: 0,
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '34344'
                },
                initResult: false,
                initScore: 0,
                submitJudge: true,
            },
        }

        expect(newAnswer).toMatchObject(expected);
    });

    test("two right answers", () => {
        // arrange and act
        const { newInitScore, newScore, newAnswer } = getQuestionResult({ ...questionData, studentAnswer: { 
            11: {
                answer: 'A',
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '1'
                },
                initResult: true,
                initScore: 3,
                submitJudge: true,
            },
        } });

        // assert
        expect(newInitScore).toBe(6);
        expect(newScore).toBe(6);

        const expected = {
            11: {
                answer: 'A',
                result: true,
                score: 3,
                initAnswer: 'A',
                initResult: true,
                initScore: 3,
            },
            22: {
                answer: {
                    '__填空1__': '1'
                },
                result: true,
                score: 3,
                initAnswer: {
                    '__填空1__': '1'
                },
                initResult: true,
                initScore: 3,
                submitJudge: true,
            },
        }

        expect(newAnswer).toMatchObject(expected);
    });
});

// 自由模式全部试卷评测
describe("train Tests", () => {
    test("full train test", async() => {
        // arrange and act
        const questionContent = await fs.readFile(`../api/app/common/questions.json`, 'utf-8');
        const questions = JSON.parse(questionContent);

        const answerContent = await fs.readFile(`../api/app/common/answer.json`, 'utf-8');
        const allAnswer = JSON.parse(answerContent);

        const { judgeResult, studentScore, initialScore } = judgeQuestion(questions, allAnswer, testData, true);
        
        // assert
        expect(studentScore).toBe(90);
        expect(initialScore).toBe(73);
        expect(judgeResult).toMatchObject(testData);
    });
});

// 自由模式重做 非订正模式
describe("redo free train Tests", () => {
    test("redo train free mode simple", async() => {
        // arrange and act
        const questionData = {
            questionType: '单选题',
            questionDetail: null
        };

        const studentAnswer = {
            "score": 2,
            "answer": "C",
            "result": true,
            "initScore": 2,
            "initAnswer": "C",
            "initResult": true
        };

        const newRecord = getNewFreeModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                "score": 0,
                "answer": null,
                "result": undefined,
                "initScore": 2,
                "initAnswer": "C",
                "initResult": true
            },
            finalResult: false,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train free mode complex", async() => {
        // arrange and act
        const questionData = {
            questionType: '综合题',
            questionDetail: [{
                UUID: 'sssss',
                questionType: '单选题',
            }, {
                UUID: 'aaaaa',
                questionType: '文本',
            }]
        };

        const studentAnswer = {
            'sssss': {
                "score": 2,
                "answer": "C",
                "result": true,
                "initScore": 2,
                "initAnswer": "C",
                "initResult": true
            }
        };

        const newRecord = getNewFreeModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                'sssss': {
                    "score": 0,
                    "answer": null,
                    "result": undefined,
                    "initScore": 2,
                    "initAnswer": "C",
                    "initResult": true
                }
            },
            finalResult: false,
        };

        expect(newRecord).toMatchObject(testData);
    });
});

// 自由模式重做 订正模式
describe("redo free train correct mode Tests", () => {
    test("redo train free mode correct mode simple", async() => {
        // arrange and act
        const questionData = {
            questionType: '单选题',
            questionDetail: null,
            correctMode: true,
        };

        const studentAnswer = {
            "score": 2,
            "answer": "C",
            "result": true,
            "initScore": 2,
            "initAnswer": "C",
            "initResult": true
        };

        const newRecord = getNewFreeModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                "score": 2,
                "answer": "C",
                "result": true,
                "initScore": 2,
                "initAnswer": "C",
                "initResult": true
            },
            finalResult: true,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train free mode correct mode complex", async() => {
        // arrange and act
        const questionData = {
            questionType: '综合题',
            questionDetail: [{
                UUID: 'sssss',
                questionType: '单选题',
            }, {
                UUID: 'aaaaa',
                questionType: '文本',
            }],
            correctMode: true,
        };

        const studentAnswer = {
            'sssss': {
                "score": 2,
                "answer": "C",
                "result": true,
                "initScore": 2,
                "initAnswer": "C",
                "initResult": true
            }
        };

        const newRecord = getNewFreeModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                'sssss': {
                    "score": 2,
                    "answer": "C",
                    "result": true,
                    "initScore": 2,
                    "initAnswer": "C",
                    "initResult": true
                }
            },
            finalResult: true,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train free mode correct mode complex no answer", async() => {
        // arrange and act
        const questionData = {
            questionType: '综合题',
            questionDetail: [{
                UUID: 'sssss',
                questionType: '单选题',
            }, {
                UUID: 'aaaaa',
                questionType: '文本',
            }],
            correctMode: true,
        };

        const studentAnswer = null;

        const newRecord = getNewFreeModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                'sssss': {
                    "score": 0,
                    "answer": null,
                    "result": undefined,
                    "initScore": 0,
                    "initAnswer": null,
                    "initResult": false
                }
            },
            finalResult: false,
        };

        expect(newRecord).toMatchObject(testData);
    });
});

// 训练模式重做 订正模式
describe("redo train mode Tests", () => {
    test("redo train mode simple", async() => {
        // arrange and act
        const questionData = {
            questionType: '单选题',
            questionDetail: null
        };

        const studentAnswer = {
            "score": 2,
            "answer": "C",
            "result": true,
        };

        const newRecord = getNewTrainModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                "score": 2,
                "answer": "C",
                "result": true,
            },
            finalResult: true,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train mode simple no answer", async() => {
        // arrange and act
        const questionData = {
            questionType: '单选题',
            questionDetail: null
        };

        const studentAnswer = null;

        const newRecord = getNewTrainModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                "score": 0,
                "answer": null,
                "result": undefined,
            },
            finalResult: false,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train mode complex no answer", async() => {
        // arrange and act
        const questionData = {
            questionType: '综合题',
            questionDetail: [{
                UUID: 'sssss',
                questionType: '单选题',
            }, {
                UUID: 'aaaaa',
                questionType: '文本',
            }, {
                UUID: 'bbb',
                questionType: '单选题',
            },]
        };

        const studentAnswer = null;

        const newRecord = getNewTrainModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                'sssss': {
                    "score": 0,
                    "answer": null,
                    "result": undefined,
                }, 'bbb': {
                    "score": 0,
                    "answer": null,
                    "result": undefined,
                }
            },
            finalResult: false,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train mode complex no all correct", async() => {
        // arrange and act
        const questionData = {
            questionType: '综合题',
            questionDetail: [{
                UUID: 'sssss',
                questionType: '单选题',
            }, {
                UUID: 'aaaaa',
                questionType: '文本',
            }, {
                UUID: 'bbb',
                questionType: '单选题',
            },]
        };

        const studentAnswer = {
            'sssss': {
                "score": 2,
                "answer": "C",
                "result": true,
            }
        };;

        const newRecord = getNewTrainModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                'sssss': {
                    "score": 2,
                    "answer": "C",
                    "result": true,
                }, 'bbb': {
                    "score": 0,
                    "answer": null,
                    "result": undefined,
                }
            },
            finalResult: false,
        };

        expect(newRecord).toMatchObject(testData);
    });

    test("redo train mode complex all correct", async() => {
        // arrange and act
        const questionData = {
            questionType: '综合题',
            questionDetail: [{
                UUID: 'sssss',
                questionType: '单选题',
            }, {
                UUID: 'aaaaa',
                questionType: '文本',
            }, {
                UUID: 'bbb',
                questionType: '单选题',
            },]
        };

        const studentAnswer = {
            'sssss': {
                "score": 2,
                "answer": "C",
                "result": true,
            },
            'bbb': {
                "score": 2,
                "answer": "C",
                "result": true,
            }
        };;

        const newRecord = getNewTrainModeRecord({ ...questionData, studentAnswer });
        
        // assert
        const testData = {
            newRecord: {
                'sssss': {
                    "score": 2,
                    "answer": "C",
                    "result": true,
                }, 'bbb': {
                    "score": 2,
                    "answer": "C",
                    "result": true,
                }
            },
            finalResult: true,
        };

        expect(newRecord).toMatchObject(testData);
    });
});