use sea_orm::{entity::prelude::*, From<PERSON>ueryResult};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct CourseInfoItem {
  pub id: i32,
  pub course_slug: String,
  pub container_info: Option<Json>,  
  pub save_code: i32,
  pub save_run_result: i32,
}
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct CourseIndicsInfoItem {
  pub id: i32,
  pub indics: Option<Json>,  
}
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Deserialize, Serialize)]
pub struct CourseItem {
  pub id: i32,
  pub course_name: String,
  pub course_slug: String,
  pub course_description: Option<String>,
  pub publish: i32,
  pub statist: Option<Json>,
  pub indics: Option<Json>,  
  pub container_info: Option<Json>,  
  pub creater: Option<Json>,
  pub course_type: String,
  pub save_code: i32,
  pub save_run_result: i32,
  pub allow_paste: i32,
  pub allow_copy: i32,
  pub question_answer: i32,  
  pub program_language: String,
  pub teams: Option<Json>,
  pub history_teams: Option<Json>,
  pub teachers: Option<Json>,
  pub upload_count: i32,  
  pub download_count: i32,
}
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize)]
#[sea_orm(table_name = "course")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub course_name: String,
    pub course_slug: String,
    pub course_description: Option<String>,
    pub publish: i32,
    pub statist: Option<Json>,
    pub indics: Option<Json>,  
    pub container_info: Option<Json>,  
    pub creater: Option<Json>,
    pub course_type: String,
    pub save_code: i32,
    pub save_run_result: i32,
    pub allow_paste: i32,
    pub allow_copy: i32,
    pub question_answer: i32,  
    pub program_language: String,
    pub teams: Option<Json>,
    pub history_teams: Option<Json>,
    pub teachers: Option<Json>,
    pub upload_count: i32,  
    pub download_count: i32,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct UploadCourse {
  pub id: Option<i32>,
  pub course_name: String,
  pub course_slug: String,
  pub course_description: Option<String>,
  pub publish: i32,
  pub statist: Option<Json>,
  pub indics: Option<Json>,  
  pub container_info: Option<Json>,  
  pub creater: Option<Json>,
  pub course_type: String,
  pub save_code: i32,
  pub save_run_result: i32,
  pub allow_paste: i32,
  pub allow_copy: i32,
  pub question_answer: i32,  
  pub program_language: String,
  pub teams: Option<Json>,
  pub history_teams: Option<Json>,
  pub teachers: Option<Json>,
  pub upload_count: i32,  
  pub download_count: i32,
  pub created_at: DateTime,
  pub updated_at: DateTime,
  pub deleted_at: Option<DateTime>,
}
#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
  #[sea_orm(has_many = "super::section::Entity",)]
  Section,
  
  // #[sea_orm(
  //   belongs_to = "super::user::Entity",
  //   from = "Column::CreaterId",
  //   to = "super::user::Column::Id",
  // )]
  // User,
}
impl Related<super::section::Entity> for Entity {
  fn to() -> RelationDef {
      Relation::Section.def()
  }
}
// impl Related<super::user::Entity> for Entity {
//   fn to() -> RelationDef {
//       Relation::User.def()
//   }
// }
impl ActiveModelBehavior for ActiveModel {}