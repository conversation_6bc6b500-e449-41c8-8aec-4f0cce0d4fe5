//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6
use serde::{Deserialize, Serialize};
use sea_orm::{entity::prelude::*, FromQueryResult};

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct TeamUserItem {
    pub id: i32,
    pub team_id: i32,
    pub user_id: i32,
}
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct TeamToUserItem {
    pub user_id: i32,
}

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Deserialize, Serialize)]
#[sea_orm(table_name = "team_user")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub team_id: i32,
    pub user_id: i32,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, <PERSON><PERSON><PERSON><PERSON>, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::team::Entity",
        from = "Column::TeamId",
        to = "super::team::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Team,
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::UserId",
        to = "super::user::Column::Id",
        on_update = "Cascade",
        on_delete = "Restrict"
    )]
    User,
}

impl Related<super::team::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Team.def()
    }
}

impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
