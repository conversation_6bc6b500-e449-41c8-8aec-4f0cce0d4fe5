//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.6

use sea_orm::{entity::prelude::*, FromQueryResult};
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq)]
pub struct CourseUser {
    pub username: String,
    pub avatar: Option<String>,
}
#[derive(<PERSON><PERSON>, Debug, PartialEq, Eq)]
pub struct UserRecord {
    pub id: i32,
    pub username: String,
    pub password: String,
    pub name: String,
    pub sen: Option<String>,
    pub avatar: Option<String>,
    pub state: Option<String>,
    pub admin_authority: Option<Json>,
    pub school: Option<String>,
    pub last_active_time: Option<DateTime>,
    pub last_active_ip: Option<String>,
}
#[derive(C<PERSON>, Debug, PartialEq, Eq, FromQueryResult)]
pub struct UserShortItem {
    pub id: i32,
    pub username: String,
    pub name: String,
}

#[derive(<PERSON><PERSON>, Debug, <PERSON><PERSON><PERSON>q, DeriveEntityModel, Eq, Deserialize, Serialize)]
#[sea_orm(table_name = "user")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub username: String,
    pub password: String,
    pub name: String,
    pub sen: Option<String>,
    pub avatar: Option<String>,
    pub state: Option<String>,
    pub admin_authority: Option<Json>,
    pub school: Option<String>,
    pub last_active_time: Option<DateTime>,
    pub last_active_ip: Option<String>,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::team_user::Entity")]
    TeamUser,
    #[sea_orm(has_many = "super::terminal::Entity")]
    Terminal,
    // #[sea_orm(has_many = "super::course::Entity")]
    // Course,
}

impl Related<super::team_user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::TeamUser.def()
    }
}
// impl Related<super::course::Entity> for Entity {
//     fn to() -> RelationDef {
//         Relation::Course.def()
//     }
// }


impl ActiveModelBehavior for ActiveModel {}

#[derive(Debug, Clone, PartialEq, Eq, FromQueryResult, Deserialize, Serialize)]
pub struct UserShortRecord {
    pub id: i32,
    pub username: String,
    pub name: String,
    pub sen: Option<String>,
}