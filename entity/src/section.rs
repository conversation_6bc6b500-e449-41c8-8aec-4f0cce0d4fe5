use sea_orm::{entity::prelude::*, From<PERSON>ueryResult};
use serde::{Deserialize, Serialize};


#[derive(Debug, <PERSON>lone, PartialEq, Eq, Deserialize, Serialize, FromQueryResult)]
pub struct SectionItem {
  
  pub id: i32,
  pub course_id: String,
  pub chapter_name: String,
  pub section_name: String,
  pub section_type: String,
  pub ext: String,
  pub created_at: DateTime,
}
#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel, Eq, Serialize)]
#[sea_orm(table_name = "section")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub course_id: String,
    pub chapter_name: String,
    pub section_name: String,
    pub section_type: String,
    pub ext: String,
    pub record: Option<Json>,
    pub history_records: Option<Json>,  
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Enum<PERSON><PERSON>, DeriveRelation)]
pub enum Relation {
  #[sea_orm(
      belongs_to = "super::course::Entity",
      from = "Column::CourseId",
      to = "super::course::Column::Id",
      on_update = "Cascade",
      on_delete = "Cascade"
  )]
  Course,
  #[sea_orm(has_many = "super::section_record::Entity",)]
  SectionRecord,
}

impl Related<super::course::Entity> for Entity {
  fn to() -> RelationDef {
      Relation::Course.def()
  }
}
impl Related<super::section_record::Entity> for Entity {
  fn to() -> RelationDef {
      Relation::SectionRecord.def()
  }
}
impl ActiveModelBehavior for ActiveModel {}