import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '机房教学系统',
  },
  favicons: [
     '/favicon.ico',
  ],
  routes: [
    {
      path: "/",
      redirect: "/login",
      component: "@/pages/login/index.jsx"
    },
    {
      path: "/login",
      component: "@/pages/login/index.jsx",
      hideInMenu: true,
    },
    {
      path: '/update',
      component: "@/pages/update/index.jsx",
      hideInMenu: true,
    },
    {
      path: '/startUpWizard',
      component: "@/pages/startUpWizard/index.jsx",
      hideInMenu: true,
    },
    {
      path: '/offlineSync',
      component: "@/pages/offlineSync/index.jsx",
      hideInMenu: true,
    },
    {
      path: '/offlineSyncGlobal',
      component: "@/pages/offlineSync/index.jsx",
      hideInMenu: true,
      layout: false
    },
    {
      name: '课程',
      path: "/course/courseList",
      component: "@/pages/course/courseList.jsx",
    },
    {
      name: '课程详情',
      path: "/course/courseDetail/:courseSlug",
      component: "@/pages/course/courseDetail.jsx",
      hideInMenu: true,
      showBack: true,
      showNavName: true,
    },
    {
      name: '课程节详情',
      path: "/course/sectionDetail/:courseSlug/:chapterName/:sectionName",
      component: "@/pages/course/sectionDetail.jsx",
      hideInMenu: true,
      showBack: true,
      showNavName: true,
    },
    {
      name: '课程进度',
      path: "/course/courseProgress/:courseSlug",
      component: "@/pages/course/courseProgress.jsx",
      hideInMenu: true,
      showBack: true,
      showNavName: true,
    },
    {
      name: '班级',
      path: "/classManager",
      component: "@/pages/classManager/index.jsx",
      // showBack: true,
    },
    {
      name: '终端',
      path: '/terminal',
      component: "@/pages/terminal/index.jsx",
      // showBack: true,
    },
  ],

  proxy: {
    "/api": {
      // target: "http://cnbo.nfls.com.cn",
      // target: "https://nflsbo.i51cy.com",
      target: "http://127.0.0.1:46048",
      // pathRewrite: { "^/api": "" },
      changeOrigin: true,
      secure: false,
      ws: false,
    },
    '/netWork-api': {
      // target: 'http://cnbo.nfls.com.cn',
      // target: 'http://nflsbo.i51cy.com',
      target: "http://127.0.0.1:46048",
      // pathRewrite: { '^/netWork-api': '/api' },
      changeOrigin: true,
      secure: false,
      ws: false,
    },
    "/faye": {
      // target: "http://cnbo.nfls.com.cn",
      target: "https://nflsbo.i51cy.com",
      // target: "http://127.0.0.1:46048",
      // pathRewrite: { "^/faye": "" },
      changeOrigin: true,
      secure: false,
      ws: true,
    },
    '/file': {
      // target: "http://cnbo.nfls.com.cn",
      target: 'http://127.0.0.1:46048',
      // target: 'https://nflsbo.i51cy.com',
      changeOrigin: true,
      secure: false,
    },
    '/microapps': {
      // target: "http://cnbo.nfls.com.cn",
      target: 'http://127.0.0.1:46048',
      // target: 'https://nflsbo.i51cy.com',
      changeOrigin: true,
      secure: false,
    },
  },

  mfsu: {
    exclude: [
      'faye' // 用在WebWorker中，根据https://umijs.org/docs/guides/mfsu#worker-%E5%85%BC%E5%AE%B9%E9%97%AE%E9%A2%98
    ],
  },
  tailwindcss: {},
  npmClient: 'pnpm',
  esbuildMinifyIIFE: true,
});
