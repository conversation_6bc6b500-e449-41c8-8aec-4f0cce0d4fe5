# 教师机服务器

## 本地开发
1. 安装rust
2. 配置rust代理
3. 安装vscode
4. vscode打开src/main.rs
5. 新建一个调试项目，弹出框里面确认一下
6. x86编译器
rustup install 1.74.1
rustup default 1.74.1
rustup target add i686-pc-windows-msvc
7. cmd汉字问题
Win + R
regedit
HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Command Processor
点击右键 - 新建，选择 “字符串值”
命名为 “autorun”, 点击右击修改，数值数据填写 “chcp 65001”，确定

## 发布
1. 用Windows电脑
2. 在api/Cargo.toml修改版本号
3. build.bat

## 数据库表
1. 先创建migration
2. 建表
3. 从migration自动生成entity

## 调试
拷贝init目录到target/debug目录下
拷贝server.env目录到target/debug目录下
如果需要websocket调试，可以将index.html拷贝到target/debug/static目录下，然后访问http://127.0.0.1:36048/


# 可以用umijs进行端口转发
https://hxr.jsnje.cn/s/csxx
http://127.0.0.1:8003

rustup override set 1.75.0
rustup default 1.75.0