# plt-teacher-app

教师端应用程序

# Tauri Win7支持

### 前三步现在的实现方式是win7在下载器中直接下载解压到安装目录，tauri只在main.rs里面做启动判断，所以前三步可以不做；
### 因为第三步中的"fixedRuntime"会在win10/11上面指定路径，但是win7指定路径无效会报错找不到webview环境（不知道为什么，暂时无法解决，新项目就没有问题），导致win7必须在main.rs里面设置环境变量，但是在main.rs里面设置环境变量会导致win10无法使用系统自带webview

## 第一步：下载支持win7的最后一个WebView2版本
Microsoft.WebView2.FixedVersionRuntime.109.0.1518.78.x64.cab

## 第二步：解压到项目的src-tauri目录中
Expand .\Microsoft.WebView2.FixedVersionRuntime.109.0.1518.78.x64.cab -F:* ./src-tauri

## 第三步：修改打包配置
tauri.conf.json
````
{
  "bundle": {
    "windows": {
      "webviewInstallMode": {
        "type": "fixedRuntime",
        "path": "./Microsoft.WebView2.FixedVersionRuntime.109.0.1518.78.x64/"
      }
    }
  }
}
````

## 第四步：降级rust到1.77.2
修改[rust-toolchain.toml](rust-toolchain.toml)；[Cargo.toml](src-tauri/Cargo.toml)。

## 第五步：尝试npm run tauri build，并降级报错依赖；例如：
```
cargo update cargo_metadata@0.19.2 --precise 0.19.0
```

## 第六步：打包
```
npm run tauri build
```
