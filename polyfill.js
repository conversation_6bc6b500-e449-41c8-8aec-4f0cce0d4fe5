// Polyfill for File object in Node.js 18
// 修复 undici 模块在 Node.js 18 中的兼容性问题

if (typeof global.File === 'undefined') {
  // 简单的 File 类 polyfill
  global.File = class File {
    constructor(bits, name, options = {}) {
      this.bits = bits;
      this.name = name;
      this.type = options.type || '';
      this.lastModified = options.lastModified || Date.now();
    }
    
    get size() {
      return this.bits.reduce((acc, bit) => acc + (bit.length || bit.byteLength || 0), 0);
    }
  };
  
  console.log('[Polyfill] Global File object has been polyfilled for Node.js 18');
}

if (typeof global.FormData === 'undefined') {
  const { FormData } = require('formdata-node');
  global.FormData = FormData;
  console.log('[Polyfill] Global FormData object has been polyfilled');
}



