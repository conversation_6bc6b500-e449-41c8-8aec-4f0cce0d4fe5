{"3": {"answer": "D"}, "72": [{"guide": "\n\n<video controls=\"controls\" src=\"../assets/居民消费商品分类价格指数表1.m4v\" width=\"400\">居民消费商品分类价格指数表1.m4v</video>\n\n", "score": 2.5}, {"guide": "\n\n<video controls=\"controls\" src=\"../assets/居民消费商品分类价格指数表2.m4v\" width=\"400\">居民消费商品分类价格指数表2.m4v</video>\n\n", "score": 2.5}, {"guide": "\n\n<video controls=\"controls\" src=\"../assets/居民消费商品分类价格指数表3.m4v\" width=\"400\">居民消费商品分类价格指数表3.m4v</video>\n\n", "score": 2.5}, {"guide": "<video controls=\"controls\" src=\"../assets/居民消费商品分类价格指数表4.m4v\" width=\"400\">居民消费商品分类价格指数表4.m4v</video>\n\n", "score": 2.5}], "95": {"2375504dfd24257d8f4b56a546cd4d68": {"score": {" __[填空1]__ ": 2.5, " __[填空2]__ ": 2.5, " __[填空3]__ ": 2.5, " __[填空4]__ ": 2.5}, "answer": {" __[填空1]__ ": [["D"]], " __[填空2]__ ": [["B"]], " __[填空3]__ ": [["C"]], " __[填空4]__ ": [["A"]]}}, "54320e4e20b3bc5576b77fac025fc7fd": {"score": 2.5, "answer": "A"}, "d59918d300b99d8afc0710be5731fb57": {"score": {" __[填空1]__ ": 2.5, " __[填空2]__ ": 2.5, " __[填空3]__ ": 2.5}, "answer": {" __[填空1]__ ": [["C"]], " __[填空2]__ ": [["A"]], " __[填空3]__ ": [["B"]]}}}, "236": {"answer": "D"}, "320": {"answer": "D"}, "321": {"answer": "B"}, "322": {"answer": "D"}, "323": {"answer": "B"}, "985": {"note": "信息系统安全风险的来源\n（1） 技术和设计上的不完善，漏洞与缺陷导致；\n（2） 由人为因素引起的风险，人为对信息系统的攻击、人为无意的信息泄露。\n（3）软硬件因素造成的信息安全风险。\n（4）网络因素造成的信息安全风险。\n（5）数据因素造成的信息安全风险。", "answer": "D"}, "987": {"answer": "C"}, "989": {"note": "计算机病毒是计算机程序中插入的破坏计算机功能或者数据的一个程序、一段可执行代码。", "answer": "B"}, "990": {"note": "计算机病毒是在计算机程序中插入的破坏计算机功能或者数据的一个程序、一段可执行代码。", "answer": "C"}, "992": {"note": "病毒的特性:传播性、隐蔽性、传染性、潜伏性、可触发性、表现性和破坏性", "answer": "D"}, "996": {"note": "木马，全称特洛伊木马病毒，是指通过特定的程序（木马程序）来控制计算机。通常有两个可执行程序：一个是控制端，另一个是被控制端。\n“木马”程序和一般病毒的不同\n通过将自身伪装吸引用户下载执行，向施种木马者提供打开被种主机的门户，使施种者可以任意毁坏、窃取被种者的文件，设置远程操控被种主机。", "answer": "C"}, "1000": {"answer": "C"}, "1001": {"answer": "D"}, "1018": {"answer": "D"}, "1023": {"note": "传感器：传感器是一种检测装置，能感受到被测量的信息，并能将感受到的信息按一定规律转换为电信号或其他所需要形式，以满足信息的传输、处理存储、显示、记录和控制等要求。\n按功能分：光敏传感器、声敏传感器、气敏传感器、化学传感器、压敏传感器、热敏传感器。\n常见传感器：位移传感器、形变传感器、重力传感器、加速度传感器、湿度传感器、温度传感器、超声波测距传感器等。", "answer": "C"}, "1027": {"note": "物联网的概念：通过二维码识读设备、射频识别装置、红外线感应器、全球定位系统和激光扫描器等信息传感设备，按约定的协议，将任何物品与互联网相连接，进行信息交换和通信，以实现智能化识别、定位、跟踪、监控和管理的一种网络。", "answer": "D"}, "1043": {"note": "是一种利用可见光波谱(如灯泡发出的光)进行数据传输的全新无线传输技术。与光纤通信拥有同样的优点，高带宽，高速率，不同的是，LiFi是使光传播在我们周围的环境中，自然光能到达的任何地方，就有LiFi的信号。", "answer": "D"}, "1067": {"answer": "D"}, "1068": {"note": "调制解调器，它能把计算机的数字信号翻译成可沿普通电话线传送的模拟信号，而这些模拟信号又可被线路另一端的另一个调制解调器接收，并译成计算机可懂的数字信号。光信号及声音信号等都是模拟信号。如果家里就两台电脑，直接互联也能组成一个网络。", "answer": "D"}, "1072": {"note": "调制解调器，它能把计算机的数字信号翻译成可沿普通电话线传送的模拟信号，而这些模拟信号又可被线路另一端的另一个调制解调器接收，并译成计算机可懂的数字信号。", "answer": "C"}, "1073": {"note": "常见网络设备：路由器、交换机、网卡、集线器、中继器、网桥等。", "answer": "A"}, "1089": {"answer": "A"}, "1158": {"answer": "C"}, "1165": {"answer": "A"}, "1186": {"score": {"__[填空1]__": 2.5, "__[填空2]__": 2.5, "__[填空3]__": 2.5, "__[填空4]__": 2.5}, "answer": {"__[填空1]__": ["int"], "__[填空2]__": ["x%10", "x%100%10"], "__[填空3]__": ["x//100", "x//10//10"], "__[填空4]__": ["y"]}}, "1230": {"score": {"__[填空1]__": 2.5, "__[填空2]__": 2.5, "__[填空3]__": 2.5, "__[填空4]__": 2.5}, "answer": {"__[填空1]__": ["2"], "__[填空2]__": ["pi"], "__[填空3]__": ["op"], "__[填空4]__": ["print"]}}}