
const testData = {
    3: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    72: {
      score: 0,
      answer: null,
      result: false,
      initScore: 0,
      initAnswer: null,
      initResult: false,
      submitJudge: true,
    },
    95: {
      '2375504dfd24257d8f4b56a546cd4d68': {
        score: 10,
        answer: {
          ' __[填空1]__ ': 'D',
          ' __[填空2]__ ': 'B',
          ' __[填空3]__ ': 'C',
          ' __[填空4]__ ': 'A',
        },
        result: true,
        results: {
          ' __[填空1]__ ': true,
          ' __[填空2]__ ': true,
          ' __[填空3]__ ': true,
          ' __[填空4]__ ': true,
        },
        initScore: 10,
        initAnswer: {
          ' __[填空1]__ ': 'D',
          ' __[填空2]__ ': 'B',
          ' __[填空3]__ ': 'C',
          ' __[填空4]__ ': 'A',
        },
        initResult: true,
        initResults: {
          ' __[填空1]__ ': true,
          ' __[填空2]__ ': true,
          ' __[填空3]__ ': true,
          ' __[填空4]__ ': true,
        },
        submitJudge: true,
      },
      '54320e4e20b3bc5576b77fac025fc7fd': {
        score: 2.5,
        answer: 'A',
        result: true,
        initScore: 2.5,
        initAnswer: 'A',
        initResult: true,
      },
      d59918d300b99d8afc0710be5731fb57: {
        score: 7.5,
        answer: {
          ' __[填空1]__ ': 'C',
          ' __[填空2]__ ': 'A',
          ' __[填空3]__ ': 'B',
        },
        result: true,
        results: {
          ' __[填空1]__ ': true,
          ' __[填空2]__ ': true,
          ' __[填空3]__ ': true,
        },
        initScore: 0,
        initAnswer: {
          ' __[填空1]__ ': 'A',
        },
        initResult: false,
        initResults: {
          ' __[填空1]__ ': false,
          ' __[填空2]__ ': false,
          ' __[填空3]__ ': false,
        },
        submitJudge: true,
      },
    },
    236: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    320: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    321: {
      score: 2,
      answer: 'B',
      result: true,
      initScore: 2,
      initAnswer: 'B',
      initResult: true,
    },
    322: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    323: {
      score: 2,
      answer: 'B',
      result: true,
      initScore: 2,
      initAnswer: 'B',
      initResult: true,
    },
    985: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    987: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    989: {
      score: 2,
      answer: 'B',
      result: true,
      initScore: 2,
      initAnswer: 'B',
      initResult: true,
    },
    990: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    992: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    996: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    1000: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    1001: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    1018: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    1023: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    1027: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    1043: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    1067: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 2,
      initAnswer: 'D',
      initResult: true,
    },
    1068: {
      score: 2,
      answer: 'D',
      result: true,
      initScore: 0,
      initAnswer: 'C',
      initResult: false,
    },
    1072: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    1073: {
      score: 2,
      answer: 'A',
      result: true,
      initScore: 2,
      initAnswer: 'A',
      initResult: true,
    },
    1089: {
      score: 2,
      answer: 'A',
      result: true,
      initScore: 2,
      initAnswer: 'A',
      initResult: true,
    },
    1158: {
      score: 2,
      answer: 'C',
      result: true,
      initScore: 2,
      initAnswer: 'C',
      initResult: true,
    },
    1165: {
      score: 2,
      answer: 'A',
      result: true,
      initScore: 2,
      initAnswer: 'A',
      initResult: true,
    },
    1186: {
      score: 10,
      answer: {
        '__[填空1]__': 'int',
        '__[填空2]__': 'x%10',
        '__[填空3]__': 'x//100',
        '__[填空4]__': 'y',
      },
      result: true,
      results: {
        '__[填空1]__': true,
        '__[填空2]__': true,
        '__[填空3]__': true,
        '__[填空4]__': true,
      },
      initScore: 2.5,
      initAnswer: {
        '__[填空1]__': 'int',
      },
      initResult: false,
      initResults: {
        '__[填空1]__': true,
        '__[填空2]__': false,
        '__[填空3]__': false,
        '__[填空4]__': false,
      },
      submitJudge: true,
    },
    1230: {
      score: 10,
      answer: {
        '__[填空1]__': '2',
        '__[填空2]__': 'pi',
        '__[填空3]__': 'op',
        '__[填空4]__': 'print',
      },
      result: true,
      results: {
        '__[填空1]__': true,
        '__[填空2]__': true,
        '__[填空3]__': true,
        '__[填空4]__': true,
      },
      initScore: 10,
      initAnswer: {
        '__[填空1]__': '2',
        '__[填空2]__': 'pi',
        '__[填空3]__': 'op',
        '__[填空4]__': 'print',
      },
      initResult: true,
      initResults: {
        '__[填空1]__': true,
        '__[填空2]__': true,
        '__[填空3]__': true,
        '__[填空4]__': true,
      },
      submitJudge: true,
    },
};

exports.testData = testData;
