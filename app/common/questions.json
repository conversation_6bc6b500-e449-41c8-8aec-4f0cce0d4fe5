[{"name": "单选题", "type": ["单选题"], "count": 25, "score": 2, "questions": [{"id": 1043, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下面关于Li-Fi的说法错误的是（ ）", "options": [{"key": "A", "text": "Li-Fi是可见光无线通信技术。"}, {"key": "B", "text": "数据传输速率比Wi-Fi快很多。"}, {"key": "C", "text": "在空气中直接传输光信号，无需有线信道传输介质。"}, {"key": "D", "text": "能耗高，安全性低。"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 633, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 992, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下列不属于计算机病毒的特性的是（ ）。", "options": [{"key": "A", "text": "传播性"}, {"key": "B", "text": "潜伏性"}, {"key": "C", "text": "表现性"}, {"key": "D", "text": "免疫性"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 588, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 320, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "以下不是根据网络覆盖范围划分的网络类别的是（ ）", "options": [{"key": "A", "text": "局域网"}, {"key": "B", "text": "城域网"}, {"key": "C", "text": "广域网"}, {"key": "D", "text": "物联网"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "卓凡202209题库", "status": "发布", "questionBankID": 1, "originalID": 2266, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1027, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "共享单车（自行车）在地铁、公交、站点、居民区、商业区、公共服务区等处随处可见，完成了交通行业的最后一块“拼图”，给人们的生活带来了极大的便利。下列说法不正确的是（ ）。", "options": [{"key": "A", "text": "共享单车是一个物联网信息系统"}, {"key": "B", "text": "共享单车采用了二维码识别技术"}, {"key": "C", "text": "共享单车的收费依托云平台"}, {"key": "D", "text": "共享单车的云平台不需要获取单车的位置信息"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 619, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 3, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "高考志愿刚刚填报结束第二天，小张同学就收到一条手机短信，称他已经被某高校录取，并要求向指定帐号汇款5000元作为第一学年学费，你认为这条信息属于（ ）。", "options": [{"key": "A", "text": "高可信度信息"}, {"key": "B", "text": "高准确度信息"}, {"key": "C", "text": "有效信息"}, {"key": "D", "text": "低可信信息"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "卓帆", "status": "草稿", "questionBankID": 1, "originalID": 100, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1073, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "以下哪个设备不属于网络设备（ ）。", "options": [{"key": "A", "text": "电源适配器"}, {"key": "B", "text": "无线路由器"}, {"key": "C", "text": "调制解调器"}, {"key": "D", "text": "无线网卡"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 663, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 321, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "平时我们访问百度网站时都会在浏览器地址栏里输入其域名 'www.baidu.com' ，但在互联网中，计算机实际上是通过百度的IP地址对其服务器进行访问的，在这一过程中，承担着将域名翻译成IP地址的工作的是（ ）。", "options": [{"key": "A", "text": "路由器"}, {"key": "B", "text": "DNS服务器"}, {"key": "C", "text": "交换机"}, {"key": "D", "text": "调制解调器"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "卓凡202209题库", "status": "发布", "questionBankID": 1, "originalID": 2267, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1001, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "李岚购买了一台新电脑，用来上网课、网购以及娱乐。为保证计算机信息系统安全，他采取了以下措施，其中不合理的是（ ）。", "options": [{"key": "A", "text": "安装杀毒软件"}, {"key": "B", "text": "定期给计算机系统升级"}, {"key": "C", "text": "给自己的电脑设置登录密码"}, {"key": "D", "text": "经常变换计算机放置位置"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 596, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 236, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "目前多数国产手机使用的是一种开源的操作系统，下面所列系统中属于这类的是（ ）", "options": [{"key": "A", "text": "Windows"}, {"key": "B", "text": "Linux"}, {"key": "C", "text": "iOS"}, {"key": "D", "text": "Android"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "2022年真题", "status": "发布", "questionBankID": 1, "originalID": 2183, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 990, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "预防手机病毒的措施中不包括选项（ ）。", "options": [{"key": "A", "text": "不随意连接公共场合的Wi-Fi"}, {"key": "B", "text": "不浏览危险网站，尤其是弹出式广告特别多的网站"}, {"key": "C", "text": "不保存手机号码到通讯录"}, {"key": "D", "text": "不接收陌生请求，因为手机病毒会自动搜索无线范围内的设备进行病毒传播"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 586, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 989, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "关于防范计算机病毒，下列做法正确的是（ ）。\n①安装杀毒软件并开启实时监控\n②经常进行操作系统和重要软件更新\n③开启浏览器中的加载项\n④使用移动存储设备前，先杀毒\n⑤在完成计算机全盘病毒扫描后随意打开陌生人发来的电子邮件", "options": [{"key": "A", "text": "①②④⑤"}, {"key": "B", "text": "①②④"}, {"key": "C", "text": "①②③④⑤"}, {"key": "D", "text": "①③④"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 585, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 996, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下列有关木马的说法错误的是（ ）。", "options": [{"key": "A", "text": "木马是一种特殊的病毒"}, {"key": "B", "text": "木马一般以窃取、毁坏为目的"}, {"key": "C", "text": "比起一般病毒，木马危害不大，可以不用太关注"}, {"key": "D", "text": "木马通常有两个可执行程序，一个是控制端，另一个是被控制端"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 591, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1089, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "应用软件是指专门为某一应用目的而编制的软件系统，下列选项不是应用软件的是（ ）。", "options": [{"key": "A", "text": "Windows10"}, {"key": "B", "text": "QQ"}, {"key": "C", "text": "Python"}, {"key": "D", "text": "Photoshop", "image": ""}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "2022年真题", "status": "发布", "questionBankID": 1, "originalID": 679, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-22T09:32:14.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1158, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下列不适合作为密码使用的是（ ）。 ", "options": [{"key": "A", "text": "ui7#*1r", "image": ""}, {"key": "B", "text": "@*yh637jk", "image": ""}, {"key": "C", "text": "********", "image": ""}, {"key": "D", "text": "cGyh09%h", "image": ""}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "发布", "questionBankID": 1, "originalID": 746, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1068, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "关于网络设备中的“猫”的说法错误的是（ ）", "options": [{"key": "A", "text": "是modem的英译，全称调制解调器"}, {"key": "B", "text": "可以将模拟数字信号相互转换"}, {"key": "C", "text": "可以将光电信号相互转换"}, {"key": "D", "text": "“猫”是局域网的一个必须设备"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 659, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1023, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "研究小组设计了一款智能花盆，通过APP告知系统盆中正在种植的植物类型，系统就能根据植物生长特点和土壤的当前状态来判断是否给浇水装置发送执行指令，由此可以看出，该系统中最有可能用到了哪种传感器（ ）。", "options": [{"key": "A", "text": "环境温湿度传感器"}, {"key": "B", "text": "光线传感器"}, {"key": "C", "text": "土壤干湿度传感器"}, {"key": "D", "text": "液体浓度传感器"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 615, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 322, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "电子地图导航软件能够为我们提供语音导航、线路规划、公交地铁路线查询等服务，使用起来非常方便。下列选项没有电子地图导航功能的是（ ）", "options": [{"key": "A", "text": "高德地图"}, {"key": "B", "text": "百度地图"}, {"key": "C", "text": "腾讯地图"}, {"key": "D", "text": "北京市旅游地图"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "卓凡202209题库", "status": "发布", "questionBankID": 1, "originalID": 2268, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1067, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下列选项中不是无线网基础架构设备的是（ ）", "options": [{"key": "A", "text": "无线网卡"}, {"key": "B", "text": "AP设备（AccessPoint）"}, {"key": "C", "text": "交换机"}, {"key": "D", "text": "笔记本电脑"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 658, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1000, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下列关于网络安全的描述中，说法错误的是（ ）。", "options": [{"key": "A", "text": "系统漏洞是指在硬件、软件、网络协议的具体实现或者系统安全策略上存在的缺陷"}, {"key": "B", "text": "后门程序是指留在计算机系统中，供极少数特殊使用者绕过安全控制而获取对程序或者系统的访问权的程序"}, {"key": "C", "text": "系统漏洞只在软件应用初期存在，通过不断完善与修补可以得到完全控制"}, {"key": "D", "text": "后门程序与系统漏洞都容易被黑客攻击，从而引发安全风险"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 595, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1165, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "以下选项中不属于信息系统安全管理阶段的是（ ）。", "options": [{"key": "A", "text": "设计规划阶段", "image": ""}, {"key": "B", "text": "事前防御阶段", "image": ""}, {"key": "C", "text": "事后响应阶段", "image": ""}, {"key": "D", "text": "实时监测阶段", "image": ""}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "发布", "questionBankID": 1, "originalID": 752, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1018, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "下列选项<strong>不属于</strong>物联网连接技术的是（ ）。", "options": [{"key": "A", "text": "蓝牙"}, {"key": "B", "text": "Wi-Fi"}, {"key": "C", "text": "Zigbee"}, {"key": "D", "text": "RFID"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "发布", "questionBankID": 1, "originalID": 610, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 987, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "以下做法正确的是（ ）。", "options": [{"key": "A", "text": "在微信群中，点开未经核实的陌生人发来的小程序"}, {"key": "B", "text": "朋友通过微信发来借钱信息，立刻转账"}, {"key": "C", "text": "扫二维码前，认真核实"}, {"key": "D", "text": "购物时后，接到付款出现问题需要提供银行卡密码，立刻提供密码给对方"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 583, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 1072, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "通过电话线把计算机接入互联网必须采用的设备是（ ）。", "options": [{"key": "A", "text": "无线网卡"}, {"key": "B", "text": "交换机"}, {"key": "C", "text": "调制解调器"}, {"key": "D", "text": "路由器"}]}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 662, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 323, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "在网上发布他人的敏感、私密或令人尴尬的信息，包括传播私人信息或图像。这种行为属于（ ）", "options": [{"key": "A", "text": "网络诈骗"}, {"key": "B", "text": "披露隐私"}, {"key": "C", "text": "网络盯梢"}, {"key": "D", "text": "编造谎言"}]}, "difficultyConfig": "较难", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "卓凡202209题库", "status": "发布", "questionBankID": 1, "originalID": 2269, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}, {"id": 985, "title": null, "createUserID": 1, "questionType": "单选题", "questionDetail": {"content": "影响网络信息系统安全的常见因素不包括（ ）。", "options": [{"key": "A", "text": "网络因素"}, {"key": "B", "text": "人为因素"}, {"key": "C", "text": "技术因素"}, {"key": "D", "text": "噪声因素"}]}, "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "草稿", "questionBankID": 1, "originalID": 581, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 0}]}, {"name": "操作题", "type": ["编程填空题", "WPS表格操作题", "Access操作题"], "count": 3, "limit": [2, 1, 1], "score": 10, "questions": [{"id": 1186, "title": "反转三位数", "createUserID": 1, "questionType": "编程填空题", "questionDetail": {"code": "x=__[填空1]__(input(\"请输入一个任意三位整数：\"))\nge=__[填空2]__\nshi=x//10%10\nbai=__[填空3]__\n__[填空4]__=ge*100+shi*10+bai\nprint(y)", "content": "下列程序实现输入一个任意三位正整数，将它反转输出，例如369反转成963。请完善该程序编写。", "answerKeys": ["__[填空1]__", "__[填空2]__", "__[填空3]__", "__[填空4]__"], "originCode": "x=__[填空1]__(input(\"请输入一个任意三位整数：\"))\nge=__[填空2]__\nshi=x//10%10\nbai=__[填空3]__\n__[填空4]__=ge*100+shi*10+bai\nprint(y)", "extraCodePath": "反转三位数.py"}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "2022年真题", "status": "发布", "questionBankID": 1, "originalID": 771, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 10}, {"id": 1230, "title": "尼拉坎特哈级数求兀近似值", "createUserID": 1, "questionType": "编程填空题", "questionDetail": {"code": "op=1  #op保存分数项前的符号，取值为1或-1\npi=3  \nfor i in range(2, 200, __[填空1]__):\n    pi = pi + __[填空2]__  # 循环迭代求pi的近似值\n    op = - __[填空3]__  # 分数项前的符号系数变成原来的相反数\nprint(__[填空4]__)\n", "content": "进行以下操作并保存结果。\n（1）计算π的方法有很多种，印度数学家尼拉坎特哈发现了一个可用于计算元的无穷级数，其展开公式如下∶\n![image.png](../assets/f2ffdf8e1c5586abc3340c9ce5bf54cf.png)\n\n（2）请观察各分数项符号和分母的规律，编写代码实现求项数为100时的兀近似值。\n（3）编写完成后原名保存并关闭应用软件。\n", "answerKeys": ["__[填空1]__", "__[填空2]__", "__[填空3]__", "__[填空4]__"], "originCode": "op=1  #op保存分数项前的符号，取值为1或-1\npi=3  \nfor i in range(2, 200, __[填空1]__):\n    pi = pi + __[填空2]__  # 循环迭代求pi的近似值\n    op = - __[填空3]__  # 分数项前的符号系数变成原来的相反数\nprint(__[填空4]__)\n", "extraContent": "进行以下操作并保存结果。\n（1）计算π的方法有很多种，印度数学家尼拉坎特哈发现了一个可用于计算元的无穷级数，其展开公式如下∶\n![image.png](../assets/f2ffdf8e1c5586abc3340c9ce5bf54cf.png)\n\n（2）请观察各分数项符号和分母的规律，编写代码实现求项数为100时的兀近似值。\n（3）编写完成后原名保存并关闭应用软件。\n", "extraCodePath": "尼拉坎特哈级数求兀近似值.py", "extraContentImage": "811.jpg"}, "difficultyConfig": "较难", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "发布", "questionBankID": 1, "originalID": 811, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 10}, {"id": 72, "title": "居民消费商品分类价格指数表", "createUserID": 1, "questionType": "WPS表格操作题", "questionDetail": {"content": "下载<a href=\"../assets/居民消费商品分类价格指数表.xlsx\">居民消费商品分类价格指数表.xlsx</a>，进行以下操作后保存并提交。\n", "extraContent": "进行以下操作后保存并提交。\n", "instructions": [{"description": "添加标题为“居民消费商品分类价格指数表”，将A1:E1合并居中，并将标题设置为微软雅黑、加粗、14、蓝色；", "parseDiffScript": "const sheet1 = inputData[0];\r\nconst merge = sheet1['!merges'][0];\r\nconst isMerge = merge.s.c === 0 && merge.s.r === 0 && merge.e.c === 4 && merge.e.r === 0;\r\nif (!merge || !isMerge) {\r\n    return false;\r\n}\r\n\r\nconst cell = sheet1['A1'];\r\n\r\nconst isAlign = cell.s.alignment.horizontal === 'center' && cell.s.alignment.vertical === 'center';\r\nconst isFont = cell.s.font.bold === 1 && cell.s.font.color.rgb === '0070C0' && cell.s.font.name === '微软雅黑' && cell.s.font.sz === 14;\r\n\r\nif (!isAlign || !isFont) {\r\n    return false;\r\n}\r\n\r\nreturn true;"}, {"description": "将表格数据的列设为最适合的列宽；", "parseDiffScript": "const sheet1 = inputData[0];\r\n\r\n// 检测列宽\r\nconst cols = sheet1['!cols'];\r\nfor (let i = 0; i < 5; i ++) {\r\n    if(!cols[i].width) {\r\n        return false;\r\n    }\r\n\r\n    let standard = null;\r\n    switch(i) {\r\n        case 0:\r\n            standard = 11.1\r\n            break;\r\n        case 1:\r\n            standard = 15.6\r\n            break;\r\n        default:\r\n            standard = 6.89\r\n            break;\r\n    }\r\n\r\n    if(Math.abs(cols[i].width - standard) > 2) {\r\n        return false;\r\n    }\r\n}\r\n\r\nreturn true; "}, {"description": "在工作表Sheet1中按类别进行分类汇总，汇总方式为“求和”，汇总项为“全国、城市、农村”；", "parseDiffScript": "const sheet1 = inputData[0];\r\n\r\nfunction testRows (col, value) {\r\n    if (sheet1[col].w !== value) {\r\n        console.log(sheet1[col])\r\n        return false;\r\n    }\r\n    return true;\r\n}\r\n\r\nif (!testRows('A13', '食品 汇总')) {\r\n    return false;\r\n}\r\n\r\nif (!testRows('A19', '衣着 汇总')) {\r\n    return false;\r\n}\r\n\r\nif (!testRows('A26', '生活用品 汇总')) {\r\n    return false;\r\n}\r\n\r\nif (!testRows('A32', '交通和通信 汇总')) {\r\n    return false;\r\n}\r\n\r\nif (!testRows('A37', '教育文化 汇总')) {\r\n    return false;\r\n}\r\n\r\nif (!testRows('A40', '医疗保健 汇总')) {\r\n    return false;\r\n}\r\n\r\nif (!testRows('A41', '总计')) {\r\n    return false;\r\n}\r\n\r\nreturn true;"}, {"description": "按照“居民消费样张.jpg”样张制作簇状柱形图,插入到当前工作表中；\r\n\n<img alt=\"居民消费样张.jpg\" src=\"../assets/居民消费样张.jpg\" />\n\n", "parseDiffScript": "const sheet1 = inputData[0];\r\nconst { findPathByStr } = helper;\r\nconst refCellPaths = [{\"cell\":\"居民消费价格指数\",\"path\":\"c:title/0/c:tx/0/c:rich/0/a:p/0/a:r/0/a:t/0\"},{\"cell\":\"类别\",\"path\":null},{\"cell\":\"名称\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:tx/0/c:strRef/0/c:strCache/0/c:pt/0/c:v/0\"},{\"cell\":\"全国\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:tx/0/c:strRef/0/c:strCache/0/c:pt/0/c:v/0\"},{\"cell\":\"城市\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:tx/0/c:strRef/0/c:strCache/0/c:pt/0/c:v/0\"},{\"cell\":\"农村\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/3/c:tx/0/c:strRef/0/c:strCache/0/c:pt/0/c:v/0\"},{\"cell\":\"食品 汇总\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:cat/0/c:strRef/0/c:strCache/0/c:pt/0/c:v/0\"},{\"cell\":\"1064.2\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/0/c:v/0\"},{\"cell\":\"1062.2\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:val/0/c:numRef/0/c:numCache/0/c:pt/0/c:v/0\"},{\"cell\":\"1070.0\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/3/c:val/0/c:numRef/0/c:numCache/0/c:pt/0/c:v/0\"},{\"cell\":\"衣着 汇总\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:cat/0/c:strRef/0/c:strCache/0/c:pt/1/c:v/0\"},{\"cell\":\"505.7\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/1/c:v/0\"},{\"cell\":\"509.0\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:val/0/c:numRef/0/c:numCache/0/c:pt/1/c:v/0\"},{\"cell\":\"508.0\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/3/c:val/0/c:numRef/0/c:numCache/0/c:pt/1/c:v/0\"},{\"cell\":\"生活用品 汇总\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:cat/0/c:strRef/0/c:strCache/0/c:pt/2/c:v/0\"},{\"cell\":\"607.6\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/2/c:v/0\"},{\"cell\":\"607.4\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:val/0/c:numRef/0/c:numCache/0/c:pt/2/c:v/0\"},{\"cell\":\"607.0\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/2/c:v/0\"},{\"cell\":\"交通和通信 汇总\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:cat/0/c:strRef/0/c:strCache/0/c:pt/3/c:v/0\"},{\"cell\":\"494.0\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/3/c:v/0\"},{\"cell\":\"493.7\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:val/0/c:numRef/0/c:numCache/0/c:pt/3/c:v/0\"},{\"cell\":\"494.5\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/3/c:val/0/c:numRef/0/c:numCache/0/c:pt/3/c:v/0\"},{\"cell\":\"教育文化 汇总\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:cat/0/c:strRef/0/c:strCache/0/c:pt/4/c:v/0\"},{\"cell\":\"408.9\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/4/c:v/0\"},{\"cell\":\"409.6\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:val/0/c:numRef/0/c:numCache/0/c:pt/4/c:v/0\"},{\"cell\":\"407.3\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/3/c:val/0/c:numRef/0/c:numCache/0/c:pt/4/c:v/0\"},{\"cell\":\"医疗保健 汇总\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/0/c:cat/0/c:strRef/0/c:strCache/0/c:pt/5/c:v/0\"},{\"cell\":\"205.2\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/1/c:val/0/c:numRef/0/c:numCache/0/c:pt/5/c:v/0\"},{\"cell\":\"205.3\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/2/c:val/0/c:numRef/0/c:numCache/0/c:pt/5/c:v/0\"},{\"cell\":\"205.1\",\"path\":\"c:plotArea/0/c:barChart/0/c:ser/3/c:val/0/c:numRef/0/c:numCache/0/c:pt/5/c:v/0\"}];\r\n\r\n// 通过对比内容确定表格结果\r\nfunction ensureChart(drawel) {\r\n    try {\r\n        const chart = drawel['chart'][\"c:chartSpace\"][\"c:chart\"][0];\r\n        for(const refCellPath of refCellPaths) {\r\n            const { cell, path: refPath } = refCellPath;\r\n            const path = findPathByStr(chart, cell);\r\n            if(path !== refPath) {\r\n                console.error(cell, '路径差异', path, '路径 / 参考 ', refPath);\r\n                return false;\r\n            }\r\n        }\r\n    }\r\n    catch(e) {\r\n        console.error(e);\r\n    }\r\n\r\n    return true;\r\n}\r\n\r\nconst drawels = sheet1[\"!drawel\"];\r\nif(!drawels) {\r\n  return false;\r\n}\r\n\r\nfor(const drawel of drawels) {\r\n    if(!ensureChart(drawel)) {\r\n        return false;\r\n    }\r\n}\r\n\r\nreturn drawels.length > 0;"}], "extraTargetFile": "居民消费商品分类价格指数表.xlsx", "extraContentWithInstructions": "1094.jpg"}, "difficultyConfig": "适中", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": "卓帆", "status": "发布", "questionBankID": 1, "originalID": 1094, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-17T03:42:29.000Z", "deleted_at": null, "totalScore": 10}]}, {"name": "综合题", "type": ["综合题"], "count": 1, "score": 20, "questions": [{"id": 95, "title": "物联网农场", "createUserID": 1, "questionType": "综合题", "questionDetail": [{"UUID": "29e2458b8f9c718744f7102f95871242", "questionType": "文本", "questionDetail": {"content": "2015年3月5日上午十二届全国人大三次会议上，李克强总理在政府工作报告中首次提出“互联网+”行动计划，制定“互联网+”行动计划。“物联网农场”依托于部署在农场的各种传感节点（环境温湿度、土壤水分、光照情况、二氧化碳等）和无线通信网络实现农业生产环境的只能感知、智能预警、智能决策、智能分析、专家在线指导，为农业生产提供精准化种植、可视化管理、智能化决策。"}}, {"UUID": "2375504dfd24257d8f4b56a546cd4d68", "questionType": "选择填空题", "questionDetail": {"content": "根据物联网农场的功能需求，我们需要哪些传感器感知这些数据?（从下列选项中选择合适的选项)\n\n![image.png](../assets/6dd40384324030b69b6270ab4a27229b.png)\n\n __[填空1]__  __[填空2]__  __[填空3]__  __[填空4]__ ", "options": [{"key": "A", "text": "二氧化碳传感器", "image": ""}, {"key": "B", "text": "湿度传感器", "image": ""}, {"key": "C", "text": "光照传感器", "image": ""}, {"key": "D", "text": "温度传感器", "image": ""}], "answerKeys": [" __[填空1]__ ", " __[填空2]__ ", " __[填空3]__ ", " __[填空4]__ "], "extraContentImage": "2375504dfd24257d8f4b56a546cd4d68.jpg"}}, {"UUID": "54320e4e20b3bc5576b77fac025fc7fd", "questionType": "单选题", "questionDetail": {"content": "该系统基于（）系统结构，无论在何处，只要能上网即可无需安装任何程序直接访问系统。", "options": [{"key": "A", "text": "B/S", "image": ""}, {"key": "B", "text": "C/S", "image": ""}]}}, {"UUID": "d59918d300b99d8afc0710be5731fb57", "questionType": "选择填空题", "questionDetail": {"content": "温度预警：农场工作人员通过监控管理系统设定温度的预警值，当大棚内某一温度传感器周围的温度超过预警值时，农场工作人员的手机就会收到预警短信，提示该位置需要降温。下图是温度预警流程，请完善流程图，在① __[填空1]__ ② __[填空2]__ ③ __[填空3]__ 处选择相应的流程。\n\n![image.png](../assets/7cab093adeea6e4da2f38678336a8e13.png)", "options": [{"key": "A", "text": "读取温度传感器", "image": ""}, {"key": "B", "text": "判断温度是否超标", "image": ""}, {"key": "C", "text": "设定新的报警温度", "image": ""}], "answerKeys": [" __[填空1]__ ", " __[填空2]__ ", " __[填空3]__ "], "extraContentImage": "d59918d300b99d8afc0710be5731fb57.jpg"}}], "difficultyConfig": "简单", "difficulty": null, "trainsUsed": null, "answerCount": null, "passCount": null, "source": null, "status": "发布", "questionBankID": 1, "originalID": 1114, "created_at": "2022-11-28T04:53:03.000Z", "updated_at": "2022-11-25T02:09:49.000Z", "deleted_at": null, "totalScore": {"2375504dfd24257d8f4b56a546cd4d68": 10, "54320e4e20b3bc5576b77fac025fc7fd": 2.5, "d59918d300b99d8afc0710be5731fb57": 7.5}}]}]