'use strict';

const { setupModel } = require('../utils/mysql');

// 不适用多数据库接口白名单
const noModelPortMap = {
  '/file/getFileUploadeds': true, // 仅测试
  '/file/uploadHashFile': true, // 仅测试
};

module.exports = (options, app) => {
  /**
   * load database to app[config.delegate]
   * @param {Object} config config for load
   *   - delegate: load model to app[delegate]
   *   - baseDir: where model located
   *   - other sequelize configures(database username, password, etc...)
   * @return {Object} sequelize instance
   */

  return async function multipModels(ctx, next) {
    // 取request
    const { path, schoolSlug, service } = ctx; 

    if(noModelPortMap[path]) {
      await next();
      return;
    }

    let model = null;

    try {
      model = await setupModel(app, schoolSlug, service, ctx);
    }
    catch(e) {
      ctx.body = { code: 400, message: e.message};
      return;
    }

    // console.log('schoolModel', ctx.requestUUID, schoolSlug, '开始分配Model', model.schoolSlug);

    await next();

    // console.log('schoolModel', ctx.requestUUID, schoolSlug, '结束分配Model', model.schoolSlug);
  };
};