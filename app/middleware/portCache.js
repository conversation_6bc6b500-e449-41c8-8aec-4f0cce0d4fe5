// 接口路径，缓存时间
const cachedPortMap = {  
    '/web/home': 1,
    '/web/course/courseList': 1,
};

module.exports = (options, app) => {
    const redis = app.redis.get('cache');

    return async function portCache(ctx, next) {
      // 取request
      const { request, schoolSlug } = ctx; 
      const { path, url, method } = request;
      console.log(method, url);

      // 判断是否走缓存
      const cachedTimeoutMins = cachedPortMap[path];
      if(!cachedTimeoutMins) {
        await next();
        return;
      }

      const key = `${schoolSlug}_${url}`;

      // 验证是否存在缓存
      let existCachedResponse = await redis.get(key);
      if(existCachedResponse) {
        ctx.body = existCachedResponse;
        return;
      }

      // 执行接口
      await next();

      // 建立缓存
      existCachedResponse = JSON.stringify(ctx.body);
      await redis.set(key, existCachedResponse, 'EX', cachedTimeoutMins * 60);
    }
}