'use strict';
const { parse } = require('qs');
const md5 = require('md5');
// const URLMatcher = /^\/([^\/]+)(.*)$/;

const getSign = (object) => {
  const keys = Object.keys(object);
  // let keys = [];
  // for (const key of orkeys) {
  //   keys.push(key);
  // }

  keys.sort((a, b) => {
      return a < b ? -1 : 1;
  })

  let resultArr = []

  for (const key of keys) {
    if (!object[key] || object[key] === "") {
      continue;
    }
    resultArr.push(`${key}=${object[key]}`);
  }

  return resultArr.join('&')
}

// 以下清单中各接口免予检查
const whiteLists = [
  '/back/memory/dump',
  '/back/test/school',
  '/back/train/system/init',
  '/back/train/system/clear',
  '/web/user/verify',
  '/admin/user/verify',
  // '/web/notice/getNoticeMessage',
  '/back/asyncTask',
  '/back/database/init',
  '/back/badacaixi/xianzhuozi',
  '/back/lock/release',
  '/web/user/login',
  '/web/user/finalLogin',
  '/computerRoomTrain/teacherLogin',
  '/pclabTrain/teacherLogin',
  '/pclabTrain/teacherLoginForPLT',
  '/web/user/logoff',
  '/web/home',
  '/web/user/session',
  '/web/user/checkPassword',
  '/admin/user/adminLogin',
  '/admin/user/adminLogoff',
  '/admin/user/adminSession',
  '/admin/user/extendSession',
  '/admin/train/upload/question/oj/xml',
  '/web/icourse/container/delete',
  '/back/badacaixi/xianzhuozi',
  '/web/course/queryCourseList',
  '/web/user/checkPassword',
  // '/web/icourse/container/delete',
  // '/web/course/queryCourseList',
  '/faye/online', // 由faye发起上线记录
  '/faye/report', // 由faye发起离线记录
  // '/admin/course' // !!! 仅限测试场景
  '/admin/user/sso/checkSSOExist',
  '/admin/user/sso/getSSOUrl',
  '/admin/user/sso/bindHxrAccount',
  '/admin/user/sso/checkUserExist',
  '/admin/user/sso/bindHxrAccountAuto',
  '/web/test/stressTestLogin', // 压力测试登录
  '/thirdPart/license',
  '/test/getTrainResultWithTest',
  '/computerRoomTrain/license/registy',
  '/admin/sso/njyz/getTeacherInfo',
  '/admin/sso/njyz/getStudentInfo',
  '/pclabTrain/loginByCode',
  '/admin/wechat/getWeChatLoginCode',
  '/admin/wechat/adminLoginByState',
  '/admin/user/loginDemo',
  '/oauth2/authorize',
  '/oauth2/token',
  '/oauth2/user',
  '/admin/llm/inference'
];

// 权限
const jurisdiction = { 
  'get': {
    '/admin/systemConfig': ['homePage'],
    '/admin/user/userList': ['user'],
    '/admin/class/getClassListAll': ['user', 'course', 'team'],
    '/admin/class/getClassList': ['team', 'course'],
    '/admin/class/filterUser': ['team'],
    '/admin/course/queryCourseList': ['course'],
    '/admin/user/getAllAdmins': ['course'],
    '/admin/course/getCourseBycourseSlug': ['course'],
    '/admin/course/getCourseDirectory/:courseSlug': ['course'],
    '/admin/course/getMediaFesources/:courseSlug': ['course'],
    '/admin/course/:courseSlug/downMediaFesources/:fileName': ['course'],
    '/admin/course/getInputData/:courseSlug': ['course'],
    '/admin/course/:courseSlug/downInputData/:fileName': ['course'],
    '/admin/course/getStudentData/:courseSlug': ['course'],
    '/adimin/user/getAllStudentsByteam/:teamID': ['course', 'team'],
    '/admin/user/get/:id': ['team', 'user'],
    '/admin/user/changeUserState/:userID/:state': ['team', 'user'],
    '/admin/user/toAdmin/:id': ['team', 'user'],
    '/admin/course/querySectionContentAndType': ['course'],
    '/admin/course/:courseSlug/getStudentData/:studentID': ['course'],
    '/admin/tag': ['course'],
    '/admin/tag/findTag': ['course'],
    '/admin/course/getCodeRecord': ['course'],
  },
  'post': {
    '/admin/course': ['course'],
    '/admin/ifCourseSlug': ['course'],
    '/admin/notice': ['homePage'],
    '/admin/user/userList': ['user'],
    '/admin/user/userList/createUserListUser': ['user'],
    '/admin/class/userClassContact': ['user'],
    '/admin/class/checkClassName': ['team'],
    '/admin/class/createClass': ['team'],
    '/admin/user/newuserList': ['team'],
    '/admin/course/:courseSlug/downStudentData/:studentID': ['course'],
    '/admin/listXmlQuestionData': ['course'],
    '/admin/tag': ['course'],
    // '/admin/section/saveHistoryRecords': ['section']
  },
  'delete':{
    '/admin/notice/:id': ['homePage'],
    '/admin/user/deleteAlluser': ['user'],
    '/admin/class/deleteClass/:id': ['team'],
    '/admin/class/userClassContact': ['team'],
    '/admin/course/deleteCourse/:courseSlug': ['course'],
  },
  'put': {
    '/admin/notice/:id': ['homePage'],
    '/admin/user/changeUserNickName/:userID/:nickName': ['user'],
    '/admin/class/putClassName/:id': ['team'],
    '/admin/putCourse': ['course'],
    '/admin/user/changePassword/:id': ['team', 'user'],
    '/admin/systemConfig/CarouselFigure': ['homePage'],
    '/admin/systemConfig/NoticeOpen': ['homePage'],
    '/admin/course/saveCourseSort': ['course'],
    '/admin/course/xmlFile': ['course'],
    '/admin/section/projectBook': ['course'],
    '/admin/section/linkProject': ['course'],
    '/admin/course/renameChapter': ['course'],
  } 
 }

const whiteListStarts = [
  '/web/notice/',
  '/web/user/changePassword/',
  '/admin/user/changePassword/',
  '/web/user/changeAvatar/',
  '/admin/user/changeAvatar/',
  '/admin/course/previewStudentData/',
];

// 请求唯一序号
let requestID = 1;

module.exports = (options, app) => {

  return async function interfaceMap(ctx, next) {
    try{

      // 允许跨域，正常不开放，调试用
      // ctx.set('Access-Control-Allow-Origin', '*');
      // 取request
      const { request, session } = ctx; 
      const { method, url, path, ip, host, header } = request;

      // console.log('host:',host)
      // 生成 唯一序号
      const requestUUID = `${process.pid}_${requestID++}`; 
      ctx.requestUUID = requestUUID;
      let schoolSlug = null;

      // 生产环境，使用HTTP HEADER传递学校
      if(header['x-school-slug']) {
        schoolSlug = header['x-school-slug'];

        // 确保小写
        schoolSlug = schoolSlug.toLowerCase();
      }
      else {
        // 测试环境，使用Host获取学校名称
        const hostParts = host.match(/(.*).hxr/);
        
        if(hostParts){
          schoolSlug = hostParts[1];
          
          // 确保小写
          schoolSlug = schoolSlug.toLowerCase();

          // 生产环境网关侧k6专用
          if(schoolSlug === 'api') {
            schoolSlug = 'csxx';
          }
        }
        else {
          schoolSlug = 'csxx';
        }
      }

      // !!! 调试专用 !!!
      //schoolSlug = 'czbj';
      process.env.schoolSlug = schoolSlug;

      // 方便其他模块使用学校名称
      ctx.schoolSlug = schoolSlug;
      
      // const useragent = request.header['user-agent'];
      // const urlParts = path.match(URLMatcher);
      // let platform = 'web';

      // if(urlParts) {
      //   switch(urlParts[1]) {
      //     case 'back':
      //       platform = 'admin';
      //       break;
      //     case 'admin':
      //       platform = 'admin';
      //     break;
      //     default:
      //       break;
      //   }
      // }
      // if(header.host.indexOf('127.0.0.1:') !== -1) {
      //  // ctx.session.user = { id: 1 };
      //   await next();
      //   return;
      // }

      // let userID = -1;
      // let userType = 'guest';

      // 如果会话不在同一个学校，则替换掉
      let { user = null } = session;
      if(user && user.schoolSlug !== schoolSlug) {
        user = null;
      }
      
      let signInfo = null;

      session.ip = ip;

      const urlArr = url.split('?');

      if (!urlArr || !urlArr[1]) {
        signInfo = request.body;
      } else {
        signInfo = parse(urlArr[1])
      }

      // 所有第三方接口需经过单点登录检测
      if(path.indexOf('thirdPart') !== -1 && signInfo && signInfo.sign && signInfo.app_id) {
        // if (!signInfo) {
        //   ctx.body = {code: 400, message: '未收到签名信息'};
        //   return;
        // }

        const {
          timestamp, sign, app_id, ...other
        } = signInfo;

        if (!sign || !app_id) {
          ctx.body = {code: 400, message: '未收到签名信息'};
          return;
        }

        if (other.secret) {
          ctx.status = 400;
          ctx.body = {code: 400, message: '请勿发送密钥'};
          return;
        }

        // console.log('parse(urlArr[1]):',parse(urlArr[1]))

        const now = Date.now();
        if (now - timestamp > 1000 * 60 * 2) {
          ctx.status = 400;
          ctx.body = {code: 400, message: '请求超时，已失效'};
          return;
        }
        // 验证sign
        if (!app.config.thridParty || !app.config.thridParty[app_id]) {
          ctx.status = 400;
          ctx.body = {code: 400, message: '未找到该app_id'};
          return;
        }

        const ownSign = md5(getSign({
          ...other,
          app_id: app_id,
          secret: app.config.thridParty[app_id],
          // username: username,
          // password: password,
          // condition,
          timestamp: timestamp,
        }))
        // const ownSign = md5(`app_id=${app_id}`+'&' + `secret=${app.config.thridParty[app_id]}`+'&' + `timestamp=${timestamp}`)
        // console.log('sign:',sign)
        // console.log('ownSign:',ownSign)
        if (sign !== ownSign) {
          ctx.status = 400;

          // 调试代码！！！！！！
          // ctx.body = {code: 400, message: JSON.stringify({
          //   app_id: app_id,
          //   secret: app.config.thridParty[app_id],
          //   // username: username,
          //   // password: password,
          //   // condition,
          //   sign,
          //   timestamp: timestamp,
          //   ownSign
          // })};

          ctx.body = {code: 400, message: '签名未通过'};
          return;
        }

        await next();
        return;
      }

      // 如果老师密码是弱密码，前台的除了登录，修改密码和退出登录以外，其他操作都是要限制的
      if (user && user.isAdmin && user.needModifyPassword) {
        const permissionPath = [ '/web/user/login', '/web/user/logoff', '/web/user/session', '/web/user/changePassword'];
        if (path.startsWith('/web') && !permissionPath.find(permission => path.indexOf(permission) !== -1)) {
          ctx.status = 400;
          ctx.body = {code: 400, message: '管理员账号密码过于简单，存在安全隐患，请及时修改密码，其他功能暂时禁止'};
          return;
        }
      }
      
      // 接口是否在白名单中，如果在直接放行
      if(whiteLists.indexOf(path) !== -1) {
        // 记录用户操作
        console.log(method, path, schoolSlug, ip, requestUUID);
        await next();
        console.log('interfaceMap', requestUUID, '处理完毕');
        return;
      }

      for(const whiteListStart of whiteListStarts) {
        if(path.startsWith(whiteListStart)) {
          console.log(method, path, schoolSlug, ip, requestUUID);
          await next();
          console.log('interfaceMap', requestUUID, '处理完毕');
          return;
        }
      }

      // 验证接口学校是否一致，如果不一致进行过滤

      // 判断url是否为管理员权限接口
      if (path.startsWith('/admin')) {
        if (!(user && user.adminAuthority)) {
          ctx.status = 400;
          ctx.body = {code: 400, message: '您访问的功能需要教师权限！您的访问IP已被记录！'};
          console.log('非法使用管理员接口' + url + '，请求IP' + ip);
          return;
        }

        // 判断管理员权限
        // 取到接口对应的权限数组
        // 获取到path路径和method接口类型（接口类型转小写）
        let newMethod = method.toLowerCase();
        // 获取到的对象
        let type = jurisdiction[newMethod];
        // 获取到的数组
        let typeArray = type[path];
        if (typeArray && typeArray.length){
          // 取到session里面用户的权限
          const authority = typeof user.adminAuthority === 'string'? JSON.parse(user.adminAuthority): user.adminAuthority;
          let authorized = false;
          for(let row of typeArray){
            if(authority[row] === true) {
              authorized = true;
            }
          }
          if (!authorized) {
            ctx.status = 400;
            ctx.body = {code: 400, message: '您无权访问该接口！'};
            console.log('非法使用管理员接口' + url + '，请求IP' + ip);
            return;
          }
        }
 
        // userID = session.user.id;
        // userType = session.user.isAdmin === 1 ? '管理员' : '普通用户';
      } else {
        if (!user) {
          ctx.status = 400;
          ctx.body = {code: 400, message: '您访问的功能需要登录后使用！'};
          console.log('非法使用前台接口' + url + '，请求IP' + ip);
          return;
        }

        // userID = session.user.id;
        // userType = session.user.isAdmin === 1 ? '管理员' : '普通用户';
      }

      // 记录用户操作
      const userId = user && user.id ? user.id : null;
      console.log(method, path, schoolSlug, ip, userId, requestUUID);

      // 实际执行接口
      await next();

      console.log('interfaceMap', requestUUID, '处理完毕');

      // 用户操作仅记录写操作, 仅在允许记录的平台记录
      // if(method.toLowerCase() !== 'get' && platform === 'admin') {
      //     const userOperation = {
      //         measurement: 'user_operation',
      //         tags: {
      //             ip,
      //             useragent,
      //             userID,
      //             userType,
      //             platform,
      //             path,
      //             method,
      //             schoolSlug
      //         },
      //         fields: {
      //             request: request.body,
      //             response: ctx.body
      //         },
      //     };

      //     userOperation.time = moment().format('YYYY-MM-DD hh:mm:sss');

      //     // 直接输出于命令行
      //     // console.log('userOperation', JSON.stringify(userOperation));
      // }
    }catch(e){
      console.log(e.message, e.stack, '底层异常未捕获，请开发人员注意检查！');
      ctx.code = 500;
      ctx.body = { stack: e.stack, message: e.message };
      return;
    }
  };
};