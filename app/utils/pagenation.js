
// 分页  
function pagenation(list, total, pageSize, pageNum){

  let pages = null;
  let size = null;
  let firstPage = null;
  let prePage = null;
  let hasPreviousPage = null;
  let hasNextPage = null;

  if(!list || !list.length)
  {
    return {
      pages, list:[], firstPage, prePage, hasPreviousPage, hasNextPage, size, total 
    }
  }

  pages = Math.ceil(total / pageSize);

  if(pageNum > pages){  
    pageNum = pages;  
  }

  // 如果是pageNum =1且pages>pageNum
  if(pages == 1){
    firstPage = 1;
    prePage = 1;
    hasPreviousPage = false;
    hasNextPage = false;
  }
  else
  {
    if(pageNum == 1)
    {   
      firstPage = 1;
      prePage = 0;
      hasPreviousPage = false;
      hasNextPage = true;
    }
    else
    {
      if(pageNum != pages){
        prePage = 0;
        hasNextPage = true;
      }
      else{
        prePage = 1;
        hasNextPage = false;
      }
    
      firstPage = 0;
      hasPreviousPage = true;
    }
  }

  size = list.length;
  return {pages, list, firstPage, prePage, hasPreviousPage, hasNextPage, size, total };
}

// 导出分页函数
module.exports = {
  pagenation
};