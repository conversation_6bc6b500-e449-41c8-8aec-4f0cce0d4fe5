const crypto = require('crypto');
const fs = require("mz/fs");
const exec = require('child_process').exec;

function executeQ(command, runDurationLimitMS = null) {
  console.log(command);
  return new Promise((resolve, reject) => {
    let killerTimerID = null;
    const subProcess = exec(command, function(status, stdout, stderr){
      if(killerTimerID) {
        clearTimeout(killerTimerID);
      }

      if(status || stderr)
      {
        reject({status, stderr});
        return;
      }

      resolve(stdout);
    });

    if(runDurationLimitMS) {
      killerTimerID = setTimeout(() => {
        subProcess.kill();
        reject(`您的代码执行时间超过了题目限制的${runDurationLimitMS}ms，已被强制终止`);
      }, runDurationLimitMS);
    }
  });
}

const readFileMd5 = (path) =>{
    return new Promise((reslove) => {
        const md5sum = crypto.createHash('md5');
        const stream = fs.createReadStream(path);
  
        stream.on('data', function(chunk) {
            md5sum.update(chunk);
        });

        stream.on('end', function() {
            const fileMd5 = md5sum.digest('hex');
            reslove(fileMd5);
        })
    })
}

const delDir = function (path) {
    if(path === '/' || path.indexOf('..') !== -1) {
        throw new Error(`请注意检查需要删除的路径${path}，此路径有风险，删除已停止工作!`);
    }
    
    return executeQ(`rm -rf ${path}`);
}

exports.readFileMd5 = readFileMd5;
exports.delDir = delDir;