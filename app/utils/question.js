
function getComplexScore(answer, questionDetail = []) {
    let configTotalScore = 0;

    if (!questionDetail || !Array.isArray(questionDetail)) {
        return configTotalScore;
    }

    for (const key in answer) {
        const item = answer[key];
        const currentQuestion = questionDetail.find((i) => i.UUID === key);
        if (currentQuestion && (currentQuestion.questionType === '填空题' || currentQuestion.questionType === '选择填空题')) {
            configTotalScore += item.score && Object.values(item.score) && Object.values(item.score).length ? Object.values(item.score).reduce((partialSum, a) => partialSum + a, 0) : 0;
            continue;
        }
        configTotalScore += item.score || 0;
    }

    return configTotalScore;
}

function getComplexSeperateScore(answer, questionDetail = []) {
    let configTotalScore = {};

    if (!questionDetail || !Array.isArray(questionDetail)) {
        return configTotalScore;
    }

    for (const key in answer) {
        const item = answer[key];
        const currentQuestion = questionDetail.find((i) => i.UUID === key);
        if (currentQuestion && (currentQuestion.questionType === '填空题' || currentQuestion.questionType === '选择填空题')) {
            configTotalScore[key] = item.score && Object.values(item.score) && Object.values(item.score).length ? Object.values(item.score).reduce((partialSum, a) => partialSum + a, 0) : 0;
            continue;
        }

        configTotalScore[key] = item.score || 0;
    }

    return configTotalScore;
}

function getTotalScore({ questionType, answer, questionDetail, mode }) {
    let totalScore = 0;

    if (!questionType) {
        throw new Error('请提供题目类型');
    }

    switch (questionType) {
        case '单选题':
        case '文本':
            break;
        case '多选题':
            totalScore = answer.score;
            break;
        case '综合题':
            if (mode === 'score') {
                // 只需要总分
                totalScore = getComplexScore(answer, questionDetail);
            } else {
                // 需要小题分数
                totalScore = getComplexSeperateScore(answer, questionDetail);
            }
            break;
        case '填空题':
        case '选择填空题':
        case '编程填空题':
            const { score: questionScore } = answer || {};
            totalScore += questionScore && Object.values(questionScore) && Object.values(questionScore).length ? Object.values(questionScore).reduce((partialSum, a) => partialSum + a, 0) : 0;
            break;
        case 'Access操作题':
        case 'WPS表格操作题':
        case '在线编程评测题':
            totalScore = answer && answer.length ? answer.map((i) => (i && i.score ? i.score : 0)).reduce((prev, cur) => prev + cur, 0) : 0;
            break;
        default:
            throw new Error(`计算总分，不支持的题型${questionType}`);
    }

    return totalScore;
}

const modifyQuestionScoreByRatio = (ratio, { questionType, answer }) => {
    switch (questionType) {
        case '文本':
            break;
        case '单选题':
        case '多选题':
            answer.score *= ratio;
            break;
        case '填空题':
        case '选择填空题':
        case '编程填空题':
            if (!answer.score) {
                throw new Error('选择填空题分数未设置');
            }
            for (const key in answer.score) {
                answer.score[key] *= ratio;
            }
            break;
        case 'Access操作题':
        case 'WPS表格操作题':
        case '在线编程评测题':
            for (const item of answer) {
                item.score *= ratio;
            }
            break;
        default:
            throw new Error(`按比例修改分数，不支持的题型${questionType}`);
    }
};

// 非单选题，得分等比缩放
const adjustQuestionScore = (value, questions) => {
    for (const question of questions) {
        const { questionType, answer, questionDetail } = question;

        let totalScore = getTotalScore({ questionType, answer, questionDetail, mode: 'score' });
        let ratio = value / totalScore;

        switch (questionType) {
            case '单选题':
            case '多选题':
                break;
            case '综合题':
                if (!answer || !Object.values(answer) || !Object.values(answer).length) {
                    break;
                }

                for (const uuid in answer) {
                    const subQuestion = questionDetail.find((i) => i.UUID === uuid);
                    if (!subQuestion) {
                        throw new Error(`综合题 ${uuid} 不存在`);
                    }

                    const { questionType: subType } = subQuestion;
                    modifyQuestionScoreByRatio(ratio, { questionType: subType, answer: answer[uuid] });
                }
                break;
            case '填空题':
            case '编程填空题':
            case 'Access操作题':
            case 'WPS表格操作题':
            case '在线编程评测题':
                modifyQuestionScoreByRatio(ratio, { questionType, answer });
                break;
            default:
                throw new Error(`得分比例缩放，不支持的题型${questionType}`);
        }
    }
};

function getDiscriminative(trainRecords, trainScoreMap, trainPlan) {
    const trainMap = {};
    
    for (const train in trainRecords) {
        const scoreList = trainRecords[train];

        const allNumber = scoreList.length;
        if (allNumber < 10) {
          console.error(`训练${trainPlan} ${train} 人数 ${allNumber} 不足十人，不予区分度统计`);
          continue;
        }

        // 计算区分度 https://www.wsbookshow.com/uploads/bookfile/201202/9787508493381_1.pdf
        // 根据分数从高到低排序
        const studentScores = scoreList.sort((a, b) => b - a);

        // 高分组为前27%，低分组为后27%
        let base = 0.27;
        const highIndex = Math.ceil(allNumber * base);
        const lowIndex = Math.floor(allNumber * (1 - base)) - 1;
        const highGroup = studentScores.slice(0, highIndex);
        const lowGroup = studentScores.slice(lowIndex, allNumber - 1);

        // （27﹪高分组的平均分－27﹪低分组的平均分）÷满分值 https://www.zxjsq.net/a/zixun/2013/0829/62.html
        if (highGroup.length && lowGroup.length) {
          const AHigh = (highGroup.reduce((prev, cur) => parseFloat(prev) + parseFloat(cur), 0) / highGroup.length); 
          const ALow = (lowGroup.reduce((prev, cur) => parseFloat(prev) + parseFloat(cur), 0) / lowGroup.length);

          const totalScore = trainScoreMap[train];
          if (!totalScore) {
            console.error(`训练${trainPlan} ${train} 总分 ${totalScore} 无效，无法统计`);
            continue;
          }

          // 区分度
          trainMap[train] = (AHigh - ALow) / totalScore;
        }
    }

    return trainMap;
}

exports.getTotalScore = getTotalScore;
exports.adjustQuestionScore = adjustQuestionScore;
exports.getDiscriminative = getDiscriminative;