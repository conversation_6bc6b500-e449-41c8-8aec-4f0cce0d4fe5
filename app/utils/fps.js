const xml2js = require('xml2js');
const fs = require('mz/fs');
const cheerio = require('cheerio');
const md5 = require('md5');
const moment = require('moment');

// XML实体引用替换
function escapeXml(unsafe) {
  return unsafe.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/"/g, '&quot;')
               .replace(/'/g, '&apos;');
}

// 时间单位映射
const timeUnitMap = {
  's': 1,
  'm': 60,
  'h': 60 * 60,
};

// 内存单位映射
const memoryUnitMap = {
  'mb': 1,
  'gb': 1024,
}

// 可用编程语言
const availableLanguages = ['C', 'C++', 'Python', 'Pascal', 'Java', 'C#', 'Obj-C', 'Clang', 'Clang++', 'JavaScript', 'Go'];

async function getInputLength(xmlPath) {
  // 入参检查
  const exist = await fs.exists(xmlPath);
  if (!exist) {
    throw new Error(`${xmlPath} 文件不存在，请检查！`);
  }

  // 加载文件
  const xmlString = await fs.readFile(xmlPath, {
    encoding: 'utf-8'
  });

  const parser = new xml2js.Parser();
  const xmlContent = await parser.parseStringPromise(xmlString);

  const { fps } = xmlContent;
  if (!fps) {
    throw new Error(`${xmlPath} 的文件格式不是FPS格式，请检查！`);
  }

  // 加载题目
  if (fps.item.length !== 1) {
    throw new Error(`仅支持FPS格式包含1道题，目前 ${xmlPath} 的题目数为 ${fps.item.length}`);
  }

  // 解析参数
  const question = fps.item[0];

  return question && question.test_input && question.test_input.length;

}

// 获取FPS格式XML文件用于判决的题目配置
async function getFPSJudgeQuestion(xmlPath, targetLanguage) {
  // 入参检查
  const exist = await fs.exists(xmlPath);
  if (!exist) {
    throw new Error(`${xmlPath} 文件不存在，请检查！`);
  }

  if (availableLanguages.indexOf(targetLanguage) === -1) {
    throw new Error(`${targetLanguage} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
  }

  // 加载文件
  const xmlString = await fs.readFile(xmlPath, {
    encoding: 'utf-8'
  });

  const parser = new xml2js.Parser();
  const xmlContent = await parser.parseStringPromise(xmlString);

  const { fps } = xmlContent;
  if (!fps) {
    throw new Error(`${xmlPath} 的文件格式不是FPS格式，请检查！`);
  }

  // 加载题目
  if (!fps.item || !fps.item.length || fps.item.length !== 1) {
    throw new Error(`仅支持FPS格式包含1道题，目前 ${xmlPath} 的题目数为 ${fps.item && fps.item.length ? fps.item : 0}`);
  }

  // 解析参数
  const question = fps.item[0];
  const { time_limit: time_limits = null, memory_limit: memory_limits = null, spj_file: spjs = null, test_input: test_inputs = [], test_output: test_outputs = [], source_file: sourceFiles = null } = question;

  if (!time_limits || 0 === time_limits.length) {
    throw new Error(`${xmlPath} 中必要参数time_limit未设定!`);
  }

  if (!memory_limits || 0 === memory_limits.length) {
    throw new Error(`${xmlPath} 中必要参数memory_limit未设定!`);
  }

  if (test_inputs.length !== test_outputs.length) {
    throw new Error(`${xmlPath} 中必要参数test_inputs 数量与 test_outputs不一致!`);
  }

  // 从多个配置中找到最匹配的CPU时间限制
  let cpuSecondsLimit = null;
  for (const { _, $ } of time_limits) {
    const { unit = 's', language } = $;

    // 如果有指定语言
    if (language) {
      // 检查可用性
      if (availableLanguages.indexOf(language) === -1) {
        throw new Error(`${language} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
      }

      // 如非指定，跳过
      if (targetLanguage !== language) {
        continue;
      }
    }

    // 换算时间单位
    const unitToSecond = timeUnitMap[unit];
    if (!unitToSecond) {
      throw new Error(`${xmlPath} 无法识别的时间单位 ${unit}!`)
    }

    // 计算CPU秒数限制
    cpuSecondsLimit = parseInt(_) * unitToSecond;
  }

  // !!! 增加了教学版OJ题运行时间限制，避免判决队列卡死
  if (cpuSecondsLimit > 5.0) {
    throw new Error(`${xmlPath} 为了学生可以尽快的获得判决结果，请教师将本题时间限制调降至5秒以下，并合理的调整判决数据！`);
  }

  // 从多个配置中找到最匹配的内存限制
  let memoryMegaBytesLimit = null;
  for (const { _, $ } of memory_limits) {
    const { unit = 'm', language } = $;

    // 如果有指定语言
    if (language) {
      // 检查可用性
      if (availableLanguages.indexOf(language) === -1) {
        throw new Error(`${language} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
      }

      // 如非指定，跳过
      if (targetLanguage !== language) {
        continue;
      }
    }

    // 换算内存单位
    const unitToMegeBytes = memoryUnitMap[unit];
    if (!unitToMegeBytes) {
      throw new Error(`${xmlPath} 无法识别的容量单位 ${unit}!`)
    }

    // 计算内存限制
    memoryMegaBytesLimit = parseInt(_) * unitToMegeBytes;
  }

  // 搜集spj主程序
  const spjJudgers = spjs && spjs.length ? spjs.map(spj => spj.$) : [];
  const sourceFileList = sourceFiles && sourceFiles.length ? sourceFiles.map(sourceFile => sourceFile.$) : [];
  // 题目数据
  let questions = [];

  if (test_inputs && test_inputs.length) {
    // 循环加载输出输出数据，并且过滤掉隐藏数据
    for (let i = 0; i < test_inputs.length; i++) {
      const testInput = test_inputs[i].$;
      const testOutput = test_outputs[i].$;

      const { src: outSrc = null } = testOutput || {};

      // 没有输入数据，有输出数据的情况
      if ((!testInput || !testInput.src) && (testOutput && testOutput.src)) {
        // 整理题目数据
        questions.push({
          inSrc: null,
          outSrc
        });

        continue;
      }
      // 资源中心和南外， 该字段是stringify的[]， 字段名是close
      const {
        // hide = null, 
        src: inSrc
      } = testInput;

      // // 如果有需要对于当前语言隐藏的数据，跳过一下
      // if(hide && hide !== null) {
      //     const hideLanguages = hide.split(',').map(hideLanguage => hideLanguage.trim());
      //     for(const hideLanguage of hideLanguages) {
      //         // 检查可用性
      //         if(availableLanguages.indexOf(hideLanguage) === -1) {
      //             throw new Error(`第${i}组测试数据的语言${hideLanguage}作为隐藏语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
      //         }    
      //     }

      //     if(hideLanguages.indexOf(targetLanguage) !== -1) {
      //         continue
      //     }
      // }

      if (!testOutput || !testOutput.src) {
        // 整理题目数据
        questions.push({
          inSrc,
          outSrc: null
        });
        continue;
      }

      // 整理题目数据
      questions.push({
        inSrc,
        outSrc
      });
    }
  }

  // SPJ状态下可以没有测试输入输出
  if (spjJudgers.length === 0 && questions.length === 0) {
    throw new Error(`${xmlPath} 对于编程语言 ${targetLanguage} 没有可用的数据，请检查！`);
  }

  const result = {
    cpuSecondsLimit, memoryMegaBytesLimit, spjJudgers, questions, sourceFileList
  };

  // console.debug(result);

  return result;
}

// 获取FPS格式XML文件用于判决的题目配置
async function getFPSSharedFileHashList(xmlPath) {
  // 加载文件
  const xmlString = await fs.readFile(xmlPath, {
    encoding: 'utf-8'
  });

  const parser = new xml2js.Parser();
  const xmlContent = await parser.parseStringPromise(xmlString);

  const { fps } = xmlContent;
  if (!fps) {
    throw new Error(`${xmlPath} 的文件格式不是FPS格式，请检查！`);
  }

  if (!fps.item) {
    return [];
  }

  // 加载题目
  if (fps.item.length !== 1) {
    throw new Error(`仅支持FPS格式包含1道题，目前 ${xmlPath} 的题目数为 ${fps.item.length}`);
  }

  // 解析参数
  const question = fps.item[0];
  const { spj_file: spjs = null, test_input: test_inputs = null, test_output: test_outputs = null } = question;

  const paths = [];
  if (spjs) {
    for (const { $ } of spjs) {
      if (!$) {
        continue;
      }

      paths.push($.src);
    }
  }

  if (test_inputs) {
    for (const { $ } of test_inputs) {
      if (!$) {
        continue;
      }

      paths.push($.src);
    }
  }

  if (test_outputs) {
    for (const { $ } of test_outputs) {
      if (!$) {
        continue;
      }

      paths.push($.src);
    }
  }

  // 分析路径里面的Hash串
  const hashs = paths.map(path => path.match(/\/([^\.]+)\.\w+$/)[1]);
  // console.debug(hashs);

  return hashs;
}

// 生成uuID
function makeUUID(params) {
  const value = `${moment().format('YYYYMMDDHHmmss')}_${params}`;
  return md5(value);
}

/*
* 获取拆解OI类型XML文件格式
* @params: content<String>, ifHasAnswer<Boolean>
* @return: { ifShowAnswer<String>, questions<Array>, title<String> }
*/
function getOIQuestion(content, ifHasAnswer) {
  const srcReg = /src=[\"]([^\"]*)[\"]?/ig; // 匹配图片中的href
  content = content.replace(srcReg, (_, src) => {
    return `src='${src}'`;
  });

  const questions = [];
  const $xml = cheerio.load(content, {
    xmlMode: true,
    decodeEntities: true,
  });
  const ifShowAnswer = $xml('ifShowAnswer').text();
  const ifSubmitLimit = $xml('ifSubmitLimit').text();
  const submitLimit = $xml('submitLimit').text();
  const title = $xml('title').text();

  const rawQuestions = $xml('question');
  // 循环大题
  for (let i = 0; i < rawQuestions.length; i++) {
    const rawQuestion = $xml(rawQuestions[i]);

    const value = rawQuestions[i];

    // 历史数据
    const label = value.attribs.title; // 大题标题
    const content = rawQuestion.find('content').text(); // 大题文本内容

    // 获取文本
    rawQuestion.find('content').each(function () {
      const { attribs } = this;

      // 兼容历史数据
      if (!attribs.UUID) {
        if (label) {
          questions.push({
            index: value.attribs.index,
            UUID: makeUUID(label),
            content: label,
            questionType: '文本',
          });
        }

        if (content) {
          questions.push({
            index: value.attribs.index,
            UUID: makeUUID(content),
            content: content,
            questionType: '文本',
          });
        }
      } else {
        // 处理新数据
        $xml(this).each(function () {
          const option = {
            index: attribs.index,
            UUID: attribs.UUID,
            content: $xml(this).text(),
            questionType: '文本',
          };
          questions.push(option);
        });
      }
    });

    ['singleChoice', 'multipleChoice'].forEach((key) => {
      // 循环大题中获取选择题
      rawQuestion.find(key).each(function () {
        const { attribs } = this;
        const question = {
          index: attribs.index,
          UUID: attribs.UUID || makeUUID(attribs.text), // compatible with the former data
          content: attribs.text,
          options: [],
          questionType: key === 'singleChoice' ? '单选题' : '多选题',
        };

        if (ifHasAnswer) {
          question.answer = (key === 'singleChoice' ? attribs.answer : (attribs.answer && attribs.answer.length ? attribs.answer.split('、') : ''));
        }

        $xml(this).find('option').each(function () {
          const option = {
            key: this.attribs.value,
            image: this.attribs.src,
            text: $xml(this).text(),
          };
          question.options.push(option);
        });

        questions.push(question);
      });
    });

    // 循环大题中获取填空题
    rawQuestion.find('completion').each(function () {
      const { attribs } = this;
      const completionText = attribs.text;
      let newContent = '';
      const answer = [];
      if (completionText) {
        const completionTextList = completionText.split('【__');

        let index = 1;
        for (const text of completionTextList) {
          const regValue = text.match(/(.+)__】/);
          if (!regValue) {
            if (text.startsWith('__】')) {
              newContent += '【__' + text;
            } else {
              newContent += text;
            }
            continue;
          }
          newContent += '【____】';
          answer.push({ key: index, text: regValue[1].split('|') });
          const endText = text.substring(regValue[0] ? regValue[0].length : 0, text.length);
          newContent += endText;
          index += 1;
        }
      }

      const question = {
        index: attribs.index,
        UUID: attribs.UUID || makeUUID(attribs.text), // compatible with the former data
        content: newContent,
        questionType: '填空题',
      };

      if (ifHasAnswer) {
        question.answer = answer;
      } else {
        question.answer = answer.map((data) => ({ ...data, text: '' }))
      }
      questions.push(question);
    });

    rawQuestion.find('jumbledSentence').each(function () {
      const { attribs } = this;

      const question = {
        index: attribs.index,
        UUID: attribs.UUID || makeUUID(attribs.text), // compatible with the former data
        content: attribs.text,
        options: [],
        answer: {},
        questionType: '选择填空题',
      };

      $xml(this).find('answer').each(function () {
        const answerKey = $xml(this).text();
        question.answer[answerKey] = this.attribs.key;
      });

      $xml(this).find('option').each(function () {
        const option = {
          key: this.attribs.value,
          image: this.attribs.src,
          text: $xml(this).text(),
        };
        question.options.push(option);
      });

      if (!ifHasAnswer) {
        question.answer = {};
      }

      questions.push(question);
    });

    rawQuestion.find('codeCompletion').each(function () {
      const { attribs } = this;

      const question = {
        index: attribs.index,
        UUID: attribs.UUID || makeUUID(attribs.text), // compatible with the former data
        content: attribs.text,
        answer: {},
        questionType: '编程填空题',
      };

      $xml(this).find('answer').each(function () {
        const answerKey = this.attribs.key;
        question.answer[answerKey] = [];

        $xml(this).find('subAnswer').each(function () {
          const answerContent = $xml(this).text();
          question.answer[answerKey].push(answerContent);
        });
      });

      if (!ifHasAnswer) {
        question.answer = {};
      }

      questions.push(question);
    });
  };

  questions.sort((a, b) => {
    return parseInt(a.index, 10) - parseInt(b.index, 10);
  });

  return { ifShowAnswer, ifSubmitLimit, submitLimit, questions, title };
}

/*
* 处理oj课的题解
* @params: content<String>
* @returns: questions<Object>
*/
function getOJAnswer(content) {
  const $xml = cheerio.load(content, {
    xmlMode: true,
    decodeEntities: true,
  });

  const questions = [];
  const rawQuestions = $xml('fps>item');
  for (let i = 0; i < rawQuestions.length; i++) {
    const rawQuestion = $xml(rawQuestions[i]);
    // 整理测试输入输出文件
    let testFiles = [];
    rawQuestion.find('test_input').each(function (idx) {
      const fileName = `${idx + 1}`;
      const testFile = {
        type: 'inout',
        fileName,
        in: this.attribs.src,
        out: null,
      };
      testFiles.push(testFile);
    });

    rawQuestion.find('test_output').each(function (idx) {
      const testFile = testFiles[idx];
      if (!testFile) {
        return;
      }

      // testFile.out = $xml(this).text();
      testFile.out = this.attribs.src;
    });
    // 必须输入输出数量匹配，否则过滤之
    testFiles = testFiles.filter(testFile => testFile.out);

    // 整理样例输入输出
    let inOutExamples = [];
    rawQuestion.find('sample_input').each(function () {
      const inOutExample = {
        inExample: $xml(this).text(),
        outExample: null,
        description: null,
      };
      inOutExamples.push(inOutExample);
    });

    rawQuestion.find('sample_output').each(function (idx) {
      let inOutExample = inOutExamples[idx];
      if (!inOutExample) {
        return;
      }

      inOutExample.outExample = $xml(this).text();
    });

    rawQuestion.find('sample_description').each(function (idx) {
      let inOutExample = inOutExamples[idx];
      if (!inOutExample) {
        return;
      }

      inOutExample.description = $xml(this).text();
    });

    let tags = rawQuestion.find('source').text();
    try {
      tags = JSON.parse(tags);
    } catch (e) {
      tags = [];
    }

    // 处理资源文件
    const file = [];
    rawQuestion.find('source_file').each(function () {
      // testFile.out = $xml(this).text();
      file.push({ name: this.attribs.name, path: this.attribs.src });
    });
    // 处理资源文件
    const spjFiles = [];
    rawQuestion.find('spj_file').each(function () {
      spjFiles.push({ name: this.attribs.name, path: this.attribs.src });
    });

    // 处理资源文件
    const exampleSolutions = {
      openSolution: rawQuestion.find('solution_open').text(),
      solutionList: [],
    };
    rawQuestion.find('solution').each(function (idx) {
      const { publishUser, note, time, avatar } = this.attribs;
      exampleSolutions.solutionList.push({
        index: idx + 1,
        code: $xml(this).text(),
        publishUser,
        note,
        time,
        avatar
      });
    });

    // 组织题目信息
    questions.push({
      content: rawQuestion.find('description').text(),
      dataRangePrompts: rawQuestion.find('hint').text(),
      inFormat: rawQuestion.find('input').text(),
      inOutExamples,
      outFormat: rawQuestion.find('output').text(),
      questionType: rawQuestion.find('question_type').text(),
      // serial: rawQuestion.find('serial').text() ? rawQuestion.find('serial').text() : null, // 题号
      title: rawQuestion.find('title').text(),
      timeLimit: rawQuestion.find('time_limit').text(),
      memoryLimit: rawQuestion.find('memory_limit').text(),
      defaultCode: rawQuestion.find('defaultCode').text(),
      judgeMenu: {
        testFiles,
        file,
        spjFiles,
      },
      tags,
      exampleSolutions,
    });
  }
  return questions[0];
}

/* 
* 变成OJ题的xml文件
* @params: question<Object>
* @returns: xmlLines<String>
*/
function changeToOJ(question) {
  const xmlLines = [
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<?xml-stylesheet type="text/css" href="book.css"?>',
  ];
  xmlLines.push('<fps version="1.2" url="https://github.com/zhblue/freeproblemset/">');
  xmlLines.push('<generator name="NFLSOJ" url="http://www.nfls.com.cn:10443/"/>');
  // 题目详细信息
  xmlLines.push('<item>');
  if (question.title) {
    xmlLines.push(`<title><![CDATA[${question.title}]]></title>`);
  }

  if (question.timeLimit) {
    xmlLines.push(`<time_limit unit="s"><![CDATA[${question.timeLimit}]]></time_limit>`);
  }

  if (question.memoryLimit) {
    xmlLines.push(`<memory_limit unit="mb"><![CDATA[${question.memoryLimit}]]></memory_limit>`);
  }

  if (question.content) {
    xmlLines.push(`<description><![CDATA[${question.content ? question.content : ''}]]></description>`);
  }

  if (question.inFormat) {
    xmlLines.push(`<input><![CDATA[${question.inFormat}]]></input>`);
  }

  if (question.outFormat) {
    xmlLines.push(`<output><![CDATA[${question.outFormat}]]></output>`);
  }

  if (question.questionType) {
    xmlLines.push(`<question_type><![CDATA[${question.questionType}]]></question_type>`);
  } else {
    xmlLines.push(`<question_type><![CDATA[编程题]]></question_type>`);
  }

  if (question.inOutExamples) {
    for (const inOutExample of question.inOutExamples) {
      xmlLines.push(`<sample_input><![CDATA[${inOutExample.inExample}]]></sample_input>`);
      xmlLines.push(`<sample_output><![CDATA[${inOutExample.outExample}]]></sample_output>`);
      xmlLines.push(`<sample_description><![CDATA[${inOutExample.description ? inOutExample.description : ''}]]></sample_description>`);
    }
  }

  if (question.judgeMenu) {
    const { testFiles, spjFiles, file } = question.judgeMenu;

    // 判决数据
    if (testFiles && testFiles.length) {
      for (const testFile of testFiles) {
        if (!testFile.in && !testFile.out) {
          continue;
        }

        if (testFile.in) {
          xmlLines.push(`<test_input src="${testFile.in}"/>`);
        } else if (!testFile.in && testFile.out) {
          xmlLines.push('<test_input />');
        }

        if (testFile.out) {
          xmlLines.push(`<test_output src="${testFile.out}"/>`);
        }
      }
    }

    // 资源文件
    if (file && file.length) {
      for (const sourceFile of file) {
        xmlLines.push(`<source_file name="${sourceFile.name}" src="${sourceFile.path}"/>`);
      }
    }

    // 特殊判决
    if (spjFiles && spjFiles.length) {
      for (const spjFile of spjFiles) {
        xmlLines.push(`<spj_file name="${spjFile.name}" src="${spjFile.path}"/>`);
      }
    }
  }

  // 实例题解
  if (question.exampleSolutions) {
    const { solutionList } = question.exampleSolutions;
    if (solutionList && solutionList.length) {
      for (const solution of solutionList) {
        const { note, code, publishUser, time, avatar } = solution;
        xmlLines.push(`<solution publishUser="${publishUser}" ${avatar ? `avatar="${avatar}"` : ''} note="${note}" time="${time}"><![CDATA[${code}]]></solution>`)
      }
    }

  }

  // 默认显示代码
  if (question.defaultCode) {
    xmlLines.push(`<defaultCode><![CDATA[${question.defaultCode}]]></defaultCode>`);
  }

  if (question.dataRangePrompts) {
    xmlLines.push(`<hint><![CDATA[${question.dataRangePrompts}]]></hint>`);
  }
  if (question.tags && question.tags.length) {
    xmlLines.push(`<source><![CDATA[${JSON.stringify(question.tags)}]]></source>`);
  }

  xmlLines.push('</item>');
  xmlLines.push('</fps>');
  return xmlLines.join('\n');
}

/*
* 生成客观题OI的XML文件
* @params: question: { questionList<Array>, title<String> }, ifShowAnswer<String>
* @returns: xmlLines<String>
*/
function changeToOI(question, ifShowAnswer, ifSubmitLimit, submitLimit) {
  const xmlLines = [
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<?xml-stylesheet type="text/css" href="book.css"?>',
  ];
  const { questionList, title } = question;
  if (ifShowAnswer) {
    xmlLines.push(`<ifShowAnswer>${ifShowAnswer}</ifShowAnswer>`);
  }
  if (ifSubmitLimit) {
    xmlLines.push(`<ifSubmitLimit>${ifSubmitLimit}</ifSubmitLimit>`);
  }

  if (submitLimit) {
    xmlLines.push(`<submitLimit>${submitLimit}</submitLimit>`);
  }

  if (title) {
    xmlLines.push(`<title><![CDATA[${title}]]></title>`);
  }
  
  if (questionList && questionList.length) {
    xmlLines.push('<question>');

    // 题目列表
    questionList.forEach((value, index) => {
      const { questionType, UUID } = value;
      switch (questionType) {
        case '文本':
          xmlLines.push(`<content UUID="${UUID}" index="${index}"><![CDATA[${value.content || ''}]]></content>`);
          break;
        case '单选题':
          xmlLines.push(`<singleChoice UUID="${UUID}" text="${escapeXml(value.content)}" index="${index}" answer="${value.answer}">`);
          if (value.options && value.options.length) {
            value.options.forEach((option) => {
              xmlLines.push(`<option value="${option.key}" src="${option.image}"><![CDATA[${option.text || ''}]]></option>`);
            });
          }
          xmlLines.push('</singleChoice>');
          break;
        case '多选题':
          xmlLines.push(`<multipleChoice UUID="${UUID}" text="${escapeXml(value.content)}" index="${index}" answer="${value.answer.join('、')}">`);
          if (value.options && value.options.length) {
            value.options.forEach((option) => {
              xmlLines.push(`<option value="${option.key}" src="${option.image}"><![CDATA[${option.text || ''}]]></option>`);
            });
          }
          xmlLines.push('</multipleChoice>');
          break;
        case '填空题':
          let blankParts = value.content ? value.content.split('【____】') : '';
          let content = value.content;

          // 空格排序
          if (blankParts && blankParts.length) {
            let newHtml = '';
            blankParts.forEach((each, blankIndex) => {
              newHtml += each;
              // 防止内容的双引号和图片之类的双引号重叠造成bug
              // newHtml += each.replace(/\"/g, "\'");
              if (blankIndex !== blankParts.length - 1) {
                const answer = value.answer.find((answerData) => parseInt(answerData.key) === (parseInt(blankIndex) + 1));
                newHtml += `【__${answer ? answer.text.join('|') : ''}__】`;
              }
            });

            content = newHtml;
          }

          xmlLines.push(`<completion UUID="${UUID}" text="${escapeXml(content)}" index="${index}"></completion>`);
          break;
        case '选择填空题':
          {
            xmlLines.push(`<jumbledSentence UUID="${UUID}" text="${escapeXml(value.content)}" index="${index}">`);

            for (const answerKey in value.answer) {
              xmlLines.push(`<answer key="${value.answer[answerKey]}"><![CDATA[${answerKey}]]></answer>`);
            }

            if (value.options && value.options.length) {
              value.options.forEach((option) => {
                xmlLines.push(`<option value="${option.key}" src="${option.image}"><![CDATA[${option.text || ''}]]></option>`);
              });
            }
            xmlLines.push('</jumbledSentence>');
            break;
          }
        case '编程填空题':
          {
            xmlLines.push(`<codeCompletion UUID="${UUID}" text="${escapeXml(value.content)}" index="${index}">`);

            for (const answerKey in value.answer) {
              xmlLines.push(`<answer key="${answerKey}">`);

              const subAnswers = value.answer[answerKey];
              for (const answer of subAnswers) {
                xmlLines.push(`<subAnswer><![CDATA[${answer}]]></subAnswer>`);
              }

              xmlLines.push('</answer>');
            }

            xmlLines.push('</codeCompletion>');
            break;
          }
        default:
          break;
      }
    });

    xmlLines.push('</question>');
  }
  return xmlLines.join('\n');
}

const languageMap = {
  c: 'C',
  cpp: 'C++',
  py: 'Python',
  pas: 'Pascal',
  java: 'Java',
  cs: 'C#',
  m: 'Obj-C',
  cl: 'CLang',
  cp: 'Clang++',
  js: 'JavaScript',
  go: 'Go',
};

// 获取FPS格式XML核心内容、输入、输出和SPJ数组
async function parseFPSXML(xmlString) {
  const parser = new xml2js.Parser();
  const xmlContent = await parser.parseStringPromise(xmlString);
  const { fps } = xmlContent;
  if (!fps) {
    throw new Error('文件格式不是FPS格式，请检查！');
  }

  if (!fps.item) {
    return {
      xmlContent, spjJudgers: [], testInputs: [], testOutputs: [],
    };
  }

  // 加载题目
  if (fps.item.length !== 1) {
    throw new Error(`仅支持FPS格式包含1道题，目前题目数为 ${fps.item && fps.item.length ? fps.item.length : 0}`);
  }

  // 解析参数
  const question = fps.item[0];
  const { time_limit: timeLimits = null, memory_limit: memoryLimits = null, spj_file: spjs = null, test_input: rawTestInputs = null, test_output: rawTestOutputs = null, source_file: rawScourceFiles = null } = question;

  if (!timeLimits || timeLimits.length === 0) {
    throw new Error('必要参数time_limit未设定!');
  }

  if (!memoryLimits || memoryLimits.length === 0) {
    throw new Error('必要参数memory_limit未设定!');
  }

  if (rawTestInputs || rawTestOutputs) {
    if (!rawTestInputs || rawTestInputs.length === 0) {
      throw new Error('必要参数test_inputs未设定!');
    }

    if (!rawTestOutputs || rawTestOutputs.length === 0) {
      throw new Error('必要参数test_outputs未设定!');
    }

    if (rawTestInputs.length !== rawTestOutputs.length) {
      throw new Error('必要参数test_inputs 数量与 test_outputs不一致!');
    }
  }

  // 从多个配置中找到最匹配的CPU时间限制
  for (const { $ } of timeLimits) {
    const { unit = 's', language } = $;

    // 如果有指定语言
    if (language) {
      // 检查可用性
      if (availableLanguages.indexOf(language) === -1) {
        throw new Error(`${language} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
      }
    }

    // 换算时间单位
    const unitToSecond = timeUnitMap[unit];
    if (!unitToSecond) {
      throw new Error(`无法识别的时间单位 ${unit}!`);
    }
  }

  // 从多个配置中找到最匹配的内存限制
  for (const { $ } of memoryLimits) {
    const { unit = 'm', language } = $;

    // 如果有指定语言
    if (language) {
      // 检查可用性
      if (availableLanguages.indexOf(language) === -1) {
        throw new Error(`${language} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
      }
    }

    // 换算内存单位
    const unitToMegeBytes = memoryUnitMap[unit];
    if (!unitToMegeBytes) {
      throw new Error(`无法识别的容量单位 ${unit}!`);
    }
  }

  // 搜集spj主程序
  const spjJudgers = [];

  if (spjs) {
    for (let i = 0; i < spjs.length; i++) {
      const { $ } = spjs[i];
      const { name } = $;

      // 裁剪后缀名
      const extList = name.match(/^\s*(.*)\.([^.]+)$/);
      const ext = extList && extList.length ? extList[2] : '';

      const language = languageMap[ext];
      // 检查可用性
      if (availableLanguages.indexOf(language) === -1) {
        throw new Error(`第${i}组SPJ的语言${language}不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
      }

      spjJudgers.push({
        language,
        code: spjs[i]._,
        ext,
        $,
      });

      delete spjs[i]._;
    }
  }

  // 题目数据
  const testInputs = [];
  const testOutputs = [];
  if (rawTestInputs && rawTestInputs.length) {
    // 循环加载输出输出数据，并且过滤掉隐藏数据
    for (let i = 0; i < rawTestInputs.length; i++) {
      const rawTestInput = rawTestInputs[i];
      if (typeof rawTestInput === 'object') {
        const { $ } = rawTestInput;
        const { hide = null } = $;

        // 如果有需要对于当前语言隐藏的数据，跳过一下
        if (hide !== null) {
          const hideLanguages = hide.split(',').map((hideLanguage) => hideLanguage.trim());
          for (const hideLanguage of hideLanguages) {
            // 检查可用性
            if (availableLanguages.indexOf(hideLanguage) === -1) {
              throw new Error(`第${i}组测试数据的语言${hideLanguage}作为隐藏语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
            }
          }
        }

        testInputs.push({
          content: rawTestInput._,
          $,
        });

        delete rawTestInput._;
      } else {
        const $ = {};
        rawTestInputs[i] = { $ };
        testInputs.push({
          content: rawTestInput,
          $,
        });
      }

      const output$ = {};
      testOutputs.push({
        content: rawTestOutputs[i],
        $: output$,
      });

      rawTestOutputs[i] = { $: output$ };
    }
  }

  // 资源文件处理
  const sourceFiles = [];
  if (rawScourceFiles && rawScourceFiles.length) {
    // 循环加载输出输出数据，并且过滤掉隐藏数据
    for (let i = 0; i < rawScourceFiles.length; i++) {
      const sourceFile = rawScourceFiles[i];
      if (typeof sourceFile === 'object') {
        const { $ } = sourceFile;
        sourceFiles.push({
          content: sourceFile._,
          $,
        });

        delete sourceFile._;
      } else {
        const $ = {};
        rawScourceFiles[i] = { $ };
        sourceFiles.push({
          content: rawScourceFiles,
          $,
        });
      }
    }
  }

  const result = {
    xmlContent, spjJudgers, testInputs, testOutputs, sourceFiles,
  };

  return result;
}

// 获取当前日期的格式化字符串（YYYY/MM/DD）
function getCurrentDatePath() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
}

// 获取字符串字节数
function getStringByteLength(str) {
  return Buffer.byteLength(str);
}

// 根据获取的XML核心内容，输出XML
function stringifyXML(xmlConent) {
  const builder = new xml2js.Builder();
  return builder.buildObject(xmlConent);
}

/*
* 大题->小题 二级层级下的客观题格式转换
* 获取拆解OI类型XML文件格式
* @params: content<String>, ifHasAnswer<Boolean>
* @returns: { ifShowAnswer<String>, questions<Array>, title<String> }
*/
function getOIQuestionOld(content, ifHasAnswer) {
  const srcReg = /src=[\"]([^\"]*)[\"]?/ig; // 匹配图片中的href
  content = content.replace(srcReg, (_, src) => {
    return `src='${src}'`;
  });

  const questions = [];
  const $xml = cheerio.load(content, {
    xmlMode: true,
    decodeEntities: true,
  });
  const ifShowAnswer = $xml('ifShowAnswer').text();
  const ifSubmitLimit = $xml('ifSubmitLimit').text();
  const submitLimit = $xml('submitLimit').text();
  const title = $xml('title').text();

  const rawQuestions = $xml('question');
  // 循环大题
  for (let i = 0; i < rawQuestions.length; i++) {
    const rawQuestion = $xml(rawQuestions[i]);

    const value = rawQuestions[i];
    const label = value.attribs.title;
    const content = rawQuestion.find('content').text();

    const questionsChild = [];
    ['singleChoice', 'multipleChoice'].forEach((key) => {
      // 循环大题中获取选择题
      rawQuestion.find(key).each(function () {
        const { attribs } = this;
        const question = {
          index: attribs.index,
          content: attribs.text,
          options: [],
          UUID: makeUUID(attribs.text),
          questionType: key === 'singleChoice' ? '单选题' : '多选题',
        };

        if (ifHasAnswer) {
          question.answer = (key === 'singleChoice' ? attribs.answer : (attribs.answer && attribs.answer.length ? attribs.answer.split('、') : ''));
        }

        $xml(this).find('option').each(function () {
          const option = {
            key: this.attribs.value,
            image: this.attribs.src,
            text: $xml(this).text(),
          };
          question.options.push(option);
        });

        questionsChild.push(question);
      });
    });

    // 循环大题中获取填空题
    rawQuestion.find('completion').each(function () {
      const { attribs } = this;
      const completionText = attribs.text;
      let newContent = '';
      const answer = [];
      if (completionText) {
        const completionTextList = completionText.split('【__');

        let index = 1;
        for (const text of completionTextList) {
          const regValue = text.match(/(.+)__】/);
          if (!regValue) {
            if (text.startsWith('__】')) {
              newContent += '【__' + text;
            } else {
              newContent += text;
            }
            continue;
          }
          newContent += '【____】';
          answer.push({ key: index, text: regValue[1].split('|') });
          const endText = text.substring(regValue[0] ? regValue[0].length : 0, text.length);
          newContent += endText;
          index += 1;
        }
      }

      const question = {
        index: attribs.index,
        content: newContent,
        questionType: '填空题',
        UUID: makeUUID(attribs.text),
      };

      if (ifHasAnswer) {
        question.answer = answer;
      } else {
        question.answer = answer.map((data) => ({ ...data, text: '' }))
      }
      questionsChild.push(question);
    });

    questionsChild.sort((a, b) => {
      return parseInt(a.index, 10) - parseInt(b.index, 10);
    });

    const option = { label, question: questionsChild };
    if (content) {
      option.content = content;
    }
    questions.push(option);
  };

  return { ifShowAnswer, ifSubmitLimit, submitLimit, questions, title };
}


exports.getInputLength = getInputLength;
exports.getFPSJudgeQuestion = getFPSJudgeQuestion;
exports.getFPSSharedFileHashList = getFPSSharedFileHashList;
exports.getOIQuestion = getOIQuestion;
exports.makeUUID = makeUUID;
exports.getOJAnswer = getOJAnswer;
exports.changeToOJ = changeToOJ;
exports.changeToOI = changeToOI;
exports.parseFPSXML = parseFPSXML;
// exports.parseFPSXMLForTrain = parseFPSXMLForTrain;
exports.getStringByteLength = getStringByteLength;
exports.stringifyXML = stringifyXML;
exports.getOIQuestionOld = getOIQuestionOld;

exports.availableLanguages = availableLanguages;