

// 解析课程引用资源
// @params fileContent
// @return ['../assets/xxx.png']
function getOIAndOJResources (fileContent) {
  let resources = parseResources(fileContent);

  const srcRegRule = new RegExp("src\\s*=\\s*['\"]([^'\"]+)['\"]", "g");
  const htmlImage = fileContent.match(srcRegRule) || [];

  htmlImage.forEach(image => {
    // file/course/slug/img.png?u=111111
    const parts = image.match(/src='file\/course\/(.+)\?(.?)/) || image.match(/src="file\/course\/(.+)\?(.?)/);
    // file/course/slug/img.png
    const imageParts = image.match(/src='file\/course\/(.+)\/(.+)/) || image.match(/src="file\/course\/(.+)\/(.+)/);
    let fileType = '';
    let fileName = '';

    if (imageParts && imageParts[1] && imageParts[2].length) {
      const srcParts = imageParts[1].split('/');
      fileType = srcParts[1];
      fileName = imageParts[2].substr(0, imageParts[2].length - 1);
    }

    if (parts && parts[1]) {
      const srcParts = parts[1].split('/');
      fileType = srcParts[1];
      fileName = srcParts[2];
    }

    if (fileType && fileName) {
      resources.push(`../${fileType}/${fileName}`);
    }
  });
  return resources;
}

// 解析课程引用资源
// @params fileContent JSON
// @return ['../assets/xxx.png']
function getAIResources(fileContent) {
  const { cells } = fileContent;
  const resources = [];

  for (const cell of cells) {
    const { source, cell_type, metadata } = cell;
    const { type } = metadata;
    const sourceText = source.join('');
    // 除 resources 和 filepreviewer 外的微应用无需检测
    const noCheck = (cell_type === 'code' && type && type !== 'resources' && type !== 'filepreviewer');
    if (noCheck) {
      continue;
    }

    const isResources = cell_type === 'code' && type && type === 'resources';
    const isFilePreviewer = cell_type === 'code' && type && type === 'filepreviewer';

    if (cell_type === 'markdown') {
      source.forEach((para) => {
        const parseList = parseResources(para);

        parseList.forEach(i => resources.push(i));
      });
      continue;
    }

    // 代码块-资源
    if (isResources) {
      // 资源类型为视频
      const videoParts = sourceText ? sourceText.match(/(?<=Video\()(.*?)(?=\))/g) : null;
      // 资源类型为图片
      const imageParts = sourceText ? sourceText.match(/(?<=Image\()(.*?)(?=\))/g) : null;

      const resourceParts = videoParts || imageParts;
      const allPathParts = resourceParts[0].match(/(?<=')(.*?)(?=')/);
      resources.push(allPathParts[0]);
      continue;
    }

    // 微应用文件预览
    if (isFilePreviewer) {
      const { config } = metadata;
      const { fileName } = config;
      const sourceName = `../assets/${fileName}`;
      resources.push(sourceName);
      continue;
    }

    // 代码块引用资源
    if (cell_type === 'code') {
      source.forEach((para) => {
        const resourceParts = para.match(/\.\.\/assets\/(.+)\.\w{1,4}/g) || para.match(/\.\.\/input\/(.+)\.\w{1,4}/g);
        if (resourceParts && resourceParts[0]) {
          resources.push(resourceParts[0]);
        }
      });
    }
  }

  return resources;
}

function getAccessResources(fileContent) {
  const { content, instructions } = fileContent;
  let resources = [];

  const contentList = parseResources(content);
  resources = contentList;

  instructions.forEach(item => {
    const descriptionList = parseResources(item.description);
    const guideList = parseResources(item.guide);

    descriptionList.forEach(i => resources.push(i));
    guideList.forEach(i => resources.push(i));
  })

  return resources;
}

function parseResources(content) {
  if (!content) {
    return [];
  }

  const resources = [];

  // 匹配 video 标签
  const videoReg = new RegExp("<video[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>", "g");
  const htmlVideo = content.match(videoReg) || [];

  htmlVideo.forEach((video) => {
    const videoParts = video.match("<video[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>");

    if (videoParts && videoParts[1]) {
      const isResources = videoParts[1].match(/(\.\.\/assets\/(.+))/) || videoParts[1].match(/(\.\.\/input\/(.+))/);
      if (isResources) {
        resources.push(videoParts[1]);
      }

      const parts = videoParts[1].match(/\.\.\/assets\/(.+)\?(.?)/);
      if (parts && parts[1]) {
        resources.push(`../assets/${parts[1]}`);
      }
    }
  });

  // 匹配 img 标签
  const htmlReg = new RegExp("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>", "g");
  const htmlImage = content.match(htmlReg) || [];

  htmlImage.forEach((img) => {
    const imgParts = img.match("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>");
    if (imgParts && imgParts[1]) {
      const isResources = imgParts[1].match(/(\.\.\/assets\/(.+))/) || imgParts[1].match(/(\.\.\/input\/(.+))/);

      if (isResources) {
        resources.push(imgParts[1]);
      }

      const parts = imgParts[1].match(/\.\.\/assets\/(.+)\?(.?)/);
      if (parts && parts[1]) {
        resources.push(`../assets/${parts[1]}`);
      }
    }
  })

  // 匹配 a 标签
  const hrefReg = new RegExp("(?<=href=\").+?(?=\")|(?<=href=\').+?(?=\')", "g");
  const htmlHref = content.match(hrefReg) || [];
  htmlHref.forEach((a) => {
    const hrefParts = a.match(/(\.\.\/assets\/(.+))/) || a.match(/(\.\.\/input\/(.+))/);
    if (hrefParts) {
      resources.push(a);
    }
  });

  // 匹配 markdown
  const matchMarkdownResource = content.match(/(\.\.\/assets\/(.+))/) || content.match(/(\.\.\/input\/(.+))/);
  if (matchMarkdownResource) {
      const markdownPart = content.match(/!?\[.*?\]\(((.*?)\..{2,5})\)/g);
      if (markdownPart) {
        markdownPart.forEach(item => {
          const markdownImage = item.match(/!?\[.*?\]\(((.*?)\..{2,5})\)/);
          resources.push(markdownImage[1]);
      })
      }
  }

  return resources;
}

// get the assets files of the scratch course
const getScratchAssets = (content) => {
  const resources = new Set();
  const targets = content['targets'];
  for (const target of targets) {
    const costumes = target['costumes'];
    for (const costume of costumes) {
      const assetId = costume['assetId'];
      const dataFormat = costume['dataFormat'];

      if (assetId && dataFormat) {
        resources.add(`../assets/${assetId}.${dataFormat}`);
      }
    }
  }

  return Array.from(resources);
}

const getPPTAssets = (content) => {
  const resources = [];
  const { assetDir = 'assets', fileName, fileExt = 'pdf' } = content;

  resources.push(`../${assetDir}/${fileName}.${fileExt}`);
  return resources;
}

const getCodeBlankAssets = (fileContent) => {
  let resources = [];
  const { content } = fileContent;
  if (!content) {
    return resources;
  }
  
  resources = parseResources(content);
  return resources;
}

// 休眠
function sleep(time) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

// 编程填空题判分 去除字符串以外的字符空格
function handleTrimStr(s) {
  if (!s) {
    return s;
  }

  const reg = /[\"|\'](.*?)[\"|\']/gi;
  const quoteStrs = s.match(reg);

  let subStrs = s.split(/"|'/);

  const newArr = [];
  for (let str of subStrs) {
    if (quoteStrs && quoteStrs.indexOf(`"${str}"`) !== -1) {
      str = `"${str}"`;
    } else if (quoteStrs && quoteStrs.indexOf(`'${str}'`) !== -1) {
      str = `'${str}'`;
    } else {
      str = str.replace(/\s/g, '');
    }

    newArr.push(str);
  }

  return newArr.join('');
}

function list2Object(allPlans) {
  const planList = [];

  for (let i = 0; i < allPlans.length; i += 2) {
    if (i % 2 === 0) {
      planList.push({ time: allPlans[i + 1], planID: allPlans[i] });
    }
  }

  return planList;
}

exports.getOIAndOJResources = getOIAndOJResources;
exports.getAIResources = getAIResources;
exports.sleep = sleep;
exports.getAccessResources = getAccessResources;
exports.getScratchAssets = getScratchAssets;
exports.parseResources = parseResources;
exports.getPPTAssets = getPPTAssets;
exports.getCodeBlankAssets = getCodeBlankAssets;
exports.handleTrimStr = handleTrimStr;
exports.list2Object = list2Object;
