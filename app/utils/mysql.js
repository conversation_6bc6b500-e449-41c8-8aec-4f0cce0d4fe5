
const pathLib = require('path');
const sleep = require('mz-modules/sleep');
const AUTH_RETRIES = Symbol('authenticateRetries');

// 存储本线程的数据库连接
const modelInstanceMap = {};

// 学校配置参数缓存
const schoolConfigMap = {};

/**
 * Authenticate to test Database connection.
 *
 * This method will retry 3 times when database connect fail in temporary, to avoid Egg start failed.
 * @param {Application} database instance of sequelize
 */
async function authenticate(app, database) {
  database[AUTH_RETRIES] = database[AUTH_RETRIES] || 0;

  try {
    await database.authenticate();
  } catch (e) {
    if (database[AUTH_RETRIES] >= 3) throw e;

    // sleep 1s to retry, max 3 times
    database[AUTH_RETRIES] += 1;
    app.logger.warn(`Sequelize Error: ${e.message}, sleep 1 seconds to retry...`);
    await sleep(1000);
    await authenticate(app, database);
  }
}

async function loadDatabase(app, schoolSlug, config) {
  // 建立Sequlize数据库连接
  // console.log('config:',config)

  const sequelize = config.connectionUri ?
    new app.Sequelize(config.connectionUri, config) :
    new app.Sequelize(config.database, config.username, config.password, config);

  // 配置学校标记
  sequelize.schoolSlug = schoolSlug;

  // 加载Model目录中的文件到Sequlize上
  const models = [];
  const delegate = 'model';
  const { baseDir, exclude = undefined } = config;

  // 这里的目的是加载Model文件夹中的学校Model到app
  const target = Symbol(`${schoolSlug}_${delegate}`);
  const modelDir = pathLib.join(app.baseDir, 'app', baseDir);
  app.loader.loadToApp(modelDir, target, {
    caseStyle: 'upper',
    ignore: exclude,
    filter(model) {
      if (!model || !model.sequelize) return false;
      models.push(model);
      return true;
    },
    initializer(factory) {
      if (typeof factory === 'function') {
        return factory(app, sequelize);
      }
    },
  });
  Object.assign(sequelize, app[target]);

  // 初始化Model中的关联
  models.forEach(model => {
    try 
    {
      typeof model.associate === 'function' && model.associate(sequelize);
    }
    catch(e) {
      console.log(`${model.name}表关联异常！`);
      throw new Error(`${model.name}表关联异常！`);
    }
  });

  // 尝试与数据库真正连接
  await authenticate(app, sequelize);

  return sequelize;
}

// 清理空闲的数据库连接
function removeIdleDBConnection() {
  const interval = 5 * 60 * 1000;
  const now = Date.now();
  
  for (const schoolSlug in modelInstanceMap) {
    const model = modelInstanceMap[schoolSlug];
    const duration = now - model.timeStamp;

    // 如果时间超过5分钟，删除
    if (duration < interval) {
      continue;
    }

    // 关闭连接
    model.close()
    
    // 删除引用
    delete modelInstanceMap[schoolSlug];
  }

}

async function setupModel(app, schoolSlug, service, ctx, noCache = false) {
    let model = null;
 
    // 看看有无缓存的Model
    if (!noCache) {
      model = modelInstanceMap[schoolSlug];
    }

    if(!model) {
      // 创建新的模型，并且缓存
      let schoolConfig = schoolConfigMap[schoolSlug];
      if(!schoolConfig) {
        // 获取数据库配置
        const baseConfig = await service.school.getSchoolDatabaseConfig(schoolSlug);
        if(!baseConfig) {
          console.error(`无法获取配置，${schoolSlug}学校尚未开通，请联系系统管理员！`);
          throw new Error( `无法获取配置，请检查网址是否输入正确，如果学校尚未开通，请加QQ群氦星人信息教学支持，群号：375577627，联系群内技术支持！`);
        }

        // 合并原始配置，得到学校数据库配置
        schoolConfig = {
          ...app.config.sequelize,
          delegate: 'model',
          baseDir: 'model/school',
          ...baseConfig,
          logging: function(msg) {
            console.log.apply(null, [schoolSlug, msg]);
          }
        };
        
        schoolConfigMap[schoolSlug] = schoolConfig;
      }

      // 连接模型
      try {
        model = await loadDatabase(app, schoolSlug, schoolConfig);
      }
      catch(e) {
        console.log('e:',e)
        console.error(`无法连接数据库，${schoolSlug}学校尚未开通，请联系系统管理员！`);
        throw new Error( `无法连接数据库，请检查网址是否输入正确，如果学校尚未开通，请加QQ群氦星人信息教学支持，群号：375577627，联系群内技术支持！！`);
      }

      // 缓存模型
      if(!noCache) {
        modelInstanceMap[schoolSlug] = model;
      }
    }

    model.timeStamp = Date.now();
    ctx.model = model;
    
    return model;
}

exports.setupModel = setupModel;
exports.removeIdleDBConnection = removeIdleDBConnection;
