
const wget = require('./wget-internal');
const process = require('process');

const wgetPromised = function(src, output, options = { 
    gunzip: true,
    headers: {
        'accept-encoding': 'gzip',
        'user-agent': 'hxr-async-task',
    }
}) {
    // console.log("wget options", options)
    return new Promise(function(resolve, reject) {
      // 开始时间
      const beginAt = (new Date()).getTime();
      console.log(`开始下载文件${src}至${output}`)
      const download = wget.download(src, output, options);
      let lastProgress = 0.0;
      
      download.on('error', function(err) {
          reject(err);
      });
  
      download.once('end', function(downloadedSize) {
        const totalSize = Math.round(downloadedSize / 1024.0 / 1024.0 * 100.0) / 100.0;
        console.log(`实际传输大小${totalSize}M字节`);

        const endAt = (new Date()).getTime();
        const duration = (endAt - beginAt) / 1000.0;
        const speed = Math.round(totalSize / duration * 100.0) / 100.0;
        console.log(`速度${speed}M字节/秒`);
  
        resolve(output)
      });
  
      download.on('progress', function(p) {
          let progress = Math.round(p * 100.0 * 100.0) / 100.0;
          if(progress - lastProgress <= 10.0) {
              return;
          }

          lastProgress = progress;
          console.log(`下载进度${progress}%`);
      });
  
    });
}

exports.wgetPromised = wgetPromised;

async function test() {
    await wgetPromised('http://institutions.i51cy.com/file/download/7d8e7290-e883-11ec-8c69-098f491ddfb1/1.in', 'demo.iso');
}

// 测试用例
console.log(process.argv);
if(process.argv[1] === __filename) {
    test();
}