const { handleTrimStr } = require('./tool');

function getSingleChoiceResult(answer, studentAnswer) {
  if (!studentAnswer) {
    return false;
  }

  if (studentAnswer !== answer) {
    return false;
  }

  return true;
}

function getMultipleChoiceAnswerResult(answer, studentAnswer) {
  if (!studentAnswer || !studentAnswer.length) {
    return false;
  }

  if (answer.length !== studentAnswer.length) {
    return false;
  }

  for (const rAnswer of answer) {
    if (studentAnswer.indexOf(rAnswer) === -1) {
      return false;
    }
  }

  return true;
}

function judgeBlank(newVersion, stuAnswer, rawAnswer) {
  if (!stuAnswer) {
    return false;
  }

  const answerList = stuAnswer.split(',');
  if (!answerList.length) {
    return false;
  }

  // 新格式"__[填空1]__": [['A'], ['B']], 满足外层数组其一即可，如果答案为多选，即内层数组长度大于1，则学生答案需与此答案一致
  if (newVersion) {
    let result = false;
    for (const answers of rawAnswer) {
      const answerStr = answers.sort().join(',');
      const stuAnserStr = stuAnswer.split(',').sort().join(',');
      if (answerStr === stuAnserStr) {
        result = true;
      }
    }

    return result;
  } else {
    const rawAnswerList = rawAnswer.split(',');
    // 旧格式如"__[填空1]__": "A,B", 满足答案其一即可
    for (const item of answerList) {
      if (rawAnswerList.indexOf(item) === -1) {
        return false;
      }
    }

    return true;
  }
}

function getJumbledSentenceResult(answer, studentAnswer, score) {
  if (!studentAnswer || !Object.keys(studentAnswer) || !Object.keys(studentAnswer).length) {
    return { result: false, score: 0 };
  }

  let totalScore = 0;
  let results = {};
  let result = true;

  for (const answerKey in answer) {
    let answers = answer[answerKey];
    let newStuAnswer = studentAnswer[answerKey];

    let newVersion = Array.isArray(answers);
    let isPass = judgeBlank(newVersion, newStuAnswer, answers);

    // 小题错误
    if (!isPass) {
      result = false;
      results[answerKey] = false;
      continue;
    }

    // 小题正确
    results[answerKey] = true;
    if (score) {
      totalScore += score[answerKey];
    }
  }

  return { result, results, score: totalScore };
}

// 填空题判分
function getBlanksResult(answer, studentAnswer, score) {
  if (!studentAnswer || !Object.keys(studentAnswer) || !Object.keys(studentAnswer).length) {
    return { result: false, score: 0 };
  }

  let totalScore = 0;
  let results = {};
  let result = true;
  for (const answerKey in answer) {
    let answers = answer[answerKey];
    let newStuAnswer = studentAnswer[answerKey];

    if (!newStuAnswer) {
      result = false;
      results[answerKey] = false;
      continue;
    }

    // 答案去除空格
    answers = answers.map(ans => {
      return ans && ans.trim();
    });

    newStuAnswer = newStuAnswer.trim();

    const hasAnswer = answers.indexOf(newStuAnswer) !== -1;
    if (!hasAnswer) {
      results[answerKey] = false;
      result = false;
      continue;
    }

    if (hasAnswer && score) {
      totalScore += score[answerKey];
    }

    results[answerKey] = true;
  }

  return { result, results, score: totalScore };
}

// 编程填空题判分
function getCodeBlankResult(answer, studentAnswer, score) {
  if (!studentAnswer || !Object.keys(studentAnswer) || !Object.keys(studentAnswer).length) {
    return { result: false, score: 0 };
  }

  let totalScore = 0;
  let results = {};
  let result = true;
  for (const answerKey in answer) {
    let answers = answer[answerKey];
    let newStuAnswer = studentAnswer[answerKey];

    if (!newStuAnswer) {
      result = false;
      results[answerKey] = false;
      continue;
    }

    // 判分去除空格，字符串内空格除外
    answers = answers.map(ans => {
      return handleTrimStr(ans);
    });

    newStuAnswer = handleTrimStr(newStuAnswer);

    const hasAnswer = answers.indexOf(newStuAnswer) !== -1;
    if (!hasAnswer) {
      results[answerKey] = false;
      result = false;
      continue;
    }

    if (hasAnswer && score) {
      totalScore += score[answerKey];
    }

    results[answerKey] = true;
  }

  return { result, results, score: totalScore };
}

function getAccessResult(answer, studentAnswerContent) {
  if (!studentAnswerContent || !studentAnswerContent.length) {
    return { result: false, score: 0 };
  }

  let totalScore = 0;
  let result = true;
  for (let i = 0; i < answer.length; i++) {
    const hasAnswer = studentAnswerContent && studentAnswerContent[i] && studentAnswerContent[i].status === 'finish';

    if (!hasAnswer) {
      result = false;
      continue;
    } else {
      totalScore += answer[i].score;
    }
  }

  return { result, score: totalScore };
}

/* 
 * 自由模式学生做题，获取每题的判分结果，初始得分和得分
 * @param questionType // 题目类型 
 * @param questionScore // 单选题得分由模板给出，题目本身没有分数
 * @param referAnswer // 题目参考答案 
 * @param studentAnswer // 学生答案
 * @param freeMode // 自由模式
 * @return newInitScore // 初始得分，如果不是初次判分，返回已有的初始得分 
 * @return newScore // 题目得分 
 * @return newAnswer // 题目答案，包括学生答案内容，学生答案结果，学生答案得分及初始得分 
 *  {
 *    answer,
 *    score,
 *    result,
 *    results, // 填空题 选择填空题 编程填空题
 *    initAnswer,
 *    initScore,
 *    initResult,
 *    initResults, // 填空题 选择填空题 编程填空题
 *  }
*/
function getQuestionResult({ freeMode, questionID, questionType, questionDetail, questionScore, referAnswer, studentAnswer }) {
  // debugger;
  if (!questionID) {
    throw new Error(`题目 ${questionID} 不存在`);
  }

  if (!referAnswer) {
    throw new Error(`题目 ${questionID} 无参考答案`);
  }

  // 新的答案
  let newAnswer = {};
  // 得分
  let newScore = 0;
  let newInitScore = 0;
  // 结果
  let newResult = false;
  // 填空题 选择填空题 编程填空题 结果
  let newResults = {};

  let blankTypes = ['填空题', '选择填空题', '编程填空题'].indexOf(questionType) !== -1;

  // 参考答案 非综合题
  const { answer: referAnswerContent, score: answerScore } = referAnswer;

  // 学生答案
  const { answer: studentAnswerContent, initResult, initResults, initAnswer, initScore, submitJudge } = studentAnswer || {};

  // 初次判分
  let noJudge = (questionType !== '综合题') ? (initResult === undefined) : false;

  // 综合题单独处理
  if (freeMode && questionType !== '综合题') {
    let defaultResult = { newInitScore: noJudge ? newScore : initScore, newScore, newAnswer: studentAnswer };

    // 自由模式非单选题，如果未提交判分，则不保存判分结果
    if (questionType !== '单选题' && !submitJudge) {
      return defaultResult;
    }

    // 自由模式单选题，如果未作答，跳过
    if (questionType === '单选题' && !studentAnswerContent) {
      return defaultResult;
    }
  }

  switch (questionType) {
    case '单选题':
      {
        let result = getSingleChoiceResult(referAnswerContent, studentAnswerContent);
        newScore = result ? questionScore : 0;
        newResult = result;
        break;
      }
    case '多选题':
      {
        let result = getMultipleChoiceAnswerResult(referAnswerContent, studentAnswerContent);
        newScore = result ? answerScore : 0;
        newResult = result;
        break;
      }
    case '选择填空题':
      {
        let { result, results, score } = getJumbledSentenceResult(referAnswerContent, studentAnswerContent, answerScore);
        newScore = score;
        newResult = result;
        newResults = results;
        break;
      }
    case '填空题':
      {
        let { result, results, score } = getBlanksResult(referAnswerContent, studentAnswerContent, answerScore);
        newScore = score;
        newResult = result;
        newResults = results;
        break;
      }
    case '编程填空题':
      {
        let { result, results, score } = getCodeBlankResult(referAnswerContent, studentAnswerContent, answerScore);
        newResult = result;
        newScore = score;
        newResults = results;
        break;
      }
    case 'Access操作题':
    case 'WPS表格操作题':
      {
        let { result, score } = getAccessResult(referAnswer, studentAnswerContent);
        newResult = result;
        newScore = score;
        break;
      }
    case '综合题':
      {
        // 综合题得分
        let complexScore = 0;
        // 综合题初始得分
        let complexInitScore = 0;
        // 综合题学生答案
        let complexResults = {};

        const defaultResult = { newInitScore: complexInitScore, newScore: complexScore, newAnswer: complexResults };

        if (!questionDetail || !questionDetail.length) {
          console.error(`综合题 ${questionID} 无小题内容`);
          return defaultResult;
        }

        if (!studentAnswer) {
          return defaultResult;
        }

        for (const smallQuestion of questionDetail) {
          const { UUID, questionType: type } = smallQuestion;
          // 忽略文本
          if (type === '文本') {
            continue;
          }

          const currentReferAnswer = referAnswer[UUID];
          if (!currentReferAnswer) {
            throw new Error(`题目 ${questionID} ${UUID} 无参考答案`);
          }

          // 未作答，跳过
          const currentStudentAnswer = studentAnswer[UUID];
          if (!currentStudentAnswer) {
            continue;
          }

          let questionScore = 0;
          if (type === '单选题') {
            questionScore = currentReferAnswer.score;
          }

          let { newInitScore, newScore, newAnswer } = getQuestionResult({ freeMode, questionID: UUID, questionType: type, questionScore, referAnswer: currentReferAnswer, studentAnswer: currentStudentAnswer });

          complexResults[UUID] = newAnswer;
          complexScore += newScore;
          complexInitScore += newInitScore;
        }

        return { newInitScore: complexInitScore, newScore: complexScore, newAnswer: complexResults };
      }
    default:
      throw new Error(`获取答题结果，不支持的题型${questionType}`);
  }

  // 记录小题判分
  newAnswer = { ...studentAnswer, answer: studentAnswerContent, result: newResult, score: newScore };
  if (blankTypes) {
    newAnswer['results'] = newResults;
  }

  // 本题初始得分
  newInitScore = noJudge ? newScore : initScore;

  // 自由模式初始得分
  if (freeMode) {
    newAnswer['initResult'] = noJudge ? newResult : initResult;
    newAnswer['initAnswer'] = noJudge ? studentAnswerContent : initAnswer;
    newAnswer['initScore'] = newInitScore;

    if (blankTypes) {
      newAnswer['initResults'] = noJudge ? newResults : initResults;
    }
  }

  return { newInitScore, newScore, newAnswer };
}

// 自由模式学生做题，获取整卷的初始得分和总分
function judgeQuestion(questions = [], allAnswer = {}, studentAnswer = {}, freeMode) {
  const judgeResult = studentAnswer || {};
  let studentScore = 0;
  let initialScore = 0;

  for (const question of questions) {
    const { score: bigScore, questions: subQuestions } = question;

    for (const subQuestion of subQuestions) {
      const { id, questionType, questionDetail: smallQuestions } = subQuestion;

      const result = getQuestionResult({ freeMode, questionID: id, questionType, questionDetail: smallQuestions, questionScore: bigScore, referAnswer: allAnswer[id], studentAnswer: studentAnswer[id] });
      const { newInitScore, newScore, newAnswer } = result;

      // 记录总分
      studentScore += newScore;
      // 初始得分
      initialScore += newInitScore;
      // 记录答案
      judgeResult[id] = newAnswer;
    }
  }

  return {
    judgeResult,
    studentScore,
    initialScore,
  }
}

/*
  自由模式单个题目评测
  @return: {
    noJudge: true, //表示此题为初次判分
    judgeResult: {
      // 此题判分结果
      id: {
        result,
        [results],
        answer,
        score,
        // 初次判分增加字段：
        initResult, // 初次判分结果
        [initResults], // 填空题、选择填空题、编程填空题填空项初次判分结果 
        initAnswer, // 初次作答答案
        initScore, // 初次作答分数
      }
    }
  }
*/
function judgeQuestionbyID({ question, UUID, blockScore, answer = {}, studentAnswer = {} }) {
  let judgeResult = {};

  const { id, questionType } = question;
  const { answer: rawAnswer, score: answerScore } = answer;

  // 记录学生答案
  judgeResult[id] = studentAnswer && studentAnswer[id] ? studentAnswer[id] : {};

  // 简单题初次判分
  let isFirstTimeJudge = false;
  if (questionType !== '综合题') {
    let noAnswer = !studentAnswer || !studentAnswer[id] || !studentAnswer[id].answer;
    isFirstTimeJudge = noAnswer || studentAnswer[id].initResult === undefined;

    if (noAnswer) {
      judgeResult[id] = { result: false, score: 0 };
      return { noJudge: isFirstTimeJudge, judgeResult };
    }
  }

  switch (questionType) {
    case '单选题':
      {
        const { answer: studentAnswerContent } = studentAnswer[id] || {};
        let result = getSingleChoiceResult(rawAnswer, studentAnswerContent);
        let newScore = result ? blockScore : 0;
        judgeResult[id] = { ...judgeResult[id], result, score: newScore, answer: studentAnswerContent };

        if (isFirstTimeJudge) {
          judgeResult[id] = {
            ...judgeResult[id],
            initResult: result,
            initAnswer: studentAnswerContent,
            initScore: newScore,
          }
        }
        break;
      }
    case '综合题':
      {
        let noAnswer = !studentAnswer || !studentAnswer[id] || !studentAnswer[id][UUID] || !studentAnswer[id][UUID].answer;

        if (noAnswer) {
          judgeResult[id][UUID] = {
            answer: null,
            result: false,
            score: 0,
          }
          return { noJudge: true, judgeResult };
        }

        // 初次判分
        isFirstTimeJudge = noAnswer || studentAnswer[id][UUID].initResult === undefined;

        const { questionDetail } = question;
        const smallQuestion = questionDetail.find(i => i.UUID === UUID);
        if (!smallQuestion) {
          throw new Error(`题目${id} ${UUID} 不存在`);
        }

        const { questionType } = smallQuestion;
        // 忽略文本
        if (questionType === '文本') {
          break;
        }

        // 获取小题答案
        const { answer: subAnswer, score: subScore } = answer[UUID];

        // 学生答案
        const { answer: studentAnswerContent } = studentAnswer[id][UUID];

        // 根据类型判决
        switch (questionType) {
          case '单选题':
            {
              let result = getSingleChoiceResult(subAnswer, studentAnswerContent);
              let newScore = result ? subScore : 0;
              judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, score: newScore, answer: studentAnswerContent };

              if (isFirstTimeJudge) {
                judgeResult[id][UUID] = {
                  ...judgeResult[id][UUID],
                  initResult: result,
                  initAnswer: studentAnswerContent,
                  initScore: newScore,
                }
              }
              break;
            }
          case '多选题':
            {
              let result = getMultipleChoiceAnswerResult(subAnswer, studentAnswerContent);
              let newScore = result ? subScore : 0;
              judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, score: newScore, answer: studentAnswerContent };

              if (isFirstTimeJudge) {
                judgeResult[id][UUID] = {
                  ...judgeResult[id][UUID],
                  initResult: result,
                  initAnswer: studentAnswerContent,
                  initScore: newScore,
                }
              }
              break;
            }
          case '选择填空题':
            {
              let { result, results, score } = getJumbledSentenceResult(subAnswer, studentAnswerContent, subScore);
              judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, results, score, answer: studentAnswerContent };


              if (isFirstTimeJudge) {
                judgeResult[id][UUID] = {
                  ...judgeResult[id][UUID],
                  initResult: result,
                  initResults: results,
                  initAnswer: studentAnswerContent,
                  initScore: score,
                }
              }
              break;
            }
          case '填空题':
            {
              let { result, results, score } = getBlanksResult(subAnswer, studentAnswerContent, subScore);
              judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, results, score, answer: studentAnswerContent };

              if (isFirstTimeJudge) {
                judgeResult[id][UUID] = {
                  ...judgeResult[id][UUID],
                  initResult: result,
                  initResults: results,
                  initAnswer: studentAnswerContent,
                  initScore: score,
                }
              }
              break;
            }
          default:
            throw new Error(`综合题判分，不支持的小题题型${questionType}`);
        }
      }
      break;
    case '填空题':
      {
        const { answer: studentAnswerContent } = studentAnswer[id] || {};
        let { result, results, score } = getBlanksResult(rawAnswer, studentAnswerContent, answerScore);
        judgeResult[id] = { ...judgeResult[id], result, results, score, answer: studentAnswerContent };

        if (isFirstTimeJudge) {
          judgeResult[id] = {
            ...judgeResult[id],
            initResult: result,
            initResults: results,
            initAnswer: studentAnswerContent,
            initScore: score,
          }
        }
      }
      break;
    case '编程填空题':
      {
        const { answer: studentAnswerContent } = studentAnswer[id] || {};
        let { result, results, score } = getCodeBlankResult(rawAnswer, studentAnswerContent, answerScore);
        judgeResult[id] = { ...judgeResult[id], result, results, score, answer: studentAnswerContent };

        if (isFirstTimeJudge) {
          judgeResult[id] = {
            ...judgeResult[id],
            initResult: result,
            initResults: results,
            initAnswer: studentAnswerContent,
            initScore: score,
          }
        }
      }
      break;
    case 'Access操作题':
    case 'WPS表格操作题':
      {
        const { answer: studentAnswerContent } = studentAnswer[id] || {};
        let { result, score } = getAccessResult(answer, studentAnswerContent);
        judgeResult[id] = { ...judgeResult[id], result, score, answer: studentAnswerContent };

        if (isFirstTimeJudge) {
          judgeResult[id] = {
            ...judgeResult[id],
            initResult: result,
            initAnswer: studentAnswerContent,
            initScore: score,
          }
        }
      }
      break;
    default:
      throw new Error(`按照ID判分，不支持的题型${questionType}`);
  }

  return { noJudge: isFirstTimeJudge, judgeResult };
}

// 交卷判分
function submitQuestion(questions = [], allAnswer = {}, studentAnswer = {}, freeMode, initialScore = 0) {
  const judgeResult = {};
  let studentScore = 0;

  /*
    initialScore // 初始得分：每答一题，记录此题得分，后续再答题时，如果是之前未答过的题，即没有result字段，记录在初始得分

    // 初始得分增加记录字段
    questionID: {
      initResult, // 初次判分结果
      initResults, // 填空题、选择填空题、编程填空题填空项初次判分结果 
      initAnswer, // 初次作答答案
      initScore, // 初次作答分数
    }
  */

  // 遍历大题
  for (const question of questions) {
    const { score: bigScore, questions: subQuestions } = question;

    for (const subQuestion of subQuestions) {
      const { id, questionType } = subQuestion;
      const { answer, score: answerScore } = allAnswer[id];
      // console.log(id, studentScore);
      // if(id === 3977) {
      //   debugger
      // }

      if (!judgeResult[id]) {
        judgeResult[id] = {};
      }

      if ((!studentAnswer || !studentAnswer[id])) {
        judgeResult[id] = {
          answer: null,
          result: false,
          score: 0,
        };

        if (freeMode) {
          judgeResult[id] = {
            ...judgeResult[id],
            initResult: false,
            initAnswer: null,
            initScore: 0,
          };
        }
        continue;
      }

      // 默认值，学生答案
      judgeResult[id] = {
        ...studentAnswer[id],
      };

      // 非单选题，修改评测状态
      if (questionType !== '单选题') {
        judgeResult[id].submitJudge = true;
      }

      const { answer: studentAnswerContent } = studentAnswer[id];

      // 简单题初次判分
      let noJudge = false;
      if (questionType !== '综合题') {
        noJudge = studentAnswer[id].initResult === undefined;
      }

      switch (questionType) {
        case '单选题':
          {
            let result = getSingleChoiceResult(answer, studentAnswerContent);
            let newScore = result ? bigScore : 0;

            judgeResult[id] = {
              ...judgeResult[id],
              answer: studentAnswerContent,
              result,
              score: newScore, // 单选题使用模板配置分数
            };

            studentScore += newScore;
            // 初始得分
            if (noJudge) {
              initialScore += newScore;

              // 自由模式下，记录题目分数
              if (freeMode) {
                judgeResult[id] = {
                  ...judgeResult[id],
                  initResult: result,
                  initAnswer: studentAnswerContent,
                  initScore: newScore, // 单选题使用模板配置分数
                };
              }
            }
          }
          break;
        case '多选题':
          {
            let result = getMultipleChoiceAnswerResult(answer, studentAnswerContent);
            let newScore = result ? bigScore : 0;

            judgeResult[id] = {
              ...judgeResult[id],
              answer: studentAnswerContent,
              result,
              score: newScore,
            };

            studentScore += newScore;
            // 初始得分
            if (noJudge) {
              initialScore += newScore;

              // 自由模式下，记录题目分数
              if (freeMode) {
                judgeResult[id] = {
                  ...judgeResult[id],
                  initResult: result,
                  initAnswer: studentAnswerContent,
                  initScore: newScore,
                };
              }
            }
          }
          break;
        case '综合题':
          {
            const { id, questionDetail: smallQuestions } = subQuestion;
            const answer = allAnswer[id]; // 参考答案

            if (!judgeResult[id]) {
              judgeResult[id] = {};
            }

            const stuAnswer = studentAnswer && studentAnswer[id] ? studentAnswer[id] : null;

            for (const smallQuestion of smallQuestions) {
              const { UUID, questionType } = smallQuestion;
              // 忽略文本
              if (questionType === '文本') {
                continue;
              }

              // 获取小题答案
              const { answer: rawAnswer, score: answerScore } = answer[UUID];

              if (!judgeResult[id][UUID]) {
                judgeResult[id][UUID] = {};
              }

              // 没有答案
              if (!stuAnswer || !stuAnswer[UUID] || !stuAnswer[UUID].answer) {
                judgeResult[id][UUID] = {
                  answer: null,
                  result: false,
                  score: 0,
                };

                if (freeMode) {
                  judgeResult[id][UUID] = {
                    ...judgeResult[id][UUID],
                    initResult: false,
                    initAnswer: null,
                    initScore: 0,
                  };
                }
                continue;
              }

              // 默认值为学生答案
              judgeResult[id][UUID] = {
                ...stuAnswer[UUID],
              };

              // 非单选题，修改评测状态
              if (stuAnswer[UUID].submitJudge !== undefined) {
                judgeResult[id][UUID].submitJudge = true;
              }

              const { answer: studentAnswerContent, initResult } = stuAnswer[UUID];

              // 初次判分
              noJudge = initResult === undefined;

              // 根据类型判决
              switch (questionType) {
                case '单选题':
                  {
                    let result = getSingleChoiceResult(rawAnswer, studentAnswerContent);
                    let newScore = result ? answerScore : 0;

                    judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, score: newScore };

                    studentScore += newScore;
                    // 初始得分
                    if (noJudge) {
                      initialScore += newScore;

                      // 自由模式下，记录题目分数
                      if (freeMode) {
                        judgeResult[id][UUID] = {
                          ...judgeResult[id][UUID],
                          initResult: result,
                          initAnswer: studentAnswerContent,
                          initScore: newScore,
                        };
                      }
                    }
                    break;
                  }
                case '多选题':
                  {
                    let result = getMultipleChoiceAnswerResult(rawAnswer, studentAnswerContent);

                    let newScore = result ? answerScore : 0;

                    judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, score: newScore };

                    studentScore += newScore;

                    // 初始得分
                    if (noJudge) {
                      initialScore += newScore;

                      // 自由模式下，记录题目分数
                      if (freeMode) {
                        judgeResult[id][UUID] = {
                          ...judgeResult[id][UUID],
                          initResult: result,
                          initAnswer: studentAnswerContent,
                          initScore: newScore,
                        };
                      }
                    }
                    break;
                  }
                case '选择填空题':
                  {
                    let { result, results, score } = getJumbledSentenceResult(rawAnswer, studentAnswerContent, answerScore);

                    judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, results, score };

                    studentScore += score;
                    if (noJudge) {
                      initialScore += score;

                      // 自由模式下，记录题目分数
                      if (freeMode) {
                        judgeResult[id][UUID] = {
                          ...judgeResult[id][UUID],
                          initResult: result,
                          initResults: results,
                          initAnswer: studentAnswerContent,
                          initScore: score,
                        };
                      }
                    }
                    break;
                  }
                case '填空题':
                  {

                    let { result, results, score } = getBlanksResult(rawAnswer, studentAnswerContent, answerScore);

                    judgeResult[id][UUID] = { ...judgeResult[id][UUID], result, results, score };

                    studentScore += score;
                    if (noJudge) {
                      initialScore += score;

                      // 自由模式下，记录题目分数
                      if (freeMode) {
                        judgeResult[id][UUID] = {
                          ...judgeResult[id][UUID],
                          initResult: result,
                          initResults: results,
                          initAnswer: studentAnswerContent,
                          initScore: score,
                        };
                      }
                    }
                    break;
                  }
                default:
                  throw new Error(`综合题，不支持的子类题型${questionType}`);
              }
            }
          }
          break;
        case '填空题':
          {
            const { answer: studentAnswerContent } = studentAnswer[id];
            let { result, results, score } = getBlanksResult(answer, studentAnswerContent, answerScore);
            judgeResult[id] = { ...judgeResult[id], result, results, score };

            studentScore += score;
            if (noJudge) {
              initialScore += score;

              // 自由模式下，记录题目分数
              if (freeMode) {
                judgeResult[id] = {
                  ...judgeResult[id],
                  initResult: result,
                  initResults: results,
                  initAnswer: studentAnswerContent,
                  initScore: score,
                };
              }
            }
          }
          break;
        case '编程填空题':
          {
            let { result, results, score } = getCodeBlankResult(answer, studentAnswerContent, answerScore);

            judgeResult[id] = { ...judgeResult[id], result, results, score };

            studentScore += score;
            if (noJudge) {
              initialScore += score;

              // 自由模式下，记录题目分数
              if (freeMode) {
                judgeResult[id] = {
                  ...judgeResult[id],
                  initResult: result,
                  initResults: results,
                  initAnswer: studentAnswerContent,
                  initScore: score,
                };
              }
            }
          }
          break;
        case 'Access操作题':
        case 'WPS表格操作题':
          {
            let { result, score } = getAccessResult(allAnswer[id], studentAnswerContent);

            judgeResult[id] = { ...judgeResult[id], result, score };

            studentScore += score;
            if (noJudge) {
              initialScore += score;

              // 自由模式下，记录题目分数
              if (freeMode) {
                judgeResult[id] = {
                  ...judgeResult[id],
                  initResult: result,
                  initAnswer: studentAnswerContent,
                  initScore: score,
                };
              }
            }
          }
          break;
        case '在线编程评测题':
          {
            let { result, results, score } = studentAnswer[id];

            judgeResult[id] = { ...judgeResult[id], result, results, score };

            studentScore += score;
            if (noJudge) {
              initialScore += score;

              // 自由模式下，记录题目分数
              if (freeMode) {
                judgeResult[id] = {
                  ...judgeResult[id],
                  initResult: result,
                  initAnswer: studentAnswerContent,
                  initScore: score,
                };
              }
            }
          }
          break;
        default:
          throw new Error(`提交判分，不支持的题型${questionType}`);
      }
    }
  }

  return {
    judgeResult,
    studentScore,
    initialScore,
  }
}

// 自由模式重做
function getNewFreeModeRecord({ studentAnswer, questionType, questionDetail, correctMode }) {
  let newRecord = {};
  let finalResult = true;

  if (questionType === '综合题') {
    if (studentAnswer) {
      newRecord = {
        ...studentAnswer,
      };
    }

    for (const item of questionDetail) {
      const { UUID, questionType: type } = item;
      if (type === '文本') {
        continue;
      }

      if (studentAnswer && studentAnswer[UUID]) {
        // 订正模式 correctMode(true)：保留正确答案，清空错误答案
        if (correctMode && studentAnswer[UUID].result) {
          newRecord[UUID] = {
            ...studentAnswer[UUID],
          };
          continue;
        }

        finalResult = false;

        newRecord[UUID] = {
          ...studentAnswer[UUID],
          score: 0,
          answer: null,
          result: undefined,
        }

        if (studentAnswer[UUID].submitJudge) {
          newRecord[UUID] = {
            ...newRecord[UUID],
            submitJudge: false,
          }
        }

        if (studentAnswer[UUID].results) {
          newRecord[UUID] = {
            ...newRecord[UUID],
            results: [],
          }
        }
      } else {
        finalResult = false;
        newRecord[UUID] = {
          score: 0,
          answer: null,
          result: undefined,
          initScore: 0,
          initAnswer: null,
          initResult: false,
        }
      }
    }
  } else {
    if (studentAnswer) {
      // 订正模式 correctMode(true)：保留正确答案，清空错误答案
      if (correctMode && studentAnswer.result) {
        newRecord = {
          ...studentAnswer,
        };

        return { newRecord, finalResult };
      }

      finalResult = false;

      newRecord = {
        ...studentAnswer,
        score: 0,
        answer: null,
        result: undefined,
      }

      if (studentAnswer.submitJudge) {
        newRecord = {
          ...newRecord,
          submitJudge: false,
        }
      }

      if (studentAnswer.results) {
        newRecord = {
          ...newRecord,
          results: [],
        }
      }
    } else {
      finalResult = false;
      newRecord = {
        score: 0,
        answer: null,
        result: undefined,
        initScore: 0,
        initAnswer: null,
        initResult: false,
      };
    }
  }

  return { newRecord, finalResult };
}

// 训练模式重做 订正模式
function getNewTrainModeRecord({ studentAnswer, questionType, questionDetail }) {
  let newRecord = {};
  let finalResult = true;

  if (questionType === '综合题') {
    if (studentAnswer) {
      newRecord = {
        ...studentAnswer,
      };
    }

    for (const item of questionDetail) {
      const { UUID, questionType: type } = item;
      if (type === '文本') {
        continue;
      }

      // 保留正确答案
      if (studentAnswer && studentAnswer[UUID] && studentAnswer[UUID].result) {
        newRecord[UUID] = {
          ...studentAnswer[UUID],
        }
      } else {
        finalResult = false;
        newRecord[UUID] = {
          score: 0,
          answer: null,
          result: undefined,
        }
      }
    }
  } else {
    // 保留正确答案
    if (studentAnswer && studentAnswer.result) {
      newRecord = {
        ...studentAnswer,
      }
    } else {
      finalResult = false;
      newRecord = {
        score: 0,
        answer: null,
        result: undefined,
      };
    }
  }

  return { newRecord, finalResult };
}

exports.judgeQuestion = judgeQuestion;
exports.judgeQuestionbyID = judgeQuestionbyID;
exports.submitQuestion = submitQuestion;

exports.getNewFreeModeRecord = getNewFreeModeRecord;
exports.getNewTrainModeRecord = getNewTrainModeRecord;
