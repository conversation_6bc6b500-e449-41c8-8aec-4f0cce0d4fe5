const microTypeNameMap = {
    'text' : {
      type: '文本框',
      extName: 'txt',
      binary: false,
    },
    'table' : {
      type: '简易表格',
      extName: 'csv',
      binary: false,
    },
    'flow': {
      type: '流程图填空',
      extName: 'json',
      binary: false,
    },
    'mind': {
      type: '思维导图',
      extName: 'json',
      binary: false,
    },
    'spreadsheet': {
      type: '工作表',
      extName: 'json',
      binary: false,
    },
    'networksimulator': {
      type: '网络模拟器',
      extName: 'json',
      binary: false,
    },
    'drawio': {
      type: 'DrawIO图表',
      extName: 'json',
      binary: false,
    },
}


// BASE 64格式PNG头部
exports.pngBase64Header = 'data:image/png;base64,';
exports.microTypeNameMap = microTypeNameMap;