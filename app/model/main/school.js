'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, JSON } = app.Sequelize;

  const School = model.define('school', {
    // 学校信息 同一个地区不允许两所学校重名
    fullName: { type: STRING(64), comment: '学校全称', allowNull: false},
    logo: { type: STRING(255), comment: 'LOGO', allowNull: false},
    slug: { type: STRING(16), comment: '域名前缀', unique: true },
    administrationID: { type: INTEGER, comment: '地区ID', allowNull: false },
    computers: { type: INTEGER, comment: '机房计算机总数', allowNull: false },
    // schoolType: { type: STRING(16), comment: "阶段：小学/初中/高中/职校/大学", allowNull: false },
    // tracks: { type: INTEGER, comment: '轨数：一个年级几个班级', allowNull: false },
    // 联系人信息
    registerName:  { type: STRING(64), comment: '注册教师姓名', allowNull: false},
    registerEmail: { type: STRING(64), comment: '注册教师邮箱', allowNull: false},
    registerCellphone: { type: STRING(64), comment: '注册教师手机', allowNull: false},
    registerPassword: { type: STRING(64), comment: '注册教师密码', allowNull: false},
    registerCertifcate: { type: STRING(255), comment: '注册教师身份认证材料', allowNull: false},
    // 系统配置
    dbConfig: { type: JSON, comment: '数据库配置', allowNull: false },

    onlineNum: { type: INTEGER, comment: '在线人数', allowNull: true },
    registrationNum: { type: INTEGER, comment: '注册人数', allowNull: true },
    classNum: { type: INTEGER, comment: '班级数', allowNull: true },
    courseNum: { type: INTEGER, comment: '课程数', allowNull: true },

    auditStatus: { type: STRING(64), comment: '审核状态', allowNull: true },
    auditReason: { type: STRING(64), comment: '审核理由', allowNull: true },
    ifService: { type: INTEGER, comment: '是否正在使用', allowNull: true },
  }, {
    comment: '学校',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { unique: true, fields: ['fullName', 'administrationID', 'deleted_at'] }, // 没有删掉的课程不能同一个标识符
    ]
  });

  return School;
}; 
