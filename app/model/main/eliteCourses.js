'use strict';

module.exports = (app, model) => {
  const { STRING, TEXT, JSON } = app.Sequelize;

  const EliteCourses = model.define('eliteCourses', {
    schoolSlug: { type: STRING(16), comment: '学校标识', allowNull: false },
    courseSlug: { type: STRING(16), comment: '课程标识', allowNull: false },
    courseInfo: { type: JSON, comment: '课程名称、简介、用户名、用户头像、选修/必修', allowNull: false },
    indics: { type: JSON, comment: '章节目录缓存' }, // 包含了章（名称、顺序、目录名）、节（名称、顺序、文件名、SectionID）
    publishInfo: { type: TEXT('long'), comment: "课程发布说明", allowNull: true },
    status: { type: STRING(64), comment: '状态：是否发布', allowNull: false },
  }, {
    comment: '精品课程',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
  });

  return EliteCourses;
}; 
