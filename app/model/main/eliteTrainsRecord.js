'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, TEXT, DATE, JSON } = app.Sequelize;

  const EliteTrainsRecord = model.define('eliteTrainsRecord', {
    importSchoolSlug: { type: STRING(16), comment: '导入人学校slug' },
    importDisplayName: { type: STRING(32), comment: '导入人昵称' },
    importUserName: { type: STRING(32), comment: '导入人用户名' },
    importTrainID: { type: INTEGER, comment: '导入精品试卷id' }
  }, {
    comment: '精品试卷导入记录',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
  });

  return EliteTrainsRecord;
}; 
