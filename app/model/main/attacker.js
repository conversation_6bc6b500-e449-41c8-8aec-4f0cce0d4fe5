'use strict';

module.exports = (app, model) => {
  const { STRING } = app.Sequelize;

  // 攻击者库
  const Attacker = model.define('attacker', {
    ipAddress: {type: STRING(16), comment: 'IP地址'},
    cellphone: {type: STRING(16), comment: '手机（登录账号）'},
    password: {type: STRING(64), comment: '密码Hash值'},
  }, {
    comment: '攻击者库',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [{
        fields: ['ipAddress', "created_at"]
    }]
  });

  return Attacker;
}; 
