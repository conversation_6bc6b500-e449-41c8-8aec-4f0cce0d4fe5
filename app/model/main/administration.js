'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, DOUBLE } = app.Sequelize;

  const Administration = model.define('administration', {
    name: {type: STRING(255), comment: '名称', allowNull: false, unique: false},
    // longitude: {type: DOUBLE, comment: '经度', allowNull: false},
    // latitude: {type: DOUBLE, comment: '纬度', allowNull: false},
    parentID: {type: INTEGER, comment: '父级节点ID', allowNull: false},
    level: {type: STRING(16), comment: '等级', allowNull: false},
  }, {
    comment: '行政区划',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  Administration.associate = () => {
    const { School } = model;

    Administration.hasMany(School, { as: 'schools', foreignKey: 'administrationID', constraints: false });
  }

  return Administration;
}; 
