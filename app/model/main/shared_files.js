'use strict';

module.exports = (app, model) => {
  const { INTEGER, BIGINT, STRING, TEXT } = app.Sequelize;
  // 共享文件，使用Hash技术加速文件传输和减少冗余文件存储空间
  const SharedFiles = model.define('shared_files', {
      hash: { primaryKey: true,  type: STRING(255), comment: '散列值'}, 
      filename: { type: TEXT, comment: '首次上传时的文件名'},
      ext: { type: STRING(128), comment: '扩展名'},
      year: { type: INTEGER, comment: '首次上传时的年份'},
      month: { type: INTEGER, comment: '首次上传时的月份'},
      day: { type: INTEGER, comment: '首次上传时的日期'},
      file_size: { type: BIGINT, comment: '文件字节数' },
      ref_count: { type: BIGINT, comment: '文件的引用计数'},
      schoolSlug: { type: STRING(32), comment: '首次存储的学校slug'},
  }, {
    comment: 'shared_files',
    paranoid: true,
    freezeTableName: true,
    underscored: false,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  return SharedFiles;
}; 