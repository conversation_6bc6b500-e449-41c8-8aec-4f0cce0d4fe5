'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, DATE, JSON } = app.Sequelize;

  const DeploymentPermission = model.define('deployment_permission', {
    schoolID: { type: INTEGER, comment: '学校ID', allowNull: true },
    startTime: { type: DATE(6), comment: '服务开始时间' },
    endTime: { type: DATE(6), comment: '服务结束时间' },
    permission: { type: JSON, comment: '功能配置' },
    trainPermissionStatusInfo: { type: JSO<PERSON>, comment: '机房占用权限情况' },
    trainPeopleUsed: { type: JSO<PERSON>, comment: '在线训练占用权限情况' },
    coursePeopleUsed: { type: JSON, comment: '课程占用权限情况' },
  }, {
    comment: '部署',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  DeploymentPermission.associate = () => {
    // const model = app.model;
    // const { School } = model;
    // DeploymentPermission.belongsTo(School, { as: 'school', foreignKey: 'schoolID' });
  }

  return DeploymentPermission;
}; 
