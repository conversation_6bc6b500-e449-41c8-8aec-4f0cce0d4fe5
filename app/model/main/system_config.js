'use strict';

module.exports = (app, model) => {
  const { STRING, JSON} = app.Sequelize;
  // 全局系统配置
  const SystemConfig = model.define('system_configs', {
    // 自定义配置名称
    key: {type: STRING(128), unique: true, comment: '自定义配置名称'},
    // 自定义配置内容
    value: {type: JSON, comment: '自定义配置内容'},
  }, {
    comment: '系统配置',
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return SystemConfig
};  