'use strict';

module.exports = (app, model) => {
  const { STRING } = app.Sequelize;

  const SchoolIdMap = model.define('schoolIdMap', {
    schoolSlug: { type: STRING(16), comment: '域名前缀', allowNull: false, unique: false },
    SchoolId: { type: STRING(64), comment: '昆山学校ID', allowNull: false, unique: true },
  }, {
    comment: '学校',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  SchoolIdMap.associate = () => {
    const { School } = model;
    SchoolIdMap.belongsTo(School, { as: 'school', sourceKey: 'slug', foreignKey: 'schoolSlug', constraints: false  });
  }

  return SchoolIdMap;
}; 
