'use strict';

module.exports = (app, model) => {
  const { STRING } = app.Sequelize;

  // 弱密码库
  const WeakPassword = model.define('weakPassword', {
    rawPassword: {type: STRING(64), comment: '密码'},
    password: {type: STRING(64), comment: '密码Hash值'},
  }, {
    comment: '弱密码库',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [{
        fields: ['password']
    }]
  });

  return WeakPassword;
}; 
