'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, TEXT, DATE, JSON } = app.Sequelize;

  const EliteTrains = model.define('eliteTrains', {
    publishSchoolSlug: { type: STRING(16), comment: '发布人学校slug' },
    publishDisplayName: { type: STRING(32), comment: '发布人昵称' },
    publishUserName: { type: STRING(32), comment: '发布人用户名' },

    year: { type: STRING(16), comment: '学年' },

    seriesName: { type: STRING(64), comment: '系列名称' },

    trainName: { type: STRING(64), comment: '训练名称' },
    trainContent: { type: JSO<PERSON>, comment: '试卷内容' },

    trainTemplateName: { type: JSON, comment: '训练模版名称' },
    trainTemplate: { type: JSON, comment: '训练模版' },
    templateDifficulty: { type: <PERSON><PERSON><PERSON>, comment: '模板难度配置' },

    publishTime: { type: DATE, comment: '创建时间' },
    updateTime: { type: DATE, comment: '修改时间' },
    note: { type: TEXT, comment: '备注' },
  }, {
    comment: '精品试卷',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
  });

  return EliteTrains;
}; 
