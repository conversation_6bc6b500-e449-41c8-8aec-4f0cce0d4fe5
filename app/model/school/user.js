'use strict';

module.exports = (app, model) => {
  const { STRING, JSON, DATE } = app.Sequelize;

  // 用户信息
  const User = model.define('user', {
    username: {type: STRING(32), comment: '账号'},
    password: {type: STRING(64), comment: '密码'},
    name: {type: STRING(64), comment: '姓名'},
    sen: { type: STRING(64), comment: '学籍号' }, // 非必填
    avatar: {type: STRING(128), comment: '头像'},
    state: {type: STRING(32), comment: '状态'},
    adminAuthority: {type: JSON, comment: '管理权限定义'}, // NULL代表非管理员
    school: {type: STRING(64), comment: '学校'},
    ssoName: {type: STRING(128), comment: '单点登录用户名'},
    sso: {type: JSON, comment: '单点登录用户信息'},
    // 统计字段
    lastActiveTime: {type: DATE(6), comment: '最后活跃时间'},
    // 微信
    openID: {type: STRING(32), comment: 'openID'},
    wechatInfo: {type: JSON, comment: '微信用户信息'},
  }, {
    comment: '用户',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { unique: true, fields: ['username', 'deleted_at'] },  // 大概率是学号，不允许重复
      { unique: true, fields: ['sen', 'deleted_at'] }, // 学籍号，不允许重复
    ]
  });

  User.associate = () => {
    const { User, Team, TeamUser, TrainUserRecord, TrainThroughTrainPlan, TrainSeries } = model;
    User.belongsToMany(Team, {as: 'team', through: 'team_user', foreignKey: 'userID', otherKey: 'teamID', constraints: false });  
    User.hasMany(TeamUser, {as: 'teamUser', sourceKey: 'id', foreignKey: 'userID', constraints: false });
    User.hasMany(TrainUserRecord, {as: 'trainUserRecord', sourceKey: 'id', foreignKey: 'userID', constraints: false });
    User.hasMany(TrainThroughTrainPlan, {as: 'trainThroughTrainPlan', sourceKey: 'id', foreignKey: 'userID', constraints: false });
    User.hasMany(TrainSeries, {as: 'trainSeries', sourceKey: 'id', foreignKey: 'createUserID', constraints: false });
  }
  return User
}; 