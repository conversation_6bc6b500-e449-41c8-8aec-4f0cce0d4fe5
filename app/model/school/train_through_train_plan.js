'use strict';

module.exports = (app, model) => {
  const { BOOLEAN, INTEGER } = app.Sequelize;
  // 训练用户计划关联表
  const TrainThroughTrainPlan = model.define('train_through_train_plan', {
    id: {type: INTEGER, primaryKey: true, autoIncrement: true, comment: 'id'},
    userID: {type: INTEGER, comment: '用户ID'},
    trainID: {type: INTEGER, comment: '训练ID'},
    planID: {type: INTEGER, comment: '计划ID'},
    isolate: { type: BOOLEAN, comment: '单独设置，不参与计划统计' }
  }, {
    comment: '训练用户计划关联表',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    // indexes: [
    //   { name: 'train_question_tag_questionID_tagID_unique', unique: true, fields: ['questionID', 'tagID', 'deleted_at'] }, // 同一所学校禁止出现两个同名班级
    // ]
  });

  TrainThroughTrainPlan.associate = () => {
    const { Train, TrainPlan, User } = model;
    TrainThroughTrainPlan.belongsTo(User, {as: 'user', foreignKey: 'userID'});
    TrainThroughTrainPlan.belongsTo(Train, {as: 'train', foreignKey: 'trainID'});
    TrainThroughTrainPlan.belongsTo(TrainPlan, {as: 'trainPlan', foreignKey: 'planID'});
  }

  return TrainThroughTrainPlan;
}; 
