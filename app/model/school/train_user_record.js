'use strict';

module.exports = (app, model) => {
  const { INTEGER, JSON, STRING, DATE, DECIMAL, BOOLEAN } = app.Sequelize;

  // 节记录
  const TrainUserRecord = model.define('train_user_record', {
    userID: {type: INTEGER, comment: '用户ID'},
    planID: {type: INTEGER, comment: '训练计划ID'},
    trainID: {type: INTEGER, comment: '训练ID'},
    record: { type: JSON, comment: '训练记录' },
    score: { type: DECIMAL(10, 2), comment: '实际得分', defaultValue: 0 },
    initialScore: { type: DECIMAL(10, 2), comment: '初始得分', defaultValue: 0 },
    status: {type: STRING(32), comment: '状态'},
    correctMode: {type: BOOLEAN, comment: '订正模式'},
    correctQuestionIDs: { type: JSON, comment: '答对题目ID' },
    submitTimes: {type: INTEGER, comment: '提交次数'},
    submitTime: {type: DATE, comment: '结束时间'},
  }, {
    comment: '训练记录',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });
  TrainUserRecord.associate = () => {
    const { TrainUserRecord, TrainPlan, User } = model; 
    TrainUserRecord.belongsTo(TrainPlan, {as: 'trainPlan', foreignKey: 'planID', constraints: false });
    TrainUserRecord.hasMany(User, {as: 'user', sourceKey: 'userID', foreignKey: 'id', constraints: false });
  }
  return TrainUserRecord
}; 