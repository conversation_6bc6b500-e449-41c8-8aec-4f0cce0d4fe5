// 试卷系列表，包括名称、优先级、创建时间、更新时间
module.exports = (app, model) => {
    const { STRING, INTEGER, JSON } = app.Sequelize;
    
    // 试卷系列
    const TrainSeries = model.define('train_series', {
        id: { type: INTEGER, primaryKey: true, autoIncrement: true },
        name: { type: STRING(128), comment: '系列名称' },
        createUserID: { type: INTEGER, comment: '创建人ID' },
        teachers: { type: JSON, comment: '分享教师ID数组' },
        priority: { type: INTEGER, comment: '优先级' },
        trainIDs: { type: JSON, comment: '试卷ID列表' },
    }, {
      comment: '试卷系列',
      paranoid: true,
      underscored: false,
      freezeTableName: true, // Model tableName will be the same as the model name
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      deletedAt: 'deleted_at',
    });
  
    TrainSeries.associate = () => {
        const { User } = model;
        // 系列表与用户表是一对多的关系
        TrainSeries.belongsTo(User, {as: 'createUser', foreignKey: 'createUserID'});

        // 与试卷是通过JSON字段关联的，没法建立关系
    }
  
    return TrainSeries;
  };