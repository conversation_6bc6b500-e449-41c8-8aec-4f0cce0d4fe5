'use strict';

module.exports = (app, model) => {
  const { INTEGER } = app.Sequelize;

  // 训练
  const TrainUpdateRecord = model.define('train_update_record', {
    trainID: {type: INTEGER, comment: '试卷ID'},
    deleteQuestionID: {type: INTEGER, comment: '删除的题目ID'},
    addQuestionID: {type: INTEGER, comment: '增加的题目ID'},
  }, {
    comment: '试卷去重记录',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  TrainUpdateRecord.associate = () => {
    const { Train, TrainUpdateRecord } = model; 
    TrainUpdateRecord.belongsTo(Train, {as: 'trainUpdateRecord', foreignKey: 'trainID' });
  }

  return TrainUpdateRecord
};