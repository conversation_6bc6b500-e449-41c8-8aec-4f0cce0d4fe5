'use strict';

module.exports = (app, model) => {
  const { STRING, DECIMAL, INTEGER, DATE, JSON } = app.Sequelize;

  // 训练计划
  const TrainPlan = model.define('train_plan', {
    name: {type: STRING(32), comment: '名称'},
    trainID: {type: INTEGER, comment: '训练ID'},
    score: {type: INTEGER, comment: '总分'},
    duration: {type: INTEGER, comment: '时长'},
    minTrainDuration: {type: INTEGER, comment: '最短训练时长'},
    startTime: {type: DATE, comment: '开始时间'},
    endTime: {type: DATE, comment: '结束时间'},
    environment: {type: STRING(32), comment: '训练环境'},
    code: {type: STRING(32), comment: '训练码'},
    year: {type: STRING(32), comment: '学年'},
    openForAllClass: {type: INTEGER, comment: '向所有班级开放'},
    openClasses: { type: JSO<PERSON>, comment: '开放班级ID' },
    status: {type: STRING(32), comment: '状态'},
    discriminative: {type: JSON, comment: '区分度'},
    enrolledCount: {type: INTEGER, comment: '报名人数'},
    submitCount: {type: INTEGER, comment: '交卷人数'},
    average: {type: DECIMAL(10, 2), comment: '平均分'},
    variance: {type: DECIMAL(10, 2), comment: '方差'},
    statisticsData: {type: app.Sequelize.JSON, comment: '统计信息'},
    abstract: {type: STRING(255), comment: '训练简介/备注'},
    mode: { type: STRING(32), comment: '模式' },

    ifShowScore: {type: INTEGER, comment: '是否展示训练成绩'},
    ifShowCorrectionResults: {type: INTEGER, comment: '是否展示试题批改结果'},
    ifShowWrongAnswer: {type: INTEGER, comment: '是否展示错题答案'},
    ifSetWrongProblemCollection: {type: INTEGER, comment: '是否允许收录⾄错题集'},
    ifRandomOrder: {type: INTEGER, comment: '是否随机顺序'},
    ifForbidViewPaper: {type: INTEGER, comment: '是否禁⽌查看试卷'},

    createUserID: {type: INTEGER, comment: '创建人'},
    teachers: { type: JSON, comment: '分享教师' },
  }, {
    comment: '训练计划',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });
  TrainPlan.associate = () => {
    const { TrainPlan, Train, TrainPlanClass, TrainUserRecord, TrainThroughTrainPlan, User } = model;
    // TrainPlan.belongsTo(Train, {as: 'train',  foreignKey: 'trainID' });
    // TrainPlan.belongsToMany(Train, {as: 'trains', through: 'train_through_train_plan', foreignKey: 'questionID', otherKey: 'tagID'});
    TrainPlan.hasMany(TrainThroughTrainPlan, {as: 'trainThroughTrainPlan',  foreignKey: 'planID' });
    // TrainPlan.hasMany(TrainPlanClass, {as: 'trainPlanClass',  foreignKey: 'planID' });
    TrainPlan.hasMany(TrainUserRecord, {as: 'trainUserRecord',  foreignKey: 'planID' });
    TrainPlan.belongsTo(User, {as: 'createUser', foreignKey: 'createUserID' });
  }

  return TrainPlan
};