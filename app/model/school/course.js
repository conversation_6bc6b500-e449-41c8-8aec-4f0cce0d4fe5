'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, TEXT, JSON } = app.Sequelize;

  // 学校课程
  const Course = model.define('course', {
    courseName: {type: STRING(128), comment: '课程名称'},
    courseSlug: { type: STRING(32), comment: '课程标识' },
    courseDescription: { type: TEXT, comment: '课程描述' },
    publish: { type: INTEGER, comment: '是否发布'},
    statist: { type: JSON, comment: '统计信息' },
    indics: { type: JSON, comment: '章节目录缓存' }, // 包含了章（名称、顺序、目录名）、节（名称、顺序、文件名、SectionID）
    containerInfo: { type: JSON, comment: '容器配置' },
    createrID: { type: INTEGER, comment: '创建者'},
    courseType: { type: STRING(32), comment: '选修课/必修课'},
    saveCode: { type: INTEGER, comment: '是否保存代码'},
    saveRunResult: { type: INTEGER, comment: '是否保存运行结果'},
    allowPaste: { type: INTEGER, comment: '是否允许粘贴'},
    allowCopy: { type: INTEGER, comment: '是否允许复制'},
    programLanguage: { type: STRING(16), comment: '语言配置', defaultValue:'Python'},
    questionAnswer: { type: INTEGER, comment: '是否可以查看题解'},
    teams: { type: JSON, comment: '开放班级' },
    historyTeams: { type: JSON, comment: '历史班级' },
    teachers: { type: JSON, comment: '开放老师' },

    // 统计字段
    // readCount: { type: INTEGER, comment: '浏览次数' },
    uploadCount: { type: INTEGER, comment: '上传次数', defaultValue: 0 },
    downloadCount: { type: INTEGER, comment: '下载次数', defaultValue: 0 },
  }, {
    comment: '学校课程',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { unique: true, fields: ['courseSlug', 'deleted_at'] }, // 没有删掉的课程不能同一个标识符
    ]
  });
  
  // 增加学校标记
  Course.schoolSlug = model.schoolSlug;

  Course.associate = () => {
    const { Course, User } = model; 
    Course.belongsTo(User, {as: 'creater', foreignKey: 'createrID', constraints: false });
  }

  return Course
}; 