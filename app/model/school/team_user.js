'use strict';

module.exports = (app, model) => {
  const { INTEGER, STRING } = app.Sequelize;

  // 队伍用户关联表
  const TeamUser = model.define('team_user', {
    id: {type: INTEGER, primaryKey: true, autoIncrement: true, comment: 'id'},
    teamID: {type: INTEGER, comment: '队伍ID'},
    userID: {type: INTEGER, comment: '用户ID'},
  }, {
    comment: '队伍用户关联表',
    paranoid: true,
    freezeTableName: true,
    underscored: false,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { unique: true, fields: ['teamID', 'userID', 'deleted_at'] },
    ]
  });
  
  // 增加学校标记
  TeamUser.schoolSlug = model.schoolSlug;

  TeamUser.associate = () => {
    const { TeamUser, Team, User } = model; 
    TeamUser.belongsTo(User, {as: 'User', foreignKey: 'userID', constraints: false });
    TeamUser.belongsTo(Team, {as: 'Team', foreignKey: 'teamID', constraints: false });
  }

  return TeamUser;
}; 