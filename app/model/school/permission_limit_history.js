'use strict';

module.exports = (app, model) => {
  const { STRING, JSON, INTEGER, DATE } = app.Sequelize;
  
  // 人数限定历史：班级/备课组等
  const PermissionLimitHistory = model.define('permission_limit_history', {
    type: {type: STRING(32), comment: '分类'},
    people: {type: INTEGER, comment: '限制用户数'},
    hasUsed: { type: JSON, comment: '占用用户' },
    startTime: {type: DATE, comment: '开始时间'},
    endTime: {type: DATE, comment: '结束时间'},
    historyClass: { type: JSO<PERSON>, comment: '历史班级分布' },
    note: { type: JSON, comment: '其他信息' },
  }, {
    comment: '人数限定历史',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    // indexes: [
    //   { name: 'team_name_year_type_deleted_at', unique: true, fields: ['name', 'year', 'type', 'deleted_at'] }, // 同一所学校禁止出现两个同名班级
    // ]
  });

  PermissionLimitHistory.associate = () => {
    // const { TeamUser, Team } = model;
    // Team.hasMany(TeamUser, {as: 'teamUser', foreignKey: 'teamID', constraints: false });
  }

  return PermissionLimitHistory
}; 