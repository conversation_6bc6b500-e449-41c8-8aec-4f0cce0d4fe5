'use strict';

module.exports = (app, model) => {
  const { STRING } = app.Sequelize;
  
  // 队伍：班级/备课组等
  const Team = model.define('team', {
    name: {type: STRING(128), comment: '队伍名'},
    type: { type: STRING(128), comment: '类型：班级/备课组'},
    year: { type: STRING(32), comment: '学年'},
    classCode: { type: STRING(256), comment: '统一身份认证班级id'},
    gradeName: { type: STRING(128), comment: '统一身份认证年级'},
    className: { type: STRING(128), comment: '统一身份认证班级'},
  }, {
    comment: '队伍',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { name: 'team_name_year_type_deleted_at', unique: true, fields: ['name', 'year', 'type', 'deleted_at'] }, // 同一所学校禁止出现两个同名班级
    ]
  });

  Team.associate = () => {
    const { TeamUser, Team } = model;
    Team.hasMany(TeamUser, {as: 'teamUser', foreignKey: 'teamID', constraints: false });
  }

  return Team
}; 