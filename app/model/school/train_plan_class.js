'use strict';

module.exports = (app, model) => {
  const { INTEGER, DECIMAL } = app.Sequelize;

  // 训练参与班级
  const TrainPlanClass = model.define('train_plan_class', {
    teamID: {type: INTEGER, comment: '用户ID'},
    planID: {type: INTEGER, comment: '计划ID'},
    trainID: {type: INTEGER, comment: '训练ID'},
    enrolledCount: {type: INTEGER, comment: '报名人数'},
    submitCount: {type: INTEGER, comment: '交卷人数'},
    average: {type: DECIMAL(10, 2), comment: '平均分'},
    variance: {type: DECIMAL(10, 2), comment: '方差'},
  }, {
    comment: '训练参与班级',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });
  TrainPlanClass.associate = () => {
    const { TrainPlanClass, Team, TrainPlan, Train } = model; 
    TrainPlanClass.belongsTo(Team, {as: 'team', foreignKey: 'teamID' });
    TrainPlanClass.belongsTo(TrainPlan, {as: 'trainPlan', foreignKey: 'planID' });
    TrainPlanClass.belongsTo(Train, {as: 'train', foreignKey: 'trainID' });
  }
  return TrainPlanClass
};