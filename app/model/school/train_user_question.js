'use strict';

module.exports = (app, model) => {
  const { STRING, DECIMAL, INTEGER, TEXT } = app.Sequelize;

  // 错题题库
  const TrainUserQuestion = model.define('train_user_question', {
    questionID: {type: INTEGER, comment: '题目ID'},
    userID: {type: INTEGER, comment: '用户ID'},
    year: {type: STRING(32), comment: '学年'},
    trainPlanName: {type: STRING(64), comment: '来源训练名称'},
  }, {
    comment: '错题题库',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });
  TrainUserQuestion.associate = () => {
    const { TrainUserQuestion, Questions, User } = model; 
    TrainUserQuestion.belongsTo(Questions, {as: 'question',  foreignKey: 'questionID' });
    TrainUserQuestion.belongsTo(User, {as: 'user', foreignKey: 'userID' });
  }

  return TrainUserQuestion
};