'use strict';

module.exports = (app, model) => {
  const { INTEGER, STRING, JSON } = app.Sequelize;

  // 课节名称ID速查表
  const Section = model.define('section', {
    courseID: { type: INTEGER, comment: '课程ID'},
    chapterName: {type: STRING(128), comment: '章/单元名称'}, // 注意不包含序号
    sectionName: {type: STRING(128), comment: '节名称'}, // 注意不包含序号
    sectionType: {type: STRING(32), comment: '节类型 OJ/AI'},
    ext: {type: STRING(32), comment: '后缀名 .xml/.ipynb'},
    record: { type: JSON, comment: '提交记录' },
    historyRecords: { type: JSON, comment: '文件历史版本信息记录' },
  }, {
    comment: '课节名称ID速查表',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { unique: true, fields: ['courseID', 'chapterName', 'sectionName', 'deleted_at'] }, // 同一章下的各节不能同名
    ]
  });

  Section.associate = () => {
    const { Course, Section, SectionRecord } = model; 
    Section.belongsTo(Course, {as: 'course', foreignKey: 'courseID', constraints: false });
    Section.hasMany(SectionRecord, {as: 'sectionForeign',foreignKey: 'sectionID', constraints: false });
  }

  return Section;
}; 