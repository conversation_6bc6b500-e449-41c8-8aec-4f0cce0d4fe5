'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER, DECIMAL, JSON } = app.Sequelize;
  // 题库
  const Questions = model.define('train_questions', {
    title: {type: STRING(128), comment: '标题'},
    createUserID: {type: INTEGER, comment: '创建人'},
    questionType: {type: STRING(16), comment:'题目类型'},
    questionDetail: {type: JSON, comment:'题目详情'},
    answer: {type: JSON, comment:'题目答案'},
    difficultyConfig: {type: STRING(16), comment: '难度设置'},
    difficulty: {type: DECIMAL(10, 2), comment: '难度'},

    trainsUsed: {type: JSO<PERSON>, comment: '引用'},
    answerCount: {type: INTEGER, comment: '全部答题人数'}, // 答题人数去除record为空的人数
    passCount: {type: INTEGER, comment: '通过人数'},
    // source: {type: INTEGER, comment: '来源'}, // 已废弃字段，目前用试卷系列代替
    status: {type: STRING(16), comment: '状态'},
    author: {type: STRING(64), comment: '作者'},
    questionBankID: {type: INTEGER, comment: '题库ID'},
    originalID: {type: INTEGER, comment: '远程题库对应ID'},
  }, {
    comment: '题库',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
  });

  Questions.associate = () => {
    const { Questions, Tag, User, TrainQuestionBank } = model;
    Questions.belongsToMany(Tag, {as: 'tags', through: 'train_question_tag', foreignKey: 'questionID', otherKey: 'tagID'});
    Questions.belongsTo(User, {as: 'createUser', foreignKey: 'createUserID' });
    Questions.belongsTo(TrainQuestionBank, {as: 'trainQuestionBank', foreignKey: 'questionBankID' });
  } 

  return Questions;
};
