'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER } = app.Sequelize;
  // 题目标签信息
  const QuestionTag = model.define('train_question_tag', {
    id: {type: INTEGER, primaryKey: true, autoIncrement: true, comment: 'id'},
    questionID: {type: INTEGER, comment: '题目ID'},
    tagID: {type: INTEGER, comment: '标签ID'}
  }, {
    comment: '题目标签',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { name: 'train_question_tag_questionID_tagID_unique', unique: true, fields: ['questionID', 'tagID', 'deleted_at'] }, // 同一所学校禁止出现两个同名班级
    ]
  });

  QuestionTag.associate = () => {
    const { Questions, Tag } = model;
    QuestionTag.belongsTo(Questions, {as: 'questions', foreignKey: 'questionID'});
    QuestionTag.belongsTo(Tag, {as: 'tag', foreignKey: 'tagID'});
  }

  return QuestionTag;
}; 
