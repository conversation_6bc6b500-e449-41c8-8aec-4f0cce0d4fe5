'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER } = app.Sequelize;
  // 标签信息
  const Tag = model.define('train_tag', {
    tagName: {type: STRING(128), comment: '标签名'},
    tagType: {type: STRING(64), comment: '标签类型'},
    parentID: {type: INTEGER, comment: '父级节点ID'},
    questionBankID: {type: INTEGER, comment: '题库ID'},
    originalID: {type: INTEGER, comment: '远程题库对应ID'},
  }, {
    comment: '标签',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { unique: true, fields: ['tagName', 'tagType', 'deleted_at'] },
    ]
  });
  
  Tag.associate = () => {
    const { Tag, TrainQuestionBank } = model;
    Tag.belongsTo(TrainQuestionBank, {as: 'trainQuestionBank', foreignKey: 'questionBankID' });
  } 

  return Tag
}; 