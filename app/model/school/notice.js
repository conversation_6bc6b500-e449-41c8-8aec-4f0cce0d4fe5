'use strict';

module.exports = (app, model) => {
  const { STRING, TEXT } = app.Sequelize;

  // 学校公告
  const Notice = model.define('notice', {
    title: {type: STRING(64), comment: '公告标题'},
    preface: {type: STRING(255), comment: '封面图片地址'},
    content: {type: TEXT('long'), comment: '公告内容'},
    text: {type: TEXT('long'), comment: '公告内容预览'},
    status: {type: STRING(64), comment: '状态(草稿/已发布/置顶)'},
  }, {
    comment: '学校公告',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  return Notice
}; 