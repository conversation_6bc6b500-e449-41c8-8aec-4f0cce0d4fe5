'use strict';

module.exports = (app, model) => {
  const { STRING, INTEGER } = app.Sequelize;

  // 模版
  const Template = model.define('train_template', {
    name: {type: STRING(64), comment: '模版名称'},
    content: {type: app.Sequelize.JSON, comment: '模版内容'},
    duration: {type: INTEGER, comment: '时长'},
    score: {type: INTEGER, comment: '总分'},
    createUserID: {type: INTEGER, comment: '创建人'},
    templateDifficulty: {type: app.Sequelize.JSON, comment: '难度配置'},
  }, {
    comment: '模版',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  Template.associate = () => {
    const { Template, User } = model;
    Template.belongsTo(User, {as: 'createUser', foreignKey: 'createUserID' });
  } 

  
  return Template
};