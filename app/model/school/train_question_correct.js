'use strict';

module.exports = (app, model) => {
  const { STRING, JSON, INTEGER, TEXT, DATE, BOOLEAN } = app.Sequelize;

  // 错题题库
  const TrainQuestionCorrect = model.define('train_question_correct', {
    questionBankID: { type: INTEGER, comment: '题库ID' },
    remoteQuestionBankID: { type: INTEGER, comment: '远程题库ID' },

    applyDisplayName: { type: STRING(32), comment: '修改人昵称' },
    applyUserName: { type: STRING(32), comment: '修改人用户名' },
    applySchoolName: { type: STRING(128), comment: '修改人学校名称' },
    applySchoolSlug: { type: STRING(16), comment: '修改人学校slug' },
    applyReason: { type: TEXT, comment: '送审理由' },
    applyTime: { type: DATE, comment: '送审时间' },

    reviewDisplayName: { type: STRING(32), comment: '审核人昵称' },
    reviewUserName: { type: STRING(32), comment: '审核人用户名' },
    reviewSchoolName: { type: STRING(128), comment: '审核人学校名称' },
    reviewSchoolSlug: { type: STRING(16), comment: '审核人学校slug' },
    reviewReason: { type: TEXT, comment: '审核备注' },
    reviewTime: { type: DATE, comment: '审核时间' },

    questionID: { type: INTEGER, comment: '题目ID' },
    originalID: { type: INTEGER, comment: '发布方题目对应ID' },
    questionType: { type: STRING(16), comment: '题目类型' },
    title: { type: STRING(128), comment: '标题' },
    questionDetail: { type: JSON, comment: '题目详情' },
    answer: { type: JSON, comment: '题目答案' },
    difficultyConfig: { type: STRING(16), comment: '难度设置' },
    tags: { type: JSON, comment: '标签' },

    status: { type: STRING(16), comment: '审核状态：待审核/审核通过/审核不通过' },
    acceptUpdate: { type: BOOLEAN, comment: '本题是否接收更新' },
  }, {
    comment: '题库勘误',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });

  return TrainQuestionCorrect;
};