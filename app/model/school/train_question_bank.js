'use strict';

module.exports = (app, model) => {
  const { STRING, DECIMAL, INTEGER, TEXT, DATE } = app.Sequelize;

  // 训练题库
  const TrainQuestionBank = model.define('train_question_bank', {
    name: {type: STRING(64), comment: '题库名称'},
    abstract: {type: STRING(255), comment: '备注'},
    ifSelfBuilding: {type: INTEGER, comment: '是否自建'},
    fromURL: {type: STRING(64), comment: '来源地址'},
    toID: {type: INTEGER, comment: '发布远程题库对应ID'},

    lastSyncAt: { type: STRING(32), comment: '上次接收时间' },
  }, {
    comment: '训练题库',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });
  // TrainQuestionBank.associate = () => {
  //   const { TrainPlan, Train } = model; 
  //   TrainQuestionBank.hasMany(TrainPlan, {as: 'trainPlan',  foreignKey: 'trainID' });
  // }

  return TrainQuestionBank
};