'use strict';

module.exports = (app, model) => {
  const { STRING, DECIMAL, INTEGER, TEXT, BOOLEAN } = app.Sequelize;

  // 训练
  const Train = model.define('train', {
    name: {type: STRING(64), comment: '训练名称'},
    abstract: {type: STRING(255), comment: '训练简介/备注'},
    cover: {type: TEXT, comment: '封面'},
    templateName: {type: STRING(255), comment: '训练结构'},
    template: {type: app.Sequelize.JSON, comment: '训练模版'},
    year: {type: STRING(16), comment: '学年'},
    duration: {type: INTEGER, comment: '时长'},
    score: {type: INTEGER, comment: '总分'},
    notice: {type: TEXT, comment: '注意事项'},
    difficulty: {type: DECIMAL(10, 2), comment: '难度'},
    discriminative: {type: DECIMAL(10, 2), comment: '区分度'},
    content: {type: app.Sequelize.JSON, comment: '试卷内容'},
    createUserID: {type: INTEGER, comment: '创建人'},

    status: {type: STRING(16), comment: '状态'},

    isFinish: {type: BOOLEAN, comment: '配置完成'},

    ifShowScore: {type: INTEGER, comment: '是否展示训练成绩'},
    ifShowCorrectionResults: {type: INTEGER, comment: '是否展示试题批改结果'},
    ifShowWrongAnswer: {type: INTEGER, comment: '是否展示错题答案'},
    ifSetWrongProblemCollection: {type: INTEGER, comment: '是否允许收录⾄错题集'},
    templateDifficulty: {type: app.Sequelize.JSON, comment: '难度配置'},

    teachers: { type: app.Sequelize.JSON, comment: '开放老师' },
  }, {
    comment: '训练',
    paranoid: true,
    underscored: false,
    freezeTableName: true, // Model tableName will be the same as the model name
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at'
  });
  Train.associate = () => {
    const { TrainPlan, Train, User, TrainSeries } = model; 
    Train.hasMany(TrainPlan, {as: 'trainPlan',  foreignKey: 'trainID' });
    Train.belongsTo(User, {as: 'createUser', foreignKey: 'createUserID' });
  }

  return Train
};