'use strict';

module.exports = (app, model) => {
  const { INTEGER, JSON } = app.Sequelize;

  // 节记录
  const SectionRecord = model.define('section_record', {
    userID: {type: INTEGER, comment: '用户ID'},
    sectionID: {type: INTEGER, comment: '节ID'},
    stayTime: {type: INTEGER, comment: '页面停留时间(s)'},
    record: { type: JSON, comment: '节记录' },
    totalScore: { type: INTEGER, comment: '总的问题个数', defaultValue: 0 },
    passCount: { type: INTEGER, comment: '答题通过个数', defaultValue: 0 },
  }, {
    comment: '节记录',
    paranoid: true,
    underscored: false,
    freezeTableName: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    indexes: [
      { fields: ['userID', 'sectionID', 'deleted_at'] }, // 出现了大量的慢查询，所以加上这个索引
    ]
  });
  SectionRecord.associate = () => {
    const { SectionRecord, Section } = model; 
    SectionRecord.belongsTo(Section, {as: 'section', foreignKey: 'sectionID', constraints: false });
  }
  return SectionRecord
}; 