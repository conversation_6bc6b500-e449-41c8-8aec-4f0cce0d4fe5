'use strict';

const moment = require('moment');
const fs = require("mz/fs");
const fsExtra = require('fs-extra');

module.exports = app => {
  class SectionController extends app.Controller {
    // 设置项目书
    async projectBook(){
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        checked: { type: 'bool', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      let response = null;
      let transaction = null;
      
      try{
        const { courseSlug, chapterName, sectionName, checked } = request.body;
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.section.projectBook(courseSlug, chapterName, sectionName, checked, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};

    }

    async changeInternel() {
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        checked: { type: 'bool', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      let response = null;
      let transaction = null;
      
      try{
        const { courseSlug, chapterName, sectionName, checked } = request.body;
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.section.changeInternel(courseSlug, chapterName, sectionName, checked, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 链接至项目书
    async linkProject() {
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        projectSectionName: { type: 'string', required: true},
        projectIndex: { type: 'number', required: true},
        projectCodeIndex: { type: 'number', required: true},
        codeIndex: { type: 'number', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }
      let response = null;
      let transaction = null;
      try{
        const { courseSlug, chapterName, sectionName, projectSectionName, projectIndex, codeIndex, projectCodeIndex } = request.body;
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.section.linkProject(courseSlug, chapterName, sectionName, projectSectionName, projectIndex, codeIndex, projectCodeIndex, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 取消链项目书 
    async cancelLinkProject() {
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }
       // sectionName, projectIndex, codeIndex
       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        projectSectionName: { type: 'string', required: true},
        projectIndex: { type: 'number', required: true},
        projectCodeIndex: { type: 'number', required: true},
        codeIndex: { type: 'number', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }
      let response = null;
      let transaction = null;
      try{
        const { courseSlug, chapterName, sectionName, projectSectionName, projectIndex, codeIndex, projectCodeIndex } = request.body;
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.section.cancelLinkProject(courseSlug, chapterName, sectionName, projectSectionName, projectIndex, codeIndex, projectCodeIndex, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 提交微应用文件记录
    async postMicroAppFile() {
      const { ctx } = this;
      const { request, session } = ctx;
      const { user } = session;

      if (!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        codeResult: { type: 'object', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      try{
        const userID = user.id;
        const { courseSlug, codeResult, chapterName } = request.body;
        await ctx.service.section.postMicroAppFile(userID, courseSlug, codeResult, chapterName);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }
      ctx.body = {code: 0, message: '成功'};
    }

    // 提交微应用记录
    async commitMicroAppRecord() {
      const { ctx } = this;
      const { request, session, model } = ctx;
      const { user } = session;

      if (!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        codeResult: { type: 'object', required: true},
        chapterName: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        UUID: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: JSON.stringify(e.errors)};
        return;
      }

      const { UUID, courseSlug, codeResult, chapterName, sectionName, userID } = request.body;
      const selectUserID = userID ? userID : user.id;

      try {
        // 保存文件内容
        await ctx.service.section.postMicroAppFile(selectUserID, courseSlug, codeResult);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }

      const transaction = await model.transaction();
      
      try{
        const userID = selectUserID;
        let fullName = null;
        const { type, fileName } = codeResult[0];
        switch(type) {
          case 'text':
            fullName = `${fileName}.txt`;
            break;
          case 'table':
            fullName = `${fileName}.csv`;
            break;
          case 'mind':
          case 'flow':
          case 'networksimulator':
          case 'spreadsheet':
          case 'drawio':
            fullName = `${fileName}.json`;
            break;
          default:
            break;
        }

        // 检查权限
        const { teamIDs } = user;
        const { currentSection: section } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName, transaction);

        const sectionID = section.sectionID;
        
        // 去数据库中查找记录
        const progressRecord = await ctx.service.section.getAiProgressByUser(sectionID, userID, transaction);
        const { record } = progressRecord;
        const progress = record ? record.UUIDsMap : {};
        const path =`${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.ipynb`;
        // console.log('content', content, 'path', path);
        progress[UUID] = {
          fileName: fullName,
          time: moment().format('YYYY-MM-DD HH:mm:ss'),
          microAppFileType: type
        };
        
        const { UUIDs } = await ctx.service.section.getRecordUUIDsAndContent(path)

        let passCount = 0;

        // 重新计算AI课程分数
        for (const progressKey in progress) {

          const progressValue = progress[progressKey];
          // 如果不是微应用并且没有通过的话
          if ((progressValue.type !== 'success' && !progressValue.microAppFileType)) {
            continue;
          }
          if (!UUIDs.includes(progressKey)) {
            continue;
          }
          // 要么代码块通过，要么是微应用
          passCount += 1;
        }

        await ctx.service.section.recordAIProgress(sectionID, userID, { UUIDsMap: {...progress} }, UUIDs.length, passCount, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功'};
    }

    // 提交 scratch 记录
    async commitScratchRecord() {
      const { ctx } = this;
      const { request, session, model } = ctx;
      const { user } = session;

      if (!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

        // 校验参数
        const rule = {
        courseSlug: { type: 'string', required: true},
        studentRecord: { type: 'object', required: true},
        chapterName: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { courseSlug, studentRecord, chapterName, sectionName, userID, type } = request.body;
      const selectUserID = userID ? userID : user.id;

      let fullName = null;
      fullName = `${chapterName}/${sectionName}.json`;

      try {
        // 保存文件内容
        let newStudentRecord = studentRecord;
        // 后台课程进度仅修改 judgeSteps 内容
        if (type === 'progress') {
          const studentFile = newStudentRecord[0];
          const { message, fileName, type: sectionType } = studentFile;
          const { judgeSteps } = message;
          const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${fullName}`;
          const fileContent = await ctx.service.course.getFile(pathDir, true);
          newStudentRecord = [{ fileName, type: sectionType, message: { ...fileContent, judgeSteps }}];
        }
        await ctx.service.section.postMicroAppFile(selectUserID, courseSlug, newStudentRecord, chapterName);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }

      const transaction = await model.transaction();
      let response = null;
      
      try{
        const userID = selectUserID;
        let section = null;
        
        // 检查权限
        const { teamIDs } = user;
        if (type !== 'progress') {
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName, transaction);
          section = currentSection;
        } else {
          const course = await model.Course.findOne({
            attributes: ['indics'],
            where: { courseSlug },
            raw: true,
            transaction
          });
  
          const { indics } = course;
  
          // 查询当前课程的sectionID
          const currentChapter = indics.find(chapter => chapter.chapterName === chapterName);
          if(!currentChapter) {
            throw new Error(`本章 ${chapterName} 不存在，可能老师已经修改，请尝试返回课程列表页面！`);
          }
  
          const currentSection = currentChapter.sections.find(section => section.sectionName === sectionName);
          if(!currentSection) {
            throw new Error(`本课程${sectionName}不存在，可能老师已经修改，请尝试返回课程列表页面！`);
          }

          section = currentSection;
        }

        const sectionID = section.sectionID;

        // 计算得分
        let passCount = 0;
        let totalScore = 0;
        const studentFile = studentRecord[0];
        const { message, type: sectionType } = studentFile;
        const { judgeSteps } = message;

        // microbit课程没有评分，暂时处理为1分
        if (sectionType === 'MicroBit') {
          passCount = 1;
          totalScore = 1;
        }
        
        const studentSteps = [];
        if (judgeSteps  && judgeSteps.length) {
          for (const step of judgeSteps) {
            totalScore += step.score;
            if (step.pass) {
              passCount += step.score;
            }
  
            if (step.manual) {
              passCount += step.rate;
            }
  
            const { code, ...info } = step;
            studentSteps.push({ ...info });
          }
        }

        const progress = {
          fileName: fullName,
          time: moment().format('YYYY-MM-DD HH:mm:ss'),
          judgeSteps: studentSteps,
        };
        
        await ctx.service.section.recordAIProgress(sectionID, userID, { ...progress }, totalScore, passCount, transaction);

        response = { passCount, totalScore }
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 提交代码记录，处理项目书逻辑
    async commitSourceBlockRecord() {
      const { ctx } = this;
      const { request, session, model, schoolSlug } = ctx;
      const { user } = session;

      if (!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        UUID: { type: 'string', required: true},
        kernel: { type: 'object', required: true},

      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      let response = null;
      let transaction = null;

      // 获取userID
      const { teamIDs } = user;
      const userID = user.id;

      try{
        // 启用事务
        transaction = await model.transaction({autocommit: false});

        const { courseSlug, chapterName, sectionName } = request.body;

        // 检查权限
        const { currentSection: section } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName, transaction);
        const sectionID = section.sectionID;
        
        const params = { ...request.body, sectionID, userID, schoolSlug };
        response = await ctx.service.section.commitSourceBlockRecord(params, transaction);
      }
      catch(e) {
        console.log('commitSourceBlockRecord', schoolSlug, 'userID:', userID, e)
        if(transaction) {
          await transaction.rollback();
        }
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async removeRecordFromSectionRecord() {
      const { ctx } = this;
      const { request, session, model } = ctx;
      const { user } = session;

      if (!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        UUID: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }
      const transaction = await model.transaction();
      try{
        const { UUID, courseSlug, chapterName, sectionName } = request.body;
        const userID = user.id;

        // // 获取sectionID
        const section = await ctx.service.section.getSection(courseSlug, chapterName, sectionName, transaction);
        if(section) {
          const sectionID = section.dataValues ? section.dataValues.id : section.id;
          
          // 去数据库中查找记录
          const progressRecord = await ctx.service.section.getAiProgressByUser(sectionID, userID);
          const { record } = progressRecord;
          const progress = record ? record.UUIDsMap : {};
          if (record && record.UUIDsMap) {
            const progress = record ? record.UUIDsMap : {};
            if (progress[UUID]) {
              delete progress[UUID];
              progressRecord.passCount -= 1; 
            }
          }
          const { passCount, totalScore } = progressRecord;

          await ctx.service.section.recordAIProgress(sectionID, userID, { UUIDsMap: {...progress} }, totalScore, passCount, transaction);
        }
      }
      catch(e) {
        await transaction.rollback()
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', };
    }

    async getStudentFileContent() {
      const { ctx } = this;
      const { session } = ctx;
      const { user } = session;

      if (!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      // 校验参数
      const rule = {
        fileName: { type: 'string', required: true},
        courseSlug: { type: 'object', required: true}
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      let response = null;
      try {
        const { config } = app;
        const { courseSlug, fileName } = ctx.request.body;
        // 拼接主目录
        const pathDir = `${config.file.cd}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${user.id}/${fileName}`;
        response = await ctx.service.course.getFile(pathDir, false);
      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取某一个章节的所有答题记录
    async getSectionRecord(){
      const { ctx, service } = this;
      const { model, query, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
        teamID: { type: 'string', required: true},
      };
      try {
        ctx.validate(rule, query);
        if(isNaN(parseInt(query.teamID, 10))) {
          throw new Error('当前选择班级错误')
        }
      } catch(e) {
        console.log('getSectionRecord', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;
      
      try{
        const { courseSlug, chapterName, sectionName, teamID } = query;
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.section.getSectionRecord(courseSlug, chapterName, sectionName, parseInt(teamID, 10), transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 查询之前学生是否有答题记录
    async hasRecord(){
      const { ctx, service } = this;
      const { query, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

       // 校验参数
       const rule = {
        courseSlug: { type: 'string', required: true},
        sectionName: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
      };
      
      try {
        ctx.validate(rule, query);
      } catch(e) {
        console.log('hasRecord', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      
      try{
        const { courseSlug, chapterName, sectionName } = query;
        response = await service.section.hasRecord(courseSlug, chapterName, sectionName);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getCurrentChapterAndClassSectionRecord() {

      const { ctx, service } = this;

       // 校验参数 userIDList = [], sectionList = [], courseSlug, chapterName
       const rule = {
        classID: { type: 'string', required: true},
        courseSlug: { type: 'string', required: true},
        chapterName: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('getCurrentChapterAndClassSectionRecord', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const {
        classID,
        courseSlug,
        chapterName
      } = ctx.query;

      let response = null;
      
      try{
        response = await service.section.getCurrentChapterAndClassSectionRecord({ classID: parseInt(classID, 10), courseSlug, chapterName });
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 保存课程历史记录
    async saveHistoryRecords() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        saveTime: { type: 'int', required: true, allowEmpty: false },
        modifiedType: { type: 'string', required: true, allowEmpty: false },
        fileContent: {type: 'string', required: true, allowEmpty: false },
        comment: { type: 'string', required: false, allowEmpty: true },
      };
 
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('saveHistoryRecords', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;

      try{
        // 校验权限
        const { courseSlug, chapterName, sectionName } = ctx.request.body;
        await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        const sectionQuery = { ...ctx.request.body, userID, teamIDs };
        // 启用事务
        transaction = await model.transaction({autocommit: false});

        // 入库
        response = await service.section.saveHistoryRecords(sectionQuery, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取历史版本数据
    async getHistoryRecords() {
      const { ctx, service } = this;
      const { session, query } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
      };

      try {
        ctx.validate(rule, query);
      } catch(e) {
        console.log('getHistoryRecords', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;

      try{
          const sectionQuery = { ...query, userID };

          // 校验权限
          const { courseSlug, chapterName, sectionName } = sectionQuery;
          await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

          response = await service.section.getHistoryRecords(sectionQuery);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取历史版本文件内容
    async getHistoryFile() {
      const { ctx, service } = this;
      const { session, query } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        version: { type: 'string', required: true, allowEmpty: false },
      };
  
      try {
        ctx.validate(rule, query);
      } catch(e) {
        console.log('getHistoryFile', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;

      try{
          // 校验权限
          const { courseSlug, chapterName, sectionName } = query;
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
          const { sectionType, ext } = currentSection;
          const sectionQuery = { ...query, userID, sectionType, ext };

          response = await service.section.getHistoryFile(sectionQuery);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 恢复当前课程内容为指定版本历史课程内容
    async recoverHistoryFile() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        version: { type: 'int', required: true, allowEmpty: false },
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('recoverHistoryFile', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;

      try{
        // 启用事务
        transaction = await model.transaction({autocommit: false});
                          
        // 校验权限
        const { courseSlug, chapterName, sectionName } = ctx.request.body;
        const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        const { sectionTitle } = currentSection;
        const sectionQuery = { ...ctx.request.body, userID, sectionTitle };
        // 入库
        response = await service.section.recoverHistoryFile(sectionQuery, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 将指定版本文件内容另存为新课程
    async saveHistoryFileAs() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        version: { type: 'int', required: true, allowEmpty: false },
        newSectionName: { type: 'string', required: true, allowEmpty: false },
        newSectionTitle: { type: 'string', required: false, allowEmpty: true },
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('saveHistoryFileAs', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;

      try{
        // 启用事务
        transaction = await model.transaction({autocommit: false});
        
        // 校验权限
        const { courseSlug, chapterName, sectionName } = ctx.request.body;
        const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        const { sectionType, ext } = currentSection;
        const sectionQuery = { ...ctx.request.body, userID, sectionType, ext };
        // 入库
        response = await service.section.saveHistoryFileAs(sectionQuery, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 更新历史版本说明字段
    async updateHistoryComment() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        version: { type: 'int', required: true, allowEmpty: false },
        comment: { type: 'string', required: false, allowEmpty: true },
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('updateHistoryComment', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;

      try{
        const sectionQuery = { ...ctx.request.body, userID };

        // 启用事务
        transaction = await model.transaction({autocommit: false});

        // 校验权限
        const { courseSlug, chapterName, sectionName } = sectionQuery;
        await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

        // 入库
        response = await service.section.updateHistoryComment(sectionQuery, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getMicroAppPrimaryContentByUUID() {
      const { ctx, service } = this;
      const { UUID } = ctx.params;
      const { courseSlug, chapterName, sectionName, fullFilename } = ctx.query;
      const rule = {
        courseSlug: { type: 'string', required: true },
        chapterName: { type: 'string', required: true },
        sectionName: { type: 'string', required: true },
        fullFilename: { type: 'string', required: false },
      };
  
      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('updateHistoryComment', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }
      let currentCell;
      try {
        const path =`${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.ipynb`;
        const content = await service.course.getFile(path, true);
        if (!content.cells || !content.cells.length) {
          throw new Error('当前文件内容不存在');
        }
        currentCell = content.cells.find(cell => cell.metadata.UUID === UUID);
        if (!currentCell) {
          throw new Error('当前文件内容不存在');
        }
        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${ctx.session.user.id}/${fullFilename}`;
        await fsExtra.remove(pathDir);
      } catch(err) {
        ctx.body = { code: 1, data: 'fail', message: err.message };
        return;
      }
      ctx.body = { code: 0, data: currentCell };
    }

    // 提交access答题记录
    async submitAccessAnswer() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        studentAnswer: { type: 'object', required: false, allowEmpty: true },
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('submitAccessAnswer', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;

      try{
        const sectionQuery = { ...ctx.request.body, userID };

        // 启用事务
        transaction = await model.transaction({autocommit: false});

        // // 校验权限
        // const { courseSlug, chapterName, sectionName } = sectionQuery;
        // await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

        // 入库
        response = await service.section.submitAccessAnswer(sectionQuery, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 提交答题记录
    async submitPPTRecord() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID } = user;

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        studentAnswer: { type: 'object', required: false, allowEmpty: true },
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('submitPPTRecord', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = await model.transaction({autocommit: false});

      try{
        const { courseSlug, chapterName, sectionName, studentAnswer } = ctx.request.body;
        // 入库
        response = await service.section.submitPPTRecord({ courseSlug, chapterName, sectionName, studentAnswer, userID }, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        console.error('submitPPTRecord', e)
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 检查课程发布/草稿状态
    async checkSectionStatus() {
      const { ctx, service } = this;
      const { session, query } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
      };
  
      try {
        ctx.validate(rule, query);
      } catch(e) {
        console.log('checkSectionStatus', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;

      try{
          // 校验权限
          const { courseSlug, chapterName, sectionName } = query;
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
          const { sectionType, ext } = currentSection;
          const sectionQuery = { ...query, userID, sectionType, ext };

          response = await service.section.checkSectionStatus(sectionQuery);
      }
      catch(e) {
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async submitCodeAnswer() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID } = user;

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        studentAnswer: { type: 'object', required: false, allowEmpty: true },
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('submitCodeAnswer', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = await model.transaction({autocommit: false});

      try{
        const { courseSlug, chapterName, sectionName, studentAnswer } = ctx.request.body;
        // 入库
        response = await service.section.submitCodeAnswer({ courseSlug, chapterName, sectionName, studentAnswer, userID }, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        console.error('submitCodeAnswer', e)
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 自动生成ai课程内容
    async autoGenerateCourseContent() {
      const { ctx } = this;
      const { service, session, schoolSlug } = ctx;

      // 校验权限
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      // 仅限特定学校使用
      const allowedSlugs = ['csxx', 'fhsyxx'];
      if (!allowedSlugs.includes(schoolSlug)) {
        ctx.body = {code: 400, message: '该学校不允许使用'};
        return;
      }

      let response = null;

      try {
        const { courseSlug, chapterName, sectionName, prompt } = ctx.request.body;
        await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

        response = await service.gpt.getGptResponse(prompt);
      } catch(e) {
        console.error('generateAICourseContent', e)
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 根据参考内容和提示词自动生成ai课程内容
    async generateCourseContent() {
      const { ctx } = this;
      const { service, session, schoolSlug } = ctx;

      // 校验权限
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        sectionName: { type: 'string', required: true, allowEmpty: false },
        selectSectionIDs: { type: 'object', required: false, allowEmpty: true },
        prompt: { type: 'string', required: true, allowEmpty: false },
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      // 仅限特定学校使用
      const allowedSlugs = ['csxx', 'fhsyxx'];
      if (!allowedSlugs.includes(schoolSlug)) {
        ctx.body = {code: 400, message: '该学校不允许使用'};
        return;
      }

      let response = null;

      try {
        const { courseSlug, chapterName, sectionName, selectSectionIDs, prompt } = ctx.request.body;
        await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

        // 如果选择了参考内容，获取参考内容课程markdown内容，拼接到prompt后面
        let referenceContent = '';
        if (selectSectionIDs && selectSectionIDs.length > 0) {
          const { content } = await ctx.service.section.getSectionContent(selectSectionIDs);
          referenceContent = content;
        }

        const newPrompt = referenceContent ? `根据参考内容：${referenceContent}。\n ${prompt} ` : prompt;
        response = await service.gpt.getGptResponse(newPrompt);
      } catch(e) {
        console.error('generateCourseContent', e)
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

  }
  return SectionController;
}