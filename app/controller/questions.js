'use strict';

module.exports = app => {
  class QuestionsController extends app.Controller {
    // 创建训练
    async createQuestions() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 标题
        title: {type: 'string', required: false},
        // 题库ID
        questionBankID: {type: 'number', required: false},
        // 题目类型
        questionType: {type: 'string', required: false},
        // 题目详情
        questionDetail: {type: 'object', required: false},
        // 题目答案
        answer: {type: 'object', required: false},
        // 难度设置
        difficultyConfig: {type: 'string', required: false},
        // 难度
        difficulty: {type: 'number', required: false},
        // 区分度
        discriminative: {type: 'number', required: false},
        // 引用
        trainsUsed: {type: 'object', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 标签
        tags: {type: 'object', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.createUserID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.questions.createQuestions(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量创建训练
    async bulkCreateQuestions() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        nodes: {
          type: 'array',
          itemType: 'object',
          rule: {
            // 标题
            title: {type: 'string', required: false},
            // 题库ID
            questionBankID: {type: 'number', required: false},
            // 题目类型
            questionType: {type: 'string', required: false},
            // 题目详情
            questionDetail: {type: 'object', required: false},
            // 题目答案
            answer: {type: 'object', required: false},
            // 难度设置
            difficultyConfig: {type: 'string', required: false},
            // 难度
            difficulty: {type: 'number', required: false},
            // 区分度
            discriminative: {type: 'number', required: false},
            // 引用
            trainsUsed: {type: 'object', required: false},
            // 状态
            status: {type: 'string', required: false},
  
            // 标签
            tags: {type: 'object', required: false},
          },
        }
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let responses = [];
      try
      {
        for(const row of ctx.request.body.nodes) {
          // 获取更新数据
          const requestBody = row;
          const node = {};
          for(const key in rule.nodes.rule) {
            const value = requestBody[key];
            if(value === undefined) {
              continue;
            }
    
            node[key] = value;
          }
    
          node.createUserID = ctx.session.user.id;
          // console.log('node:', node);
          // continue;
          // 创建训练
          const response = await this.service.questions.createQuestions({ ...node, mode: 'import' }, transaction);
          responses.push(response);
        }
      }
      catch(e)
      {
        ctx.body = { code: 500, message: e.message };
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: responses};
    }

    // 修改训练
    async putQuestions() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 标题
        title: {type: 'string', required: false},
        // 创建人
        // createUserID: {type: 'number', required: false},
        // 题库ID
        questionBankID: {type: 'number', required: true},
        // 题目类型
        questionType: {type: 'string', required: false},
        // 题目详情
        questionDetail: {type: 'object', required: false},
        // 题目答案
        answer: {type: 'object', required: false},
        // 难度
        difficulty: {type: 'number', required: false},
        // 难度设置
        difficultyConfig: {type: 'string', required: false},
        // 区分度
        discriminative: {type: 'number', required: false},
        // 引用
        trainsUsed: {type: 'object', required: false},
        // 状态
        status: {type: 'string', required: false},

        // 作者
        author: {type: 'string', required: false},

        // 标签
        tags: {type: 'object', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        response = await this.service.questions.putQuestions(ctx.params.id, node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 题目勘误记录
    async applyQuestionCorrect() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题目id
        questionID: {type: 'number', required: true},

        originalID: {type: 'number', required: true},

        // 标题
        title: {type: 'string', required: false},
        // 题库ID
        questionBankID: {type: 'number', required: true},
        // 题目类型
        questionType: {type: 'string', required: true},
        // 题目详情
        questionDetail: {type: 'object', required: true},
        // 题目答案
        answer: {type: 'object', required: true},
        // 难度
        difficulty: {type: 'number', required: false},
        // 难度设置
        difficultyConfig: {type: 'string', required: false},

        // 状态
        status: {type: 'string', required: false},

        // 标签
        tags: {type: 'object', required: false},

        allTags: {type: 'object', required: false},

        acceptUpdate: { type: 'boolean', required: true },

        // 送审理由
        applyReason: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 题目勘误记录
        await this.service.questions.createQuestionCorrect(node, transaction);
        // 修改题目
        response = await this.service.questions.putQuestions(node.questionID, node, transaction);
      }
      catch(e)
      {
        console.error(e, 'createQuestionCorrect')
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 相似题目检测
    async checkDuplicateQuestions() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题目详情
        questions: { type: 'object', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const { questions = [] } = ctx.request.body;

      let responses = [];
      try
      {
        let index = 0;
        for (const question of questions) {
          const response = await this.service.questions.checkDuplicateQuestions(question);

          if (response && response.duplicate) {
            responses.push({ index, ...response});
          }

          index++;
        }
      }
      catch(e)
      {
        ctx.body = {code: 500, message: e.message};
        return;
      }

      ctx.body = { code: 0, message: '成功', data: responses };
    }

    // 修改训练
    async bulkPutQuestions() {
      const { ctx } = this;
      const nodeRules = {
            id: {type: 'int', required: true},
            // 标题
            title: {type: 'string', required: false},
            // 创建人
            // createUserID: {type: 'number', required: false},
            // 题库ID
            questionBankID: {type: 'number', required: false},
            // 题目类型
            questionType: {type: 'string', required: false},
            // 题目详情
            questionDetail: {type: 'object', required: false},
            // 题目答案
            answer: {type: 'object', required: false},
            // 难度
            difficulty: {type: 'number', required: false},
            // 难度设置
            difficultyConfig: {type: 'string', required: false},
            // 区分度
            discriminative: {type: 'number', required: false},
            // 引用
            trainsUsed: {type: 'object', required: false},
            // 状态
            status: {type: 'string', required: false},
            // 标签
            tags: {type: 'object', required: false},
      };

      // 校验参数
      const rules = {
        updateQuestions: {
          type: 'array', 
          itemType: 'object',
          rule: nodeRules
        },
      };

      try {
        ctx.validate(rules, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        const rows = ctx.request.body.updateQuestions;
        for (const row of rows) {
          // 获取更新数据
          const node = {};
          for(const key in nodeRules) {
            if (key === 'id') {
              continue;
            }
            const value = row[key];
            if(value === undefined) {
              continue;
            }

            node[key] = value;
          }
          // 修改训练
          response = await this.service.questions.putQuestions(row.id, node, transaction);
        }
      }
      catch(e)
      {
        console.error(e);
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功'};
    }

    // 获取训练
    async getQuestions() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练
        response = await this.service.questions.getQuestions(ctx.params.id);
      }
      catch(e)
      {
        console.error('getQuestions', e)
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除训练
    async destoryQuestions() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练
        response = await this.service.questions.destoryQuestions(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练列表
    async getQuestionsList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 创建人
        createUserID: {type: 'number', required: false},
        // 题目类型
        questionType: {type: 'string', required: false},
        // 难度  [0, 1]
        difficulty: {type: 'number', required: false},
        // 区分度  [0, 1]
        discriminative: {type: 'number', required: false},
        // 未引用
        notTrainsUsed: {type: 'object', required: false},
        // 未在全部考试中引用
        notAllTrainsUsed: {type: 'object', required: false},
        // 题干或标签
        search: {type: 'string', required: false},

        pageSize: { type: 'number', required: true},
        pageNum: { type: 'number', required: true},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try
      {
        // 创建训练
        response = await this.service.questions.getQuestionsList(node);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getQuestionBankStatistics() {
      const { ctx } = this;

      let response;
      const { id } = ctx.params;

      try {
        // 修改训练
        response = await ctx.service.questions.getQuestionBankStatistics(id);
      } catch(e) {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getAllCorrectQuestions() {
      const { ctx } = this;

      let response;
      const { id } = ctx.query;

      try {
        // 修改训练
        response = await ctx.service.questions.getAllCorrectQuestions(id);
      } catch(e) {
        console.error(e, 'getAllCorrectQuestions');
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取题库中重复的题库
    async getTrainDuplicateQuestions() {
      const { ctx } = this;

      let response;
      const { id, type, similar = 0.8 } = ctx.query;

      try
      {
        // 获取题库中重复的题库（暂时为单选、综合）
        response = await this.service.questions.getTrainDuplicateQuestions(id, type, parseFloat(similar));
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取题库中重复的题库(异步任务)
    async getTrainDuplicateQuestionsBytaskTrain() {
      const { ctx } = this;
      const { id, type, similar } = ctx.query;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      let response;
      try
      {
        console.log('调用远程查重1', id, type, similar);
        // 获取权限
        response = await this.service.thirdPart.getTrainDuplicateQuestionsBytaskTrain(parseInt(id), type, similar, transaction);
      }
      catch(e)
      {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      await transaction.commit();

      // console.log('查重响应response:',response);
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async updateCorrectQuestion() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题目id
        questionID: { type: 'number', required: true },

        originalID: { type: 'number', required: true },

        // 标题
        title: {type: 'string', required: false},
        // 题库ID
        questionBankID: {type: 'number', required: true},
        // 题目类型
        questionType: {type: 'string', required: true},
        // 题目详情
        questionDetail: {type: 'object', required: true},
        // 题目答案
        answer: {type: 'object', required: true},
        // 难度
        difficulty: {type: 'number', required: false},
        // 难度设置
        difficultyConfig: {type: 'string', required: false},

        // 状态
        status: {type: 'string', required: false},

        // 标签
        tags: {type: 'object', required: false},

        acceptUpdate: { type: 'boolean', required: true },

        // 送审理由
        applyReason: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据 是否接收远程题库此题更新
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 题目勘误记录
        response = await this.service.questions.updateCorrectQuestion(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'updateCorrectQuestion')
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async reviewQuestionCorrect() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        reviewQuestion: { type: 'object', required: true },
        // 审核结果：审核通过/审核不通过
        reviewResult: { type: 'string', required: true },
        // 审核理由
        reviewReason: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try {
        // 题目勘误记录
        response = await this.service.questions.reviewQuestionCorrect(node, transaction);
      } catch(e) {
        ctx.body = { code: 500, message: e.message };
        console.error(e, 'reviewQuestionCorrect')
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async reviewQuestionCorrectManual() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        reviewQuestion: { type: 'object', required: true },
        // 审核结果：审核通过/审核不通过
        reviewResult: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await app.mainModel.transaction({autocommit: false});

      let response;
      try {
        // 题目勘误记录
        response = await this.service.questions.reviewQuestionCorrectManual(node, transaction);
      } catch(e) {
        ctx.body = { code: 500, message: e.message };
        console.error(e, 'reviewQuestionCorrectManual')
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 复制题目
    async copyQuestion() {
      const { ctx } = this;

      let response;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { questionID, questionBankID } = ctx.request.body;
      
      try {
        response = await ctx.service.questions.copyQuestion(questionID, questionBankID, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'copyQuestion')
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 跨题库复制题目
    async copyQuestionCrossBank() {
      const { ctx } = this;

      let response;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { questionID, questionBankID } = ctx.request.body;
      
      try {
        response = await ctx.service.questions.copyQuestionCrossBank(questionID, questionBankID, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'copyQuestion')
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量修改题目状态
    async changeQuestionStatus() {
      const { ctx } = this;

      let response;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { selectQuestionIDs, status } = ctx.request.body;
      
      try {
        response = await ctx.service.questions.changeQuestionStatus(selectQuestionIDs, status, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'changeQuestionStatus')
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量修改题目标签
    async modifyQuestionTags() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        selectQuestionIDs: { type: 'object', required: true },
        allSelectTagIDs: { type: 'object', required: true },
        modifyTagType: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { selectQuestionIDs, modifyTagType, allSelectTagIDs } = ctx.request.body;
      
      try {
        response = await ctx.service.questions.modifyQuestionTags({ selectQuestionIDs, modifyTagType, allSelectTagIDs }, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'modifyQuestionTags')
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 检查引用此题目的所有试卷
    async checkQuestionReferInTrains () {
      const { ctx } = this;

      let response;

      const { questionID } = ctx.query;
      
      try {
        response = await ctx.service.questions.checkQuestionReferInTrains(questionID);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'checkQuestionReferInTrains')
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除题目及题目在试卷中的引用
    async removeQuestionInTrains() {
      const { ctx } = this;

      let response;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { trainIDs, deleteQuestionID } = ctx.request.body;
      
      try {
        response = await ctx.service.questions.removeQuestionInTrains({ trainIDs, deleteQuestionID }, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'removeQuestionInTrains')
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量根据题目id与标签id修改标签关联
    async updateBulkQuestionSolution() {
      const {ctx, service} = this;

      const rule = {
        nodes: {
          type: 'array',
          itemType: 'object',
          rule: {
            id: {type: 'number', required: true},
            // 题解
            solution: {type: 'string', required: true},
          },
        }
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const nodes = ctx.request.body.nodes.map(row => ({ id: row.id, solution: row.solution }));

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      try {
        await service.questions.updateBulkQuestionSolution(nodes, transaction);
      } catch(e) {
        ctx.body = { code: 500, message: e.message };
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = { code: 0, message: '成功' };
    }

  }

  return QuestionsController;
}