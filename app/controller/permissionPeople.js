'use strict';

module.exports = app => {
  class PermissionPeopleController extends app.Controller {
    // 刷新权限数据
    async refreshLimitPeople() {
      const { ctx } = this;
      const transaction = await ctx.model.transaction({autocommit: false});
      let response = null;
      try{
        response = await ctx.service.permissionPeople.refreshLimitPeople(true, transaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      await transaction.commit();
      ctx.body = { code: 0, message: '成功' };
    }

    // 刷新权限数据
    async refreshCourseLimitPeople() {
      const { ctx } = this;
      const transaction = await ctx.model.transaction({autocommit: false});
      let response = null;
      try{
        response = await ctx.service.permissionPeople.refreshCourseLimitPeople( transaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      await transaction.commit();
      ctx.body = { code: 0, message: '成功' };
    }

    // 刷新权限数据
    async refreshTrainLimitPeople() {
      const { ctx } = this;
      const transaction = await ctx.model.transaction({autocommit: false});
      let response = null;
      try{
        response = await ctx.service.permissionPeople.refreshTrainLimitPeople(true, transaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      await transaction.commit();
      ctx.body = { code: 0, message: '成功' };
    }
		
		// 获取占用情况
		async getLimitPeople() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        key: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { key } = ctx.query;

      let response = null;

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { mainModel } = app;
      const mainTransaction = await mainModel.transaction({autocommit: false});

      try{
        response = await ctx.service.permissionPeople.getLimitPeople(key, transaction, mainTransaction);
      }catch(e){
        console.error(e);
        ctx.body = {code: 1, message: '失败' + e.message};
        await transaction.rollback();
        await mainTransaction.rollback();
        return;
      }

      await transaction.commit();
      await mainTransaction.commit();
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}
		
		// 根据占用人员ID获取人员所在班级树
		async getLimitPeopleClass() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        userIDs: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { userIDs } = ctx.request.body;

      let response = null;
      try{
        response = await ctx.service.permissionPeople.getLimitPeopleClass(JSON.parse(userIDs));
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}
		
		// 更新占用
		async updateLimitPeople() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        userIDs: { type: 'string', required: true},
        classIDs: { type: 'object', required: false },
				key: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { userIDs, key, classIDs } = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { mainModel } = app;
      const mainTransaction = await mainModel.transaction({autocommit: false});

      let response = null;
      try{
        response = await ctx.service.permissionPeople.updateLimitPeople(JSON.parse(userIDs), classIDs, key, transaction, mainModel, mainTransaction);
      } catch(e) {
        ctx.body = {code: 1, message: '失败' + e.message};
        transaction.rollback();
        mainTransaction.rollback();
        return;
      }
      transaction.commit();
      mainTransaction.commit();
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}

		// 根据新班级id求人员id，并合并原有占用人员id
		async getLimisUserByClassID() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        classIDs: { type: 'string', required: true},
				key: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { classIDs, key } = ctx.query;

      let response = null;
      try{
        response = await ctx.service.permissionPeople.getLimisUserByClassID(JSON.parse(classIDs), key);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}

		// 读取历史记录
		async getHistoryPermission() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        key: { type: 'string', required: false},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { key } = ctx.query;

      let response = null;
      try{
        response = await ctx.service.permissionPeople.getHistoryPermission(key);
      }catch(e){
        console.log(e, 'eee')
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}

		// 按照历史记录和班级记录，恢复记录
		async replaceWithHistory() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        historyID: { type: 'string', required: true},
				classID: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { historyID, classID } = ctx.query;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { mainModel } = app;
      const mainTransaction = await mainModel.transaction({autocommit: false});

      let response = null;
      try{
        response = await ctx.service.permissionPeople.replaceWithHistory(parseInt(historyID), parseInt(classID), transaction, mainModel, mainTransaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        transaction.rollback();
        mainTransaction.rollback();
        return;
      }
      transaction.commit();
      mainTransaction.commit();
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}

		// 按照历史记录和班级记录，恢复记录
		async replaceOneHistory() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        historyID: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { historyID } = ctx.query;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { mainModel } = app;
      const mainTransaction = await mainModel.transaction({autocommit: false});

      let response = null;
      try{
        response = await ctx.service.permissionPeople.replaceOneHistory(parseInt(historyID), transaction, mainModel, mainTransaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        transaction.rollback();
        mainTransaction.rollback();
        return;
      }
      transaction.commit();
      mainTransaction.commit();
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}

		// 按照历史记录和班级记录，恢复记录
		async replaceOneHistory() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        historyID: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.query);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { historyID } = ctx.query;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      try{
        response = await ctx.service.permissionPeople.replaceOneHistory(parseInt(historyID), transaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        transaction.rollback();
        return;
      }
      transaction.commit();
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
		}

    // 超管按照班级批量进行清理训练计划占用
    async clearTrainUserByBulkClass() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        classIDs: { type: 'object', required: true},
        ifClearOtherClassUser: { type: 'boolean', required: false},
        ifClearOtherClassUserNote: { type: 'string', required: false },
        configKey: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { classIDs, ifClearOtherClassUser, ifClearOtherClassUserNote, configKey } = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { mainModel } = app;
      const mainTransaction = await mainModel.transaction({autocommit: false});

      let response = null;
      try{
        response = await ctx.service.permissionPeople.clearTrainUserByBulkClass({ classIDs, ifClearOtherClassUser, note: ifClearOtherClassUserNote, configKey }, transaction, mainTransaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        transaction.rollback();
        mainTransaction.rollback();
        return;
      }
      transaction.commit();
      mainTransaction.commit();
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
    }

    // 超管按照班级批量进行清理训练计划占用
    async clearAllOccupations() {
      const { ctx } = this;
      // 校验参数
      const rule = {
        configKey: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

			const { configKey } = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { mainModel } = app;
      const mainTransaction = await mainModel.transaction({autocommit: false});

      let response = null;
      try{
        response = await ctx.service.permissionPeople.clearAllOccupations({ configKey }, transaction, mainTransaction);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        transaction.rollback();
        mainTransaction.rollback();
        return;
      }
      transaction.commit();
      mainTransaction.commit();
      // console.log('response:',response)
  
      ctx.body = { code: 0, data: response ,message: '成功' };
    }
  }

  return PermissionPeopleController;
};