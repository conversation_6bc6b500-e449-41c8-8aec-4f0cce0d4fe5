'use strict';

module.exports = app => {
  class TrainThroughTrainPlanController extends app.Controller {
    // 创建训练
    async createTrainPlan() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练ID
        // trainID: {type: 'number', required: true},
        selectTrainIDs: {type: 'object', required: true},
        // 训练名称
        name: { type: 'string', required: false },
        // 总分
        score: {type: 'number', required: false},
        // 时长
        duration: {type: 'number', required: false},

        minTrainDuration: {type: 'number', required: false},
        
        // 开始时间
        startTime: {type: 'datetime', required: false},
        // 结束时间
        endTime: {type: 'datetime', required: false},
        // 训练环境
        environment: {type: 'string', required: false},
        // 参与班级
        openClasses: {type: 'object', required: true},

        // 训练码
        code: {type: 'string', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 报名人数
        enrolledCount: {type: 'number', required: false},
        // 交卷人数
        submitCount: {type: 'number', required: false},
        // 平均分
        average: {type: 'number', required: false},
        // 方差
        variance: {type: 'number', required: false},
        // 训练简介/备注
        abstract: { type: 'string', required: false},
        // 是否展示训练成绩
        ifShowScore: {type: 'number', required: false},
        // 是否展示试题批改结果
        ifShowCorrectionResults: {type: 'number', required: false},
        // 是否展示错题答案
        ifShowWrongAnswer: {type: 'number', required: false},
        // 是否允许收录⾄错题集
        ifSetWrongProblemCollection: {type: 'number', required: false},

        // 题目随机乱序
        ifRandomOrder: {type: 'number', required: false},

        // 是否禁止查看试卷
        ifForbidViewPaper: {type: 'number', required: false},

        // 学生与训练对应关系
        studentTrainLists: {type: 'object', required: false},
        // 训练模式
        mode: { type: 'string', require: false },

        // 学年
        year: { type: 'string', require: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.log('eee', e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined || value === '') {
          continue;
        }

        node[key] = value;
      }
      node.createUserID = ctx.session.user.id;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.createTrainPlan(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改训练
    async putTrainPlan() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练ID
        // trainID: {type: 'number', required: true},
        // 训练名称
        name: { type: 'string', required: false },
        // 总分
        score: {type: 'number', required: false},
        // 时长
        duration: {type: 'number', required: false},
        // 开始时间
        startTime: {type: 'datetime', required: false},
        // 结束时间
        endTime: {type: 'datetime', required: false},
        // 训练环境
        environment: {type: 'string', required: false},
        // 参与班级
        classes: {type: 'object', required: false},
        // 向所有班级开放
        openForAllClass: {type: 'number', required: true},
        // 训练码
        code: {type: 'string', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 报名人数
        enrolledCount: {type: 'number', required: false},
        // 交卷人数
        submitCount: {type: 'number', required: false},
        // 平均分
        average: {type: 'number', required: false},
        // 方差
        variance: {type: 'number', required: false},
        // 训练简介/备注
        abstract: { type: 'string', required: false},
        // 是否展示训练成绩
        ifShowScore: {type: 'number', required: false},
        // 是否展示试题批改结果
        ifShowCorrectionResults: {type: 'number', required: false},
        // 是否展示错题答案
        ifShowWrongAnswer: {type: 'number', required: false},
        // 是否允许收录⾄错题集
        ifSetWrongProblemCollection: {type: 'number', required: false},

        // 题目随机乱序
        ifRandomOrder: {type: 'number', required: false},

        // 是否禁止查看试卷
        ifForbidViewPaper: {type: 'number', required: false},
        
        // 学生与训练对应关系
        studentTrainLists: {type: 'object', required: false},
        // 模式
        mode: { type: 'string', required: false }
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        response = await this.service.trainThroughTrainPlan.putTrainPlan(ctx.params.id, node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练
    async getTrainPlan() {
      const { ctx } = this;

      let response;

      const { trainID } = ctx.query;
      try
      {
        // 获取训练
        response = await this.service.trainThroughTrainPlan.getTrainPlan(ctx.params.id, parseInt(trainID, 10));
      }
      catch(e)
      {
        console.error(e);
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练计划：包含用户、用户训练记录和所有的相关训练
    async getTrainPlanInfo() {
      const { ctx } = this;

      let response;

      const { trainPlanID } = ctx.query;
      try
      {
        /// 获取训练计划：包含用户、用户训练记录和所有的相关训练
        response = await ctx.service.trainThroughTrainPlan.getTrainPlanInfo(trainPlanID);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除训练
    async destoryTrainPlan() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练
        response = await this.service.trainThroughTrainPlan.destoryTrainPlan(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量删除训练
    async destoryTrainPlans() {
      const { ctx } = this;

      let response;
      
      const transaction = await ctx.model.transaction({autocommit: false});

      const { trainPlanIDs } = ctx.request.body;

      try {
        response = await ctx.service.trainThroughTrainPlan.destoryTrainPlans(trainPlanIDs, transaction);
      } catch(e) {
        console.error(e);
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练列表
    async getTrainPlanList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: false},
        // 排序
        columnKey: { type: 'string', required: false},
        order: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.getTrainPlanList(node);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async shareTrainPlan() {
      const { ctx } = this;

      const { session } = ctx;
      if (!session || !session.user) {
        ctx.body = {code: 400, message: '请登录'};
        return;
      }

      const { user: { id: userID } = {} } = session;

      // 校验参数
      const rule = {
        // 训练计划ID
        id: { type: 'number', required: true },
        // 分享的用户ID
        teachers: { type: 'array', required: true },
      }

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.error(e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      const { id, teachers } = ctx.request.body;

      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try {
        response = await ctx.service.trainThroughTrainPlan.shareTrainPlan({ id, teachers, userID }, transaction);
      } catch(e) {
        console.error(e);
        ctx.body = { code: 500, message: e.message };
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }


    // 获取训练列表
    async getTrainPlanListWithClass() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: false},
        // 排序
        columnKey: { type: 'string', required: false},
        order: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取查询条件
      const {year = undefined, columnKey = undefined, order = undefined} = ctx.query;

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.getTrainPlanListWithClass(year, columnKey, order);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练 通过训练码
    async getTrainPlanByCode() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练码
        trainCode: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response;

      const { trainCode } = ctx.query;
      try {
        response = await ctx.service.trainThroughTrainPlan.getTrainPlanByCode(trainCode);
      } catch(e) {
        ctx.body = { code: 500, message: e.message }
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };

    }

    // 从文件获取所需训练信息
    async getTrainContentFromQuestionFile() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        id: { type: 'string', required: false},
        // 训练ID
        trainPlanID: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.params); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.getTrainContentFromQuestionFile(ctx.params.id, ctx.params.trainPlanID);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async submitExam() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练ID
        trainPlanID: { type: 'number', required: true },
        // 总分
        studentAnswer: { type: 'object', required: false },

        trainID: { type: 'number', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const { trainPlanID, studentAnswer = {}, trainID, showAnswer = false } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      const schoolSlug = ctx.schoolSlug;

      let response;
      const userID = ctx.session.user.id;
      try {
        // TODO 检查时间
        response = await ctx.service.trainThroughTrainPlan.submitExam({ trainPlanID, schoolSlug, studentAnswer, userID, trainID, showAnswer }, transaction);
      } catch(e) {
        console.error(e, 'submitExam');
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async dynamicUpdateStudentRecord() {
      const { ctx } = this;

      // 校验参数
      const rule = {

        trainPlanID: { type: 'number', required: true },

        record: { type: 'object', required: false },

        trainID: { type: 'number', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const { trainPlanID, trainID, record = {} } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      const schoolSlug = ctx.schoolSlug;
      const userID = ctx.session.user.id;

      let response;
      try {
        response = await ctx.service.trainThroughTrainPlan.dynamicUpdateStudentRecord({ planID: trainPlanID, trainID, schoolSlug, studentAnswer: record, userID }, transaction);
      } catch(e) {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async judgeQuestionByID() {
      const { ctx } = this;

      // 校验参数
      const rule = {

        trainPlanID: { type: 'number', required: true },

        record: { type: 'object', required: false },

        trainID: { type: 'number', required: true },

        questionID: { type: 'number', required: true },

        UUID: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { session, schoolSlug } = ctx;
      if (!session || !session.user) {
        ctx.body = {code: 400, message: '请登录'};
        return;
      }

      const { user: { id: userID } } = session;

      // 获取更新数据
      const { trainPlanID, trainID, record = {}, questionID, UUID } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({ autocommit: false });

      let response;
      try {
        response = await ctx.service.trainThroughTrainPlan.judgeQuestionByID({ planID: trainPlanID, trainID, schoolSlug, studentAnswer: record, userID, questionID, UUID }, transaction);
      } catch(e) {
        console.error('judgeQuestionByID', e);
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async resetStudentAnswer() {
      const { ctx } = this;

      // 校验参数
      const rule = {

        trainPlanID: { type: 'number', required: true },

        record: { type: 'object', required: false },

        trainID: { type: 'number', required: true },

        questionID: { type: 'number', required: true },

        UUID: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { session, schoolSlug } = ctx;
      if (!session || !session.user) {
        ctx.body = {code: 400, message: '请登录'};
        return;
      }

      const { user: { id: userID } } = session;

      // 获取更新数据
      const { trainPlanID, trainID, record = {}, questionID, UUID } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({ autocommit: false });

      let response;
      try {
        response = await ctx.service.trainThroughTrainPlan.resetStudentAnswer({ planID: trainPlanID, trainID, schoolSlug, studentAnswer: record, userID, questionID, UUID }, transaction);
      } catch(e) {
        console.error('resetStudentAnswer', e);
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 报名训练
    async createTrainRecordWithStudent() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练ID
        planID: {type: 'string', required: true},

        trainID: {type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.params); 
      } catch(e) {
        console.log('eee', e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { planID } = ctx.params;
      const { trainID } = ctx.request.body;

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.createTrainRecordWithStudent(parseInt(planID, 10), trainID, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 停止训练计划
    async stopTrainPlan() {
      const { ctx } = this;
      const { ids } = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.stopTrainPlan(ids, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        console.error(e, 'stopTrainPlan');
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 延期
    async delayTrainPlan() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        planID: { type: 'number', required: true },
        delayTime: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.log('delayTrainPlan validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;

      try {
        const { planID, delayTime } = ctx.request.body;

        response = await ctx.service.trainThroughTrainPlan.delayTrainPlan({ planID, delayTime }, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'delayTrainPlan');
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改计划名称等配置
    async updateTrainPlan() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        planID: { type: 'number', required: true },
        name: { type: 'string', required: true },

        // 是否展示训练成绩
        ifShowScore: {type: 'number', required: false},
        // 是否展示试题批改结果
        ifShowCorrectionResults: {type: 'number', required: false},
        // 是否展示错题答案
        ifShowWrongAnswer: {type: 'number', required: false},
        // 是否允许收录⾄错题集
        ifSetWrongProblemCollection: {type: 'number', required: false},

        // 题目随机乱序
        ifRandomOrder: {type: 'number', required: false},

        // 是否禁止查看试卷
        ifForbidViewPaper: {type: 'number', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.log('updateTrainPlan validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;

      try {
        response = await ctx.service.trainThroughTrainPlan.updateTrainPlan(node, transaction);
      } catch(e) {
        ctx.body = {code: 500, message: e.message};
        console.error(e, 'updateTrainPlan');
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改训练记录
    async putTrainRecordWithStudent() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学生答题记录
        record: {type: 'object', required: true},

        trainID: {type: 'number', required: false},
        // 状态
        // status: {type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.log('eee', e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.trainThroughTrainPlan.putTrainRecordWithStudent(parseInt(ctx.params.id, 10), node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 训练分析列表
    async getTrainPlanStatisticsList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练id
        // trainID: { type: 'string', required: true },
        // 训练计划id
        trainPlanID: { type: 'string', required: false },
        // 班级id
        // classID: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getTrainPlanStatisticsList validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = null;
      try {
        const { trainID, trainPlanID, classID } = ctx.query;
        // response = await this.service.trainThroughTrainPlan.getTrainPlanStatisticsList({ trainID, trainPlanID, classID });
        response = await this.service.trainThroughTrainPlan.getTrainPlanStatisticsList({ trainPlanID });
      } catch(e) {
        console.log('getTrainPlanStatisticsList', e);
        ctx.body = { code: e.code, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 按题目获取统计
    async getQuestionStatistics() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练计划id
        trainPlanID: { type: 'string', required: true },
        // 班级id
        classIDs: { type: 'string', required: false },
        // 是否仅显示错误数据
        showError: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getQuestionStatistics validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = null;
      try {
        const { trainPlanID, classIDs, showError } = ctx.query;
        response = await this.service.trainThroughTrainPlan.getQuestionStatistics({ trainPlanID, classIDs, showError });
      } catch(e) {
        console.log('getQuestionStatistics error', e);
        ctx.body = { code: e.code, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 按知识点获取统计
    async getTagStatistics() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练计划id
        trainPlanID: { type: 'string', required: true },
        // 班级id
        classIDs: { type: 'string', required: false },
        // 是否仅显示错误数据
        showError: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getTagStatistics validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = null;
      try {
        const { trainPlanID, classIDs, showError } = ctx.query;
        response = await this.service.trainThroughTrainPlan.getTagStatistics({ trainPlanID, classIDs, showError });
      } catch(e) {
        console.log('getTagStatistics error', e);
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 按学生获取统计
    async getStudentStatistics() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练计划id
        trainPlanID: { type: 'string', required: true },
        // 班级ids
        classIDs: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getStudentStatistics validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = null;
      try {
        const { trainPlanID, classIDs } = ctx.query;
        response = await this.service.trainThroughTrainPlan.getStudentStatistics({ trainPlanID, classIDs });
      } catch(e) {
        console.log('getStudentStatistics error', e);
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async getClassList() {
      const { ctx } = this;

      let response = null;
      try {
        response = await ctx.service.trainThroughTrainPlan.getClassList();
      } catch(e) {
        console.log('getClassList error', e);
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 根据班级获取学生
    async getUserListByClasses() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 班级ids
        classIDs: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getUserListByClasses validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = null;
      try {
        const { classIDs } = ctx.query;
        response = await this.service.trainThroughTrainPlan.getUserListByClasses(classIDs);
      } catch(e) {
        console.log('getUserListByClasses error', e);
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async syncTrainPlans() {
      const { ctx } = this;

      let response = null;
      try {
        response = await ctx.service.trainThroughTrainPlan.syncTrainPlans();
      } catch(e) {
        console.log('syncTrainPlans error', e);
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // TEST 手动触发评卷
    async TESTstatistics() {
      const { ctx } = this;
      const { model, schoolSlug } = ctx;
      
       // 启用事务
      const transaction = await model.transaction({autocommit: false});

      const plans = [1];

      let response = null;
  
      // 更改考试状态
      try{
        // 获取需要修改状态的训练计划并修改，返回需要判分的train_user_record
        const { allNeedTrainPlanIDs, allNeedTrainPlanInfos } = await ctx.service.trainThroughTrainPlan.changeTrainStatus(plans, schoolSlug, transaction);
  
        if (allNeedTrainPlanInfos && allNeedTrainPlanInfos.length) {
          // 判分
          for(const row of allNeedTrainPlanInfos) {
            try {
              await ctx.service.trainThroughTrainPlan.submitExam(row, transaction);
            } catch(e) {
              if (e.message.indexOf('请勿重复提交')=== -1) {
                throw new Error(e);
              }
            }
          }
  
          // 统计
          for(const row of allNeedTrainPlanIDs) {
            response = await ctx.service.trainThroughTrainPlan.getTrainPlanStatistics(row, schoolSlug, transaction);
          }
        }
  
      } catch(e) {
        console.log(e, 'trainResult')
        await transaction.rollback();
        ctx.body = { code: 500, message: e.message };
        return;
      }
  
      await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 学生重做
    async redoTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // PlanID
        trainPlanID: {type: 'number', required: true },
        // trainID
        trainID: {type: 'number', required: false },

        correctMode: {type: 'boolean', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.log('redoTrain', e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        
        if (!ctx.session || !ctx.session.user) {
          throw new Error('请登录');
        }

        const { user } = ctx.session;
        const userID = user.id;

        // 创建训练
        response = await ctx.service.trainThroughTrainPlan.redoTrain({ ...node, userID }, transaction);
      }
      catch(e)
      {
        console.error(e, 'redotrain')
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取多个训练计划导出结果
    async getBulkTrainPlanStatistics() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练计划id
        trainPlanIDs: { type: 'string', required: true },
        needDetail: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getStudentStatistics validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = {};
      try {
        const trainPlanIDs = JSON.parse(ctx.query.trainPlanIDs)
        response = await ctx.service.trainThroughTrainPlan.getBulkTrainPlanStatistics(trainPlanIDs, ctx.query.needDetail!== 'false');
      } catch(e) {
        console.log(e, 'trainResult')
        // await transaction.rollback();
        ctx.body = { code: 500, message: e.message };
        return;
      }
  
      // await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 获取多个训练计划导出试题难度
    async getBulkTrainPlanStatisticsWithDifficulty() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练计划id
        trainPlanIDs: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('getStudentStatistics validation', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      let response = {};
      try {
        const trainPlanIDs = JSON.parse(ctx.query.trainPlanIDs)
        response = await ctx.service.trainThroughTrainPlan.getBulkTrainPlanStatisticsWithDifficulty(trainPlanIDs);
      } catch(e) {
        console.log(e, 'trainResult')
        // await transaction.rollback();
        ctx.body = { code: 500, message: e.message };
        return;
      }
  
      // await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 实现增加填空题参考答案，并重新计算学生本题得分，并更新学生总分
    async updateFillBlankAnswer() {
      // 校验用户权限
      const { ctx } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return;
      }

      const { isAdmin, adminAuthority } = user;
      if(!isAdmin || !adminAuthority.train) {
        ctx.body = {code: 400, data, message: "无管理员权限"};
        return;
      }

      // 校验参数
      const rule = {
        // 训练计划id
        trainPlanID: { type: 'string', required: true },
        // 训练id
        trainID: { type: 'integer', required: true },
        // 题目id
        questionID: { type: 'integer', required: true },
        // 填空项
        key: { type: 'string', required: true },
        // 新增的参考答案
        addAnswers: { type: 'array', required: true, itemType: 'string' },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.log('updateFillBlankAnswer检测入参失败', e);
        ctx.body = { code: 400, message: e.errors };
        return;
      }

      const { trainPlanID, trainID, questionID, key, addAnswers } = ctx.request.body;
      // 启动事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = {};
      try {
        response = await ctx.service.trainThroughTrainPlan.updateFillBlankAnswer(trainPlanID, trainID, questionID, key, addAnswers, transaction);
        await transaction.commit();
      } catch(e) {
        await transaction.rollback();
        console.log('updateFillBlankAnswer error', e);
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }
  }

  return TrainThroughTrainPlanController;
}