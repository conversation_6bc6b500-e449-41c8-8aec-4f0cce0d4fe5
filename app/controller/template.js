'use strict';

module.exports = app => {
  class TemplateController extends app.Controller {
    // 创建训练
    async createTemplate() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 模版名称
        name: {type: 'string', required: true},
        // 模版内容
        content: {type: 'object', required: true},
        // 时长
        duration: {type: 'number', required: true},
        // 总分
        score: {type: 'number', required: true},
        // 难度配置
        templateDifficulty: { type: 'object', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.createUserID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.template.createTemplate(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改训练
    async putTemplate() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 模版名称
        name: {type: 'string', required: false},
        // 模版内容
        content: {type: 'object', required: false},
        // 时长
        duration: {type: 'number', required: false},
        // 总分
        score: {type: 'number', required: false},
        // 难度配置
        templateDifficulty: { type: 'object', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        response = await this.service.template.putTemplate(ctx.params.id, node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练
    async getTemplate() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练
        response = await this.service.template.getTemplate(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除训练
    async destoryTemplate() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练
        response = await this.service.template.destoryTemplate(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练列表
    async getTemplateList() {
      const { ctx } = this;

      let response;
      try
      {
        // 创建训练
        response = await this.service.template.getTemplateList();
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }
  }

  return TemplateController;
}