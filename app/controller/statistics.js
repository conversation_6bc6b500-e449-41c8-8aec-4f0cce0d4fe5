
module.exports = app => {
  class StatisticsController extends app.Controller {
    // 训练 数据管理 获取媒体资源
    async getTrainPlanStatisticsData() {
      const {ctx} = this;
      const { trainPlanID } = ctx.params;

      let response = null;
      try {
        response = await ctx.service.statistics.getTrainPlanStatisticsData(parseInt(trainPlanID));
      } catch(e) {
        ctx.body = { code: 1, message: e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};
    }
  }

  return StatisticsController
}