'use strict';

module.exports = app => {
  class ICourseController extends app.Controller {
    // 请求获取交互式课程Kernel分配
    async requestKernelAllocate() {
      // 入参检查
      const { ctx } = this;

      const rule = {
        // 课程标识
        courseSlug: {type: 'string', required: true},
        // 章名称
        chapterName: {type: 'string', required: true},
        // 节名称
        sectionName: {type: 'string', required: true},
        // 页面客户端ID
        pageClientID: {type: 'string', required: true},
      }

      ctx.validate(rule);

      // 权限检查，需已登录用户
      const { schoolSlug, session, request } = ctx;
      const { user } = session;
      if(!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      // 加载参数
      // 班级和用户ID
      const { teamIDs, id: userID } = user;

      // 课程、章、节名称
      const { courseSlug, chapterName, sectionName, pageClientID } = request.body;

      // 容器Name = i_学校标记_课程标记
      const containerName = ['i', schoolSlug, courseSlug].join('-');

      // 检查权限
      try {
        // 检查权限并获取容器配置
        const { courseName, currentSection: section, containerInfo } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

        // 课节类型必须为交互式课程
        const { sectionType } = section;
        if(sectionType !== 'AI') {
          throw new Error(`课节类型为${sectionType}，必须为交互式课程(AI)`);
        }

        // 向Icourse-worker发起生成Kernel请求
        await ctx.service.icourse.requestKernelAllocate(containerName, schoolSlug, courseName, courseSlug, chapterName, sectionName, pageClientID, section, containerInfo, userID);

        // 记录所占用资源，在页面连接断开（关闭页面、关闭Kernel、刷新页面）时主动发起释放资源
        await ctx.service.faye.addPageResourceRecord(pageClientID, 'icourse_kernel', {
          containerName, chapterName, sectionName
        });
      }
      catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      // 返回成功消息
      ctx.body = { code: 0, data: containerName };
    }

    // 请求获取交互式课程Kernel释放
    async requestKernelFree() {
      // 入参检查
      const { ctx } = this;

      const rule = {
        // 课程标识
        courseSlug: {type: 'string', required: true},
        // 章名称
        chapterName: {type: 'string', required: true},
        // 节名称
        sectionName: {type: 'string', required: true},
        // 页面客户端ID
        pageClientID: {type: 'string', required: true},
      }

      ctx.validate(rule);

      // 权限检查，需已登录用户
      const { schoolSlug, session, request } = ctx;
      const { user } = session;
      if(!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      // 加载参数
      // 班级和用户ID
      const { teamIDs, id: userID } = user;

      // 课程、章、节名称
      const { courseSlug, chapterName, sectionName, pageClientID } = request.body;

      // 容器Name = i_学校标记_课程标记
      const containerName = ['i', schoolSlug, courseSlug].join('-');

      // 检查权限
      try {
        // 检查权限并获取容器配置
        const { currentSection: section } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);

        // 课节类型必须为交互式课程
        const { sectionType } = section;
        if(sectionType !== 'AI') {
          throw new Error(`课节类型为${sectionType}，必须为交互式课程(AI)`);
        }

        // 向Icourse-worker发起释放Kernel请求
        await ctx.service.icourse.requestKernelFree(containerName, chapterName, sectionName, pageClientID);

        // 删除所占用资源记录
        await ctx.service.faye.removePageResourceRecrod(pageClientID, 'icourse_kernel');
      }
      catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      // 返回成功消息
      ctx.body = { code: 0, message: '已受理，请等待Kernel开通消息！' };
    }

    // 清理容器
    async clearContainer() {
      const { ctx } = this;
      const { schoolSlug = null, courseSlug = null, exitMessage = null } = ctx.query;
      await ctx.service.icourse.clearContainer(schoolSlug, courseSlug, exitMessage);
      ctx.body = {code: 0};
    }
  }

  return ICourseController;
}
