'use strict';
const { getFPSJudgeQuestion } = require('../utils/fps');
const fs = require('mz/fs');

async function getProcessedData(data) {
  const { judgeMenu, memoryLimit, timeLimit} = data.questionDetail;

  // 处理 testFiles
  const testFiles = judgeMenu.testFiles.map((testFile) => {
    const { in: inPath, out: outPath, ...fileRest } = testFile;

    // 返回处理后的文件路径
    return {
      ...fileRest,
      inSrc: inPath, // 直接使用输入文件路径
      outSrc: outPath, // 直接使用输出文件路径
    };
  });

  // 返回处理后的数据
  return {
    cpuSecondsLimit: timeLimit, // 设置时间限制
    memoryMegaBytesLimit: memoryLimit, // 设置内存限制
    spjJudgers: [], // 没有特殊判题器
    questions: testFiles, // 将处理后的 testFiles 作为 questions
    sourceFileList: [], // 没有额外的源文件
  };
}
/**
 * 从文件路径读取内容
 * @param {string} filePath 文件路径
 * @returns {string} 文件内容
 */
async function readFileContent(filePath) {
  try {
    return fs.readFileSync(filePath, { encoding: 'utf-8' });
  } catch (error) {
    console.error(`无法读取文件 ${filePath}:`, error.message);
    return null;
  }
}

module.exports = app => {
  class EditorOjController extends app.Controller {
    async testrun() {
      const { ctx } = this;

      const { session } = ctx;
      // 收到请求指令
      const command = ctx.request.body;

      if (!command.clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 提交至队列，各分片轮询
      const queue = app.queue['runner'];
      await queue.add(command, { 
        removeOnComplete: true 
      });

      ctx.body = { code: 0, data: { status: '已提交，请开启监听' } };
    }


    async judge() {
      // 获取当前时间
      const nowTime = new Date().getTime();
      const { ctx } = this;
      const { session } = ctx;
      const { config } = app;
      // 收到请求指令
      const command = ctx.request.body;

      if (!command.clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 从会话中获取schoolSlug，isAdmin和userID
      const { user } = session;
      const { id: userID, schoolSlug, isAdmin, teamIDs } = user;

      // 取一下Redis实例
      const redis = app.redis.get('judge');
 
      command.teamIDs = teamIDs;
      command.userID = userID;

      // 如果是非管理员用户，通过提交锁机制，限制其过快提交
      if(!isAdmin) {
        const submitLockKey = `s_${schoolSlug}_u_${userID}_sl`;

        // 判断该用户两次提交间隔是否大于15秒
        const lastSubmitTime = await redis.get(submitLockKey);
        if(lastSubmitTime) {
          ctx.body = { code: 0, data: {status: 429, message: '两次提交间隔应大于5秒哦！' }, message: '请查看status'};
          return;
        }

        // 未大于15秒，设定提交锁必须超过15秒方可提交
        await redis.set(submitLockKey, nowTime, 'EX', 5);
      }

      // 析出分析权限用到的参数
      const { solutionType, state, language } = command;

      // 增加学校信息
      state.schoolSlug = schoolSlug;

      // 根据业务类型，讨论权限和FSP文件地址
      let fspFilePath = null;
      let questionDetail = null;
      switch(solutionType) {
        case 'course':
          {
            const { courseSlug, chapterName, sectionName } = state;

            // 获取课程信息并检查权限
            try {
              const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
              const { sectionID, sectionType } = currentSection;
              // 判断课程类型是否正确
              if (sectionType !== 'OJ') {
                throw new Error(`课程类型${sectionType}错误！`);
              }

              // 记录配置
              state.sectionID = sectionID;
            }
            catch(e) {
              const { message } = e;
              ctx.body = { code: 403, message};
              return;
            }

            // 构成FPS文件位置
            fspFilePath = `${config.file.dir}/${schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.xml`;
          }
          break;
        case 'train':
          {
            // 数据库查询
            const { questionID } = state;
            const transaction = await ctx.model.transaction({autocommit: false});
            
            try {
              questionDetail = await ctx.service.train.questionInfo(questionID, transaction);
            }
            catch(e) {
              const { message } = e;
              ctx.body = { code: 403, message};
              return;
            }
            fspFilePath = `${questionID}`;
          }
          break;
        default:
          ctx.body = { code: 500, message: `尚未支持的类型${solutionType}`};
          return;
      }

      // 文件是否有缓存，有缓存，自缓存直接返回
      const fpsKey = `q_${fspFilePath}`;

      let fpsJSON = await redis.get(fpsKey);
      switch(solutionType) {
        case 'course':
          {
            if(fpsJSON) {
              command.fps = JSON.parse(fpsJSON);
            } else {
              try {
                // 没有缓存，则加载FPS格式XML文件，读取CPU、内存限制，读取SPJ列表和判决文件列表
                command.fps = await getFPSJudgeQuestion(fspFilePath, language);
                fpsJSON = JSON.stringify(command.fps);
                // 缓存两分钟
                await redis.set(fpsKey, fpsJSON, 'EX', 60 * 2);
              } catch(e) {
                console.error(e);
                ctx.body = { code: 500, message: e.message };
                return;
              }
              
            }
            // console.log(fpsJSON, 'fpsJSON')
            if (fpsJSON === '{}') {
              ctx.body = { status: 500, message: `获取不到文件`};
              return;
            }
          }
          break;
        case 'train': 
          {
            command.state.solutionType = "course";
            if(fpsJSON) {
              command.fps = JSON.parse(fpsJSON);
            } else {
              try {
                // 没有缓存，则加载FPS格式XML文件，读取CPU、内存限制，读取SPJ列表和判决文件列表
                command.fps = await getProcessedData(questionDetail);
                fpsJSON = JSON.stringify(command.fps);
                // 缓存两分钟
                await redis.set(fpsKey, fpsJSON, 'EX', 60 * 2);
              } catch(e) {
                console.error(e);
                ctx.body = { code: 500, message: e.message };
                return;
              }
            }

          }
          break;
        default:
          ctx.body = { code: 500, message: `尚未支持的类型${solutionType}`};
          return;
      }
      // 提交至队列
      const queue = app.queue['judger'];
      await queue.add(command, { 
        removeOnComplete: true 
      });

      ctx.body = { code: 0 };
    }
  }

  return EditorOjController;
}