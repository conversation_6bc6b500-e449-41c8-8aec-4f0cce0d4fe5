'use strict';
const moment = require('moment');
const md5 = require('md5');
const { Hmac } = require('../utils/crypto');

module.exports = app => {
  const mainRedis = app.redis.get('session');

  class SSOController extends app.Controller {
    
    // 获取班级树
    async getClassTree() {
      const { ctx, service } = this;

      if (!ctx.session.user) {
        ctx.body = { code: 1, message: '请先授权' };
        return;
      }

      const { user } = ctx.session;

      const { VerificationCode, PageIndex = 1, PageSize = 100 } = ctx.query;
      let yearDatas = [];
      let SchoolId = null;
      let TeacherId = null;
      // 调用接口
      try {
        const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

        if (!deploymentSlug) {
          throw new Error('当前学校未配置单点登录，请检查系统地址');
        }

        switch(deploymentSlug) {
          case 'hxr.jsnje.cn':
          // case 'hxr.i51cy.com':
            yearDatas = await service.sso.getClassTreeNje({ user, VerificationCode, PageIndex, PageSize });
            break;
          case 'hxr.i51cy.com':
          case 'ai.ksedu.cn':
            const { classDatas, SchoolId: ksSchool, TeacherId: ksTeacherId } = await service.sso.getClassTreeKS({ user, PageIndex, PageSize });
            yearDatas = classDatas;
            SchoolId = ksSchool;
            TeacherId = ksTeacherId;
            break;
          case 'njyz':
            break;
          default:
            console.error(`不支持的单点登录 ${deploymentSlug}`);
            break;
        }
      } catch(e) {
        console.log('getClassTree', e)
        ctx.body = {code: 500, message: e.message};
        return;
      }
      ctx.body = { code: 0, data: yearDatas, SchoolId, TeacherId, message: "成功!" };
    }

    async getTeacherClassList() {
      const { ctx } = this;

      if (!ctx.session.user) {
        ctx.body = { code: 1, message: '请先授权' };
        return;
      }
      
      const { SchoolId, TeacherId } = ctx.query;
      let list = [];
      // 调用接口
      try {

        list = await ctx.service.sso.getTeacherClassList({ SchoolId, TeacherId });
        
      } catch(e) {
        console.log('getTeacherClassList', e)
        ctx.body = {code: 500, message: e.message};
        return;
      }
      ctx.body = { code: 0, data: list, message: "成功!" };
    }

    async getSSOUrl() {
      const { ctx } = this;

      const { sso: allSSO } = app.config;
      const { schoolSlug } = ctx;
      
      const { field, ssoSlug } = ctx.query;

      let res = null;
      // 调用接口
      try {
        res = await ctx.service.sso.getSSOLoginUrl({ allSSO, schoolSlug, field, ssoSlug });
      } catch(e) {
        console.log('getSSOUrl', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }
      ctx.body = { code: 0, data: res, message: "成功!" };
    }

    // 检查是否能查询到已有账号
    async checkSSOExist() {
      const { ctx, service } = this;
      const { session, schoolSlug } = ctx;

      let response = null;

      const { UserName: checkName, UserType, OrgCode, finalLogin, field, schoolID } = ctx.query;

      try {
        // 绑定教师账号
        const user = await service.sso.checkSSOExist(checkName, UserType, schoolID);

        if (!user) {
          ctx.body = { code: 0, data: null };
          return;
        }

        if (finalLogin) {
          await ctx.service.user.removeUselessClients(user.id);
        }

        // 如果不是已经在线 或 正在强制登录，应当写入sesion
        if(!user.signIn || finalLogin || !field) {
          session.user = {
            id: user.id,
            username: user.username,
            ssoName: user.ssoName,
            OrgCode,
            name: user.name,
            isAdmin: user.adminAuthority? 1 : 0,
            avatar: user.avatar,
            adminAuthority: user.adminAuthority,
            lastInteractionAt: (new Date()).getTime(),
            schoolSlug: schoolSlug,
            teamIDs: user.teamIDs || [], // 用户所属班级
            openID: user.openID,
            wechatInfo: user.wechatInfo,
            permissionData: user.permissionData,
            permission: user.permission,
          };
        }

        // // 重置csrf
        // ctx.rotateCsrfSecret();

        response = user.dataValues || user;
      } catch(e) {
        console.log('checkSSOExist', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    // 关联或新建账号
    async bindHxrAccount() {
      const { ctx, service } = this;

      // 校验参数
      const rule = {
        UserName: {type: 'string', required: true},
        bindName: {type: 'string', required: true},
        newPassword: {type: 'string', required: true},
        type: {type: 'string', required: true},
        schoolID: { type: 'string', required: false },
      };

      ctx.validate(rule);

      const transaction = await ctx.model.transaction({ autocommit: false })
      let response = null;

      const { bindName, UserName, RealName, schoolID, OrgCode, UserType, newPassword, Email, QQ, type } = ctx.request.body;

      try {
        // 绑定教师账号
        if (type === 'bind') {
          await service.sso.bindHxrAccount({ bindName, UserName, schoolID, OrgCode, UserType, newPassword, Email, QQ }, transaction);
        }

        if (type === 'create') {
          await service.sso.createHxrAccount({ bindName, UserName, RealName, schoolID, OrgCode, UserType, newPassword, Email, QQ }, transaction);
        }

      } catch(e) {
        console.log('bindHxrAccount', e)
        await transaction.rollback();
        ctx.body = { code: 500, message: e.message };
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = { code: 0, data: response, message: "成功!" };
    } 

    async bindHxrAccountAuto() {
      const { ctx, service } = this;
      const { session, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        UserName: {type: 'string', required: true},
      };

      ctx.validate(rule);

      const transaction = await ctx.model.transaction({ autocommit: false })
      let response = null;

      const { UserName, RealName, UserType, Email, QQ, OrgCode, finalLogin, field } = ctx.request.body;

      try {
        // 在第三方平台验证账号有效性
        await ctx.service.sso.checkUserKS(UserName);
        
        // 绑定账号
        let user = await service.sso.bindHxrAccountAuto({ UserName, RealName, UserType, Email, QQ, OrgCode, field }, transaction);
        if (!user) {
          ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
          return;
        }

        if (finalLogin) {
          await ctx.service.user.removeUselessClients(user.id);
        }

         // 获取首页管理的弹出公告
        let noticeData = await service.systemConfig.getNoticeData();

        // 如果不是已经在线 或 正在强制登录，应当写入sesion
        if(!user.signIn || finalLogin || field === 'admin') {
          session.user = {
            id: user.id,
            username: user.username,
            ssoName: user.ssoName,
            OrgCode,
            name: user.name,
            isAdmin: user.adminAuthority? 1 : 0,
            avatar: user.avatar,
            adminAuthority: user.adminAuthority,
            lastInteractionAt: (new Date()).getTime(),
            schoolSlug: schoolSlug ? schoolSlug : this.ctx.schoolSlug,
            teamIDs: user.teamIDs || [], // 用户所属班级
            openID: user.openID,
            wechatInfo: user.wechatInfo,
            permissionData: user.permissionData,
            permission: user.permission,
          };
        }

        // 重置csrf
        ctx.rotateCsrfSecret();

        user = user.dataValues || user;
        response = { user, noticeData };

      } catch(e) {
        console.log('bindHxrAccount', e)
        await transaction.rollback();
        ctx.body = { code: 500, message: e.message };
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      // 部署标记
      const { deploymentSlug } = app.config.hxr;
      ctx.body = { code: 0, data: response, csrfToken: session.csrfToken, deploymentSlug, message: "成功!" };
    }

    async getVerificationCode() {
      const { ctx, service } = this;

      let response = null;

      const { UserName: checkName, UserType, OrgCode } = ctx.query;

      try {
        // 绑定教师账号
        response = await service.sso.getVerificationCode(checkName, OrgCode, UserType);

      } catch(e) {
        console.log('getVerificationCode', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async getStudentsByClassID() {
      const { ctx, service } = this;

      let response = null;

      const { VerificationCode, selectPullClassIDs, SchoolId, Page = 1, PageSize = 10 } = ctx.request.body;

      try {
        const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

        if (!deploymentSlug) {
          throw new Error('当前学校未配置单点登录，请检查系统地址');
        }

        // 获取学生列表
        switch(deploymentSlug) {
          case 'hxr.jsnje.cn':
          // case 'hxr.i51cy.com':
            response = await service.sso.getStudentsByClassID({ VerificationCode, selectPullClassIDs });
            break;
          case 'hxr.i51cy.com':
          case 'ai.ksedu.cn':
            response = await service.sso.getStudentsByClassIDKS({ selectPullClassIDs, SchoolId, Page, PageSize });
            break;
          default:
            break;
        }

        // response = [
        //   {
        //     "classID": "d2679a6a-0754-484c-926b-0057a2285bce",
        //     "students": [
        //       {
        //         index: 1,
        //         "Id": "57cbc830-6450-472c-8aa0-e3d10949acd7",
        //         "XSZH": "cebaceba205x",
        //         "ZZJGM": "32010081999",
        //         "OrgName": "直属测试学校",
        //         "XM": "测吧测吧",
        //         "XBM": "男",
        //         "SFZJLXM": "1",
        //         "SFZJLXMName": "居民身份证",
        //         "SFZJH": "1101***********05X",
        //         "JZSJHM": "198*****058",
        //         "BJBH": "8C4B6BE4-3CFC-4B12-A3F1-1FC70B2A19BF",
        //         "XDM": "2",
        //         "XDMName": "初中",
        //         "NJ": "2013",
        //         "NJName": "九年级",
        //         "BJ": "6"
        //       }
        //     ]
        //   },
        //   {
        //     "classID": "d2679a6a-0754-484c-926b-1111111111111",
        //     "students": []
        //   },
        //   {
        //     "classID": "d2679a6a-0754-484c-926b-22222222222",
        //     "students": []
        //   },
        //   {
        //     "classID": "4b82333b-427f-4446-a533-04e6f8500359",
        //     "students": []
        //   } 
        // ];

      } catch(e) {
        console.log('getStudentsByClassID', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async getStudentsByClassIDSingleKS() {
      const { ctx } = this;

      let response = null;

      const { classID, SchoolId, Page = 1, PageSize = 10 } = ctx.request.body;

      try {
        response = await ctx.service.sso.getStudentsByClassIDSingleKS({ classID, SchoolId, Page, PageSize });
      } catch(e) {
        console.log('getStudentsByClassIDSingleKS', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async importClassAndStudents() {
      const { ctx, service } = this;

      const transaction = await ctx.model.transaction({ autocommit: false });
      let response = null;

      const { selectClasses, studentPassword } = ctx.request.body;

      let currentYear = '2021学年';
      if (moment().format('YYYYMM') > `${moment().format('YYYY')}07`) {
        currentYear = parseInt(moment().format('YYYY'));
      } else {
        currentYear = parseInt(moment().format('YYYY')) - 1;
      }

      try {
        const deploymentSlug = await this.ctx.service.sso.getSSOConfig(transaction);

        if (!deploymentSlug) {
          throw new Error('当前学校未配置单点登录，请检查系统地址');
        }

        // 上传班级和学生
        response = await service.sso.importClassAndStudentsNje({ selectClasses, currentYear, deploymentSlug, studentPassword }, transaction);

      } catch(e) {
        console.log('importClassAndStudents', e)
        await transaction.rollback();
        ctx.body = { code: 500, message: e.message };
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async getAllSchoolKS() {
      const { ctx } = this;

      let response = null;

      try {
        response = await ctx.service.sso.getAllSchoolKS();
      } catch(e) {
        console.log('getAllSchoolKS', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    /* ======================单点登录 njyz ======================== */

    async getAllSchoolNJYZ() {
      const { ctx } = this;

      let response = null;

      try {
        response = await ctx.service.sso.getAllSchoolNJYZ();
      } catch(e) {
        console.log('getAllSchoolNJYZ', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async getTeacherInfo() {
      const { ctx } = this;

      let response = null;

      const { userID, schoolID } = ctx.query;

      try {
        response = await ctx.service.sso.getTeacherInfo({ userID, schoolID });
      } catch(e) {
        console.log('getTeacherInfo', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async getStudentInfo() {
      const { ctx } = this;

      let response = null;

      const { userID, schoolID } = ctx.query;

      try {
        response = await ctx.service.sso.getStudentInfo({ userID, schoolID });
      } catch(e) {
        console.log('getStudentInfo', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    async getTeacherClassListNJYZ() {
      const { ctx } = this;

      let response = null;

      if (!ctx.session.user) {
        ctx.body = { code: 400, message: '请先登录' };
        return;
      }

      const { user } = ctx.session;

      try {
        response = await ctx.service.sso.getTeacherClassListNJYZ({ user });
      } catch(e) {
        console.log('getTeacherClassListNJYZ', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }

    // 获取班级学生列表
    async getStudentsByClassIDNJYZ() {
      const { ctx } = this;

      let response = null;

      if (!ctx.session.user) {
        ctx.body = { code: 400, message: '请先登录' };
        return;
      }

      const { user } = ctx.session;

      const { selectPullClassIDs, Page = 1, PageSize = 10 } = ctx.request.body;

      try {
        response = await ctx.service.sso.getStudentsByClassIDNJYZ({ user, selectPullClassIDs, Page, PageSize });
      } catch(e) {
        console.log('getStudentsByClassIDNJYZ', e)
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, data: response, message: "成功!" };
    }
  }

  return SSOController;
};
