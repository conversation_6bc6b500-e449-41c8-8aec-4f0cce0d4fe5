'use strict';
const fs = require("mz/fs");
const sendToWormhole = require('stream-wormhole');

module.exports = app => {
  class TrainController extends app.Controller {
    // 训练 数据管理 获取媒体资源
    async getMediaResources() {
      const {ctx} = this;
      const { trainID } = ctx.params;

      let response = null;
      try {
        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/train/${trainID}/assets`;
        response = await ctx.service.train.list(pathDir);
      } catch(e) {
        ctx.body = { code: 1, message: e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 删除资源文件
    async deleteSourceFile() {
      const { ctx } = this;
      const { user } = ctx.session;

      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const {trainID, name} = ctx.request.body;
        
      if(name.match(/\.\./)) {
        ctx.body = {code: 500, message: "非法的路径！"};
        return;
      }
      
      let response = null;

      try
      {
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/train/${trainID}/assets/${name}`;
        response = await ctx.service.train.deleteSourceFile(path);
      }
      catch(e) {
        ctx.body = {code: 500, data: e.message, message: "发生服务器错误!"};
        return;
      }

      if(response){
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        ctx.body = {code: 0, message: '没有数据'};
      }
    }

    // 训练 媒体资源 下载数据
    async downMediaResources() {
      const {ctx } = this;

      try{
        const { trainID, fileName } = ctx.params;
        const { path } = ctx.query;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/train/${trainID}/assets/${path ? path : fileName}`;
        const exist = await fs.exists(filePath);

        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.body = await fs.createReadStream(filePath);
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 训练资源文件上传
    async trainAssetsUpload() {
      const { ctx, service } = this;
      const { trainID, isCover } = ctx.query;

      // 获取文件流 
      let url = null;
      try {
        const stream = await ctx.getFileStream();
        url = await service.train.trainAssetsUpload(stream, trainID, '/assets', isCover);
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        console.error(err);
        return;
      }
      // if(url) {
      //   ctx.body = {code: 500, data: url, message: "失败!"};
      // } else {
      //   ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
      // }
      ctx.body = {code: 0, data: url, message: "成功!"};
    }

    // 训练题目资源文件上传
    async trainQuestionAssetsUpload() {
      const { ctx, service } = this;
      const { questionID, isCover } = ctx.query;

      let url = null;
      try {
        // 获取文件流
        const stream = await ctx.getFileStream();
        url = await service.train.trainQuestionAssetsUpload(stream, questionID, '/assets', isCover);
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        console.error(err, 'trainQuestionAssetsUpload')
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }
      // if(url) {
      //   ctx.body = {code: 500, data: url, message: "失败!"};
      // } else {
      //   ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
      // }
      ctx.body = {code: 0, data: url, message: "成功!"};
    }

    // 训练题目 数据管理 获取媒体资源
    async getTrainQuestionMediaResources() {
      const {ctx} = this;
      const { questionID } = ctx.params;

      let response = null;
      try {
        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets`;
        response = await ctx.service.train.list(pathDir);
      } catch(e) {
        ctx.body = { code: 1, message: e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除资源文件
    async deleteTrainQuestionSourceFile() {
      const { ctx } = this;
      const { user } = ctx.session;

      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const {questionID, name} = ctx.request.body;
        
      if(name.match(/\.\./)) {
        ctx.body = {code: 500, message: "非法的路径！"};
        return;
      }
      
      let response = null;

      try
      {
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets/${name}`;
        response = await ctx.service.train.deleteSourceFile(path);
      }
      catch(e) {
        ctx.body = {code: 500, data: e.message, message: "发生服务器错误!"};
        return;
      }

      if(response){
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        ctx.body = {code: 0, message: '没有数据'};
      }
    }

    // 训练 媒体资源 下载数据
    async downTrainQuestionMediaResources() {
      const {ctx } = this;

      try{
        const { questionID, fileName } = ctx.params;
        const { path } = ctx.query;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets/${path ? path : fileName}`;
        const exist = await fs.exists(filePath);

        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.body = await fs.createReadStream(filePath);
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 训练封面上传
    async trainCoverUpload() {
      const { ctx, service } = this;
      const { trainID } = ctx.query;

      // 获取文件流
      const stream = await ctx.getFileStream();
      let url = null;
      try {
        stream.filename = 'cover.png';
        url = await service.train.trainAssetsUpload(stream, trainID, '/assets', true);
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }
      // if(url) {
      //   ctx.body = {code: 500, data: url, message: "失败!"};
      // } else {
      //   ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
      // }
      ctx.body = {code: 0, data: url, message: "成功!"};
    }

    // 创建训练
    async createTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 训练名称
        name: { type: 'string', required: true},
        // 训练简介/备注
        abstract: { type: 'string', required: false},
        // 封面
        cover: { type: 'string', required: false},
        // 学年
        year: { type: 'string', required: true},
        // 训练结构
        templateName: { type: 'string', required: true},
        // 训练模版
        template: { type: 'object', required: true},
        // 时长
        duration: {type: 'number', required: false},
        // 总分
        score: {type: 'number', required: false},
        // 注意事项
        notice: {type: 'string', required: false},
        // 难度
        difficulty: {type: 'number', required: false},
        // 是否展示训练成绩
        ifShowScore: {type: 'number', required: false},
        // 是否展示试题批改结果
        ifShowCorrectionResults: {type: 'number', required: false},
        // 是否展示错题答案
        ifShowWrongAnswer: {type: 'number', required: false},
        // 是否允许收录⾄错题集
        ifSetWrongProblemCollection: {type: 'number', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 难度配置
        templateDifficulty: { type: 'object', required: false},

        // 系列
        series: { type: 'number', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.createUserID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.train.createTrain(node, transaction);
        const { config } = app;
        await this.service.file.mkdirs(`${config.file.dir}/${this.ctx.schoolSlug}/train/${response.id}/assets/`);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 创建训练
    async createTrains() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        trains: {
          type: 'array',
          itemType: 'object',
          rule: {
            // 训练名称
            name: { type: 'string', required: true},
            // 训练简介/备注
            abstract: { type: 'string', required: false},
            // 封面
            cover: { type: 'string', required: false},
            // 学年
            year: { type: 'string', required: true},
            // 训练结构
            templateName: { type: 'string', required: true},
            // 训练模版
            template: { type: 'object', required: true},
            // 训练模版
            content: { type: 'object', required: false},
            // 时长
            duration: {type: 'number', required: false},
            // 总分
            score: {type: 'number', required: false},
            // 注意事项
            notice: {type: 'string', required: false},
            // 难度
            difficulty: {type: 'number', required: false},
            // 是否展示训练成绩
            ifShowScore: {type: 'number', required: false},
            // 是否展示试题批改结果
            ifShowCorrectionResults: {type: 'number', required: false},
            // 是否展示错题答案
            ifShowWrongAnswer: {type: 'number', required: false},
            // 是否允许收录⾄错题集
            ifSetWrongProblemCollection: {type: 'number', required: false},
            // 是否完整
            isFinish: {type: 'number', required: false},
            // 状态
            status: {type: 'string', required: false},
            // 难度配置
            templateDifficulty: { type: 'object', required: false},
            // 系列
            series: { type: 'number', required: true},
          }
        }
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        const nodes = [];
        for (const row of ctx.request.body.trains) {
          // 获取更新数据
          const requestBody = row;
          const node = {};
          for(const key in rule.trains.rule) {
            const value = requestBody[key];
            if(value === undefined) {
              continue;
            }
    
            node[key] = value;
          }
    
          node.createUserID = ctx.session.user.id;

          nodes.push(node)
        }
        // console.log('nodes:',nodes);
        // 创建训练
        response = await this.service.train.createTrains(nodes, transaction);
        const { config } = app;
        for(const row of response) {
          // console.log(row.id)
          await this.service.file.mkdirs(`${config.file.dir}/${this.ctx.schoolSlug}/train/${row.id}/assets/`);
        }
      }
      catch(e)
      {
        console.error(e)
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改训练
    async putTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        id: {type: 'number', required: false},
        // 训练名称
        name: { type: 'string', required: false},
        // 封面
        cover: { type: 'string', required: false},
        // 训练简介/备注
        abstract: { type: 'string', required: false},
        // 学年
        year: { type: 'string', required: false},
        // 分数
        score: { type: 'number', required: false },
        // 注意事项
        notice: {type: 'string', required: false},
        // 难度
        difficulty: {type: 'number', required: false},
        // 区分度
        discriminative: {type: 'number', required: false},
        // 试卷内容
        content: {type: 'object', required: false},
        // 是否展示训练成绩
        ifShowScore: {type: 'number', required: false},
        // 是否展示试题批改结果
        ifShowCorrectionResults: {type: 'number', required: false},
        // 是否展示错题答案
        ifShowWrongAnswer: {type: 'number', required: false},
        // 是否允许收录⾄错题集
        ifSetWrongProblemCollection: {type: 'number', required: false},
        // 状态
        status: {type: 'string', required: false},

        isFinish: {type: 'boolean', required: false},
        
        template: { type: 'object', required: false},
        // 难度配置
        templateDifficulty: { type: 'object', required: false},

        // 分享教师
        teachers: { type: 'object', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        const { id } = ctx.request.body;
        response = await this.service.train.putTrain(id, node, transaction);
      }
      catch(e)
      {
        console.error(e)
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改训练题目引用情况
    async replaceQuestionInTrains() {
      const { ctx } = this;
      // 获取更新数据
      const requestBody = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        console.log(requestBody);
        // ctx.body = {code: 1, message: '成功', data: '测试'};
        // return;
        response = await this.service.train.replaceQuestionInTrains(requestBody, transaction);
        console.log('去重成功',response);
      }
      catch(e)
      {
        console.error(e)
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 记录训练修改情况
    async recordTrainUpdate() {
      const { ctx } = this;
      // 获取更新数据
      const requestBody = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        console.log(requestBody);
        // ctx.body = {code: 1, message: '成功', data: '测试'};
        // return;
        response = await this.service.train.recordTrainUpdate(requestBody, transaction);
      }
      catch(e)
      {
        console.error(e)
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async putTrainTemplate() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        id: { type: 'number', required: true },

        templateName: { type: 'string', required: true },
        
        template: { type: 'object', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练
        const { id } = ctx.request.body;
        response = await this.service.train.putTrainTemplate(id, node, transaction);
      }
      catch(e)
      {
        console.error(e)
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 创建训练
    async copyTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        id: {type: 'number', required: true},
        // 训练名称
        name: { type: 'string', required: true},
        // 训练简介/备注
        abstract: { type: 'string', required: false},
        // 封面
        cover: { type: 'string', required: false},
        // 学年
        year: { type: 'string', required: true},
        // 训练结构
        templateName: { type: 'string', required: true},
        // 训练模版
        template: { type: 'object', required: true},
        // 时长
        duration: {type: 'number', required: false},
        // 总分
        score: {type: 'number', required: false},
        // 注意事项
        notice: {type: 'string', required: false},
        // 难度
        difficulty: {type: 'number', required: false},
        // 是否展示训练成绩
        ifShowScore: {type: 'number', required: false},
        // 是否展示试题批改结果
        ifShowCorrectionResults: {type: 'number', required: false},
        // 是否展示错题答案
        ifShowWrongAnswer: {type: 'number', required: false},
        // 是否允许收录⾄错题集
        ifSetWrongProblemCollection: {type: 'number', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 难度配置
        templateDifficulty: { type: 'object', required: false},

        selectNewSeries: { type: 'number', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.createUserID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.train.copyTrain(node, transaction);
        const { config } = app;
        await this.service.file.mkdirs(`${config.file.dir}/${this.ctx.schoolSlug}/train/${response.id}/assets/`);
      }
      catch(e)
      {
        console.error(e)
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练
    async getTrain() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练
        response = await this.service.train.getTrain(ctx.params.id);
      }
      catch(e)
      {
        console.error('getTrain', e);
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除训练
    async destoryTrain() {
      const { ctx } = this;

      let response;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      try
      {
        // 修改训练
        const { id, seriesId } = ctx.request.body;
        response = await this.service.train.destoryTrain(id, seriesId, transaction);
      }
      catch(e)
      {
        console.error('destoryTrain', e);
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async destoryTrains() {
      const { ctx } = this;

      let response;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { trainIDs, seriesId } = ctx.request.body;
      try {
        // 修改训练
        response = await ctx.service.train.destoryTrains(trainIDs, seriesId, transaction);
      } catch(e) {
        console.error('destoryTrains', e);
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练列表
    async getTrainList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: false},
        series: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try
      {
        // 创建训练
        response = await this.service.train.getTrainList(node);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async publishTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        year: { type: 'string', required: true },

        // 训练名称
        trainName: { type: 'string', required: true },

        seriesName: { type: 'string', required: true },
        
        trainContent: { type: 'object', required: true },

        // 训练结构
        trainTemplateName: { type: 'string', required: true },
        // 训练模版
        trainTemplate: { type: 'object', required: true },
        // 难度配置
        templateDifficulty: { type: 'object', required: false },

        // 备注
        note: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.train.publishTrain(node, transaction);
      }
      catch(e)
      {
        console.error(e, 'publishTrain')
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getEliteTrains() {
      const { ctx } = this;

      const { year } = ctx.query;
      let response;
      try {
        response = await ctx.service.train.getEliteTrains({ year });
      } catch(e) {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async importTrains() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 精品试卷id
        selectTrainIDs: { type: 'object', required: true },

        seriesID: { type: 'number', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await ctx.service.train.importTrains(node, transaction);
      }
      catch(e)
      {
        console.error(e, 'importTrains')
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async destoryEliteTrains() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 精品试卷id
        trainIDs: { type: 'object', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 检查精品试卷发布权限
      await ctx.service.train.checkPermission('trainWebTime', '训练管理-精品试卷-发布');

      // 启用事务
      const transaction = await app.mainModel.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await ctx.service.train.destoryEliteTrains(node, transaction);
      }
      catch(e)
      {
        console.error(e, 'destoryEliteTrains')
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getTrainSeries() {
      const { ctx } = this;

      if (!ctx.session.user) {
        ctx.body = {code: 400, message: '请先登录'};
        return;
      }

      const { id: userID } = ctx.session.user;

      let response;
      try {
        response = await ctx.service.train.getTrainSeries(userID);
      } catch(e) {
        console.error(e, 'getTrainSeries')
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改试卷系列名称
    async updateSeriesName() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        seriesId: { type: 'int', required: true },
        series: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.updateSeriesName(node, transaction);
      }
      catch(e)
      {
        console.error(e, 'updateSeriesName')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 创建者修改试卷系列分享教师ID集合
    async shareTrainSeries() {
      const { ctx } = this;

      // 判断是否登录
      if(!ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 获取当前用户ID
      const currentUserID = ctx.session.user.id;

      // 校验参数
      const rule = {
        teachers: { type: 'array', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { id } = ctx.params;
      const { teachers } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.shareTrainSeries(currentUserID, id, teachers, transaction);
      }
      catch(e)
      {
        console.error(e, 'shareTrainSeries')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }


    // 创建者修改试卷系列分享教师ID集合
    async unshareTrainSeries() {
      const { ctx } = this;

      // 判断是否登录
      if(!ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 获取当前用户ID
      const currentUserID = ctx.session.user.id;

      const { id } = ctx.params;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.unshareTrainSeries(currentUserID, id, transaction);
      }
      catch(e)
      {
        console.error(e, 'unshareTrainSeries')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 创建试卷系列
    async createTrainSeries() {
      const { ctx } = this;

      if (!ctx.session.user) {
        ctx.body = {code: 400, message: '请先登录'};
        return;
      }

      const { id: userID } = ctx.session.user;

      // 校验参数
      const rule = {
        series: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.createTrainSeries({ ...node, userID }, transaction);
      }
      catch(e)
      {
        console.error(e, 'createTrainSeries')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async destoryTrainSeries() {
      const { ctx } = this;

      // 检查是否登录，是否有权限
      if (!ctx.session.user) {
        ctx.body = {code: 400, message: '请先登录'};
        return;
      }

      const { id: userID } = ctx.session.user;

      // 校验参数
      const rule = {
        seriesId: { type: 'int', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.destoryTrainSeries({ ...node, userID }, transaction);
      }
      catch(e)
      {
        console.error(e, 'destoryTrainSeries')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }
    
    async bulkUpdateTrainSeries() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        series: { type: 'int', required: true },
        trainIDs: { type: 'array', required: true },
        originSeriesId: { type: 'int', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e, 'bulkUpdateTrainSeries');
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { originSeriesId, series, trainIDs } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.bulkUpdateTrainSeries(originSeriesId, series, trainIDs, transaction);
      }
      catch(e)
      {
        console.error(e, 'bulkUpdateTrainSeries')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功'};
    }

    async changeSeriesPriority() {
      const { ctx } = this;

      if (!ctx.session.user) {
        ctx.body = {code: 400, message: '请先登录'};
        return;
      }

      const { id: userID } = ctx.session.user;

      // 校验参数
      const rule = {
        fromID: { type: 'int', required: true },
        toID: { type: 'int', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        console.error(e, 'changeSeriesPriority');
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { fromID, toID } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        response = await ctx.service.train.changeSeriesPriority(fromID, toID, userID, transaction);
      }
      catch(e)
      {
        console.error(e, 'changeSeriesPriority')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response };
    }
    
    // 训练系统恢复
    async initSystem() {
      const { ctx } = this;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      try
      {
        // 训练系统恢复
        await ctx.service.train.initSystem(transaction);
      }
      catch(e)
      {
        console.error(e, 'initSystem')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功'};
    }

    // 训练系统清除
    async cleanSystem() {
      const { ctx } = this;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      let response = null;

      try
      {
        // 训练系统清除
        response = await ctx.service.train.cleanSystem(transaction);
      }
      catch(e)
      {
        console.error(e, 'cleanSystem')
        ctx.body = {code: 500, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }
  }

  return TrainController;
}