'use strict';
module.exports = app => {
  class NoticeController extends app.Controller {
    // 后台 首页公告信息管理
    // 新增公告
    async addNotice() {
      const {ctx} = this;
      const { user } = ctx.session

      if (!user) {
        ctx.body = {code: 1, message: '请先登录。'};
        return;
      }

      if (!user.isAdmin) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        // 标题
        title: {type: 'string', required: false},
        // 图片url
        preface: {type: 'string', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 公告内容
        content: {type: 'string', required: false},
        // 公告内容预览
        text: {type: 'string', required: false},
      };

      // 获取页面上的值
      const { title, preface, status, content, text } = ctx.request.body;
      // 校验参数
      ctx.validate(rule, ctx.request.body);

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.notice.addNotice(title, preface, status, content, text, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改公告信息
    async putNotice() {
      const { ctx } = this;
      const { user } =ctx.session

      if (!user) {
        ctx.body = {code: 1, message: '请先登录。'};
        return;
      }

      if (!user.isAdmin) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        // 标题
        title: {type: 'string', required: false},
        // 图片url
        preface: {type: 'string', required: false},
        // 状态
        status: {type: 'string', required: false},
        // 公告内容
        content: {type: 'string', required: false},
        // 公告内容预览
        text: {type: 'string', required: false},
      };

      const { id } = ctx.params;

      // 获取页面上的值
      const { title, preface, status, content, text } = ctx.request.body;
      // 校验参数
      ctx.validate(rule, ctx.request.body);

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.notice.putNotice(id, title, preface, status, content, text, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除公告
    async deleteNotice() {
      const { ctx } = this;

      const { id } = ctx.params;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.notice.deleteNotice(id, transaction);
      } 
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取公告列表
    async getNoticeList() {
       const { ctx } = this;
       const { user } = ctx.session
      if (!user) {
        ctx.body = {code: 1, message: '请先登录。'};
        return;
      }

      if (!user.isAdmin) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const { status } = ctx.query;

      const response = await ctx.service.notice.getNoticeList(status);

      ctx.body = {code: 0, message: '成功！', data: response};
    }

    // 后台 通过id获取公告详情
    async getNoticeDetails(){
      const {ctx, service} = this;
      const { id } = ctx.params;

      let response = null;
      
      try{
       response = await service.notice.getNoticeDetails(id);
      }catch(e){
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    
    }

    // 前台 通过id获取公告详情
    async getNoticeMessage() {
      const {ctx, service} = this;
      const { noticeID } = ctx.params;

      let response = null;
      try {
        response = await service.notice.getNoticeMessage(noticeID);
      }catch(e){
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};

    }

  }
  return NoticeController;
}