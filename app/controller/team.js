'use strict';

module.exports = app => {
  class TeamController extends app.Controller {
    // 获取所有班级列表(精简)
    async getClassListAllLess() {
        const { ctx } = this;

        let response = null;
        try {
         response = await ctx.service.team.getClassListAllLess(ctx.query && ctx.query.year ? ctx.query.year : null);
        }catch(e){
          console.log(e, 'getClassListAllLess');
          ctx.body = {code: 500,  message: e.message};
          return;
        }
      
        ctx.body = {code: 0, message: '成功', data: response};

    }

    // 用户班级关联
    async userClassContact() {
      const { ctx } = this;
        
      // 校验参数
      const rule = {
        //用户ID
        userID: {type: 'string', required: true},
        // 班级ID
        classID: {type: 'string', required: true}
      };

      ctx.validate(rule);

      const { userID, classID } = ctx.request.body;

      let classIDs = JSON.parse(classID);


      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;

      try
      {
          response = await ctx.service.team.userClassContact(parseInt(userID, 10), classIDs, transaction);
      }
      catch(e) {
          console.log(e, 'userClassContact');
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: e.message};
          return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = { code: 0, message: '成功', data: response };

    }

  
    async getClassList() {
      const { ctx } = this;

      let {offset, pageSize, search, year} = ctx.query;

      offset = offset ? parseInt(offset, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;

      try{
        response = await ctx.service.team.getClassList(offset, pageSize, search, year, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};

    }

    async filterUser() {
      const { ctx } = this;

      let {offset, pageSize, classID, search} = ctx.query;

      offset = offset ? parseInt(offset, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;

      let response = null;
      try{
        response = await ctx.service.team.filterUser(parseInt(classID, 10), offset, pageSize, search);
      }catch(e){
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async filterUserWithComputerRoomTrain() {
      const { ctx } = this;

      let {classID} = ctx.query;

      let response = null;
      try{
        response = await ctx.service.team.filterUserWithComputerRoomTrain(parseInt(classID, 10));
      }catch(e){
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getClassAllUsers() {
      const { ctx } = this;

      let { classID, search} = ctx.query;

      let response = null;
      try{
        response = await ctx.service.team.getClassAllUsers(parseInt(classID, 10), search);
      }catch(e){
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除班级
    async deleteClass(){
      const { ctx } = this;
      const {id} = ctx.params;
      let {deleteUser, offset, pageSize, search, year} = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      // let {offset, pageSize, search, year} = ctx.query;
      offset = offset ? parseInt(offset, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;

      let response = null;

      try{
        await ctx.service.team.deleteClass(id, deleteUser, transaction);
        response = await ctx.service.team.getClassList(offset, pageSize, search, year, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量删除班级
    async deleteClasses() {
      const { ctx } = this;
      const { ids } = ctx.request.body; // 接收多个班级ID
      let { deleteUser, offset, pageSize, search, year } = ctx.request.body;
    
      // 启用事务
      const transaction = await ctx.model.transaction({ autocommit: false });
    
      offset = offset ? parseInt(offset, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;
    
      let response = null;
    
      try {
        await ctx.service.team.deleteClasses(ids, deleteUser, transaction);
        response = await ctx.service.team.getClassList(offset, pageSize, search, year, transaction);
      } catch (e) {
        await transaction.rollback();
        ctx.body = { code: 500, message: '失败' + e.message };
        return;
      }
    
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }


    async checkClassName() {
      const { ctx } = this;

      let {name, year} = ctx.request.body;

      let response = null
      try{
        response = await ctx.service.team.checkClassName(name, year);
      }catch(e){
        ctx.body = {code: 500, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};

    }

    async createClass() {
      const { ctx } = this;

      // // 权限管理
      // const result = await ctx.service.adminExternal.getServicePermissions();
      // const classListLess = await service.class.getClassListAllLess();

      // const { openState, serviceInfo } = result;
      // let classNumber = 0;
      
      // for (let key in serviceInfo) {
      //   const endTimeStamp = moment(serviceInfo[key].endTime).valueOf();
      //   const startTimeStamp = moment(serviceInfo[key].startTime).valueOf();
      //   if (endTimeStamp > moment().valueOf() && startTimeStamp < moment().valueOf()) {
      //     if (classNumber < serviceInfo[key].classNumber) {
      //       classNumber = parseInt(serviceInfo[key].classNumber, 10);
      //     }
      //   }
      // };

      // if (classListLess.length >= classNumber) {
      //   ctx.body = { code: 1, message: '班级数量已达上限' };
      //   return;
      // };
      
      // 校验参数
      const rule = {
        //班级名称
        name: {type: 'string', required: true},
      };

      try{
        ctx.validate(rule, request.body);
      }catch(e){
        ctx.body = {code: 400, data: e, message: e.message};
      }

      const {name, year,type} = ctx.request.body;
      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;
      // const type ="class"
      try
      {
          response = await ctx.service.team.createClass(name, year, type, transaction);
      }
      catch(e) {
          console.log(e, 'createClass');
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: e.message};
          return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
  }

    // 修改班级名称
    async putClassName(){
      const { ctx } = this;

      const { id } = ctx.params;
      const { name } = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});


      let response = null;

      try{
        await ctx.service.team.putClassName(id, name, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async changeClassYear() {
      const { ctx } = this;

      const { ids, year } = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      let response = null;

      try{
        await ctx.service.team.changeClassYear(ids, year, transaction);
      }
      catch(e) {
        console.error('changeClassYear', e)
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除用户班级关联
    async deleteUserClassContact() {
      const { ctx } = this;
        
      // 校验参数
      const rule = {
        //用户ID
        userID: {type: 'string', required: true},
        // 班级ID
        classID: {type: 'string', required: true}
      };

      ctx.validate(rule);

      const {userID, classID} = ctx.request.body;

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;

      try
      {
          response = await ctx.service.team.deleteUserClassContact(parseInt(userID, 10), parseInt(classID, 10), transaction);
      }
      catch(e) {
          console.log(e, deleteUserClassContact);
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: e.message};
          return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();     
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async recoverTeamAndUsers() {
      const { ctx } = this;

      let { ids } = ctx.query;

      let response = null;
      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      try{
        response = await ctx.service.team.recoverTeamAndUsers(ids);
      }catch(e){
        console.log(e, 'recoverTeamAndUsers');
        await transaction.rollback();
        ctx.body = { code: 500, data: e, message: e.message };
        return;
      }
    
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();  
      ctx.body = { code: 0, message: '成功', data: response };
    }

    async getClassListAndUser(){
      const { ctx } = this;

      let response = null;
      // 开启事务
      // const transaction = await ctx.model.transaction({autocommit: false});
      try{
        response = await ctx.service.team.getClassListAndUser();
      }catch(e){
        console.log(e, 'getClassListAndUser');
        // await transaction.rollback();
        ctx.body = { code: 500, data: e, message: e.message };
        return;
      }
      ctx.body = { code: 0, message: '成功', data: response };
    }

    async resetClassPassword() {
      const { ctx } = this;

      let response = null;
      
      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      try{
        let { selectClassIDs, resetPassword } = ctx.request.body;

        response = await ctx.service.team.resetClassPassword({ selectClassIDs, resetPassword }, transaction);
      }catch(e){
        console.log(e, 'resetClassPassword');
        await transaction.rollback();
        ctx.body = { code: 500, data: e, message: e.message };
        return;
      }

      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async checkRearrangeClasses() {
      const { ctx } = this;

      let response = null;
      
      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      try {
        let { uploadData, rearrangeMode, oldClassOption, oldClassNameAffix, selectedOldClassIds } = ctx.request.body;

        response = await ctx.service.team.checkRearrangeClasses({ uploadData, rearrangeMode, oldClassOption, oldClassNameAffix, selectedOldClassIds }, transaction);
      } catch(e) {
        console.log(e, 'checkRearrangeClasses');
        ctx.body = { code: 500, data: e, message: e.message };

        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async importRearrangeClasses() {
      const { ctx } = this;

      let response = null;
      
      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      try {
        let { 
          rearrangeMode,
          oldClassOption,
          oldClassNameAffix,
          selectedOldClassIds,
          rawUploadData,
          resetPassword,
         } = ctx.request.body;

        response = await ctx.service.team.importRearrangeClasses({ 
          rearrangeMode,
          oldClassOption,
          oldClassNameAffix,
          selectedOldClassIds,
          rawUploadData,
          resetPassword,
         }, transaction);
      } catch(e) {
        console.log(e, 'importRearrangeClasses');
        ctx.body = { code: 500, data: e, message: e.message };

        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = { code: 0, message: '成功', data: response };
    } 
  }

  return TeamController;
}