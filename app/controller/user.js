'use strict';
const moment = require('moment');

module.exports = app => {
  const mainRedis = app.redis.get('session');
  const { sso } = app.config;

  class UserController extends app.Controller {
     // 传出验证码
     async verify() {
      const { ctx } = this;
      let captcha = null;
      try
      {
        // 服务里面的方法
        captcha = await this.service.user.captcha();
        
        if (ctx.session.captchaCode && ctx.session.captchaCode instanceof Array) {
          ctx.session.captchaCode.push(captcha.text); // 验证码存储在数组中
        }
        else {
          ctx.session.captchaCode = [captcha.text];
        }
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }
      ctx.response.type = 'image/svg+xml';  // 知道你个返回的类型
      ctx.body = captcha.data;// 返回一张图片
    }

    // 用户登录
    async login() {
      const { ctx } = this;
      const { session, service, request } = ctx;

      // 校验参数
      const rule = {
        username:{ type: 'string', required: true},
        password:{ type: 'string', required: true},
        verify: {type: 'string', required: false},
        isWeakPassword: {type: 'boolean', required: true},
        finalLogin: { type: 'boolean', required: false },
      };
        
      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }

      const { ip, body } = ctx.request;
      const { username, password: inputPassword, verify: rawVerifyCode = null, isWeakPassword, finalLogin } = body;

      // 如果验证码存在，则转小写
      const verifyCode = rawVerifyCode ? rawVerifyCode.toLowerCase() : null;

      // 弱密码攻击保护
      let password = null;
      try {
        password = await service.user.weakPasswordAttackProtection(ip, username, inputPassword, isWeakPassword);
      }
      catch(e) {
        ctx.body = { code: e.code, message: e.message };
        return;
      }

      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const redis = app.redis.get('session');
      const key = `ip_login_error_per_5min_${ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);
      
      // // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录60分钟
      // if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
      //   await redis.expire(key, 60 * 60);
      //   ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
      //   return;
      // }

      session.captchaCode = session.captchaCode || []; // 确保验证码数组存在

      const currentCaptchas = session.captchaCode;

      const needVerifyCode = ipWrongPasswdPer5Min > 5 || session.needVerifyCode;
    
      // 如果不是万能验证码或者多次输错密码，则需校验验证码
      if ((verifyCode !== 'yopu123') && needVerifyCode) {
        // 打开多个页面时，验证码与其中一个相同即可登录
        const match = (currentCaptchas && verifyCode) ? currentCaptchas.some(code => code.toLowerCase() === verifyCode): false;
      
        // 验证码是否一致
        if(!match){
          // 增加IP登录错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
          await redis
          .multi()
            .incr(key)
            .expire(key, 5 * 60)
          .exec();

          // 重新生成验证码
          let nextCaptchaData = null;

          // 生成验证码
          try
          {
            // 服务里面的方法
            const nextCaptcha = await ctx.service.user.captcha();
            currentCaptchas.push(nextCaptcha.text); // 验证码存储在数组中
            nextCaptchaData = nextCaptcha.data;
          }
          catch(e)
          {
            ctx.body = {code: e.code, message: e.message};
            return;
          }

          // 需要验证码状态计入会话
          ctx.session.needVerifyCode = needVerifyCode;

          ctx.body = {code: 500, message: '验证码不相同', captcha: nextCaptchaData, needVerifyCode, csrfToken: session.csrfToken};
          return;
        }
      }
      else {
        // 如果是万能验证码或者不需要验证码，则不需要验证验证码
        ctx.session.needVerifyCode = false;
      }

      // 获取公告
      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      // 获取用户信息
      let user;
      
      try {
        user = await service.user.getUserByLogin(username, password, transaction);
      } catch (e) {
        // 获取验证码
        let nextCaptchaData = null;
        try
        {
          // 服务里面的方法
          const nextCaptcha = await ctx.service.user.captcha();
          currentCaptchas.push(nextCaptcha.text); // 验证码存储在数组中
          nextCaptchaData = nextCaptcha.data;
        }
        catch(e)
        {
          ctx.body = {code: e.code, message: e.message};
          return;
        }

        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        await transaction.rollback();
			  ctx.body = {code: 1, message: '失败' + e.message, captcha: nextCaptchaData, needVerifyCode, csrfToken: session.csrfToken};
			  return;
      }

      if (finalLogin) {
        await ctx.service.user.removeUselessClients(user.id);
      }

      // 获取首页管理的弹出公告
      let noticeData = await service.systemConfig.getNoticeData();

      // 如果不是已经在线 或 正在强制登录，应当写入sesion
      if(!user.signIn  || finalLogin) {
        session.user = {
          id: user.id,
          username: user.username,
          name: user.name,
          avatar: user.avatar,
          signIn: user.signIn,
          lastInteractionAt: (new Date()).getTime(),
          isAdmin: user.adminAuthority ? 1: 0,
          adminAuthority: user.adminAuthority,
          teamIDs: user.teamIDs, // 用户所属班级
          schoolSlug: ctx.schoolSlug,
          // 如果密码是万能秘钥或者用户名是手机号，就不做弱密码检测
          needModifyPassword: isWeakPassword && !(password === '2fdb3be15236116c02966b50d4a3277a9fc60d7f' || /^[1][3-9][0-9]{9}$/.test(username)),
          ban: user.allowStudentChangePassword,
          banName: user.allowStudentChangeName,
          permission: user.permission,
        };

        // 测试！！！
        // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
        //   session.user.ssoName = 'ceshi2';
        //   session.user.OrgCode = '32010081999';
        // }
      }

      // 重置csrf
      ctx.rotateCsrfSecret();

      user = user.dataValues ? user.dataValues : user;
      user.isAdmin = user.adminAuthority? 1: 0;

      // 部署标记
      const { deploymentSlug } = app.config.hxr;

      // 统计最后活跃时间
      await service.user.changeActiveTime(user.id);

      // 提交事务
      await transaction.commit();

      ctx.body = {code: 0, message: "成功!", data: { user, noticeData }, csrfToken: session.csrfToken, deploymentSlug};
    }

    async stressTestLogin() {
      const { ctx } = this;
      const { schoolSlug, model, session } = ctx;
      const { csrfToken, needVerifyCode = false } = session;
      const { deploymentSlug } = app.config.hxr;
      const { apiPath, AccountId, AccountPassword, teacherPassword, studentPassword, AppCode, AppKey, ...ssoInfo } = sso[deploymentSlug] || {};

      // 寻找登录用户的所在班级记录
      const team = await model.TeamUser.findOne({
        attributes: ['teamID', 'userID']
      })

      if(!team) {
        ctx.body = {code: 0, message: "必须至少有一个班级，里面有一位学生，才可以启动压力测试!"};
        return;
      }
      
      const { teamID, userID } = team;

      // 随便找个学生账号
      const user = await model.User.findOne({
        where: {
          id: userID
        },
        raw: true
      });

      if(!user) {
        ctx.body = {code: 0, message: "必须至少有一个班级，里面有一位学生，才可以启动压力测试!"};
        return;
      }
      
      const userSession = {
        id: -1, // 所做事务不会被提交
        username: 'stressTest',
        name: '压力测试',
        avatar: null,
        signIn: false,
        lastInteractionAt: (new Date()).getTime(),
        isAdmin: user.adminAuthority ? 1: 0,
        adminAuthority: user.adminAuthority,
        teamIDs: [teamID], // 用户所属班级
        schoolSlug,
        // 如果密码是万能秘钥，就不做弱密码检测
        needModifyPassword:  false,
        ban: false,
      };

      session.user = userSession;

      // 加入学校段名称
      ctx.body = {code: 0, message: '成功！', data: userSession, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoInfo };
    }

    // 读取用户会话
    async getSession(){
      const {ctx} = this;
      let result = null;
      const { schoolSlug, service, session } = ctx;
      const { csrfToken, needVerifyCode = false } = session;
      const { deploymentSlug } = app.config.hxr;
      const { managerment } = app.config;

      const ssoSlug = await this.ctx.service.sso.getSSOConfig();

      let ssoConfig = null;
      const currentSSOConfigs = sso[ssoSlug];
      if (currentSSOConfigs) {
        const { apiPath, AccountId, AccountPassword, teacherPassword, studentPassword, AppCode, AppKey, ...ssoInfo } = currentSSOConfigs;
        ssoConfig = ssoInfo;
      }


      if (session.user && session.user.schoolSlug && session.user.schoolSlug !== schoolSlug) {
        ctx.body = {code: 400, message: '没有用户登录信息', schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, managerment };
        return;
      }

      // let MaintainNotice = null;
      let permissionData = null;
      try {
        // MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
        permissionData = await service.systemConfig.getInfomationByKey('enableFunction');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      // if (MaintainNotice && MaintainNotice.value) {
      //   ctx.body = {code: 400, message: '正在维护中', MaintainNotice, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, managerment };
      //   return;
      // }

      try
      {
        result = await service.user.getSession();
      }
      catch(e)
      {
        // 加入学校段名称
        ctx.body = {code: e.code, message: e.message, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, managerment };
        return; 
      }

      // // 统计最后活跃时间
      // const activeTimeResult = await service.user.changeActiveTime(result.id);
      
      // 加入学校段名称
      ctx.body = { code: 0, message: '成功！', data: result, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, permissionData: permissionData ? permissionData.value : {}, managerment };
    }

    //注销
    async logoff(){
      const {ctx} = this;

      if(ctx.session.user){
        // 删除已存在的用户session
        const userSessionsKey = `${ctx.schoolSlug}_${ctx.session.user.id}_SESSIONID`;
        await mainRedis.del(userSessionsKey);

        // 从 course_user_ids 中去除此id
        const key = `${ctx.schoolSlug}_course_user_ids`;
        await mainRedis.hdel(key, ctx.session.user.id);

        // 从 login_user_ids 中去除此id
        const loginKey = `${ctx.schoolSlug}_login_user_ids`;
        await mainRedis.hdel(loginKey, ctx.session.user.id);

        delete ctx.session.user;
        ctx.session.save();
      }

      ctx.body = {code: 0, message:'成功！'};
    }

    // 改session 里的 noticeData
    async popupClosed() {
      const {ctx} = this;
      const { user } = ctx.session;

      if(!user || !user.id) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      user.noticeData = false;

      ctx.body = {code: 0, message: "成功!", data: 1};
    }

    //后台注销
    async adminLogoff(){
      const {ctx} = this;
      delete ctx.session.user;
      ctx.body = {code: 0, message:'成功！'};
    }

    // 管理员登录
    async adminLogin() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        username:{ type: 'string', required: true},
        password:{ type: 'string', required: true},
        isWeakPassword: {type: 'boolean', required: true},
        verify: {type: 'string', required: false},
      };

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }
      
      const { ip, body } = request;
      const {username, password: inputPassword, verify, isWeakPassword} = body;

      // 弱密码攻击保护
      let password = null;
      try {
        password = await service.user.weakPasswordAttackProtection(ip, username, inputPassword, isWeakPassword);
      }
      catch(e) {
        ctx.body = { code: e.code, message: e.message };
        return;
      }

      // 获取公告
      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }
    
      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const redis = app.redis.get('session');
      const key = `ip_wrong_passwd_per_5min_${ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);

      // // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录10分钟
      // if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
      //   await redis.expire(key, 60 * 60);
      //   ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
      //   return;
      // }

      const needVerifyCode = ipWrongPasswdPer5Min > 1;

      // 无论验证码对错均需要重新生成验证码
      const currentCaptchas = session.captchaCode;

      let nextCaptchaData = null;

      if (verify !== 'yopu123' && needVerifyCode) {
        try
        {
          // 服务里面的方法
          const nextCaptcha = await ctx.service.user.captcha();
          ctx.session.captchaCode = [nextCaptcha.text];
          nextCaptchaData = nextCaptcha.data;
        }
        catch(e)
        {
          ctx.body = {code: e.code, message: e.message};
          return;
        }

        // 打开多个页面时，验证码与其中一个相同即可登录
        const match = currentCaptchas && verify ? currentCaptchas.some(code => code.toLowerCase() === verify.toLowerCase()): false;
        
        // 验证码是否一致
        if(!match){
          // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
          await redis
          .multi()
            .incr(key)
            .expire(key, 5 * 60)
          .exec();

          ctx.body = {code: 500, message: '验证码不相同', captcha: nextCaptchaData, csrfToken: session.csrfToken};
          return;
        }
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      // 获取用户信息
      let user;

      try {
        user = await service.user.getUserByLogin(username, password, transaction);
      } catch (e) {
        try
        {
          // 服务里面的方法
          const nextCaptcha = await ctx.service.user.captcha();
          ctx.session.captchaCode = [nextCaptcha.text];
          nextCaptchaData = nextCaptcha.data;
        }
        catch(e)
        {
          ctx.body = {code: e.code, message: e.message};
          return;
        }
        // 提示前端
        ctx.session.needVerifyCode = needVerifyCode
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        await transaction.rollback();
        ctx.body = { code: 1, message: '账号或密码错误！', captcha: nextCaptchaData, csrfToken: session.csrfToken};
        return;
      }
      

      if(!user.adminAuthority) {
        try
        {
          // 服务里面的方法
          const nextCaptcha = await ctx.service.user.captcha();
          ctx.session.captchaCode = [nextCaptcha.text];
          nextCaptchaData = nextCaptcha.data;
        }
        catch(e)
        {
          ctx.body = {code: e.code, message: e.message};
          return;
        }
        await transaction.commit();
        ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', captcha: nextCaptchaData, csrfToken: session.csrfToken};
        return;
      }

      // 部署标记
      const { deploymentSlug } = app.config.hxr;

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        deploymentSlug,
        teamIDs: user.teamIDs, // 用户所属班级
        ifSuperAdmin: user ? user.ifSuperAdmin : false,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        permissionData: user.permissionData,
        needModifyPassword: isWeakPassword && password !== '2fdb3be15236116c02966b50d4a3277a9fc60d7f',
        permission: user.permission,
      };


      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // Faye实时通信客户端ID
      const clientID = await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      // 提交事务
      await transaction.commit();

      // 统计最后活跃时间
      const activeTimeResult = await service.user.changeActiveTime(user.id);

      ctx.body = { code: 0, message: "成功!", data: { user, clientID }, schoolSlug, csrfToken: session.csrfToken, deploymentSlug };
    }

    // 管理员登录
    async adminLoginFromManager() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        username:{ type: 'string', required: true},
        password:{ type: 'string', required: true},
        verify: {type: 'string', required: false},
      };

      const {username, password, verify} = request.body;

      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }
    
      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const redis = app.redis.get('session');
      const key = `ip_wrong_passwd_per_5min_${ctx.request.ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);

      // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录10分钟
      if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
        await redis.expire(key, 60 * 60);
        ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
        return;
      }

      const needVerifyCode = ipWrongPasswdPer5Min > 5;


      // 如果使用万能验证码，无需校验
      if (verify && verify !== 'yopu123' && needVerifyCode) {
        // 打开多个页面时，验证码与其中一个相同即可登录
        const match =  session.captchaCode ? session.captchaCode.some(code=> {
          if (code.toLowerCase() === verify.toLowerCase()) return true;
         }): false; 

        // 验证码是否一致
        if(!match){
          // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
          await redis
          .multi()
            .incr(key)
            .expire(key, 5 * 60)
          .exec();

          ctx.body = {code: 500, message: '验证码不相同', csrfToken: session.csrfToken};
          return;
        }
      }

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }
      
      // 获取用户信息
      let user;

      try {
        user = await service.user.getUserByLoginFromManager();
      } catch (e) {
        console.error(e);
        ctx.body = {code: 500, message: e.message, csrfToken: session.csrfToken};
        return;
      }
      
      if(!user)
      {
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
        // 要求强制验证验证码
        session.needVerifyCode = true;
        return;
      }

      if(!user.adminAuthority) {
        ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', csrfToken: session.csrfToken};
        return;
      }

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        teamIDs: user.teamIDs, // 用户所属班级
        ifSuperAdmin: user ? user.ifSuperAdmin : false,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        permissionData: user.permissionData,
        permission: user.permission,
      };

      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 验证登录时间
      const clientID = await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      // 部署标记
      // const { deploymentSlug } = app.config.hxr;

      // 统计最后活跃时间
      // const activeTimeResult = await service.user.changeActiveTime(user.id);

      // 生成临时远程登录口令
      const code = parseInt(Math.random() * 100000000);
      // 在30分钟内，可用口令登录
      await redis.set(`pcLabTrainLoginHxr-${code}`, JSON.stringify({username: user.username, password: '2fdb3be15236116c02966b50d4a3277a9fc60d7f'}), 'EX', 30*60);

      ctx.body = { code: 0, message: "成功!", data: code };
    }

    // 昆山demo登录专用
    async loginDemo() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        username:{ type: 'string', required: true},
      };

      const {username} = request.body;

      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }
    
      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const redis = app.redis.get('session');
      const key = `ip_wrong_passwd_per_5min_${ctx.request.ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);

      // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录10分钟
      if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
        await redis.expire(key, 60 * 60);
        ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
        return;
      }

      // const needVerifyCode = ipWrongPasswdPer5Min > 5;


      // 如果使用万能验证码，无需校验
      // if (verify && verify !== 'yopu123' && needVerifyCode) {
      //   // 打开多个页面时，验证码与其中一个相同即可登录
      //   const match =  session.captchaCode ? session.captchaCode.some(code=> {
      //     if (code.toLowerCase() === verify.toLowerCase()) return true;
      //    }): false; 

      //   // 验证码是否一致
      //   if(!match){
      //     // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
      //     await redis
      //     .multi()
      //       .incr(key)
      //       .expire(key, 5 * 60)
      //     .exec();

      //     ctx.body = {code: 500, message: '验证码不相同', csrfToken: session.csrfToken};
      //     return;
      //   }
      // }

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }
      
      // 获取用户信息
      let user;

      try {
        user = await service.user.loginDemo(username);
      } catch (e) {
        console.error(e);
        ctx.body = {code: 500, message: e.message, csrfToken: session.csrfToken};
        return;
      }
      
      if(!user)
      {
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
        // 要求强制验证验证码
        session.needVerifyCode = true;
        return;
      }

      // if(!user.adminAuthority) {
      //   ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', csrfToken: session.csrfToken};
      //   return;
      // }
      // 部署标记
      const { deploymentSlug } = app.config.hxr;

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        deploymentSlug,
        teamIDs: user.teamIDs, // 用户所属班级
        ifSuperAdmin: user ? user.ifSuperAdmin : false,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        permissionData: user.permissionData,
        // needModifyPassword: isWeakPassword && password !== '2fdb3be15236116c02966b50d4a3277a9fc60d7f',
        needModifyPassword: false,
        permission: user.permission,
      };


      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 验证登录时间
      const clientID = await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      // 统计最后活跃时间
      const activeTimeResult = await service.user.changeActiveTime(user.id);

      ctx.body = { code: 0, message: "成功!", data: { user, clientID }, schoolSlug, csrfToken: session.csrfToken, deploymentSlug };
    }

    // 调用缓存
    async getAdminSession(){
      const { ctx } = this;
      const { schoolSlug, service, session } = ctx;
      const { csrfToken, needVerifyCode = false } = session;
      const { deploymentSlug } = app.config.hxr;
      const { managerment } = app.config;

      let ssoConfig = null;
      const ssoSlug = await this.ctx.service.sso.getSSOConfig();
      if (ssoSlug && sso[ssoSlug]) {
        const { name, path, DEFAULTCODECOUNT } = sso[ssoSlug];
        ssoConfig = { name, path, DEFAULTCODECOUNT } ;
      }

      if (session.user && session.user.schoolSlug && session.user.schoolSlug !== schoolSlug) {
        ctx.body = {code: 400, message: '没有用户登录信息', schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, managerment };
        return;
      }

      let result = null;
      try {
        result = await service.user.getAdminSession();
      } catch(e) {
        ctx.body = {code: e.code, message: e.message, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, managerment };
        return; 
      }

      let MaintainNotice = null;
      let permissionData = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
        permissionData = await service.systemConfig.getInfomationByKey('enableFunction');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 400, message: '正在维护中', MaintainNotice, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, managerment };
        return;
      }

      // 统计最后活跃时间
      const activeTimeResult = await service.user.changeActiveTime(result.id);
      // console.log('result:',result)
      
      ctx.body = {code: 0, message: '成功！', data: result, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, permissionData: permissionData ? permissionData.value : {}, managerment };
      // ctx.body = {code: 0, message: '成功！', data: result, schoolSlug, needVerifyCode, csrfToken, deploymentSlug, sso: ssoConfig, permissionData: result.permissionData, managerment };
    }

    // 用户列表
    async getUserList() {
      const {ctx, service} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user.isAdmin){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      let {offset, pageSize, isAdmin, search, isTeam } = ctx.query;

      offset = offset ? parseInt(offset, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;

      const users = await service.user.getUserList(offset, pageSize, isAdmin, search, isTeam);
      if(users){
        ctx.body = {code: 0, message: '成功', data: users};
      } else {
        ctx.body = {code: 1, message: '失败'};
      }
    }

      // 上传用户
      async createUserList() {
        const { ctx, service } = this;
        const { userArr, classID } = ctx.request.body;
        // 开启事务
        const transaction = await ctx.model.transaction({autocommit: false});
  
        let userResult = null;
  
        try
        {
          userResult = await service.user.createUserList(userArr, parseInt(classID, 10), transaction);
        }
        catch(e) {
          console.log(e, 'createUserList');
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: e.message};
          return;
        }
  
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: userResult};
      }

    // 班级管理用户批量上传
    async newcreateUserList() {
      const { ctx, service } = this;
      const { userArr, classID } = ctx.request.body;
      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let userResult = null;

      try
      {
        userResult = await service.user.newcreateUserList(userArr, transaction);
      }
      catch(e) {
        console.log(e, 'newcreateUserList');
        await transaction.rollback();
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      if(userResult){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: userResult};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }
  
    // 批量上传用户 用户页面
    async createUserListUser() {
      const { ctx, service } = this;

      // 校验参数
      const rule = {
        // 密码
        userArr: { type: 'array', required: true, itemType: 'object', rule: {
          username: 'string', min: 3, max: 8, regex: /\w[\w_\d]{7}/,
          password: 'string', min: 16, max: 16,
        }}
      };

      // ctx.validate(rule);

      const { userArr } = ctx.request.body;

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let userResult = null;

      try
      {
        // 创建或更新
        userResult = await service.user.createUserListUser(userArr, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      if(userResult){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: userResult};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }
  
    // 批量删除用户
    async deleteAlluser() {
      const { ctx, service } = this;

      const {users} = ctx.request.body;

      if(!ctx.session){
        ctx.body = {code: 1, message: '没有用户登录信息'};
        return;
      }

      const {session} = ctx;
      const {user} = session;
      if(!user.id){
        ctx.body = {code: 1, message: '没有用户登录信息'};
        return;
      }

      if (JSON.parse(users).indexOf(user.id) !== -1) {
        ctx.body = {code: 1, message: '不可删除当前登录账号！'};
        return;
      }

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;

      try
      {
          response = await ctx.service.user.deleteAlluser(JSON.parse(users), transaction);
      }
      catch(e) {
          console.log(e, 'deleteAlluser');
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: e.message};
          return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      if(response){
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        ctx.body = {code: 0, message: '没有数据'};
      }
    }

    async changeUserNickName() {
      const { ctx, service } = this;
      const { userID, nickName } = ctx.params;

      if(!ctx.session.user || !ctx.session.user.isAdmin){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        if (ctx.session.user.id === parseInt(userID, 10)) {
          ctx.session.user.name = nickName;
        }

        response = await ctx.service.user.changeUserNickName(userID, nickName, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }
      
      if(response){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }

    async webChangeUserNickName() {
      const { ctx, service } = this;
      const { nickName } = ctx.params;

      if(!ctx.session.user){
        ctx.body = {code: 1, message: '请先登录！'};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        // if (ctx.session.user.id === parseInt(ctx.session.user.id, 10)) {
        //   ctx.session.user.name = nickName;
        // }
        ctx.session.user.name = nickName;

        response = await ctx.service.user.changeUserNickName(ctx.session.user.id, nickName, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }
      
      if(response){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }
  
    // 课程信息页获取所有教师
    async getAllAdmins() {
      const {ctx, service} = this;
      // 判断是否登录
      if(!ctx.session.user|| !ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      const users = await service.user.getAllAdmins();
      if(users){
        ctx.body = {code: 0, message: '成功', data: users};
      } else {
        ctx.body = {code: 1, message: '失败'};
      }
    }

    // 根据班级id获取所有学生
    async getAllStudentsByteam() {
      const {ctx, service} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user|| !user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }
      const { teamID } = ctx.params
      const users = await service.user.getAllStudentsByteam(teamID);
      if(users){
        ctx.body = {code: 0, message: '成功', data: users};
      } else {
        ctx.body = {code: 1, message: '失败'};
      }
    }

    //获取用户信息
    async adminGetUser(){
      const { ctx } = this;
      // 判断是否登录
      if(!ctx.session.user || !ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const { id } = ctx.params;

      const result = await ctx.service.user.getUser(id);


      if(result){
        ctx.body = {code: 0, message: '成功', data: result};
      } else {
        ctx.body = {code: 1, message: '失败'};
      }
    }

    // 修改状态
    async changeUserState() {
      const { ctx, service } = this;
      const { userID, state } = ctx.params;

      if(!ctx.session.user || !ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.user.changeUserState(userID, state, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    //添加管理员
    async changeUserAdmin(){
      const {ctx, service} = this;
      const {id} = ctx.params;
      //判断是否登录
      if(!ctx.session.user || !ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      //从页面获取值
      const {adminAuthority} = ctx.request.body;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let result = null;
      
      try{
        result = await ctx.service.user.changeUserAdmin(id, adminAuthority, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: result};        
    }

    async unbindsso(){
      const { ctx } = this;
      const { id } = ctx.params;
      //判断是否登录
      if(!ctx.session.user || !ctx.session.user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let result = null;
      
      try{
        result = await ctx.service.user.unbindsso(id, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: result};        
    }

    //修改密码
    async changePassword(){
      const {ctx} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user || !user.adminAuthority){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        // 密码
        password:{ type: 'string', required: true},   
      };

      ctx.validate(rule, ctx.request.body);
      
      const { id, password } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.user.changePassword(id, password, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }
      
      if(response){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }

    // 前台修改密码
    async changePasswordByWeb() {
      const { ctx } = this;

      // 判断是否登录
      if(!ctx.session.user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      // 校验参数
      const rule = {
        // 密码
        password:{ type: 'string', required: true},   
      };

      ctx.validate(rule, ctx.request.body);
      
      const { request } = ctx; 
      const { ip } = request;
      const { password, schoolSlug } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.user.changePassword(ctx.session.user.id, password, schoolSlug, ip, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败：' + e.message};
        return;
      }
      
      if(response){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.session.user.needModifyPassword = null;
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }

    async changeAvatarByWeb(){
      const { ctx } = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      // 校验参数
      const rule = {
        // 密码
        avatar:{ type: 'string', required: true},   
      };

      ctx.validate(rule, ctx.request.body);
      
      const { id, avatar } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.user.changeAvatarByWeb(id, avatar, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败,' + e.message};
        return;
      }
      
      if(response){
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败'};
      }
    }

    async checkPassword() {
      const {ctx} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      // 校验参数
      const rule = {
        // 密码
        password:{ type: 'string', required: true},   
      };

      ctx.validate(rule, ctx.request.body);
      
      const {  password } = ctx.request.body;

      let response = null;
      
      try{
        response = await ctx.service.user.checkPassword(ctx.session.user.id, password);
      }
      catch(e) {
        ctx.body = {code: 500, message: e.message};
        return;
      }
      
        ctx.body = {code: 0, message: '成功', data: response};

    }
    // 禁止学生用户修改密码
    async setBan() {
      const {ctx} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      // 校验参数
      const rule = {
        // 密码
        ban:{ type: 'bool', required: true},   
      };
      const transaction = await ctx.model.transaction({autocommit: false});
      try{
        ctx.validate(rule, ctx.request.body);
        
        const { ban } = ctx.request.body;

        // 启用事务
        await ctx.service.user.setBan(ban, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功'};
    }

    // 查询学生是不是可以修改密码
    async getBan() {
      const {ctx} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }
      let response = null;
      try{
        // 启用事务
        response = await ctx.service.user.getSystemConfig('ban');
      }
      catch(e) {
        ctx.body = {code: 500, message: e.message};
        return;
      }
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 禁止学生用户修改密码
    async setBanName() {
      const {ctx} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }

      // 校验参数
      const rule = {
        // 密码
        banName:{ type: 'bool', required: true},   
      };
      const transaction = await ctx.model.transaction({autocommit: false});
      try{
        ctx.validate(rule, ctx.request.body);
        
        const { banName } = ctx.request.body;

        // 启用事务
        await ctx.service.user.setBanName(banName, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功'};
    }

    // 查询学生是不是可以修改密码
    async getBanName() {
      const {ctx} = this;
      const { user } = ctx.session;
      // 判断是否登录
      if(!user){
        ctx.body = {code: 1, message: '请先登录或注册！'};
        return;
      }
      let response = null;
      try{
        // 启用事务
        response = await ctx.service.user.getSystemConfig('banName');
      }
      catch(e) {
        ctx.body = {code: 500, message: e.message};
        return;
      }
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getClientID() {
      const { ctx } = this;
      const { session } = ctx;
      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const clientID = await ctx.service.user.getClientID();
      ctx.body = { code: 0, data: clientID };
    }
    
    async extendSession() {
      const { ctx } = this;
      const { session } = ctx;
      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      ctx.body = { code: 0, message: '成功' };
    }

    async handleClassAccounts() {
      const { ctx } = this;
      const { session } = ctx;
      if (!session || !session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 校验参数
      const rule = {
        selectClassID: { type: 'number', required: true },
        mode: { type: 'string', required: true },
      };

      ctx.validate(rule, ctx.request.body);
      
      const { selectClassID, mode } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try {
        response = await ctx.service.user.handleClassAccounts({ selectClassID, mode }, transaction);
      } catch (e) {
        console.error(e, 'handleClassAccounts')
        await transaction.rollback();
        ctx.body = { code: 1, message: e.message };
        return;
      }

      await transaction.commit(); 
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 检测使用弱密码用户
    async checkWeakPasswordUser(){
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 密码
        passwords: { type: 'object', required: true },   
      };

      ctx.validate(rule, ctx.request.body);
      
      const { passwords } = ctx.request.body;

      let response = null;

      try{
        response = await ctx.service.user.checkWeakPasswordUser({ passwords });
      }
      catch(e) {
        console.error(e);
        ctx.body = { code: 1, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 批量修改用户密码
    async batchChangePassword(){
      const { ctx } = this;

      // 校验参数
      const rule = {
        password: { type: 'string', required: true },
        userIDs: { type: 'object', required: true },
      };

      ctx.validate(rule, ctx.request.body);

      // 校验用户权限，检测是否为超级管理员
      const { user } = ctx.session;
      if (!user) {
        ctx.body = { code: 1, message: '请先登录管理员账户。' };
        return;
      }

      const { adminAuthority } = user;
      if (!adminAuthority || !adminAuthority.user) {
        ctx.body = { code: 1, message: '您没有权限进行此操作。' };
        return;
      }
      
      const { userIDs, password } = ctx.request.body; 

      let response = null;

      const transaction = await ctx.model.transaction({autocommit: false});

      try {
        response = await ctx.service.user.batchChangePassword({ userIDs, password }, transaction);
      } catch (e) {
        console.error(e);
        await transaction.rollback();
        ctx.body = { code: 1, message: e.message };
        return;
      }

      await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }
  }

  return UserController;
}
