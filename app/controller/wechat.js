
module.exports = app => {
  class WechatController extends app.Controller {
    // 获取微信用户绑定信息
    async getWechatBindInfo() {
      const { ctx, service } = this;
      const rule = {
        id: { type: 'number', required: true},
      };

      try {
        ctx.validate(rule, ctx.params);
      }
      catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { id } = ctx.params;

      // const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try {
        response = await service.wechat.getWechatBindInfo(id);
      } catch(e) {
        ctx.body = {code: 1, message: '失败' + e.message};
        // await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      // await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 设置微信用户绑定信息
    async setWechatBindInfo() {
      const { ctx, service } = this;
      const rule = {
        id: { type: 'number', required: true},
        openID: { type: 'number', required: true},
        wechatInfo: { type: 'object', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body);
      }
      catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { id, openID, wechatInfo } = ctx.request.body;

      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try {
        response = await service.wechat.setWechatBindInfo(id, openID, wechatInfo, transaction);
      } catch(e) {
        ctx.body = {code: 1, message: '失败' + e.message};
        await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 发送绑定二维码申请
    async getWeChatCode() {
      const { ctx, service } = this;
      const rule = {
        // 用户id
        userID: {type: 'number', required: true},
        // 账号
        username: {type: 'string', required: true},
        // 学校slug
        schoolSlug: {type: 'string', required: true},
        // 部署slug
        deploymentSlug: {type: 'string', required: true},
        // 系统
        system: {type: 'string', required: true},
        // 入口URL
        url: {type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body);
      }
      catch(e){
        console.log(e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { userID, username, schoolSlug, deploymentSlug, system, url } = ctx.request.body;

      // const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try {
        // if (ctx.session.wechatState) {
        //   response = ctx.session.wechatState
        // } else {
        //   response = await service.wechat.getWeChatCode({ userID, username, schoolSlug, deploymentSlug, system, url });
        // }
        response = await service.wechat.getWeChatCode({ userID, username, schoolSlug, deploymentSlug, system, url });
      } catch(e) {
        ctx.body = {code: 1, message: '失败' + e.message};
        // await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      // await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 发送登录二维码申请
    async getWeChatLoginCode() {
      const { ctx, service } = this;
      const rule = {
        // 学校slug
        schoolSlug: {type: 'string', required: true},
        // 部署slug
        deploymentSlug: {type: 'string', required: true},
        // 系统
        system: {type: 'string', required: true},
        // 入口URL
        url: {type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body);
      }
      catch(e){
        console.log(e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { schoolSlug, deploymentSlug, system, url } = ctx.request.body;

      // const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try {
        // if (ctx.session.wechatState) {
        //   response = ctx.session.wechatState
        // } else {
        //   response = await service.wechat.getWeChatCode({ schoolSlug, deploymentSlug, system, url });
        // }
        response = await service.wechat.getWeChatLoginCode({ schoolSlug, deploymentSlug, system, url });
      } catch(e) {
        ctx.body = {code: 1, message: '失败' + e.message};
        // await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      // await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response };
    }
    
    // 管理员登录
    async adminLogin() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        openID: { type: 'string', required: true},
        // url: { type: 'string', required: true},
        state: { type: 'string', required: true},
        system: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }

      // 这里因为暂时只有一个系统，所以就不校验了

      const {openID, state, system} = request.body;

      // 将登录信息写入缓存
      const redis = app.redis.get('session');

      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }
      
      // 获取用户信息
      let user;

      try {
        user = await service.wechat.getUserInfoByOpenID(openID, system, state);
      } catch (e) {
        console.error(e);
        ctx.body = {code: 500, message: e.message, csrfToken: session.csrfToken};
        return;
      }
      
      if(!user)
      {
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        // await redis
        // .multi()
        //   .incr(key)
        //   .expire(key, 5 * 60)
        // .exec();

        ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
        // 要求强制验证验证码
        session.needVerifyCode = true;
        return;
      }

      if(!user.adminAuthority) {
        ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', csrfToken: session.csrfToken};
        return;
      }

      // 部署标记
      // const { deploymentSlug } = app.config.hxr;
      // 写入sesion
      await redis.set(`${state}_login`, JSON.stringify(user), 'EX', 10 * 60 * 1000);

      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 验证登录时间
      // const clientID = await service.user.getClientID();
      
      // // 重置csrf
      // ctx.rotateCsrfSecret();

      // // 统计最后活跃时间
      // const activeTimeResult = await service.user.changeActiveTime(user.id);

      ctx.body = { code: 0, message: "成功!" };
    }
    
    // 根据state进行登录
    async adminLoginByState() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        state: { type: 'string', required: true},
        // system: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }

      const {state} = request.body;

      // 将登录信息写入缓存
      const redis = app.redis.get('session');
      const userData = await redis.get(`${state}_login`);
      const user = JSON.parse(userData);

      // 部署标记
      const { deploymentSlug } = app.config.hxr;

      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        deploymentSlug,
        teamIDs: user.teamIDs, // 用户所属班级
        ifSuperAdmin: user ? user.ifSuperAdmin : false,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        permissionData: user.permissionData,
        permission: user.permission,
      };

      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 验证登录时间
      const clientID = await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      // 统计最后活跃时间
      const activeTimeResult = await service.user.changeActiveTime(user.id);

      ctx.body = { code: 0, message: "成功!", data: { user, clientID }, schoolSlug, csrfToken: session.csrfToken, deploymentSlug };
    }

    // 刷新绑定状态
    async refreshBindStatus() {
      const { ctx } = this;

      const { session, service, request, schoolSlug } = ctx;
      
      // 获取用户信息
      let user;

      const { username } = session.user;

      try {
        user = await service.wechat.getUserInfoByUsername(username);
      } catch (e) {
        console.error(e);
        ctx.body = {code: 500, message: e.message, csrfToken: session.csrfToken};
        return;
      }
      const { deploymentSlug } = app.config.hxr;

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        deploymentSlug,
        teamIDs: user.teamIDs, // 用户所属班级
        ifSuperAdmin: user ? user.ifSuperAdmin : false,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        openID: user.openID,
        wechatInfo: user.wechatInfo,
        permissionData: user.permissionData,
        permission: user.permission,
      };

      ctx.body = { code: 0, message: "成功!" };
    }

    // 通知前端刷新状态
    async toWebRefreshBindStatus() {
      // 根据传入的state，刷新绑定状态
      const { ctx } = this;

      // 校验参数
      const rule = {
        state: { type: 'string', required: true},
        status: { type: 'string', required: false},
        key: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body);
      }
      catch(e){
        console.log(e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { state, status, key } = ctx.request.body;

      console.log('state:',state);

      const redis = app.redis.get('session');

      // 发送消息，通知前台登录成功
      app.client.publish(key? key : `/${state}/admin/bind`, status ? status :'success');

      ctx.body = { code: 0, message: "成功!" };
    }

    // 解绑微信
    async unbindWechat() {
      const { ctx } = this;

      const { session, service, request, schoolSlug } = ctx;

      const { id } = session.user;

      const transaction = await ctx.model.transaction({autocommit: false});
      // 解绑微信
      try {
        await service.wechat.unbindWechat(id, transaction);
      } catch (e) {
        console.error(e);
        await transaction.rollback();
        ctx.body = {code: 500, message: e.message};
        return;
      }

      await transaction.commit();
      
      ctx.body = { code: 0, message: "成功!" };
    }
  }

  return WechatController;
}