'use strict';
const fs = require("mz/fs");
const { getInputLength, getOIQuestion } = require('../utils/fps');
const { microTypeNameMap } = require('../utils/microapps');
const fsExtra = require('fs-extra');

module.exports = app => {
  const { jupyter } = app.config;
  const redis = app.redis.get('session');

  class CourseController extends app.Controller {
    // 创建课程
    async createCourse() {
      // 获取学校、课程简写
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

      // 校验参数
      const rule = {
        courseName: { type: 'string', required: true},
        courseSlug: { type: 'string', format: /^[0-9a-z]{1,}$/, required: true},
        courseDescription: { type: 'string', required: false},
        publish: { type: 'int', min: 0, max: 1, required: false},
        statist: { type: 'object', required: false },
        indics: { type: 'object', required: false },
        containerInfo: { type: 'object', required: false },
        courseType: { type: 'enum', values: ['选修课', '必修课'], required: false },
        saveCode: { type: 'int', min: 0, max: 1, required: false},
        saveRunResult: { type: 'int', min: 0, max: 1, required: false},
        allowPaste: { type: 'int', min: 0, max: 1, required: false},
        allowCopy: { type: 'int', min: 0, max: 1, required: false},
        teams: { type: 'object', required: false },
        teachers: { type: 'object', required: false },
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 建立入库条目，并将当前用户作为创建者
      const createrID = user.id;
      const item = {
        createrID,
        publish: 1,
        statist: {
          students: 0,
          chapters: 0,
        },
        indics: [],
        containerInfo: {
          image: jupyter.image, 
          cpuLimit: jupyter.cpuRequest,
          memoryRequest: jupyter.memoryRequest,
        },
        courseType: '必修课',
        saveCode: 1,
        saveRunResult: 1,
        allowPaste: 1,
        allowCopy: 1,
        teams: [],
        teachers: [],
      };

      const { body } = request;
      for(const key in rule) {
        const value = body[key];
        if(value === undefined) {
          continue;
        }

        item[key] = value;
      }

      let response = null;
      let transaction = null;
      
      try{
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.course.createCourse(item, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // CourseSlug查重
    async ifCourseSlug() {
      // 获取学校、课程简写
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

      // 校验参数
      const rule = {
        courseSlug: { type: 'string', required: false},
        courseName: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { body } = request;
      const item = {
        createrID: user.id
      };
      for(const key in rule) {
        const value = body[key];
        if(value === undefined) {
          continue;
        }

        item[key] = value;
      }

      let response = null;
      let transaction = null;
      
      try{
        // 启用事务
        transaction = await model.transaction({autocommit: false});
  
        // 入库
        response = await service.course.ifCourseSlug(item, transaction);
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改课程
    async putCourse() {
      // 获取学校、课程简写
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

      // 校验参数
      const rule = {
        courseName: { type: 'string', required: false},
        courseDescription: { type: 'string', required: false},
        createrID: { type: 'int', required: false},
        publish: { type: 'int', min: 0, max: 1, required: false},
        statist: { type: 'object', required: false },
        indics: { type: 'object', required: false },
        containerInfo: { type: 'object', required: false },
        courseType: { type: 'enum', values: ['选修课', '必修课'], required: false },
        saveCode: { type: 'int', min: 0, max: 1, required: false},
        saveRunResult: { type: 'int', min: 0, max: 1, required: false},
        allowPaste: { type: 'int', min: 0, max: 1, required: false},
        allowCopy: { type: 'int', min: 0, max: 1, required: false},
        questionAnswer: { type: 'int', min: 0, max: 1, required: false},
        programLanguage: { type: 'string', required: false},
        teams: { type: 'object', required: false },
        teachers: { type: 'object', required: false },
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 建立入库条目
      const item = {};

      const { body } = request;
      for(const key in rule) {
        const value = body[key];
        if(value === undefined) {
          continue;
        }

        item[key] = value;
      }

      // 启用事务
      const transaction = await model.transaction({autocommit: false});

      let response = null;
      const { courseSlug } = ctx.params;
      try{
        // 入库
        response = await service.course.putCourse(item, courseSlug, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 课程列表查询
    async queryCourseList() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, message: "没有用户登录信息"};
        return
      }
      
      const data = await service.course.queryCourseList(user.id);
      
      ctx.body = {code: 0, data, message: "成功!"};
    }

    // 后台课程列表查询
    async queryCourseListAdmin() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user|| !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      // 精品课程权限
      const permission = await ctx.model.SystemConfig.findOne({
        where: { key: 'enableFunction' },
        attributes: ['value'],
        raw: true
      });

      let eliteCourses = [];
      let elitePermission = false;
      if (permission) {
        elitePermission = permission.value;

        if (elitePermission['课程管理-精品资源-接收']) {
          const { mainModel } = app;
          eliteCourses = await mainModel.EliteCourses.findAll({
            where: {
              status: '发布'
            }
          });
        }
      }

      const data = await service.course.queryCourseListAdmin(ctx.session.user.id);
      ctx.body = {code: 0, data, eliteCourses, getEliteCoursesPermission: elitePermission['课程管理-精品资源-接收'] || false, message: "成功!"};
    }

    // 课程目录查询
    async queryCourseDirectory() {
      const { ctx, service } = this;
      const { schoolSlug } = ctx;

      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { courseSlug } = ctx.query;
      let data = null;
      try {
        data = await service.course.queryCourseDirectory(user.id, courseSlug);
      }
      catch(e){
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 加入学校段名称
      data.schoolSlug = schoolSlug;

      ctx.body = {code: 0, data, message: "成功!"};
    }

    // 获取课程开放班级列表
    async getCourseTeams() {
      const { ctx, service } = this;
      const { courseSlug } = ctx.query;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return;
      }

      const { isAdmin, adminAuthority } = user;
      const { course } = adminAuthority;

      // 检查用户是否为管理员，是否有查看课程的权限
      if (!isAdmin || !course) {
        ctx.body = {code: 400, message: "没有管理员权限，检查用户是否为管理员且无查看课程的权限？"};
        return;
      }
      
      let teams = [];
      try {
        if(isAdmin){
          teams = await service.course.queryCourseOpenTeams(courseSlug);
        }
      } catch(e){
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      ctx.body = {code: 0, data: teams, message: "成功!"};
    }

    // 获取节内容及类型
    async querySectionContentAndType() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { id: userID, teamIDs, isAdmin: isUserAdmin } = user;
      const { courseSlug, chapterName, sectionName, ifLoadCode = true, teamID } = ctx.query;
      let data = null;
      let teams = [];
      let statis = {};
      let isAdmin = false;

      let courseConfigs = {};
      try {
        // 检查权限
        courseConfigs = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
      } catch(e) {
        console.log(e, 'querySectionContentAndType--checkUserPermissionGetConfig');
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      // 检查授权人数是否超过限制
      const { permission: { course = {} } = {} } = user;
      const { limit } = course || {};
      const key = `${ctx.schoolSlug}_course_user_ids`;

      // 只记录学生用户
      if (!isUserAdmin) {
        if (limit > -1) {
          // 获取redis中的用户id集合
          const userCount = await redis.hlen(key);
  
          // 比较用户id集合长度与限制人数
          if (userCount >= limit) {
            // 查看用户id是否在集合中
            const hasUser = await redis.hexists(key, user.id);
            if (!hasUser) {
              ctx.body = {
                code: 403,
                message: '课程授权已满，请联系管理员'
              }
              return;
            }
          }
        }

        // 记录用户访问
        const loginKey = `${ctx.schoolSlug}_login_user_ids`;
        const sessions = await redis.hget(loginKey, user.id);
        await redis.hset(key, user.id, sessions);
      }
    
      try {
        const { course, currentSection, isCourseTeacher } = courseConfigs;
        // 获取课程数据
        isAdmin = isCourseTeacher;
        data = await service.course.querySectionContentAndType(courseSlug, chapterName, sectionName, ifLoadCode, userID, course, currentSection, false);
        // 课程所有者或开放课程的教师可以查询统计信息
        if(isCourseTeacher){
          teams = await service.course.queryCourseOpenTeams(courseSlug);
          
          if (teams && teams.length) {
            // 根据前台传递的课程ID获取班级数据
            const team = teams.find((team)=> {
              return team.dataValues.id === Number(teamID);
            })
            
            // 使用section ID、user ID和课程数据统计学生数据
            const { type, content } = data;
            const { sectionID } = currentSection;

            if (team && team.id) {
              const { id: teamID } = team;
              switch(type) {
                case 'AI':
                  statis = await service.section.sectionStatisResultAI(sectionID, teamID, content);
                  break;
                case 'Access':
                case 'Excel':
                case 'OJ':
                  statis = await service.section.sectionStatisResultOJ(sectionID, teamID);
                  break;
                case 'OI':
                  statis = await service.section.sectionStatisResultOI(sectionID, teamID, content);
                  break;
                case 'Scratch':
                  statis = await service.section.sectionStatisResultScratch(sectionID, teamID, content);
                  break;
                case 'PPT':
                  break;
                case 'CodeBlank':
                  statis = await service.section.sectionStatisResultCodeBlank(sectionID, teamID, content);
                  break;
                case 'MicroBit':
                  break;
                default:
                  throw new Error(`未识别的课程类型${type}`);
              }
            }
          }
        }
      }catch(e) {
        console.log(e, 'querySectionContentAndType');
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      ctx.body = {code: 0, data: { ...data, isAdmin, teams, ...statis }, message: "成功!"};
    }

    // 获取节内容及类型
    async querySectionContentAndTypeAdmin() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }
      const { id: userID, isAdmin, teamIDs } = user;
      if(!isAdmin){
        ctx.body = {code: 400, data, message: "请先登录管理员账户。"};
        return
      }

      const { courseSlug, chapterName, sectionName, ifLoadCode } = ctx.query;
      let data = null;
      try {
         // 检查权限
        const { course, currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        data = await service.course.querySectionContentAndTypeAdmin(courseSlug, chapterName, sectionName, ifLoadCode, userID, course, currentSection, true);
      }catch(e) {
        console.log(e, 'querySectionContentAndTypeAdmin');
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      
      ctx.body = {code: 0, data, message: "成功!"};
    }

    // AI班级结果统计
    async sectionStatisResult() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data: "", message: "请先登录"};
        return
      }

      const { id: userID, teamIDs } = user;

      const { courseSlug, chapterName, sectionName, teamID } = ctx.query;
      let data = null;
      try {
          // 检查权限
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
          const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.ipynb`;
          const response = await ctx.service.course.getFile(path, true);
          const { sectionID } = currentSection;
          data = await service.section.sectionStatisResultAI(sectionID, teamID, response);
        }catch(e) {
          console.log(e, 'sectionStatisResult');
          ctx.body = {code: 500, data: e, message: e.message};
          return;
        }

      ctx.body = {code: 0, data, message: "成功!"};
    }

    async sectionStatisResultOJ() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { id: userID, teamIDs } = user;
      const { courseSlug, chapterName, sectionName, teamID } = ctx.query;
      let data = null;
      try {
          // 检查权限
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
          const { sectionID } = currentSection;
          data = await service.section.sectionStatisResultOJ(sectionID, teamID);
        }catch(e) {
            console.log(e, 'sectionStatisResultOJ');
            ctx.body = {code: 500, data: e, message: e.message};
            return;
        }

      ctx.body = {code: 0, data, message: "成功!"};
    }

    async sectionStatisResultCodeBlank() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { id: userID, teamIDs } = user;
      const { courseSlug, chapterName, sectionName, teamID } = ctx.query;
      let data = null;
      try {
        // 检查权限
        const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        const { sectionID, ext } = currentSection;

        // 获取并解析客观题内容
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const { content } = await ctx.service.course.getCodeBlankContentFromFile({ path });
        data = await service.section.sectionStatisResultCodeBlank(sectionID, teamID, content);
      }catch(e) {
        console.log(e, 'sectionStatisResultCodeBlank');
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      ctx.body = {code: 0, data, message: "成功!"};
    }

    async sectionStatisResultOI() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const {id: userID, teamIDs } =user;
      const { courseSlug, chapterName, sectionName, teamID } = ctx.query;
      let data = null;

      try {
        // 检查权限
        const { currentSection: section } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        // 获取并解析客观题内容
        const { sectionID } = section;
        const { questions } = await ctx.service.course.getOIContentFromFile(courseSlug, chapterName, section, false);
        data = await service.section.sectionStatisResultOI(sectionID, teamID, questions);
      }catch(e) {
          console.log(e, 'sectionStatisResultOI');
          ctx.body = {code: 500, data: e, message: e.message};
          return;
      }

      ctx.body = {code: 0, data, message: "成功!"};
    }

    // Scratch 班级结果统计
    async sectionStatisResultScratch() {
      const { ctx, service } = this;
      const { user } = ctx.session;
      if(!user){
        ctx.body = {code: 400, data: "", message: "请先登录"};
        return
      }

      const { id: userID, teamIDs } = user;

      const { courseSlug, chapterName, sectionName, teamID } = ctx.query;
      let data = null;
      try {
          // 检查权限
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
          const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.json`;
          const response = await ctx.service.course.getFile(path, true);
          const { sectionID } = currentSection;
          data = await service.section.sectionStatisResultScratch(sectionID, teamID, response);
        }catch(e) {
          console.log(e, 'sectionStatisResult');
          ctx.body = {code: 500, data: e, message: e.message};
          return;
        }

      ctx.body = {code: 0, data, message: "成功!"};
    }

    // 根据 courseSlug 查询课程信息 getCourseBycourseSlug
    async getCourseBycourseSlug() {
      const { ctx, service } = this;
      const { schoolSlug } = ctx;
      const { user } = ctx.session;
      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { courseSlug } = ctx.params;
      const data = await service.course.getCourseBycourseSlug(courseSlug);

      // 精品课程权限
      const permission = await ctx.model.SystemConfig.findOne({
        where: { key: 'enableFunction' },
        attributes: ['value'],
        raw: true
      });

      // 没有配置项，关闭权限
      const elitePermission = permission ? permission.value: false;
      let eliteCoursePublished = null;
      if(elitePermission && elitePermission["课程管理-精品资源-发布"]) {
        const { mainModel } = app;
        eliteCoursePublished = await mainModel.EliteCourses.findOne({
          where: {
            courseSlug,
            schoolSlug,
          }
        });
      }

      if (!data) {
        throw new Error('当前课程不存在');
      }

      // 检查是否老师有权限查看课程
      const teachers = typeof data.teachers === 'string' ? JSON.parse(data.teachers) : data.teachers;
      // 只有在开放老师列表里面或者是这个老师创建的才可以查看操作
      const exist = parseInt(data.createrID, 10) === parseInt(user.id, 10) || (Array.isArray(teachers) && teachers.find(teacher => parseInt(teacher, 10) === parseInt(user.id, 10)))
      if (!exist) {
        ctx.body = {code: 1, message: "您暂时没有权限查看当前课程，请联系课程的创建者向您开放课程!", noPermission: true};
        return;
      }
      
      if(!data){
        ctx.body = {code: 1, message: "未找到对应课程!"};
        return
      }

      ctx.body = {
        code: 0, 
        data, 
        elitePublishPermission: elitePermission && elitePermission["课程管理-精品资源-发布"] ? elitePermission["课程管理-精品资源-发布"] : false, 
        eliteCoursePublished: eliteCoursePublished || false,
        ifScratchPermission: elitePermission && elitePermission["Scratch"],
        message: "成功!"};
    }

    async getCourseAndSectionBycourseSlug() {
      const { ctx, service } = this;
      const { schoolSlug } = ctx;
      const { user } = ctx.session;
      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { courseSlug } = ctx.params;
      const data = await service.course.getCourseAndSectionBycourseSlug(courseSlug);

      // 精品课程权限
      const permission = await ctx.model.SystemConfig.findOne({
        where: { key: 'enableFunction' },
        attributes: ['value'],
        raw: true
      });

      // 没有配置项，关闭权限
      const elitePermission = permission ? permission.value: false;
      let eliteCoursePublished = null;
      if(elitePermission && elitePermission["课程管理-精品资源-发布"]) {
        const { mainModel } = app;
        eliteCoursePublished = await mainModel.EliteCourses.findOne({
          where: {
            courseSlug,
            schoolSlug,
          }
        });
      }

      if (!data) {
        throw new Error('当前课程不存在');
      }

      // // 检查是否老师有权限查看课程
      // const teachers = typeof data.teachers === 'string' ? JSON.parse(data.teachers) : data.teachers;
      // // 只有在开放老师列表里面或者是这个老师创建的才可以查看操作
      // const exist = parseInt(data.createrID, 10) === parseInt(user.id, 10) || (Array.isArray(teachers) && teachers.find(teacher => parseInt(teacher, 10) === parseInt(user.id, 10)))
      // if (!exist) {
      //   ctx.body = {code: 1, message: "您暂时没有权限查看当前课程，请联系课程的创建者向您开放课程!", noPermission: true};
      //   return;
      // }
      
      // if(!data){
      //   ctx.body = {code: 1, message: "未找到对应课程!"};
      //   return
      // }

      ctx.body = {
        code: 0, 
        data, 
        elitePublishPermission: elitePermission && elitePermission["课程管理-精品资源-发布"] ? elitePermission["课程管理-精品资源-发布"] : false, 
        eliteCoursePublished: eliteCoursePublished || false,
        ifScratchPermission: elitePermission && elitePermission["Scratch"],
        message: "成功!"};
    }

    // 查询是否重复 courseSlug或名称
    async findExitCourseSlug() {
      const { ctx, service } = this;
      if(!ctx.session.user || !ctx.session.user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { courseSlug, courseName } = ctx.query;
      let data = null;
      try{
         data = await service.course.findExitCourseSlug(courseSlug, courseName);
      }catch(e){
        ctx.body = {code: 500, message: e.message};
        return;
      }
      
      if(data){
        const message = data.courseSlug === courseSlug ? `您上传的课件与${data.creater.name}老师上传的课件重复，请打开课件包中的course.json文件修改courseSlug字段的值` : `您上传的课件与${data.creater.name}老师上传的课件重复，请打开课件包中的course.json文件修改courseName字段的值`
        ctx.body = {code: 1, message};
        return
      }
      ctx.body = {code: 0,  message: "成功!"};
    }

    // 课程获取课程内容目录
    async getCourseDirectory() {
      const { ctx, service } = this;
      const { user } =ctx.session;
      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const { courseSlug } = ctx.params;
      let data = null;
      try{
        data = await service.course.getCourseDirectory(courseSlug);
      }catch(e){
        ctx.body = {code: 500, message: e.message};
        return;
      }
      
      
      if(!data){
        ctx.body = {code: 1, message: "未找到对应课程!"};
        return
      }
      ctx.body = {code: 0, data, message: "成功!"};
    }

    // 删除课程
    async deleteCourse() {
      const { ctx } = this;
      const { user } = ctx.session;

      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }
      const {courseSlug} = ctx.params;

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;

      try
      {
          response = await ctx.service.course.deleteCourse(courseSlug, parseInt(ctx.session.user.id, 10), transaction);
      }
      catch(e) {
          console.log(e, 'deleteCourse');
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: "发生服务器错误!"};
          return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 课程内容目录排序后保存
    async saveCourseSort() {
      const { ctx, service } = this;
      const { user } =ctx.session;
      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }


      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});

      const { courseSlug, indics, acrossChapter} = ctx.request.body;
      let data = null;
      try{
        // 先查是否为当前课程的管理员
        const result = await ctx.service.directory.adminCheckLectureAuthDirectory(courseSlug, user.id);
        if (!result) {
          ctx.body = { code: 1, message: '暂无权限'};
          return;
        }
        data = await service.course.saveCourseSort(courseSlug, indics, acrossChapter, transaction);
      }catch(e){
        await transaction.rollback();
        ctx.body = {code: 500, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, data, message: "成功!"};
    }

    // 删除课节
    async deleteSection() {
      const { ctx } = this;
      const { user } = ctx.session;

      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const {courseSlug, chapterName, sectionName} = ctx.request.body;

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;

      try
      {
          response = await ctx.service.course.deleteSection(courseSlug, chapterName, sectionName, transaction);
      }
      catch(e) {
        console.error('deleteSection', e)
          await transaction.rollback();
          ctx.body = {code: 500, data: e.message, message: "发生服务器错误!"};
          return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      if(response){
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        ctx.body = {code: 0, message: '没有数据'};
      }
    }

    // 删除章
    async deleteChapter() {
      const { ctx } = this;
      const { user } = ctx.session;

      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const {courseSlug, chapterName} = ctx.request.body;

      // 开启事务
      let transaction = null;
      let response = null;

      try
      {
        transaction = await ctx.model.transaction({autocommit: false});
        response = await ctx.service.course.deleteChapter(courseSlug, chapterName, transaction);
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      }
      catch(e) {
        console.error('deleteChapter', e);
        
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 500, data: e.message, message: "发生服务器错误!"};
        return;
      }

      if(response){
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        ctx.body = {code: 0, message: '没有数据'};
      }
    }

    // 删除资源文件
    async deleteSourceFile() {
      const { ctx } = this;
      const { user } = ctx.session;

      if(!user || !user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      const {courseSlug, type, name} = ctx.request.body;
        
      if(name.match(/\.\./)) {
        ctx.body = {code: 500, message: "非法的路径！"};
        return;
      }
      
      let response = null;

      try
      {
          response = await ctx.service.course.deleteSourceFile(courseSlug, type, name);
      }
      catch(e) {
          ctx.body = {code: 500, data: e.message, message: "发生服务器错误!"};
          return;
      }

      if(response){
        ctx.body = {code: 0, message: '成功', data: response};
      } else {
        ctx.body = {code: 0, message: '没有数据'};
      }
    }

    async createCourseSections() {
      // 获取学校、课程简写
      const { ctx, service } = this;
      const { model, request, session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        courseName: { type: 'string', required: true},
        courseSlug: { type: 'string', required: true},
        courseDescription: { type: 'string', required: false},
        publish: { type: 'int', min: 0, max: 1, required: false},
        statist: { type: 'object', required: false },
        indics: { type: 'object', required: false },
        containerInfo: { type: 'object', required: false },
        chapterNameMap: { type: 'object', required: false },
        courseType: { type: 'enum', values: ['选修课', '必修课'], required: false },
        saveCode: { type: 'int', min: 0, max: 1, required: false},
        saveRunResult: { type: 'int', min: 0, max: 1, required: false},
        allowPaste: { type: 'int', min: 0, max: 1, required: false},
        allowCopy: { type: 'int', min: 0, max: 1, required: false},
        teams: { type: 'object', required: false },
        teachers: { type: 'object', required: false },
      };

      try {
        ctx.validate(rule, request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 建立入库条目，并将当前用户作为创建者
      const createrID = user.id;
      const item = {
        createrID,
        publish: 1,
        statist: {
          students: 0,
          chapters: 0,
        },
        indics: [],
        containerInfo: {
          image: jupyter.image, 
          cpuLimit: jupyter.cpuRequest,
          memoryRequest: jupyter.memoryRequest,
        },
        courseType: '必修课',
        saveCode: 1,
        saveRunResult: 1,
        allowPaste: 1,
        allowCopy: 1,
        teams: [],
        teachers: [],
      };

      const { body } = request;
      for(const key in rule) {
        const value = body[key];
        if(value === undefined) {
          continue;
        }

        item[key] = value;
      }

      // 启用事务
      const transaction = await model.transaction({autocommit: false});

      let response = null;
      
      try{
        // 入库
        await service.course.createCourseSections(item, transaction);

        // 统计次数
        await ctx.service.course.changeCount(item.courseSlug, 'uploadCount', transaction)
      }
      catch(e) {
        console.error(e, '在createCourseSections时出错');
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 课程内容目录 保存 
    async saveCourseDirectory() {
      
      const { ctx } = this;
      const { user } = ctx.session;
      
      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        newChapterName: { type: 'string', required: true },
        newChapterTitle: { type: 'string', required: true },
        chapterIndex: { type: 'number', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const item = { indics: [] };

      const { body } =  ctx.request;
      for(const key in rule) {
        const value = body[key];
        if(value === undefined) {
          continue;
        }

        item[key] = value;
      }

      const {courseSlug} = ctx.params;

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;

      try
      {
          response = await ctx.service.course.saveCourseDirectory(courseSlug, item, transaction);
      }
      catch(e) {
          console.log(e, 'saveCourseDirectory');
          await transaction.rollback();
          ctx.body = { code: 500, data: e, message: e.message };
          return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 修改章节目录
    async modifyCourseDirectory() {
      const { ctx } = this;
      const { user } = ctx.session;
      
      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        chapterName: { type: 'string', required: true },
        modifyType: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 开启事务
      const transaction = await ctx.model.transaction({autocommit: false});
      
      let response = null;
      const { courseSlug, chapterName, sectionName, modifyType, modifyValue } = ctx.request.body;
      try {
          response = await ctx.service.course.modifyCourseDirectory(courseSlug, { chapterName, sectionName, modifyType, modifyValue }, transaction);
      } catch(e) {
          console.log(e, 'modifyCourseDirectory');
          await transaction.rollback();
          ctx.body = {code: 500, data: e, message: e.message };
          return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      ctx.body = { code: 0, message: '成功', data: response };
    }

    // 课程内容目录 上传
    async contentDirectory(){
      const { ctx, service } = this;
      const { user } = ctx.session;
      
      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        indics: { type: 'array', required: true },
        courseSlug: { type: 'string', format: /^[0-9a-z]{1,}$/, required: true},
        section: { type: 'object', required: true }
      };

      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const node = { indics: [] };

      const { body } =  ctx.request;
      for(const key in rule) {
        const value = body[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 开启事务
      let transaction = null;

      try
      {
        transaction = await ctx.model.transaction({autocommit: false});
        await service.course.contentDirectory(node, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      }
      catch(e) {
        console.log(e, 'contentDirectory');
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功'};
    }
    
    // 课程 数据管理 获取媒体资源
    async getMediaFesources() {
      const {ctx} = this;
      const { user } = ctx.session;
      const { courseSlug } = ctx.params;

      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      let response = null;
      try {
        // 先查是否为当前课程的管理员
        const result = await ctx.service.directory.adminCheckLectureAuthDirectory(courseSlug, userID);
        if (!result) {
          ctx.body = { code: 1, message: '暂无权限'};
          return;
        }

        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets`;
        response = await ctx.service.course.list(pathDir);
      } catch(e) {
        ctx.body = { code: 1, message: e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 课程 媒体资源 下载数据
    async downMediaFesources() {
      const {ctx, service } = this;
      const { session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

      try{
        const { courseSlug, fileName } = ctx.params;
        const { path } = ctx.query;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets/${path ? path : fileName}`;
        const exist = await fs.exists(filePath);

        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.body = await fs.createReadStream(filePath);
  
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 前台学生数据下载
    async webDownStudentData() {
      const {ctx, service } = this;
      const { session } = ctx;
      const { user } = session;

      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      try{
        const { courseSlug, fileName } = ctx.params;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${fileName}`;
        const exist = await fs.exists(filePath);

        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.set('Access-Control-Allow-Origin', '*');
        this.ctx.body = await fs.createReadStream(filePath);
  
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 前台学生数据下载
    async webDownStudentDataInput() {
      const {ctx, service } = this;
      const { session } = ctx;
      const { user } = session;

      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      try{
        const { courseSlug, fileName } = ctx.params;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/input/${fileName}`;
        const exist = await fs.exists(filePath);

        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.body = await fs.createReadStream(filePath);
  
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 前台学生数据删除
    async deleteFileDirWeb() {
      const {ctx} = this;
      const { user } = ctx.session;

      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      const rule = {
        // courseSlug
        courseSlug: {type: 'string', required: true},
        // filedir
        filedir: {type: 'string', required: true},
      }
  
      try {
        ctx.validate(rule, ctx.request.body);
        
        if(ctx.request.body.filedir.match(/\.\./)) {
          throw new Error('非法的路径！');
        }
      }catch(e) {
        console.error('删除学生数据', e.message);
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const { courseSlug, filedir} = ctx.request.body;
      try {
    
        const { config } = app;

        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${filedir}`;

        await fsExtra.remove(pathDir);

      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }

      ctx.body = {code: 0, message: '成功'};
    }

    // 后台删除学生数据
    async deleteStudentFile() {
      const {ctx} = this;
      const { user } = ctx.session;

      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true},

        studentID: {type: 'number', required: true},

        selectFileName: {type: 'string', required: true},
      }
  
      try {
        ctx.validate(rule, ctx.request.body);
      }catch(e) {
        console.error('删除学生数据', e);
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const { courseSlug, studentID, selectFileName } = ctx.request.body;
      try {
        const { config } = app;

        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${studentID}/${selectFileName}`;

        await fsExtra.remove(pathDir);

      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }

      ctx.body = {code: 0, message: '成功'};
    }
    
    // 课程 输入数据 获取输入数据列表
    async getInputData() {
      const {ctx} = this;
      const { user } = ctx.session;
      const { courseSlug } = ctx.params;

      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      let response = null;
      try {
        // 先查是否为当前课程的管理员
        const result = await ctx.service.directory.adminCheckLectureAuthDirectory(courseSlug, userID);
        if (!result) {
          ctx.body = { code: 1, message: '暂无权限'};
          return;
        }

        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/input`;
        
      
        response = await ctx.service.course.list(pathDir);
      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 课程 输入数据 输入数据下载
    async downInputData() {
      const {ctx, service } = this;
      const { session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

      try{
        const { courseSlug, fileName } = ctx.params;
        const { path } = ctx.query;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/input/${path ? path : fileName}`;
        const exist = await fs.exists(filePath);
        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.body = await fs.createReadStream(filePath);
  
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 课程 输入数据 输入数据下载
    async downSectionData() {
      const {ctx } = this;
      const { session } = ctx;
      const { user } = session;

      if (!user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录管理员账户。'};
        return;
      }

      try{
        const { courseSlug, fileName } = ctx.params;
        const { chapterName } = ctx.query;
        const { config } = app;
        const filePath = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${fileName}`;

        const exist = await fs.exists(filePath);
        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.body = await fs.createReadStream(filePath);
  
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
    }

    // 课程 获取 学生数据 列表
    async getStudentDataList() {
      const {ctx} = this;
      const { user } = ctx.session;
      const { courseSlug } = ctx.params;

      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      let response = null;
      try {
        response = await ctx.service.course.getStudentDataList(courseSlug);
      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};

    }

    // 课程 获取 学生数据
    async getStudentData() {
      const {ctx} = this;
      const { user } = ctx.session;
      const { courseSlug, studentID } = ctx.params;
      let userID = null;
      if (user) {
        userID = user.id;
      }

      if(!userID) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      let response = null;
      try {
        // 先查是否为当前课程的管理员
        const result = await ctx.service.directory.adminCheckLectureAuthDirectory(courseSlug, userID);
        if (!result) {
          ctx.body = { code: 1, message: '暂无权限'};
          return;
        }

        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${studentID}`;

        response = await ctx.service.course.list(pathDir);
      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
      
      ctx.body = {code: 0, message: '成功', data: response};
 
    }

    // 后台学生数据 预览
    async previewStudentData() {
      const { ctx } = this;

      try{
        const { courseSlug, fileName, studentID } = ctx.params;
        const { config } = app;
        const filePath =  `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${studentID}/${fileName}`;
        const exist = await fs.exists(filePath);

        if (!exist) {
          ctx.body = { code: 1, message: '未找到下载文件' }
          return;
        }
        const filesize = (await fs.stat(filePath)).size.toString();
        this.ctx.attachment(filePath);
        this.ctx.set('Content-Length', filesize);
        this.ctx.set('Content-Type', 'application/octet-stream');
        this.ctx.set('Access-Control-Allow-Origin', '*');
        this.ctx.body = await fs.createReadStream(filePath);
  
      }catch(e){
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
 
    }


    // 前台获取数据
    async getFileDirWeb() {
      const {ctx} = this;
      const { user } = ctx.session;

      if(!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }
      let userID = user.id;
      
      const { courseSlug } = ctx.params;

      let response = [];
      let inputResponse = [];
      try {
    

        const { config } = app;
        // 拼接主目录
        const pathDir = `${config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}`;
        const inputArr = `${config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/input`
        response = await ctx.service.course.list(pathDir);
        inputResponse = await ctx.service.course.list(inputArr);
      } catch(e) {
        ctx.body = { code: 1, message: '失败' + e.message }
        return;
      }
        
        ctx.body = {code: 0, message: '成功', data: {response, inputResponse}};
    }

    async getChapterResult() {
      const {ctx} = this;
      const { user } = ctx.session;
      if(!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      const rule = {
        // 课程标识
        courseSlug: {type: 'string', required: true},
        // 班级
        classID: {type: 'string', required: true},
        // 章节名称
        chapterName: {type: 'string', required: true},
        //状态
        selectState: {type: 'string', required: true},
      }
  
      try {
        ctx.validate(rule, ctx.query);
      }catch(e) {
        console.log(e, 'getChapterResult');
        return;
      }
      
      const {  selectState } = ctx.query;
      let response = null;

      let sectionNumbers = 0;
      let staticsResult = {};
      let sectionNames = [];
      try{
        const { classID, courseSlug, chapterName } = ctx.query;
        const { userList, staticsResult: res, sectionNumbers: numbers, sectionNames: sections } = await ctx.service.course.getChapterResult({ classID, courseSlug, chapterName });
        response = userList;
        sectionNumbers = numbers;
        staticsResult = res;
        sectionNames = sections;
      }
      catch(e) {
        console.log(e,'0000')
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      const dataReact = response.dataValues  ? response.dataValues : response;
      if(selectState === '全部'){
        ctx.body = { code: 0, data: response, staticsResult, sectionNames, message: '成功' };
        return  
      };

      // 优化课程运行状态筛选逻辑
      const selectMap = {
        运行错误: ['RE', 'WA', 'TL', 'PE', 'RI', '错误', '运行报错'],
        已答题: ['运行通过', 'AC', 'RE', 'WA', 'TL', 'PE', 'RI', '正确', '错误', '未运行'],
        全部通过: [100, 'AC', '正确', '运行通过'],
      }

      const filterList = dataReact.filter(item => {
        const { id, username, name, answer, ...dataItems } = item; // 去除id等用户字段
        const { scoreList } = item;
        const filterState = selectMap[selectState] || [selectState]; // 筛选条件，如果没有在map中设置默认为前端传递的筛选项
        // 未答题和全部通过时，所有项均需满足条件
        if (selectState === '未答题') {
          return !scoreList.length;
        }

        // 状态不是未答题，均为已答题
        if (selectState === '已答题') {
          return scoreList.length;
        }
        
        if (selectState === '全部通过') {
          return scoreList.length >= sectionNumbers && scoreList.every(i => i);
        }

        // 有一项运行错误则为错误
        if (selectState === '运行错误') {
          return scoreList.some(i => !i);
        }
        // 非全部通过时，只需有一项满足条件即可
        return Object.values(dataItems).find(i => filterState.includes(i));
      });

      ctx.body = { code: 0, data: filterList, staticsResult, sectionNames, message: '成功' };
    }

    async getSectionResult() {
      const {ctx} = this;
      const { config } = app;
      const { user } = ctx.session;
      if(!user) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }

      const rule = {
        // 课程标识
        courseSlug: {type: 'string', required: true},
        // 班级
        classID: {type: 'string', required: true},
        // 章节名称
        chapterName: {type: 'string', required: true},
        // 节名称
        sectionName: {type: 'string', required: true},
        //状态
        selectState: {type: 'string', required: true},
      }
  
      try {
        ctx.validate(rule, ctx.query);
      }catch(e) {
        console.log(e, 'getSectionResult');
        return;
      }
      
      const { selectState } = ctx.query;
      let response = null;
      let sectionType = '';
      let staticsResult = {};

      try{
        const { id: userID, teamIDs } = user;
        const { classID, courseSlug, chapterName, sectionName } = ctx.query;

        // 检查权限
        const { course, currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        // 获取该班级名单
        const userIDList = await ctx.service.team.getTeamUser(classID);
        const userIDs = userIDList.map((userValue) => userValue.userID);

        const userList = await ctx.service.user.getUsersByID(userIDs);

        staticsResult = { 全部: userList.length, 未答题: 0, 已答题: 0, 运行错误: 0, 全部通过: 0, 待评分: 0, 全部满分: 0 };

        // 获取相关答题章节
        const section = await ctx.service.section.getSection(courseSlug, chapterName, sectionName);

        // 获取答题记录
        const recordList = await ctx.service.section.getRecordMessage(userIDs, section.id);

        // const { sectionType } = section;
        sectionType = section.sectionType

        let count = 0;
        let key = null;
        let countMap = {};
        const keyMap = {};

        const itemNames = new Set();

        const countTypeMap = {
          'code': 0
        };
        let answerKeys = [];

        if (sectionType === 'OJ') {
          // 构成FSP文件位置
          const fspFilePath = `${config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.xml`;
          count = await getInputLength(fspFilePath);
          key = '数据';
        } else if (sectionType === 'AI') {
          const sectionContent = await ctx.service.course.querySectionContentAndType(courseSlug, chapterName, sectionName, true, userID, course, currentSection);
          if (sectionContent) {
            const { cells } = sectionContent.content;
            cells.forEach((cell) => {
              const { metadata, cell_type } = cell;
              const { type: fileType, UUID, fileName, unopened = false } = metadata;
              //只看代码块
              if (cell_type !== 'code') {
                return;
              }
              
              // 图片视频资源不展示
              if(fileType === 'resources' || fileType === 'filepreviewer') {
                return;
              }

              const code = cell.source && cell.source.length ? cell.source[0] : '';

              // 隐藏代码块不显示
              if (code.match(/^# >隐藏并自动执行/)) {
                return count;
              }

              count += 1;
              countMap[count] = UUID;

              // 代码识别是否为微应用
              if (fileType && fileType !== 'resources' && fileType !== 'filepreviewer') {
                if (!countTypeMap[fileType]) {
                  countTypeMap[fileType] = 0;
                }
  
                countTypeMap[fileType] += 1;

                const microCodeConfig = microTypeNameMap[fileType]
                if (!microCodeConfig) {
                  ctx.body = { code: 1, message: `不支持的微应用${fileType}!` };
                  return;
                }  

                // 如果是不开放的话，就不计数
                if (unopened) {
                  return;
                }
                const microTypeName = microCodeConfig.type;
                const microTypeExt = microCodeConfig.extName;
                keyMap[count]= `${microTypeName}-${countTypeMap[fileType]}-${fileName}.${microTypeExt}`;
                return count;
              } else {
                countTypeMap['code'] += 1;
                // 如果是不开放的话，就不计数
                if (unopened) {
                  return;
                }
                keyMap[count] = `代码${countTypeMap['code']}`;
                return count;
              }
            });
          }

          key = '代码';
        } else if (sectionType === 'OI') {
          // 构成客观题XML文件位置
          const filePath = `${config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.xml`;
          const fileContent = await ctx.service.course.getFile(filePath);
          const { questions } = getOIQuestion(fileContent);

          for (const question of questions) {
            const { questionType } = question;
            if (questionType === '文本'){
              continue;
            }

            countMap[count] = question;
            count += 1;
          }
          key = '题目';
        } else if (sectionType === 'Access' || sectionType === 'Excel') {
          const filePath = `${config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.json`;
          const fileContent = await ctx.service.course.getFile(filePath, true);
          const { instructions } = fileContent;
          count = instructions && instructions.length ? instructions.length : 0;
          key = '步骤';
        } else if (sectionType === 'Scratch') {
          const filePath = `${config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.json`;
          const fileContent = await ctx.service.course.getFile(filePath, true);
          const { judgeSteps } = fileContent;
          count = judgeSteps && judgeSteps.length ? judgeSteps.length : 0;
          key = '步骤';
        } else if (sectionType === 'PPT') {
          key = '页面';
        } else if (sectionType === 'CodeBlank') {
          const filePath = `${config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.code`;
          const fileContent = await ctx.service.course.getFile(filePath, true);
          const { questions } = fileContent;
          const question = questions[0];

          if (!question) {
            throw new Error('题目不存在');
          }
          const { answer = {} } = question;
          answerKeys = Object.keys(answer);
          count = answerKeys.length;
          key = '填空';
        } else if (sectionType === 'MicroBit') {
          key = '项目';
        }

        // 合并数据
        for (let i = 0; i < userList.length; i += 1) {
          const userValue = userList[i];
          const userID = userValue.id;

          if (sectionType === 'Scratch') {
            userValue.score = {};
            userValue.details = [];
          }

          // 获取该用户的答题记录
          let record = recordList.find(value => value.userID === userID);

          if(record && record.record && sectionType === 'PPT') {
            const { totalPages } = record.record;
            count = totalPages;
          }

          if(record && record.record && sectionType === 'MicroBit') {
            // const { fileName } = record.record;
            count = 1;
          }

          // 提交时间
          userValue.submitTime = record ? record.dataValues.updated_at : null;

          if (!count) {
            continue;
          }

          for (let i = 0; i < count; i++) {
            if (!record || !record.record) {

              const keyValue = keyMap && keyMap[i + 1] ? keyMap[i + 1] : key;
              if (!keyValue) {
                continue;
              }

              userValue[sectionType === 'AI' ? `${keyValue}` : `${keyValue}${i + 1}`] = '未答题';
              userValue[sectionType === 'AI' ? `${keyValue}学生答案` : `${keyValue}${i + 1}学生答案`] = '';

              continue;
            }

            switch(sectionType)
            {
              case 'OJ':
                {
                  const { detail } = record.record;
                  const OJStateMap = {
                    3: 'RI', // 表示程序执行中
                    4: 'AC', // 表示程序的输出与标准输出完全一致。
                    5: 'PE', // 表示程序的输出在不考虑空白(空格/回车\r\n/Tab)的情况下, 与标准输出完全一致。
                    6: 'WA', // 错误的答案
                    10: 'RE', // 运行错误
                    7: 'TL', // 超时
                  };
                  const stateCode = detail[i].state;
                  userValue[`${key}${i + 1}`] = stateCode ? OJStateMap[stateCode] : 0;
                  itemNames.add(`${key}${i + 1}`);
                }
                break;
              case 'Excel':
              case 'Access':
                {
                  const { steps } = record.record;

                  userValue[`${key}${i + 1}`] = steps[i] && steps[i].status && steps[i].status === 'finish' ? '正确' : '错误';
                  itemNames.add(`${key}${i + 1}`);
                  break;
                }
              case 'PPT':
                {
                  const { page } = record.record;
                  userValue[`${key}${i + 1}`] = page > i ? '已读' : '未读';
                  itemNames.add(`${key}${i + 1}`);
                  break;
                }
              case 'Scratch':
                {
                  const { judgeSteps } = record.record;
                  console.log(judgeSteps);
                  // if (judgeSteps[i] == undefined){
                  //   itemNames.add(`${key}${i + 1}`);

                  //   break;
                  // }
                  let result = judgeSteps[i] && judgeSteps[i].manual ? ( judgeSteps[i].rate || judgeSteps[i].rate === 0 ? judgeSteps[i].rate : '未评分' ) : (judgeSteps[i] && judgeSteps[i].pass ? judgeSteps[i].score : '未答题');
                  userValue[`${key}${i + 1}`] = result;

                  if (judgeSteps[i]?.score !== undefined) {
                    userValue.score[`${key}${i + 1}`] = judgeSteps[i].score;
                    userValue.details.push(judgeSteps[i]);
                  }
                  // userValue.score[`${key}${i + 1}`] = judgeSteps[i].score;
                  

                  itemNames.add(`${key}${i + 1}`);
                  break;
                }
              case 'MicroBit':
                {
                  const { fileName } = record.record;
                  userValue[`${key}${i + 1}`] = fileName ? '已提交' : '未提交';
                  itemNames.add(`${key}${i + 1}`);
                  break;
                }
                break;
              case 'OI':
                {
                  if(!record.record){
                    throw Error('暂无答题记录！')
                  }
                  const answer = record.record;
                  const { UUID, questionType } = countMap[i]; 
                  if (answer[UUID]){
                    const questionAns = answer[UUID];
                    if (questionAns)  {
                      if (!userValue.answer) {
                        userValue.answer = {};
                      }

                      switch (questionType) {
                        case '多选题':
                          questionAns.answer = questionAns.answer && questionAns.answer.length ? questionAns.answer.join('、') : '';
                          break;
                        case '填空题':
                          questionAns.answer = questionAns.answer && questionAns.answer.length ? questionAns.answer.map((i) => i.text || '').join('、') : '';
                          break;
                        default:
                          break;
                      }
                      userValue.answer[`${key}${i + 1}`] = questionAns.answer;
                      userValue[`${key}${i + 1}`] = questionAns.status ? '正确' : questionAns.answer ? '错误' : '未答题';
                      userValue[`${key}${i + 1}学生答案`] = questionAns.answer ? questionAns.answer : '';
                    }
                  } else {
                    if (!userValue.answer) {
                      userValue.answer = {};
                    }
                    userValue[`${key}${i + 1}`] = '未答题';
                    userValue[`${key}${i + 1}学生答案`] = '';
                  }

                  itemNames.add(`${key}${i + 1}`);
                }
                break;
              case "AI":
                {
                  const AIContent = record.record;
                  const UUIDKey = countMap[i + 1];
                  const keyValue = keyMap[i + 1];
                  if (!keyValue) {
                    continue;
                  }
                  if (!AIContent || !AIContent.UUIDsMap || !AIContent.UUIDsMap[UUIDKey]) {
                    userValue[`${keyValue}`] = '未答题';
                    continue;
                  }
                  if (AIContent.UUIDsMap[UUIDKey].microAppFileType) {
                    userValue[`${keyValue}`] = '运行通过';
                    continue;
                  }
                  userValue[`${keyValue}`] = AIContent.UUIDsMap[UUIDKey].status;
                  userValue[`${keyValue}学生答案`] = JSON.stringify(AIContent.UUIDsMap[UUIDKey].code);

                  itemNames.add(`${keyValue}`);
                }
                break;
              case 'CodeBlank':
                {
                  if(!record || !record.record){
                    throw Error('暂无答题记录！')
                  }

                  const { submitTimes, ...studentAnswer } = record.record;

                  userValue[`${key}${i + 1}`] = studentAnswer[answerKeys[i]] && studentAnswer[answerKeys[i]].status ? '正确' : studentAnswer[answerKeys[i]].studentAnswer ? `错误` : '未答题';
                  userValue[`${key}${i + 1}学生答案`] = studentAnswer[answerKeys[i]].studentAnswer ? studentAnswer[answerKeys[i]].studentAnswer : '';
                  itemNames.add(`${key}${i + 1}`);
                }
                break;
              default:
                throw new Error('未知的类型' + sectionType);
            }
          }

          if (sectionType === 'Scratch') {
            const { details } = userValue;

            if (details.length) {
              staticsResult['已答题'] += 1;
            } else {
              staticsResult['未答题'] += 1;
            }
  
            if (details.find(detail => detail.manual && detail.rate === undefined)) {
              staticsResult['待评分'] += 1;
            }
  
            if (details.length && details.every(detail => detail.pass || (detail.rate && detail.rate >= detail.score))) {
              staticsResult['全部满分'] += 1;
            }
          }

        }

        response = { list: userList, count, sectionType, itemNames: [...itemNames], keyMap, staticsResult };
      }
      catch(e) {
        console.log(e,'0000')
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      const dataReact = response;
      if(selectState === '全部'){
        ctx.body = { code: 0, data: response, message: '成功' };
        return;
      }

      if (sectionType === 'AI') {
        const filterList  = dataReact.list.filter(item => {
          const { id, username, name, answer, index, ...dataItems } = item; // 去除id等用户字段
          // const filterState = [selectState]; // 筛选条件，如果没有在map中设置默认为前端传递的筛选项
          const valueArr = Object.values(dataItems);
          // 未答题和运行通过时，所有项均需满足条件
          if (selectState === '运行通过' || selectState === '未答题') {
            return valueArr.every(i => selectState === i);
          }

          if (selectState === '运行报错') {
            // 运行报错时，只需有一项满足条件即可
            return valueArr.find(i => selectState === i);
          }
          
          return valueArr.every(i => i !== '运行报错') && valueArr.find(i => '运行通过' !== i) && valueArr.find(i => '未答题' !== i);
        })
        ctx.body = { code: 0, data: { ...dataReact, list: filterList }, message: '成功' };
        return
      }

      // 优化课程运行状态筛选逻辑
      const selectMap = {
        运行错误: ['RE', 'WA', 'TL', 'PE', 'RI', '错误', '运行报错'],
        已答题: ['运行通过', 'AC', 'RE', 'WA', 'TL', 'PE', 'RI', '正确', '错误', '未运行', '已提交', '已读'],
        全部通过: [100, 'AC', '正确', '运行通过', '已提交'],
      }

      const filterList = dataReact.list.filter(item => {
        const { id, username, name, answer, score, details, submitTime, ...dataItems } = item; // 去除id等用户字段
        const filterState = selectMap[selectState] || [selectState]; // 筛选条件，如果没有在map中设置默认为前端传递的筛选项
        // 未答题和全部通过时，所有项均需满足条件
        if (selectState === '全部通过' || selectState === '未答题') {
          return Object.values(dataItems).every(i => filterState.includes(i));
        }

        // scratch课程处理
        if(selectState === '待评分') {
          return details.find(detail => detail.manual && detail.rate === undefined);
        }

        if (selectState === '全部满分') {
          return details.length && details.every(detail => detail.pass || (detail.rate && detail.rate >= detail.score));
        }

        if (sectionType === 'Scratch' && selectState === '已答题') {
          return details.length;
        }

        // 非全部通过时，只需有一项满足条件即可
        return Object.values(dataItems).find(i => filterState.includes(i));
      });

      ctx.body = { code: 0, data: { ...dataReact, list: filterList }, message: '成功' };
    }

    async updateAIFile() {
      const {ctx} = this;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        content: {type: 'object', required: true, allowEmpty: false },
        courseSlug: {type: 'string', required: true, allowEmpty: false },
        sectionType: {type: 'string', required: true, allowEmpty: false },
        chapterName: {type: 'string', required: true, allowEmpty: false },
        sectionName: {type: 'string', required: true, allowEmpty: false },
        sectionRecord: {type: 'object', required: false },
      };

      ctx.validate(rule, ctx.request.body);
      let transaction = await ctx.model.transaction({autocommit: false});

      const {content, courseSlug, chapterName, sectionName, sectionType, sectionRecord } = ctx.request.body;
      try{
        if (sectionRecord) {
          await ctx.service.section.linkSectionProject(courseSlug, chapterName, sectionName, sectionRecord, content);
        }
        if (sectionType === 'AI') {
          await ctx.service.course.updateAIFile(courseSlug, chapterName, sectionName, content);
          // 保存历史记录
          const query = { userID, courseSlug, chapterName, sectionName, fileContent: JSON.stringify(content), modifiedType: '发布' };
          await ctx.service.section.saveHistoryRecords(query, transaction);
        }
      }catch(e){
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 

      ctx.body = {code: 0, message: "成功!"};
    }

    // 修改xml文件内容
    async updateXMLFile(){
      const {ctx} = this;
      const { user } = ctx.session;
      const redis = app.redis.get('judge');
      const { config } = app;
      const { schoolSlug } = ctx;

      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        content: {type: 'object', required: true, allowEmpty: false },
        courseSlug: {type: 'string', required: true, allowEmpty: false },
        sectionType: {type: 'string', required: true, allowEmpty: false },
        chapterName: {type: 'string', required: true, allowEmpty: false },
        sectionName: {type: 'string', required: true, allowEmpty: false },
      };

      ctx.validate(rule, ctx.request.body);

      const { courseSlug, chapterName, sectionName } = ctx.request.body;
      let transaction = await ctx.model.transaction({autocommit: false});
      try{
        const xmlArgs = { ...ctx.request.body, userID };
        await ctx.service.course.updateXMLFile(xmlArgs, transaction);
      }catch(e){
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      
      // 后台修改文件，清掉缓存
      const filePath = `${config.file.dir}/${schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.xml`;;
      const fileKey = `q_${filePath}`;

      await redis.del(fileKey);
      ctx.body = {code: 0, message: "成功!"};
    }

    async createCourseFile() {
      const {ctx} = this;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        chapterName: {type: 'string', required: true, allowEmpty: false },
        sectionName: {type: 'string', required: true, allowEmpty: false },
        sectionTitle: {type: 'string', required: true, allowEmpty: false },
        sectionType: {type: 'string', required: true, allowEmpty: false },
        ext: {type: 'string', required: true, allowEmpty: false },
        courseSlug: {type: 'string', required: true, allowEmpty: false },
        sectionIndex: {type: 'string', required: false, allowEmpty: false },
      };

      ctx.validate(rule, ctx.request.body);

      const { courseSlug, chapterName, sectionName, sectionTitle, sectionType, sectionIndex, ext, fileExt } = ctx.request.body;
      let transaction = await ctx.model.transaction({autocommit: false});
      try{
        await ctx.service.course.createCourseFile({ courseSlug, chapterName, sectionName, sectionTitle, sectionType, sectionIndex, ext, fileExt }, transaction);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        await transaction.rollback();
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: "成功!"};
    }

    // 列举题目文件 listXmlQuestionData
    async listXmlQuestionData() {
      const {ctx, service} = this;

      const rule = {
        // 题号
        paths: {type: 'array', required: true },
      };
      ctx.validate(rule, ctx.request.body);

      const {paths} = ctx.request.body;

      let response = null;
      try {
        response = await service.course.listXmlQuestionData(paths);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = { code: 0, data: response, message: '成功' };

    }

    // 获取文件内容 getxmldata
    async getxmldata() {
      const {ctx, service} = this;

      const rule = {
        // 题号
        filePath: {type: 'string', required: true },
      };
      ctx.validate(rule, ctx.query);

      const { filePath } = ctx.query;

      let response = null;
      try {
        response = await service.course.getxmldata(filePath);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = { code: 0, data: response, message: '成功' };

    }

    // 获取文件内容
    async getxmldataByCourse() {
      const {ctx, service} = this;
      const { schoolSlug } = ctx;

      const rule = {
        // 题号
        filename: {type: 'string', required: true },
        ext: { type: 'string', required: true },
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        sectionName: {type: 'string', required: true },
      };
      ctx.validate(rule, ctx.query);

      const { filename, ext, courseSlug, chapterName, sectionName } = ctx.query;

      let response = null;
      try {
        // 获取课程文件内容
        const path = await ctx.service.course.getOJSourceFilePath({ schoolSlug, courseSlug, chapterName, sectionName, filename, ext });
        if (!path) {
          console.error('文件不存在！', path);
          ctx.body = {code: 1, message: '文件不存在！'};
          return;
        }

        response = await service.course.getxmldata(path);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = { code: 0, data: response, message: '成功' };

    }

    // 课程进度获取代码 
    async getCodeRecord() {
      const {ctx} = this;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        chapterName: {type: 'string', required: true },
        sectionName: {type: 'string', required: true },
        courseSlug: {type: 'string', required: true },
      };
      ctx.validate(rule, ctx.query);

      const {userID: uid, courseSlug, chapterName, sectionName, index} = ctx.query;
      const { teamIDs } = user;
      
      let response = null;
      
      try {
         // 检查权限
        const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
        response = await ctx.service.course.getCodeRecord(uid, courseSlug, chapterName, sectionName, currentSection, index);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      ctx.body = { code: 0, data: response, message: '成功' };
    }

    // 重命名章
    async renameChapter() {
      const {ctx} = this;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        newChapterName: {type: 'string', required: true },
        newChapterTitle: {type: 'string', required: true },
      };

      ctx.validate(rule, ctx.request.body);

      const { courseSlug, chapterName, newChapterName, newChapterTitle } = ctx.request.body;
      let transaction = await ctx.model.transaction({autocommit: false});
      try{
        await ctx.service.course.renameChapter(courseSlug, chapterName, newChapterName, newChapterTitle, transaction);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        await transaction.rollback();
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: "成功!"};
    }

    // 重命名节
    async renameSection() {
      const {ctx} = this;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        sectionName: {type: 'string', required: true },
        newSectionName: {type: 'string', required: false },
        newSectionTitle: {type: 'string', required: false },
      };

      ctx.validate(rule, ctx.request.body);

      const { courseSlug, chapterName, sectionName, newSectionName, newSectionTitle } = ctx.request.body;
      let transaction = await ctx.model.transaction({autocommit: false});
      try{
        await ctx.service.course.renameSection(courseSlug, chapterName, sectionName, newSectionName, newSectionTitle, transaction);
      }catch(e){
        ctx.body = {code: 1, message: e.message};
        await transaction.rollback();
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: "成功!"};
    }

    async pasteSection() {
      const { ctx, service } = this;
      const { model, session } = ctx;
      const { user } = session;
      const { id: userID, teamIDs } = user;

      const rule = {
        courseSlug: { type: 'string', required: true, allowEmpty: false },
        chapterName: { type: 'string', required: true, allowEmpty: false },
        copySection: { type: 'object', required: true, allowEmpty: false, rule: {
          copyCourseSlug: { type: 'string', required: true, allowEmpty: false },
          copyChapterName: { type: 'string', required: true, allowEmpty: false },
          copySectionName: { type: 'string', required: true, allowEmpty: false },
        }},
      };
  
      try {
        ctx.validate(rule, ctx.request.body);
      } catch(e) {
        console.log('pasteSection', e)
        ctx.body = {code: 400, message: e.message};
        return;
      }

      let response = null;
      let transaction = null;

      try{
        // 启用事务
        transaction = await model.transaction({autocommit: false});

        // 校验权限
        const { copySection, courseSlug, chapterName, resources } = ctx.request.body;
        const { copyCourseSlug, copyChapterName, copySectionName } = copySection;
        const sourceResponse = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, copyCourseSlug, copyChapterName, copySectionName);
        const { currentSection: sourceSection } = sourceResponse;

        // 入库
        response = await service.course.pasteSection({ courseSlug, chapterName, copySection, userID, sourceSection, resources }, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }
      catch(e) {
        if(transaction) {
          await transaction.rollback();
        }

        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async pasteSectionCrossCourse() {
      const {ctx} = this;
      const { model } = ctx;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        schoolSlug: {type: 'string', required: false },
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        copySection: { type: 'object', required: true, allowEmpty: false, rule: {
          copyCourseSlug: { type: 'string', required: true, allowEmpty: false },
          copyChapterName: { type: 'string', required: true, allowEmpty: false },
          copySectionName: { type: 'string', required: true, allowEmpty: false },
        }},
      };
      
      ctx.validate(rule, ctx.request.body);

      const { teamIDs } = user;
      
      let response = null;
      let transaction = null;
      try {
        // 启用事务
        transaction = await model.transaction({autocommit: false});
         // 校验权限
        const { copySection } = ctx.request.body;
        const { copyCourseSlug, copyChapterName, copySectionName } = copySection;
        const sourceResponse = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, copyCourseSlug, copyChapterName, copySectionName);
        const { currentSection: sourceSection } = sourceResponse;

        const sectionQuery = { ...ctx.request.body, userID, sourceSection };
        response = await ctx.service.course.pasteSectionCrossCourse(sectionQuery, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();   
      }catch(e){
        if(transaction) {
          await transaction.rollback();
        }
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      ctx.body = { code: 0, data: response, message: '成功' };
    }

    async clearStudentsRecord() {
      const {ctx} = this;
      const { model } = ctx;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        sectionName: {type: 'string', required: false },
        classID: {type: 'number', required: true },
        clearInfo: {type: 'number', required: true },
      };
      
      ctx.validate(rule, ctx.request.body);

      const { teamIDs } = user;
      
      // 启用事务
      const transaction = await model.transaction({autocommit: false});
      try {
         // 校验权限
        const { courseSlug, chapterName, sectionName, classID, clearInfo } = ctx.request.body;
        if (sectionName) {
          const sourceResponse = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName, transaction);
          const { currentSection } = sourceResponse;
          const { sectionID, sectionType } = currentSection;
          await ctx.service.course.clearStudentsRecord([sectionID], classID, clearInfo, courseSlug, transaction, sectionType)
        } else {
          // 没有sectionName的时候，需要查询一下，获取当前章下的所有节
          const course = await model.Course.findOne({
            where: {
              courseSlug
            },
            transaction,
            raw: true
          })
          const currentChapter = course.indics.find(indicsitem => indicsitem.chapterName === chapterName);
          if (!currentChapter) {
            throw new Error('当前章节不存在')
          };
          const sectionIDs = [];
          currentChapter.sections.forEach(section => {
            sectionIDs.push(section.sectionID)
          })
          await ctx.service.course.clearStudentsRecord(sectionIDs, classID, clearInfo, courseSlug, transaction) 
        }
      }catch(e){
        console.log('e', e)
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();  
      ctx.body = { code: 0, message: '成功' };
    }

    async clearStudentRecord() {
      const {ctx} = this;
      const { model } = ctx;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        sectionName: {type: 'string', required: true },
      };
      
      ctx.validate(rule, ctx.request.body);

      
      // 启用事务
      const transaction = await model.transaction({autocommit: false});
      try {
         // 校验权限
        const { courseSlug, chapterName, sectionName } = ctx.request.body;
        await ctx.service.course.clearStudentRecord(courseSlug, chapterName, sectionName, userID, transaction);
      }catch(e){
        console.log('clearStudentRecord', e)
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();  
      ctx.body = { code: 0, message: '成功' };
    }

    async batchSwitchChapter() {
      const {ctx} = this;
      const { model } = ctx;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        chapterName: {type: 'string', required: true },
        classID: {type: 'number', required: true },
        batchSwitchChapterType: {type: 'string', required: true },
        batchSwitchChapterOption: {type: 'string', required: true },
      };

      ctx.validate(rule, ctx.request.body);
      
      // 启用事务
      const transaction = await model.transaction({autocommit: false});
      try {
         // 校验权限
        const { courseSlug, chapterName, classID, batchSwitchChapterType, batchSwitchChapterOption } = ctx.request.body;
        await ctx.service.course.batchSwitchChapter(courseSlug, chapterName, classID, batchSwitchChapterOption, batchSwitchChapterType, transaction)
      }catch(e){
        console.log('e', e)
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();  
      ctx.body = { code: 0, message: '成功' };
    }

    async publishEliteCourse() {
      const { ctx } = this;
      const { model } = ctx;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        publishCourseInfo: {type: 'string', required: false },
        detailList: {type: 'object', required: true },
        status: {type: 'string', required: false },
      };

      ctx.validate(rule, ctx.request.body);
      
      // 启用事务
      const transaction = await app.mainModel.transaction({ autocommit: false });
      try {
         // 校验权限
        const { courseSlug, publishCourseInfo, detailList, status = '发布', clientID, requestID } = ctx.request.body;
        await ctx.service.course.publishEliteCourse({ courseSlug, status, publishCourseInfo, detailList, clientID, requestID }, transaction);
      } catch(e) {
        console.log('publishEliteCourse', e)
        await transaction.rollback();
        ctx.body = { code: 1, message: e.message };
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();  
      ctx.body = { code: 0, message: '成功' };
    }

    async importEliteCourse() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;
      const { mainModel } = app;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }

      const { clientID, requestID } = ctx.request.body;
      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      // 启用事务
      const mainTransaction = await app.mainModel.transaction({ autocommit: false });
      try {
         // 校验权限
        const { importEliteCourseSlug, importEliteSchoolSlug, newImportCourseName, newImportCourseSlug } = ctx.request.body;

        const eliteSchoolDir = `${app.config.file.eliteCoursesDir}/${importEliteSchoolSlug}`;
        const eliteCourseDir = `${app.config.file.eliteCoursesDir}/${importEliteSchoolSlug}/${importEliteCourseSlug}`;

        // 导入课程
        console.log('eliteCourseDir', eliteCourseDir)
        const eliteCourseExistCheck = await fs.exists(eliteCourseDir);
        if (!eliteCourseExistCheck) {
          throw new Error('精品课程不存在')
        }

        const existCourse = await mainModel.EliteCourses.findOne({
          where: {
            schoolSlug: importEliteSchoolSlug,
            courseSlug: importEliteCourseSlug,
          },
          attributes: ['indics'],
          raw: true,
          mainTransaction
        });

        if (!existCourse) {
          throw new Error('精品课程记录不存在')
        }

        const { indics } = existCourse;

        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          clientID,
          requestID,
          eliteSchoolDir, 
          importEliteCourseSlug, 
          userID, 
          newImportCourseSlug, 
          newImportCourseName,
          indics,
          schoolSlug,
          type: 'importEliteCourses'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

      } catch(e) {
        console.log('importEliteCourse', e)

        await mainTransaction.rollback();
        ctx.body = { code: 1, message: e.message };
        return;
      }

      // 如果不是压力测试用户则提交事务
      await mainTransaction.commit();
      ctx.body = { code: 0, message: '成功' };
    }

    async exportCourseProgressExcel() {
      const { ctx } = this;
      const { user } = ctx.session;
      let userID = null;
      if (user) {
        userID = user.id;
      }
      if(!userID || !user.adminAuthority) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      const rule = {
        courseSlug: {type: 'string', required: true },
        classIDs: {type: 'string', required: true },
      };

      ctx.validate(rule, ctx.query);

      const { courseSlug, classIDs } = ctx.query;

      let res = null;
      try {
        res = await ctx.service.course.exportCourseProgressExcel({ courseSlug, classIDs: JSON.parse(classIDs) });
      } catch(e) {
        ctx.body = { code: 500, message: e.message };
        return;
      }

      ctx.body = { code: 0, message: "成功!", data: res };
    }
  }

  return CourseController;
}
