'use strict';

module.exports = app => {
  class ThirdPartController extends app.Controller {
    // 获取权限
    async getComputerRoomPermission() {
      const { ctx } = this;
      let response;
      try
      {
        // 获取权限
        response = await this.service.thirdPart.getComputerRoomPermission();
      }
      catch(e)
      {
        ctx.body = {code: 1, message: e.message};
        // await transaction.rollback();
        return;
      }
      
      // await transaction.commit();

      // console.log('response:',response)
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 占用机房
    async registy() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 机房名称
        lab: {type: 'string', required: true},
        // mac地址
        mac: {type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      // const requestBody = ctx.request.body;
      // const node = {};
      // for(const key in rule) {
      //   const value = requestBody[key];
      //   if(value === undefined) {
      //     continue;
      //   }

      //   node[key] = value;
      // }

      // node.createUserID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.thirdPart.registy(ctx.request.body.lab, ctx.request.body.mac, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 发布远程题库
    async uploadQuestionBank() {
      const { ctx } = this;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      let response;
      try
      {
        // 获取权限
        response = await this.service.thirdPart.uploadQuestionBank(parseInt(ctx.params.id), transaction);
      }
      catch(e)
      {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      await transaction.commit();

      // console.log('response:',response)
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getQuestionBank() {
      const { ctx } = this;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      let response;
      try
      {
        // 获取权限
        response = await this.service.thirdPart.getQuestionBank(parseInt(ctx.params.id), ctx.query.needDelete, transaction);
      }
      catch(e)
      {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      await transaction.commit();

      // console.log('response:',response)
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getQuestionBankCallBack() {
      const { ctx } = this;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      let response;
      try
      {
        // 获取权限
        response = await this.service.thirdPart.getQuestionBankCallBack(parseInt(ctx.params.id), ctx.query.needDelete, transaction);
      }
      catch(e)
      {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      await transaction.commit();

      // console.log('response:',response)
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // // 获取资源中心图片资源
    // async uploadToHxrMain() {
    //   const {ctx, service} = this;
    //   let response = null;
    //   const data = ctx.request.body;
    //   // 调用接口
    //   try
    //   {
    //     // 获取时间戳
    //     const timestamp = await service.thridParty.getTimestampAsSecondFormat(new Date());

    //     const node = {
    //       app_id: app.config.platHxrMainAPI.app_id,
    //       // secret: app.config.platHxrMainAPI.secret,
    //       data,
    //       timestamp
    //     };
    //     // 获取键值对
    //     const sign = await service.thridParty.getSingAndNode(node, timestamp);
        
    //     // const cookieNode = {
    //     //   // app_id: app.config.platHxrMainAPI.app_id,
    //     //   // secret: app.config.platHxrMainAPI.secret,
    //     //   timestamp
    //     // };
    //     // // 获取键值对
    //     // const cookieSign = await service.thridParty.getSingAndNode(cookieNode, timestamp);

    //     response = await service.thridParty.uploadToHxrMain({ ...node, sign });
    //   }
    //   catch(e) {
    //     console.log('e', e)
    //     ctx.body = {code: 500, message: e.message};
    //     return;
    //   }
    //   ctx.body = {code: 0, data: response, message: "成功!"};
    // }

    // 获取各校可获取题库列表
    async getSchoolTrainQuestionBankList() {
      const { ctx } = this;
      let response;
      try
      {
        // 获取权限
        response = await this.service.thirdPart.getSchoolTrainQuestionBankList();
      }
      catch(e)
      {
        ctx.body = {code: 1, message: e.message};
        // await transaction.rollback();
        return;
      }
      
      // await transaction.commit();

      // console.log('response:',response)
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 创建训练列表
    async getOrCreateQuestionBank() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题库名称
        name: {type: 'string', required: true},
        fromURL: {type: 'string', required: true},
        needDelete: {type: 'boolean', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.ifSelfBuilding = 1;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练列表
        response = await this.service.thirdPart.getOrCreateQuestionBank(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

  }

  return ThirdPartController
}