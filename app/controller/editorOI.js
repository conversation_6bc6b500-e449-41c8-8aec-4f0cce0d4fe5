'use strict';
const { getOIQuestion } = require('../utils/fps');

module.exports = app => {
  class EditorOIController extends app.Controller {
    async submit() {
        // 获取当前时间
        const nowTime = new Date().getTime();
        const { ctx } = this;
        const { session } = ctx;
        const { config } = app;

        // 从会话中获取schoolSlug，isAdmin和userID
        const { user } = session;
        const { id: userID, schoolSlug, isAdmin, teamIDs } = user;

        // 收到请求指令
        const command = ctx.request.body;

        if (!session.user) {
            ctx.body = { code: 1, message: '请先登录'};
            return;
        }

        // 记录相关字段
        command.schoolSlug = schoolSlug;
        command.userID = userID;
        command.teamIDs = teamIDs;
        
        // 取一下Redis实例
        const redis = app.redis.get('judge');

        // 如果是非管理员用户，通过提交锁机制，限制其过快提交
        if(!isAdmin) {
          const submitLockKey = `s_${schoolSlug}_u_${userID}_oi`;

          // 判断该用户两次提交间隔是否大于15秒
          const lastSubmitTime = await redis.get(submitLockKey);
          if(lastSubmitTime) {
            ctx.body = { code: 1, data: { status: 429, message: '两次提交间隔应大于10秒哦！'}, message: '两次提交间隔应大于10秒哦！'};
            return;
          }

          // 未大于15秒，设定提交锁必须超过15秒方可提交
          await redis.set(submitLockKey, nowTime, 'EX', 10);
        }

        // 析出分析权限用到的参数
        const { courseSlug, chapterName, sectionName } = command;
        let filePath = null;
        let transaction = await this.ctx.model.transaction({ autocommit: false });
        let newJudgeResult = null;
         
         try {
           // 获取课程信息并检查权限
          const { currentSection } = await ctx.service.course.checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName);
          const { sectionID, sectionType } = currentSection;
           // 判断课程类型是否正确
          if (sectionType !== 'OI') {
            throw new Error(`课程类型${sectionType}错误！`);
          }

          // 记录配置
          command.sectionID = sectionID;

          // 构成文件位置
          filePath = `${config.file.dir}/${schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.xml`;

          // 文件是否有缓存，有缓存，自缓存直接返回
          const fileKey = `q_${filePath}`;

          let parsedFileContent = await redis.get(fileKey);
          if(parsedFileContent) {
            const { questions, ifShowAnswer } = JSON.parse(parsedFileContent);
            command.rawData = questions;
            command.ifShowAnswer = ifShowAnswer;
          } else {
            try {
              // 没有缓存，则加载客观题XML文件，读取答案展示配置
              const content = await ctx.service.course.getFile(filePath);
              const { questions, ifShowAnswer } = getOIQuestion(content, true);

              // 建立缓存
              const parsedFileContent = JSON.stringify({
                questions,
                ifShowAnswer,
              });

              // 缓存两分钟
              await redis.set(fileKey, parsedFileContent, 'EX', 60 * 2);
              
              command.rawData = questions;
              command.ifShowAnswer = ifShowAnswer;
            } catch(e) {
              console.error(e);
              ctx.body = { code: 1, data: { status: 500, message: `获取不到文件` }, message: `获取不到文件`};
              return;
            }
          }

          // 判分并保存结果
          newJudgeResult = await this.ctx.service.section.OIJudge(command, transaction);
        }
        catch(e) {
          console.log(e, 'submit oi')
          await transaction.rollback();
          ctx.body = { code: 1, data: e };
          return;
        }
        
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = { code: 0, data: newJudgeResult };
      }
  }

  return EditorOIController;
}