module.exports = app => {
    const coursePathMatcher = /course\/(\w+)\/([^\/]+)\/([^\/]+)/;

    class FayeController extends app.Controller {
        // Faye页面上线报告 （每个页面1次）
        async online() {
            const {ctx} = this;
            const { user } = ctx.session;

            // 获取用户信息
            if(!user) {
                ctx.body = {code: 1, message: '请先登录！'};
                return;
            }

            const { id: userID } = user;

            if(!userID) {
                ctx.body = {code: 1, message: '请先登录！'};
                return;
            }

            // 检测入参
            const rule = {
                userID: {
                    type: 'string',
                    required: true,
                },
                clientID: {
                    type: 'string', 
                    required: true 
                },
                sessionID: {
                    type: 'string', 
                    required: true 
                }
            };

            ctx.validate(rule, ctx.request.body);

            // 统计最后活跃时间
            const activeTimeResult = await ctx.service.user.changeActiveTime(userID);

            // 解析入参
            const { userID: clientUserID } = ctx.request.body;
            if(clientUserID != userID) {
                ctx.body = {code: 403, message: "userID不一致!"};
            }

            ctx.body = { code: 0, message: "成功!" };
        }

        // Faye页面停留与离线报告 （每30分钟、关闭页面）
        async report() {
            const { ctx } = this;

            // 检测入参
            const rule = {
                coursePageStayRecords: {
                    type: 'array', 
                    itemType: 'object',
                    rule: {
                        pageURL: "string",
                        duration: "int"
                    },
                    required: false 
                },
                userID: {
                    type: 'string', 
                    required: true 
                },
                disconnectClientID: {
                    type: 'string', 
                    required: false 
                }
            };

            try {
                ctx.validate(rule, ctx.request.body);
            }
            catch(e) {
                console.error("FayeController.report校验错误", e);
                ctx.body = {code: 1, message: e.message};
                return;
            }

            // 解析入参，将其转换为课程、章、节名称
            const { userID, coursePageStayRecords = [], disconnectClientID = null } = ctx.request.body;

            // 保存课程停留时长
            if(coursePageStayRecords.length) {
                // 取不同的课程名称
                const courseSlugSet = new Set();
                const records = [];

                // 按照课程先整理
                for(const coursePageStayRecord of coursePageStayRecords) {
                    const { pageURL, duration } = coursePageStayRecord;

                    const pageURLMatchResult = pageURL.match(coursePathMatcher);
                    if(!pageURLMatchResult) {
                        throw new Error(`无法匹配的URL${pageURL}`);
                    }

                    const courseSlug = pageURLMatchResult[1];
                    const chapterName = pageURLMatchResult[2];
                    const sectionName = pageURLMatchResult[3];

                    courseSlugSet.add(courseSlug);
                    records.push({
                        courseSlug,
                        chapterName,
                        sectionName,
                        duration
                    });
                    // TODO: 应当根据courseSlug和chapterName确认用户是否具有当前章权限
                }

                // 开启事务
                const transaction = await ctx.model.transaction({autocommit: false});
    
                try {
                    // 根据课程名称获取课程章节缓存
                    const courseSlugIDMap = await ctx.service.course.getCourseSlugIndicsMap(Array.from(courseSlugSet), transaction);

                    // 根据课程slug，章、节名称获取SectionID
                    for(const record of records) {
                        const { courseSlug, chapterName, sectionName, duration } = record;
                        const indics = courseSlugIDMap[courseSlug];
                        const currentChapter = indics.find(indic => indic.chapterName === chapterName);
                        if(!currentChapter || !currentChapter.sections) {
                            console.error(`课程${courseSlug}章${chapterName}不存在，无法获取节ID`);
                            continue;
                        }

                        const currentSection = currentChapter.sections.find(section => section.sectionName === sectionName);
                        if(!currentSection) {
                            console.error(`课程${courseSlug}章${chapterName}节${sectionName}不存在，无法获取节ID`);
                            continue;
                        }
                        
                        await ctx.service.course.saveStayTime(currentSection.sectionID, userID, duration, transaction);
                    }
                } catch(e){
                    console.error(e);
                    ctx.body = {code: 1, message: e.message};
                    await transaction.rollback();
                    return;
                }

                // 如果不是压力测试用户则提交事务
                (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
            }

            // 离线通知
            if(disconnectClientID) {
                // 处理资源释放、学校、用户和本次断开连接的页面clientID
                await ctx.service.faye.onPageDisconnect(disconnectClientID);
            }

            // 统计最后活跃时间
            await ctx.service.user.changeActiveTime(userID);

            ctx.body = {code: 0, message: "成功!"};
        }
    }

    return FayeController;
}