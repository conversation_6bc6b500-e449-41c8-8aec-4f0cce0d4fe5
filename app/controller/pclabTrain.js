'use strict';

const { encrypt } = require('../utils/crypto');

// 休眠
function sleep(time) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

module.exports = app => {
  class PclabTrainController extends app.Controller {
    // 获取权限
    async getComputerRoomPermission() {
      const { ctx } = this;
      let response;
      try
      {
        // 获取权限
        response = await this.service.pclabTrain.getComputerRoomPermission();
      }
      catch(e)
      {
        ctx.body = {code: 1, message: e.message};
        // await transaction.rollback();
        return;
      }
      
      // await transaction.commit();

      // console.log('response:',response)
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 占用机房
    async registy() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 机房名称
        lab: {type: 'string', required: true},
        // mac地址
        mac: {type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      // const requestBody = ctx.request.body;
      // const node = {};
      // for(const key in rule) {
      //   const value = requestBody[key];
      //   if(value === undefined) {
      //     continue;
      //   }

      //   node[key] = value;
      // }

      // node.createUserID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练
        response = await this.service.pclabTrain.registy(ctx.request.body.lab, ctx.request.body.mac, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async filterUserWithComputerRoomTrain() {
      const { ctx } = this;

      let {classID} = ctx.query;

      let response = null;
      try{
        response = await ctx.service.pclabTrain.filterUserWithComputerRoomTrain(parseInt(classID, 10));
      }catch(e){
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    async filterBulkUserWithComputerRoomTrain() {
      const { ctx } = this;

      let {classIDs} = ctx.query;

      let response = null;
      try{
        response = await ctx.service.pclabTrain.filterBulkUserWithComputerRoomTrain(classIDs ? classIDs.split(',').map(row => parseInt(row)) : []);
      }catch(e){
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取所有班级列表(精简)
    async getClassListAllLess() {
      const { ctx } = this;

      let response = null;
      try {
        response = await ctx.service.pclabTrain.getClassListAllLess(ctx.query && ctx.query.year ? ctx.query.year : null);
      }catch(e){
        console.log(e, 'getClassListAllLess');
        ctx.body = {code: 500,  message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取所有班级列表(精简)
    async getBulkClassListAllLess() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: false},
        classIDs: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try {
        response = await ctx.service.pclabTrain.getBulkClassListAllLess(node.year ? node.year : null, node.classIDs ? node.classIDs : null);
      }catch(e){
        console.log(e, 'getBulkClassListAllLess');
        ctx.body = {code: 500,  message: e.message};
        return;
      }
    
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 训练 教师登录
    async teacherLogin() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        username:{ type: 'string', required: true},
        password:{ type: 'string', required: true},
        mac: { type: 'string', required: true},
        // 机房名称
        lab: {type: 'string', required: true},
        // 是否跳过许可证
        isSkipLicense: {type: 'bool', required: false},
      };

      const {username, password, mac, isSkipLicense = false} = request.body;

      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }

      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const redis = app.redis.get('session');
      const key = `ip_wrong_passwd_per_5min_${ctx.request.ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);

      // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录10分钟
      if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
        await redis.expire(key, 60 * 60);
        ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
        return;
      }

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }
      
      // 获取用户信息
      let user = await service.pclabTrain.getUserByLogin(username, password);
      
      if(!user)
      {
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        // ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
        ctx.body = { code: 401, message: '登录失败，请检查网址或账号密码错误', status: "auth failed", csrfToken: session.csrfToken };
        // 要求强制验证验证码
        session.needVerifyCode = true;
        return;
      }

      if(!user.adminAuthority) {
        ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', csrfToken: session.csrfToken};
        return;
      }

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        teamIDs: user.teamIDs, // 用户所属班级
      };

      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 生成clientID
      await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      const transaction = await ctx.model.transaction({autocommit: false});

      try
      {
        // 学校名称
        const schoolNameResult = await service.systemConfig.getInfomationByKey('SchoolName');

        // 授权检查
        if (!isSkipLicense) {
          let permissionResult = await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);

          switch(permissionResult.status) {
            case 'ok':
              // 取所有教师账号
              // const allTeacherData = await this.ctx.service.pclabTrain.getAllTeacher();
              // console.log('schoolNameResult:',schoolNameResult)
              // console.log('ok:')
              const permissionResponse = await this.service.pclabTrain.registy(ctx.request.body.lab, ctx.request.body.mac, transaction);
              ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              break;
            case 'overdue':
              ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您没有可用授权，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              break;
            case 'empty':
            default:
              // 有可能在权限回收之后，会有多余的机房，可用数有可能会为负数
              if (permissionResult.availableLicenseCount > 0) {
                // 授权
                const permissionResponse = await this.service.pclabTrain.registy(ctx.request.body.lab, ctx.request.body.mac, transaction);
                permissionResult = await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);
                // console.log('permissionResponse:',permissionResponse);
                ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功!", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              } else if (permissionResult.totalLicenseCount) {
                ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您购买的授权已用尽，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              } else {
                ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您尚未购买过任何授权，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              }
          }
        }

        // 生成临时远程登录口令
        const code = parseInt(Math.random() * 100000000);
        // 在30分钟内，可用口令登录
        await redis.set(`pcLabTrainLoginHxr-${code}`, JSON.stringify({username, password}), 'EX', 30*60);

        // 跳过授权
        if(!ctx.body) {
          ctx.body = { licence: null, code: 0, message: "成功!", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
        }

        // 补上登录口令
        ctx.body.loginCode = code;

        // Faye实时通信客户ID
        const clientID = await service.user.getClientID();
        ctx.body.clientID = clientID;
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message, csrfToken: session.csrfToken};
        await transaction.rollback();
        return;
      }
      await transaction.commit();
    }

    // 教学 教师登录
    async teacherLoginForPLT() {
      const { ctx } = this;
      const { session, service, request, schoolSlug } = ctx;

      // 校验参数
      const rule = {
        username:{ type: 'string', required: true},
        password:{ type: 'string', required: true},
        mac: { type: 'string', required: true},
        // 机房名称
        lab: {type: 'string', required: true},
        // 是否跳过许可证
        isSkipLicense: {type: 'bool', required: false},
      };

      const {username, password, mac, isSkipLicense = false} = request.body;

      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }

      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const redis = app.redis.get('session');
      const key = `ip_wrong_passwd_per_5min_${ctx.request.ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);

      // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录10分钟
      if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
        await redis.expire(key, 60 * 60);
        ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
        return;
      }

      try {
        ctx.validate(rule, request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors, csrfToken: session.csrfToken};
        return;
      }
      
      // 获取用户信息
      let user = await service.pclabTrain.getUserByLogin(username, password);
      
      if(!user)
      {
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        // ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
        ctx.body = { code: 401, message: '登录失败，请检查网址或账号密码错误', status: "auth failed", csrfToken: session.csrfToken };
        // 要求强制验证验证码
        session.needVerifyCode = true;
        return;
      }

      if(!user.adminAuthority) {
        ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', csrfToken: session.csrfToken};
        return;
      }

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        teamIDs: user.teamIDs, // 用户所属班级
      };

      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 生成clientID
      await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      const transaction = await ctx.model.transaction({autocommit: false});

      try
      {
        // 学校名称
        const schoolNameResult = await service.systemConfig.getInfomationByKey('SchoolName');

        // 授权检查
        if (!isSkipLicense) {
          let permissionResult = await this.ctx.service.pclabTrain.getComputerRoomPermissionForPLT(mac, transaction);

          switch(permissionResult.status) {
            case 'ok':
              // 取所有教师账号
              // const allTeacherData = await this.ctx.service.pclabTrain.getAllTeacher();
              // console.log('schoolNameResult:',schoolNameResult)
              // console.log('ok:')
              const permissionResponse = await this.service.pclabTrain.registyForPLT(ctx.request.body.lab, ctx.request.body.mac, transaction);
              ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              break;
            case 'overdue':
              ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您没有可用授权，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              break;
            case 'empty':
            default:
              // 有可能在权限回收之后，会有多余的机房，可用数有可能会为负数
              if (permissionResult.availableLicenseCount > 0) {
                // 授权
                const permissionResponse = await this.service.pclabTrain.registyForPLT(ctx.request.body.lab, ctx.request.body.mac, transaction);
                permissionResult = await this.ctx.service.pclabTrain.getComputerRoomPermissionForPLT(mac, transaction);
                // console.log('permissionResponse:',permissionResponse);
                ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功!", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              } else if (permissionResult.totalLicenseCount) {
                ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您购买的授权已用尽，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              } else {
                ctx.body = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您尚未购买过任何授权，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              }
          }
        }

        // 生成临时远程登录口令
        const code = parseInt(Math.random() * 100000000);
        // 在30分钟内，可用口令登录
        await redis.set(`pcLabTrainLoginHxr-${code}`, JSON.stringify({username, password}), 'EX', 30*60);

        // 跳过授权
        if(!ctx.body) {
          ctx.body = { licence: null, code: 0, message: "成功!", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
        }

        // 补上登录口令
        ctx.body.loginCode = code;

        // Faye实时通信客户ID
        const clientID = await service.user.getClientID();
        ctx.body.clientID = clientID;
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message, csrfToken: session.csrfToken};
        await transaction.rollback();
        return;
      }
      await transaction.commit();
    }

    // 获取训练列表
    async getTrainList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: false},
        series: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try
      {
        // 获取训练列表
        response = await this.service.pclabTrain.getTrainListWithComputerRoomTrain(node);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取全部系列
    async getTrainSeries() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 用户ID
        userID: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try {
        // 创建训练
        response = await ctx.service.pclabTrain.getAllSeries(node);
      } catch(e) {
        console.error(e);
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量获取训练列表
    async getBulkTrainList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: false},
        trainIDs: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try
      {
        // 创建训练
        response = await this.service.pclabTrain.getBulkTrainListWithComputerRoomTrain(node);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练临时文件
    async createTrainFileZip() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        trainID: { type: 'string', required: true},
        enableMP4: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const { trainID, enableMP4 = "false" } = ctx.query;

      let response;
      try
      {
        // 获取训练临时文件
        response = await this.service.pclabTrain.createTrainFileZip(trainID, enableMP4 === 'true');
      }
      catch(e)
      {
        console.error(e, 'createTrainFileZip')
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练临时文件
    async createBulkTrainFileZip() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        trainIDs: { type: 'string', required: true},
        enableMP4: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const { trainIDs, enableMP4 = "false" } = ctx.query;

      let response = {};
      try
      {
        response.train_infos = await this.service.pclabTrain.getBulkTrainListWithComputerRoomTrain({ trainIDs });
        // 获取训练临时文件
        const urlInfo = await this.service.pclabTrain.createBulkTrainFileZip(trainIDs.split(',').map(row => parseInt(row)), enableMP4 === 'true');
        response.train_file_url = urlInfo;
        // response.mp4_files_url = urlInfo.mp4_files_url;
      }
      catch(e)
      {
        // console.error(e, 'createTrainFileZip')
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练临时文件
    async createOneTrainFileZip() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        trainID: { type: 'string', required: true},
        appVersion: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const { trainID, appVersion } = ctx.query;

      let response = {};
      try
      {
        response.train_info = await this.service.pclabTrain.getOneTrainListWithComputerRoomTrain({ trainID, appVersion });
        
        // 获取训练临时文件
        const urlInfo = await this.service.pclabTrain.createOneTrainFileZip(trainID);
        response.train_file_url = urlInfo.train_file_url;
        response.mp4_files = urlInfo.mp4_files_url;
        // response.train_id = trainID;
      }
      catch(e)
      {
        console.error(e, 'createTrainFileZip')
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 上报学生训练结果
    async postTrainPlanRecord() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        mode: { type: 'string', required: false},
        // trainID: { type: 'string', required: true},
        // records: { type: 'object', required: true},
        name: { type: 'string', required: true},
        // session: { type: 'object', required: false},
        duration: { type: 'number', required: true },
        start_time: { type: 'string', required: false},
        end_time: { type: 'string', required: false},
        open_classes: { type: 'string', required: false },
        if_set_wrong_problem_collection: { type: 'number', required: true },
        if_show_wrong_answer: { type: 'number', required: true },
        if_show_correction_results: { type: 'number', required: true },
        if_show_score: { type: 'number', required: true },

        remote_id: { type: 'string', required: false }
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;

      try {
        ctx.validate(rule, ctx.request.body.train_plan); 
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body.train_plan;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      if(!ctx.session.user || !ctx.session.user.adminAuthority){
        ctx.body = {code: 400, data, message: "请先登录"};
        return
      }

      node.session = ctx.session.user;
      node.records = ctx.request.body.train_user_records;
      node.applys = ctx.request.body.applys;

      const transaction = await ctx.model.transaction({autocommit: false});
      let response = null;
      try{
        response = await ctx.service.pclabTrain.postTrainPlanRecord(node, transaction);
      }catch(e){
        console.error('postTrainPlanRecord', e)
        ctx.body = {code: 1, message: '失败' + e.message};
        await transaction.rollback();
        return;
      }
      // console.log('response:',response)
      
      await transaction.commit();
      ctx.body = { code: 0, message: '成功', data: response.id };
    }

    // hxr凭口令登录
    async loginByCode() {
      const { ctx } = this;
      const { session, service, schoolSlug } = ctx;
      // 校验参数
      const rule = {
        code: { type: 'string', required: true},
      };
      // const { mode, trainID, records, session: nodeSession, beginAt, endAt } = node;
      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.log('e:',e)
        ctx.body = {code: 400, message: e.errors};
        return;
      }
      // console.log(ctx.query.code)
      
      const redis = app.redis.get('session');
      let loginCode = await redis.get(`pcLabTrainLoginHxr-${ctx.query.code}`);
      try {
        loginCode = JSON.parse(loginCode)
      } catch (e) {
        ctx.body = {code: 500, data: e, message: 'code错误'};
        return;
      }
      // console.log(loginCode)
      const { username, password } = loginCode;

      let MaintainNotice = null;
      try {
        MaintainNotice = await service.systemConfig.getInfomationByKey('MaintainNotice');
      } catch(e) {
        ctx.body = {code: 500, data: e, message: e.message};
        return;
      }
      if (MaintainNotice && MaintainNotice.value) {
        ctx.body = {code: 500, message: '目前正在维护', csrfToken: session.csrfToken};
        return;
      }
    
      // 是否需要验证码是按照IP获取的，不保存在会话中
      // 如果5分钟内该IP密码出错超过5次，则需要验证码
      const key = `ip_wrong_passwd_per_5min_${ctx.request.ip}`;
      let ipWrongPasswdPer5Min = await redis.get(key);
      ipWrongPasswdPer5Min = (ipWrongPasswdPer5Min === null) ? 0: parseInt(ipWrongPasswdPer5Min, 10);

      // 如果5分钟内该IP出错超过ipWrongPasswd5Min次，则锁定IP无法登录10分钟
      if(ipWrongPasswdPer5Min >= app.config.ipWrongPasswd5Min) {
        await redis.expire(key, 60 * 60);
        ctx.body = { code: 1, message: `您的IP地址${ctx.request.ip}频繁登录出错，怀疑尝试弱密码，已按照安全策略被禁用1小时` };
        return;
      }

      // const needVerifyCode = ipWrongPasswdPer5Min > 5;


      // 如果使用万能验证码，无需校验
      // if (verify && verify !== 'yopu123' && needVerifyCode) {
      //   // 打开多个页面时，验证码与其中一个相同即可登录
      //   const match =  session.captchaCode ? session.captchaCode.some(code=> {
      //     if (code.toLowerCase() === verify.toLowerCase()) return true;
      //    }): false; 

      //   // 验证码是否一致
      //   if(!match){
      //     // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
      //     await redis
      //     .multi()
      //       .incr(key)
      //       .expire(key, 5 * 60)
      //     .exec();

      //     ctx.body = {code: 500, message: '验证码不相同', csrfToken: session.csrfToken};
      //     return;
      //   }
      // }
      
      // 获取用户信息
      let user = await service.user.getUserByLogin(username, password);
      
      if(!user)
      {
        // 增加IP密码错误计数器，若密码错误5分钟内超过45次，则锁定IP无法登录1小时
        await redis
        .multi()
          .incr(key)
          .expire(key, 5 * 60)
        .exec();

        ctx.body = { code: 1, message: '账号或密码错误！', csrfToken: session.csrfToken };
        // 要求强制验证验证码
        session.needVerifyCode = true;
        return;
      }

      if(!user.adminAuthority) {
        ctx.body = {code: 1, message: '只有教师账号具有管理平台登录权限！', csrfToken: session.csrfToken};
        return;
      }

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.schoolSlug,
        teamIDs: user.teamIDs, // 用户所属班级
        ifSuperAdmin: user ? user.ifSuperAdmin : false,
      };

      // // 测试！！！
      // if (ctx.schoolSlug === 'csxx' && user.id === 1) {
      //   session.user.ssoName = 'JIANGJIE'; // ceshi2
      //   session.user.OrgCode = '32010081999';
      // }

      // 验证登录时间
      const clientID = await service.user.getClientID();
      
      // 重置csrf
      ctx.rotateCsrfSecret();

      // 部署标记
      const { deploymentSlug } = app.config.hxr;

      // 统计最后活跃时间
      const activeTimeResult = await service.user.changeActiveTime(user.id);

      ctx.body = {code: 0, message: "成功!", data: { user }, csrfToken: session.csrfToken, deploymentSlug};
    }

    async getEliteTrains() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        year: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { year } = ctx.query;
      let response;
      try {
        response = await ctx.service.pclabTrain.getEliteTrains({ year });
      } catch(e) {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async importTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 学年
        // year: { type: 'string', required: true },

        // 精品试卷id
        selectTrainID: { type: 'string', required: true },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        console.error(e);
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }
      // console.log('node:',node)

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = {};
      let trainInfo;
      try
      {
        // 创建训练
        trainInfo = await ctx.service.pclabTrain.importTrain(node, transaction);
      }
      catch(e)
      {
        console.error(e, 'importTrains')
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      console.log('trainInfo', trainInfo)

      try
      {
        response.train_info = await this.service.pclabTrain.getOneTrainListWithComputerRoomTrain({ trainID: trainInfo.id });
        // 获取训练临时文件
        const urlInfo = await this.service.pclabTrain.createOneTrainFileZip(trainInfo.id);
        response.train_file_url = urlInfo.train_file_url;
        response.mp4_files = urlInfo.mp4_files_url;
      }
      catch(e)
      {
        console.error(e, 'importTrains')
        ctx.body = {code: e.code, message: e.message};
        // await transaction.rollback();
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取临时离线更新文件
    async getUpdateFileZip() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;

      const { user } = ctx.session;
      // 判断是否登录
      if(!user.isAdmin){
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        trainIDs: { type: 'object', required: true},
        classIDs: { type: 'object', required: true},
        mac: { type: 'string', required: true},
        lab: { type: 'string', required: true},
        site_url: { type: 'string', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.message};
        return;
      }

      const { trainIDs, classIDs, mac, site_url, lab, clientID, requestID, } = ctx.request.body;

      let response = {
        train: [],
      };

      // 先核查权限
      const transaction = await ctx.model.transaction({autocommit: false});
      // let ctxBody = {};
      try
      {
        // 部署标记
        // const { deploymentSlug } = app.config.hxr;
        // 获取学校基本信息
        // 获取学校名称
        const schoolNameResult = await this.ctx.service.systemConfig.getInfomationByKey('SchoolName');

        // 获取当前教师账号、密码
        const teacherResult = await this.ctx.service.pclabTrain.getTeacherInfo(user.id);

        let permissionResult = await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);
        console.log('permissionResult:',permissionResult)

        switch(permissionResult.status) {
          case 'ok':
            // 取所有教师账号
            // const allTeacherData = await this.ctx.service.pclabTrain.getAllTeacher();
            // console.log('schoolNameResult:',schoolNameResult)
            // console.log('ok:')
            const permissionResponse = await this.service.pclabTrain.registy(ctx.request.body.lab, ctx.request.body.mac, transaction);
            console.log('permissionResponse:',permissionResponse)
            response.permission = {
              default_password: teacherResult.password,
              schoolName: schoolNameResult.value,
              default_username: teacherResult.username,
              lab,
              site_url,
              licence: encrypt(JSON.stringify(permissionResult)),
            };
            // ctxBody = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功", schoolName: user.schoolName, displayName: user.name, csrfToken: session.csrfToken, user };
            break;
          case 'overdue':
            throw new Error('您没有可用授权，烦请联系025-83466679购买');
            // ctxBody = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您没有可用授权，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
            break;
          case 'empty':
          default:
            // 有可能在权限回收之后，会有多余的机房，可用数有可能会为负数
            if (permissionResult.availableLicenseCount > 0) {
              // 授权
              const permissionResponse = await this.service.pclabTrain.registy(ctx.request.body.lab, ctx.request.body.mac, transaction);
              permissionResult = await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);
              // console.log('permissionResponse:',permissionResponse);
              // response.permission = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功!", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
              response.permission = encrypt(JSON.stringify({
                // MAC: encrypt(mac),
                default_password: teacherResult.password,
                schoolName: schoolNameResult.value,
                default_username: teacherResult.username,
                lab,
                site_url,
                licence: encrypt(JSON.stringify(permissionResult)),
              }));
            } else if (permissionResult.totalLicenseCount) {
              throw new Error('您购买的授权已用尽，烦请联系025-83466679购买');
              // ctxBody = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您购买的授权已用尽，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
            } else {
              throw new Error('您尚未购买过任何授权，烦请联系025-83466679购买');
              // ctxBody = { licence: encrypt(JSON.stringify(permissionResult)), code: 511, message: "您尚未购买过任何授权，烦请联系025-83466679购买", schoolName: schoolNameResult.value, user, csrfToken: session.csrfToken };
            }
        }

      }  catch(e) {
        ctx.body = {code: e.code, message: e.message, csrfToken: session.csrfToken};
        await transaction.rollback();
        return;
      }
      await transaction.commit();

      try
      {
        // 获取训练数据
        for(const trainID of trainIDs) {
          const trainInfoRow = {
            train_id: trainID,
          }
          // 获取训练临时文件数据
          trainInfoRow.train_info = await this.service.pclabTrain.getOneTrainListWithComputerRoomTrain({ trainID });

          const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainID);
          trainInfoRow.trainData = trainData;

          // 获取训练临时文件
          // const urlInfo = await this.service.pclabTrain.createOneTrainFileZipOffline(trainID);
          // trainInfoRow.train_file_url = urlInfo.train_file_url;
          // trainInfoRow.mp4_files = urlInfo.mp4_files_url;
        // response.train_id = trainID;

          response.train.push(trainInfoRow);
        }
        // 获取所有训练id
        response.allTrainIds = await this.service.pclabTrain.getAllTrainIDs();

        // 获取班级信息
        response.classList = await ctx.service.pclabTrain.filterBulkUserWithComputerRoomTrain(classIDs ? classIDs : []);
        response.allClassIds = await ctx.service.pclabTrain.getAllClassIds();

        // 获取教师信息
        response.teacherList = await ctx.service.pclabTrain.getAllTeachers();

        // 获取所有系列
        response.seriesList = await ctx.service.pclabTrain.getAllSeries();

        // await sleep(1000)

        // 生成离线更新文件
        // resultUrl = await this.service.pclabTrain.generateOfflineUpdateFile(response);

        // 提交至队列，各分片轮询
        const userID = user.id;
        const queue = app.queue['train-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          clientID,
          requestID,
          userID,
          data: response,
          schoolSlug, 
          type: 'exportOfflineSyncFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

      } catch(e) {
        console.error(e, 'createTrainFileZip')
        ctx.body = {code: 1, message: e.message};
        return;
      }

      ctx.body = { code: 0, message: '成功，请开启监听' };
    }
    
  }

  return PclabTrainController
}