'use strict';

const getChildren = (rows, parentID, chartDataMap) => {
  if(!rows || !rows.length) {
    return null
  }

  return rows.filter(row => row.parentID === parentID).map(row => {
    const children = getChildren(rows, row.id, chartDataMap);
    if (children && children.length) {
      return {
        ...row,
        children,
        count: chartDataMap[row.id] ? chartDataMap[row.id] : 0,
      }
    }

    if (!chartDataMap[row.id]) {
      return;
    }

    return {
      ...row,
      count: chartDataMap[row.id] ? chartDataMap[row.id] : 0,
    }
  })
}

module.exports = app => {
  class TrainUserQuestionController extends app.Controller {
    // 创建错题集列表
    async createTrainUserQuestion() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题目ID
        questionIDs: {type: 'object', required: true},
        // year
        year: {type: 'string', required: true},
        // 训练名称 来源
        trainPlanName: {type: 'string', required: true},
        // 用户ID
        // userID: {type: 'number', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.userID = ctx.session.user.id;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建错题集列表
        response = await this.service.trainUserQuestion.createTrainUserQuestion(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改错题集列表
    // async putTrainUserQuestion() {
    //   const { ctx } = this;

    //   // 校验参数
    //   const rule = {
    //     // 题目ID
    //     questionID: {type: 'number', required: true},
    //     // 用户ID
    //     userID: {type: 'number', required: true},
    //   };

    //   try {
    //     ctx.validate(rule, ctx.request.body); 
    //   } catch(e) {
    //     ctx.body = {code: 400, message: e.errors};
    //     return;
    //   }

    //   // 获取更新数据
    //   const requestBody = ctx.request.body;
    //   const node = {};
    //   for(const key in rule) {
    //     const value = requestBody[key];
    //     if(value === undefined) {
    //       continue;
    //     }

    //     node[key] = value;
    //   }

    //   // 启用事务
    //   const transaction = await ctx.model.transaction({autocommit: false});

    //   let response;
    //   try
    //   {
    //     // 修改错题集列表
    //     response = await this.service.trainUserQuestion.putTrainUserQuestion(ctx.params.id, node, transaction);
    //   }
    //   catch(e)
    //   {
    //     ctx.body = {code: e.code, message: e.message};
    //     await transaction.rollback();
    //     return;
    //   }
      
    //   await transaction.commit();

    //   ctx.body = {code: 0, message: '成功', data: response};
    // }

    // 获取错题集列表
    async getTrainUserQuestion() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改错题集列表
        response = await this.service.trainUserQuestion.getTrainUserQuestion(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除错题集列表
    async destoryTrainUserQuestion() {
      const { ctx } = this;

      let response;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      try
      {
        // 修改错题集列表
        response = await this.service.trainUserQuestion.destoryTrainUserQuestion(ctx.params.questionID, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除我的错题集列表
    async destoryMyTrainUserQuestion() {
      const { ctx } = this;

      let response;
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      try
      {
        // 修改错题集列表
        response = await this.service.trainUserQuestion.destoryMyTrainUserQuestion(transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取错题集列表列表
    async getTrainUserQuestionList() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        questionType: {type: 'string', required: false},
        trainsUseds: {type: 'object', required: false},
        tagIDs: {type: 'object', required: false},
        year: {type: 'string', required: false},
        sources: {type: 'object', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response;
      try
      {
        // 获取错题集列表
        response = await this.service.trainUserQuestion.getTrainUserQuestionList(node);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }
    
    async getTrainUserQuestionTrain() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题目ID
        questionIDs: {type: 'object', required: true},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      let response;
      try
      {
        // 获取错题集列表
        response = await this.service.trainUserQuestion.getTrainUserQuestionTrain(ctx.request.body.questionIDs);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 标签列表
    async getAllTagTree() {
      const {ctx} = this;
      // 校验参数
      const rule = {
        // status: { type: 'string', required: false},
        // questionBankID: { type: 'string', required: false},
        year: { type: 'string', required: false},
        questionType: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        const chartData = await ctx.service.trainUserQuestion.getTagChartData(node);
        // console.log('chartData:',chartData);
        const chartDataMap = {};
        chartData.forEach(element => {
          chartDataMap[element.tagID] = element.count;
        });
        
        const result = await ctx.service.tag.getTrainTagList();
        const resultData = result && result.train ? result.train : [];
        // console.log('resultData:',resultData);

        const filterResult = resultData.filter(row => !row.parentID);

        response = filterResult
        .map(row => {
          return {
            ...row, 
            children: getChildren(resultData, row.id, chartDataMap),
            count: chartDataMap[row.id] ? chartDataMap[row.id] : 0,
          };
        });

        function getValidTags(node) {
          if (!node) {
            return;
          }

          const { count, children } = node;

          if (count) {
            return node;
          }

          if (children && children.length) {
            const result = children.filter(i => i && getValidTags(i));

            if (result && result.length) {
              return {
                ...node,
                children: result,
              };
            }
          }
        }

        response = response.map((node) => getValidTags(node));

      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }
    
    async getQuestionsCountByTagAndType() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        tagIDs: { type: 'string', required: false},
        year: { type: 'string', required: false},
        trainsUseds: {type: 'object', required: false},
        sources: {type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        response = await ctx.service.trainUserQuestion.getQuestionsCountByTagAndType(node);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }
    
    async destoryBulkTrainUserQuestion() {
      const { ctx } = this;

      let response;
      const transaction = await ctx.model.transaction({autocommit: false});

      try
      {
        // 修改错题集列表
        const { questionIDs = [] } = ctx.request.body;
        response = await this.service.trainUserQuestion.destoryBulkTrainUserQuestion(questionIDs, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }

      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

        
    async getQuestionSource() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        year: { type: 'string', required: false },
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      const userID = this.ctx.session.user.id;

      try{
        response = await ctx.service.trainUserQuestion.getQuestionSource({ userID, ...node });
      }catch(e){
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }

    async getAllUserQuestions() {
      const { ctx } = this;

      let response = null;
      const userID = this.ctx.session.user.id;

      try{
        response = await ctx.service.trainUserQuestion.getAllUserQuestions({ userID });
      }catch(e){
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      ctx.body = { code: 0, message: '成功', data: response };
    }
  }

  return TrainUserQuestionController;
}