'use strict';

const getChildren = (rows, parentID, chartDataMap) => {
  if(!rows || !rows.length) {
    return null
  }

  return rows.filter(row => row.parentID === parentID).map(row => {
    // console.log('row:',row)
    const children = getChildren(rows, row.id, chartDataMap);
    if (children && children.length) {
      return {
        ...row,
        children,
        count: chartDataMap[row.id] ? chartDataMap[row.id] : 0,
      }
    }

    return {
      ...row,
      count: chartDataMap[row.id] ? chartDataMap[row.id] : 0,
    }
  })
}

module.exports = app => {
  class TagController extends app.Controller {
    // 标签新增
    async postTag(){
      const {ctx} = this;

      const rule = {
        // 标签名
        tagName: {type: 'string', required: true},
        // 标签类型
        tagType: {type: 'string', required: true},
        // 父级节点
        parentID: {type: 'number', required: false},
        // 题库ID
        questionBankID: {type: 'number', required: false},
      }

      ctx.validate(rule, ctx.request.body);

      const { tagName, tagType, parentID, questionBankID } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.tag.postTag(tagName, tagType, parentID, questionBankID, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
          // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 批量新增、修改、删除题目知识点
    async bulkEditTag(){
      const {ctx} = this;

      const rule = {
        nodes: {
          type: 'array', required: true, itemType: 'object'
        },
        questionBankID: {type: 'number', required: false},
      }

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const { nodes, questionBankID } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.tag.bulkEditTag(questionBankID, nodes, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: e.message};
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除标签
    async bulkDeleteTags(){
      const {ctx, service} = this;

      if (!ctx.session.adminUser) {
        ctx.body = {code: 1, message: '请先登录账户。'};
        return;
      }
      const rule = {
        // 标签名
        deleteTagids: {type: 'array', required: true},
      
      }

      ctx.validate(rule, ctx.request.body);

      const { deleteTagids } = ctx.request.body;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.tag.bulkDeleteTags(deleteTagids, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

          // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 标签列表
    async getTag() {
      const {ctx, service} = this;
      let response = null;
      try{
        response = await ctx.service.tag.getTag();
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 训练标签列表
    async getTrainTagList() {
      const {ctx} = this;
      let response = null;
      try{
        response = await ctx.service.tag.getTrainTagList();
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 训练标签列表
    async getTrainTagTree() {
      const {ctx} = this;
      // 校验参数
      const rule = {
        status: { type: 'string', required: false},
        questionBankID: { type: 'string', required: true},
        questionType: { type: 'string', required: false},
        // 题目来源试卷系列：只有该系列试卷中出现的题才算
        sourceSeriesID: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.query); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.query;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        const chartData = await ctx.service.tag.getTagChartData(node);
        // console.log('chartData:',chartData);
        const chartDataMap = {};
        chartData.forEach(element => {
          chartDataMap[element.tagID] = element.count;
        });
        const { questionBankID } = ctx.query;
        const result = await ctx.service.tag.getTrainTagList(questionBankID);
        const resultData = result && result.train ? result.train : [];
        // console.log('resultData:',resultData);
        response = resultData.filter(row => !row.parentID).map(row => ({ ...row, children: getChildren(resultData, row.id, chartDataMap), count: chartDataMap[row.id] ? chartDataMap[row.id] : 0 }));
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }
    
    async getQuestionsCountByTagAndType() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        tagIDs: { type: 'object', required: false},
        search: { type: 'string', required: false},
        questionBankID: { type: 'number', required: true},
        status: { type: 'object', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        response = await ctx.service.tag.getQuestionsCountByTagAndType(node);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getQuestionsCountByTagAndTypeAdmin() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        tagIDs: { type: 'object', required: false},
        search: { type: 'string', required: false},
        questionBankID: { type: 'number', required: true},
        status: { type: 'object', required: false},
        // 题目来源试卷系列：只有该系列试卷中出现的题才算
        sourceSeriesID: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        response = await ctx.service.tag.getQuestionsCountByTagAndTypeAdmin(node);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getQuestionCountByTagAndDifficulty() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        tagIDs: { type: 'object', required: false},
        search: { type: 'string', required: false},
        questionBankID: { type: 'number', required: true},
        status: { type: 'string', required: false},
        // 题目来源试卷系列：只有该系列试卷中出现的题才算
        sourceSeriesID: { type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        response = await ctx.service.tag.getQuestionCountByTagAndDifficulty(node);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }
    
    async getQuestionsListByTagAndType() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        tagIDs: { type: 'object', required: false},
        questionType: { type: 'string', required: false},
        search: { type: 'string', required: false},
        questionBankID: { type: 'number', required: true},
        sourceSeriesID: { type: 'string', required: false},
        status: { type: 'object', required: false},
        pageIndex: { type: 'number', required: false, },  // 添加分页参数
        pageSize: { type: 'number', required: false, }   // 添加分页参数
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        response = await ctx.service.tag.getQuestionsListByTagAndType(node);
      }catch(e){
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    async getQuestionsListByTagAndTypeAdmin() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        tagIDs: { type: 'object', required: false},
        questionType: { type: 'string', required: false},
        search: { type: 'string', required: false},
        questionBankID: { type: 'number', required: true},
        status: { type: 'object', required: false},
        // 题目来源
        sourceSeriesID: { type: 'string', required: false},
        sortOrder: { type: 'boolean', required: false },
        pageIndex: { type: 'number', required: false },  // 添加分页参数
        pageSize: { type: 'number', required: false }   // 添加分页参数
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      let response = null;
      try{
        response = await ctx.service.tag.getQuestionsListByTagAndTypeAdmin(node);
      }catch(e){
        console.error(e)
        ctx.body = {code: 1, message: '失败' + e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }


    // 标签查重
    async findTag() {
      const {ctx, service} = this;

      const { tagName } = ctx.query;

      const response = await ctx.service.tag.findTag(tagName);
      if(response){
        ctx.body = {code: 0, message: response};
      } else {
        ctx.body = {code: 1};
      }
    }

    // 批量根据题目id与标签id修改标签关联
    async updateBulkQuestionTag() {
      const {ctx, service} = this;

      const rule = {
        nodes: {
          type: 'array',
          itemType: 'object',
          rule: {
            id: {type: 'number', required: true},
            // 标签
            tags: {type: 'object', required: false},
            // 标签
            difficultyConfig: {type: 'string', required: false},
          },
        }
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      const nodes = ctx.request.body.nodes.map(row => ({ id: row.id, tags: row.tags, difficultyConfig: row.difficultyConfig }));

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      try {
        await service.tag.updateBulkQuestionTag(nodes, transaction);
      } catch(e) {
        ctx.body = { code: 500, message: e.message };
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = { code: 0, message: '成功' };
    }

  }
  return TagController;
}