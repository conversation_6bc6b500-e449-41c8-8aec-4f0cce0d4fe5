'use strict';
const sendToWormhole = require('stream-wormhole');
const fs = require("mz/fs");
const path = require('path');

module.exports = app => {
  class FileController extends app.Controller {
    // 上传视频，图片
    async upload() {
      const {ctx, service} = this;

      // 获取文件流
      const stream = await ctx.getFileStream();
      let url = null;
      try {
        url = await service.file.upload(stream);
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: url, message: "失败!"};
        throw err;
      }

      ctx.body = {code: 0, data: url, message: "成功!"};
    }

    // 课程封面图片上传
    async courseImgUpload() {
      const { ctx, service } = this;
      const { hash, size, courseSlug } = ctx.query;

      // 获取文件流
      const stream = await ctx.getFileStream();
      let url = null;
      try {
        url = await service.file.courseImgUpload(stream, hash, size, courseSlug);
      } catch (err) {
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }
      if(url) {
        ctx.body = {code: 500, data: url, message: "失败!"};
      } else {
        ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
      }
      
    }

    // 上传共享文件，需要自己建立映射，用于课程上传
    async uploadSharedFile() {
      const {ctx, service} = this;
      const { hash, size } = ctx.query;
      
      // 获取文件流
      const stream = await ctx.getFileStream();
      let mainTransaction = null;
      let path = null;

      try {
        const { mainModel } = app;
        // 启用事务
        mainTransaction = await mainModel.transaction({autocommit: false});

        path = await service.file.uploadSharedFile(stream, hash, size, mainTransaction);
        
        // 如果不是压力测试用户则提交事务
        await mainTransaction.commit();
      } catch (err) {
        console.error(err.message, 'uploadSharedFile');
        if(mainTransaction) {
          await mainTransaction.rollback();
        }
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }

      ctx.body = {code: 0, data: path, message: "成功!"};
    }
    
    // 上传资源文件,并且建立映射
    async uploadHashFile() {
      const {ctx, service} = this;
      const { hash, size, courseSlug, fileType, isCover = false } = ctx.query;
      
      // 获取文件流
      const stream = await ctx.getFileStream();

      try {
        await service.file.uploadHashFile(stream, hash, size, courseSlug, fileType, isCover);
      } catch (err) {
        console.error(err.message, 'uploadHashFile');
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }
      ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
    }

    // 上传微应用资源文件,并且建立映射
    async uploadMicroAssets() {
      const {ctx, service} = this;
      const { hash, size, fileName, extName, courseSlug, isCover = false } = ctx.query;
      
      // 获取文件流
      const stream = await ctx.getFileStream();

      try {
        await service.file.uploadMicroAssets({ readStream: stream, fileName, extName, hash, size, courseSlug, fileType: 'assets', isCover });
      } catch (err) {
        console.error(err.message, 'uploadMicroAssets');
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }
      ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
    }

    // word图片粘贴转存
    async uploadWordImage() {
      const {ctx, service} = this;
      const { courseSlug } = ctx.query;
      
      // 获取文件流
      const stream = await ctx.getFileStream();

      const fileType = 'assets';

      let response =  null;
      try {
        response = await service.file.uploadWordImage(stream, courseSlug, fileType);
      } catch (err) {
        console.error(err.message, 'uploadWordImage');
        // 必须将上传的文件流消费掉，要不然浏览器响应会卡死
        await sendToWormhole(stream);
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }
      ctx.body = {code: 0, data: response, message: "成功!"};
    }

    // 上传判决数据
    async uploadHashJudgeFile() {
      const {ctx, service} = this;
      const { content, fileName, hash, size } = ctx.request.body;

      // 打开主数据库
      const { mainModel } = app;

      // 启用事务
      let transaction = null;
      let filePath = null;

      try {
        transaction = await mainModel.transaction({autocommit: false});
        filePath = await service.file.uploadHashJudgeFile(content, fileName, hash, size, transaction);
        
        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      } catch (err) {
        if(transaction) {
          await transaction.rollback();
        }

        console.error('uploadHashJudgeFile', err.message)
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }

      ctx.body = {code: 0, data: { filePath }, message: "成功!"};
    }

    // 批量上传判决数据
    async bulkUploadHashJudgeFile() {
      const {ctx, service} = this;
      const { files } = ctx.request.body;
      
      // 打开主数据库
      const { mainModel } = app;

      // 接受返回数据
      const result = [];
      let transaction = null;

      try {
        // 启用事务
        transaction = await mainModel.transaction({autocommit: false});
        for(let uploadJudgeFile of files){
          const { content, fileName, hash, size } = uploadJudgeFile;
          const path = await service.file.uploadHashJudgeFile(content, fileName, hash, size, transaction);
          result.push(path);
        }

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      } catch (err) {
        if(transaction) {
          await transaction.rollback();
        }

        console.error('bulkUploadHashJudgeFile', err.message)
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }

      ctx.body = {code: 0, data: result, message: "成功!"};
    }


    // 关联共享文件到实际文件
    async bulkLinkHashFile() {
      const {ctx, service} = this;
      const { links } = ctx.request.body;
      let transaction = null;

      try {
        // 链接文件
        await service.file.makeLinks(links);
        const hashs = links.map(link => link.hash);

        // 启用事务
        const { mainModel } = app;
        transaction = await mainModel.transaction({autocommit: false});
        await service.file.addSharedFilesRef(hashs, transaction);

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      } catch (err) {
        console.error(err.message);
        if(transaction) {
          await transaction.rollback();
        }
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }

      ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
    }

    // 上传章节文件
    async uploadChapterFile() {
      const {ctx, service} = this;
      const { courseSlug, chapterName, sectionName, sectionType, isCover, forceUpload } = ctx.query;
      // 获取文件流
      const stream = await ctx.getFileStream();

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
      try {
        await service.file.uploadChapterFile(stream, courseSlug, chapterName, sectionName, sectionType, isCover, forceUpload, transaction);

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'uploadCount', transaction)
      } catch (err) {
        console.log(err, 'uploadChapterFile');
        await sendToWormhole(stream);
        await transaction.rollback();
        ctx.body = {code: 500, data: err, message: err.message };
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit();  
      ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
    }

    // 向章节中上传FPS格式OJ的文本内容到XML文件中
    async uploadChapterFPSXMLContent() {
      const {ctx, service} = this;
      const { content, fileName, courseSlug, chapterName, sectionName } = ctx.request.body;

      try {
        await service.file.uploadChapterFPSXMLContent( content, fileName, courseSlug, chapterName, sectionName );

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'uploadCount')
      } catch (err) {
        ctx.body = {code: 500, data: err.message, message: err.message};
        return;
      }

      ctx.body = {code: 0, data: '上传成功!', message: "成功!"};
    }

    // 获取已经存在的共享文件
    async getExistSharedFiles() {
      const {ctx, service} = this;
      const { hashs } = ctx.request.body;
      // 获取上传过的文件hash
      const result = await service.file.getExistSharedFiles(hashs.split(','));

      ctx.body = {code: 0, data: result, message: "成功!"};
    }

    // 文件预览
    async filePreview() {
      const { ctx } = this;
      const { request } = ctx;
      const { header } = request;
      const { host } = header;
      const { courseSlug, type, name } = ctx.request.query;

      let filePath = null; 
      if((type === 'assets') || (type === 'input')) {
        filePath =`${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${type}/${name}`;
      }else{
        filePath = `${app.config.file.dir}/${ctx.schoolSlug}/student/course/${courseSlug}/${type}/${name}`;
      }
      const fileExist = await fs.exists(filePath);
      if(!fileExist) {
        ctx.status = 404;
        ctx.body = `文件不存在`;
        return;
      }
      const targetHost = ["localhost", '127.0.0.1'].indexOf(host) !== -1 ? 'njyz.hxr.i51cy.com': host;

      const redirectPath = `//${targetHost}/file/course/${courseSlug}/${type}/${name}`;
      ctx.unsafeRedirect(redirectPath);
      return;
    }
    
    // 读取文件内容
    async getContent() {
      const { ctx } = this;
      const { courseSlug, type, name } = ctx.request.query;
      let filePath = null; 
      if((type === 'assets') || (type === 'input')) {
        filePath =`${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${type}/${name}`;
      }else{
        filePath = `${app.config.file.dir}/${ctx.schoolSlug}/student/course/${courseSlug}/${type}/${name}`;
      };
      const fileExist = await fs.exists(filePath);
      if(!fileExist) {
        ctx.status = 404;
        ctx.body = `文件不存在`;
        return;
      }
      // console.log(filePath, '-----')

      // ctx.set('content-type', 'text/html; charset=UTF-8');
      // const data = await fs.readFile(filePath, 'UTF-8');
      // const fileInfo = await fs.stat(filePath);
      // const size = fileInfo.size ? fileInfo.size / 8192 : 0;
      // ctx.body = { data: size > 100 ? data.slice(0, 102400) : data };
      const filesize = (await fs.stat(filePath)).size.toString();
      ctx.attachment(filePath);
      ctx.set('Content-Length', filesize);
      ctx.set('Content-Type', 'application/octet-stream');
      ctx.body = await fs.createReadStream(filePath);
    }

    // 查询课程封面存不存在
    async checkCourseImgExit() {
      const {ctx, service} = this;
      const { courseSlug } = ctx.query;
      let result = null;
      try{
        // 在本机调试的时候，因为无法挂载链接，所以造成文件无法读取，从而不会显示课程封面内容 
        result = await service.file.checkCourseImgExit(courseSlug);
      }catch(e){
        ctx.body = {code: 500,  message: e.message};
        return;
      }
      

      ctx.body = {code: 0, data: result, message: "成功!"};
    }

    async checkSchoolCourseImgExit() {
      const { ctx, service } = this;
      const { schoolSlug, courseSlug } = ctx.query;
      let result = null;
      try{
        // 在本机调试的时候，因为无法挂载链接，所以造成文件无法读取，从而不会显示课程封面内容 
        result = await service.file.checkSchoolCourseImgExit(schoolSlug, courseSlug);
      }catch(e){
        ctx.body = {code: 500,  message: e.message};
        return;
      }
      

      ctx.body = {code: 0, data: result, message: "成功!"};
    }

    // 查询课程封面存不存在
    async delCourseImg() {
      const {ctx, service} = this;
      const { courseSlug } = ctx.query;

      let response = null;

      try
      {
        response = await service.file.delCourseImg(courseSlug);
      }catch(e) {
        ctx.body = {code: 500, message: e.message };
        return;
      }

      ctx.body = {code: 0, data: response, message: "成功!"};
    }

    async downloadInOutData() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;

      const command = ctx.request.body;
      const { inPath, outPath, clientID, fileName, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const { id: userID } = session.user;

      try {
        let fileList = [inPath, outPath]
        fileList = fileList.filter(fileListItem => { 
          if(!fileListItem) {
            return false;
          }
          return fileListItem.match(/data\/shared_files\/(\d{4})\/(\d{1,2})\/(\d{1,2})\/(.+).(in|out)/);
        })
        if (!fileList.length) {
          ctx.body = [];
          return;
        } 
        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          schoolSlug,
          userID,
          clientID,
          requestID,
          paths: fileList,
          fileName,
          type: 'packedInOutFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });
      }
      catch(e) {
          const { message } = e;
          ctx.body = { code: 1, status: 500, message };
          return;
      }
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    async downloadChapterData() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, clientID, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      const userID = user.id;

      // 获取学校名称
      const { schoolSlug } = user;

      // 检测当前用户是否为该课老师
      try {
        const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
        if (!course) {
          throw new Error('您没有打包下载的权限');
        }
        const indicsData = await ctx.service.course.getIndicsData(courseSlug);
        const indics = indicsData.dataValues ? indicsData.dataValues.indics : indicsData.indics;

        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];
        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          ...command,
          userID,
          indics,
          clientID,
          requestID,
          schoolSlug, 
          courseSlug,
          fileDir: app.config.file.dir,
          type: 'packedFullChapterFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'downloadCount')
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1,  status: 500, message };
        return;
      }

      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    async downloadSectionData() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, clientID, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      
      // 获取学校名称
      const { schoolSlug } = user;
      const userID = user.id;
      // 检测当前用户是否为该课老师
      try {
        const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
        if (!course) {
          throw new Error('您没有打包下载的权限');
        }

        const queue = app.queue['course-task'];
        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        // 提交至队列，各分片轮询
        await queue.add({
          ...command,
          userID,
          clientID,
          requestID,
          schoolSlug, 
          courseSlug,
          fileDir: app.config.file.dir,
          type: 'packedChapterFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'downloadCount')
      }
      catch(e) {
        console.error(e)
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    async downloadCourseContent() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, clientID, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      
      // 获取学校名称
      const { schoolSlug } = user;
      const userID = user.id;

      // 检测当前用户是否为该课老师
      try {
        const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
        const indicsData = await ctx.service.course.getIndicsData(courseSlug);
        const indics = indicsData.dataValues ? indicsData.dataValues.indics : indicsData.indics;
        
        const courseData = await ctx.service.course.getCourseInfoBycourseSlug(courseSlug);
        
        const { createrID, publish, courseType, creater } = courseData;
        if (!course) {
          throw new Error('您没有打包下载的权限');
        }
        // 导出课程章节名称与文件夹名称映射表
        const chapterNameMap = {};
        indics.forEach(chapter => {
          const { chapterName, chapterTitle } = chapter;
          if (chapterTitle) {
            chapterNameMap[chapterName] = chapterTitle;
          }
        });

        const { allowCopy, allowPaste, courseDescription, courseName, saveCode, saveRunResult, ...otherCourseConfigMap } = course;
        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          clientID,
          requestID,
          userID,
          createrID, 
          publish,
          courseType,
          courseJson: {
            ...otherCourseConfigMap,
            allowPaste: allowPaste ? true : false,
            allowCopy: allowCopy ? true : false,
            courseDescription: courseDescription,
            courseName: courseName,
            courseSlug: courseSlug,
            saveCode: saveCode ? true : false,
            saveRunResult: saveRunResult ? true : false,
            chapterNameMap,
            schoolSlug, 
            courseSlug,
            creater
          },
          schoolSlug, 
          courseSlug,
          fileDir: app.config.file.dir,
          indics,
          type: 'packedCourseFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'downloadCount')
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }
    async downloadCourseContentForPLT() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, clientID, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      
      // 获取学校名称
      const { schoolSlug } = user;
      const userID = user.id;

      // 检测当前用户是否为该课老师
      try {
        const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
        const indicsData = await ctx.service.course.getIndicsData(courseSlug);
        const indics = indicsData.dataValues ? indicsData.dataValues.indics : indicsData.indics;
        
        const courseData = await ctx.service.course.getCourseInfoBycourseSlug(courseSlug);
        
        const { createrID, publish, courseType, creater} = courseData;
        if (!course) {
          throw new Error('您没有打包下载的权限');
        }
        // 导出课程章节名称与文件夹名称映射表
        const chapterNameMap = {};
        indics.forEach(chapter => {
          const { chapterName, chapterTitle } = chapter;
          if (chapterTitle) {
            chapterNameMap[chapterName] = chapterTitle;
          }
        });

        const { allowCopy, allowPaste, courseDescription, courseName, saveCode, saveRunResult, ...otherCourseConfigMap } = course;
        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          clientID,
          requestID,
          userID,
          createrID, 
          publish,
          courseType,
          courseJson: {
            ...otherCourseConfigMap,
            allowPaste: allowPaste ? true : false,
            allowCopy: allowCopy ? true : false,
            courseDescription: courseDescription,
            courseName: courseName,
            courseSlug: courseSlug,
            saveCode: saveCode ? true : false,
            saveRunResult: saveRunResult ? true : false,
            chapterNameMap,
            schoolSlug, 
            courseSlug,
            creater
          },
          schoolSlug, 
          courseSlug,
          fileDir: app.config.file.dir,
          indics,
          type: 'packedCourseFileForPLT'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'downloadCount')
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    async updateCourseContentForPLT() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, clientID, requestID } = command;
      console.log("command = ", command, clientID)

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      
      // 获取学校名称
      const { schoolSlug } = user;
      const userID = user.id;

      // 检测当前用户是否为该课老师
      try {
        const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
        const indicsData = await ctx.service.course.getIndicsData(courseSlug);
        const indics = indicsData.dataValues ? indicsData.dataValues.indics : indicsData.indics;
        
        const courseData = await ctx.service.course.getCourseInfoBycourseSlug(courseSlug);
        
        const { createrID, publish, courseType, creater} = courseData;
        if (!course) {
          throw new Error('您没有打包下载的权限');
        }
        // 导出课程章节名称与文件夹名称映射表
        const chapterNameMap = {};
        indics.forEach(chapter => {
          const { chapterName, chapterTitle } = chapter;
          if (chapterTitle) {
            chapterNameMap[chapterName] = chapterTitle;
          }
        });

        const { allowCopy, allowPaste, courseDescription, courseName, saveCode, saveRunResult, ...otherCourseConfigMap } = course;
        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          clientID,
          requestID,
          userID,
          createrID, 
          publish,
          courseType,
          courseJson: {
            ...otherCourseConfigMap,
            allowPaste: allowPaste ? true : false,
            allowCopy: allowCopy ? true : false,
            courseDescription: courseDescription,
            courseName: courseName,
            courseSlug: courseSlug,
            saveCode: saveCode ? true : false,
            saveRunResult: saveRunResult ? true : false,
            chapterNameMap,
            schoolSlug, 
            courseSlug,
            creater
          },
          schoolSlug, 
          courseSlug,
          fileDir: app.config.file.dir,
          indics,
          type: 'packedCourseFileForPLT'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });

        console.log('courseSlug = ', courseSlug);

        // 统计次数
        await ctx.service.course.changeCount(courseSlug, 'downloadCount')
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    async downloadFile() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { path, clientID, requestID } = command;

      if(path.indexOf('/data/shared_files/') !== 0) {
        ctx.body = { code: 1, message: '仅允许下载共享文件系统内容！您的非法操作和IP已被记录！ ^_^' };
        return;
      }

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const { id: userID } = session.user;

      try {
        if (!path) {
          ctx.body = { code: 1, message: '当前路径不存在'};
          return;
        }
        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        await queue.add({
          clientID,
          requestID,
          schoolSlug,
          userID,
          paths: [path],
          type: 'packedFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }

      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    async downloadCourseFile() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { clientID, requestID, filename, ext, courseSlug, chapterName, sectionName } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }
      const { id: userID } = session.user;

      try {
        const path = await ctx.service.course.getOJSourceFilePath({ schoolSlug, courseSlug, chapterName, sectionName, filename, ext });
        if (!path) {
          console.error('downloadCourseFile 当前路径不存在', courseSlug, chapterName, sectionName, filename, ext);
          ctx.body = { code: 1, message: '当前路径不存在'};
          return;
        }
        
        // 提交至队列，各分片轮询
        const queue = app.queue['course-task'];

        await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

        queue.add({
          clientID,
          requestID,
          schoolSlug,
          userID,
          paths: [path],
          type: 'packedFile'
        }, {
          priority: 1,
          removeOnComplete: true,
          removeOnFail: true,
        });
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }

      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    // 按照班级下载学生数据文件
    async downloadCourseDataByClass() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, teamID, clientID, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      const userID = user.id;
      // 获取学校名称
      const { schoolSlug } = user;

      // 检测当前用户是否为该课老师
      try {
          const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
          if (!course) {
            throw new Error('您没有打包下载的权限');
          }

          // 获取班级信息
          const { studentList, teamName } = await ctx.service.team.getTeamDetail(teamID);
          const studentMap = {};
          for (const student of studentList) {
            const { id, name, username } = student;
            if (studentMap[id]) {
              continue;
            }

            studentMap[id] = `${username}-${name}`;
          }

          // 提交至队列，各分片轮询
          const queue = app.queue['course-task'];
          await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });
          await queue.add({
            type: 'packStudentDataByClass',
            clientID,
            requestID,
            courseName: course.courseName,
            teamName,
            studentMap,
            schoolSlug, 
            userID,
            courseSlug,
            fileDir: app.config.file.dir
          }, {
            priority: 1,
            removeOnComplete: true,
            removeOnFail: true,
          });
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }
      
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    // 按照指定学号下载学生数据文件
    async downloadCourseDataByStudent() {
      const { ctx } = this;
      const { session } = ctx;

      // 收到请求指令
      const command = ctx.request.body;
      const { courseSlug, student, clientID, requestID } = command;

      if (!clientID) {
        ctx.body = { code: 1, message: '当前页面通信未经过验证，请重新刷新页面' };
        return;
      }

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      // 获取会话信息
      const { user } = session;
      const userID = user.id;
      // 获取学校名称
      const { schoolSlug } = user;

      // 检测当前用户是否为该课老师
      try {
          const course = await ctx.service.course.checkTeacherPermission(userID, courseSlug);
          if (!course) {
            throw new Error('您没有打包下载的权限');
          }

          const { id, name, username } = student;
          const dirName = `${username}-${name}`;

          // 提交至队列，各分片轮询
          const queue = app.queue['course-task'];
          await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });
          await queue.add({
            type: 'packStudentDataByStudent',
            clientID,
            requestID,
            userID,
            courseName: course.courseName,
            studentID: id,
            dirName,
            schoolSlug, 
            courseSlug,
            fileDir: app.config.file.dir
          }, {
            priority: 1,
            removeOnComplete: true,
            removeOnFail: true,
          });
      }
      catch(e) {
        const { message } = e;
        ctx.body = { code: 1, status: 500, message };
        return;
      }
      
      ctx.body = { code: 0, message: '成功，请开启监听'};
      return; 
    }

    // 下载文件
    async downloadRawFile() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;

      // 收到请求指令
      const { filePath, fileName } = ctx.query;

      if (!session.user) {
        ctx.body = { code: 1, message: '请先登录'};
        return;
      }

      const newFilePath = path.resolve(`${app.config.file.dir}/${schoolSlug}`, filePath);
      ctx.attachment(fileName);
      ctx.set('Content-Type', 'application/octet-stream');

      ctx.body = fs.createReadStream(newFilePath);
    }
  }

  return FileController;
}