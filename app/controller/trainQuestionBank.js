'use strict';

const { Writable } = require('stream');



module.exports = app => {
  class TrainQuestionBankController extends app.Controller {
    // 创建训练列表
    async createTrainQuestionBank() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题库名称
        name: {type: 'string', required: true},
        // 备注
        abstract: {type: 'string', required: false},
        nodes: {type: 'object', required: false} 
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      node.ifSelfBuilding = 1;

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 创建训练列表
        response = await this.service.trainQuestionBank.createTrainQuestionBank(node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改训练列表
    async putTrainQuestionBank() {
      const { ctx } = this;

      // 校验参数
      const rule = {
        // 题库名称
        name: {type: 'string', required: false},
        // 备注
        abstract: {type: 'string', required: false},
      };

      try {
        ctx.validate(rule, ctx.request.body); 
      } catch(e) {
        ctx.body = {code: 400, message: e.errors};
        return;
      }

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
        const value = requestBody[key];
        if(value === undefined) {
          continue;
        }

        node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response;
      try
      {
        // 修改训练列表
        response = await this.service.trainQuestionBank.putTrainQuestionBank(ctx.params.id, node, transaction);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        await transaction.rollback();
        return;
      }
      
      await transaction.commit();

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练列表
    async getTrainQuestionBank() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练列表
        response = await this.service.trainQuestionBank.getTrainQuestionBank(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 删除训练列表
    async destoryTrainQuestionBank() {
      const { ctx } = this;

      let response;
      try
      {
        // 修改训练列表
        response = await this.service.trainQuestionBank.destoryTrainQuestionBank(ctx.params.id);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message};
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取训练列表列表
    async getTrainQuestionBankList() {
      const { ctx } = this;

      let response;
      try
      {
        // 获取训练列表
        response = await this.service.trainQuestionBank.getTrainQuestionBankList();
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取题库列表
    async getTrainQuestionBankListAdmin() {
      const { ctx } = this;

      let response;
      try
      {
        // 获取训练列表
        response = await this.service.trainQuestionBank.getTrainQuestionBankListAdmin();
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 获取AI生成的题解
    async explainQuestionByAI() {
      const { ctx } = this;
      const { questionID } = ctx.params;

      let response;
      try
      {
        // 获取训练列表
        response = await this.service.trainQuestionBank.explainQuestionByAI(questionID);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 预热AI生成题解
    async warmupAIExplain() {
      const { ctx } = this;
      const { questionBankID } = ctx.params;

      let response;
      try
      {
        // 预热AI生成题解
        response = await this.service.trainQuestionBank.warmupAIExplain(questionBankID);
      }
      catch(e)
      {
        ctx.body = {code: e.code, message: e.message}
        return;
      }

      ctx.body = {code: 0, message: '成功', data: response};
    }

    
    // 批量导入fps xml格式的OJ题目
    async uploadQuestionsForXmlFiles() {
      const { ctx, service } = this;
      const { questionBankID } = ctx.query;
      const { user } = ctx.session
      if (!user) {
        ctx.body = {code: 1, message: '请先登录。'};
        return;
      }
      const { mainModel } = app;

      // 获取文件名（从请求头中读取）
      const chunks = [];
      const writableStream = new Writable({
        write(chunk, encoding, callback) {
          chunks.push(chunk); // 将数据块保存到数组中
          callback();
        },
      });

      let transaction = null;

      try {

        // 将请求体的数据流传输到可写流
        await new Promise((resolve, reject) => {
          ctx.req.pipe(writableStream)
            .on('finish', resolve) // 数据传输完成
            .on('error', reject); // 发生错误
        });

        // 将数据块合并为完整的 Buffer
        const fileBuffer = Buffer.concat(chunks);
        // 直接处理数据流
        const content = fileBuffer.toString('utf-8');
        // 启用事务
        transaction = await mainModel.transaction({autocommit: false});

        const questionDetails = await service.questions.parseFPSXMLForTrain(content, user, transaction);

        // 遍历questionDetails存入数据库
        await service.trainQuestionBank.uploadQuestionsForXmlFiles(questionDetails, user, questionBankID, transaction);

        // TODO: 根据数据库返回的id，将资源文件存入对应位置
        
        // TODO: 重复题目录入拦截



        console.log(questionDetails);


        // }

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      } catch (err) {
        if(transaction) {
          await transaction.rollback();
        }

        console.error('uploadQuestionsForXmlFiles', err.message)
        ctx.body = {code: 500, data: err.message, message: "失败!"};
        return;
      }

      ctx.body = {code: 0, message: "成功!"};
    }
     

  }

  return TrainQuestionBankController;
}