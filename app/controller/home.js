'use strict';
module.exports = app => {
  class HomeController extends app.Controller {
    async getHomePage() {
        const {ctx, service} = this;

        const data = {};
        try{
        // BANNER
        data.news = await service.systemConfig.getInfomation();
        // 新闻
        data.notices = await service.notice.getNoticeList('已发布');
        // 学校标记
        data.schoolSlug = ctx.schoolSlug;

        }catch(e){
          ctx.body = {code: 500, message: e.message};
          return
        }
        ctx.body = {code: 0, message: "成功!", data};
    }

    async getInfomation(){
      const { ctx } = this;
       // 调用Service
       let data = null;
      try{
         data = await ctx.service.systemConfig.getInfomation();
      }catch(e){
        ctx.body = {code: 500, message: e.message};
        return
      }
      // [{id: 1, key: 'banner', value: '[{xxxx: xxx},{xxss:sss}]'},{id: 2, key: 'canner', value: '[{xxxx: xxx},{xxss:sss}]'}]
      ctx.body = {code: 0, message: "成功!", data};
    }

    // 后台首页获取登录提示
    async getWebModal() {
      const { ctx } = this;
      const { user } = ctx.session

      if (!user) {
        ctx.body = {code: 1, message: '请先登录。'};
        return;
      }

      let infomationDatas = null;
       
      try{
        // 调用Service
        infomationDatas = await ctx.service.systemConfig.getWebModal();
      }
      catch(e) {
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }
      
      ctx.body = {code: 0, message: "成功!", data: infomationDatas};
    }

    async updateWebModal() {
      const { ctx } = this;
      const { user } = ctx.session
      if (!user) {
        ctx.body = {code: 1, message: '请先登录。'};
        return;
      }
  

      if (!user.isAdmin) {
        ctx.body = {code: 1, message: '请先登录教师账户！'};
        return;
      }

      // 校验参数
      const rule = {
        // 名称
        // id: {type: 'string', required: true},
        // key: {type: 'string', required: true},
        value: {type: 'integer', required: false},
      };
      ctx.validate(rule, ctx.request.body);

      // 获取更新数据
      const requestBody = ctx.request.body;
      const node = {};
      for(const key in rule) {
          const value = requestBody[key];
          if(value === undefined) {
              continue;
          }

          node[key] = value;
      }

      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});

      let response = null;
      
      try{
        response = await ctx.service.systemConfig.updateWebModal( parseInt(ctx.params.id, 10), node, transaction);
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 1, message:  e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: response};
    }

    // 修改轮播图
    async putCarouselFigure(){
      const { ctx } = this;
 
      const { value } = ctx.request.body;
       
      // 启用事务
      const transaction = await ctx.model.transaction({autocommit: false});
 
      let infomationDatas = null;
        
      try{
        // 调用Service
        infomationDatas = await ctx.service.systemConfig.putInfomation(value, 'CarouselFigure', transaction);
        // {id: 1, key: 'banner', value: '[{xxxx: xxx},{xxss:sss}]'}
      }
      catch(e) {
        await transaction.rollback();
        ctx.body = {code: 500, message: '失败' + e.message};
        return;
      }

      // 如果不是压力测试用户则提交事务
      (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
      ctx.body = {code: 0, message: '成功', data: infomationDatas};
     }
    
    // 修改是否前台登录
    async putNoticeOpen(){
      const { ctx } = this;
  
        const { value } = ctx.request.body;
        // ctx.validate(rule);
        
        // 启用事务
        const transaction = await ctx.model.transaction({autocommit: false});
  
        let infomationDatas = null;
        
        try{
          // 调用Service
          infomationDatas = await ctx.service.systemConfig.putInfomation(value, 'NoticeOpen', transaction);
          // {id: 1, key: 'banner', value: '[{xxxx: xxx},{xxss:sss}]'}
        }
        catch(e) {
          await transaction.rollback();
          ctx.body = {code: 500, message: '失败' + e.message};
          return;
        }

        // 如果不是压力测试用户则提交事务
        (ctx.session.user && ctx.session.user.id === -1) ? await transaction.rollback() : await transaction.commit(); 
        ctx.body = {code: 0, message: '成功', data: infomationDatas};       
      }

  }

  return HomeController;
}
