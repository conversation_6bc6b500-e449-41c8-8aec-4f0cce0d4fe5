'use strict';
const { makeUUID } = require('../utils/fps');

module.exports = app => {
  const mainRedis = app.redis.get('session');
  const { clientMap } = app.config.oauth2Provider;

  class OAuth2Controller extends app.Controller {
    // /oauth2/authorize 验证用户会话并返回code，用户已经登录后，请求参数client_id, 响应code
    async authorize() {
        const { ctx } = this;
        const { client_id } = ctx.request.body;

        // 验证client_id
        const client = clientMap[client_id];
        if (!client) {
            ctx.status = 400;
            ctx.body = { message: 'client_id不存在' }
            return;
        }

        // 验证用户会话
        const user = ctx.session.user;
        if (!user) {
            ctx.body = { message: '用户未登录' }
            return;
        }

        // 生成用户信息
        const userinfo = {
            id: user.id, // 用户ID
            name: user.name, // 用户姓名
            login: user.username, // 登录账号
            email: `${user.username}@${ctx.schoolSlug}.${app.config.hxr.deploymentSlug}` // 伪造邮箱，类似于***************************
        };

        // 生成code
        const code = makeUUID('code');

        // oauth2-code的HASH表中存储code，key为code，value为用户信息
        await mainRedis.set(`oauth2-code:${code}`, JSON.stringify({ client_id, userinfo }), 'EX', app.config.oauth2Provider.codeExpire);

        // 返回code
        ctx.body = { code };
    }

    // /oauth2/token 验证code并返回accessToken, 请求参数client_id, client_secret, code，响应accessToken
    async token() {
        const { ctx } = this;
        const { client_id, client_secret, code } = ctx.request.body;

        // 验证client_id
        const client = clientMap[client_id];
        if (!client) {
            ctx.status = 400;
            ctx.body = { message: 'client_id不存在' };
            return;
        }

        // 验证client_secret
        if (client.clientSecret !== client_secret) {
            ctx.status = 400;
            ctx.body = { message: 'client_secret错误' };
            return;
        }

        // 验证code
        const codeInfoStr = await mainRedis.get(`oauth2-code:${code}`);
        if (!codeInfoStr) {
            ctx.status = 400;
            ctx.body = { message: 'code不存在' };
            return;
        }

        const codeInfo = JSON.parse(codeInfoStr);

        // 核对client_id
        if (codeInfo.client_id !== client_id) {
            ctx.status = 400;
            ctx.body = { message: 'client_id错误' };
            return;
        }

        // 生成accessToken
        const accessToken = makeUUID('accessToken');

        // oauth2-token的HASH表中存储accessToken，key为accessToken，value为用户信息
        await mainRedis.set(`oauth2-token:${accessToken}`, JSON.stringify(codeInfo.userinfo), 'EX', app.config.oauth2Provider.accessTokenExpire);

        // 返回accessToken和过期时间
        ctx.body = { accessToken, accessTokenExpire: app.config.oauth2Provider.accessTokenExpire };
    }

    // 验证accessToken并返回用户信息，请求参数accessToken，响应用户信息(id, name, login, email)
    async user() {
        const { ctx } = this;

        // 验证Header中的token ${Token}
        const authorization = ctx.get('Authorization');
        if (!authorization) {
            ctx.status = 400;
            ctx.body = { message: 'Authorization不存在' };
            return;
        }

        const authorizationParts = authorization.split(' ');
        if (authorizationParts.length !== 2 || authorizationParts[0] !== 'token') {
            ctx.status = 400;
            ctx.body = { message: 'Authorization格式错误' };
            return;
        }

        const accessToken = authorizationParts[1];

        // 获取userinfo
        const userinfo = await mainRedis.get(`oauth2-token:${accessToken}`);
        if (!userinfo) {
            ctx.status = 400;
            ctx.body = { message: 'accessToken不存在' };
            return;
        }

        // 响应Accept-Language：zh-Hans
        ctx.set('Accept-Language', 'zh-Hans');

        // 响应用户信息(id, name, login, email)
        ctx.body = JSON.parse(userinfo);
    }
  }

    return OAuth2Controller;
}