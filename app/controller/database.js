'use strict';
const { writeHeapSnapshot } = require('node:v8');

module.exports = app => {
  class DataBaseController extends app.Controller {
    // 初始化
    async init() {
      const { ctx } = this;

      const { username=null, password=null, adminName="管理员", schoolName="测试学校", carouselFigure = null } = ctx.query;

      // 调用Service
      await ctx.service.database.init(username, password, adminName, schoolName, carouselFigure);

      ctx.body = {code: 0, message: "成功!"};
    }

    async alive() {
      const {ctx} = this;
      ctx.body = {code: 0, message: "成功!"};
    }

    // 验证学校名称
    async school() {
      const {ctx} = this;
      const { model } = ctx;
      const { schoolSlug } = model;

      // 调用Service
      const schoolTag = await ctx.service.database.getSchoolTag();

      ctx.body = { code: 0, model: schoolSlug, schoolSlug: ctx.schoolSlug, tagName: schoolTag.tagName };
    }

    // 解除IP锁定
    async releaseIPLock() {
      const { ctx } = this;
      const { ip = ctx.request.ip } = ctx.query;
      const redis = app.redis.get('session');
      const key = `ip_wrong_passwd_per_5min_${ip}`;
      await redis.del(key);
      ctx.body = { code: 0, message: '成功', key, ip: ctx.request.ip, header: ctx.request.header };
    }

    // 内存泄露调试
    async memoryDump() {
      // 写入当前目录下
      const path = `./${Date.now()}.heapsnapshot`;
      writeHeapSnapshot(path);

      this.ctx.body = { code: 0, message: '成功', path };
    }
  }

  return DataBaseController;
};