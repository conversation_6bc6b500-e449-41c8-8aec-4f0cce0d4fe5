'use strict';
const OpenAI = require('openai');

module.exports = app => {

  class LLMController extends app.Controller {
    // 调用大语言模型获取推理结果
    async inference() {
      const { ctx } = this;
      const { 
        messages = null,
        model = "deepseek-chat",
        temperature = 1.0,
        responseFormat = 'text',
        maxTokens = 4096,
        stream = false,
      } = ctx.request.body;

      // 验证请求参数
      ctx.validate({
        messages: { type: 'array', required: true, itemType: 'object', 
          properties: {
            role: { type: 'string', required: true },
            content: { type: 'string', required: true },
          },
        },
        model: { type: 'string', required: false, default: 'deepseek-chat' },
        temperature: { type: 'number', required: false, default: 1.0, max: 2.0, min: 0.0 },
        responseFormat: { type: 'string', required: false, default: 'text' },
        maxTokens: { type: 'number', required: false, default: 4096, max: 8192, min: 1 },
        stream: { type: 'boolean', required: false, default: false },
      });

      // 检查模型是否存在
      const llmConfig = app.config.llm[model];
      if (!llmConfig) {
        ctx.throw(400, `模型 ${model} 不存在`);
      }
      
      // 初始化 OpenAI 客户端
      const openai = new OpenAI({
        baseURL: llmConfig.baseURL,
        apiKey: llmConfig.apiKey,
      });
      
      try {
        // 调用 OpenAI API 进行推理
        const completion = await openai.chat.completions.create({
            messages: messages,
            model,
            temperature,
            response_format: {
                'type': responseFormat,
            },
            maxTokens,
            stream,
          });

          ctx.status = 200;
          ctx.body = completion.choices[0].message.content;
      } catch (error) {
        ctx.throw(500, `Error in LLM inference: ${error.message}`);
      }
    }
  }

  return LLMController;
}