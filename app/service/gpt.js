const axios = require('axios');



module.exports = app => {
    class GptService extends app.Service {
        async getGptResponse(prompt) {
            // ChatGPT API endpoint
            const API_URL = 'https://api.openai-proxy.com/v1/chat/completions';

            // Your OpenAI API key
            const API_KEY = '***************************************************';

            let response;

            // 根据提问长度选择不同模型
            let model = "gpt-3.5-turbo";
            if (prompt.length >= 3000) {
                model = "gpt-3.5-turbo-16k";
            }

            // 检查提问长度，如果超出 12000 个字符，则截断前半部分，保留后半部分内容
            const maxLen = 12000;
            if (prompt.length > maxLen) {
                prompt = prompt.substring(prompt.length - maxLen);
            }

            console.log("开始请求=====================================", model)
            
            try {
                response = await axios.post(API_URL, {
                    messages: [{"role": "user", "content": prompt}],
                    model: model,
                    // max_tokens: 150,
                    temperature: 0.7,
                    n: 1,
                }, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    timeout: 100000
                });
            } catch (error) {
                console.error(error);
                throw new Error(`GPT API 请求失败 ${error.message}`);
            }

            const { data, status, error } = response;
            console.log("请求结束=====================================")

            if (status !== 200) {
                console.error(error);
                throw new Error(`GPT API 请求失败 ${data.error}`);
            }

            if (error) {
                console.error(error);
                throw new Error(`GPT API 请求失败 ${error}`);
            }

            const { choices } = data;
            if (!choices || choices.length === 0) {
                throw new Error(`GPT API 请求失败 ${data.error}`);
            }

            let result = choices[0].message.content;

            return result;
        }
    }

    return GptService;
}
