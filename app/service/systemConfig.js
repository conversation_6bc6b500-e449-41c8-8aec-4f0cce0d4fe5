const moment = require('moment');
const { Op } = require('sequelize');

module.exports = app => {
  // const { model } = this.ctx;
  class SystemConfigService extends app.Service {

    async getInfomation(){
      const { model } = this.ctx;
      let condition = {[Op.or]: {},[Op.and]: {}};

        let basicArr = ['CarouselFigure','InformationBulletin', 'NoticeOpen'];
        condition[Op.or].key = basicArr;
        const result = await model.SystemConfig.findAll({where: {...condition }});
        let response = {};
        for(let code of result) {
            response[code.key] = code
        }

        return response;
    }
    
    async putInfomation(value,key, transaction){
      const { model } = this.ctx;
      return await model.SystemConfig.update({
        value: value,
      },{
        where: {key: key},
        transaction
      });
    }

    // 获取前台弹出公告
    async getWebModal(){
      const { model } = this.ctx;
      return await model.SystemConfig.findOne({
        where: { key: 'Notice'}
      });
    }

    async updateWebModal(id, node, transaction){
      const { model } = this.ctx;
      await model.SystemConfig.update({
        value: node.value,
      },{
        where: {key: 'Notice', id},
        transaction
      });

      return await model.SystemConfig.findOne({
        where: { key: 'Notice', id},
        transaction
      })
    }

    // 前台登录时查询弹出公告
    async getNoticeData(){
      const { model } = this.ctx;
      const response = await model.SystemConfig.findOne({
        where: { key: 'Notice' }
      });
      let value = {}
      if(response && response.dataValues && response.dataValues.value){
         value = await model.Notice.findOne({
          where: {id: response.dataValues.value}
        })
      }
      return value;
    }

    async getInfomationByKey(key, transaction = false){
      const { model } = this.ctx;
      return await model.SystemConfig.findOne({
        where: { key },
        raw: true,
        transaction,
      });
    }
  }
  return SystemConfigService;
}