const moment = require('moment');
moment.locale("zh-CN");
const sequelize = require('sequelize');
const { Op, QueryTypes } = sequelize;
const fs = require("mz/fs");
const path = require('path');
const { makeUUID } = require('../utils/fps');
const { getDiscriminative } = require('../utils/question');
const { delDir } = require('../utils/file');


function isAllEqual(array) {
  if (array.length > 0) {
      return !array.some(function(value, index) {
          return value !== array[0];
      });
  } else {
      return true;
  }
}

//! 将srcDir文件下的文件、文件夹递归的复制到tarDir下
const copyFolder = function(srcDir, tarDir, cb) {
  fs.readdir(srcDir, function(err, files) {
    let count = 0;
    const checkEnd = function() {
      ++count == files && files.length && cb && cb();
    }

    if (err) {
      checkEnd();
      return;
    }

    files.forEach(function(file) {
      const srcPath = path.join(srcDir, file);
      const tarPath = path.join(tarDir, file);

      fs.stat(srcPath, function(err, stats) {
        if (stats.isDirectory()) {
          // console.log('mkdir', tarPath);
          fs.mkdir(tarPath, function(err) {
            if (err) {
              console.log(err);
              return;
            }

            copyFolder(srcPath, tarPath, checkEnd);
          });
        } else {
          fs.copyFile(srcPath, tarPath, checkEnd);
        }
      });
    });

    //为空时直接回调
    files && files.length && files.length === 0 && cb && cb();
  });
}

module.exports = app => {
  class DatabaseService extends app.Service {
    // 初始化数据库
    async init(username, password, adminName, schoolName, carouselFigure){
      // 测试模式
      const DEBUG = app.config.appDebug;
      const { model } = this.ctx;
      // console.log()
      const SystemConfig = model.SystemConfig;

      const {ctx} = this;
      // 首先读取系统system_config表，获知当前数据库版本号
      let dbVersionConfig = null;

      try{
        dbVersionConfig = await SystemConfig.findOne({ where: {key: 'dbVersion'}})
      }
      catch(e)
      {
        console.log(e);
      }

      const currentDBVersion = 113;
      let dbVersion = dbVersionConfig ? parseInt(dbVersionConfig.value): 0;

      // 读取是否已经删除了训练trainDeleted
      let trainDeletedConfig = null;
      try{
        trainDeletedConfig = await SystemConfig.findOne({ where: {key: 'trainDeleted'}})
      }
      catch(e)
      {
        console.log(e);
      }

      // 删除训练数据后，应当设置system_config表中的trainDeleted为true
      const trainDeleted = trainDeletedConfig ? trainDeletedConfig.value === 'true' : false;
      
      // 逐一判断是否需要进行数据内容初始化，如果需要执行
      if(dbVersion < 1)
      {
        await this.ctx.model.sync();

        // 删除team_user因为user和team表多对多建立的
        // 索引team_user_userID_teamID_unique
        // 因为其中不包含deleted_at，所以会出问题
        try {
          // 没有这个索引就跳过
          await model.query(`ALTER TABLE team_user DROP INDEX team_user_userID_teamID_unique`);
        }
        catch(e) {

        }
        

        // 初始化配置
        const transaction = await ctx.model.transaction({autocommit: false});

        try {
          // 首页配置
          await SystemConfig.bulkCreate([{
            key: 'CarouselFigure',
            value: carouselFigure ? [carouselFigure]: null, // 轮播图
          }, {
            key: 'Notice',
            value: null,  // 弹出公告
          }, {
            key: 'SchoolName',
            value: schoolName,  // 学校名称
          },{
            key: 'NoticeOpen',
            value: true // 公告显示
          },{
            key: 'trainPermission',
            value: {
              hasUsed: [],
              hasClassIDs: []
            } // 在线训练人数限制
          },{
            key: 'coursePermission',
            value: {
              hasUsed: [],
              hasClassIDs: []
            } // 课程人数限制
          }], {
            transaction
          });

          // 插入用户
          if((username !== '未登记') && (password !== '未登记')) {
            await this.ctx.model.User.create({
              username,
              password,
              name: adminName,
              adminAuthority: {"user": true, "homePage": true, "course": true, "team": true},
            }, {
              transaction
            });
          }

          // 如果配置了需要更新题库，则同步题库
          if (app.config.INITTRAIN === 'true') {
            // 创建模版
            await model.Template.create({
              name: '江苏省普通高中学业水平合格性考试（信息技术）',
              // content:  [
              //   { name: '单选题', type: ['单选题'], count: 25, score: 2 },
              //   { name: '综合题', type: ['综合题'], count: 1, score: 20 },
              //   { name: '操作题', type: ['WPS表格操作题', 'Access操作题', '编程填空题'], count: 3, score: 10 },
              // ],
              content:  [
                { name: '单选题', type: ['单选题'], count: 25, score: 2 },
                { name: '操作题', type: ['编程填空题', 'WPS表格操作题', 'Access操作题'], count: 3, score: 10, limit: [2, 1, 1] },
                { name: '综合题', type: ['综合题'], count: 1, score: 20 },
              ],
              templateDifficulty: {"简单": "12", "较难": "2", "适中": "15"},
              duration: 60,
              score: 100,
            }, { transaction })


            const questionBankList = await this.ctx.service.thirdPart.getSchoolTrainQuestionBankList(transaction);

            for(const row of questionBankList) {
              // console.log('row:', row);
              await this.ctx.service.thirdPart.getOrCreateQuestionBank({ name: row.name, fromURL: `${row.id}`, needDelete: true }, transaction);
            }
          }
        } 
        catch(e) {
          console.error(e, 'database init');
          await transaction.rollback();
          return;
        }
      
        // 提交事务
        await transaction.commit();

        if (app.config.INITTRAIN) {
          try {
            await model.query("ALTER TABLE train_question_tag DROP FOREIGN KEY train_question_tag_ibfk_1 ;")
            await model.query("ALTER TABLE train_question_tag DROP FOREIGN KEY train_question_tag_ibfk_2 ;")
            // await model.query("ALTER TABLE `train_question_tag` DROP INDEX `tagID`");
            await model.query("ALTER TABLE `train_question_tag` DROP INDEX `train_question_tag_questionID_tagID_unique`");
            await model.query("ALTER TABLE `train_question_tag` ADD UNIQUE `train_question_tag_questionID_tagID_unique` (`questionID`, `tagID`, `deleted_at`) USING BTREE;");
            // await model.query("ALTER TABLE `train_question_tag` ADD INDEX `tagID` (`tagID`) USING BTREE");
            await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_1` FOREIGN KEY (`questionID`) REFERENCES `train_questions`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE");
            await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_2` FOREIGN KEY (`tagID`) REFERENCES `train_tag`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE;");
          }
          catch(e) {
            try {
              await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_1` FOREIGN KEY (`questionID`) REFERENCES `train_questions`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE");
            } catch (e) {}
            try {
              await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_2` FOREIGN KEY (`tagID`) REFERENCES `train_tag`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE;");
            } catch (e) {}
  
            console.log(e);
            // await transaction.rollback();
            // throw new Error('database 67 error', e)
          }
  
          try {
            await model.query("ALTER TABLE `train_question_tag` RENAME INDEX `train_question_tag_ibfk_2` TO `tagID`;")
          } catch (e) {}
        }
        
        dbVersion = currentDBVersion;
      }

      // 为了南京十二中，将9版本，直接升级到18版本
      if(dbVersion === 9) {
        // 重置section_record数据
        const transaction = await ctx.model.transaction({autocommit: false});

        try{
          // 获取所有AI课程节点
          const sectionList = await model.Section.findAll({
            where: { sectionType: 'AI' },
            attributes: ['id', 'record', 'chapterName', 'sectionName', 'ext'],
            include: [{
              model: model.Course,
              as: 'course',
              attributes: ['id', 'courseSlug']
            }],
            transaction
          });
          const sectionIDs = [];
          const sectionsMap = {};
          const updateSectionList = [];
          // 循环获取文件
          if (sectionList && sectionList.length) {
            for (const section of sectionList) {
              const { id, course, chapterName, sectionName, ext } = section && section.dataValues ? section.dataValues : section;
              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${course.courseSlug}/${chapterName}/${sectionName}.${ext}`;

              let fileContent = null;
              try {
                // 获取文件
                fileContent = await ctx.service.course.getFile(path, true);
              } catch(e) {
                continue
              }
              if (fileContent && fileContent.cells) {
                fileContent.cells = fileContent.cells.value ?  fileContent.cells.value :  fileContent.cells;
              }
              if (!fileContent || !fileContent.cells || !fileContent.cells.length) {
                continue;
              }
              
              sectionIDs.push(id);
              let codeIndex = 0;
              const key = `${course.courseSlug}_${chapterName}_${sectionName}`;
              for (const cell of fileContent.cells) {
                if (cell.cell_type !== 'code') {
                  continue;
                }
  
                codeIndex += 1;
                if (!cell.id) {
                  cell.id = makeUUID(`${id}_${codeIndex}`);
                }
                if (!sectionsMap[id]) {
                  sectionsMap[id] = {};
                }
  
                sectionsMap[id][codeIndex] = cell.id;

                if (!sectionsMap[key]) {
                  sectionsMap[key] = {};
                }
  
                sectionsMap[key][codeIndex] = cell.id;
              }

              // await ctx.service.course.updateAIFile(course.courseSlug, chapterName, sectionName, fileContent);
            }

            for (const section of sectionList) {
              const { id, course, chapterName, sectionName, record } = section && section.dataValues ? section.dataValues : section;
              const UUIDkey = `${course.courseSlug}_${chapterName}_${sectionName}`;

              const sectionMap = sectionsMap[UUIDkey];
              // 如果不存在该记录则无需修改
              if (!sectionMap) {
                continue;
              }
              for (const key in record) {
                if (isNaN(parseInt(key, 10))) {
                  continue;
                }
                
                const UUID = sectionMap[key];
                if (!UUID) {
                  continue;
                }

                if (record[key].project && record[key].project.sectionName) {
                  record[key].project = {
                    ...record[key].project,
                    UUID
                  }
                }

                record[UUID] = record[key];
              }

              updateSectionList.push({
                id,
                record
              });
            }

            await model.Section.bulkCreate(updateSectionList, {
              updateOnDuplicate: ['record', 'updated_at'],
              transaction
            });
  
            // 获取所有AI课程节点
            const sectionRecordList = await model.SectionRecord.findAll({
              where: { sectionID: { [Op.in]: sectionIDs }},
              attributes: ['id', 'record', 'sectionID'],
              raw: true,
              transaction
            });
            
            if (sectionRecordList && sectionRecordList.length) {
              const updateSectionRecordList = [];
              for (const sectionRecord of sectionRecordList) {
                const { id, record, sectionID } = sectionRecord;

                // 只处理AI
                const passCount = record.passCount && record.passCount.length ? record.passCount : [];
                let ifLoadSave = false;
                // 重新计算AI课程分数
                for (const recordKey in record) {
                  if (recordKey === 'codeCount' || recordKey === 'passCount' || recordKey === 'turtleModels') {
                    continue;
                  }

                  // 已经是UUID，不处理了
                  if(recordKey.length > 10) {
                    continue;
                  }

                  let UUID = null;
                  // 存在后期插入数据，需重新排版codeIndex
                  if (parseInt(recordKey, 10) === 1){
                    const recordDetail = record[recordKey];
                    const code = recordDetail ? recordDetail.code : null;
                    if (code.match(/:123456@192.168.59.38:3128/)) {
                      ifLoadSave = true;
                      UUID = makeUUID(`${sectionID}`);
                    }
                    else {
                      const codeIndex = ifLoadSave ? parseInt(recordKey, 10) - 1 : parseInt(recordKey, 10);

                      UUID = sectionsMap[sectionID][codeIndex];
                    }
                  } else {
                    const codeIndex = ifLoadSave ? parseInt(recordKey, 10) - 1 : parseInt(recordKey, 10);
                    if(!sectionsMap[sectionID] || Number.isNaN(codeIndex)) {
                      debugger;
                    }
                    UUID = sectionsMap[sectionID][codeIndex];
                  }

                  if (!UUID) {
                    continue;
                  }

                  record[UUID] = record[recordKey];

                  delete record[recordKey];

                  if (record[UUID].type !== 'success') {
                    continue;
                  }
                  if (passCount.indexOf(UUID) !== -1) {
                    continue;
                  }
                  const passIndex = passCount.findIndex(v => parseInt(v, 10) === parseInt(recordKey, 10));

                  passCount.splice(passIndex, 1, UUID);
    
                  // passCount.push(recordKey);
                }
    
                record.passCount = passCount;
    
                updateSectionRecordList.push({
                  id,
                  record
                });
              }

              await model.SectionRecord.bulkCreate(updateSectionRecordList, {
                updateOnDuplicate: ['record', 'updated_at'],
                transaction
              });
            }
          }
        } 
        catch(e) {
          console.log(e, '======')
          await transaction.rollback();
          return;
        }
      
        await transaction.commit();
      }

      if(dbVersion === 9) {
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.team_user DROP INDEX team_user_userID_teamID_unique, ADD INDEX team_user_userID_teamID_unique (teamID, userID, deleted_at) USING BTREE`);
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course ADD questionAnswer int NULL DEFAULT 0 AFTER ifInternel;`);
      }

      if(dbVersion === 9) {
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.section ADD kernelMessage JSON NULL DEFAULT NULL AFTER record;`);

        const transaction = await ctx.model.transaction({autocommit: false});

        try{
          // 获取所有AI课程节点
          const sectionList = await model.Section.findAll({
            where: { sectionType: 'AI' },
            attributes: ['id', 'record', 'chapterName', 'sectionName', 'ext'],
            include: [{
              model: model.Course,
              as: 'course',
              attributes: ['id', 'courseSlug']
            }],
            transaction
          });

          // 循环获取文件
          if (sectionList && sectionList.length) {
            for (const section of sectionList) {
              let { course, chapterName, sectionName, ext, record } = section && section.dataValues ? section.dataValues : section;
              if (!course) {
                continue;
              }

              // 有一批没有特殊记录的数据，认为是空对象就好了
              if(!record) {
                record = {}
              }

              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${course.courseSlug}/${chapterName}/${sectionName}.${ext}`;
              let fileContent = null;
              try {
                // 获取文件
                fileContent = await ctx.service.course.getFile(path, true);
              } catch(e) {
                console.log('e', e)
                continue
              }
              if (!fileContent || !fileContent.cells) {
                continue;
              }
              
              fileContent = {
                ...fileContent,
                "metadata": {
                  "kernelSetting": {
                    "kernelMemoryLimit": 210,
                    "kernelMemorySpace": 3000,
                    "kernelRecursiveLevel": 1000,
                  }
                },
              }

              await ctx.service.course.updateAIFile(course.courseSlug, chapterName, sectionName, fileContent);
            }
          }
        } 
        catch(e) {
          console.log(e, '======')
          await transaction.rollback();
          return;
        }
      
        await transaction.commit();
      }

      if (dbVersion === 9) {
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course ADD programLanguage VARCHAR(16) default 'Python' COMMENT '语言配置' AFTER questionAnswer;`);
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.section_record ADD stayTime int NULL DEFAULT 0  COMMENT '页面停留时间（s）' AFTER record;`);
      }

      // 更新course表的indics缓存
      if (dbVersion < 18) {
        const transaction = await ctx.model.transaction({autocommit: false});
        const { Section, Course } = ctx.model;
        try {
          // 获取所有课程
          const courses = await Course.findAll({
            where: {
              deleted_at: null,
            },
            attributes: ['id', 'indics', 'courseSlug'],
            raw: true,
            transaction
          });

          for (const course of courses) {
            const { id: courseID, indics, courseSlug } = course;
            if (!course) return;

            // 获取该课程下所有章节列表
            const sectionList = await Section.findAll({
              where: {courseID},
              attributes: ['id', 'chapterName', 'sectionName', 'sectionType', 'ext'],
              raw: true,
              transaction
            });

            // 创建section索引表
            const sectionHashMap = {};
            for (const section of sectionList) {
              sectionHashMap[`${section.chapterName}_${section.sectionName}`] = section.id;
            }
          
            // 在缓存中存入SectionID
            const newIndics = indics.map(chapter => {
              const { sections, chapterName } = chapter;
              sections.forEach(section => {
                // 如果没有存储发布状态，状态为true
                section.status = section.status !== false;
                // 查找sectionID
                const sectionID = sectionHashMap[`${chapterName}_${section.sectionName}`];
                // 在indics中增加section ID
                section.sectionID = sectionID;
              });
              return chapter;
            });

            // 更新章节
            await Course.update({
              indics: newIndics
            }, {
              where: {courseSlug},
              transaction
            });
          }
        }catch(e) {
          console.log(e)
          await transaction.rollback();
          return;
        }
        await transaction.commit();
      }

      // 升级IPYNB格式到4.5，合并多余的字段到Meta里面
      if (dbVersion < 19) {
        // 获取所有AI课程节点
        const sectionList = await model.Section.findAll({
          where: { sectionType: 'AI' },
          attributes: ['id', 'record', 'chapterName', 'sectionName', 'ext'],
          include: [{
            model: model.Course,
            as: 'course',
            attributes: ['id', 'courseSlug']
          }],
        });

        // 循环获取文件
        if (sectionList && sectionList.length) {
          for (const section of sectionList) {
            let { course, chapterName, sectionName, ext } = section.dataValues ? section.dataValues : section;

            const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${course.courseSlug}/${chapterName}/${sectionName}.${ext}`;
            let fileContent = null;
            try {
              // 获取文件
              fileContent = await ctx.service.course.getFile(path, true);
            } catch(e) {
              console.log('获取文件error', path);
              continue;
            }
            // 升级文件内容
            if (fileContent) {
              fileContent = await ctx.service.file.updateAIFileContent(fileContent);
            }
            
            await ctx.service.course.updateAIFile(course.courseSlug, chapterName, sectionName, fileContent);
          }
        }
      }

      // 检查section_record表重复记录，删除之
      if (dbVersion < 20) {
        // 提取有重复记录的userID和sectionID
        const transaction = await ctx.model.transaction({autocommit: false});
        const repeatUserIDAndSectionIDs = await model.query('SELECT userID, sectionID FROM `section_record` where `deleted_at` IS NULL group by userID, sectionID HAVING count(id) > 1', {
          transaction,
          raw: true,
          type: QueryTypes.SELECT,
        });

        for(const repeatUserIDAndSectionID of repeatUserIDAndSectionIDs) {
          const { userID, sectionID } = repeatUserIDAndSectionID;

          // 获取全部重复记录
          const repeatValidRecords = await model.SectionRecord.findAll({
            where: {
              userID,
              sectionID,
              record: {
                [Op.not] : null,
              }
            },
            order: [['id', 'asc']],
            transaction
          });

          // 有多条有值记录，需要归并处置
          if(repeatValidRecords.length > 1) {
            // 删除的有值记录
            let removeValidIDs = [];

            // 自删除集合删除全部有记录数据ID，以此处处理逻辑为准
            const oddRecordIDs = repeatValidRecords.map(repeatRecord => repeatRecord.dataValues.id);

            // 处理内容相同的情况
            const records = repeatValidRecords.map(repeatValidRecord => JSON.stringify(repeatValidRecord.dataValues.record));
            if(isAllEqual(records)) {
              // 保留第一条
              removeValidIDs = oddRecordIDs.concat();
              removeValidIDs.shift();
            }
            else {
              // 获取最新更新数据
              const recordTimeList = repeatValidRecords.map(record => Date.parse(record.dataValues.updated_at));
              const isSameTime = isAllEqual(recordTimeList); // 是否所有项更新时间相同

              if (!isSameTime) {
                // 获取最新更新时间
                const updateRecord = repeatValidRecords.reduce(function (pre, curv) {
                  return Date.parse(pre.dataValues.updated_at) < Date.parse(curv.dataValues.updated_at) ? curv : pre;  
                });
                // 删除非最新更新数据
                oddRecordIDs.forEach(id => {
                  if(id !== updateRecord.dataValues.id) {
                    removeValidIDs.push(id); // 记录删除
                  }
                });
              }
              else {
                // 更新时间一致，保留分数更大的数据
                // 查询课程类型
                const recordContent = await model.Section.findOne({
                  where: {
                    id: repeatValidRecords[0].dataValues.sectionID,
                  },
                  raw: true,
                  transaction
                })

                switch(recordContent.sectionType) {
                  case 'OJ':
                    {
                      const newerRecord = repeatValidRecords.reduce(function (pre, curv) {
                        return (pre.dataValues.record.score < curv.dataValues.record.score || pre.dataValues.record.count < curv.dataValues.record.count) ? curv : pre;
                      });
            
                      const newerID = newerRecord.dataValues.id;
                      oddRecordIDs.forEach(id => {
                        if(id !== newerID) {
                          removeValidIDs.push(id); // 记录删除
                        }
                      });
                    }
                    break;
                  case 'OI':
                  case 'AI':
                    console.error(`最后存储的Record记录应当一致，不一致，请人工介入筛查 ${ctx.schoolSlug} ${repeatValidRecords.map(repeatRecord => repeatRecord.dataValues.id).join(', ')}`);
                    break;
                  default: 
                    throw new Error(`未识别的类型${recordContent.sectionType}`)
                }
              }
            }

            await model.SectionRecord.destroy({
              where: {
                id: { [Op.in]: removeValidIDs }
              },
              transaction
            });
          }

          // 获取全部重复记录
          const repeatRecords = await model.SectionRecord.findAll({
            where: {
              userID,
              sectionID,
            },
            order: [['id', 'asc']],
            transaction
          });

          // 已经没有多余数据了，不用跑了
          if(repeatRecords.length <= 1) {
            continue;
          }

          let totalNullRecordTime = 0;
          let firstRecord = null;
          const removeIDs = [];

          // 循环每条重复记录
          for(const repeatRecord of repeatRecords) {
            const { id, record, stayTime } = repeatRecord.dataValues;

            // 其中记录为空的数据获取他们的停留时间，求和
            if(!record) {
              totalNullRecordTime += stayTime;

              // 空记录删除
              removeIDs.push(id);
              continue;
            }

            // 第一条有Record记录，保留
            if(!firstRecord) {
              firstRecord = repeatRecord;
              continue;
            }

            // 其他记录删除
            removeIDs.push(id);
          }

          // 如果有首条有Recrod记录
          if(firstRecord) {
            if(totalNullRecordTime) {
              firstRecord.stayTime += totalNullRecordTime;
              await firstRecord.save({ transaction });
            }
          }
          else {
            // 如果只有非Record记录，空一条出来，不要删除，时间累计
            removeIDs.shift();
            firstRecord = repeatRecords[0];
            firstRecord.stayTime = totalNullRecordTime;
            await firstRecord.save({ transaction });
          }

          await model.SectionRecord.destroy({
            where: {
              id: { [Op.in]: removeIDs }
            },
            transaction
          });
        }

        // const repeatUserIDAndSectionIDs2 = await model.query('SELECT userID, sectionID FROM `section_record` where `deleted_at` IS NULL group by userID, sectionID HAVING count(id) > 1', {
        //   transaction,
        //   raw: true,
        //   type: QueryTypes.SELECT,
        // });

        // await transaction.rollback();

        // console.log(repeatUserIDAndSectionIDs2);

        // return;
        
        await transaction.commit();
      }
      
      // 重新生成交互式课程的记录，统一微应用格式
      if (dbVersion < 21) {
        const transaction = await ctx.model.transaction({autocommit: false});
        try {
          // 获取所有的AI课程
          const allAISections = await ctx.model.Section.findAll({
            where: {
              sectionType: 'AI',
            },
            transaction,
            attributes: ['id', 'courseID', 'chapterName', 'sectionName'],
            include: [{
              model: model.Course,
              attributes: ['courseName', 'courseSlug'],
              as: 'course'
            }],
            raw: true,
          });
          const sectionIDArray = [];
          const AIContents = {};
          for (let i = 0; i < allAISections.length; i += 1) {
            const AISection = allAISections[i];
            sectionIDArray.push(AISection.id);
            const { chapterName, sectionName } = AISection;
            const path = `${app.config.file.dir}/${ctx.schoolSlug}/course/${AISection['course.courseSlug']}/${chapterName}/${sectionName}.ipynb`;
            try {
              const response = await ctx.service.course.getFile(path, true);
              AIContents[AISection.id] = response;
            } catch(err) {
              console.error(`${sectionName}.ipynb异常，path是${path}`)
            }
          }
          // 获取所有的答题记录AI课程的答题记录
          const allSectionRecords = await model.SectionRecord.findAll({
            where: {
              sectionID: {
                [Op.in]: sectionIDArray
              },
              record: {
                [Op.not]: null,
              }
            },
            raw: true,
            transaction
          });
          // 开始刷数据库数据
          const updateSectionRecords = [];
          for (let j = 0; j < allSectionRecords.length; j += 1) {
            const sectionRecord = allSectionRecords[j];
            const { sectionID, record, userID } = sectionRecord;
            // 没有答题记录，什么都不操作
            if (!record) {
              continue;
            }
            const { passCount } = record;
            // 获取当前节的内容
            const content = AIContents[sectionID];
            if (!content) {
              continue;
            }
            
            const { cells } = content;
            if (!cells || !cells.length) {
              continue;
            }
            // 获取所有微应用的uuid
            const microAppUUIDs = [];
            for (const cell of cells) {
              if(cell['cell_type'] === 'code' && cell['metadata']['type'] && cell['metadata']['type'] !== 'resources' && cell['metadata']['type'] !== 'filepreviewer' ) {
                const metadata = cell['metadata'];
                microAppUUIDs.push({
                  UUID: metadata.UUID,
                  sourceValue: metadata
                })
              }  
            }
            // 没有微应用就不用了再计算了
            if (!microAppUUIDs.length) {
              continue;
            }
            // 如果有微应用的话，那么就要进行值的修改
            for (let k = 0; k < microAppUUIDs.length; k += 1) {
              const { UUID, sourceValue } = microAppUUIDs[k];
              // 如果是已经被删除的微应用，passCount应该删除
              for (let key in record) {
                if (key === 'passCount' || key === 'codeCount' || key === 'turtleModels') {
                  continue;
                }
                const exist = microAppUUIDs.find(microAppUUID => microAppUUID.UUID === key);
                if (!exist) {
                  if (record.passCount && record.passCount.length) {
                    record.passCount = record.passCount.filter(passCountItem => passCountItem !== key);
                  }
                  delete record[key];
                }
              }
              if (!record[UUID]) {
                continue;
              }
              // 获取文件修改时间
              const { type: fileType, fileName } = sourceValue; 
              const { extName } = microTypeNameMap[fileType];
              const fileFullName = `${fileName}.${extName}`;
              
              const course = allAISections.find(AISection => AISection.id === sectionID);
              // 拼接主目录
              const pathDir = `${app.config.file.dir}/${ctx.schoolSlug}/student/course/${course['course.courseSlug']}/${userID}/${fileFullName}`;
              let fileStat = null;
              try {
                fileStat = await fs.stat(pathDir);
              } catch(err) {
           
              }
              if (fileStat && fileStat.isFile()) {
                // 文件修改时间
                const { mtime } = fileStat;
                // 微应用格式
                record[UUID] = {
                  microAppFileType: fileType,
                  filename: fileFullName,
                  time: moment(mtime).format('YYYY-MM-DD HH:mm:ss'),
                }
              } else {
                const codeResult = record[UUID].result && record[UUID].result.codeResult;
                if (codeResult && codeResult.length) {
                  const message = codeResult[0].message;
                  await fs.writeFile(pathDir, (typeof message === 'string') ? message : JSON.stringify(message));
                  record[UUID] = {
                    microAppFileType: fileType,
                    filename: fileFullName,
                    time: moment().format('YYYY-MM-DD HH:mm:ss'),
                  }
                }
              }
              if (!passCount) {
                passCount = [];
              };
              if (!passCount.includes(UUID)) {
                passCount.push(UUID);
              }
            }
            updateSectionRecords.push(sectionRecord);
          }
          await model.SectionRecord.bulkCreate(updateSectionRecords, { 
            updateOnDuplicate: ['record', 'updated_at'],
            transaction
          });
        } catch(err) {
          console.log('21升级交互式课程记录失败', err);
          await transaction.rollback();
          throw new Error(err.message)
        }
        await transaction.commit();
      }

      // 更新course表的indics缓存，增加课程名称字段
      if (dbVersion < 22) {
        const transaction = await ctx.model.transaction({autocommit: false});
        const { Course } = ctx.model;
        try {
          // 获取所有课程
          const courses = await Course.findAll({
            where: {
              deleted_at: null,
            },
            attributes: ['id', 'indics', 'courseSlug'],
            raw: true,
            transaction
          });

          for (const course of courses) {
            const { indics, courseSlug } = course;
            if (!course) return;
          
            // 在缓存中存入SectionTitle
            const newIndics = indics.map(chapter => {
              const { sections } = chapter;
              sections.forEach(section => {
                section.sectionTitle = section.sectionName;
              });
              return chapter;
            });

            // 更新章节
            await Course.update({
              indics: newIndics
            }, {
              where: {courseSlug},
              transaction
            });
          }
        }catch(e) {
          console.log(e)
          await transaction.rollback();
          return;
        }
        await transaction.commit();
      }

      if (dbVersion < 23) {
        const transaction = await ctx.model.transaction({autocommit: false});
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.section_record ADD passCount int NULL DEFAULT 0  COMMENT '答题通过个数' AFTER record;`);
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.section_record ADD totalScore int NULL DEFAULT 0  COMMENT '总的问题个数' AFTER record;`);
        try {
          // 获取所有的AI课程
          const allSections = await ctx.model.Section.findAll({
            transaction,
            attributes: ['id', 'courseID', 'chapterName', 'sectionName', 'sectionType'],
            include: [{
              model: model.Course,
              attributes: ['courseName', 'courseSlug'],
              as: 'course'
            }],
            raw: true,
          });
          const sectionIDArray = [];
          const AIContents = {};
          const OIContents = {};
          const OJContents = {};
          // 读取文件内容，获得要查询的sectionRecord的sectionID，以及课程的内容
          for (let i = 0; i < allSections.length; i += 1) {
            const sections = allSections[i];
            sectionIDArray.push(sections.id);
            const { chapterName, sectionName, sectionType } = sections;
            let path = '';
            if (sectionType === 'AI') {
              path = `${app.config.file.dir}/${ctx.schoolSlug}/course/${sections['course.courseSlug']}/${chapterName}/${sectionName}.ipynb`;
            } 
            try {
              if (path) {
                const response = await ctx.service.course.getFile(path, true);
                AIContents[sections.id] = response;
              }
            } catch(err) {
              console.error(`${sectionName}.ipynb异常，path是${path}`)
            }
            switch(sectionType) {
              case 'OI':
                OIContents[sections.id] = true;
                break;
              case 'OJ':
                OJContents[sections.id] = true;
                break;
              default: 
                break;
            }
          }
          // 获取所有的答题记录AI课程的答题记录
          const allSectionRecords = await model.SectionRecord.findAll({
            where: {
              sectionID: {
                [Op.in]: sectionIDArray
              }
            },
            raw: true,
            transaction
          });
          // 开始刷数据库数据
          const updateSectionRecords = [];
          for (let j = 0; j < allSectionRecords.length; j += 1) {
            const sectionRecord = allSectionRecords[j];
            let { sectionID, record } = sectionRecord;
            // 没有答题记录，什么都不操作
            if (!record) {
              continue;
            }
            if (AIContents[sectionID]) {
              const AIContent = AIContents[sectionID];
              delete record.codeCount;
              delete record.passCount;
              let { cells } = AIContent;
              if (!cells ) {
                continue;
              }

              if (!cells.value && !cells.length ) {
                continue;
              }

              if (cells.value && !cells.value.length ) {
                continue;
              }
              let UUIDList = []
              cells = cells.value ? cells.value : cells;
              cells.forEach((cell) => {
                // 必须是代码块
                if (cell['cell_type'] !== 'code') {
                  return;
                }

                // 图片视频资源不展示
                if(cell.metadata.type === 'resources' || cell.metadata.type === 'filepreviewer') {
                  return;
                }

                const code = cell.source && cell.source.length ? cell.source[0] : '';

                // 教师自动执行代码块不显示
                if (code.match(/^# >隐藏并自动执行/)) {
                  return;
                } 

                // 记录返回数据
                UUIDList.push(cell.metadata.UUID);
              });
              let passCount = 0;
              for (let key in record) {
                
                if (!UUIDList.includes(key)) {
                  delete record[key];
                  continue;
                }
                if (record[key].microAppFileType || record[key].status === '运行通过') {
                  passCount += 1;
                }
              }

              sectionRecord.passCount = passCount;
              sectionRecord.totalScore = UUIDList.length;
              sectionRecord.record = {
                UUIDsMap: record 
              }
              updateSectionRecords.push(sectionRecord);
            }
            if (OIContents[sectionID]) {
              delete record.codeCount;
              delete record.passCount;
              let passCount = 0;
              let totalScore = 0;
              for (let bigQuestion in record) {
                for (let smallQuestion in record[bigQuestion]) {
                  totalScore += 1;
                  if (record[bigQuestion][smallQuestion].status) {
                    passCount += 1;
                  }
                }
              }
              sectionRecord.passCount = passCount;
              sectionRecord.totalScore = totalScore;
              updateSectionRecords.push(sectionRecord);
            }
            if (OJContents[sectionID]) {
              if (!record.detail || !record.detail.length) {
                continue;
              }
              let passCount = 0;
              let totalScore = 0;
              record.detail.forEach(detailItem => {
                totalScore += 1;
                if (parseInt(detailItem.score, 10) === 100) {
                  passCount += 1;
                }
              });
              sectionRecord.passCount = passCount;
              sectionRecord.totalScore = totalScore;
              updateSectionRecords.push(sectionRecord);
            }
          }
          await model.SectionRecord.bulkCreate(updateSectionRecords, { 
            updateOnDuplicate: ['record', 'passCount', 'totalScore', 'updated_at'],
            transaction
          });
          // 开始把章节的缓存里面加上totalScore，用来和sectionRecord里面的passCount计算课程运行结果，用在课程进度展示
        } catch(err) {
          console.log('err', err);
          await transaction.rollback();
          throw new Error(err.message)
        }
        await transaction.commit();
      }

      // 更新course表的indics缓存，增加章名称字段
      if (dbVersion < 24) {
        const transaction = await ctx.model.transaction({autocommit: false});
        const { Course } = ctx.model;
        try {
          // 获取所有课程
          const courses = await Course.findAll({
            where: {
              deleted_at: null,
            },
            attributes: ['id', 'indics', 'courseSlug'],
            raw: true,
            transaction
          });

          for (const course of courses) {
            const { indics, courseSlug } = course;
            if (!course) return;
          
            // 在缓存中存入chapterTitle
            const newIndics = indics.map(chapter => {
              const { chapterTitle, chapterName } = chapter;
              if (!chapterTitle) {
                chapter.chapterTitle = chapterName;
              }
              return chapter;
            });

            // 更新章节
            await Course.update({
              indics: newIndics
            }, {
              where: {courseSlug},
              transaction
            });
          }
        }catch(e) {
          console.log(e)
          await transaction.rollback();
          return;
        }
        await transaction.commit();
      }

      // 更新section表AI课程的sectionType字段；升级AI课程文件版本到4.5
      if (dbVersion < 25) {
        const transaction = await ctx.model.transaction({autocommit: false});
        try {
          // 更新缓存indics
          // 获取所有课程
          const courses = await model.Course.findAll({
            where: {
              deleted_at: null,
            },
            attributes: ['id', 'indics', 'courseSlug'],
            raw: true,
            transaction
          });

          for (const course of courses) {
            const { id: courseID, indics, courseSlug } = course;

            // 获取该课程下所有章节列表
            const sectionList = await model.Section.findAll({
              where: {courseID},
              attributes: ['id', 'chapterName', 'sectionName', 'sectionType', 'ext'],
              raw: true,
              transaction
            });

            // 创建section索引表
            const sectionHashMap = {};
            for (const section of sectionList) {
              sectionHashMap[`${section.chapterName}_${section.sectionName}`] = section.id;
            }
          
            const newIndics = [];
            // 在缓存中存入SectionID
            for (const chapter of indics) {
              const { sections, chapterName } = chapter;
              
              const newSections = [];
              for (const section of sections) {
                const { sectionName, sectionType, ext } = section;
                // 如果没有存储发布状态，状态为true
                section.status = section.status !== false;
                // 查找sectionID
                const sectionID = sectionHashMap[`${chapterName}_${sectionName}`];
                if (!sectionID) {
                  // console.error(`${courseID}_${chapterName}_${sectionName} 课程未创建！`);
                  // 新建节
                  const newSectionType = (sectionType ===  'AI') ? null : sectionType;

                  const newSection = await model.Section.create({
                    courseID: courseID,
                    chapterName: chapterName,
                    sectionName: sectionName,
                    sectionType: newSectionType,
                    ext: ext,
                    record: null,
                  }, {
                    transaction
                  });

                  // 在indics中增加section ID
                  section.sectionID = newSection.dataValues.id;
                } else {
                  // 在indics中增加section ID
                  section.sectionID = sectionID;
                }
                newSections.push(section);
              }

              const newChapter = { ...chapter, sections: newSections };

              newIndics.push(newChapter);
            }

            // 更新章节
            await model.Course.update({
              indics: newIndics
            }, {
              where: {courseSlug},
              transaction
            });
          }

          // 更新section表AI课程的sectionType字段；升级AI课程文件版本到4.5
          // 获取没有记录sectionType的AI课程
          const sectionList = await model.Section.findAll({
            where: { sectionType: null },
            attributes: ['id', 'record', 'chapterName', 'sectionName', 'sectionType', 'ext'],
            include: [{
              model: model.Course,
              as: 'course',
              attributes: ['id', 'courseSlug']
            }],
            transaction
          });

          // 循环获取文件
          if (sectionList && sectionList.length) {
            for (const section of sectionList) {
              let { course, chapterName, sectionName, ext } = section.dataValues ? section.dataValues : section;

              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${course.courseSlug}/${chapterName}/${sectionName}.${ext}`;
              let fileContent = null;
              try {
                // 获取文件
                fileContent = await ctx.service.course.getFile(path, true);
              } catch(e) {
                console.error('获取文件error', path);
                continue;
              }
              // 升级文件内容
              if (fileContent && fileContent.nbformat_minor !== 5) {
                fileContent = await ctx.service.file.updateAIFileContent(fileContent);
              }
              
              await ctx.service.course.updateAIFile(course.courseSlug, chapterName, sectionName, fileContent);
            }
          }
                    
          // 更新sectionType 字段
          await model.Section.update({
            sectionType: 'AI'
          }, {
            where: { sectionType: null },
            transaction
          });

        }catch(e) {
          console.error(e, 'database updateAIFile 25');
          await transaction.rollback();
          return;
        }
        await transaction.commit();
      }
      
      // 更新section表AI课程的sectionType字段；升级AI课程文件版本到4.5
      if (dbVersion < 26) {
        await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course ADD historyTeams json NULL  COMMENT '历史班级' AFTER teams;`);

      }

      // section表新增 historyRecords 字段
      if (dbVersion < 27) { 
        const transaction = await ctx.model.transaction({autocommit: false});
        try {
          await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.section ADD COLUMN historyRecords JSON NULL DEFAULT NULL COMMENT '文件历史版本信息记录' AFTER record;`);
        }catch(e) {
          console.log('section history records error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      // 将section record存储的项目书信息存储到文件中
      if (dbVersion < 28) {
        const { ctx } = this;
        const { model } = ctx; 
        const { Course, Section } = model;
        const transaction = await model.transaction({autocommit: false});
        try {
          // 删除 Course 表 ifInternel 字段
          await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course DROP COLUMN ifInternel`);

          // 删除 Section 表 kernelMessage 字段
          await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.section DROP COLUMN kernelMessage`);

          // 所有AI课程
          const sections = await Section.findAll({
            where: {
              sectionType: 'AI',
            },
            include: [{
              model: Course,
              as: 'course',
              attributes: ['id', 'courseSlug']
            }],
            attributes: ['id', 'record', 'chapterName', 'sectionName', 'ext']
          });

          for (const section of sections) {
            const { record: records, course, chapterName, sectionName, ext } = section.dataValues ? section.dataValues : section;
            const { courseSlug } = course;

            const path = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
            let fileContent = null;
            try {
              // 获取文件
              fileContent = await ctx.service.course.getFile(path, true);
            } catch(e) {
              console.error('获取文件error', path, e);
              continue;
            }
            
            // 将项目书链接数据从section表移入文件
            const cells = fileContent.cells;
            if (cells && cells.length) {
              for (const cell of cells) {
                if (records && records[cell.metadata.UUID] && records[cell.metadata.UUID].project) {
                  cell.metadata.linkProject = records[cell.metadata.UUID].project;
                } 
              }
            }

            // 更新 kernelSetting
            const newKernelSetting = {
              internetAccess: true,
              kernelType: "server",
              kernelMemoryLimit: fileContent.metadata.kernelSetting && fileContent.metadata.kernelSetting.kernelMemoryLimit ? fileContent.metadata.kernelSetting.kernelMemoryLimit: 128,
              kernelMemorySpace: fileContent.metadata.kernelSetting && fileContent.metadata.kernelSetting.kernelMemorySpace ? fileContent.metadata.kernelSetting.kernelMemorySpace: 1024,
              kernelRecursiveLevel: fileContent.metadata.kernelSetting && fileContent.metadata.kernelSetting.kernelRecursiveLevel ? fileContent.metadata.kernelSetting.kernelRecursiveLevel: 1000,
            };

            fileContent = {
              ...fileContent,
              metadata: {
                ...fileContent.metadata,
                kernelSetting: newKernelSetting,
              },
            };

            // 更新文件
            await ctx.service.course.updateAIFile(courseSlug, chapterName, sectionName, fileContent);
          }
        }catch(e) {
          console.log('dbVersion 28', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      if (dbVersion < 29) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });
        try {
          const allWrongStatus = await model.SectionRecord.findAll({
            where: {
              record: {
                [Op.substring]: ['未运行']
              }
            },
            transaction,
            raw: true
          })
          const updateArray = [];
          for (let i = 0; i < allWrongStatus.length; i += 1) {
            const submitRecordItem = allWrongStatus[i];
            const { record } = submitRecordItem;
            if (!record || !record.UUIDsMap) {
              continue;
            }
            let shouldUpdateFlag = false
            for (let key in record.UUIDsMap) {
              const item = record.UUIDsMap[key];
              const {
                microAppFileType,
                status,
                type,
                result,
                code
              } = item;
              if (microAppFileType) {
                continue;
              }
              if (status === '未运行' && type === 'notRun' && result && result.length) {
                shouldUpdateFlag = true;
                const isError = result.find(e => e.type === 'error');
                
                if (isError) {
                  record.UUIDsMap[key] = { type: 'error', status: '运行报错', code, result };
                } else {
                  record.UUIDsMap[key] = { type: 'success', status: '运行通过', code, result };
                }
              }
            }
            if (shouldUpdateFlag) {
              updateArray.push(submitRecordItem)
            }
          }
          await model.SectionRecord.bulkCreate(updateArray, { 
            updateOnDuplicate: ['record'],
            transaction
          });
        } catch(err) {
          console.log('err', err)
          transaction.rollback();
          return;
        }
        await transaction.commit()
      }

      // 更新课程镜像配置
      if (dbVersion < 30) {
        const { ctx } = this;
        const { model } = ctx; 
        const { Course } = model;

        const transaction = await model.transaction({ autocommit: false });
        try {
          const courses = await model.Course.findAll({
            raw: true,
            transaction
          });

          for (const course of courses) {
            const { containerInfo } = course;
            const newConfig = {
              "image": containerInfo['image'],
              "cpuLimit": 2,
              "memoryRequest": "4Gi"
            };
            course.containerInfo = newConfig;
          }

          await Course.bulkCreate(courses, { 
            updateOnDuplicate: ['containerInfo'],
            transaction
          });

        }catch(e) {
          console.log('database 30 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      /* 
      * 修改客观题（包括历史课程）数据结构
      * 1. 二级结构变为一级，大题标题变为文本题
      * 2. 添加每道题的唯一标识 UUID
      * 3. 修改课程记录结构
      */
      if (dbVersion < 31) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });
        try {
          const allOISections = await model.Section.findAll({
            where: {
              sectionType: 'OI'
            },
            include: [{
              model: model.Course,
              as: 'course',
              attributes: ['id', 'courseSlug']
            }],
            transaction
          });

          for (const OISection of allOISections) {
            const { course, chapterName, sectionName, ext, id: sectionID } = OISection;

            const { courseSlug } = course;
            await ctx.service.course.updateOIStructure({ courseSlug, chapterName, sectionID, sectionName, ext }, transaction);
          }
        } catch(e) {
          console.log('database 31 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

       // 将学生运行错误信息储存到临时文件夹的同时，也要将之前储存的文件都删掉
       if (dbVersion < 32) {
        const { ctx } = this;
        const { model } = ctx; 
        const { mainModel } = app;
        const { SectionRecord } = model;
        const { SharedFiles } = mainModel;
        const transaction = await mainModel.transaction({ autocommit: false });

        try {
          const sectionRecords = await SectionRecord.findAll({
            raw: true,
            where: {
              record: {
                [Op.substring]: `inoutData`
              }
            }
          });
          for (let i = 0; i < sectionRecords.length; i += 1) {
            const sectionRecord = sectionRecords[i];
            const { record } = sectionRecord;
            const { inoutData } = record;
            if (!inoutData || !inoutData.length) {
              continue
            }
            for (let key of inoutData) {
              if (!key.stdoutSrc) {
                continue
              }
              const lastIndex = key.stdoutSrc.lastIndexOf('/');
              const filenameArray = key.stdoutSrc.slice(lastIndex + 1).split('.');
              const [hash, ext] = filenameArray;
              
              await SharedFiles.update({ 
                ref_count: sequelize.literal('ref_count - 1')
                }, {
                where: {
                  hash,
                  ext
                },
                transaction
              });
              const sharedFile = await SharedFiles.findOne({
                where: {
                  hash,
                  ext
                },
                raw: true,
                transaction
              });
              if (!sharedFile) {
                continue
              }
              if (sharedFile.ref_count <= 0) {
                // 没有引用就删掉文件
                try {
                  await fs.unlink(key.stdoutSrc)
                } catch(err) {
                  console.log('err', err.message)
                }
              }
            }
          }

        }catch(e) {
          console.log('database 30 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      // user course 表新增 统计 字段
      if (dbVersion < 33) { 
        // const transaction = await ctx.model.transaction({autocommit: false});
        try {
          // await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course ADD COLUMN readCount INTEGER NULL DEFAULT NULL COMMENT '浏览次数' AFTER teachers;`);
          await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course ADD COLUMN uploadCount INTEGER NULL DEFAULT 0 COMMENT '上传次数' AFTER teachers;`);
          await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.course ADD COLUMN downloadCount INTEGER NULL DEFAULT 0 COMMENT '下载次数' AFTER uploadCount;`);

          await model.query(`ALTER TABLE \`${this.ctx.schoolSlug}\`.user ADD COLUMN lastActiveTime DATETIME(6) NULL DEFAULT NULL COMMENT '最后活跃时间' AFTER school;`);

          // await this.ctx.model.Statistics.sync();
        } catch(e) {
          console.log('dbVersion 33 error', e);
          // await transaction.rollback();
          throw new Error(e.message);
        }
        // await transaction.commit();
      }
      

       // users表增加userCode字段记录统一身份认证账号
      if (dbVersion < 34) {
        await model.query("ALTER TABLE `user` ADD `ssoEmail` VARCHAR(32) NULL DEFAULT NULL COMMENT '统一身份认证账号邮箱' AFTER `school`;");
        await model.query("ALTER TABLE `user` ADD `ssoQQ` VARCHAR(32) NULL DEFAULT NULL COMMENT '统一身份认证账号QQ号' AFTER `school`;");
        await model.query("ALTER TABLE `user` ADD `userCode` VARCHAR(32) NULL DEFAULT NULL COMMENT '统一身份认证账号' AFTER `school`;");

        await model.query("ALTER TABLE `team` ADD `gradeName` VARCHAR(128) NULL DEFAULT NULL COMMENT '统一身份认证年级' AFTER `year`;");
        await model.query("ALTER TABLE `team` ADD `className` VARCHAR(128) NULL DEFAULT NULL COMMENT '统一身份认证班级' AFTER `year`;");
        await model.query("ALTER TABLE `team` ADD `classCode` VARCHAR(128) NULL DEFAULT NULL COMMENT '统一身份认证班级id' AFTER `year`;");
      }

      if (dbVersion < 35) {
        // await model.query("ALTER TABLE `user` DROP COLUMN userCode");
        // await model.query("ALTER TABLE `user` DROP COLUMN ssoQQ");
        // await model.query("ALTER TABLE `user` DROP COLUMN ssoEmail");
        await model.query("ALTER TABLE `user` ADD `sso` JSON NULL DEFAULT NULL COMMENT '单点登录账号信息' AFTER `school`;");
        await model.query("ALTER TABLE `user` ADD `ssoName` VARCHAR(128) NULL DEFAULT NULL COMMENT '单点登录用户名' AFTER `school`;");
      }

      if (dbVersion < 36) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });

        try {
          const allCourses = await model.Course.findAll({
            raw: true,
            transaction
          });

          for (const course of allCourses) {
            const { indics = [] } = course;

            for (const chapter of indics) {
              const { sections = [] } = chapter;
              for (const section of sections) {
                if (section.sectionType === 'Access') {
                  section.status = false;
                }
              }
            }
          }

          await model.Course.bulkCreate(allCourses, { 
            updateOnDuplicate: ['indics'],
            transaction
          });

        } catch (e) {
          console.log('dbVersion 36 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }

        await transaction.commit();
      }

      if (dbVersion < 37) {
        const { ctx } = this;
        const { model } = ctx; 
        const { mainModel } = app;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.Train.sync({transaction});
          await model.TrainPlan.sync({transaction});
          await model.TrainPlanClass.sync({transaction});
          await model.Template.sync({transaction});
          await model.TrainQuestionBank.sync({transaction});
          await model.Questions.sync({transaction});
          await model.query("alter table `tag` rename to `train_tag`;");
          await model.QuestionTag.sync({transaction});

          await model.Template.create({
            name: '江苏省普通高中学业水平合格性考试（信息技术）',
            // content:  [
            //   { name: '单选题', type: ['单选题'], count: 25, score: 2 },
            //   { name: '综合题', type: ['综合题'], count: 1, score: 20 },
            //   { name: '操作题', type: ['WPS表格操作题', 'Access操作题', '编程填空题'], count: 3, score: 10 },
            // ],
            content:  [
              { name: '单选题', type: ['单选题'], count: 25, score: 2 },
              { name: '操作题', type: ['WPS表格操作题', 'Access操作题', '编程填空题'], count: 3, score: 10, limit: [2, 1, 1] },
              { name: '综合题', type: ['综合题'], count: 1, score: 20 },
            ],
            templateDifficulty: {"简单": "12", "较难": "2", "适中": "15"},
            duration: 60,
            score: 100,
          }, { transaction })

          try {
            await model.query("ALTER TABLE `train_question_tag` DROP INDEX `train_question_tag_questionID_tagID_unique`, ADD UNIQUE `train_question_tag_questionID_tagID_unique` (`questionID`, `tagID`, `deleted_at`) USING BTREE;")
          } catch(e) {}
        } catch(e) {
          console.log('database 37 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      if (dbVersion < 38) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.TrainUserRecord.sync({transaction});
        } catch(e) {
          console.log('database 38 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
      }

      if (dbVersion < 39) {
        const { ctx } = this;
        const { model } = ctx; 
        
        await model.query("ALTER TABLE `train_tag` ADD `parentID` INT NOT NULL COMMENT '父级节点ID' AFTER `tagType`;");
        await model.query("ALTER TABLE `train_tag` ADD `questionBankID` INT NOT NULL COMMENT '题库ID' AFTER `parentID`;");
        await model.query("ALTER TABLE `train_tag` ADD `originalID` INT NULL COMMENT '远程题库对应ID' AFTER `questionBankID`;");
      }

      if (dbVersion < 40) {
        const { ctx } = this;
        const { model } = ctx; 

        try {
          await model.query(" ALTER TABLE `user` DROP `userCode`, DROP `ssoQQ`, DROP `ssoEmail`;");
        } catch (e) {}
      }

      // if (dbVersion < 39) {
      //   const { ctx } = this;
      //   const { mainModel } = app;
      //   const transaction = await mainModel.transaction({ autocommit: false });

      //   try {
      //     await mainModel.TrainFiles.sync({transaction});
      //   } catch(e) {
      //     console.log('database 39 error', e);
      //     await transaction.rollback();
      //     throw new Error(e.message);
      //   }
      // }

      if (dbVersion < 41 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        
        await model.query("ALTER TABLE `train_plan` ADD `statisticsData` JSON NULL COMMENT '统计信息' AFTER `abstract`;");
      }

      if (dbVersion < 42 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        
        await model.query("ALTER TABLE `train_questions` ADD `originalID` INT NULL COMMENT '远程题库对应ID' AFTER `questionBankID`;");
        await model.query("ALTER TABLE `train_tag` ADD `originalID` INT NULL COMMENT '远程题库对应ID' AFTER `questionBankID`;");
        await model.query("ALTER TABLE `train_question_bank` ADD `toID` INT NULL COMMENT '发布远程题库对应ID' AFTER `fromURL`;");
      }

      if (dbVersion < 43 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        
        await model.query("ALTER TABLE `csxx`.`train_question_tag` DROP PRIMARY KEY, ADD INDEX `question_tag_id` (`questionID`, `tagID`) USING BTREE;");
      }

      
      if (dbVersion < 44 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        
        await model.query("ALTER TABLE `train_plan_class` ADD `enrolledCount` INT NULL COMMENT '报名人数' AFTER `trainID`, ADD `submitCount` INT NULL COMMENT '交卷人数' AFTER `trainID`, ADD `average` decimal(10, 2) NULL COMMENT '平均分' AFTER `trainID`, ADD `variance` decimal(10, 2) NULL COMMENT '方差' AFTER `trainID`;");
      }

      if (dbVersion < 45 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        
        await model.query("ALTER TABLE `train_user_record` DROP COLUMN `totalScore`;");
        await model.query("ALTER TABLE `train_user_record` MODIFY COLUMN `submitTime` datetime;");
        await model.query("ALTER TABLE `train_plan` ADD `openForAllClass` INT DEFAULT 0 COMMENT '向所有班级开放' AFTER `code`;");
      }

      if (dbVersion < 46 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 选择填空题 编程填空题 填空题 【填空1】 转为 !--填空1--!
          const blankQuestions = await model.Questions.findAll({
            where: {
              questionType: {
                [Op.in]: ['编程填空题', '综合题']
              }
            },
            raw: true,
            transaction
          });

          if (blankQuestions && blankQuestions.length) {
            for (const question of blankQuestions) {
              const { questionType, questionDetail, answer } = question;
              if (questionType === '编程填空题') {
                let { code, originCode, answerKeys = [] } = questionDetail;
                questionDetail.code = code.replace(/【/g, '!--').replace(/】/g, '--!');
                questionDetail.originCode = originCode ? originCode.replace(/【/g, '!--').replace(/】/g, '--!') : null;
                questionDetail.answerKeys = answerKeys.map(key => key.replace(/【/g, '!--').replace(/】/g, '--!'));
  
                if (!answer) {
                  console.error(`${questionType} ${question.id} 未配置答案`);
                  continue;
                }
                const { score, answer: answerContent } = answer;
  
                if (!answerContent) {
                  console.error(`${questionType} ${question.id} 未配置答案`);
                  continue;
                }
  
                const newScore = {};
                const newAnswer = {};
  
                answerKeys.forEach((key) => {
                  let newKey = key.replace(/【/g, '!--').replace(/】/g, '--!');
  
                  if (!answerContent[key]) {
                    console.error(`${questionType} ${question.id} ${key} 未配置答案`);
                  }
  
                  if (!score[key]) {
                    console.error(`${questionType} ${question.id} ${key} 未配置答案分数`);
                  }
                  
                  // 新答案
                  newAnswer[newKey] = answerContent[key];
                  // 新分数
                  newScore[newKey] = score && score[key] ? score[key] : 0;
                });
  
                answer.answer = newAnswer;
                answer.score = newScore;
              }
  
              if (questionType === '综合题') {
                const { questionDetail = [], answer } = question;
  
                for (const subQuestion of questionDetail) {
                  const { UUID, questionType, questionDetail } = subQuestion;
  
                  if (questionType === '填空题' || questionType === '选择填空题') {
                    const { content, answerKeys = [] } = questionDetail;
  
                    questionDetail.content = content.replace(/【/g, '!--').replace(/】/g, '--!');
                    questionDetail.answerKeys = answerKeys.map(key => key.replace(/【/g, '!--').replace(/】/g, '--!'));
  
                    const subAnswer = answer[UUID];
  
                    if (!subAnswer) {
                      console.error(`${questionType} ${question.id} ${UUID} 未配置答案`);
                      continue;
                    }
  
                    const { score, answer: answerContent } = subAnswer;
  
                    if (!answerContent) {
                      console.error(`${questionType} ${question.id} ${UUID} 未配置答案`);
                      continue;
                    }
  
                    const newAnswer = {};
                    const newScore = {};
                    answerKeys.forEach((key) => {
                      let newKey = key.replace(/【/g, '!--').replace(/】/g, '--!');
      
                      if (!answerContent[key]) {
                        console.error(`${questionType} ${question.id} ${UUID} ${key} 未配置答案`);
                      }
      
                      if (questionType === '填空题' && !score[key]) {
                        console.error(`${questionType} ${question.id} ${UUID} ${key} 未配置答案分数`);
                      }
                      
                      // 新答案
                      newAnswer[newKey] = answerContent[key];
  
                      // 新分数
                      if (questionType === '填空题') {
                        newScore[newKey] = score && score[key] ? score[key] : 0;
                      }
  
                      // 选择填空题答案分数 按题目空格给分
                      if (questionType === '选择填空题') {
                        const answerCount = Object.keys(answerContent).length;
  
                        // 整数或0.5的倍数 1.25，均分
                        if (score % answerCount === 0 || `${score / answerCount}`.match(/^[1-9]\d*\.[5]$|0\.[5]$|^[1-9]\d*$/) || score / answerCount === 1.25) {
                          newScore[newKey] = score / answerCount;
                        }
                      }
                    });
      
                    answer[UUID].answer = newAnswer;
                    answer[UUID].score = newScore;
  
                    // 选择填空题答案分数 按题目空格给分
                    if (questionType === '选择填空题') {
                      // 非均分
                      if (Object.keys(newAnswer) && Object.keys(newAnswer).length && (!Object.keys(newScore) || !Object.keys(newScore).length)) {
                        const answerCount = Object.keys(newAnswer).length;
                        const answerCount2ScoreMap = {
                          2.5: {
                            "!--填空1--!": 1,
                            "!--填空2--!": 1.5,
                          },
                          4: {
                              "!--填空1--!": 1,
                              "!--填空2--!": 1,
                              "!--填空3--!": 2,
                          },
                          5: {
                            "!--填空1--!": 2,
                            "!--填空2--!": 2,
                            "!--填空3--!": 1,
                          },
                        };
  
                        if (answerCount2ScoreMap[score]) {
                          answer[UUID].score = answerCount2ScoreMap[score];
                        } else {
                          console.error(`${questionType} ${question.id} ${UUID} 未配置每空答案分数`);
                          Object.keys(newAnswer).forEach(key => newScore[key] = (score / answerCount).toFixed(2));
                          answer[UUID].score = newScore;
                        }
                      }
                    }
                  }
                }
              }
            }
  
            // console.log(blankQuestions, 'blankQuestions')
            await model.Questions.bulkCreate(blankQuestions, {
              updateOnDuplicate: ['questionDetail', 'answer'],
              transaction
            });
          }
        } catch(e) {
          console.log('database 46 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }


      if (dbVersion < 47) {
        const { ctx } = this;
        const { model } = ctx; 
        // const { mainModel } = app;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.TrainUserQuestion.sync({transaction});
        } catch(e) {
          console.log('database 47 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      if (dbVersion < 48 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        
        await model.query("ALTER TABLE `train_template` ADD `templateDifficulty` JSON NULL COMMENT '难度配置' AFTER `createUserID`;");
        await model.query("ALTER TABLE `train` ADD `templateDifficulty` JSON NULL COMMENT '难度配置' AFTER `ifSetWrongProblemCollection`;");
      }

      if (dbVersion < 49 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        
        await model.query("ALTER TABLE `train_question_tag` ADD `id` INT(11) NOT NULL AUTO_INCREMENT FIRST, ADD PRIMARY KEY (`id`);");
      }

      if (dbVersion < 50 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 编程填空题【填空1】 转为 __[填空1]__
          const blankQuestions = await model.Questions.findAll({
            where: {
              questionType: {
                [Op.in]: ['编程填空题']
              }
            },
            raw: true,
            transaction
          });

          if (blankQuestions && blankQuestions.length) {
            for (const question of blankQuestions) {
              const { questionType, questionDetail, answer } = question;
              if (questionType === '编程填空题') {
                let { code, originCode, answerKeys = [] } = questionDetail;
                questionDetail.code = code.replace(/!--/g, '__[').replace(/--!/g, ']__');
                questionDetail.originCode = originCode ? originCode.replace(/!--/g, '__[').replace(/--!/g, ']__') : null;
                questionDetail.answerKeys = answerKeys.map(key => key.replace(/!--/g, '__[').replace(/--!/g, ']__'));
  
                if (!answer) {
                  console.error(`${questionType} ${question.id} 未配置答案`);
                  continue;
                }
                const { score, answer: answerContent } = answer;
  
                if (!answerContent) {
                  console.error(`${questionType} ${question.id} 未配置答案`);
                  continue;
                }
  
                const newScore = {};
                const newAnswer = {};
  
                answerKeys.forEach((key) => {
                  let newKey = key.replace(/!--/g, '__[').replace(/--!/g, ']__');
  
                  if (!answerContent[key]) {
                    console.error(`${questionType} ${question.id} ${key} 未配置答案`);
                  }
  
                  if (!score[key]) {
                    console.error(`${questionType} ${question.id} ${key} 未配置答案分数`);
                  }
                  
                  // 新答案
                  newAnswer[newKey] = answerContent[key];
                  // 新分数
                  newScore[newKey] = score && score[key] ? score[key] : 0;
                });
  
                answer.answer = newAnswer;
                answer.score = newScore;
              }
            }
  
            // console.log(blankQuestions, 'blankQuestions')
            await model.Questions.bulkCreate(blankQuestions, {
              updateOnDuplicate: ['questionDetail', 'answer'],
              transaction
            });
          }
        } catch(e) {
          console.log('database 50 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }

      if (dbVersion < 51 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        
        await model.query("ALTER TABLE `train` ADD `isFinish` Boolean NULL COMMENT '配置完成' AFTER `status`;");
      }

      if (dbVersion < 52 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        
        await model.query("ALTER TABLE `train_plan` ADD `mode` VARCHAR(16) default '训练模式' COMMENT '模式' AFTER `code`;");
      }

      if (dbVersion < 53 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 选择填空题 编程填空题 填空题 【填空1】 转为 !--填空1--!
          const blankQuestions = await model.Questions.findAll({
            where: {
              questionType: {
                [Op.in]: ['综合题']
              }
            },
            raw: true,
            transaction
          });

          if (blankQuestions && blankQuestions.length) {
            for (const question of blankQuestions) {
              const { questionType } = question;
  
              if (questionType === '综合题') {
                const { questionDetail = [], answer } = question;
  
                for (const subQuestion of questionDetail) {
                  const { UUID, questionType, questionDetail } = subQuestion;
  
                  if (questionType === '填空题' || questionType === '选择填空题') {
                    const { content, answerKeys = [] } = questionDetail;
  
                    questionDetail.content = content.replace(/!--/g, ' __[').replace(/--!/g, ']__ ');
                    questionDetail.answerKeys = answerKeys.map(key => key.replace(/!--/g, ' __[').replace(/--!/g, ']__ '));
  
                    const subAnswer = answer[UUID];
  
                    if (!subAnswer) {
                      console.error(`${questionType} ${question.id} ${UUID} 未配置答案`);
                      continue;
                    }
  
                    const { score, answer: answerContent } = subAnswer;
  
                    if (!answerContent) {
                      console.error(`${questionType} ${question.id} ${UUID} 未配置答案`);
                      continue;
                    }
  
                    const newAnswer = {};
                    const newScore = {};
                    answerKeys.forEach((key) => {
                      let newKey = key.replace(/!--/g, ' __[').replace(/--!/g, ']__ ');
      
                      if (!answerContent[key]) {
                        console.error(`${questionType} ${question.id} ${UUID} ${key} 未配置答案`);
                      }
      
                      if (!score[key]) {
                        console.error(`${questionType} ${question.id} ${UUID} ${key} 未配置答案分数`);
                      }
                      
                      // 新答案
                      newAnswer[newKey] = answerContent[key];
  
                      // 新分数
                      newScore[newKey] = score && score[key] ? score[key] : 0;
                    });
      
                    answer[UUID].answer = newAnswer;
                    answer[UUID].score = newScore;
                  }
                }
              }
            }
  
            // console.log(blankQuestions, 'blankQuestions')
            await model.Questions.bulkCreate(blankQuestions, {
              updateOnDuplicate: ['questionDetail', 'answer'],
              transaction
            });
          }
        } catch(e) {
          console.log('database 53 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }
/*
      // 刷新远程题库初始数据
      if (dbVersion < 54) {
        const { ctx } = this;
        const { model } = ctx;

        const transaction = await model.transaction({ autocommit: false });

        try {
          const questionBankList = await this.ctx.service.thirdPart.getSchoolTrainQuestionBankList(transaction);

          for(const row of questionBankList) {
            // console.log('row:', row);
            await this.ctx.service.thirdPart.getOrCreateQuestionBank({ name: row.name, fromURL: `${row.id}`, needDelete: true }, transaction);
          }
        } catch(e) {
          console.log('database 50 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }
*/
      if (dbVersion < 55 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        const { mainModel } = app;
        const transaction = await model.transaction({ autocommit: false });

        try {
          const result = await model.Template.findOne({
            where: { name: '江苏省普通高中学业水平合格性考试（信息技术）', createUserID: { [Op.eq]: null } },
            raw: true,
          })
          
          await model.Template.update({
            name: '江苏省普通高中学业水平合格性考试（信息技术）',
            content:  [
              { name: '单选题', type: ['单选题'], count: 25, score: 2 },
              { name: '操作题', type: ['WPS表格操作题', 'Access操作题', '编程填空题'], count: 3, score: 10, limit: [2, 1, 1] },
              { name: '综合题', type: ['综合题'], count: 1, score: 20 },
            ],
            templateDifficulty: {"简单": "12", "较难": "2", "适中": "15"},
            duration: 60,
            score: 100,
          }, { transaction, where: { id: result.id } })
        } catch(e) {
          console.log('database 55 error', e);
          await transaction.rollback();
          // throw new Error(e.message);
        }
        await transaction.commit();
      }

      if (dbVersion < 56 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        
        await model.query("ALTER TABLE `train_user_question` ADD `trainPlanName` VARCHAR(64) default NULL COMMENT '来源训练名称' AFTER `id`;");
      }

      if(dbVersion < 57 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        // await model.query("ALTER TABLE `train_question_tag` DROP INDEX `question_tag_id`, ADD UNIQUE `train_question_tag_questionID_tagID_unique` (`questionID`, `tagID`, `deleted_at`) USING BTREE;")
        try {
          await model.query("ALTER TABLE `train_question_tag` DROP INDEX `question_tag_id`");
        }
        catch(e) {
          try {
            await model.query("ALTER TABLE `train_question_tag` DROP INDEX `train_question_tag_questionID_tagID_unique`");
          }
          catch(e) {
            throw new Error('train_question_tag 和 train_question_tag_questionID_tagID_unique都不存在！')
          }
        }

        await model.query("ALTER TABLE `train_question_tag` ADD UNIQUE `train_question_tag_questionID_tagID_unique` (`questionID`, `tagID`, `deleted_at`) USING BTREE;")
      }

      if(dbVersion < 58 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        await model.query("UPDATE `train_template` SET `content` = '[{\"name\": \"单选题\", \"type\": [\"单选题\"], \"count\": 25, \"score\": 2}, {\"name\": \"综合题\", \"type\": [\"综合题\"], \"count\": 1, \"score\": 20}, {\"name\": \"操作题\", \"type\": [\"编程填空题\", \"WPS表格操作题\", \"Access操作题\"], \"count\": 3, \"limit\": [2, 1, 1], \"score\": 10}]' WHERE `train_template`.`id` = 1;")
      }
      
      // 59升级时候忘记提交新的题库了，所以被占用了
      // 60刷了一次题库
      // 61解决中文文件名称乱码问题
/*
      // 刷新远程题库初始数据
      if (dbVersion < 62) {
        const { ctx } = this;
        const { model } = ctx;

        const transaction = await model.transaction({ autocommit: false });

        try {
          const questionBankList = await this.ctx.service.thirdPart.getSchoolTrainQuestionBankList(transaction);

          for(const row of questionBankList) {
            // console.log('row:', row);
            await this.ctx.service.thirdPart.getOrCreateQuestionBank({ name: row.name, fromURL: `${row.id}`, needDelete: true }, transaction);
          }
        } catch(e) {
          console.log('database 60 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }
*/
      // 解决不同学年班级名称不能重复的问题
      if(dbVersion < 64) {
        const { ctx } = this;
        const { model } = ctx;
        try {
          await model.query("ALTER TABLE `team` DROP INDEX `team_name_deleted_at`");
        }
        catch(e) {
          try {
            await model.query("ALTER TABLE `team` DROP INDEX `team_name_year_deleted_at`");
          }
          catch(e) {
            throw new Error('team_name_deleted_at 和 team_name_year_deleted_at都不存在！')
          }
        }

        await model.query("ALTER TABLE `team` ADD UNIQUE `team_name_year_type_deleted_at`(`name`, `year`, `type`, `deleted_at`) USING BTREE;")
      }
/*
      // 刷新远程题库初始数据
      if (dbVersion < 65) {
        const { ctx } = this;
        const { model } = ctx;

        const transaction = await model.transaction({ autocommit: false });

        try {
          const questionBankList = await this.ctx.service.thirdPart.getSchoolTrainQuestionBankList(transaction);

          for(const row of questionBankList) {
            // console.log('row:', row);
            await this.ctx.service.thirdPart.getOrCreateQuestionBank({ name: row.name, fromURL: `${row.id}`, needDelete: true }, transaction);
          }
        } catch(e) {
          console.log('database 60 error', e);
          await transaction.rollback();
          throw new Error(e.message);
        }
        await transaction.commit();
      }
*/
      if (dbVersion < 66 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx; 
        const { mainModel } = app;
        const transaction = await model.transaction({ autocommit: false });

        try {
          const result = await model.Template.findOne({
            where: { name: '江苏省普通高中学业水平合格性考试（信息技术）', createUserID: { [Op.eq]: null } },
            raw: true,
          })
          
          await model.Template.update({
            name: '江苏省普通高中学业水平合格性考试（信息技术）',
            content:  [
              { name: '单选题', type: ['单选题'], count: 25, score: 2 },
              { name: '操作题', type: ['编程填空题', 'WPS表格操作题', 'Access操作题'], count: 3, score: 10, limit: [2, 1, 1] },
              { name: '综合题', type: ['综合题'], count: 1, score: 20 },
            ],
            templateDifficulty: {"简单": "12", "较难": "2", "适中": "15"},
            duration: 60,
            score: 100,
          }, { transaction, where: { id: result.id } })
        } catch(e) {
          console.log('database 66 error', e);
          await transaction.rollback();
          // throw new Error(e.message);
        }
        await transaction.commit();
      }

      // 解决不同学年班级名称不能重复的问题
      if(dbVersion < 67) {
        const { ctx } = this;
        const { model } = ctx;
        // const transaction = await model.transaction({ autocommit: false });
        try {
          try {
            await model.query("ALTER TABLE train_question_tag DROP FOREIGN KEY train_question_tag_ibfk_1 ;")
            await model.query("ALTER TABLE train_question_tag DROP FOREIGN KEY train_question_tag_ibfk_2 ;")
            try {
              await model.query("ALTER TABLE `train_question_tag` DROP INDEX `train_question_tag_questionID_tagID_unique`");
              // await model.query("ALTER TABLE `train_question_tag` ADD UNIQUE `train_question_tag_questionID_tagID_unique` (`questionID`, `tagID`) USING BTREE;");
            } catch (e) {
              // await model.query("ALTER TABLE `train_question_tag` DROP INDEX `tagID`");
              try {
                await model.query("ALTER TABLE `train_question_tag` DROP INDEX `question_tag_id`");
              } catch(e) {}
            }
            await model.query("ALTER TABLE `train_question_tag` ADD UNIQUE `train_question_tag_questionID_tagID_unique` (`questionID`, `tagID`, `deleted_at`) USING BTREE;");
            // await model.query("ALTER TABLE `train_question_tag` ADD INDEX `tagID` (`tagID`) USING BTREE");
            await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_1` FOREIGN KEY (`questionID`) REFERENCES `train_questions`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE");
            await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_2` FOREIGN KEY (`tagID`) REFERENCES `train_tag`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE;");
          }
          catch(e) {
            try {
              await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_1` FOREIGN KEY (`questionID`) REFERENCES `train_questions`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE");
            } catch (e) {}
            try {
              await model.query("ALTER TABLE `train_question_tag` ADD CONSTRAINT `train_question_tag_ibfk_2` FOREIGN KEY (`tagID`) REFERENCES `train_tag`(`id`) ON DELETE NO ACTION ON UPDATE CASCADE;");
            } catch (e) {}
  
            console.log(e);
            // await transaction.rollback();
            // throw new Error('database 67 error', e)
          }
        } catch(e) {}

        try {
          await model.query("ALTER TABLE `train_question_tag` RENAME INDEX `train_question_tag_ibfk_2` TO `tagID`;")
        } catch (e) {}
          // await transaction.commit();
      }

      if(dbVersion < 68 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        try{
          await model.query('ALTER TABLE `train_plan` ADD `name` VARCHAR(32) AFTER `abstract`;');
          await model.query('ALTER TABLE `train_plan` ADD `ifShowScore` int NULL DEFAULT 1 AFTER `abstract`;');
          await model.query('ALTER TABLE `train_plan` ADD `ifShowCorrectionResults` int NULL DEFAULT 1 AFTER `abstract`;');
          await model.query('ALTER TABLE `train_plan` ADD `ifShowWrongAnswer` int NULL DEFAULT 1  AFTER `abstract`;');
          await model.query('ALTER TABLE `train_plan` ADD `ifSetWrongProblemCollection` int NULL DEFAULT 1  AFTER `abstract`;');
          await model.query('ALTER TABLE `train_plan` ADD `createUserID` int NULL AFTER `abstract`;');

          await this.ctx.model.TrainThroughTrainPlan.sync();
        } catch (e){
          console.log('init 68 e',e);
        }
      }

      if(dbVersion < 69 && dbVersion >= 37) {
        const { ctx } = this;
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });
        try {
          await model.query('ALTER TABLE `train` ADD `teachers` JSON NULL AFTER `templateDifficulty`;');
          const trainPlanIDsData = await model.TrainPlanClass.findAll({
            where: { trainID: { [Op.not]: null } },
            raw: true,
            group: ['planID'],
            transaction,
          });
          const trainPlanIDs = trainPlanIDsData.map(row => row.planID);
          // console.log('trainPlanIDs:',trainPlanIDs);
          const trainUserRecordData = await model.TrainUserRecord.findAll({
            attributes: ['id', 'planID', 'userID'],
            where: { planID: { [Op.in]: trainPlanIDs } },
            raw: true,
            // group: ['planID'],
            transaction,
          });
          // console.log('trainUserRecordData:',trainUserRecordData)
          const planDatas = await model.TrainPlan.findAll({
            attributes: ['id', 'trainID'],
            where: { id: { [Op.in]: trainPlanIDs } },
            raw: true,
            // group: ['planID'],
            transaction,
          });

          const createTrainThroughtData = [];
          trainUserRecordData.forEach(row => {
            const planDataRow = planDatas.find(item => item.id === row.planID);
            if (!planDataRow) {
              return;
            }

            // 去除掉userID为-1记录
            if(row.userID === -1) {
              return;
            }

            createTrainThroughtData.push({
              userID: row.userID,
              planID: row.planID,
              trainID: planDataRow.trainID
            })
          })

          // 将原先没有名称的训练计划加上名称
          const localPlanData = await model.TrainPlan.findAll({
            where: { name: { [Op.eq]: null }, trainID: { [Op.not]: null } },
            raw: true,
            // group: ['planID'],
            transaction,
          });
          const trainWithNoName = localPlanData.map(row => row.trainID);
          const localTrainData = await model.Train.findAll({
            where: { id: { [Op.in]: trainWithNoName } },
            raw: true,
            // group: ['planID'],
            transaction,
          });
          const localTrainMap = {};
          localTrainData.forEach(row => {
            localTrainMap[row.id] = row
          })

          for (const row of localPlanData) {
            const { trainID } = row;
            if (!localTrainMap[trainID]) {
              continue;
            }
            // console.log(row.id, localTrainMap[trainID].name)
            await model.TrainPlan.update({ name: localTrainMap[trainID].name, createUserID: localTrainMap[trainID].createUserID }, {
              where: { id: row.id },
              // raw: true,
              // group: ['planID'],
              transaction,
            });
          }

          // console.log('createTrainThroughtData:',createTrainThroughtData)

          await model.TrainThroughTrainPlan.bulkCreate(createTrainThroughtData, { transaction })

          // 文件移动
          for(const row of planDatas) {
            const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/train/${row.trainID}/plan/${row.id}`;
            const toPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${row.id}/train/${row.trainID}`;
            // console.log(row)
            const assetsExist = await fs.exists(toPath);
            if (!assetsExist) {
              await this.ctx.service.file.mkdirs(toPath);
            };
            copyFolder(fromPath, toPath)
          }
          // const fromPath
        } catch (e){
          console.log('init 69 e',e);
          await transaction.rollback();
          throw new Error(e.message);
          // return;
        }
        await transaction.commit();
      }

      if(dbVersion < 70  && dbVersion >= 37){
        const { ctx } = this;
        const { model } = ctx;
        await model.query(`ALTER TABLE train_user_record CHANGE score score DECIMAL(10,2) NULL DEFAULT 0 COMMENT '实际得分'`);
      }

      if(dbVersion < 71  && dbVersion >= 69){
        const { ctx } = this;
        const { model } = ctx;
        await model.query('ALTER TABLE `train` ADD `teachers` JSON NULL AFTER `templateDifficulty`;');
      }

      if(dbVersion < 72  && dbVersion >= 37){
        const { ctx } = this;
        const { model } = ctx;
        await model.query('ALTER TABLE `train_user_record` ADD `trainID` INT NULL AFTER `planID`, ADD `initialScore` DECIMAL(10, 2) DEFAULT 0 COMMENT "初始得分" AFTER `score`;');
      }

      // 特殊升级，重新计算所有考试成绩
      if (dbVersion < 73 && dbVersion >= 72) {
        const { ctx } = this;
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });
        try {
          // 取出所有已结束训练
          const allNeedPlans = await model.TrainPlan.findAll({
            where: { status: '已结束' },
            raw: true,
            transaction,
          })
          // console.log('allNeedPlans:',allNeedPlans)

          // 统计
          for(const row of allNeedPlans) {
            await ctx.service.trainThroughTrainPlan.getTrainPlanStatistics(row.id, this.ctx.schoolSlug, transaction)
          }
          // throw new Error('---')
        } catch (e){
          // console.log('init 69 e',e);
          await transaction.rollback();
          throw new Error(e.message);
          // return;
        }
        await transaction.commit();
      }

      if (dbVersion < 74) {
        await this.ctx.model.TrainQuestionCorrect.sync();
      }

      // 错题集去重
      if (dbVersion < 75) {
        await ctx.model.query('DELETE t1 FROM `train_user_question` t1 INNER JOIN `train_user_question` t2 WHERE t1.id < t2.id AND t1.questionID = t2.questionID AND t1.userID = t2.userID;');
      }

      if (dbVersion >= 74 && dbVersion < 76) {

        // 增加各学校勘误记录
        const { model } = ctx;
        const { TrainQuestionCorrect } = model;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await ctx.model.query('ALTER TABLE `train_question_correct` ADD `remoteQuestionBankID` INT NULL AFTER `questionBankID`;');

          // 仅在测试学校升级
          if (ctx.schoolSlug === 'csxx') {
            // 连接每个学校数据库
            const correctQuestions = [];
            const { mainModel } = app;
            const schoolList = await mainModel.School.findAll({
              where: {
                auditStatus: '审核通过',
              },
              attributes: ['slug', 'fullName', 'dbConfig'],
              raw: true,
            });

            for (const school of schoolList) {
              if (!school) {
                continue;
              }

              const { dbConfig, slug, fullName } = school;

              if (!dbConfig) {
                throw new Error(`未查询到学校 ${slug} ${fullName} 数据库配置`)
              }

              // 连接学校数据库
              const sequelizeNode = new app.Sequelize(slug, dbConfig.username, dbConfig.password, { ...app.config.sequelize, ...dbConfig });

              // 查询表是否存在
              let tableExist = await sequelizeNode.query(
                `SHOW TABLES LIKE ?;`,
                { type: QueryTypes.SELECT, replacements: ['train_question_correct'] },
              )

              if (!tableExist || !tableExist.length) {
                console.error(`学校${slug} 题库勘误表不存在`)
                continue;
                // throw new Error(`学校${slug} 题库勘误表不存在`)
              }

              // 获取待审核列表
              let allCorrections = await sequelizeNode.query(
                `SELECT * from train_question_correct WHERE deleted_at is null;`,
                { type: QueryTypes.SELECT, replacements: [] },
              );

              if (allCorrections && allCorrections.length) {
                correctQuestions.push(...allCorrections)
              }
            }

            const updateData = correctQuestions.map((i) => {
              const { id, ...info } = i;
              return {
                ...info,
                remoteQuestionBankID: 1,
              }
            });

            await TrainQuestionCorrect.bulkCreate(updateData, {
              updateOnDuplicate: ['updated_at'],
              transaction
            });
          }

        } catch (e){
          console.log('76',e);
          await transaction.rollback();
          throw new Error(e.message);
        }

        await transaction.commit();
      }

      // 学生训练记录增加trainID
      if(dbVersion >= 37 && dbVersion < 77) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 获取没有记录trainID的记录
          const allRecords = await model.TrainUserRecord.findAll({
            where: {
              trainID: null,
            },
            raw: true,
            transaction
          });

          // 获取关联表记录
          const allTrainPlans = await model.TrainThroughTrainPlan.findAll({
            raw: true,
            transaction
          });

          const trainMap = {};
          for (const trainPlan of allTrainPlans) {
            const { userID, planID, trainID } = trainPlan;
            trainMap[`${userID}-${planID}`] = trainID;
          }

          for (const record of allRecords) {
            const { id, userID, planID } = record;
            if (!userID || !planID) {
              throw new Error(`训练记录id ${id} 记录字段缺失`);
            }

            if (!trainMap[`${userID}-${planID}`]) {
              console.error(`训练记录id ${id} 无法查找到trainID`);
              continue;
            }

            record.trainID = trainMap[`${userID}-${planID}`];
          }

          // 批量更新
          await model.TrainUserRecord.bulkCreate(allRecords, {
            updateOnDuplicate: ['trainID'],
            transaction
          });
        } catch(e) {
          console.log('77',e);
          await transaction.rollback();
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion >= 37 && dbVersion < 78) {
        const { model } = ctx;
        await model.query('ALTER TABLE `train_question_bank` ADD `lastSyncAt` VARCHAR(32) NULL COMMENT "上次接收时间" AFTER `toID`;');

        const { TrainQuestionBank } = model;
        await TrainQuestionBank.update({ lastSyncAt: "2022-11-09 19:52:00" }, {
          where: { ifSelfBuilding: 0 }
        })
      }

      // 题目统计
      if(dbVersion >= 37 && dbVersion < 79) {

        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `train_questions` ADD `answerCount` INT NULL AFTER `status`, ADD `passCount` INT NULL AFTER `answerCount`;', { transaction: transaction });

          await model.query('ALTER TABLE `train_questions` DROP COLUMN `discriminative`', { transaction: transaction });

          await model.query('ALTER TABLE `train_plan` ADD `discriminative` JSON NULL AFTER `status`;', { transaction: transaction });

          // ===========================计算试卷区分度========================================

          // 查找已结束训练计划
          const allTrainPlans = await model.TrainPlan.findAll({
            attributes: ['id', 'name', 'trainID', 'score', 'duration', 'startTime', 'endTime', 'environment', 'code', 'openForAllClass', 'status', 'discriminative', 'enrolledCount', 'submitCount', 'average', 'variance', 'statisticsData', 'abstract', 'mode', 'ifShowScore', 'ifShowCorrectionResults', 'ifShowWrongAnswer', 'ifSetWrongProblemCollection', 'createUserID', 'created_at', 'updated_at', 'deleted_at'],
            where: {
              endTime: { [Op.lt]: moment().format('YYYY-MM-DD HH:mm:ss') },
              mode: '考试模式'
            },
            raw: true,
            transaction
          });

          // 全部record不为空的记录
          if (allTrainPlans && allTrainPlans.length) {
            const allTrainPlanIDs = allTrainPlans.map(i => i.id);

            const allValidStudentRecords = await model.TrainUserRecord.findAll({
              where: {
                record: {
                  [Op.not]: null
                },
                trainID: {
                  [Op.not]: null
                },
                planID: {
                  [Op.in]: allTrainPlanIDs
                },
                status: '已提交',
              },
              raw: true,
              transaction
            });

            // 学生分数
            const trainIDSet = new Set();
            const questionIDSet = new Set();
            const questionResultMap = new Map();
            const trainRecordMap = {};
            for (const studentRecord of allValidStudentRecords) {
              const { score, trainID, planID, record } = studentRecord;

              // 训练id集合
              if (!trainIDSet.has(trainID)) {
                trainIDSet.add(trainID);
              }

              // 学生训练分数集合
              if (!trainRecordMap[planID]) {
                trainRecordMap[planID] = {};
              }
  
              if (!trainRecordMap[planID][trainID]) {
                trainRecordMap[planID][trainID] = [];
              }

              trainRecordMap[planID][trainID].push(score);

              // 统计题目答题次数
              const questionIDs = Object.keys(record);
              for (const questionID of questionIDs) {
                if (!questionIDSet.has(questionID)) {
                  questionIDSet.add(questionID);
                }

                if (!questionResultMap[questionID]) {
                  questionResultMap[questionID] = {
                    answerCount: 0,
                    passCount: 0,
                  };
                }

                questionResultMap[questionID].answerCount++;
                if (record[questionID] && record[questionID].result) {
                  questionResultMap[questionID].passCount++;
                }
              }
            }

            // 获取训练分数
            const allTrains = await model.Train.findAll({
              where: {
                id: {
                  [Op.in]: [...trainIDSet]
                },
              },
              raw: true,
              attributes: ['id', 'score'],
              transaction
            });

            const trainScoreMap = {};
            for (const train of allTrains) {
              trainScoreMap[train.id] = train.score;
            }
  
            // 计算区分度
            const resultPlanMap = {};
            for (const trainPlan in trainRecordMap) {
              const trainRecords = trainRecordMap[trainPlan];
              resultPlanMap[trainPlan] = getDiscriminative(trainRecords, trainScoreMap, trainPlan);
            }
  
            for (const trainPlan of allTrainPlans) {
              if (resultPlanMap[trainPlan.id]) {
                trainPlan.discriminative = resultPlanMap[trainPlan.id];
              }
            }
  
            await model.TrainPlan.bulkCreate(allTrainPlans, {
              updateOnDuplicate: ['discriminative', 'updated_at'],
              transaction
            });

            // ============================计算题目全部答题人数和通过人数=======================================
            const allQuestions = await model.Questions.findAll({
              where: {
                id: {
                  [Op.in]: [...questionIDSet]
                }
              },
              raw: true,
              transaction
            });

            for (const question of allQuestions) {
              const result = questionResultMap[question.id];
              question.answerCount = result.answerCount;
              question.passCount = result.passCount;

              if (question.answerCount) {
                question.difficulty = 1 - (question.passCount / question.answerCount);
              }
            }

            await model.Questions.bulkCreate(allQuestions, {
              updateOnDuplicate: ['answerCount', 'passCount', 'difficulty', 'updated_at'],
              transaction
            });
          }
        } catch(e) {
          console.log('79',e);
          await transaction.rollback();
          throw e;
        }

        await transaction.commit();
      }


      // 题目统计
      if(dbVersion >= 37 && dbVersion < 80) {

        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('DELETE FROM `train_question_tag` WHERE `deleted_at` IS NOT NULL', { transaction: transaction });
        } catch(e) {
          console.log('79',e);
          await transaction.rollback();
          throw e;
        }

        await transaction.commit();
      }

      // 跨题库标签允许相同
      if(dbVersion >= 37 && dbVersion < 81) {

        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `train_tag` DROP INDEX `tag_tag_name_tag_type_deleted_at`, ADD INDEX `tag_tag_name_tag_type_deleted_at` (`tagName`, `tagType`, `deleted_at`, `questionBankID`) USING BTREE;', { transaction: transaction });
        } catch(e) {
          console.log('81',e);
          try {
            await model.query('ALTER TABLE `train_tag` DROP INDEX `train_tag_tag_name_tag_type_deleted_at`, ADD INDEX `tag_tag_name_tag_type_deleted_at` (`tagName`, `tagType`, `deleted_at`, `questionBankID`) USING BTREE;', { transaction: transaction });
          } catch(e) {
            await transaction.rollback();
            console.log('81',e);
            throw e;
          }
        }

        await transaction.commit();
      }

      // 跨题库标签刷新成初始
      if(dbVersion >= 81 && dbVersion < 82) {

        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 取所有远程题库
          const allNeedUpdateQuestionBank = await model.TrainQuestionBank.findAll({
            attributes: ['id'],
            raw: true,
            transaction,
            where: { ifSelfBuilding: 0 },
          });

          if (allNeedUpdateQuestionBank && allNeedUpdateQuestionBank.length) {
            // 取所有远程题库的IDs
            const allNeedUpdateQuestionBankIDs = allNeedUpdateQuestionBank.map(row => row.id);

            // 取所有所有远程题库对应的标签
            const allNeedUpdateTags = await model.Tag.findAll({
              attributes: ['id'],
              raw: true,
              transaction,
              where: { questionBankID: { [Op.in]: allNeedUpdateQuestionBankIDs } },
            })
  
            // 取所有远程题库对应的标签的IDs
            const allNeedUpdateTagsIDs = allNeedUpdateTags.map(row => row.id);
  
            // 删除标签表及关联表
            await model.query('DELETE FROM `train_question_tag` WHERE `deleted_at` IS NULL AND tagID in (?)', { replacements: [allNeedUpdateTagsIDs], transaction: transaction });
            await model.query(`UPDATE train_tag SET deleted_at = '2022-11-01 00:00:00' WHERE questionBankID in (?)`, { replacements: [allNeedUpdateQuestionBankIDs], transaction: transaction });
          }
        } catch(e) {
          await transaction.rollback();
          console.log('82',e);
          throw e;
        }

        await transaction.commit();
      }

      // 训练计划增加班级记录
      if(dbVersion >= 37 && dbVersion < 83) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `train_plan` ADD `openClasses` JSON NULL AFTER `status`;', { transaction: transaction });
          // 查询现有训练开放用户所有班级
          const allPlans = await model.TrainPlan.findAll({
            raw: true,
            transaction
          });

          const allPlanIDs = allPlans.map(i => i.id);

          // 获取班级训练情况
          const trainRecords = await model.TrainThroughTrainPlan.findAll({
            where: {
              planID: {
                [Op.in]: allPlanIDs
              }
            },
            raw: true,
            transaction
          });

          const trainPlanUserMap = {};
          const userIDs = new Set();
          for (const trainRecord of trainRecords) {
            const { planID, userID } = trainRecord;
            if (!trainPlanUserMap[planID]) {
              trainPlanUserMap[planID] = new Set();
            }

            trainPlanUserMap[planID].add(userID);
            userIDs.add(userID);
          }

          const allTeamUserRecords = await model.TeamUser.findAll({
            where: {
              userID: {
                [Op.in]: [...userIDs]
              }
            },
            raw: true,
            transaction
          });

          const userTeamMap = {};
          for (const teamRecord of allTeamUserRecords) {
            const { userID, teamID } = teamRecord;
            if (!userTeamMap[userID]) {
              userTeamMap[userID] = new Set();
            }

            userTeamMap[userID].add(teamID);
          }

          for (const plan of allPlans) { 
            const { id: planID } = plan;
            let currentUsers = trainPlanUserMap[planID];
            if (!currentUsers) {
              console.error(planID, '没有分配学生及班级');
              continue;
            }
            
            currentUsers = [...currentUsers];
            
            const teamIDs = new Set();
            for (const userID of currentUsers) {
              const userTeamIDSet = userTeamMap[userID];
              if(userTeamIDSet) {
                for(const userTeamID of userTeamIDSet) {
                  teamIDs.add(userTeamID);
                }
              }
            }

            plan.openClasses = [...teamIDs];
          }

          await model.TrainPlan.bulkCreate(allPlans, {
            updateOnDuplicate: ['openClasses', 'updated_at'],
            transaction
          });

        } catch(e) {
          console.log('83',e);
          await transaction.rollback();
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion >= 81 && dbVersion < 84) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `train_through_train_plan` ADD `isolate` BOOLEAN DEFAULT false AFTER `planID`;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('84',e);
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion >= 37 && dbVersion < 85) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `train_plan` ADD `year` VARCHAR(32) DEFAULT "2022学年" AFTER `mode`;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('85',e);
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion >= 37 && dbVersion < 86) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `train_user_record` ADD `submitTimes` INT DEFAULT 0 AFTER `status`, ADD `correctMode` BOOLEAN DEFAULT false AFTER `status`, ADD `correctQuestionIDs` JSON DEFAULT null AFTER `correctMode`;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('86',e);
          throw e;
        }

        await transaction.commit();
      }

      // 刷新训练内引用的所有题目更新时间在训练更新时间之后的训练
      if(dbVersion < 87) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 取所有题目，并形成索引
          const allQuestitons = await model.Questions.findAll({
            attributes: ['id', 'updated_at'],
            transaction,
            raw: true,
          });
          const allQuestitonUpdateMap = {};
          for(const allQuestiton of allQuestitons) {
            allQuestitonUpdateMap[allQuestiton.id] = allQuestiton.updated_at;
          }

          // 取所有训练
          const allTrains = await model.Train.findAll({
            attributes: ['id', 'content', 'updated_at'],
            transaction,
            raw: true,
          })

          const needUpdateTrains = [];

          // 循环，判断训练是否需要更新
          for(const train of allTrains) {
            let needUpdate = false;
            if (!train.content) {
              continue;
            }
            for(const title of train.content) {
              if (!title || !title.questionIDs) {
                continue;
              }

              for(const question of title.questionIDs) {
                const questionUpdate = allQuestitonUpdateMap[question];

                if (questionUpdate > train.updated_at) {
                  needUpdate = true;
                  break;
                }
              }
            }

            if (needUpdate) {
              needUpdateTrains.push(train)
            }
          }
          // console.log('needUpdateTrains:',needUpdateTrains)

          // 更新训练
          if(needUpdateTrains.length) {
            for(const needUpdateTrain of needUpdateTrains) {
              await model.Train.update({ content: needUpdateTrain.content }, {
                where: { id: needUpdateTrain.id },
                transaction,
              })
            }

            // await model.Train.update({ updated_at: moment().format('YYYY-MM-DD HH:mm:ss') }, {
            //   where: { id: { [Op.in]: needUpdateTrains.map(row => row.id) } },
            //   transaction,
            // })
          }
        } catch(e) {
          await transaction.rollback();
          console.log('87',e);
          throw e;
        }
        await transaction.commit();
      }

      if(dbVersion < 88) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          const allUsers = await model.User.findAll({
            attributes: ['id', 'sen'],
            where: {
              sen: ''
            },
            raw: true,
            transaction
          });

          const newUsers = allUsers.map(i => {
            return { ...i, sen: null }
          });

          await model.User.bulkCreate(
            newUsers, {
              updateOnDuplicate: ['sen'],
              transaction
            }
          )
        } catch (e) {
          await transaction.rollback();
          console.log('88',e);
          throw e;
        }

        await transaction.commit();
      }

      if (dbVersion < 89) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });
        const { mainModel } = app;
        const mainTransaction = await mainModel.transaction({autocommit: false});

        try {
          await ctx.model.PermissionLimitHistory.sync();
          await ctx.service.permissionPeople.refreshLimitPeopleWithDataBase(mainModel, transaction, mainTransaction);
        } catch (e) {
          await transaction.rollback();
          await mainTransaction.rollback();
          console.log('89',e);
          throw e;
        }

        await transaction.commit();
        await mainTransaction.commit();
      }

      // 一次性清除在线训练记录
      if (dbVersion < 90) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });
        const { mainModel } = app;
        const mainTransaction = await mainModel.transaction({autocommit: false});

        try {
          // 取授权记录
          const configs = await model.SystemConfig.findOne({
            where: {
              key: 'enableFunction'
            },
            raw: true,
            transaction
          });
          let trainRight = null;
          for(const row of configs.value.trainPermissionData) {
            if(row.target !== '在线训练' || row.status !== '启用') {
              continue;
            }
    
            // 取有效权限，时间取最久一个
            if (!trainRight || moment(trainRight.endTime).isBefore(row.endTime)) {
              trainRight = row;
            }
          }

          // 查看更新占用情况
          let result = await model.SystemConfig.findOne({
            where: {
              key: 'trainPermission'
            },
            raw: true,
            transaction
          });

          // 未处理过的情况
          if(result === null) {
            result = await model.SystemConfig.create({
              key: 'trainPermission',
              value: {
                hasUsed: [],
                hasClassIDs: []
              }
            }, {
              raw: true,
              transaction
            });
            await model.SystemConfig.create({
              key: 'coursePermission',
              value: {
                hasUsed: [],
                hasClassIDs: []
              }
            }, {
              raw: true,
              transaction
            });
          }
          
          // 拥有训练权限使才执行
          if (trainRight) {
            // 记录历史记录
            await model.PermissionLimitHistory.create({
              type: 'trainPermission',
              people: trainRight.trainPeople,
              hasUsed: result.value.hasUsed,
              startTime: trainRight.startTime,
              endTime: trainRight.endTime,
              historyClass: result.value.hasClassIDs,
            }, {
              raw: true,
              transaction
            })
          }

          // 跟更新占用情况
          await model.SystemConfig.update({
            value: {
              hasUsed: [],
              hasClassIDs: []
            }
          }, {
            where: {
              key: 'trainPermission'
            },
            raw: true,
            transaction
          });

          await ctx.service.permissionPeople.updateLimitPeople([], [], 'trainPermission', transaction, mainModel, mainTransaction);
        } catch (e) {
          await transaction.rollback();
          await mainTransaction.rollback();
          console.log('90',e);
          throw e;
        }

        await transaction.commit();
        await mainTransaction.commit();
      }

      if(dbVersion < 91 && dbVersion >= 89) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `permission_limit_history` ADD `note` JSON DEFAULT null AFTER `historyClass`;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('91',e);
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion < 92) {
        const { model } = this.ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // 添加来源表
          await model.TrainQuestionSource.sync();

          // 将已有来源字段记录到来源表，题目内容改为id
          const allQuestions = await model.Questions.findAll({
            attributes: ['id', 'source'],
            where: {
              source: {
                [Op.ne]: null
              }
            },
            raw: true,
            transaction
          });

          const allSources = [];

          for(const question of allQuestions) {
            if (!allSources.includes(question.source)) {
              allSources.push(question.source);
            }
          }

          const allSourceRows = [];

          // 默认优先级为0
          for(const source of allSources) {
            allSourceRows.push({
              source: source,
              priority: 0,
            });
          } 

          await model.TrainQuestionSource.bulkCreate(allSourceRows, {
            updateOnDuplicate: ['source'],
            raw: true,
            transaction
          });

          const allCreatedSources = await model.TrainQuestionSource.findAll({
            attributes: ['id', 'source'],
            where: {
              source: {
                [Op.in]: allSources
              }
            },
            raw: true,
            transaction
          });

          for(const question of allQuestions) {
            const sourceRow = allCreatedSources.find(row => row.source === question.source);
            if (!sourceRow) {
              continue;
            }
            question.source = sourceRow.id;
          }
          
          await model.Questions.bulkCreate(allQuestions, {
            updateOnDuplicate: ['source'],
            raw: true,
            transaction
          });

          // train_questions表修改source类型为 int
          await model.query('UPDATE `train_questions` SET `source` = null WHERE `source` NOT IN (SELECT `id` FROM `train_question_source`);', { transaction: transaction });
          await model.query('ALTER TABLE `train_questions` CHANGE `source` `source` INT(11) DEFAULT null;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('8',e);
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion < 93) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('ALTER TABLE `user` ADD `openID` VARCHAR(32) DEFAULT null AFTER `lastActiveTime`;', { transaction: transaction });
          await model.query('ALTER TABLE `user` ADD `wechatInfo` JSON DEFAULT null AFTER `openID`;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('93',e);
          throw e;
        }

        await transaction.commit();
      }

      if(dbVersion < 94) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // train_plan表添加字段 ifRandomOrder ifForbidViewPaper
          await model.query('ALTER TABLE `train_plan` ADD `ifForbidViewPaper` INT(1) DEFAULT 0 AFTER `IfShowScore`;', { transaction: transaction });
          await model.query('ALTER TABLE `train_plan` ADD `ifRandomOrder` INT(1) DEFAULT 0 AFTER `IfShowScore`;', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('94',e);
          throw e;
        }

        await transaction.commit();
      }

      if (dbVersion < 95) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          await model.query('UPDATE `train_questions` SET `source` = null WHERE `source` NOT IN (SELECT `id` FROM `train_question_source`);', { transaction: transaction });

          // train_questions表修改source类型为 int
          await model.query('ALTER TABLE `train_questions` CHANGE `source` `source` INT(11) DEFAULT null;', { transaction: transaction });

        } catch(e) {
          await transaction.rollback();
          console.log('95',e);
          throw e;
        }

        await transaction.commit();
      }

      // 题目来源表增加题库id字段
      if (dbVersion < 96) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // train_question_source 表增加originalID 字段和questionBankID字段
          await model.query("ALTER TABLE `train_question_source` ADD `originalID` INT NULL COMMENT '远程题库对应ID' AFTER `priority`;", { transaction: transaction });

          await model.query('ALTER TABLE `train_question_source` ADD `questionBankID` INT(11) DEFAULT null AFTER `priority`;', { transaction: transaction });

          // 获取题目的题库id和来源id记录，如果来源id在题库有记录，且来源没有记录题库id，则将对应的题库id写入来源表
          // 如果来源id有记录题库id，则新建一条来源记录，来源id为新建的来源id，题库id为来源记录的题库id
          // 如果没有查找到来源记录，则新建一条来源记录，来源id为来源id，题库id为题目对应的题库id
          const questionBankSourceRows = await model.Questions.findAll({
            attributes: ['source', 'questionBankID'],
            where: {
              source: {
                [Op.ne]: null
              }
            },
            raw: true,
            transaction
          });

          // 建立来源id和题库id的映射关系
          // { sourceID: [questionBankID] }
          const questionBankSourceMap = {};
          for (const row of questionBankSourceRows) {
            if (!questionBankSourceMap[row.source]) {
              questionBankSourceMap[row.source] = [row.questionBankID];
            } else {
              if (!questionBankSourceMap[row.source].includes(row.questionBankID)) {
                questionBankSourceMap[row.source].push(row.questionBankID);
              }
            }
          }

          // 如果来源有多个题库id，则新建来源记录，来源id为新建的来源id，题库id为来源记录的题库id
          // 如果来源只有一个题库id，则将来源id对应的题库id写入来源表
          // 如果来源没有题库id，则新建一条来源记录，来源id为来源id，题库id为题目对应的题库id

          // 获取所有来源
          const allSources = await model.TrainQuestionSource.findAll({
            attributes: ['id', 'source', 'priority', 'questionBankID'],
            transaction: transaction, 
            raw: true 
          });

          const newAllSources = [];
          const deletedSourceIDs = [];
          for (const source of allSources) {
            const { id } = source;

            const questionBankIDs = questionBankSourceMap[id];
            if (questionBankIDs && questionBankIDs.length) {
              if (questionBankIDs.length > 1) {
                // 新建来源记录
                for (const questionBankID of questionBankIDs) {
                  const newSource = {
                    source: source.source,
                    priority: source.priority,
                    questionBankID: questionBankID
                  };
                  newAllSources.push(newSource);
                }

                // 删除原来的来源记录
                deletedSourceIDs.push(id);
              } else {
                // 将来源id对应的题库id写入来源表
                newAllSources.push({
                  ...source,
                  questionBankID: questionBankIDs[0]
                });
              }
            } else {
              // 新建一条来源记录，来源id为来源id，题库id为题目对应的题库id
              console.error(`来源${source.id}没有对应题库id`)
              newAllSources.push({
                ...source,
                questionBankID: source.questionBankID
              });
            }
          }

          // 新建来源记录
          await model.TrainQuestionSource.bulkCreate(newAllSources, {
            updateOnDuplicate: ['questionBankID'],
            transaction: transaction
          });

          // 建立来源名称和来源id的映射关系
          // { source: sourceID }
          const sourceMap = {};

          const deletedSources = await model.TrainQuestionSource.findAll({
            attributes: ['id', 'source'],
            where: {
              id: {
                [Op.in]: deletedSourceIDs
              }
            },
            raw: true,
            transaction: transaction
          });

          for (const source of deletedSources) {
            sourceMap[source.source] = source.id;
          }

          const updateSources = Object.keys(sourceMap);

          // 更新引用被删除来源的题目的来源
          const deleteSources = await model.TrainQuestionSource.findAll({
            attributes: ['id', 'source', 'questionBankID'],
            where: {
              source: {
                [Op.in]: updateSources
              }
            },
            raw: true,
            transaction: transaction
          })

          const allQuestions = await model.Questions.findAll({
            attributes: ['id', 'source', 'questionBankID'],
            where: {
              source: {
                [Op.in]: deletedSourceIDs
              }
            },
            raw: true,
            transaction,
          });

          for (const question of allQuestions) {
            const curSource = deleteSources.find(item => sourceMap[item.source] && sourceMap[item.source] === question.source && item.questionBankID === question.questionBankID);
            if (!curSource) {
              console.error(`题目${question.id}没有对应的来源记录`);
              continue;
            }

            question.source = curSource.id;
          }

          await model.Questions.bulkCreate(allQuestions, {
            updateOnDuplicate: ['source'],
            transaction: transaction
          });

          // 删除原有的来源记录     
          await model.TrainQuestionSource.destroy({
            where: {
              id: {
                [Op.in]: deletedSourceIDs
              }
            },
            transaction: transaction
          });
        
        } catch(e) {
          await transaction.rollback();
          console.log('96',e);
          throw e;
        }

        await transaction.commit();
      }

      // 题目表增加作者字段
      if (dbVersion < 97) {
        await model.query('ALTER TABLE `train_questions` ADD `author` VARCHAR(255) DEFAULT null AFTER `status`;');
      }

      if(!trainDeleted)
      {
        // python题目中_ _[填空1]_ _修改为①②③
        if (dbVersion < 98) {
          const { model, schoolSlug } = this.ctx;
          const { Questions } = model;

          const transaction = await model.transaction({ autocommit: false });

          function replaceWithCircleNumbers(text) {
            const numberMap = {
              '1': '①',
              '2': '②',
              '3': '③',
              '4': '④',
              '5': '⑤',
              '6': '⑥',
              '7': '⑦',
              '8': '⑧',
              '9': '⑨',
              '10': '⑩'
              // 添加更多的映射关系，如果需要更多的圆圈数字
            };
          
            return text.replace(/__\[填空(\d+)\]__/g, function(_, number) {
              return numberMap[number] || '';
            });
          }

          function replaceAnswerKey(obj) {
            const newObj = {};
            for (const key in obj) {
              const value = obj[key];
              const newKey = replaceWithCircleNumbers(key);
              newObj[newKey] = value;
            }

            return newObj;
          }

          function updateQuestion(allQuestions) {
            for (const question of allQuestions) {
              const { questionType, questionDetail = {}, answer = {} } = question;

              if (questionType !== '编程填空题' || !questionDetail) {
                continue;
              }

              const { code, answerKeys = [] } = questionDetail;
              
              // 检查answerKeys长度不超过10
              if (answerKeys.length > 10) {
                throw new Error(`题目${question.id}的answerKeys长度超过10`);
              } else if (answerKeys.length === 0) {
                console.error(`题目${question.id}的answerKeys为空`);
                continue;
              }

              const newCode = replaceWithCircleNumbers(code);
              questionDetail.code = newCode;
              questionDetail.originCode = newCode;
              questionDetail.answerKeys = answerKeys.map((item) => {
                return replaceWithCircleNumbers(item);
              });

              // 替换答案
              if (!answer) {
                continue;
              }

              const { answer: answerContent, score } = answer;
              if (answerContent) {
                answer.answer = replaceAnswerKey(answerContent);
              }

              if (score) {
                answer.score = replaceAnswerKey(score);
              }
            }

            return allQuestions;
          }

          try {
            // 更新题库题目 ========================
            const allQuestions = await Questions.findAll({
              attributes: ['id', 'questionDetail', 'answer'],
              where: {
                questionType: '编程填空题'
              },
              raw: true,
              transaction
            });

            const allQuestionIDs = allQuestions.map(item => item.id);

            const updatedQuestions = updateQuestion(allQuestions);

            await Questions.bulkCreate(updatedQuestions, {
              updateOnDuplicate: ['questionDetail', 'answer'],
              transaction
            });

            // 更新题目勘误中的题目内容 ========================
            const allCorrectQuestions = await model.TrainQuestionCorrect.findAll({
              attributes: ['id', 'questionDetail', 'answer'],
              where: {
                questionType: '编程填空题',
              },
              attributes: ['id', 'questionDetail', 'answer'],
              raw: true,
              transaction
            });

            const updatedCorrectQuestions = updateQuestion(allCorrectQuestions);

            await model.TrainQuestionCorrect.bulkCreate(updatedCorrectQuestions, {
              updateOnDuplicate: ['questionDetail', 'answer'],
              transaction
            });

            // 获取全部训练计划 ========================
            const allTrainPlans = await model.TrainPlan.findAll({
              attributes: ['id'],
              raw: true,
              transaction
            });

            const allTrainPlanIDs = allTrainPlans.map(item => item.id);

            // 更新学生答题记录 ========================
            const allStudentRecords = await model.TrainUserRecord.findAll({
              where: {
                planID: {
                  [Op.in]: allTrainPlanIDs
                },
              },
              attributes: ['id', 'record'],
              raw: true,
              transaction
            });

            for (const studentRecord of allStudentRecords) {
              const { record } = studentRecord;
              if (!record) {
                continue;
              }

              for (const questionID in record) {
                const isCodeQuestion = allQuestionIDs.includes(Number(questionID));

                if (isCodeQuestion) {
                  const questionRecord = record[questionID];
                  if (!questionRecord) {
                    continue;
                  }

                  const { answer = {}, results = {}, initAnswer = {}, initResults = {} } = questionRecord;
                  if (answer) {
                    const newAnswer = replaceAnswerKey(answer);
                    record[questionID].answer = newAnswer;
                  }
                  
                  if (results) {
                    const newResults = replaceAnswerKey(results);
                    record[questionID].results = newResults;
                  }

                  if (initAnswer) {
                    const newInitAnswer = replaceAnswerKey(initAnswer);
                    record[questionID].initAnswer = newInitAnswer;
                  }

                  if (initResults) {
                    const newInitResults = replaceAnswerKey(initResults);
                    record[questionID].initResults = newInitResults;
                  }
                }
              }

              studentRecord.record = record;
            }

            await model.TrainUserRecord.bulkCreate(allStudentRecords, {
              updateOnDuplicate: ['record'],
              transaction
            });

            // 更新文件内容 ========================
            const { config } = app;

            // 更新题目文件 ========================
            const questionBaseDir = path.join(config.file.dir, `${schoolSlug}/trainQuestion`);
            console.log(`开始更新${schoolSlug}的题目文件，文件夹路径：${questionBaseDir}`)

            const questionExist = await fs.exists(questionBaseDir);
            if (questionExist && updatedQuestions.length > 0) {
              for (const question of updatedQuestions) {
                const { id: questionID, questionDetail, title } = question;
                if (!questionDetail || !title || !questionID) {
                  continue;
                }

                const { code } = questionDetail;
                const assetsDir = path.join(questionBaseDir, `${questionID}/assets`);
                const isExist = await fs.exists(assetsDir);
                if (!isExist) {
                  // 没有则创建
                  await fs.mkdir(assetsDir);
                }

                // 写文件
                const filePath = path.join(assetsDir, `${title.trim()}.py`);
                await fs.writeFile(filePath, code, 'utf8');
                console.log(`题目${questionID}的文件已更新`)
              }
            }

            // 更新训练计划文件 ========================
            const baseDir = path.join(config.file.dir, `${schoolSlug}/trainPlan`);
            
            console.log(`开始更新${schoolSlug}的训练计划文件，文件夹路径：${baseDir}`)
            const exist = await fs.exists(baseDir);
            if (exist) {
              const allReadFiles = [];

              for (const planID of allTrainPlanIDs) {
                const fileDir = path.join(baseDir, `${planID}/train`);
    
                // 检查是否存在
                const isExist = await fs.exists(fileDir);
                if (!isExist) {
                  continue;
                }
    
                // 读取文件夹 ========================
                const files = await fs.readdir(fileDir);
                if (!files || files.length === 0) {
                  continue;
                }
    
                for (const file of files) {
                  const trainDir = path.join(fileDir, file);
                  const stat = await fs.stat(trainDir);
                  if (!stat.isDirectory()) {
                    continue;
                  }
    
                  const trainPath = path.join(trainDir, 'questions.json');
                  const trainAnswerPath = path.join(trainDir, 'answer.json');
    
                  console.log(`开始读取${schoolSlug}的训练计划文件，文件路径：${trainPath}`)
    
                  // 读取文件内容 ========================
                  const exists = await fs.exists(trainPath);
                  if (!exists) {
                    console.error(`文件${trainPath}不存在`);
                    continue;
                  }
    
                  const questionContent = await fs.readFile(trainPath, 'utf8');
    
                  // 解析文件内容 ========================
                  let trainQuestionContent;
                  try {
                    trainQuestionContent = JSON.parse(questionContent);
                  } catch(e) {
                    console.error(e);
                    console.error(`文件${trainPath}内容不是JSON格式`);
                    continue;
                  }
    
                  if (!trainQuestionContent) {
                    console.error(`文件${trainPath}内容为空`);
                    continue;
                  }
    
                  let codeBlankQuestionIDs = [];
                  for (const item of trainQuestionContent) {
                    if (!item || !item.type || !item.questions || !item.questions.length) {
                      continue;
                    }
    
                    const { questions = [] } = item;
                    const newQuestions = updateQuestion(questions);
                    item.questions = newQuestions;
    
                    const codeIds = questions.filter((question) => {
                      return question.questionType === '编程填空题';
                    }).map((question) => {
                      return question.id;
                    });
    
                    codeBlankQuestionIDs = codeBlankQuestionIDs.concat(codeIds);
                  }
    
                  if (codeBlankQuestionIDs && codeBlankQuestionIDs.length) {
                    // 记录要更新的文件
                    allReadFiles.push({
                      path: trainPath,
                      content: JSON.stringify(trainQuestionContent, null, 2)
                    });
    
                    // 读取答案文件 ========================
                    const exists = await fs.exists(trainAnswerPath);
                    if (!exists) {
                      continue;
                    }
    
                    const answerContent = await fs.readFile(trainAnswerPath, 'utf8');
                    let answers;
                    try {
                      answers = JSON.parse(answerContent);
                    } catch(e) {
                      console.error(e);
                      console.error(`文件${trainAnswerPath}内容不是JSON格式`);
                      continue;
                    }
    
                    for (const codeBlankID of codeBlankQuestionIDs) {
                      const answer = answers[codeBlankID];
                      if (!answer) {
                        continue;
                      }
    
                      const { answer: answerContent, score } = answer;
                      if (answerContent) {
                        answer.answer = replaceAnswerKey(answerContent);
                      }
    
                      if (score) {
                        answer.score = replaceAnswerKey(score);
                      }
                    }
    
                    allReadFiles.push({
                      path: trainAnswerPath,
                      content: JSON.stringify(answers, null, 2)
                    });
                  }
                }
              }
    
              // 批量写入文件
              if (allReadFiles && allReadFiles.length) {
                for (const file of allReadFiles) {
                  const { path: filePath, content } = file;
                  await fs.writeFile(filePath, content, 'utf8');
                  console.log(`${schoolSlug}的训练计划文件更新完成，文件路径：${filePath}`)
                }
              }
            }
            await transaction.commit();
          } catch(e) {
            console.error(e);
            await transaction.rollback();
            throw e;
          }
        }

        // 单选题选项使用markdown渲染且同时选项含有图片的，删除图片引用
        if (dbVersion < 99) {
          const { model } = ctx;
          const transaction = await model.transaction({ autocommit: false });

          try {
            // 获取所有单选题
            const allQuestions = await model.Questions.findAll({
              attributes: ['id', 'questionType', 'questionDetail'],
              where: {
                questionType: {
                  [Op.in]: ['单选题', '综合题']
                }
              },
              raw: true,
              transaction
            });

            // 获取所有单选题的选项
            if (allQuestions && allQuestions.length) {
              for (const question of allQuestions) {
                const { questionType, questionDetail } = question;
                if (!questionDetail) {
                  continue;
                }

                if (questionType === '单选题') {
                  const { options = [] } = questionDetail;
                  if (!options || !options.length) {
                    continue;
                  }
    
                  const newOptions = [];
                  for (const option of options) {
                    const { image, markdown } = option;
                    if (image && image !== "" && markdown) {
                      newOptions.push({
                        ...option,
                        image: "",
                      });
                    } else {
                      newOptions.push(option);
                    }
                  }
    
                  questionDetail.options = newOptions;
                }

                if (questionType === '综合题') {
                  if (!questionDetail || !questionDetail.length) {
                    continue;
                  }

                  for (const subQuestion of questionDetail) {
                    const { questionType: type, questionDetail: detail } = subQuestion;
                    if (type !== '单选题' && type !== '多选题' && type !== '选择填空题') {
                      continue;
                    }

                    if (!detail) {
                      continue;
                    }

                    const { options = [] } = detail;
                    if (!options || !options.length) {
                      continue;
                    }

                    const newOptions = [];
                    for (const option of options) {
                      const { image, markdown } = option;
                      if (image && image !== "" && markdown) {
                        newOptions.push({
                          ...option,
                          image: "",
                        });
                      } else {
                        newOptions.push(option);
                      }
                    }

                    detail.options = newOptions;
                  }
                }
              }
            }

            // 更新题目
            await model.Questions.bulkCreate(allQuestions, {
              updateOnDuplicate: ['questionDetail'],
              transaction
            });

          } catch(e) {
            await transaction.rollback();
            console.log('99',e);
            throw e;
          }

          await transaction.commit();
        }

        // 新建表 train_series train增加字段 series
        if (dbVersion < 100) {
          const { model } = ctx;
          const transaction = await model.transaction({ autocommit: false });

          try {
            // 新建表 train_series
            await model.TrainSeries.sync();

            // // train增加字段 series
            // await model.query('ALTER TABLE `train` ADD `series` INT(11) DEFAULT null AFTER `year`;', { transaction: transaction });

            // 查找所有创建试卷的用户
            const allUsers = await model.User.findAll({
              attributes: ['id'],
              where: {
                adminAuthority: {
                  [Op.not]: null
                }
              },
              attributes: ['id'],
              raw: true,
              transaction: transaction
            });

            const allTrains = await model.Train.findAll({
              attributes: ['id', 'createUserID', 'teachers'],
              raw: true,
              transaction: transaction
            });

            const seriesTrainMap = {};
            const shareSeriesTrainMap = {};

            for (const train of allTrains) {
              const { id: trainID, createUserID, teachers } = train;
              if (!createUserID) {
                continue;
              }

              if (!seriesTrainMap[createUserID]) {
                seriesTrainMap[createUserID] = [];
              }

              if (!seriesTrainMap[createUserID].includes(trainID)) {
                seriesTrainMap[createUserID].push(trainID);
              }

              // 如果有分享的老师，则也记录
              if (teachers && teachers.length) {
                for (const teacher of teachers) {
                  const tID = parseInt(teacher, 10);
                  if (!shareSeriesTrainMap[tID]) {
                    shareSeriesTrainMap[tID] = [];
                  }
      
                  if (!shareSeriesTrainMap[tID].includes(trainID)) {
                    shareSeriesTrainMap[tID].push(trainID);
                  }
                }
              }
            }

            // 为每个老师创建系列
            const allSeries = [];
            for (const user of allUsers) {
              const { id: userID } = user;

              if (seriesTrainMap[userID]) {
                allSeries.push({
                  name: '默认系列',
                  priority: 0,
                  createUserID: userID,
                  trainIDs: seriesTrainMap[userID]
                });
              }

              if (shareSeriesTrainMap[userID]) {
                allSeries.push({
                  name: '默认分享系列',
                  priority: 0,
                  createUserID: userID,
                  trainIDs: shareSeriesTrainMap[userID]
                });
              }
            }

            await model.TrainSeries.bulkCreate(allSeries, {
              updateOnDuplicate: ['name', 'priority', 'trainIDs'],
              transaction: transaction
            });

          } catch(e) {
            await transaction.rollback();
            console.log('100',e);
            throw e;
          }

          await transaction.commit();
        }

        // trainplan表增加teachers字段，表示分享给的老师id
        if (dbVersion < 101) {
          const { model } = ctx;
          const transaction = await model.transaction({ autocommit: false });

          try {
            // trainplan表增加teachers字段，表示分享给的老师id
            await model.query('ALTER TABLE `train_plan` ADD `teachers` JSON DEFAULT null AFTER `createUserID`;', { transaction: transaction });
          } catch(e) {
            await transaction.rollback();
            console.log('101',e);
            throw e;
          }

          await transaction.commit();
        }
      }

      // section_record表建立索引，加快查询速度
      if (dbVersion < 102) {
        const { model } = ctx;
        const transaction = await model.transaction({ autocommit: false });

        try {
          // section_record表建立索引，加快查询速度， sectionID_userID_deletedAt_index
          await model.query('ALTER TABLE `section_record` ADD INDEX `sectionID_userID_deleted_at_index` (`sectionID`, `userID`, `deleted_at`);', { transaction: transaction });
        } catch(e) {
          await transaction.rollback();
          console.log('102',e);
          throw e;
        }

        await transaction.commit();
      }

      
      if(!trainDeleted)
      {
        if (dbVersion < 103) {
          const { model } = ctx;
          const transaction = await model.transaction({ autocommit: false });

          try {
            // train_plan增加字段最短作答时长
            await model.query('ALTER TABLE `train_plan` ADD `minTrainDuration` INT(11) DEFAULT null AFTER `duration`;', { transaction: transaction });
          } catch(e) {
            await transaction.rollback();
            console.log('103', e);
            throw e;
          }

          await transaction.commit();
        }

        if (dbVersion < 104) {
          try {
            // train_plan增加字段最短作答时长
            await model.query('ALTER TABLE `train_series` ADD `teachers` JSON NULL AFTER `createUserID`;');
          } catch(e) {
            console.log('104', e);
            throw e;
          }
        }

        if (dbVersion < 105) {
          const { model } = ctx;
          const transaction = await model.transaction({ autocommit: false });

          // 处理复制试卷产生的时长和总分都为空的记录
          // 更新所有没有时长的试卷时长为60
          await model.query('UPDATE `train` SET `duration` = 60 WHERE `duration` IS NULL AND deleted_at IS NULL;', { transaction: transaction });

          // 抓取所有总分为空的为被删除的试卷
          const allNullScoreTrains = await model.Train.findAll({
            attributes: ['id', 'template'],
            where: {
              score: null
            },
            raw: true,
            transaction: transaction
          });

          // 按照template计算总分
          for(const allNullScoreTrain of allNullScoreTrains) {
            const { template } = allNullScoreTrain;
            if (!template) {
              allNullScoreTrain.score = 0;
              continue;
            }

            allNullScoreTrain.score = template.reduce((total, item) => {
              return total + item.score * item.count;
            }, 0);
          }

          // 回写总分
          await model.Train.bulkCreate(allNullScoreTrains, {
            updateOnDuplicate: ['score'],
            transaction: transaction
          });

          await transaction.commit();
        }
      }

      if(dbVersion < 106) {
        // 检查team_user表是否存在名为team_user_userID_teamID_unique的索引
        // 如果存在，删除之
        try {
          await ctx.model.query('DROP INDEX `team_user_userID_teamID_unique` ON `team_user`;');
        } catch(e) {
        }
      }

      if(!trainDeleted)
      {
        if(dbVersion < 107) {
          // 如果train_tag表格存在的话，我们要删除其中涉及到远程题库的冗余标签
          let remoteQuestionBank = null;
          try {
            // 1. 通过train_question_bank表格，找到远程题库名叫 高中信息技术 对应的id
            remoteQuestionBank = await ctx.model.TrainQuestionBank.findOne({
              where: {
                name: '高中信息技术'
              },
              raw: true
            });
          }
          catch(e) {
            console.log('题库不存在，可以安全跳过', e);
          }

          //  如果train_question_bank表格不存在跳过
          if(remoteQuestionBank) {
            // 列举全部的标签
            const remoteTags = await ctx.model.Tag.findAll({
              attributes: ['id', 'tagName', 'parentID'],
              where: {
                questionBankID: remoteQuestionBank.id
              },
              raw: true
            });

            // 建立全部标签的映射表
            const tagMap = {};
            for(const tag of remoteTags) {
              tagMap[tag.id] = tag;
            }

            // 列举同名标签下的题目数量
            const tagQuestionCount = await ctx.model.QuestionTag.findAll({
              attributes: ['tagID', [model.Sequelize.fn('COUNT', model.Sequelize.col('tagID')), 'questionCount']],
              group: ['tagID'],
              raw: true
            });

            // 建立标签id和题目数量的映射表
            const tagQuestionCountMap = {};
            for(const tagQuestion of tagQuestionCount) {
              tagQuestionCountMap[tagQuestion.tagID] = tagQuestion.questionCount;
            }

            // 记录没有题目的标签作为待删除标签
            const sameNameTagIDSetMap = {};

            // 建立标签树
            const tagTree = [];
            for(const tag of remoteTags) {
              if(tag.parentID) {
                const parentTag = tagMap[tag.parentID];
                if(parentTag) {
                  if(!parentTag.children) {
                    parentTag.children = [];
                  }

                  // 寻找同名节点
                  const oldSameNameTag = parentTag.children.find(child => child.tagName === tag.tagName);
                  if(oldSameNameTag) {
                    // 验证题目数量
                    const questionCount = tagQuestionCountMap[tag.id];

                    // 如果没有题目，删除
                    if(!questionCount) {
                      let sameNameTagIDSet = sameNameTagIDSetMap[tag.tagName];
                      if(!sameNameTagIDSet) {
                        sameNameTagIDSet = new Set();
                        sameNameTagIDSetMap[tag.tagName] = sameNameTagIDSet;
                      }

                      sameNameTagIDSet.add(tag.id);
                      sameNameTagIDSet.add(oldSameNameTag.id);
                    }

                    continue;
                  }

                  parentTag.children.push(tag);
                }
              }
              else {
                tagTree.push(tag);
              }
            }

            const deleteTagIDs = [];
            
            // 对于每一个同名标签，进行按照题目数量的排序，仅保留题目数量最多的标签
            for(const sameNameTagIDSet of Object.values(sameNameTagIDSetMap)) {
              const tagIDs = Array.from(sameNameTagIDSet);
              tagIDs.sort((a, b) => {
                const questionCountA = tagQuestionCountMap[a] || 0;
                const questionCountB = tagQuestionCountMap[b] || 0;

                return questionCountB - questionCountA;
              });

              for(let i = 1; i < tagIDs.length; i++) {
                deleteTagIDs.push(tagIDs[i]);
              }
            }

            console.log('删除标签', deleteTagIDs);

            // 删除标签
            if(deleteTagIDs.length) {
              await ctx.model.Tag.destroy({
                where: {
                  id: {
                    [Op.in]: deleteTagIDs
                  }
                }
              });
            }
          }
        }

        if(dbVersion < 108) {
          const transaction = await ctx.model.transaction({ autocommit: false });

          // 解决试卷已经被删除，但是试卷系列中仍有残留引用的问题
          // 1. 验证学校是否开通了训练服务
          const isTrainDeleted = await model.SystemConfig.findOne({
            where: {
              key: 'trainDeleted'
            },
            raw: true,
            transaction
          });

          if(!(isTrainDeleted && isTrainDeleted.value === 'true')) {

            // 列举学校现存的全部试卷ID
            const trainIDRecords = await model.Train.findAll({
              attributes: ['id'],
              raw: true,
              transaction
            });

            const trainIDSets = new Set(trainIDRecords.map(record => record.id));

            // 列举全部的试卷系列
            const allSeries = await model.TrainSeries.findAll({
              attributes: ['id', 'trainIDs'],
              transaction
            });

            for(const series of allSeries) {
              const { trainIDs } = series;

              // 删除无效引用
              const newTrainIDs = trainIDs.filter(trainID => trainIDSets.has(trainID));
              if(newTrainIDs.length !== trainIDs.length) {
                series.trainIDs = newTrainIDs;
                await series.save({ transaction });
              }
            }
          }

          await transaction.commit();
        }

        if(dbVersion < 109) {
          // 解决试卷已经被删除，但是试卷系列中仍有残留引用的问题
          // 1. 验证学校是否开通了训练服务
          const isTrainDeleted = await model.SystemConfig.findOne({
            where: {
              key: 'trainDeleted'
            },
            raw: true
          });

          if(!(isTrainDeleted && isTrainDeleted.value === 'true')) {
            await ctx.model.query('ALTER TABLE `train_user_record` ADD INDEX `updateByIDAndStatus` (`id`, `status`, `deleted_at`) USING BTREE;');
            await ctx.model.query('ALTER TABLE `train_user_record` ADD INDEX `findID` (`planID`, `userID`, `trainID`, `deleted_at`, `status`) USING BTREE;');
          }
        }
        
        if(dbVersion < 110) {
          // 更新固定的机房训练模式的配置
            await ctx.model.query('UPDATE train_plan SET ifForbidViewPaper = 0, ifRandomOrder = 0, ifSetWrongProblemCollection = 1, ifShowCorrectionResults = 1, ifShowScore = 1, ifShowWrongAnswer = 1 WHERE environment = "机房训练";');
        }

        if(dbVersion < 111) {
          // system_config
          // 如果trainUpdateRecord不存在，则创建
          const trainUpdateRecord = await model.SystemConfig.findOne({
            where: {
              key: 'trainUpdateRecord'
            },
            raw: true
          });

          if(!trainUpdateRecord)
          {
            await model.SystemConfig.create({
              key: 'trainUpdateRecord',
              value: []
            }, {
              raw: true
            });
          }
        }

        if(dbVersion < 112) {
          // SELECT t1.* FROM train_question_tag t1 JOIN train_question_tag t2 ON t1.questionID = t2.questionID AND t1.tagID = t2.tagID AND t1.id > t2.id WHERE t1.deleted_at is null and t2.deleted_at is null;
          // DELETE t1 FROM train_question_tag t1 JOIN train_question_tag t2 ON t1.questionID = t2.questionID AND t1.tagID = t2.tagID AND t1.id > t2.id WHERE t1.deleted_at is null and t2.deleted_at is null;

          // 删除重复的题目标签记录：多条重复的仅保留第一条
          await ctx.model.query(`DELETE t1 FROM train_question_tag t1 JOIN train_question_tag t2 ON t1.questionID = t2.questionID AND t1.tagID = t2.tagID AND t1.id > t2.id WHERE t1.deleted_at is null and t2.deleted_at is null;`);
        }

        if(dbVersion < 113){
          // 获取数据库名称
          const dbName = await ctx.model.query('SELECT DATABASE();', { type: QueryTypes.SELECT });
          const { 'DATABASE()': databaseName } = dbName[0];
          console.log('数据库名称', databaseName);

          const packTrainDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion`;
          console.log('packTrainDir', packTrainDir); 

          if (databaseName !== 'hzchzx' && databaseName !== 'csxx') {

            // 数据库操作
            // 查询train_question_bank中fromURL为4的记录
            let trainQuestionBank = (await ctx.model.query(`SELECT * FROM train_question_bank WHERE fromURL = 4;`))[0];

            if(trainQuestionBank) {
              const trainQuestionBankID = trainQuestionBank[0].id;
              // 删除train_question_bank中fromURL为4的记录（软删除，即设置delete_at字段为当前时间）
              await ctx.model.query(`UPDATE train_question_bank SET deleted_at = NOW() WHERE fromURL = 4;`);        
              // 删除train_questions表中questionBankID等于14的记录（软删除，即设置delete_at字段为当前时间）并将其id组成一个数组
              const questionIDs = (await ctx.model.query(`SELECT id FROM train_questions WHERE questionBankID = ${trainQuestionBankID};`))[0];
              let questionIDsList = questionIDs.map(item => item.id);
              await ctx.model.query(`UPDATE train_questions SET deleted_at = NOW() WHERE id IN (${questionIDsList});`);
              // 删除train_question_tag中questionID等于14的记录（软删除，即设置delete_at字段为当前时间）
              await ctx.model.query(`UPDATE train_question_tag SET deleted_at = NOW() WHERE questionID IN (${questionIDsList});`);

              // 文件系统操作
              // 根据questionIDsList删除文件系统的内容
              for (const questionID of questionIDsList) {
                await delDir(`${packTrainDir}/${questionID}`)
              }
            }
          }
          
        }
      }
      // 将当前版本号，回写至数据库配置表dbVersion字段
      if(dbVersionConfig)
      {
        dbVersionConfig.value = currentDBVersion;
        await dbVersionConfig.save();
      }
      else
      {
        await SystemConfig.create({
          key: 'dbVersion',
          value: currentDBVersion,
        });
      }
    }

    // 查询学校Tag表，验证学校信息
    async getSchoolTag() {
      const { model } = this.ctx;
      const { Tag } = model;

      return await Tag.findOne({
        where: {
          tagType: 'school',
        },
        raw: true
      });
    }

    // 查询学校需相似的单选题信息
    async getSimilarSingleChoiceQuestion() {
      const { model } = this.ctx;
      const { Questions } = model;

      return await Questions.findAll({
        attributes: ['id', 'questionDetail'], // 指定查询的字段
        where: {
          questionType: '单选题', // 添加条件，筛选单选题
          deleted_at: null, // 添加条件，筛选未被删除的题目
          questionDetail: {
            [Op.ne]: null // 过滤掉 questionDetail 为 null 的记录
          },
        },
        limit: 1000, // 每页 10 条
        raw: true // 返回纯 JSON 对象
      });
    }

    // 查询学校需相似的综合题信息
    async getComprehensiveQuestion() {
      const { model } = this.ctx;
      const { Questions } = model;

      return await Questions.findAll({
        attributes: ['id', 'questionDetail'], // 指定查询的字段
        where: {
          questionType: '综合题', // 添加条件，筛选单选题
          deleted_at: null, // 添加条件，筛选未被删除的题目
          questionDetail: {
            [Op.ne]: null // 过滤掉 questionDetail 为 null 的记录
          },
        },
        limit: 100, // 每页 10 条
        raw: true // 返回纯 JSON 对象
      });
    }
  }

  return DatabaseService;
}