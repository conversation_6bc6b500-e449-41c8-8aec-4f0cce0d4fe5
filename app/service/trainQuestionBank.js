const { Op } = require('sequelize');
const { fs } = require('mz');

const getChildren = (rows, parentID) => {
  if(!rows || !rows.length) {
    return null
  }

  return rows.filter(row => row.parentID === parentID).map(row => {
    // console.log('row:',row)
    const children = getChildren(rows, row.id);
    if (children && children.length) {
      return {
        tagID: row.id,
        id: `${row.id}`,
        text: row.tagName,
        parentID: parentID,
        questionBankID: row.questionBankID,
        showChildren: true,
        children,
      }
    }

    return {
      tagID: row.id,
      id: `${row.id}`,
      text: row.tagName,
      parentID: parentID,
      questionBankID: row.questionBankID,
      children: [],
    }
  })
}

module.exports = app => {

  class TrainQuestionBankService extends app.Service {
    // 创建训练
    async createTrainQuestionBank(node, transaction) {
      const { model } = this.ctx;
      const { TrainQuestionBank } = model;

      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }
            
      const { nodes=[], ...data} = node;
      const result = await TrainQuestionBank.create(data, { transaction });
      // console.log('result.dataValues.id:',result.dataValues.id
      // 将复制的节点复制进去
      if(nodes && nodes.length){
        await this.ctx.service.tag.bulkEditTag(result.id, nodes, transaction);
      }
    
      return this.ctx.service.trainQuestionBank.getTrainQuestionBank(result.id, transaction)
    }

    // 修改训练
    async putTrainQuestionBank(id, node, transaction) {
      const { model } = this.ctx;
      const { TrainQuestionBank } = model;

      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      await TrainQuestionBank.update(node, {
        where: {
          id
        },
        transaction
      });

      return this.ctx.service.trainQuestionBank.getTrainQuestionBank(id, transaction)
    }

    // 获取训练
    async getTrainQuestionBank(id, transaction = false) {
      const { model } = this.ctx;
      const { TrainQuestionBank } = model

      const result = await TrainQuestionBank.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      const tagData = await this.ctx.service.tag.getTrainTagList(id, transaction);
      // console.log('tagData:',tagData);
      result.nodes = getChildren(tagData.train, 0)

      return result 
    }

    // 删除训练
    async destoryTrainQuestionBank(id, transaction = false) {
      const { model } = this.ctx;
      const { TrainQuestionBank } = model

      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      return await TrainQuestionBank.destroy({
        where: {
          id
        },
        transaction
      });
    }

    // 获取训练列表
    async getTrainQuestionBankList() {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainQuestionBank } = model;

      //获取对应值
      return await TrainQuestionBank.findAll();
    }

    async getTrainQuestionBankListAdmin() {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainQuestionBank } = model;
      // 检查权限
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      //获取对应值
      return await TrainQuestionBank.findAll();
    }

    // 获取AI生成的题解
    async explainQuestionByAI(id, transaction = false) {
      const { model, service } = this.ctx;
      const { TrainQuestionBank, Questions } = model;

      // 查询对应的题目记录及其包含的题库记录
      const question = await Questions.findOne({
        attributes: ['id', 'questionType', 'originalID', 'answer', 'questionDetail'],
        where: {
          id
        },
        include: {
          model: TrainQuestionBank,
          as: 'trainQuestionBank',
          attributes: ['ifSelfBuilding', 'toID', 'fromURL']
        },
        raw: true,
        transaction
      });

      const { questionType, originalID, answer, questionDetail } = question;
      
      const questionBankID = question['trainQuestionBank.ifSelfBuilding'] ? question['trainQuestionBank.toID']: question['trainQuestionBank.fromURL'];
      if(!questionBankID) {
        throw new Error('题目所述题库暂未发布远程题库，无法获取题解');
      }

      // 问题ID 默认用原始题库ID，如果是自建题库则用题目ID
      const questionID = originalID || id;

      // 如果questionDetail中包含题解，不要传送
      delete questionDetail.solution;

      // 标准题目格式
      const stdQuestion = {
        questionType,
        questionDetail,
        answer,
      };

      delete stdQuestion.solution;

      // 通过第三方接口请求主站获取题解，并发时可能会需要等待，等待10秒，最多三次
      let explainResponse = null;
      let count = 0;
      do {
        count++;
        const url = `thridParty/questionBank/${questionBankID}/question/${questionID}/explain`;
        explainResponse = await service.thirdPart.thirdPartFetch(
          // url, // !!本地测试
          `management-api/${url}`,
          {
            question: stdQuestion
          }
        );

        // console.log('explainResponse:', explainResponse);

        if(explainResponse && explainResponse.code === 0 && explainResponse.data) {
          break;
        }

        await new Promise(resolve => setTimeout(resolve, 10000));
      }
      while (count < 3);

      if(!(explainResponse && explainResponse.data)) {
        throw new Error('题解正在生成，请稍候再试！');
      }

      return explainResponse.data;
    }
    // OJ题型数据入库
    async uploadQuestionsForXmlFiles(questionDetails, user, questionBankID, transaction = false) {
      const { model, service } = this.ctx;
      const { Questions } = model;

      // 遍历questionDetails
      for (const questionDetail of questionDetails) {
        // 获取answer数组，定义为空数组
        const answer = [];
        //遍历questionDetail.judgeMenu.testFiles
        for (const testFile of questionDetail.judgeMenu.testFiles) {
          let testFileAnswer = {
            score: testFile.score,
            answer: testFile.fileName,
          }
          answer.push(testFileAnswer);
        }
        // 插入题目记录，并获取id
        const { id } = await Questions.create({
          title: questionDetail.title,
          createUserID: user.id,
          questionType: "在线编程评测题",
          questionDetail: questionDetail,
          answer,
          difficultyConfig: "未鉴定",
          // difficulty: "",
          // trainsUsed: "",
          // source: "",
          status: "草稿",
          // author: "",
          // answerCount: "",
          // passCount: "",
          questionBankID,
          // originalID: "",
        })
        // 获取题目的assets目录
        const dirPath = `${app.config.file.dir}/${user.schoolSlug}/trainQuestion/${id}/assets`;
        // 创建assets目录
        await this.ctx.service.file.mkdirs(dirPath);
        // /${questionDetail.extraCodePath}
        // 获取文件内容
        const fileContent = questionDetail.defaultCode;
        await fs.writeFile(`${dirPath}/${questionDetail.extraCodePath}`, fileContent);

      }

      // return explainResponse.data;
    }

    // 预热AI生成题解
    async warmupAIExplain(id) {
      // 确认题库是自身发布的共享题库，ifSelfBuilding为1，并且有toID
      const { model } = this.ctx;
      const { TrainQuestionBank } = model;

      const questionBank = await TrainQuestionBank.findOne({
        where: {
          id
        },
        raw: true
      });

      if(!questionBank || !questionBank.ifSelfBuilding || !questionBank.toID) {
        throw new Error('题库不是自身发布的共享题库，无法预热题解');
      }

      const questionBankID = questionBank.toID;

      // 列举该题库内的全部试题，整理为标准格式
      const { Questions } = model;
      let questions = await Questions.findAll({
        attributes: ['id', 'questionType', 'originalID', 'answer', 'questionDetail'],
        where: {
          questionBankID: id
        },
        raw: true
      });

      questions = questions.filter(({questionDetail, answer}) => questionDetail && answer).map(question => {
        const { questionType, originalID, answer, questionDetail } = question;
        const questionID = originalID || question.id;

        delete questionDetail.solution;
        delete questionDetail.note;

        const stdQuestion = {
          questionType,
          questionDetail,
          answer,
        };

        return {
          questionID,
          question: stdQuestion
        };
      });

      // 队列放到异步任务中，逐步生成题解和对应的TTS合成语音
      const queue = app.queue['train-task'];
      if(!queue) {
        throw new Error('队列不存在');
      }

      // 获取管理平台配置
      const { managerment, managermentAPI } = app.config;

      // 每20个题目一组作为任务放入队列
      const groupSize = 20;
      for(let i = 0; i < questions.length; i += groupSize) {
        const subQuestions = questions.slice(i, i + groupSize);

        await queue.add({
          type: 'warmupAIExplain',
          managerment,
          managermentAPI,
          questionBankID,
          questionRecords: subQuestions,
        });
      }

    }
  }

  return TrainQuestionBankService;
}