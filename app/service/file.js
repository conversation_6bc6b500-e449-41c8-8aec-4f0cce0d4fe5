const moment = require('moment');
const sendToWormhole = require('stream-wormhole');
const fs = require("mz/fs");
const fsExtra = require("fs-extra");
const path = require("path");
const AdmZip = require("adm-zip");
const { Op, QueryTypes } = require('sequelize');
const md5 = require('md5');
const { makeUUID, getStringByteLength, getFPSSharedFileHashList } = require('../utils/fps');
const { readFileMd5 } = require('../utils/file');
const PATH = require('path');
const fileType = require('file-type');

function safePipe(readStream, writeStream)
{
  return new Promise(function(resolve, reject){
    readStream.pipe(writeStream);
    writeStream.on('finish', function(){
      resolve();
    });
  });
}

function getReadStreamTextContent(readStream) {
  return new Promise(resolve => {
    let chunks = [];
    let size = 0;
    readStream.on('data', data => {
      // text += data;
      chunks.push(data);
      size += data.length;
    })
    readStream.on('end', () => {
      let buffer = Buffer.concat(chunks, size);
      let text = buffer.toString();
      resolve(text)
    })
  })
}

function getReadStreamBinaryContent(readStream) {
  return new Promise(resolve => {
    let chunks = [];
    let size = 0;
    readStream.on('data', data => {
      chunks.push(data);
      size += data.length;
    })
    readStream.on('end', () => {
      let buffer = Buffer.concat(chunks, size);
      resolve(buffer)
    })
  })
}

function parseMicroApp(sourceText) {
  const dataParts = sourceText.match(/YopuWidget\((.+)\)$/);
  if (dataParts) {
    let sourceData = dataParts[1].split(/"(.+)",/);
    sourceData = sourceData.filter(data => data && data.length);
    const [sourceFile, params] = sourceData;
    let sourceParams = {};
    if (params) {
      sourceParams = params.slice(2, (params.length - 1)).replace(/'/g, '"');
      sourceParams = JSON.parse(sourceParams);
    }
    const [type, fileName] = sourceFile.replace(/(\s|[\"])*/g, '').split(',');
    return {type, fileName, sourceParams}
  } else {
    return null;
  }
};

module.exports = app => {
  
  class File extends app.Service {
    // 检查文件类型是否是列表中允许的，仅依赖文件前100字节
    async checkFileTypeAllowed(url, filePath, fileExtensions = app.config.multipart.fileExtensionsImageOnly) {
      // 读取前100字节文件，确定文件类型
      const buffer = await fs.readFile(filePath, { start: 0, end: 100 });
      const type = await fileType.fromBuffer(buffer);

      // 无法判定类型
      if(!type) {
        return false;
      }

      // 文件路径中的后缀名与文件内容中的后缀名不一致
      const typeExtName = "." + type.ext;
      if (type && PATH.extname(filePath) !== typeExtName) {
        // 此时重命名文件，并修改url
        const newPath = filePath.replace(/(\.[^\.]+)$/, typeExtName);
        await fs.rename(filePath, newPath);
        filePath = newPath;

        // 修改url
        url = url.replace(/(\.[^\.]+)$/, typeExtName);
      }

      // 不在允许的列表中，抛出异常
      if(fileExtensions.indexOf(typeExtName) === -1) {
        const err = new Error('文件类型不允许');
        err.path = filePath;
      }

      return [url, filePath];
    }

    // 递归创建目录 异步方法  
    async mkdirs(dirPath) {
      await fs.mkdir(dirPath, { recursive: true });
    }
    
    // 上传题目数据文件
    async upload(readStream){
      const { ctx } = this;
      const timeStamp = moment().format('YYYYMMDDhhmmss');
      const fullName = readStream.filename;
      const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
      const fileName = matchFileName ? matchFileName[1]: fullName;
      const extName = matchFileName ? matchFileName[2]: 'ext';

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/file/${year}/${month}/${day}`;
      const dirUrl = `${app.config.file.url}/${year}/${month}/${day}`;
      const path = `${dirPath}/${timeStamp}_${fileName}.${extName}`;
      const url = `${dirUrl}/${timeStamp}_${fileName}.${extName}`;

      const exist = await fs.exists(dirPath);
      if (!exist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 记录URL
      const writeStream = fs.createWriteStream(path);

      // 记录转发
      await safePipe(readStream, writeStream);

      return url;
    }

    // 直接上传至共享文件
    async uploadSharedFile(readStream, hash, size, mainTransaction) {
      const { ctx } = this;
      const fullName = readStream.filename;
      const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
      const fileName = matchFileName ? matchFileName[1]: fullName;
      const extName = matchFileName ? matchFileName[2]: 'ext';

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      const path = `${dirPath}/${hash}.${extName}`;

      // 上层文件夹存不存在
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 文件存不存在
      const pathExist = await fs.exists(path);
      if (!pathExist) {
        // 记录URL
        const writeStream = fs.createWriteStream(path);
        // 记录转发
        await safePipe(readStream, writeStream);
      }

      // 连接主数据库
      const { mainModel } = app;
      const result = await mainModel.SharedFiles.findOne({
        where: { hash },
        attributes: ['ref_count'],
        transaction: mainTransaction
      });

      if(result){
        await mainModel.SharedFiles.update({
          ref_count: result['ref_count'] + 1,
        }, {
          where: {
            hash
          },
          transaction: mainTransaction
        });
      }else{
        await mainModel.SharedFiles.create({
          hash,
          filename: fileName,
          ext: extName,
          year,
          month,
          day,
          file_size: size,
          ref_count: 1,
          schoolSlug: this.ctx.schoolSlug
        }, {transaction: mainTransaction});
      }

      return path;
    }

    // 课程封面图片上传
    async courseImgUpload(readStream, hash, size, courseSlug){
      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
          const { ctx } = this;
          const fullName = readStream.filename;
          const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
          const fileName = matchFileName ? matchFileName[1]: fullName;
          const extName = matchFileName ? matchFileName[2]: 'ext';

          const result = await mainModel.SharedFiles.findOne({
            where: { hash, ext: extName },
            attributes: ['year', 'month', 'day', 'ref_count'],
            transaction
          });

          if(!result){
            // 获取年
            const year = moment().format('YYYY');
            const month = moment().format('MM');
            const day = moment().format('DD');

            // 设置路径
            const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
            const path = `${dirPath}/${hash}.${extName}`;
            // 图片原始目录是否存在，不存在则创建
            const dirPathExist = await fs.exists(dirPath);
            if (!dirPathExist) {
              await ctx.service.file.mkdirs(dirPath);
            }

            // 图片是否存在，不存在则创建
            const pathExist = await fs.exists(path);
            if (!pathExist) {
              // 记录URL
              const writeStream = fs.createWriteStream(path);
              // 记录转发
              await safePipe(readStream, writeStream);
            }
            // 映射路径
            const linkPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets`;
  
            const linkExist = await fs.exists(linkPath);
            if (!linkExist) {
              await ctx.service.file.mkdirs(linkPath);
            }
            await fs.chmod(linkPath, '777');
            // 映射目标路径是否存在
            const destExist = await fs.exists(`${linkPath}/course.png`);
            if(!destExist){
              // 映射目录
              try {
                await fs.symlink(path, `${linkPath}/course.png`);
              } catch(err) {
                console.log('err2', err)
              }
            }
            
            await mainModel.SharedFiles.create({
              hash,
              filename: fileName,
              ext: extName,
              year,
              month,
              day,
              file_size: size,
              ref_count: 1,
              schoolSlug: this.ctx.schoolSlug
            }, {transaction});
          }else{
            // 设置路径
            const dirPath = `${app.config.file.hashFile}/${result.year}/${result.month}/${result.day}`;
            const path = `${dirPath}/${hash}.${extName}`;

            // 映射路径
            const toPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets`;
            const toPathExist = await fs.exists(toPath);
            if (!toPathExist) {
              await ctx.service.file.mkdirs(toPath);
            }
            await fs.chmod(toPath, '777');
            // 映射目录是否存在
            const destPathExist = await fs.exists(`${toPath}/course.png`);
            if (!destPathExist) {
              try {
                await fs.symlink(path, `${toPath}/course.png`);
              } catch(err) {
                console.log('err1', err)
              }
            }
            
            await mainModel.SharedFiles.update({
              ref_count: result['ref_count'] + 1,
            }, {
              where: {
                hash
              },
              transaction
            });
          }

      }catch(e){
        await transaction.rollback();
        return e;
      }

      await transaction.commit();
      return false;
    }

    // 上传资源文件并添加数据记录
    async uploadHashFile(readStream, hash, size, courseSlug, fileType, isCover, resourceName){
      const { ctx } = this;
      const fullName = readStream.filename || resourceName;
      const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
      const fileName = matchFileName ? matchFileName[1]: fullName;
      const extName = matchFileName ? matchFileName[2]: 'ext';

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      const path = `${dirPath}/${hash}.${extName}`;

      // 上层文件夹存不存在
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 文件存不存在
      const pathExist = await fs.exists(path);
      if (!pathExist) {
        // 记录URL
        const writeStream = fs.createWriteStream(path);
        // 记录转发
        await safePipe(readStream, writeStream);
      } else {
        await sendToWormhole(readStream);
      }

      // 映射路径
      const linkPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${fileType}`;
      const linkPathExist = await fs.exists(linkPath);
      if (!linkPathExist) {
        await ctx.service.file.mkdirs(linkPath);
      }
      // 如果覆盖， 先删除原来文件
      if(isCover){
        const linkString = await fs.readlink(`${linkPath}/${fullName}`);
        // 找到对应hash
        const linkParts = linkString.split('/');
        const linkName = linkParts[linkParts.length - 1];
        const matchHashName = linkName.match(/(^[^\.]+)\.(.+)$/);
        const deleteHash = matchHashName[1];

        // 干掉文件
        await fsExtra.remove(`${linkPath}/${fullName}`);
        if(!deleteHash){
          return 1;
        }
        // 数据库存储hash
        const { mainModel } = app;
        // 启用事务
        const transaction = await mainModel.transaction({autocommit: false});
        try{

          const result = await mainModel.SharedFiles.findOne({
            where: { hash: deleteHash },
            attributes: ['ref_count'],
            transaction
          });

          await mainModel.SharedFiles.update({
            ref_count: result['ref_count'] + 1,
          }, {
            where: {
              hash: deleteHash
            },
            transaction
          });
        }catch(e){
          console.error(243, e.message);
          await transaction.rollback();
          return;
        }
  
        await transaction.commit();    
      }

      // 映射目标是否存在
      const destExist = await fs.exists(`${linkPath}/${fullName}`);
      if (!destExist) {
        await fs.symlink(path, `${linkPath}/${fullName}`);
      }
      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
        const result = await mainModel.SharedFiles.findOne({
          where: { hash },
          attributes: ['ref_count'],
          transaction
        });

        if(result){
          await mainModel.SharedFiles.update({
            ref_count: result['ref_count']+1,
          }, {
            where: {
              hash
            },
            transaction
          });
        }else{
          await mainModel.SharedFiles.create({
            hash,
            filename: fileName,
            ext: extName,
            year,
            month,
            day,
            file_size: size,
            ref_count: 1,
            schoolSlug: this.ctx.schoolSlug
          }, {transaction});
        }
        
      }catch(e){
        console.error(292, e.message);
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      return true;
    }


    // word图片粘贴转存
    async uploadWordImage(readStream, courseSlug, fileType){
      const { ctx } = this;
      const matchFileName = readStream.filename.match(/(^[^\.]+)\.(.+)$/);
      const extName = matchFileName ? matchFileName[2]: 'ext';

      // 写文件到 shared_files/tmp目录下
      const tempDir = `${app.config.file.hashFile}/tmp`;
      const tempDirExist = await fs.exists(tempDir);
      if (!tempDirExist) {
        await ctx.service.file.mkdirs(tempDir);
      }

      const tempPath = `${tempDir}/${md5(moment().format("YYYYMMDDHHMMSS"))}.${extName}`;
      const writeTempStream = fs.createWriteStream(tempPath);
      await safePipe(readStream, writeTempStream);
      // 获取文件 hash
      const hash = await readFileMd5(tempPath);
      // 获取文件size
      const fileSize = (await fs.stat(tempPath)).size.toString();

      // 文件名默认为 hash 避免重复
      const fullName = `${hash}.${extName}`;

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      const path = `${dirPath}/${fullName}`;

      // 上层文件夹存不存在
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 文件存不存在
      const pathExist = await fs.exists(path);
      if (!pathExist) {
        // 将文件从 tmp 移动到指定目录
        await fs.rename(tempPath, path);
      } else {
        // 将文件从tmp路径下删除
        await fs.unlink(tempPath);
      }
      
      // 映射路径
      const linkPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${fileType}`;
      const frontPath = `file/course/${courseSlug}/${fileType}/${fullName}`;
      
      const linkPathExist = await fs.exists(linkPath);
      if (!linkPathExist) {
        await ctx.service.file.mkdirs(linkPath);
      }

      // 映射目标是否存在
      const destExist = await fs.exists(`${linkPath}/${fullName}`);
      if (!destExist) {
        await fs.symlink(path, `${linkPath}/${fullName}`);
      }

      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
        const result = await mainModel.SharedFiles.findOne({
          where: { hash },
          attributes: ['ref_count'],
          transaction
        });

        if(result){
          await mainModel.SharedFiles.update({
            ref_count: result['ref_count']+1,
          }, {
            where: {
              hash
            },
            transaction
          });
        }else{
          await mainModel.SharedFiles.create({
            hash,
            filename: hash,
            ext: extName,
            year,
            month,
            day,
            file_size: fileSize,
            ref_count: 1,
            schoolSlug: this.ctx.schoolSlug
          }, {transaction});
        }
        
      }catch(e){
        console.error(292, e.message);
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      return frontPath;
    }

    // 和上边的方法不一样的地方在于转发地址
    async uploadHashFileBySection(readStream, hash, size, courseSlug, fileType, isCover, content, fileExt){
      const { ctx } = this;
      // 修改点1
      const fullName = md5(content) + '.' + fileExt;
      // end
      const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
      const fileName = matchFileName ? matchFileName[1]: fullName;
      const extName = matchFileName ? matchFileName[2]: 'ext';

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      // 修改点2， shared_files改成/file
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      // end
      const path = `${dirPath}/${hash}.${extName}`;

      // 上层文件夹存不存在
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 文件存不存在
      const pathExist = await fs.exists(path);
      if (!pathExist) {
        // 记录URL
        // const writeStream = fs.createWriteStream(path);
        // // 记录转发
        // await safePipe(readStream, writeStream);
        await fs.writeFile(path, readStream)
      } else {
        await sendToWormhole(readStream);
      }

      // 映射路径
      const linkPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${fileType}`;
      const linkPathExist = await fs.exists(linkPath);
      if (!linkPathExist) {
        await ctx.service.file.mkdirs(linkPath);
      }
      // 如果覆盖， 先删除原来文件
      if(parseInt(isCover, 10) === 1){
        const linkString = await fs.readlink(`${linkPath}/${fullName}`);
        // 找到对应hash
        const linkParts = linkString.split('/');
        const linkName = linkParts[linkParts.length - 1];
        const matchHashName = linkName.match(/(^[^\.]+)\.(.+)$/);
        const deleteHash = matchHashName[1];

        // 干掉文件
        await fsExtra.remove(`${linkPath}/${fullName}`);
        if(!deleteHash){
          return 1;
        }
        // 数据库存储hash
        const { mainModel } = app;
        // 启用事务
        const transaction = await mainModel.transaction({autocommit: false});
        try{

          const result = await mainModel.SharedFiles.findOne({
            where: { hash: deleteHash },
            attributes: ['ref_count'],
            transaction
          });

          await mainModel.SharedFiles.update({
            ref_count: result['ref_count'] + 1,
          }, {
            where: {
              hash: deleteHash
            },
            transaction
          });
        }catch(e){
          console.error(243, e.message);
          await transaction.rollback();
          return;
        }
  
        await transaction.commit();    
        return path;
      }
      // 映射目标是否存在
      const destExist = await fs.exists(`${linkPath}/${fullName}`);
      if (!destExist) {
        try {
          await fs.symlink(path, `${linkPath}/${fullName}`);
        } catch(e) {
          console.error('symlink', path, `${linkPath}/${fullName}`, e)
        }
      }
      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
        const result = await mainModel.SharedFiles.findOne({
          where: { hash },
          attributes: ['ref_count'],
          transaction
        });

        if(result){
          await mainModel.SharedFiles.update({
            ref_count: result['ref_count']+1,
          }, {
            where: {
              hash
            },
            transaction
          });
        }else{
          await mainModel.SharedFiles.create({
            hash,
            filename: fileName,
            ext: extName,
            year,
            month,
            day,
            file_size: size,
            ref_count: 1,
            schoolSlug: this.ctx.schoolSlug
          }, {transaction});
        }
        
      }catch(e){
        console.error(292, e.message);
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      return path;
    }

    // 上传判决文件并添加数据记录
    async uploadHashJudgeFile(content, fullName, hash, size, transaction){
      const { ctx } = this;

      // 获取文件名及后缀名
      const splitIndex = fullName.lastIndexOf('.');
      const fullNameLength = fullName.length;
      const extName = fullName.substring(splitIndex + 1, fullNameLength);
      const fileName = fullName.substring(0, splitIndex);

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      const path = `${dirPath}/${hash}.${extName}`;

      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 文件目录是否存在
      const pathExist = await fs.exists(path);
      if (!pathExist) {
        // 创建文件
        await fs.writeFile(path, content);
      }

      // 增加数据库记录
      const { mainModel } = app;
      const result = await mainModel.SharedFiles.findOne({
        where: { hash },
        attributes: ['ref_count'],
        transaction
      });

      if(result){
        await mainModel.SharedFiles.update({
          ref_count: result['ref_count']+1,
        }, {
          where: {
            hash
          },
          transaction
        });

      }else{
        await mainModel.SharedFiles.create({
          hash,
          filename: fileName,
          ext: extName,
          year,
          month,
          day,
          file_size: size,
          ref_count: 1,
          schoolSlug: this.ctx.schoolSlug
        }, {transaction});
      }

      return path;
    }

    // 映射hash文件
    async makeLinks(links){
      const { ctx } = this;
      const schoolDir = `${app.config.file.dir}/${ctx.schoolSlug}`;
      const dirCache = {};
      for(const link of links) {
        const { from, toRelativeDirPath, fullName } = link;

        const toDirPath = path.join(schoolDir, toRelativeDirPath);

        // 按需创建文件夹
        if(!dirCache[toDirPath]) {
          const exist = await fs.exists(toDirPath);
          if (!exist) {
            await this.mkdirs(toDirPath);
          } 

          dirCache[toDirPath] = true;
        }

        // 映射文件
        const toPath = path.join(toDirPath, fullName);
        
        // 如果目标文件已经存在会先删除
        const linkExist = await fs.exists(toPath);
        if(linkExist) {
          await fs.unlink(toPath);
        }

        // 链接文件
        try {
          await fs.symlink(from, toPath);
        } catch (e) {
          console.error('makeLinks', e);
        }
        
      }
    }

    // 增加指定Hash的引用计数
    async addSharedFilesRef(hashs, transactionMain) {
      if(0 === hashs.length) {
        return;
      }
      const questions = hashs.map(() => '?').join(',');
        
      // 主库
      const { mainModel } = app;

      // 减少次数
      return await mainModel.query(`UPDATE shared_files SET ref_count = shared_files.ref_count + 1 WHERE shared_files.hash IN (${questions})`,
      { replacements: hashs, transaction: transactionMain, type: QueryTypes.UPDATE });
    }

    // 删除掉指定Hash集合的引用计数
    async removeSharedFilesRef(hashs, transactionMain) {
      if(0 === hashs.length) {
        return;
      }
      const questions = hashs.map(() => '?').join(',');
      
      // 主库
      const { mainModel } = app;

      // 减少次数
      return await mainModel.query(`UPDATE shared_files SET ref_count = shared_files.ref_count - 1 WHERE shared_files.hash IN (${questions})`,
      { replacements: hashs, transaction: transactionMain, type: QueryTypes.UPDATE });
    }

    // 升级AI课程文件
    async updateAIFileContent(fileContent){
      // 升级版本
      fileContent.nbformat_minor = 5;
      if (!fileContent.metadata) {
        fileContent.metadata = {};
      }
      if (!fileContent.metadata.isProjectBook) {
        if (fileContent.isProjectBook) {
          fileContent.metadata.isProjectBook = fileContent.isProjectBook;
        } else {
          fileContent.metadata.isProjectBook = false;
        }
      }
      if (fileContent.isProjectBook) {
        delete fileContent.isProjectBook;
      }
      
      // 更新 kernelSetting
      const newKernelSetting = fileContent.metadata.kernelSetting && fileContent.metadata.kernelSetting.kernelMemoryLimit ?
      {
        internetAccess: true,
        kernelType: "server",
        kernelMemoryLimit: fileContent.metadata.kernelSetting && fileContent.metadata.kernelSetting.kernelMemoryLimit ? fileContent.metadata.kernelSetting.kernelMemoryLimit: 128,
        kernelRecursiveLevel: fileContent.metadata.kernelSetting && fileContent.metadata.kernelSetting.kernelRecursiveLevel ? fileContent.metadata.kernelSetting.kernelRecursiveLevel: 1000,
      } : {
        kernelType: "browser",
      };

      fileContent.metadata.kernelSetting = newKernelSetting;

      let codeIndex = 0;
      // 重写cell
      if (fileContent.cells && fileContent.cells.length) {
        for (const cell of fileContent.cells) {
          codeIndex += 1;
          if (!cell.metadata) {
            cell.metadata = {};
          }

          // UUID
          if (cell.UUID) {
            cell.metadata.UUID = cell.UUID;
            cell.id = cell.UUID;
            delete cell.UUID;
          } else if (!cell.metadata.UUID){
            cell.metadata.UUID = makeUUID(codeIndex);
            cell.id = makeUUID(codeIndex);
          }
          
          // openStatus 展开折叠状态
          if(cell.openStatus) {
            cell.metadata.openStatus = cell.openStatus;
            delete cell.openStatus;
          } else {
            cell.metadata.openStatus = true;
          }   

          // 是否前台学生可见
          cell.metadata.visibility = true;
          
          // 资源代码块处理
          if (cell.cell_type === 'code') {
            // 有type字段
            if(cell.type === 'resources') {
              cell.metadata.type = cell.type;
              delete cell.type;
              continue;
            }

            // 没有type字段，需要从source中解析
            const sourceText = cell.source && cell.source.length ? `${cell.source.join('')} ` : '';
            if (sourceText.match(/(?<=Video\()(.*?)(?=\))/g) || sourceText.match(/(?<=Image\()(.*?)(?=\))/g)) {
              cell.metadata.type = 'resources';
              delete cell.type;
              continue;
            }
          }

          /*
          微应用处理：
          1. 版本1：没有type字段，有source：只能从source中解析确定是否为微应用，重写metadata，并删除cell中的type字段
          2. 版本2：有type字段，有source：需要从source中解析数据，重写metadata，并删除cell中的type字段
          3. 版本3：有type字段，没有source：metadata已经重写。此时需要删除cell中的type字段
          4. 版本4：没有type字段，没有source：metadata已经重写。此时无需处理
          */
          if (cell.cell_type === 'code') {
            const sourceText = cell.source.join("");
            const microResult = parseMicroApp(sourceText);
            
            // 版本1|版本2: 全部从source中解析
            if (microResult) {
              const { type, fileName, sourceParams } = microResult;
              cell.metadata.fileName = fileName;
              cell.metadata.type = type;
              cell.metadata.config = sourceParams;
              cell.source = [];
              delete cell.type;
              continue;
            } else if (cell.type && cell.type !== 'resources') {
              // 版本3：有type字段，source为空，metadata已经重写。此时需要删除cell中的type字段
              delete cell.type;
              continue;
            }
          } 
        }
      }
      return fileContent;
    };

    // 上传课程文件
    async uploadChapterFile(readStream, courseSlug, chapterName, sectionName, sectionType, isCover, forceUpload, transaction = null){
      try{
        const { mainModel } = app;
        const { ctx } = this;

        const nameParts = readStream.filename;
        let extName = null;

        if(nameParts.match(/\.ipynb$/)){
          extName = 'ipynb';
        }

        if(nameParts.match(/\.xml$/)){
          extName = 'xml';
        }

        if(nameParts.match(/\.json$/)){
          extName = 'json';
        }

        if(nameParts.match(/\.sb3$/)){
          extName = 'sb3';
        }

        if(nameParts.match(/\.slide$/)){
          extName = 'slide';
        }

        if(nameParts.match(/\.code$/)){
          extName = 'code';
        }

        if (!extName) {
          throw new Error(`未识别的文件${nameParts}`);
        }

        let content = null;
        let files = [];
        if(sectionType == 'Scratch' && extName === 'sb3') {
          content = await getReadStreamBinaryContent(readStream);
        } else {
          content = await getReadStreamTextContent(readStream);

          const srcReg = /src=[\"]?([^\"]*)[\"]?/ig; 
          content = content.replace(srcReg,  (all, src) => {
            if (src && src.startsWith('data:image\/')) {
                //过滤data:URL
              let base64Data = src.replace(/^data:image\/\w+;base64,/, "");
              const splitBySemicolonArr = src.split(';')
              const fileExt = splitBySemicolonArr[0].slice(11);
              const dataBuffer = Buffer.from(base64Data, 'base64');
              files.push({file: dataBuffer, text: src, size: dataBuffer.length});
              const fullName = md5(src) + '.' + fileExt;
              return `src="../assets/${fullName}"`;
            }
            return all;
          });
        }

        // ipynb文件内容升级，与jupyter格式兼容
        if(nameParts.match(/\.ipynb$/)){
          const fileContent = await ctx.service.file.updateAIFileContent(JSON.parse(content));
          content = JSON.stringify(fileContent);
        }

        for(let i = 0; i < files.length; i += 1) {
          const fileItem = files[i];
          const hash = md5(fileItem.file);
          const splitBySemicolonArr = fileItem.text.split(';')
          const fileExt = splitBySemicolonArr[0].slice(11);
          const flag = await mainModel.SharedFiles.findOne({
            where: { hash },
            attributes: ['ref_count']
          });
          await ctx.service.file.uploadHashFileBySection(fileItem.file, hash, fileItem.size, courseSlug, 'assets', isCover ? isCover : (flag ? true : false), fileItem.text, fileExt);
        }
        
        // 设置路径
        const dirPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
        let path = `${dirPath}/${sectionName}.${extName}`;

        const exist = await fs.exists(dirPath);
        if (!exist) {
          await ctx.service.file.mkdirs(dirPath);
        }

        // 客观题结构升级
        if (sectionType === 'OI') {
          const { newFileContent: newContent, isNew } = await ctx.service.course.updateOISectionFile(content);

          // 历史结构需转换
          if (!isNew) {

            // 是否确认上传
            if (forceUpload !== 'true') {
              throw new Error('old version file');
            }
            
            content = newContent;

            // 清除学生运行记录
            const findCourse = await ctx.model.Course.findOne({
              where: {
                courseSlug
              },
              raw: true,
              transaction
            });

            if (findCourse) {
              const { id: courseID } = findCourse;
              const findSection = await ctx.model.Section.findOne({
                where: {
                  courseID,
                  chapterName,
                  sectionName
                },
                raw: true,
                transaction
              });
  
              if (findSection) {
                const { id: sectionID } = findSection;
    
                await ctx.model.SectionRecord.destroy({
                  where: {
                    sectionID
                  },
                  transaction
                });
              }
            }
          }
        }

        // upload the sb3 section file alone
        if (sectionType == 'Scratch' && extName === 'sb3'){
          // write the zip file
          const targetUnzipPath = `/data/tmp/${courseSlug}/${sectionName}`;
          const targetZipFile = `${targetUnzipPath}/${sectionName}.zip`;
          const dirExist = await fs.exists(targetUnzipPath);
          if (!dirExist) {
            await ctx.service.file.mkdirs(targetUnzipPath);
          }

          await fs.writeFile(targetZipFile, content);

          fs.access(targetZipFile, fs.constants.F_OK, async (err) => {
            if (err) {
              console.error(err, 'adm-zip unzip')
              throw new Error(err);
            } else {
              try {
                const zip = new AdmZip(targetZipFile);
                const zipEntries = zip.getEntries();
  
                // unzip files
                zip.extractAllTo(/*target path*/ targetUnzipPath, /*overwrite*/ true);
  
                zipEntries.forEach(async function (zipEntry) {
                  const file = zipEntry.getData().toString("utf8");
                  const fileName = zipEntry.entryName;
                  if (fileName === 'project.json') {
                    // get the project.json
                    // path = `${dirPath}/${sectionName}.json`;
                    await fs.writeFile(`${dirPath}/${sectionName}.json`, file);
                  } else {
                    // upload the assets files
                    const assetStream = fs.createReadStream(`${targetUnzipPath}/${fileName}`);
                    const hash = md5(file);
                    const size = getStringByteLength(file);
                    await ctx.service.file.uploadHashFile(assetStream, hash, size, courseSlug, 'assets', false, fileName);
                    // await ctx.service.file.uploadHashFileBySection(fileItem.file, hash, fileItem.size, courseSlug, 'assets', isCover ? isCover : (flag ? true : false), fileItem.text, fileExt);
                  }
                });
              } catch(e) {
                console.error(e, 'unzip')
              }
            }
          });

        } else {
          // 创建文件
          await fs.writeFile(path, content);
        }


      }catch(e){
        console.log(e, 'uploadChapterFile')
        throw new Error(e);
      }
    }

    // 删除掉FPSXML内的小文件引用计数
    async getFPSXMLSharedFileHashs(path) {
      // 加载XML文件获取Hash
      return await getFPSSharedFileHashList(path);
    }

    // 上传课程文件内容文本到文件，这种情况只可能是Judge
    async uploadChapterFPSXMLContent(content, fileName, courseSlug, chapterName, sectionName){
      try{
        const { ctx } = this;
        let extName = 'ipynb';
        if(fileName.match(/\.xml$/)){
          extName = 'xml';
        };

        // 设置路径
        const dirPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
        const path = `${dirPath}/${sectionName}.${extName}`;

        // 目录不存在创建目录
        const dirExist = await fs.exists(dirPath);
        if (!dirExist) {
          await ctx.service.file.mkdirs(dirPath);
        }

        // 文件存在，覆盖文件之前需要删除文件及其引用计数
        const fileExist = await fs.exists(path);
        if (fileExist) {
          // 数据库存储hash
          const { mainModel } = app;

          // 启用事务
          let transactionMain = null;

          try {
            // 获取Hash并释放
            const hashs = await this.getFPSXMLSharedFileHashs(path);
            transactionMain = await mainModel.transaction({autocommit: false});
            await this.removeSharedFilesRef(hashs, transactionMain);
            await transactionMain.commit();
          }
          catch(e) {
            // console.debug('uploadChapterFPSXMLContent', e.message);
            console.log('uploadChapterFPSXMLContent发生错误', e)
            if(transactionMain) {
              await transactionMain.rollback();
            }
            throw e;
          }
        }
        // 处理base64
        const files = [];
        const srcReg = /src=[\"]?([^\"]*)[\"]?/ig; 
        content = content.replace(srcReg,  (all, src) => {
          if (src && src.startsWith('data:image\/')) {
              //过滤data:URL
            let base64Data = src.replace(/^data:image\/\w+;base64,/, "");
            const splitBySemicolonArr = src.split(';')
            const fileExt = splitBySemicolonArr[0].slice(11);
            const dataBuffer = Buffer.from(base64Data, 'base64');
            files.push({file: dataBuffer, text: src, size: dataBuffer.length});
            const fullName = md5(src) + '.' + fileExt;
            return `src="../assets/${fullName}"`;
          }
          return all;
        });
        const { mainModel } = app;
        for(let i = 0; i < files.length; i += 1) {
          const fileItem = files[i];
          const hash = md5(fileItem.file);
          const splitBySemicolonArr = fileItem.text.split(';')
          const fileExt = splitBySemicolonArr[0].slice(11);
          const flag = await mainModel.SharedFiles.findOne({
            where: { hash },
            attributes: ['ref_count']
          });
          await ctx.service.file.uploadHashFileBySection(fileItem.file, hash, fileItem.size, courseSlug, 'assets', flag ? true : false, fileItem.text, fileExt);
        }

        // 创建文件
        await fs.writeFile(path, content);
      }catch(e){
        console.log('uploadChapterFPSXMLContent发生错误2', e)
      }
    }

    // 获取已经上传文件hash列表
    async getExistSharedFiles(hashs) {
      const { mainModel } = app;

      const result = await mainModel.SharedFiles.findAll({
        where: { hash: {[Op.in]: hashs} },
        attributes: ['hash', 'ext', 'year', 'month', 'day'],
        raw: true
      });

      const hashMap = {};
      for(let each of result){
        const { hash, ext, year, month, day } = each;

        // 设置路径
        const path = `${app.config.file.hashFile}/${year}/${month}/${day}/${hash}.${ext}`;
        hashMap[each.hash] = path;
      }

      return hashMap;
    }

    // 检查课程封面是否存在
    async checkCourseImgExit(courseSlug) {
      return await fs.exists(`${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets/course.png`);
    }

    async checkSchoolCourseImgExit(schoolSlug, courseSlug) {
      return await fs.exists(`${app.config.file.eliteCoursesDir}/${schoolSlug}/${courseSlug}/assets/course.png`);
    }

    // 删除课程封面
    async delCourseImg(courseSlug) {
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
        const linkString = await fs.readlink(`${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets/course.png`);
        // 找到对应hash
        const linkParts = linkString.split('/');
        const linkName = linkParts[linkParts.length - 1];
        const matchFileName = linkName.match(/(^[^\.]+)\.(.+)$/);
        const hash = matchFileName[1];
        // 减少次数
        await mainModel.query(`UPDATE shared_files SET ref_count = shared_files.ref_count - 1 WHERE shared_files.hash = ?`,
        { replacements: [hash], transaction, type: QueryTypes.UPDATE });
      }catch(e){
        console.log(e, 'delCourseImg')
        await transaction.rollback();
        return;
      }
      await transaction.commit();
      return await fsExtra.remove(`${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/assets/course.png`);
    }
  
    // 删除文件/目录 !!!请调用者负责检查用户是否有权限进行该操作，注意无法恢复
    async remove(filename) {
      // 读之前检查目录是否存在
      const ifExist = await fs.exists(filename);

      if (!ifExist) {
        throw new Error('无可删除数据')
      }
      
      // 检查当前文件是文件还是目录
      const fileInfo = await fs.stat(filename);
      // 获取文件信息错误，则视为无该文件
      if (!fileInfo) {
        throw new Error('无可删除数据文件')
      }

      // 判断是否为目录
      const ifDirectory = fileInfo.isDirectory();

      // 若为目录则递归获取目录文件
      if (ifDirectory) { 
        return await fs.rmdir(filename);
      }

      return await fs.unlink(filename);
    }

    async uploadXMLFile(path, fileName, fileContent) {
      const filepath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${path}`;
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }

    async uplodaStudentFile(path, fileName, fileContent) {
      const filepath = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${path}`;
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }

    // 批量上传资源文件并添加数据记录
    async bulkUploadHashFile({ hashPath, uploadHashFiles, courseSlug, fileType, isCover }){
      const { ctx } = this;
      
      // 映射文件夹
      const linkPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${fileType}`;
      const linkPathExist = await fs.exists(linkPath);
      if (!linkPathExist) {
        await ctx.service.file.mkdirs(linkPath);
      }

      // 获取年
      const year = moment().format('yyyy');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      
      // 上层文件夹存不存在
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 批量查询文件是否存在
      const hashlinks = [];
      for (const hashFile of uploadHashFiles) {
        const { fullName } = hashFile;
        const readStream = fs.createReadStream(`${hashPath}/${fullName}`);
                                
        // 获取文件哈希值
        const hash = await readFileMd5(`${hashPath}/${fullName}`) // stream

        // 获取文件size
        const size = (await fs.stat(`${hashPath}/${fullName}`)).size.toString();

        // 获取文件名
        const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
        const fileName = matchFileName ? matchFileName[1]: fullName;
        const ext = matchFileName ? matchFileName[2]: 'ext';

        hashlinks.push(hash);

        // 记录文件属性
        hashFile.size = size;
        hashFile.hash = hash;
        hashFile.fileName = fileName;
        hashFile.ext = ext;

        // 源路径
        const path = `${dirPath}/${hash}.${ext}`;

        // 文件存不存在
        const pathExist = await fs.exists(path);
        if (!pathExist) {
          // 记录URL
          const writeStream = fs.createWriteStream(path);
          // 记录转发
          await safePipe(readStream, writeStream);
        } else {
          await sendToWormhole(readStream);
        }
        
        // 映射文件
        const destExist = await fs.exists(`${linkPath}/${fullName}`);
        if (!destExist) {
          await fs.symlink(path, `${linkPath}/${fullName}`);
        }
      }

      // 如果覆盖， 先删除原来文件
      if(isCover){
        for (const hashFile of uploadHashFiles) {
          const { fullName } = hashFile;

          const linkString = await fs.readlink(`${linkPath}/${fullName}`);
          // 找到对应hash
          if (linkString) {
            const linkParts = linkString.split('/');
            const linkName = linkParts[linkParts.length - 1];
            const matchHashName = linkName.match(/(^[^\.]+)\.(.+)$/);
            const deleteHash = matchHashName[1];
    
            // 干掉文件
            await fsExtra.remove(`${linkPath}/${fullName}`);
            if(!deleteHash){
              return 1;
            }
          }
        }

        // 数据库存储hash
        const { mainModel } = app;
        // 启用事务
        const transaction = await mainModel.transaction({autocommit: false});
        try{
          await ctx.service.file.bulkReduceMainSharedFiles(uploadHashFiles, transaction);
        }catch(e){
          console.error('bulkUploadHashFile-isCover', e);
          await transaction.rollback();
          return;
        }
  
        await transaction.commit();    
      }

      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
        //批量创建或更新shared files记录
        await ctx.service.file.bulkUpdateMainSharedFiles(uploadHashFiles, transaction);
        
      }catch(e){
        console.error('bulkUploadHashFile-isCover-false', e.message);
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      return true;
    }

    // 批量上传判决数据文件并添加数据记录
    async bulkUploadHashJudgeFile(uploadHashFiles, transaction){
      const { ctx } = this;
      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;

      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 批量查询文件是否存在
      const pathList = [];
      for (const hashFile of uploadHashFiles) {
        const { content, hash, fileName: fullName } = hashFile;

        const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
        const fileName = matchFileName ? matchFileName[1]: fullName;
        const ext = matchFileName ? matchFileName[2]: 'ext';

        hashFile.fileName = fileName;
        hashFile.ext = ext;

        // 源路径
        const path = `${dirPath}/${hash}.${ext}`;
        pathList.push(path);
        
        // 文件目录是否存在
        const pathExist = await fs.exists(path);
        if (!pathExist) {
          // 创建文件
          await fs.writeFile(path, content);
        }
      }

      // 增加数据库记录
      await ctx.service.file.bulkUpdateMainSharedFiles(uploadHashFiles, transaction);

      return pathList;
    }

    // 批量增加文件引用计数，如有新增则新建记录
    async bulkUpdateMainSharedFiles(uploadHashFiles, transaction) {
      const { mainModel } = app;

      // 获取年
      const year = moment().format('yyyy');
      const month = moment().format('MM');
      const day = moment().format('DD');

      const hashlinks = uploadHashFiles.map(i => i.hash);
      const updateHashFiles = await mainModel.SharedFiles.findAll({
        where: { 
          hash: {
            [Op.in] : hashlinks
          }
        },
        raw: true,
        transaction
      });

      // 旧文件增加原有次数
      updateHashFiles.forEach(i => i.ref_count += 1);

      // 查找新文件
      const oldHasLinksList = updateHashFiles.map(i => i.hash);
      const newFiles = uploadHashFiles.filter(i => !oldHasLinksList.includes(i.hash));

      // 创建新文件写库数据
      if (newFiles && newFiles.length) {
        for (const newFile of newFiles) {
          updateHashFiles.push({ 
            hash: newFile.hash,
            filename: newFile.fileName,
            ext: newFile.ext,
            year, 
            month, 
            day,
            file_size: newFile.size, 
            ref_count: 1, 
            schoolSlug: this.ctx.schoolSlug
          });
        }
      }

      await mainModel.SharedFiles.bulkCreate(updateHashFiles, { 
        updateOnDuplicate: ['ref_count', 'updated_at'],
        transaction
      });
    }

    // 批量减少文件引用计数
    async bulkReduceMainSharedFiles(uploadHashFiles, transaction) {
      const { mainModel } = app;

      const hashlinks = uploadHashFiles.map(i => i.hash);
      const updateHashFiles = await mainModel.SharedFiles.findAll({
        where: { 
          hash: {
            [Op.in] : hashlinks
          }
        },
        raw: true,
        transaction
      });

      // 旧文件减少原有次数
      updateHashFiles.forEach(i => i.ref_count -= 1);

      await mainModel.SharedFiles.bulkCreate(updateHashFiles, { 
        updateOnDuplicate: ['ref_count', 'updated_at'],
        transaction
      });
    }

    // 上传微应用资源文件
    async uploadMicroAssets({ readStream, fileName, extName, hash, size, courseSlug, fileType, isCover }){
      const { ctx } = this;
      const fullName = `${fileName}.${extName}`;

      // 获取年
      const year = moment().format('YYYY');
      const month = moment().format('MM');
      const day = moment().format('DD');

      // 设置路径
      const dirPath = `${app.config.file.hashFile}/${year}/${month}/${day}`;
      const path = `${dirPath}/${hash}.${extName}`;

      // 上层文件夹存不存在
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 文件存不存在
      const pathExist = await fs.exists(path);
      if (!pathExist) {
        // 记录URL
        const writeStream = fs.createWriteStream(path);
        // 记录转发
        await safePipe(readStream, writeStream);
      } else {
        await sendToWormhole(readStream);
      }

      // 映射路径
      const linkPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${fileType}`;
      const linkPathExist = await fs.exists(linkPath);
      if (!linkPathExist) {
        await ctx.service.file.mkdirs(linkPath);
      }
      // 如果覆盖， 先删除原来文件
      if(isCover){
        const linkString = await fs.readlink(`${linkPath}/${fullName}`);
        // 找到对应hash
        const linkParts = linkString.split('/');
        const linkName = linkParts[linkParts.length - 1];
        const matchHashName = linkName.match(/(^[^\.]+)\.(.+)$/);
        const deleteHash = matchHashName[1];

        // 干掉文件
        await fsExtra.remove(`${linkPath}/${fullName}`);
        if(!deleteHash){
          return 1;
        }
        // 数据库存储hash
        const { mainModel } = app;
        // 启用事务
        const transaction = await mainModel.transaction({autocommit: false});
        try{

          const result = await mainModel.SharedFiles.findOne({
            where: { hash: deleteHash },
            attributes: ['ref_count'],
            transaction
          });

          await mainModel.SharedFiles.update({
            ref_count: result['ref_count'] + 1,
          }, {
            where: {
              hash: deleteHash
            },
            transaction
          });
        }catch(e){
          console.error(243, e.message);
          await transaction.rollback();
          return;
        }
  
        await transaction.commit();    
      }

      // 映射目标是否存在
      const destExist = await fs.exists(`${linkPath}/${fullName}`);
      if (!destExist) {
        await fs.symlink(path, `${linkPath}/${fullName}`);
      }
      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transaction = await mainModel.transaction({autocommit: false});
      try{
        const result = await mainModel.SharedFiles.findOne({
          where: { hash },
          attributes: ['ref_count'],
          transaction
        });

        if(result){
          await mainModel.SharedFiles.update({
            ref_count: result['ref_count']+1,
          }, {
            where: {
              hash
            },
            transaction
          });
        }else{
          await mainModel.SharedFiles.create({
            hash,
            filename: fileName,
            ext: extName,
            year,
            month,
            day,
            file_size: size,
            ref_count: 1,
            schoolSlug: this.ctx.schoolSlug
          }, {transaction});
        }
        
      }catch(e){
        console.error('uploadMicroAssets', e);
        await transaction.rollback();
        return;
      }

      await transaction.commit();
      return true;
    }
  }

  return File;
}