const { Op, QueryTypes } = require('sequelize');
const JsSHA = require('jssha');
const md5 = require('md5');
const { pagenation } = require('../utils/pagenation.js');
const moment = require('moment');

const configMap = {
  'trainPermission': {
    history: 'trainAdmin',
    hasUsed: 'trainPeopleUsed',
    permissionType: 'trainPermissionData',
    people: 'trainPeople',
  },
  'coursePermission': {
    history: 'coursePermission',
    hasUsed: 'coursePeopleUsed',
    permissionType: 'trainWebTime',
    people: 'coursePeople',
  },
}

module.exports = app => {
  const redis = app.redis.get('session');
  class PermissionPeopleService extends app.Service {
    // 初始化权限限定人数
    async refreshLimitPeopleWithDataBase(mainModel, transaction = false, mainTransaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // console.log('mainModel:',mainModel)

      // 查看运维平台是否有该校权限记录
      const schoolInhxr = await mainModel.School.findOne({
        where: {
          slug: ctx.schoolSlug
        },
        raw: true,
        transaction: mainTransaction,
      })
      // console.log('schoolInhxr:',schoolInhxr)
      const findDeployment = await mainModel.DeploymentPermission.findOne({
        where: { schoolID: schoolInhxr.id },
        raw: true,
        transaction: mainTransaction,
      })
      if (!findDeployment || !findDeployment.id) {
        await mainModel.DeploymentPermission.create({
          schoolID: schoolInhxr.id,
        }, {
          raw: true,
          transaction: mainTransaction,
        })
      }

      // 课程权限人数限制
      // 取所有目前课程内开放班级
      const allCourseInfos = await model.Course.findAll({
        raw: true,
        transaction,
      })
      let teamIDs = [];
      for(const allCourseInfo of allCourseInfos) {
        if (!allCourseInfo.teams) {
          continue;
        }
        teamIDs = teamIDs.concat(allCourseInfo.teams);
      }
      teamIDs = Array.from(new Set(teamIDs));

      // 根据班级查看人员
      // 按照班级id取人员
      const allUser = await model.TeamUser.findAll({
        where: {
          teamID: { [Op.in]: teamIDs }
        },
        raw: true,
        transaction
      })
      const userIDs = allUser.map(row => row.userID);
      const newHasUsed = Array.from(new Set(userIDs));

      // 记录人员id
      await model.SystemConfig.create({
        key: 'coursePermission',
        value: {
          hasUsed: newHasUsed,
          hasClassIDs: teamIDs,
        }
      }, {
        raw: true,
        transaction
      });

      // 训练授权
      // 取所有训练人员
      const allNeedPeople = await model.TrainThroughTrainPlan.findAll({
        group: ['userID'],
        raw: true,
        transaction
      })
      const trainUserIDs = allNeedPeople.map(row => row.userID);

      await model.SystemConfig.create({
        key: 'trainPermission',
        value: {
          hasUsed: trainUserIDs
        }
      }, {
        raw: true,
        transaction
      });

      // 上传至运维平台
      await mainModel.DeploymentPermission.update({
        coursePeopleUsed: newHasUsed,
        trainPeopleUsed: trainUserIDs,
      }, {
        where: { schoolID: schoolInhxr.id },
        raw: true,
        transaction: mainTransaction,
      })
    }
    
    // 按照学生记录、权限开始时间、权限结束时间取现在使用人员
    async refreshLimitPeople(ifClearUser, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // 取授权记录
      let configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      // 取课程、训练有效授权
      let courseRight = null;
      let trainRight = null;
      const { trainWebTime = [], trainPermissionData = [] } = configs.value;
      // 分析课程权限，取有效权限
      for(const row of trainWebTime) {
        if(!row.permission || row.permission.indexOf('课程') === -1) {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!courseRight || moment(courseRight.endTime).isBefore(row.endTime)) {
          courseRight = row;
        }
      }
      
      // 若没有有效授权，则跳过该流程
      if(courseRight) {
        // 取课程授权记录
        let courseConfigs = await model.SystemConfig.findOne({
          where: {
            key: 'coursePermission'
          },
          raw: true,
          transaction
        });

        let needChangeCoursePeople = false;
        // 没有记录则新增
        if (!courseConfigs || !courseConfigs.value) {
          courseConfigs = await model.SystemConfig.create({
            key: 'coursePermission',
            value: {
              ...courseRight,
              hasUsed: [],
              hasClassIDs: [],
            }
          }, {
            raw: true,
            transaction
          });

          needChangeCoursePeople = true;
        } else {
          // 判断课程有效授权是否变动过
          const { startTime, endTime, permission, people } = courseConfigs.value;
          if (startTime !== courseRight.startTime || endTime !== courseRight.endTime || permission !== courseRight.permission || people !== trainRight.people) {

            needChangeCoursePeople = true;
          }
        }

        if (needChangeCoursePeople && ifClearUser) {
          // 变动授权则清除所有 课程---课程信息---向指定班级开放
          // 取所有课程
          const allCourseDatas = await model.Course.findAll({
            raw: true,
            transaction
          })
          // 一个月内的变动，保留上次历史记录，否则历史记录重新记
          // const historyClass = configs && moment(configs.created_at).diff(moment().startOf('days'), 'day') > 31 ? [] : (configs && configs.value ? configs.value.historyClass : []);
          const historyClass = [];
          for(const row of allCourseDatas) {
            if (!row.teams || !row.teams.length) {
              continue;
            }

            historyClass.push({
              courseID: row.id,
              teams: row.teams,
              historyTeams: row.historyTeams,
            })

            const historyTeams = row.historyTeams ? row.historyTeams.concat(row.teams) : row.teams.concat([]);

            await model.Course.update({
              teams: [],
              historyTeams,
            }, {
              where: {id: row.id},
              raw: true,
              transaction
            })
          }

          // 只有在存在历史记录时记录
          if (historyClass && historyClass.length) {
            // 记录历史记录
            await model.PermissionLimitHistory.create({
              type: 'coursePermission',
              people: courseConfigs.value.people,
              hasUsed: courseConfigs.value.hasUsed,
              startTime: courseConfigs.value.startTime,
              endTime: courseConfigs.value.endTime,
              historyClass: historyClass,
              note: {
                user: {
                  id: ctx.session.user.id,
                  name: ctx.session.user.name,
                  ifSuperAdmin: ctx.session.user.ifSuperAdmin
                }
              }
            })
          }

          // 跟更新占用情况
          await model.SystemConfig.update({
            value: {
              ...courseRight,
              hasUsed: [],
              hasClassIDs: [],
            }
          }, {
            where: {
              key: 'coursePermission'
            },
            raw: true,
            transaction
          });
        }
        
      }

      // 分析在线训练权限，取有效权限
      for(const row of trainPermissionData) {
        if(row.target !== '在线训练' || row.status !== '启用') {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!trainRight || moment(trainRight.endTime).isBefore(row.endTime)) {
          trainRight = row;
        }
      }
      
      // 若没有有效授权，则跳过该流程
      if(trainRight) {
        // 取课程授权记录
        const trainConfigs = await model.SystemConfig.findOne({
          where: {
            key: 'trainPermission'
          },
          raw: true,
          transaction
        });
        
        // 没有记录则新增
        if (!trainConfigs || !trainConfigs.value) {
          // 变动授权则重新计算占用人员，新建时，被删除的用户，不占用
          const allNeedPeople = await model.TrainThroughTrainPlan.findAll({
            where: {
              created_at: { [Op.between]: [moment(trainRight.startTime), moment(trainRight.endTime)] }
            },
            group: ['userID'],
            raw: true,
            transaction
          })
          const userIDs = allNeedPeople.map(row => row.userID);

          await model.SystemConfig.create({
            key: 'trainPermission',
            value: {
              ...trainRight,
              hasUsed: userIDs
            }
          }, {
            raw: true,
            transaction
          });
        } else {
          // 判断训练有效授权是否变动过
          const { startTime, endTime, people } = trainConfigs.value;
          if (startTime !== trainRight.startTime || endTime !== trainRight.endTime || people !== trainRight.people) {
            // 变动授权则重新计算占用人员，修改时，被删除的用户，需占用
            const allNeedPeople = await model.query(
              `
                SELECT id, userID, trainID, planID, isolate, created_at, updated_at, deleted_at
                FROM train_through_train_plan AS train_through_train_plan
                WHERE train_through_train_plan.created_at BETWEEN ? AND ?
                GROUP BY userID
              `,
              { type: QueryTypes.SELECT, replacements: [moment(trainRight.startTime).startOf('day'), moment(trainRight.endTime).endOf('day')], transaction },
            )

            const userIDs = allNeedPeople.map(row => row.userID);

            // 记录历史记录
            // 训练不存在历史记录，时间变动后，人员核算自然变动，不影响任何使用s
            // await model.PermissionLimitHistory.create({
            //   type: 'trainPermission',
            //   people: trainConfigs.value.people,
            //   hasUsed: trainConfigs.value.hasUsed,
            //   startTime: trainConfigs.value.startTime,
            //   endTime: trainConfigs.value.endTime,
            //   // historyClass: historyClass,
            // })

            // 跟更新占用情况
            await model.SystemConfig.update({
              value: {
                ...trainRight,
                hasUsed: userIDs
              }
            }, {
              where: {
                key: 'trainPermission'
              },
              raw: true,
              transaction
            });
          }
        }
      }
    }

    // 清除课程人数占用记录
    async refreshCourseLimitPeople(transaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // console.log('transaction:',transaction)
      // 取授权记录
      let configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });
      // console.log('configs:',configs);

      // 取课程、训练有效授权
      let courseRight = null;
      const { trainWebTime = [] } = configs.value;
      // 分析课程权限，取有效权限
      for(const row of trainWebTime) {
        if(!row.permission || row.permission.indexOf('课程') === -1) {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!courseRight || moment(courseRight.endTime).isBefore(row.endTime)) {
          courseRight = row;
        }
      }
      
      // 若没有有效授权，则跳过该流程
      if(courseRight) {
        // 取课程授权记录
        let courseConfigs = await model.SystemConfig.findOne({
          where: {
            key: 'coursePermission'
          },
          raw: true,
          transaction
        });

        // 变动授权则清除所有 课程---课程信息---向指定班级开放
        // 取所有课程
        const allCourseDatas = await model.Course.findAll({
          raw: true,
          transaction
        })

        // 一个月内的变动，保留上次历史记录，否则历史记录重新记
        // const historyClass = configs && moment(configs.created_at).diff(moment().startOf('days'), 'day') > 31 ? [] : (configs && configs.value ? configs.value.historyClass : []);
        const historyClass = [];
        for(const row of allCourseDatas) {
          // console.log('row:', row)
          if (!row.teams || !row.teams.length) {
            continue;
          }
          row.teams = Array.from(new Set(row.teams));
          row.historyTeams = Array.from(new Set(row.historyTeams));

          historyClass.push({
            courseID: row.id,
            teams: row.teams,
            historyTeams: row.historyTeams,
          })

          let historyTeams = row.historyTeams ? row.historyTeams.concat(row.teams) : row.teams.concat([]);
          historyTeams = Array.from(new Set(historyTeams));

          await model.Course.update({
            teams: [],
            historyTeams,
          }, {
            where: {id: row.id},
            raw: true,
            transaction
          })
        }

        // 只有在存在历史记录时记录
        if (historyClass && historyClass.length) {
          // 记录历史记录
          await model.PermissionLimitHistory.create({
            type: 'coursePermission',
            people: courseConfigs.value.people,
            hasUsed: courseConfigs.value.hasUsed,
            startTime: courseConfigs.value.startTime,
            endTime: courseConfigs.value.endTime,
            historyClass: historyClass,
            note: {
              user: {
                id: ctx.session.user.id,
                name: ctx.session.user.name,
                ifSuperAdmin: ctx.session.user.ifSuperAdmin
              }
            }
          })
        }

        // 跟更新占用情况
        await model.SystemConfig.update({
          value: {
            ...courseRight,
            hasUsed: [],
            hasClassIDs: [],
          }
        }, {
          where: {
            key: 'coursePermission'
          },
          raw: true,
          transaction
        });
        
      }
    }
    // 按照学生记录、权限开始时间、权限结束时间取现在使用人员
    async refreshTrainLimitPeople(allClear, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // 取授权记录
      let configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      // 取课程、训练有效授权
      let trainRight = null;
      const { trainPermissionData = [] } = configs.value;

      // 分析在线训练权限，取有效权限
      for(const row of trainPermissionData) {
        if(row.target !== '在线训练' || row.status !== '启用') {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!trainRight || moment(trainRight.endTime).isBefore(row.endTime)) {
          trainRight = row;
        }
      }
      
      // 取课程授权记录
      const trainConfigs = await model.SystemConfig.findOne({
        where: {
          key: 'trainPermission'
        },
        raw: true,
        transaction
      });

      if (allClear) {
        // 跟更新占用情况
        await model.SystemConfig.update({
          value: {
            ...trainRight,
            hasUsed: []
          }
        }, {
          where: {
            key: 'trainPermission'
          },
          raw: true,
          transaction
        });
        return;
      }
      
      // 判断训练有效授权是否变动过
      const { startTime, endTime, people } = trainConfigs.value;
      if (startTime !== trainRight.startTime || endTime !== trainRight.endTime || people !== trainRight.people) {
        // 变动授权则重新计算占用人员，修改时，被删除的用户，需占用
        const allNeedPeople = await model.query(
          `
            SELECT id, userID, trainID, planID, isolate, created_at, updated_at, deleted_at
            FROM train_through_train_plan AS train_through_train_plan
            WHERE train_through_train_plan.created_at BETWEEN ? AND ?
            GROUP BY userID
          `,
          { type: QueryTypes.SELECT, replacements: [moment(trainRight.startTime).startOf('day'), moment(trainRight.endTime).endOf('day')], transaction },
        )

        const userIDs = allNeedPeople.map(row => row.userID);

        // 记录历史记录
        // 训练不存在历史记录，时间变动后，人员核算自然变动，不影响任何使用s
        // await model.PermissionLimitHistory.create({
        //   type: 'trainPermission',
        //   people: trainConfigs.value.people,
        //   hasUsed: trainConfigs.value.hasUsed,
        //   startTime: trainConfigs.value.startTime,
        //   endTime: trainConfigs.value.endTime,
        //   // historyClass: historyClass,
        // })

        // 跟更新占用情况
        await model.SystemConfig.update({
          value: {
            ...trainRight,
            hasUsed: userIDs
          }
        }, {
          where: {
            key: 'trainPermission'
          },
          raw: true,
          transaction
        });
      }
    }

		// 根据占用人员ID获取人员所在班级树
    async getLimitPeopleClass(userIDs, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // console.log('userIDs:',userIDs)

      // 取所有该id下的人员记录
      const allUser = await model.User.findAll({
        where: {
          id: {
            [Op.in]: userIDs,
          },
        },
        paranoid: false,
        raw: true,
        transaction,
      })

      // 按人员做索引
      const userMap = {};
      for(const row of allUser) {
        userMap[row.id] = row;
      }

      // 取所有该id下的记录
      const allTeamUser = await model.TeamUser.findAll({
        where: {
          userID: {
            [Op.in]: userIDs,
          },
        },
        paranoid: false,
        raw: true,
        transaction,
      });

      // const allUserInTeamIds = allTeamUser.map(row => row.userID);

      // 所有不在班级里的账号
      // const notInTeamUsers = allUser.filter(i => allUserInTeamIds.indexOf(i.id) === -1);

      // 按班级id做成索引
      const teamMap = {};
      for(const row of allTeamUser) {
        if (!teamMap[row.teamID]) {
          teamMap[row.teamID] = []
        }
        teamMap[row.teamID].push(userMap[row.userID])
      }
      const teamIDs = Object.keys(teamMap);
      const teamDatas = await model.Team.findAll({
        where: {
          id: {
            [Op.in]: teamIDs,
          },
        },
        raw: true,
        paranoid: false,
        transaction,
      });
      // console.log('teamDatas:',teamDatas)

      const teamList = teamDatas.map(row => ({
        ...row,
        user: teamMap[row.id]
      }));

      return teamList;
    }

    // 课程授权同步redis最新数据到数据库
    async refreshCoursePermission(schoolSlug, transaction = null, mainTransaction) {
      const { ctx } = this;
      const { model } = ctx;

      // 从redis中获取最新占用数据
      const users = await redis.hkeys(`${schoolSlug}_course_user_ids`);
      if (!users || !users.length) {
        return {
          hasUsed: [],
          hasClassIDs: [],
        }
      }

      const userIds = users.map(i => parseInt(i));

      const allTeams = await model.TeamUser.findAll({
        where: {
          userID: {
            [Op.in]: userIds,
          }
        },
        raw: true,
        transaction
      });

      const allTeamIds = allTeams.map(i => i.teamID);

      const newValue = {
        hasUsed: userIds,
        hasClassIDs: allTeamIds,
      }

      // 更新数据库的数据
      const key = 'coursePermission';
      await model.SystemConfig.update({
        key: key,
        value: newValue
      }, {
        where: {
          key: key
        },
        transaction
      });

      // 上传至运维平台
      const updateCode = {
        coursePeopleUsed: userIds,
      };

      await ctx.service.permissionPeople.updateMainPermission({ schoolSlug, updateCode }, mainTransaction);

      return newValue;
    }

    // 获取占用人员数量
    async getLimitPeople(key, transaction = null, mainTransaction = null) {
      const { ctx } = this;
      const { model } = ctx;
      const result = await ctx.service.systemConfig.getInfomationByKey(key);

      // 按照目前权限，取有效授权
      // 取授权记录
      let configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      if (!configs) {
        return result;
      }

      const { trainWebTime = [], trainPermissionData = [] } = configs.value;

      if (key === 'coursePermission') {
        let courseRight = null;
        // 分析课程权限，取有效权限
        for(const row of trainWebTime) {
          if(!row.permission || row.permission.indexOf('课程') === -1) {
            continue;
          }
  
          // 取有效权限，时间取最久一个
          if (!courseRight || moment(courseRight.endTime).isBefore(row.endTime)) {
            courseRight = row;
          }
        }

        // 同步redis最新数据到数据库
        const newValue = await ctx.service.permissionPeople.refreshCoursePermission(ctx.schoolSlug, transaction, mainTransaction);
        result.value = {
          ...result.value,
          ...courseRight,
          ...newValue,
        };

        return result;
      }

      // 分析在线训练权限，取有效权限
      let trainRight = null;
      for(const row of trainPermissionData) {
        if(row.target !== '在线训练' || row.status !== '启用') {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!trainRight || moment(trainRight.endTime).isBefore(row.endTime)) {
          trainRight = row;
        }
      }

      result.value = {
        ...result.value,
        ...trainRight
      }

      return result;
    }

		// 跟新占用人员
    async updateLimitPeople(userIDs, classIDs, key, transaction = false, mainModel, mainTransaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // console.log('userIDs:',userIDs)
      // 取原有占用人员
      const result = await ctx.service.systemConfig.getInfomationByKey(key, transaction);
      const localPermission = result.value;

      const newHasUsed = Array.from(new Set(userIDs.concat(localPermission.hasUsed)));

      // -1代表不限制人数
      // if (localPermission.people && localPermission.people > -1 && localPermission.people < newHasUsed.length) {
      //   throw new Error('您的授权限制人数已用尽')
      // }

      const updateUsedCode = { ...localPermission };
      updateUsedCode.hasUsed = newHasUsed;
      if (classIDs) {
        const localClass = updateUsedCode.hasClassIDs ? updateUsedCode.hasClassIDs : [];
        updateUsedCode.hasClassIDs = Array.from(new Set(localClass.concat(classIDs)))
      }

      await model.SystemConfig.update({ value: updateUsedCode }, {
        where: {
          key,
        },
        raw: true,
        transaction,
      })

      const updateCode = {};
      if (key === 'coursePermission') {
        updateCode.coursePeopleUsed = newHasUsed;
      } else {
        updateCode.trainPeopleUsed = newHasUsed;
      }

      // 上传至运维平台
      // 查看运维平台是否有该校权限记录
      await ctx.service.permissionPeople.updateMainPermission({ schoolSlug: ctx.schoolSlug, updateCode }, mainTransaction);
    }

    // 根据新班级id求人员id，并合并原有占用人员id
    async getLimisUserByClassID(classIDs, key, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;

      // 取原有占用人员
      const result = await ctx.service.systemConfig.getInfomationByKey(key, transaction);
      const localPermission = result.value;

      // 按照班级id取人员
      const allUser = await model.TeamUser.findAll({
        where: {
          teamID: { [Op.in]: classIDs }
        },
        raw: true,
        transaction
      })
      const userIDs = allUser.map(row => row.userID);
      const newHasUsed = Array.from(new Set(userIDs.concat(localPermission.hasUsed)));
      
      return newHasUsed
    }

    // 读取历史记录
    async getHistoryPermission(key, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      const whereRequest = {};
      if(key) {
        whereRequest.type =  key;
      }
      const result = await model.PermissionLimitHistory.findAll({
        where: whereRequest,
        raw: true,
        transaction,
        order: [['created_at', 'DESC']]
      });

      // 取牵扯到的班级和课程
      let classIDs = [];
      // let courseIDs = [];
      let userIDs = [];
      // let ifReplaceClass = [];
      for(const permissionRow of result) {
        userIDs = userIDs.concat(permissionRow.hasUsed);
        permissionRow.classIDs = [];
        // permissionRow.ifReplaceClass = [];
        if (permissionRow.historyClass && permissionRow.historyClass.length) {
          for(const row of permissionRow.historyClass) {
            if (row) {
              classIDs.push(row)
              permissionRow.classIDs.push(row)
            }
            // if (row.historyTeams) {
            //   classIDs = classIDs.concat(row.historyTeams)
            //   permissionRow.classIDs = permissionRow.classIDs.concat(row.historyTeams)
            // }
    
            // if (row.courseID) {
            //   courseIDs.push(row.courseID)
            // }
  
            // if (row.ifReplaceClass) {
            //   permissionRow.ifReplaceClass = permissionRow.ifReplaceClass.concat(row.ifReplaceClass)
            // }
          }
        }
          
        // permissionRow.classIDs = Array.from(new Set(permissionRow.classIDs));
      }
      classIDs = Array.from(new Set(classIDs));
      // courseIDs = Array.from(new Set(courseIDs));
      // userIDs = Array.from(new Set(userIDs));


      // const userInfos = await model.TeamUser.findAll({
      //   where: { userID: { [Op.in]: userIDs },  },
      //   raw: true,
      //   transaction,
      // })
      // 取班级信息
      const classInfos = await model.Team.findAll({
        where: { id: { [Op.in]: classIDs }},
        raw: true,
        transaction,
      })
      const classMap = {};
      for(const row of classInfos) {
        classMap[row.id] = row;
      }

      // 取课程信息
      // const courseInfos = await model.Course.findAll({
      //   where: { id: { [Op.in]: courseIDs.map(row => row.id) }},
      //   raw: true,
      //   transaction,
      // })
      // const courseMap = {};
      // for(const row of courseInfos) {
      //   courseMap[row.id] = row;
      // }

      // 合并
      for(const permissionRow of result) {
        if (permissionRow.historyClass && permissionRow.historyClass.length) {
          for(const row of permissionRow.historyClass) {
            if (row.teams) {
              row.teamInfos = row.teams.map(item => classMap[item])
            }
            // if (row.historyTeams) {
            //   row.historyTeamInfos = row.historyTeams.map(item => classMap[item])
            // }
    
            // if (row.courseID) {
            //   row.courseIDInfos = courseMap[row.courseID]
            // }
          }
        }

        permissionRow.classIDsInfo = permissionRow.classIDs.map(item => classMap[item]).filter(row => row);
      }

      return result
    }

    // 按照历史记录和班级记录，恢复记录
    async replaceWithHistory(historyID, classID, transaction = false, mainModel, mainTransaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // 取相应历史记录
      const historyInfo = await model.PermissionLimitHistory.findOne({
        where: { id: historyID },
        raw: true,
        transaction,
      });

      // 按照班级记录，恢复班级设置
      const { historyClass } = historyInfo;
      for(const row of historyClass) {
        if (!row.teams || row.teams.indexOf(classID) === -1) {
          continue;
        }

        row.ifReplaceClass = row.ifReplaceClass ? row.ifReplaceClass : [];
        row.ifReplaceClass.push(classID);
        row.ifReplaceClass = Array.from(new Set(row.ifReplaceClass));

        const courseInfo = await model.Course.findOne({
          where: {
            id: row.courseID,
          },
          raw: true,
          transaction,
        });

        let newTeams = courseInfo.teams ? courseInfo.teams : [];
        // console.log('newTeams:',newTeams)
        newTeams.push(classID)
        newTeams = Array.from(new Set(newTeams));
        // const newTeams = [classID];

        await model.Course.update({
          teams: newTeams,
          historyTeams: row.historyTeams.filter(row => row !== classID),
        }, {
          where: {
            id: row.courseID,
          },
          raw: true,
          transaction,
        })
      }

      // 按照班级获取人员
      const users = await model.TeamUser.findAll({
        where: { teamID: classID },
        raw: true,
        transaction,
      });
      const userIDs = users.map(row => row.userID);
      // console.log('userIDs:',userIDs)

      // 记录是否已经恢复过
      await model.PermissionLimitHistory.update(historyInfo, {
        where: { id: historyID },
        raw: true,
        transaction,
      });

      await ctx.service.permissionPeople.updateLimitPeople(userIDs, null, 'coursePermission', transaction, mainModel, mainTransaction)
    }

    // 按照历史记录记录，恢复记录
    async replaceOneHistory(historyID, transaction = false, mainModel, mainTransaction = false) {
      const { ctx } = this;
      const { model } = ctx;
      // 取相应历史记录
      const historyInfo = await model.PermissionLimitHistory.findOne({
        where: { id: historyID },
        raw: true,
        transaction,
      });

      let classIDs = [];

      // 按照班级记录，恢复班级设置
      const { historyClass } = historyInfo;
      for(const row of historyClass) {
        const courseInfo = await model.Course.findOne({
          where: {
            id: row.courseID,
          },
          raw: true,
          transaction,
        });
        classIDs = classIDs.concat(row.teams);

        const newTeams = courseInfo.teams ? (courseInfo.teams.concat(row.teams ? row.teams : [])) : row.teams;

        row.ifReplaceClass = row.ifReplaceClass ? row.ifReplaceClass : [];
        row.ifReplaceClass = row.ifReplaceClass.concat(row.teams ? row.teams : []);
        row.ifReplaceClass = Array.from(new Set(row.ifReplaceClass));

        await model.Course.update({
          teams: newTeams,
          historyTeams: row.historyTeams,
        }, {
          where: {
            id: row.courseID,
          },
          raw: true,
          transaction,
        })
      }

      classIDs = Array.from(new Set(classIDs));

      // 按照班级获取人员
      const users = await model.TeamUser.findAll({
        where: { teamID: { [Op.in]: classIDs } },
        raw: true,
        transaction,
      });
      const userIDs = users.map(row => row.userID);

      // 记录是否已经恢复过
      await model.PermissionLimitHistory.update(historyInfo, {
        where: { id: historyID },
        raw: true,
        transaction,
      });

      await ctx.service.permissionPeople.updateLimitPeople(userIDs, null, 'coursePermission', transaction, mainModel, mainTransaction)
    }

    async updateMainPermission({ schoolSlug, updateCode }, mainTransaction) {
      const { mainModel } = app;

      // 查看运维平台是否有该校权限记录
      const schoolInhxr = await mainModel.School.findOne({
        where: {
          slug: schoolSlug
        },
        raw: true,
        transaction: mainTransaction,
      })

      if (!schoolInhxr) {
        console.error(`updateMainPermission 没有找到${schoolSlug}的学校记录`);
        return;
      }

      const findDeployment = await mainModel.DeploymentPermission.findOne({
        where: { schoolID: schoolInhxr.id },
        raw: true,
        transaction: mainTransaction,
      })

      if (!findDeployment || !findDeployment.id) {
        await mainModel.DeploymentPermission.create({
          schoolID: schoolInhxr.id,
        }, {
          raw: true,
          transaction: mainTransaction,
        })
      }

      await mainModel.DeploymentPermission.update(updateCode, {
        where: { schoolID: schoolInhxr.id },
        raw: true,
        transaction: mainTransaction,
      })
    }

    // 超管按照班级批量进行清理训练计划占用
    async clearTrainUserByBulkClass({ classIDs, ifClearOtherClassUser = false, configKey, note }, transaction = false, mainTransaction = false) {
      const { ctx } = this;
      const { model } = ctx;

      let configType = configKey ? configKey : 'trainPermission';

      const configInfo = configMap[configType];

      if (!configInfo) {
        throw new Error(`配置类型错误 ${configType}`);
      }

      // 取授权记录
      let configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      if (!configs || !configs.value) {
        throw new Error('现在没有有效授权！');
      }

      const selectPermissionData = configs.value[configInfo.permissionType] || [];

      // 取课程、训练有效授权
      let targetPermission = null;

      // 分析在线训练权限，取有效权限
      for(const row of selectPermissionData) {
        if(configType === 'trainPermission' && (row.target !== '在线训练' || row.status !== '启用')) {
          continue;
        }

        if (configType === 'coursePermission' && (!row.permission || row.permission.indexOf('课程') === -1)) {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!targetPermission || moment(targetPermission.endTime).isBefore(row.endTime)) {
          targetPermission = row;
        }
      }

      if (!targetPermission) {
        throw new Error('现在没有有效授权！');
      }

      // 根据班级ID获取人员
      const users = await model.TeamUser.findAll({
        where: { teamID: { [Op.in]: classIDs } },
        raw: true,
        transaction,
      })

      // 取原有占用人员
      const result = await ctx.service.systemConfig.getInfomationByKey(configType, transaction);
      const localPermission = result.value;

      let userID = users.map(row => row.userID);  // 待删除用户id
      // 若需要清除其他班级的该学生,则不做筛选操作
      // 若不需要清除其他班级的该学生,则筛选出不再其他班级的学生
      if (!ifClearOtherClassUser) {
        const teamRequest = [
          { [Op.notIn]: classIDs },
        ]

        if (localPermission.hasClassIDs && localPermission.hasClassIDs.length) {
          teamRequest.push({ [Op.in]: localPermission.hasClassIDs })
        }

        const allNotNeedStudent = await model.TeamUser.findAll({
          where: { teamID: {
            [Op.and]: teamRequest
          }, userID: { [Op.in]: userID } },
          raw: true,
          transaction,
        });
        const notUserID = allNotNeedStudent.map(row => row.userID);
        const notUserSet = new Set(notUserID);

        userID = userID.filter(row => !notUserSet.has(row));
      }

      const newUserSet = new Set(userID);
      userID = Array.from(newUserSet);

      const hasUsed = localPermission.hasUsed.filter(row => !newUserSet.has(row));

      const classIDsSet = new Set(classIDs);
      const hasClassIDs = localPermission.hasClassIDs.filter(row => !classIDsSet.has(parseInt(row)));

      const newPermissionCode = {
        hasUsed,
        hasClassIDs,
      }

      // 记录修改之后的占用
      await model.SystemConfig.update({ value: newPermissionCode }, {
        where: {
          key: configType,
        },
        raw: true,
        transaction,
      })

      // 记录历史记录
      await model.PermissionLimitHistory.create({
        type: configInfo.history,
        people: targetPermission[configInfo.people],
        hasUsed: userID,
        startTime: targetPermission.startTime,
        endTime: targetPermission.endTime,
        historyClass: classIDs,
        note: {
          note,
          user: {
            id: ctx.session.user.id,
            name: ctx.session.user.name,
            ifSuperAdmin: ctx.session.user.ifSuperAdmin
          },
          originCount: localPermission.hasUsed.length,
          nowCount: newPermissionCode.hasUsed.length,
        }
      })

      // 上传至运维平台
      const updateCode = {};
      updateCode[configInfo.hasUsed] = hasUsed;

      await ctx.service.permissionPeople.updateMainPermission({ schoolSlug: ctx.schoolSlug, updateCode }, mainTransaction);

      // 如果是coursePermission，还需要更新redis中记录的用户登录id集合
      if (configType === 'coursePermission') {
        const key = `${ctx.schoolSlug}_course_user_ids`;
        const loginKey = `${ctx.schoolSlug}_login_user_ids`;
        const delSessionIDs = [];

        for (const delUserId of userID) {
          const sessionIDs = await redis.hget(key, delUserId);
          if (sessionIDs) {
            const sessions = sessionIDs.split(',');
            for (const session of sessions) {
              if (delSessionIDs.indexOf(session) === -1) {
                delSessionIDs.push(session);
              }
            }
          }

          await redis.hdel(key, delUserId);
          await redis.hdel(loginKey, delUserId);
        }

        // 需同时删除这些用户的session
        for (const session of delSessionIDs) {
          await redis.del(session);
        }
      }
      
      return {
        newHasUsed: newPermissionCode.hasUsed,
        classIDs: newPermissionCode.hasClassIDs,
      }
    }

     // 超管清除所有占用
     async clearAllOccupations({ configKey }, transaction = false, mainTransaction = false) {
      const { ctx } = this;
      const { model } = ctx;

      let configType = configKey ? configKey : 'trainPermission';

      const configInfo = configMap[configType];

      if (!configInfo) {
        throw new Error(`配置类型错误 ${configType}`);
      }

      // 取授权记录
      let configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      if (!configs || !configs.value) {
        throw new Error('现在没有有效授权！');
      }

      const selectPermissionData = configs.value[configInfo.permissionType] || [];

      // 取课程、训练有效授权
      let targetPermission = null;

      // 分析在线训练权限，取有效权限
      for(const row of selectPermissionData) {
        if(configType === 'trainPermission' && (row.target !== '在线训练' || row.status !== '启用')) {
          continue;
        }

        if (configType === 'coursePermission' && (!row.permission || row.permission.indexOf('课程') === -1)) {
          continue;
        }

        // 取有效权限，时间取最久一个
        if (!targetPermission || moment(targetPermission.endTime).isBefore(row.endTime)) {
          targetPermission = row;
        }
      }

      if (!targetPermission) {
        throw new Error('现在没有有效授权！');
      }

      // 取原有占用人员
      const result = await ctx.service.systemConfig.getInfomationByKey(configType, transaction);
      const localPermission = result.value;
      const newPermissionCode = {
        hasUsed: [],
        hasClassIDs: [],
      }

      // 记录修改之后的占用
      await model.SystemConfig.update({ value: newPermissionCode }, {
        where: {
          key: configType,
        },
        raw: true,
        transaction,
      })

      // 记录历史记录
      await model.PermissionLimitHistory.create({
        type: configInfo.history,
        people: targetPermission[configInfo.people],
        hasUsed: [],
        startTime: targetPermission.startTime,
        endTime: targetPermission.endTime,
        historyClass: [],
        note: {
          user: {
            id: ctx.session.user.id,
            name: ctx.session.user.name,
            ifSuperAdmin: ctx.session.user.ifSuperAdmin
          },
          originCount: localPermission.hasUsed.length,
          nowCount: newPermissionCode.hasUsed.length,
        }
      })

      // 上传至运维平台
      const updateCode = {};
      updateCode[configInfo.hasUsed] = [];

      await ctx.service.permissionPeople.updateMainPermission({ schoolSlug: ctx.schoolSlug, updateCode }, mainTransaction);

      // 所有人员
      const users = await model.TeamUser.findAll({
        raw: true,
        transaction,
      })
      let userID = users.map(row => row.userID);
      // 如果是coursePermission，还需要更新redis中记录的用户登录id集合
      if (configType === 'coursePermission') {
        const key = `${ctx.schoolSlug}_course_user_ids`;
        const loginKey = `${ctx.schoolSlug}_login_user_ids`;
        const delSessionIDs = [];

        for (const delUserId of userID) {
          const sessionIDs = await redis.hget(key, delUserId);
          if (sessionIDs) {
            const sessions = sessionIDs.split(',');
            for (const session of sessions) {
              if (delSessionIDs.indexOf(session) === -1) {
                delSessionIDs.push(session);
              }
            }
          }

          await redis.hdel(key, delUserId);
          await redis.hdel(loginKey, delUserId);
        }

        // 需同时删除这些用户的session
        for (const session of delSessionIDs) {
          await redis.del(session);
        }
      }

      //清除该学校所有redis缓存
      await redis.del(`${ctx.schoolSlug}_course_user_ids`);

      return {
        newHasUsed: [],
        classIDs: [],
      }
    }
  }
  return PermissionPeopleService;
}