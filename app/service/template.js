const { Op } = require('sequelize');

module.exports = app => {

  class TemplateService extends app.Service {
    // 创建训练
    async createTemplate(node, transaction) {
      const { model } = this.ctx;
      const { Template } = model;
      
      // 名称是否存在
      const existName = await Template.findOne({
        where: {
          name: node.name
        },
        raw: true,
      })
      if(existName){
        throw new Error(`${ node.name } 已存在！`);
      }
      const result = await Template.create(node, { transaction });

      return result;
    }

    // 修改训练
    async putTemplate(id, node, transaction) {
      const { model } = this.ctx;
      const { Template } = model;

      // 名称是否存在
      const existName = await Template.findOne({
        where: {
          name: node.name,
          id: {
            [Op.not]: id,
          }
        },
        raw: true,
      })
      if(existName){
        throw new Error(`名称${node.name}已存在！`);
      }

      await Template.update(node, {
        where: {
          id,
        },
        transaction
      });

      return await Template.findOne({
        where: {
          id
        },
        include: [{
          model: model.User,
          as: 'createUser',
          attributes: ['id', 'name', 'avatar'],
        }],
        transaction
      });
    }

    // 获取训练
    async getTemplate(id, transaction = false) {
      const { model } = this.ctx;
      const { Template } = model

      const result = await Template.findOne({
        where: {
          id
        },
        include: [{
          model: model.User,
          as: 'createUser',
          attributes: ['id', 'name', 'avatar'],
        }],
        transaction
      });
      return result
    }

    // 删除训练
    async destoryTemplate(id, transaction = false) {
      const { model } = this.ctx;
      const { Template } = model

      return await Template.destroy({
        where: {
          id
        },
        transaction
      });
    }

    // 获取训练列表
    async getTemplateList() {
      const { model } = this.ctx;
      const { Template } = model;

      //获取对应值
      const list = await Template.findAll({
        include: [{
          model: model.User,
          as: 'createUser',
          attributes: ['id', 'name', 'avatar'],
        }],
      });

      return list;
    }
  }

  return TemplateService;
}