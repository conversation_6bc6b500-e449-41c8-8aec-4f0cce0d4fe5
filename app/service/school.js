
module.exports = app => {
    class SchoolService extends app.Service {
      // 获取学校数据库配置
      async getSchoolDatabaseConfig(slug) {
        const { mainModel } = app;
        const { School } = mainModel;

        const school = await School.findOne({
            where: {
              slug,
              auditStatus: '审核通过',
              ifService: 1,
            },
            attributes: ['dbConfig'],
            raw: true
        });

        return school ? school.dbConfig: null;
      }

      // 获取学校数据库配置
      async getAllSchoolDatabaseConfig() {
        const { mainModel } = app;
        const { School } = mainModel;

        const school = await School.findAll({
            where: {
              // slug,
              auditStatus: '审核通过',
              ifService: 1,
            },
            attributes: ['slug'],
            raw: true
        });

        return school;
      }
    }

    return SchoolService;
}