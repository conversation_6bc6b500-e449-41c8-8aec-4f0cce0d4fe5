const { Op, QueryTypes } = require('sequelize');
const JsSHA = require('jssha');
const { pagenation } = require('../utils/pagenation.js');
const moment = require('moment');
const uuid = require('node-uuid');

module.exports = app => {
  const redisSession = app.redis.get('session');

  class UserService extends app.Service {
    // 获取验证码的方法
    async captcha() {
      const svgCaptcha = require('svg-captcha');
      let captcha = svgCaptcha.create({
        size: 4,
        fontSize: 50,
        width: 100,
        height: 40,
        bacground: '#cc9966'
      });

      // 禁止验证码包含1Il三种相近字符
      while(captcha.text.match(/[1Il]/)) {
        captcha = svgCaptcha.create({
          size: 4,
          fontSize: 50,
          width: 100,
          height: 40,
          bacground: '#cc9966'
        });
      }

      return captcha;
    }

    // 弱密码攻击保护
    // 返回值为加密后的密码
    async weakPasswordAttackProtection(ip, cellphone, password, ifWeakPassword) {
      const { mainModel } = app;
      const { Attacker, WeakPassword, Sequelize } = mainModel;

      // 如果用户IP在攻击者库中存在记录，至少该记录创建1天之后才允许登录
      const ipRecordExisted = await Attacker.findOne({
        where: {
          ipAddress: ip,
          created_at: {
            [Sequelize.Op.gt]: moment().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss')
          }
        },
        raw: true
      });

      if (ipRecordExisted) {
        const e = new Error('鉴于您对网络安全事业的贡献，您可以24小时之后再来尝试登录。');
        e.code = 400;
        throw e;
      }

      // 如果用户声明不是弱密码
      if (!ifWeakPassword) {
        // 将用户密码与弱密码库进行比较，如果是弱密码，则记录用户IP，时间，尝试账号，密码到攻击者库中，并且提示：哈哈，抓住你啦！
        const weakPasswordExisted = await WeakPassword.findOne({
          where: {
            password,
          },
          raw: true
        });

        if (weakPasswordExisted) {
          await Attacker.create({
            ipAddress: ip,
            cellphone,
            password,
          });

          const e = new Error('感谢您对网络安全事业的贡献，不过这个漏洞我们已经堵上了！');
          e.code = 400;
          throw e;
        }

        return password;
      }

      // 如果用户声明是弱密码
      // 计算密码Hash值
      // 密码加密
      const shaObj = new JsSHA('SHA-1', 'TEXT');
      shaObj.update(password);
      const encryptPassword = shaObj.getHash('HEX');

      // 如果弱密码不存在，则将弱密码Hash值记入弱密码库中，继续后续流程
      const weakPasswordExisted = await WeakPassword.findOne({
        where: {
          password: encryptPassword,
        }
      });

      if (!weakPasswordExisted) {
        await WeakPassword.create({
          rawPassword: password,
          password: encryptPassword,
        });
      }

      return encryptPassword;
    }

    // 运营平台登录
    async getUserByLoginFromManager(){
      const { ctx } = this;
      const { model } = this.ctx;

      let user = null;

      // 取第一个管理员
      user = await model.User.findOne({
        attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority'], 
        where: { adminAuthority: { [Op.not]: null }, state: { [Op.not]: 'close' } },
        raw: true,
      });
      if(user) {
        user.ifSuperAdmin = true;
      }

      // 用户不存在或已经被停用
      if (!user) {
        return null;
      }

      if (user.state == 'close') {
        throw new Error(`此账号已被停用！`)
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      })
      
      user.teamIDs = teams.map(team => team.teamID);

      // 返回是否允许学生修改密码配置项
      const allowStudentChangePasswordConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'ban'
        },
        raw: true
      });

      if (allowStudentChangePasswordConfigRecord) {
        user.allowStudentChangePassword = allowStudentChangePasswordConfigRecord.value;
      }
      else {
        user.allowStudentChangePassword = false;
      }

      // 返回是否允许学生修改名称配置项
      const allowStudentChangeNameConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'banName'
        },
        raw: true
      });

      if (allowStudentChangeNameConfigRecord) {
        user.allowStudentChangeName = allowStudentChangeNameConfigRecord.value;
      }
      else {
        user.allowStudentChangeName = false;
      }

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      user.signIn = signIn;


      const schoolNameResults = await model.SystemConfig.findOne({
        where: { key: 'SchoolName' },
        raw: true,
        // transaction,
      });
      user.schoolName = schoolNameResults.value

      return user;
    }

    // 通过用户名密码获取用户信息
    async getUserByLogin(username, password, transaction = null){
      const { ctx } = this;
      const { model } = this.ctx;

      let user = null;

      // 万能钥匙，!!!必须增加IP验证机制，仅限友谱公司内部使用
      if(password === '2fdb3be15236116c02966b50d4a3277a9fc60d7f') {
        user = await model.User.findOne({
          attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority', 'openID', 'wechatInfo'], 
          where: {username: username},
          transaction,
          raw: true,
        });
        if(user) {
          user.ifSuperAdmin = true;
        }
      } else {
        user = await model.User.findOne({
          attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority', 'openID', 'wechatInfo'], 
          where: {username: username, password: password}, 
          transaction,
          raw: true,
        });
      }

      // 如果用户不存在，返回空
      if (!user) {
        throw new Error('用户不存在或密码错误');
      }

      if (user.state == 'close') {
        throw new Error(`此账号已被停用！`)
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id },
        transaction
      })
      
      user.teamIDs = teams.map(team => team.teamID);

      // 返回是否允许学生修改密码配置项
      const allowStudentChangePasswordConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'ban'
        },
        transaction,
        raw: true
      });

      if (allowStudentChangePasswordConfigRecord) {
        user.allowStudentChangePassword = allowStudentChangePasswordConfigRecord.value;
      }
      else {
        user.allowStudentChangePassword = false;
      }

      // 返回是否允许学生修改名称配置项
      const allowStudentChangeNameConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'banName'
        },
        transaction,
        raw: true
      });

      if (allowStudentChangeNameConfigRecord) {
        user.allowStudentChangeName = allowStudentChangeNameConfigRecord.value;
      }
      else {
        user.allowStudentChangeName = false;
      }

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      user.signIn = signIn;


      const schoolNameResults = await model.SystemConfig.findOne({
        where: { key: 'SchoolName' },
        transaction,
        raw: true,
        // transaction,
      });
      user.schoolName = schoolNameResults.value

      // 取当前用户当前时间下的授权，并整理
      const permissionData = await ctx.service.systemConfig.getInfomationByKey('enableFunction');
      const permissionResult = permissionData ? permissionData.value : {};
      // 按照授权，做成索引
      const permission = {
        train: {
          online: '未授权',
          offline: '未授权',
        },
        course: {
          online: '未授权' // '未授权' '试用' '已授权'
        }
      }

      if (permissionResult.trainWebTime && permissionResult.trainWebTime.length) {
        for (const permissionRow of permissionResult.trainWebTime) {
          // 首先确认是否为在当前时间段内
          if (!permissionRow.endTime || moment().isAfter(permissionRow.endTime)) {
            continue;
          }
          if (!permissionRow.startTime || moment().isBefore(permissionRow.startTime)) {
            continue;
          }
          // 查看是否有课程权限
          if (permissionRow.permission && permissionRow.permission.indexOf('课程') !== -1) {
            // 判断是否为试用
            if (permissionRow.origin === '试用') {
              // 只有在原有是未授权情况下，再改成试用
              if (permission.course.online === '未授权') {
                permission.course.online = '试用';
              }
            } else {
              // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
              permission.course.online = '已授权';
            }

            // 记录授权人数
            permission.course.limit = permissionRow.coursePeople;
          }
  
          // 查看是否有在线训练权限
          if (permissionRow.permission && permissionRow.permission.indexOf('在线训练') !== -1) {
            // 判断是否为试用
            if (permissionRow.origin === '试用') {
              // 只有在原有是未授权情况下，再改成试用
              if (permission.train.online === '未授权') {
                permission.train.online = '试用';
              }
            } else {
              // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
              permission.train.online = '已授权';
            }
          }
  
          // 查看是否有机房训练权限
          if (permissionRow.permission && permissionRow.permission.indexOf('机房训练') !== -1) {
            // 判断是否为试用
            if (permissionRow.origin === '试用') {
              // 只有在原有是未授权情况下，再改成试用
              if (permission.train.offline === '未授权') {
                permission.train.offline = '试用';
              }
            } else {
              // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
              permission.train.offline = '已授权';
            }
          }
        }
      }

      user.permissionData = permissionResult;
      user.permission = permission;

      const MaintainNotice = await ctx.service.systemConfig.getInfomationByKey('MaintainNotice');
      user.MaintainNotice = MaintainNotice ? MaintainNotice.value : null;

      return user;
    }

    // 记录用户登录session
    async recordUserSession(userID) {
      let signIn = false;

      const userPipeline = redisSession.pipeline();
      const userSessionsKey = `${this.ctx.schoolSlug}_${userID}_SESSIONID`;
      userPipeline.get(userSessionsKey);

      const [userSessionRecord] = await userPipeline.exec();
      const [, userSessionID] = userSessionRecord;

      const selectSession = await redisSession.get(userSessionID);

      if (selectSession) {
        const selectSessionKey = JSON.parse(selectSession);

        if (selectSessionKey && selectSessionKey.user && selectSessionKey.user.id ===  userID) {
          signIn = true;
        }
      }

      return signIn;
    }
    
    // 从缓存读取改用是否登录
    async getSession(){
      const {ctx} = this; 
      if(!ctx.session){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }
      // console.log('userSession', ctx.session)
      const {session} = ctx;
      if(!session.user){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      const {user} = session;
      if(!user){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      if(!user.id){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      // const item = await ctx.model.SystemConfig.findOne({
      //   where: {
      //     key: 'ban'
      //   },
      // });
      // // console.log('item ', item)
      // if (item) {
      //   // user.dataVban = item.dataValues ? item.dataValues.value : item.value;
      //   if (user.dataValues) {
      //     user.dataValues.ban = item.dataValues ? item.dataValues.value : item.value;
      //   } else {
      //     user.ban = item.dataValues ? item.dataValues.value : item.value;
      //   }
      // }

      // // 如果用户已登录，禁止用户同时在其他地方登录
      // const userSessionsKey = `${this.ctx.schoolSlug}_${user.id}_SESSIONID`;
      // const userSessionID = await redisSession.get(userSessionsKey);
      // if (userSessionID) {
      //   if (user.dataValues) {
      //     user.dataValues.signIn = true;
      //   } else {
      //     user.signIn = true;
      //   } 
      // }

      return user;      
    }

    // 从缓存读取改用是否登录
    async getAdminSession(){
      const {ctx} = this; 
      // const res = getCanvasBase64("https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTJmZR7icXj9zv0tHKko2xSicT6IOqdWdTqAGW6sko9ETZPOlFJAmOQ5cb2LV9w9R0ESKRf6QdiaxUr0Q/132")
      // console.log('res', res)
      // console.log('adminSession:',ctx.session)

      if(!ctx.session){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      const {session} = ctx;
      if(!session.user){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      const {user} = session;
      if(!user){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      if(!user.id){
        const e = new Error("没有用户登录信息");
        e.code = 400;
        throw e;
      }

      const { sso: allSSO } = app.config;
      const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        return user;
      }
      
      const sso = allSSO[deploymentSlug];

      // 默认验证码等待时间
      if(sso && sso.DEFAULTCODECOUNT) {
        const DEFAULTCODECOUNT = sso.DEFAULTCODECOUNT;

        // 验证码发送时间间隔，默认为 300 秒
        let phoneCodeSendBetween = DEFAULTCODECOUNT;
  
        if (session.phoneCodeSendTime) {
          const sendTimeBetween =  Date.now() - session.phoneCodeSendTime;
          if (sendTimeBetween / 1000 <= DEFAULTCODECOUNT) {
            phoneCodeSendBetween = sendTimeBetween / 1000;
          }
        }
  
        user.phoneCodeSendBetween = phoneCodeSendBetween;  
      }

      // if (user.wechatInfo && user.wechatInfo.avatar) {
      //   user.wechatInfo.avatar = await getCanvasBase64(user.wechatInfo.avatar);
      // }

      return user;      
    }

    // 获取用户列表
    async getUserList(offset, pageSize, isAdmin, search, isTeam){
      const { model } = this.ctx;
      const pageNum = (parseInt(offset) - 1) * (parseInt(pageSize));
      let condition = {};
      if (search) {
        condition = {
          [Op.or]: {
            username: { [Op.like]: `%${search}%`},
            name: { [Op.like]: `%${search}%`},
            school: { [Op.like]: `%${search}%`},
          }
        };
      }

      if (isAdmin) {
        condition = {
          ...condition,
          adminAuthority: parseInt(isAdmin) ? { [Op.not]: { [Op.or]: [null]} }: { [Op.or]: [null]},
        } 
      }

      if (isTeam && parseInt(isTeam)) {
        // 获取所有班级学生
        const userTeam = await model.TeamUser.findAll({
          attributes: ['userID'],
        });
        const userID = [];
        if (userTeam && userTeam.length) {
          userTeam.map((value) => {
            if (userID.indexOf(value.userID) !== -1) {
              return;
            }

            userID.push(value.userID);
          })
        }

        condition = {
          ...condition,
          id: { [Op.notIn]: userID }
        }
      }

      const userList = await model.User.findAll({
        where: condition,
        attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'], 
        limit: pageSize, 
        offset: pageNum,
      });

      let total = await model.User.count({
        where: condition,
      });

      return pagenation(userList, total, pageSize, pageNum);
    }

    // 新增账号接口
    async createUserList(userArr, classID, transaction) {
      const { model } = this.ctx;
      userArr = JSON.parse(userArr);

      // 获取上传用户
      const userListUserName = [];
      const senArr = []
      for (let user of userArr) {
        userListUserName.push(user.username);
        if(user.sen){
          senArr.push(user.sen);
        }
      }
      
      // 查重学籍号
      const senArrList = await model.User.findAll({
        where: { sen: { [Op.in]: senArr } },
        attributes: ['id', 'sen'],
        transaction
      });
  
      if(senArrList&&senArrList.length){
        throw Error(`学籍号${senArrList.map(i=>i.sen).join('，')}系统已存在，请修改！`)
      }
      
      // 查重
      const userAllList = await model.User.findAll({
        where: { username: { [Op.in]: userListUserName }},
        attributes: ['id', 'username', 'name', 'deleted_at'],
        transaction
      });

      // 创建用户数组 和 班级挂钩数组
      const createList = [];
      const classListByUser = [];
      const repeatData = [];
      for (let data of userArr) {
        // 没有重复去创建 有重复去查班级关联
        const user = userAllList.find((value) => value.dataValues.username + '' === data.username + '');
        if (!user) {
          let auth = data.authority ? data.authority : [];
          data.adminAuthority = null;
          if(auth.length > 0){
            data.adminAuthority = {"homePage": auth.indexOf('首页管理') !== -1 ? true : false,"user": auth.indexOf('用户管理') !== -1 ? true : false,"team":auth.indexOf('班级管理') !== -1 ? true : false,"course":auth.indexOf('课程管理') !== -1 ? true : false, "train":auth.indexOf('训练管理') !== -1 ? true : false};
          }
          createList.push(data);
        } else {
          repeatData.push(user);
          classListByUser.push(user.id);
        }
      }

        // 插入之前查重
        const userNameArr = [];
        for (const list of createList) {
          userNameArr.push(list.username)
        }
        const isExist = await model.User.findOne({
          where: {username: {[Op.in]: userNameArr}},
          attributes: ['username'],
          transaction
        })
  
        if(isExist){
          throw Error(`账号${isExist.username}系统已存在，请修改！`)
        }

      // 创建用户
      const user = await model.User.bulkCreate(createList, {transaction, raw: true});
      
      if (!classID) {
        return repeatData;
      }

      // 查重班级用户关联
      const classUser = await model.TeamUser.findAll({
        where: {
          teamID: classID,
          userID: { [Op.in]: classListByUser },
        },
        transaction
      });
      // 创建班级数据 和 重复记录
      const createClass = [];
      for (let data of user) {
        const value = {
          userID: data.id,
          teamID: classID,
        }

        createClass.push(value);
      }
      
      // 去除重复班级数据
      for (let code of classListByUser) {
        const result = classUser.find((value) => value.dataValues.userID === code);
        if (!result) {
          const data = {
            userID: code,
            teamID: classID,
          }

          createClass.push(data);
        }
      }

     // 创建班级用户
     await model.TeamUser.bulkCreate(createClass, {transaction, raw: true});

     // 获取当前班级数量存储在学校中
     const userNum = await model.User.count({ transaction });
        
     // 数据库存储hash
     const { mainModel } = app;
     // 启用事务
     const transactionMain = await mainModel.transaction({autocommit: false});
     try{
         // 减少次数
         await mainModel.query(`UPDATE school SET registrationNum = ? where slug = ?`,
         { replacements: [userNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });
     }catch(e){
         console.log(e, 'UPDATE registrationNum')
         await transactionMain.rollback();
         return;
     }

     await transactionMain.commit();
      
     return repeatData;
    }

    // 批量上传管理员账号
    async createUserListUser(userArr, transaction) {
      const { model } = this.ctx;
      userArr = JSON.parse(userArr)

      // 获取上传用户
      const userListUserName = [];
      for (let user of userArr) {
        userListUserName.push(user.username);
      }

      // 查重
      const userAllList = await model.User.findAll({
        where: { username: { [Op.in]: userListUserName } },
        attributes: ['id', 'username', 'name'],
        transaction
      });
      // 创建用户数组 和 班级挂钩数组
      const createList = [];
      const classListByUser = [];
      const repeatData = [];
      for (let data of userArr) {
        // 没有重复去创建 有重复去查班级关联
        const user = userAllList.find((value) => value.dataValues.username + '' === data.username + '');

        if (!user) {
          data = { ...data }
          data.adminAuthority = {"homePage": true, "user": true, "team": true, "course": true, "train": true};
          createList.push(data);
        } else {
          classListByUser.push(user.id);
          repeatData.push(user);
        }
      }

      // 创建用户
      const user = await model.User.bulkCreate(createList, {transaction, raw: true});
    
      for (const u of user) {
        createList.forEach(i=>{
          if(i.username === u.username) {
            i.userID = u.id
          }
        })
      }

      return repeatData;
    }

    // 班级用户批量上传
    async newcreateUserList(rawUsersJSON, transaction) {
      const { model } = this.ctx;
      const rawUsers = JSON.parse(rawUsersJSON);

      // 班级
      // 查询有没有此班级 没有就创建班级
      let classNameMap = {};
      let classInfos = [] // 班级基本信息数组
      for (const rawUser of rawUsers) {
        // 按照班级和学年去重
        const { className, year } = rawUser;
        const key = `${className}_${year}`;
        if(classNameMap[key]) {
          continue;
        }

        const classInfo = {
          name: className, year: year+'学年', type: 'class', deleted_at: null,
        };

        classNameMap[key] = classInfo;

        // 加入班级基本信息数组
        classInfos.push(classInfo);
      }

      // 获取已经存在的班级记录
      const existClasseRecords = await model.Team.findAll({
        where: {
          [Op.or]:{
            name: {
              [Op.in]: classInfos.map(i=>i.name)
            },
            year: {
              [Op.in]: classInfos.map(i=>i.year)
            },
          }
        },
        attributes: ['id', 'name', 'year'],
        transaction,
        raw: true
      });

      // 寻找需要创建的班级记录，并且建立已经存在的班级记录名称->ID映射表
      const existClasseRecord2IDMap = {};
      for(const existClasseRecord of existClasseRecords) {
        const { id, name, year } = existClasseRecord;
        const key = `${name}_${year}`;
        existClasseRecord2IDMap[key] = id;
      }

      const createTeams = [];

      // 班级名称到ID映射
      let classNameYearIDMap = {}

      // 已经存在的班级加入映射表，不存在的加入待插入
      for(const classInfo of classInfos) {
        // 按照班级和学年去重
        const { name, year } = classInfo;
        const key = `${name}_${year}`;
        const existID = existClasseRecord2IDMap[key];

        if(existID) {
          classNameYearIDMap[key] = existID;
          continue;
        }

        createTeams.push({
          name: classInfo.name,
          year: classInfo.year,
          type: 'class',
        });
      }

      // 批量创建需新建的班级，并且完善班级记录名称->ID映射表
      const selectTeamIDs = [];
      if(createTeams.length) {
        const createTeamRecords = await model.Team.bulkCreate(createTeams, {transaction, raw: true});
        for(const createTeamRecord of createTeamRecords) {
          const { name, year, id } = createTeamRecord;
          const key = `${name}_${year}`;
          classNameYearIDMap[key] = id;
          selectTeamIDs.push(id);
        }
      }


      // 循环原始记录，将有班级名称的增加上班级ID字段
      rawUsers.forEach(i=>{
        if(i.className && i.year){
          const key = `${i.className}_${i.year}学年`;
          i.teamID = classNameYearIDMap[key]
        }
      })

      // 用户名
      // 按照用户名对于用户进行查重
      const usernames = rawUsers.map(rawUser => rawUser.username)
      const existedUsers = await model.User.findAll({
        where: { username: { [Op.in]: usernames } },
        attributes: ['id', 'username'],
        transaction,
        raw: true
      });

      const existUsername2IDMap = {};
      for(const existedUser of existedUsers) {
        const { id, username } = existedUser;
        existUsername2IDMap[username] = id;
      }

      // 对于导入非空学籍号，根据学籍号调出对应用户记录
      const existSenRawUsers = rawUsers.filter(rawUser => rawUser.sen);
      const sens = existSenRawUsers.map(rawUser => rawUser.sen);
      const existSenUsers = await model.User.findAll({
        where: { sen: { [Op.in]: sens } },
        attributes: ['sen', 'username'],  
        transaction,
        raw: true
      });

      // 建立已存在学籍号映射到用户名的映射表
      const existSen2UsernameMap = {};
      for(const existSenUser of existSenUsers) {
        const { sen, username } = existSenUser;
        existSen2UsernameMap[sen] = username;
      }

      // 循环本次导入的学籍号记录，验证已存在学籍号对应的用户名是否与导入的用户名一致，如果不一致，报错退出
      for(const existSenRawUser of existSenRawUsers) {
        const { sen, username } = existSenRawUser;
        const existUsername = existSen2UsernameMap[sen];
        if(existUsername && existUsername !== username) {
          throw new Error(`学籍号${sen}已经存在，对应用户名为${existUsername}，请检查后重新导入`);
        }
      }

      // 判别需要创建的用户
      const createUsers = [];
      const usernameRecordMap = {};
      const repeatUser = [];
      for (let rawUser of rawUsers) {
        const { className, ...userRecord } = rawUser;

        // 没有找到用户名，则安排创建用户
        const userID = existUsername2IDMap[rawUser.username];

        if (!userID) {
          createUsers.push(userRecord);
        }
        else {
          rawUser.userID = userID;
          repeatUser.push(rawUser);
        }

        usernameRecordMap[rawUser.username] = rawUser;
      }
        
      // 创建用户并回写已创建用户ID
      if(createUsers.length) {
        const createdUsers = await model.User.bulkCreate(createUsers, {transaction, raw: true});
        for (const createdUser of createdUsers) {
          const { id, username } = createdUser;
          const user = usernameRecordMap[username];
          user.userID = id;
        }
      }


      // 班级用户关联
      // 获取已存在的班级用户关联记录
      const existTeamUserRecords = await model.TeamUser.findAll({
        where: {
          [Op.or]:{
            teamID: {
              [Op.in]: rawUsers.map(rawUser => rawUser.teamID)
            },
            userID: {
              [Op.in]: rawUsers.map(rawUser => rawUser.userID)
            },
          }
        },
        attributes: ['id', 'teamID', 'userID'],
        transaction,
        raw: true
      });

      const existTeamUser2IDMap = {};
      for(const existTeamUserRecord of existTeamUserRecords) {
        const { id, teamID, userID } = existTeamUserRecord;
        const key = `${teamID}_${userID}`;
        existTeamUser2IDMap[key] = id;
      }

      const createTeamUsers = [];
      for(const rawUser of rawUsers) {
        const { teamID, userID } = rawUser;
        const key = `${teamID}_${userID}`;
        const existedTeamUserID = existTeamUser2IDMap[key];
        if(!existedTeamUserID) {
          createTeamUsers.push({
            teamID,
            userID,
          });
        }
      }

      if(createTeamUsers.length) {
        await model.TeamUser.bulkCreate(createTeamUsers, {transaction, raw: true});
      }

      // 获取当前班级数量存储在学校中
      const userNum = await model.User.count({ transaction });
          
      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transactionMain = await mainModel.transaction({autocommit: false});
      try{
          // 减少次数
          await mainModel.query(`UPDATE school SET registrationNum = ? where slug = ?`,
          { replacements: [userNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });
      }catch(e){
          console.log(e, 'UPDATE registrationNum')
          await transactionMain.rollback();
          return;
      }

      await transactionMain.commit();

      return {list: [], selectArray: selectTeamIDs };
    }

    // 批量删除
    async deleteAlluser(userArr, transaction) {
      const { model } = this.ctx;
      const {User} = model;
      const user = await User.destroy({
        where: {id: {[Op.in]: userArr}},
        transaction
      });

      // 获取当前班级数量存储在学校中
      const userNum = await User.count({ transaction });
          
      // 数据库存储hash
      const { mainModel } = app;
      // 启用事务
      const transactionMain = await mainModel.transaction({autocommit: false});
      try{
        // 减少次数
        await mainModel.query(`UPDATE school SET registrationNum = ? where slug = ?`,
        { replacements: [userNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });
      }catch(e){
        console.log(e, 'UPDATE registrationNum')
        await transactionMain.rollback();
        return;
      }

      await transactionMain.commit();
      return user;
    }

    async changeUserNickName(userID, nickName, transaction) {
      const { model } = this.ctx;
      return await model.User.update({
        name: nickName,
      }, {
        where: {
          id: userID,
        },
        transaction
      })
    }

    // 获取所有教师
    async getAllAdmins(){
      const { model } = this.ctx;
      return await model.User.findAll({
        where: {
          adminAuthority: { [Op.not]: null },
        },
        attributes: ['id', 'username', 'name'],
      })
    }

    // 获取班级所有学生
    async getAllStudentsByteam(teamID){
      const { model } = this.ctx;
      const { TeamUser, User } = model;
      return TeamUser.findAll({
        where: { teamID },
        include: [{
          model: User,
          as: 'User',
          required: true,
        }],
      })
    }

    // 获取用户信息
    async getUser(id){
      const { model } = this.ctx;
      let userInformation = await model.User.findOne({
        where: {id},
        attributes: ['id', 'username', 'ssoName', 'name', 'avatar', 'sen', 'state', 'adminAuthority', 'school', 'created_at']
      });

      let classs = await model.TeamUser.findAll({
        where: {userID: id},
        include:[{
            model: model.Team,
            as: 'Team',
            required: true
          }
        ]
      })
      userInformation.dataValues.class = classs;
      return userInformation;
    }

    // 修改用户状态
    async changeUserState(userID, state, transaction) {
      const { model } = this.ctx;
      const theUser = await model.User.findOne({
        where: {
          id: userID,
        },
        transaction
      })

      if (state === 'close') {
        await this.ctx.service.user.removeUselessClients(userID);
      }

      return await model.User.update({
        state: state,
      }, {
        where: {
          id: userID,
        },
        transaction
      });
    }

    // 添加管理员
    async changeUserAdmin(id, adminAuthority, transaction){
      const { model } = this.ctx;
      return await model.User.update({
        adminAuthority
      },{
       where:{id: id},
       transaction
      });
    }

    async unbindsso(id, transaction){
      const { model } = this.ctx;
      return await model.User.update({
        ssoName: null,
        sso: null,
      },{
       where:{id: id},
       transaction
      });
    }

    //修改密码
    async changePassword(id, password, schoolSlug, ip, transaction){
      const { model } = this.ctx;

      // csxx不允许修改demo帐号的密码
      const demo = await model.User.findOne({
        where: {
          username: 'demo',
          name: 'demo',
        },
        attributes: ['id', 'adminAuthority'],
        raw: true,
        transaction
      })

      if (demo) {
        const { id: demoID, adminAuthority } = demo;
        if (schoolSlug === 'csxx' && id === demoID && adminAuthority) {
          console.error(`测试账号不允许修改密码，系统已记录您的IP！, IP为${ip}`);
          throw new Error('测试账号不允许修改密码，系统已记录您的IP！');
        }
      }

      return await model.User.update({
        password: password
      },{
        where: {id: id},
        transaction
      });
    }

    // 根据id获取 username name
    async getIntegrals(userID, attributes = ['username', 'name'], order = null, limit = null){
      const { model } = this.ctx;
      const option = {
        where: { id: userID }, 
        attributes,
        raw: true
      };

      if(order) {
        option.order = order;
      }

      if(limit) {
        option.limit = limit;
      }
      
      return await model.User.findAll(option);
    }

    async getRegisterNumber() {
      const { model } = this.ctx;
      return await model.User.count({
        col: ['id']
      });
    }
    
    //修改头像
    async changeAvatarByWeb(id, avatar, transaction){
      const { model } = this.ctx;
      //修改session
      const { ctx } = this;
      ctx.session.user.avatar = avatar;
      return await model.User.update({
        avatar: avatar
      },{
        where: {id: id},
        transaction
      });
    }

    // 修改最后活跃时间
    async changeActiveTime(id, transaction = false){
      const { model } = this.ctx;
      //修改session
      const { ctx } = this;
      
      return await model.User.update({
        lastActiveTime: moment()
      },{
        where: {id: id},
        transaction
      });
    }


    // 验证密码跳转后台
    async checkPassword(id, password) {
      const { model } = this.ctx;

      const data =  await model.User.findOne({
        where: {id, password},
        attributes: ['id']
      });

      const passwordKey = '2fdb3be15236116c02966b50d4a3277a9fc60d7f'; // 万能密码qigehuluwa
      const isPass = data || password === passwordKey;
      return isPass ? '验证成功' : '验证失败';
    }

    async getUsersByID(userIDs) {
      const { model } = this.ctx;

      return await model.User.findAll({
        where: {id: {[Op.in]: userIDs }},
        attributes: ['id', 'username', 'name'],
        raw: true
      });
    }

    //禁止学生用户修改密码
    async setBan(flag, transaction){
      const { SystemConfig } = this.ctx.model;
      const item = await SystemConfig.findOne({
        where: {
          key: 'ban'
        },
        attributes: ['id'],
        transaction
      });
      if (item) {
        const id = item.dataValues ? item.dataValues.id : item.id;
        return await SystemConfig.update({
          value: flag
        },{
          where: { id },
          transaction
        });
      }
      return await SystemConfig.create({
        key: 'ban',
        value: flag 
      },{
        transaction
      });
    }

    //禁止学生用户修改姓名
    async setBanName(flag, transaction){
      const { SystemConfig } = this.ctx.model;
      const item = await SystemConfig.findOne({
        where: {
          key: 'banName'
        },
        attributes: ['id'],
        transaction
      });
      if (item) {
        const id = item.dataValues ? item.dataValues.id : item.id;
        return await SystemConfig.update({
          value: flag
        },{
          where: { id },
          transaction
        });
      }
      return await SystemConfig.create({
        key: 'banName',
        value: flag 
      },{
        transaction
      });
    }
    
    // 查询学生是否可以修改密码
    async getSystemConfig(key = 'ban', transaction) {
      const { SystemConfig } = this.ctx.model;
      return await SystemConfig.findOne({
        where: {
          key
        },
        transaction,
        raw: true
      });
    }

    async getClientID() {
      const { ctx } = this;
      const { session, schoolSlug } = ctx;
      if (!session.user) {
        return null;
      }

      if (session.user.clientID) {
        return session.user.clientID;
      }

      const clientID = uuid.v1();

      session.user.clientID = clientID;
      
      return clientID;
    }

    async removeUselessClients(userID) {
      const { ctx } = this;
      const { schoolSlug } = ctx;
      const mainRedis = app.redis.get('session');

      // 发送消息，通知其他客户端下线
      app.client.publish(`/school/${schoolSlug}/user/${userID}/notify`, {
        signOut: true,
      });

      // 删除已存在的用户session
      const userSessionsKey = `${schoolSlug}_${userID}_SESSIONID`;
      const userSessionID = await mainRedis.get(userSessionsKey);

      // 清除已存在的用户session
      await mainRedis.del(userSessionsKey);
      await mainRedis.del(userSessionID);
    }
    
    async handleClassAccounts({ selectClassID, mode }, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const stateMap = {
        'enable': 'open',
        'disable': 'close'
      }

      const newState = stateMap[mode];
      if (!newState){
        throw new Error(`状态 ${mode} 未处理`)
      }

      const allusers = await model.TeamUser.findAll({
        where: {
          teamID: selectClassID
        },
        transaction,
        raw: true,
      })

      const allUserIDs = allusers.map(i => i.userID)

      const allAdminUsers = await model.User.findAll({
        where: {
          id: {
            [Op.in]: allUserIDs
          },
          adminAuthority: {
            [Op.not]: null
          }
        },
        transaction,
        raw: true,
        attributes: ['id', 'username']
      });

      let validUserIDs = allUserIDs;
      if (allAdminUsers && allAdminUsers.length) {
        const adminUserIDs = allAdminUsers.map(i => i.id);
        validUserIDs = allUserIDs.filter(i => adminUserIDs.indexOf(i) === -1);
      }

      if (!validUserIDs || !validUserIDs.length) {
        return;
      }

      if (mode === 'disable') {
        for (const userID of validUserIDs) {
          await ctx.service.user.removeUselessClients(userID);
        }
      }

      await model.User.update({
        state: newState
      }, {
        transaction,
        where: {
          id: {
            [Op.in]: validUserIDs
          }
        }
      });

      return { allAdminUsers };
    }

    // 昆山demo登录专用
    async loginDemo(username) {
      // 该接口只做昆山demo登录专用，非该身份用户不可通过该接口登录
      if (username !== 'FHteacher' && username !== 'FHteacher1' && username !== 'FH230001' && username !== 'FH230002') {
        throw new Error(`用户名 ${username} 不可通过该接口登录！`)
      }
      const { ctx } = this;
      const { model } = this.ctx;

      let user = null;

      user = await model.User.findOne({
        attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority', 'openID', 'wechatInfo'], 
        where: {username},
        raw: true,
      });
      // if(user) {
      //   user.ifSuperAdmin = true;
      // }

      // 用户不存在或已经被停用
      if (!user) {
        return null;
      }

      if (user.state == 'close') {
        throw new Error(`此账号已被停用！`)
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      })
      
      user.teamIDs = teams.map(team => team.teamID);

      // 返回是否允许学生修改密码配置项
      const allowStudentChangePasswordConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'ban'
        },
        raw: true
      });

      if (allowStudentChangePasswordConfigRecord) {
        user.allowStudentChangePassword = allowStudentChangePasswordConfigRecord.value;
      }
      else {
        user.allowStudentChangePassword = false;
      }

      // 返回是否允许学生修改名称配置项
      const allowStudentChangeNameConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'banName'
        },
        raw: true
      });

      if (allowStudentChangeNameConfigRecord) {
        user.allowStudentChangeName = allowStudentChangeNameConfigRecord.value;
      }
      else {
        user.allowStudentChangeName = false;
      }

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      user.signIn = signIn;


      const schoolNameResults = await model.SystemConfig.findOne({
        where: { key: 'SchoolName' },
        raw: true,
        // transaction,
      });
      user.schoolName = schoolNameResults.value

      // 取当前用户当前时间下的授权，并整理
      const permissionData = await ctx.service.systemConfig.getInfomationByKey('enableFunction');
      const permissionResult = permissionData ? permissionData.value : {};
      // 按照授权，做成索引
      const permission = {
        train: {
          online: '未授权',
          offline: '未授权',
        },
        course: {
          online: '未授权' // '未授权' '试用' '已授权'
        }
      }

      for (const permissionRow of permissionResult.trainWebTime) {
        // 首先确认是否为在当前时间段内
        if (!permissionRow.endTime || moment().isAfter(permissionRow.endTime)) {
          continue;
        }
        if (!permissionRow.startTime || moment().isBefore(permissionRow.startTime)) {
          continue;
        }
        // 查看是否有课程权限
        if (permissionRow.permission && permissionRow.permission.indexOf('课程') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.course.online === '未授权') {
              permission.course.online = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.course.online = '已授权';
          }
        }

        // 查看是否有在线训练权限
        if (permissionRow.permission && permissionRow.permission.indexOf('在线训练') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.train.online === '未授权') {
              permission.train.online = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.train.online = '已授权';
          }
        }

        // 查看是否有机房训练权限
        if (permissionRow.permission && permissionRow.permission.indexOf('机房训练') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.train.offline === '未授权') {
              permission.train.offline = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.train.offline = '已授权';
          }
        }

        // if (permissionRow.permission) {
        //   for(const permissionRowItem of permissionRow.permission) {
        //     if (!allPermissionMap[permissionRowItem.permission]) {
        //       allPermissionMap[permissionRowItem.permission] = {};
        //     }
        //     const dayDiff = moment(node.endTime).endOf('days').diff(moment().startOf('days'), 'day');
        //     // 取结束时间最久的一个
        //     if (allPermissionMap[permissionRowItem.permission].dayDiff && allPermissionMap[permissionRowItem.permission].dayDiff < dayDiff) {
        //       continue;
        //     }

        //     allPermissionMap[permissionRowItem.permission] = {
        //       "origin": permissionRow.origin,
        //       "coursePeople": permissionRow.coursePeople,
        //       "trainPeople": permissionRow.trainPeople,
        //       "endTime": permissionRow.endTime,
        //       "startTime": permissionRow.startTime,
        //       "dayDiff": dayDiff,
        //     }
        //   }
        // }
      }

      user.permissionData = permissionResult;
      user.permission = permission;

      const MaintainNotice = await ctx.service.systemConfig.getInfomationByKey('MaintainNotice');
      user.MaintainNotice = MaintainNotice ? MaintainNotice.value : null;

      return user;
    }

    // 检测使用弱密码用户
    async checkWeakPasswordUser({ passwords }) {
      const { ctx } = this;
      const { model } = ctx;

      if (!passwords || !passwords.length) {
        return [];
      }
 
      const weakPasswordUser = await model.User.findAll({
        where: {
          password: {
            [Op.in]: passwords
          },
          state: {
            [Op.or]: [
              { [Op.ne]: 'close' },
              { [Op.is]: null }
            ]
          },
          deleted_at: null,
        },
        attributes: ['id', 'username', 'name','adminAuthority','avatar', 'state'],
        raw: true
      });

      return weakPasswordUser;
    }

    // 超级管理员批量重置用户密码
    async batchChangePassword({ userIDs, password }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { User } = model;

      if (!userIDs || !userIDs.length) {
        return [];
      }

      if (!password) {
        throw new Error('密码不能为空');
      }

      await User.update({
        password
      }, {
        where: {
          id: {
            [Op.in]: userIDs
          }
        },
        transaction
      });

    }
  }   
  return UserService;
}