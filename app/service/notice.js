const { Op, QueryTypes } = require('sequelize');
module.exports = app => {

  class NoticeService extends app.Service {
      // 新增公告
    async addNotice(title, preface, status, content, text, transaction) {
      const { model } = this.ctx;
      const { Notice } = model;
      return await Notice.create({
          title, preface, status, content, text
      }, { transaction })
    }

    // 修改公告信息
    async putNotice(noticeID, title, preface, status, content, text, transaction) {
      const { model } = this.ctx;
      const { Notice } = model;
      // status 为 草稿 是 取消 登录提示
      if(status === '草稿') {
        const isExist = await model.SystemConfig.findOne({
          where: { key: 'notice', value: noticeID },
          transaction
        });

        if(isExist) {
          await model.SystemConfig.update({
            value: '',
          },{
            where: {key: 'notice'},
            transaction
          });
        }
      }
      return await Notice.update({
        title, preface, status, content, text
      },{
        where: { id: noticeID },
        transaction
      });
    }

    // 删除公告
    async deleteNotice(noticeID, transaction){
      const { model } = this.ctx;
      const { Notice } = model;
      return await Notice.destroy({
        where: { id: noticeID },
        transaction
      });
    }

    // 获取公告列表
    async getNoticeList(status) {
      const { model } = this.ctx;
      const { Notice } = model;
      let result = [];

      if (status) {
        if (status === '草稿') {
          result = await Notice.findAll({
            where: { status: '草稿' },
          }); 
        } else {
          result = await Notice.findAll({
            where: { status: { [Op.in]: ['已发布', '置顶'] }},
          });
        }
      } else {
        result = await Notice.findAll();
      }
      return result;
    }

    // 获取公告详情
    async getNoticeDetails(noticeID) {
      const { model } = this.ctx;
      const { Notice } = model;
      return await Notice.findOne({
        where: { id: noticeID },
      });
    }

    // 前台 获取公告详情
    async getNoticeMessage(noticeID) {
      const { model } = this.ctx;
      const { Notice } = model;

      // 获取当前公告详情
      const  currentNotice = await Notice.findOne({
        where: { id: noticeID },
      });

      // 查找不到该公告
      if (!currentNotice) {
        return '不存在该公告';
      }
      // 获取上一篇
      const lastNotice = await this.ctx.model.query("SELECT * FROM `notice` AS `notice` WHERE `notice`.`id` = (select id from notice where id < ? AND `notice`.`deleted_at` IS NULL AND `notice`.`status` != '草稿' order by id desc limit 1) AND `notice`.`deleted_at` IS NULL AND `notice`.`status` != '草稿'",
        { replacements: [currentNotice.id], type: QueryTypes.SELECT }
      );

      // 获取下一篇
      const nextNotice = await this.ctx.model.query("SELECT * FROM `notice` AS `notice` WHERE `notice`.`id` = (select id from notice where id > ? AND `notice`.`deleted_at` IS NULL AND `notice`.`status` != '草稿' order by id asc limit 1) AND `notice`.`deleted_at` IS NULL AND `notice`.`status` != '草稿' ",
        { replacements: [currentNotice.id], type: QueryTypes.SELECT }
      );
      return { currentNotice, lastNotice, nextNotice };

    }
  }
  
  return NoticeService;
};
  