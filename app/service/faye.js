const { Op } = require('sequelize');

module.exports = app => {
    const redis = app.redis.get('faye');

    class FayeService extends app.Service {

        // 页面断开连接
        async onPageDisconnect(clientID) {
            const userPipeline = redis.pipeline();

            // 获取需要释放的资源
            const pageClientResourceKey = `${clientID}_PR`;
            userPipeline.hgetall(pageClientResourceKey)
            userPipeline.del(pageClientResourceKey);

            // 如果还有其他的页面，暂时就不要调整用户记录和计数器了
            const [resourceRecordResponse, , , , , ] = await userPipeline.exec();

            // TODO 确认返回结果格式，可能相关的地方代码都需要更新一下
            const resourceRecordMap = resourceRecordResponse[1];

            // 逐一释放资源
            const { ctx } = this;
            for(const resourceType in resourceRecordMap) {
                const resourceRecordJSONStr = resourceRecordMap[resourceType];
                const resourceRecord = JSON.parse(resourceRecordJSONStr);
                switch(resourceType) {
                    case 'icourse_kernel':
                        {
                            const { containerName, chapterName, sectionName } = resourceRecord;

                            // 向Icourse-worker发起释放Kernel请求
                            await ctx.service.icourse.requestKernelFree(containerName, chapterName, sectionName, clientID);
                        }
                        break;
                    default:
                        throw new Error(`异常的资源类型${resourceType}`);
                }
            }

            const globalPipeline = redis.pipeline();

            await globalPipeline.exec();
        }

        // 记录在线资源绑定情况
        async addPageResourceRecord(clientID, resourceType, resourceRecord) {
            // 计入在线资源情况
            await redis.hset(`${clientID}_PR`, {
                [resourceType]: JSON.stringify(resourceRecord)
            });
        }

        // 删除在线资源绑定
        async removePageResourceRecrod(clientID, resourceType) {
            await redis.hdel(`${clientID}_PR`, resourceType);
        }
    }

    return FayeService;
}