const moment = require('moment');
const md5 = require('md5');
const fetch = require('node-fetch');
const fs = require("mz/fs");
const { Op, cast, col } = require('sequelize');
const sequelize = require('sequelize');
const { QueryTypes } = sequelize;
const path = require('path');
const AdmZip = require("adm-zip");
const { delDir, readFileMd5 } = require('../utils/file');
const { FREETEMPLATE, orderMap } = require('../utils/refer');
const { getTotalScore, adjustQuestionScore } = require('../utils/question');
const crypto = require('crypto');

const { encrypt } = require('../utils/crypto');

const currentYear = moment().format('YYYY');
const currentMonth = moment().format('YYYYMM');
const currentSchoolYear =
          currentMonth > `${moment().format('YYYY')}07`
            ? `${currentYear}学年`
            : `${currentYear - 1}学年`;

//! 将srcDir文件下的文件、文件夹递归的复制到tarDir下
const copyFolder = async function(srcDir, tarDir, filter) {
  // console.log('copyFolder:', srcDir, tarDir)
  const files = await fs.readdir(srcDir);
  // console.log('fs.readdir files:', files)

  for(const file of files) {
    const srcPath = path.join(srcDir, file);

    if(filter) {
      if(!filter(srcPath, file)) {
        continue;
      }
    }

    const tarPath = path.join(tarDir, file);
    // console.log('copyFolder file:', srcPath, tarPath)

    const stats = await fs.stat(srcPath)
    // console.log('stat file:', err, stats)
    if (stats.isDirectory()) {
      // console.log('mkdir', tarPath);
      await fs.mkdir(tarPath);

      await copyFolder(srcPath, tarPath, filter);
    } else {
      // console.log('mkdir', tarPath);
      await fs.copyFile(srcPath, tarPath);
    }
  }

  //为空时直接回调
  // files && files.length && files.length === 0 && cb && cb();
}

module.exports = app => {
  
  class PclabTrain extends app.Service {
    // 获取去全部班级（精简）
    async getClassListAllLess(year) {
      const { model } = this.ctx;
      const { Team, TeamUser } = model;

      const condition = {};
      if(year){
        condition.year = {
          [Op.like]: '%' + year + '%'
        };
      }

      const teamDatas = await Team.findAll({
        attributes: ['id', 'name', 'year'],
        where: condition,
        order: [['created_at', 'desc']],
        raw: true,
      });
      // console.log('teamDatas:',teamDatas)

      const teamIDs = teamDatas.map(row => row.id);

      const counts = await TeamUser.count({
        where: { teamID: { [Op.in]: teamIDs } },
        raw: true,
        group: ['teamID']
      })

      const result = teamDatas.map(row => {
        const countRow = counts.find(subRow => subRow.teamID === row.id);
        // console.log('countRow:',countRow)
        return {
          ...row,
          count:  countRow ? countRow.count : 0
        }
      }).filter(row => row.count)

      return result
    }

    // 批量获取全部班级（精简）
    async getBulkClassListAllLess(year, classIDs) {
      const { model } = this.ctx;
      const { Team, TeamUser } = model;

      const condition = {};
      if(year){
        condition.year = {
          [Op.like]: '%' + year + '%'
        };
      }
      // console.log('classIDs:',classIDs)

      if (classIDs) {
        condition.id = { [Op.in]: classIDs.split(',').map(row => parseInt(row)) }
      }

      const teamDatas = await Team.findAll({
        attributes: ['id', 'name', 'year'],
        where: condition,
        order: [['created_at', 'desc']],
        raw: true,
      });
      // console.log('teamDatas:',teamDatas)

      const teamIDs = teamDatas.map(row => row.id);

      const counts = await TeamUser.count({
        where: { teamID: { [Op.in]: teamIDs } },
        raw: true,
        group: ['teamID']
      })

      const result = teamDatas.map(row => {
        const countRow = counts.find(subRow => subRow.teamID === row.id);
        return {
          ...row,
          count:  countRow ? countRow.count : 0
        }
      })

      return result
    }
    
    // 获取班级列表和人员列表,
    async filterUserWithComputerRoomTrain(classID) {
      const { model } = this.ctx;
      const { User, TeamUser } = model;

      const condition = {};

      let userList = null;
      let total = 0;

      if (classID) {
        const classUsersRep = await TeamUser.findAll({
            where: {teamID: classID },
            attributes: ['userID'],
        });

        const userIDMaps = [];
        for(let each of classUsersRep){
            userIDMaps.push(each.dataValues.userID);
        }

        userList = await User.findAll({
            attributes: ['id', 'name', 'avatar', 'state', 'username', 'school', 'sen'],
            where: {
                ...condition,
                id: {[Op.in]: userIDMaps}
            },
            order: [[cast(col('user.username'), 'CHAR'), 'asc']],
        });

        userList = userList.map((data) =>({
            ...data.dataValues,
              classID
        }));

        total = await model.TeamUser.count({
          where: { teamID: classID },
          include: [
            {
              model: User,
              as: 'User',
              // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'],
              where: condition,
              required: true,
            }
          ],
        });

      } else {
        userList = await User.findAll({
          where: condition,
          // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'], 
          order: [[cast(col('user.username'), 'CHAR'), 'asc']]
        });

        total = await model.User.count({
          where: condition,
        });
      }
      
      // return { userList, total, pageNum: offset, pageSize, classID }
      return userList
    }
    
    // 获取班级列表和人员列表,
    async filterBulkUserWithComputerRoomTrain(classIDs) {
      const { model } = this.ctx;
      const { User, TeamUser, Team } = model;

      const condition = {};
      const results = [];

      const allClassData = await Team.findAll({
        where: { id: { [Op.in]: classIDs } },
        // attributes: ['userID'],
        raw: true,
      });

      for(const classID of classIDs) {
        const findClassRow = allClassData.find(row => row.id === classID);
        if (!findClassRow) {
          continue;
        }
        let userList = null;
        let total = 0;
        // console.log('---------------------------')
        // const classUsersRep = await TeamUser.findAll({
        //   where: {teamID: classID },
        //   attributes: ['userID'],
        // });
        let classUsersRep = await model.query('SELECT `id`,`userID`,`deleted_at` FROM `team_user` AS `team_user` WHERE (`team_user`.`teamID` = ?) AND (`team_user`.`deleted_at` IS NULL)',
          { replacements: [classID], type: QueryTypes.SELECT }
        )

        // 取有delete为null的人员名单
        const hasUser = classUsersRep.filter(row => !row.deleted_at).map(row => row.userID);
        // console.log('hasUser:',hasUser)
        const hasUserSet = new Set(hasUser ? hasUser : []);
        // classUsersRep去除已经有delete为null人员
        // classUsersRep = classUsersRep.filter(row => !hasUserSet.has(row.userID));
        // console.log('---------------------------')
        // console.log('classUsersRep:',classUsersRep);

        const userIDMaps = [];
        const userIDDeleteMap = {};
        const userTeamIDMap = {};
        for(let each of classUsersRep) {
          if (each.deleted_at && hasUserSet.has(each.userID)) {
            continue;
          }
          userIDMaps.push(each.userID);
          userIDDeleteMap[each.userID] = each.deleted_at;
          userTeamIDMap[each.userID] = each.id;
        }

        // console.log('---------------------------')
        // userList = await User.findAll({
        //   // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school', 'sen', 'password'],
        //   attributes: ['id', 'username', 'password', 'name', 'sen', 'avatar', 'state', 'adminAuthority', 'school', 'created_at', 'updated_at', 'deleted_at'],      
        //   where: {
        //       ...condition,
        //       id: {[Op.in]: userIDMaps}
        //   },
        //   order: [[cast(col('user.username'), 'CHAR'), 'asc']],
        // });

        
        userList = await model.query('SELECT `id`, `username`, `password`, `name`, `sen`, `avatar`, `state`, `adminAuthority`, `school`, `created_at`, `updated_at`, `deleted_at` FROM `user` AS `user` WHERE (`user`.`id` IN (?)) ORDER BY CAST(`user`.`username` AS CHAR) ASC',
          { replacements: [userIDMaps], type: QueryTypes.SELECT }
        )
        
        userList = userList.map(row => ({ ...row, team_user_deleted_at: userIDDeleteMap[row.id], team_user_id: userTeamIDMap[row.id] }))

        // userList = userList.map((data) =>({
        //     ...data.dataValues,
        //       classID
        // }));

        total = await model.TeamUser.count({
          where: { teamID: classID },
          include: [
            {
              model: User,
              as: 'User',
              // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'],
              where: condition,
              required: true,
            }
          ],
        });

        results.push({ userList, classID, className: findClassRow.name, year: findClassRow.year })
      }
      
      // return { userList, total, pageNum: offset, pageSize, classID }
      return results
    }

    async getAllClassIds() {
      const result = await this.ctx.model.Team.findAll({
        attributes: ['id'],
        raw: true,
      });

      return result.map(row => row.id);
    }

    async getSign(object) {
      const keys = Object.keys(object);

      keys.sort((a, b) => {
        return a < b ? -1 : 1;
      })

      let resultArr = []
  
      for (const key of keys) {
        if (!object[key] || object[key] === "") {
          continue;
        }
        resultArr.push(`${key}=${object[key]}`);
      }

      return resultArr.join('&')
    }

    async signKey(data) {
      let signValue = data;
      // 加密
      const md5Value = md5(signValue);
      return md5Value.toLowerCase();
    }

    async getTimestampAsSecondFormat(date) {
      if (null === date) {
        return 0;
      }
      return `${new Date(date).getTime()}`;
    }

    async getSingAndNode(node, timestamp) {
      const signNode = {
        app_id: app.config.platHxrMainAPI.app_id,
        secret: app.config.platHxrMainAPI.secret,
        ...node,
      }
      const { service } = this.ctx;
      const signValue = await service.pclabTrain.getSign(signNode);
      return await service.pclabTrain.signKey(signValue, timestamp);
    }

    // 获取权限
    async getComputerRoomPermission(mac, transaction = false) {
      // 内测版本，暂时获取权限设置为恒定通过！！！！！！！！！！！！！！！！！！！！！！！！
      // return {
      //   totalLicenseCount: 10,
      //   used: 0,
      //   labs: [],
      //   status: 'ok',
      // };


      const { ctx } = this;
      const { SystemConfig } = ctx.model;

      // console.log('ctx.model:',ctx.model)
      const permissionResults = await SystemConfig.findOne({
        where: { key: 'enableFunction' },
        raw: true,
        transaction,
      });

      if (!permissionResults) {
        return { status: 'empty' }
      }

      let permissionDatas = permissionResults.value;
      try {
        permissionDatas = JSON.parse(permissionDatas)
      } catch (e) {}
      // console.log('permissionDatas:',permissionDatas);

      if (!permissionDatas || !permissionDatas['机房训练'] || !permissionDatas.trainPermissionData || !permissionDatas.trainPermissionData.length) {
        return { status: 'empty' }
      }

      let result = {
        totalLicenseCount: 0,
        used: 0,
        mac,
        // labs: [],
      };

      const lastRow = {};
      let hasPer = false;
      
      for (const row of permissionDatas.trainPermissionData) {
        if (row.target !== '机房训练') {
          continue;
        }
        if(row.status === '启用') {
          if((row.startTime && moment().isBefore(moment(moment(row.startTime).startOf('day')))) || (row.endTime && moment().isAfter(moment(moment(row.endTime).endOf('day'))))) {
            continue;
          }

          if (result.totalLicenseCount && result.totalLicenseCount < row.info.teacherPC) {
            continue;
          }

          result.fromDate = moment(row.startTime).format('YYYY-MM-DD');
          result.toDate = moment(row.endTime).format('YYYY-MM-DD');
          result.totalLicenseCount = row.info.teacherPC;
          hasPer = true;
          continue;
        }

        if (row.info && row.info.computerRoomName) {
          result.used += 1;
          // result.labs.push(row.info.computerRoomName)
        }

        if (row.info && row.info.teacherPC) {
          lastRow.totalLicenseCount = row.info.teacherPC;
          // lastRow.fromDate = moment(row.startTime).format('YYYY-MM-DD');
          // lastRow.toDate = moment(row.endTime).format('YYYY-MM-DD');
        }

        // if((row.startTime && moment().isBefore(moment(row.startTime))) || (row.endTime && moment().isAfter(moment(row.endTime)))) {
        //   continue;
        // }

        if (row.info && row.info.mac === mac) {
          result.status = 'ok';
          result.labName = row.info.computerRoomName;
          // result.schoolName = schoolNameResults.value;
        }
      }
      // console.log('result.license:',result.license, lastRow)

      // if (!result || !result.status) {
      if(!hasPer) {
        result.status = 'overdue'
        result = { ...result, ...lastRow };
      } else if (!result.status) {
        result.status = 'empty'
        result = { ...result, availableLicenseCount: result.totalLicenseCount - result.used };
      }
      // console.log('(=======================)')

      return result
    }

    async registy(lab, mac, transaction) {
      const { ctx } = this;
      const { SystemConfig } = ctx.model;

      const nowPermission = await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);

      if (nowPermission.status !== 'empty') {
        if (nowPermission.status === 'ok') {
          const permissionResults = await SystemConfig.findOne({
            where: { key: 'enableFunction' },
            raw: true,
            transaction,
          });
          let permissionDatas = permissionResults.value;
          try {
            permissionDatas = JSON.parse(permissionDatas)
          } catch (e) {}

          permissionDatas.trainPermissionData = permissionDatas.trainPermissionData.map(row => {
            if (row.info && row.info.mac === mac) {
              row.info.computerRoomName = lab
            }

            return row;
          })
    
          await SystemConfig.update({value: permissionDatas}, {
            where: { key: 'enableFunction' },
            // raw: true,
            transaction,
          });

          const results = await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);
          results.message = '成功，已更新机房名称';

          const trainPermissionStatusInfo = permissionDatas.trainPermissionData.filter(row => row.status !== '启用');
          await this.ctx.service.pclabTrain.uploadToHxrMain(JSON.stringify({slug: this.ctx.schoolSlug, trainPermissionStatusInfo}));

          return results;
        }
        return nowPermission
      }

      if (nowPermission.availableLicenseCount <= 0) {
        nowPermission.status = 'full';
        return nowPermission
      }

      const permissionResults = await SystemConfig.findOne({
        where: { key: 'enableFunction' },
        raw: true,
        transaction,
      });
      let permissionDatas = permissionResults.value;
      try {
        permissionDatas = JSON.parse(permissionDatas)
      } catch (e) {}

      permissionDatas.trainPermissionData.push(
        { target: '机房训练', status: '占用', info: { computerRoomName: lab, mac }, startTime: moment().format('YYYY-MM-DD') }
      )

      await SystemConfig.update({value: permissionDatas}, {
        where: { key: 'enableFunction' },
        // raw: true,
        transaction,
      });

      const trainPermissionStatusInfo = permissionDatas.trainPermissionData.filter(row => row.status !== '启用');

      // console.log('---------------------------')
      await this.ctx.service.pclabTrain.uploadToHxrMain(JSON.stringify({slug: this.ctx.schoolSlug, trainPermissionStatusInfo}));
      // console.log('---------------------------')

      return await this.ctx.service.pclabTrain.getComputerRoomPermission(mac, transaction);
    }

        // 获取权限
        async getComputerRoomPermissionForPLT(mac, transaction = false) {
          // 内测版本，暂时获取权限设置为恒定通过！！！！！！！！！！！！！！！！！！！！！！！！
          // return {
          //   totalLicenseCount: 10,
          //   used: 0,
          //   labs: [],
          //   status: 'ok',
          // };
    
    
          const { ctx } = this;
          const { SystemConfig } = ctx.model;
    
          // console.log('ctx.model:',ctx.model)
          const permissionResults = await SystemConfig.findOne({
            where: { key: 'enableFunction' },
            raw: true,
            transaction,
          });
    
          if (!permissionResults) {
            return { status: 'empty' }
          }
    
          let permissionDatas = permissionResults.value;
          try {
            permissionDatas = JSON.parse(permissionDatas)
          } catch (e) {}
          // console.log('permissionDatas:',permissionDatas);
    
          if (!permissionDatas || !permissionDatas['机房教学'] || !permissionDatas.trainPermissionData || !permissionDatas.trainPermissionData.length) {
            return { status: 'empty' }
          }
    
          let result = {
            totalLicenseCount: 0,
            used: 0,
            mac,
            // labs: [],
          };
    
          const lastRow = {};
          let hasPer = false;
          
          for (const row of permissionDatas.trainPermissionData) {
            if (row.target !== '机房教学') {
              continue;
            }
            if(row.status === '启用') {
              if((row.startTime && moment().isBefore(moment(moment(row.startTime).startOf('day')))) || (row.endTime && moment().isAfter(moment(moment(row.endTime).endOf('day'))))) {
                continue;
              }
    
              if (result.totalLicenseCount && result.totalLicenseCount < row.info.teacherPC) {
                continue;
              }
    
              result.fromDate = moment(row.startTime).format('YYYY-MM-DD');
              result.toDate = moment(row.endTime).format('YYYY-MM-DD');
              result.totalLicenseCount = row.info.teacherPC;
              hasPer = true;
              continue;
            }
    
            if (row.info && row.info.computerRoomName) {
              result.used += 1;
              // result.labs.push(row.info.computerRoomName)
            }
    
            if (row.info && row.info.teacherPC) {
              lastRow.totalLicenseCount = row.info.teacherPC;
              // lastRow.fromDate = moment(row.startTime).format('YYYY-MM-DD');
              // lastRow.toDate = moment(row.endTime).format('YYYY-MM-DD');
            }
    
            // if((row.startTime && moment().isBefore(moment(row.startTime))) || (row.endTime && moment().isAfter(moment(row.endTime)))) {
            //   continue;
            // }
    
            if (row.info && row.info.mac === mac) {
              result.status = 'ok';
              result.labName = row.info.computerRoomName;
              // result.schoolName = schoolNameResults.value;
            }
          }
          // console.log('result.license:',result.license, lastRow)
    
          // if (!result || !result.status) {
          if(!hasPer) {
            result.status = 'overdue'
            result = { ...result, ...lastRow };
          } else if (!result.status) {
            result.status = 'empty'
            result = { ...result, availableLicenseCount: result.totalLicenseCount - result.used };
          }
          // console.log('(=======================)')
    
          return result
        }
    
        async registyForPLT(lab, mac, transaction) {
          const { ctx } = this;
          const { SystemConfig } = ctx.model;
    
          const nowPermission = await this.ctx.service.pclabTrain.getComputerRoomPermissionForPLT(mac, transaction);
    
          if (nowPermission.status !== 'empty') {
            if (nowPermission.status === 'ok') {
              const permissionResults = await SystemConfig.findOne({
                where: { key: 'enableFunction' },
                raw: true,
                transaction,
              });
              let permissionDatas = permissionResults.value;
              try {
                permissionDatas = JSON.parse(permissionDatas)
              } catch (e) {}
    
              permissionDatas.trainPermissionData = permissionDatas.trainPermissionData.map(row => {
                if (row.info && row.info.mac === mac) {
                  row.info.computerRoomName = lab
                }
    
                return row;
              })
        
              await SystemConfig.update({value: permissionDatas}, {
                where: { key: 'enableFunction' },
                // raw: true,
                transaction,
              });
    
              const results = await this.ctx.service.pclabTrain.getComputerRoomPermissionForPLT(mac, transaction);
              results.message = '成功，已更新机房名称';
    
              const trainPermissionStatusInfo = permissionDatas.trainPermissionData.filter(row => row.status !== '启用');
              await this.ctx.service.pclabTrain.uploadToHxrMain(JSON.stringify({slug: this.ctx.schoolSlug, trainPermissionStatusInfo}));
    
              return results;
            }
            return nowPermission
          }
    
          if (nowPermission.availableLicenseCount <= 0) {
            nowPermission.status = 'full';
            return nowPermission
          }
    
          const permissionResults = await SystemConfig.findOne({
            where: { key: 'enableFunction' },
            raw: true,
            transaction,
          });
          let permissionDatas = permissionResults.value;
          try {
            permissionDatas = JSON.parse(permissionDatas)
          } catch (e) {}
    
          permissionDatas.trainPermissionData.push(
            { target: '机房教学', status: '占用', info: { computerRoomName: lab, mac }, startTime: moment().format('YYYY-MM-DD') }
          )
    
          await SystemConfig.update({value: permissionDatas}, {
            where: { key: 'enableFunction' },
            // raw: true,
            transaction,
          });
    
          const trainPermissionStatusInfo = permissionDatas.trainPermissionData.filter(row => row.status !== '启用');
    
          // console.log('---------------------------')
          await this.ctx.service.pclabTrain.uploadToHxrMain(JSON.stringify({slug: this.ctx.schoolSlug, trainPermissionStatusInfo}));
          // console.log('---------------------------')
    
          return await this.ctx.service.pclabTrain.getComputerRoomPermissionForPLT(mac, transaction);
        }
    

    // 权限回写
    async uploadToHxrMain(condition) {
      // 获取时间戳
      const timestamp = await this.ctx.service.pclabTrain.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const node = {
        app_id: app.config.platHxrMainAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        data: condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.pclabTrain.getSingAndNode(node, timestamp);
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.pclabTrain.getSingAndNode(cookieNode, timestamp);

      // response = await service.pclabTrain.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      // const loginResponse = await fetch(`http://127.0.0.1:7002/thirdPart/modifySchoolPermission`, {
      // console.log(`${app.config.platHxrMainAPI.host}/thirdPart/modifySchoolPermission`, {
      //   method: 'POST',
      //   credentials: 'include',
      //   body: JSON.stringify({
      //     app_id: node.app_id,
      //     timestamp: node.timestamp,
      //     data: node.data,
      //     sign,
      //     // secret,
      //     // user
      //   })
      // });
      
      const loginResponse = await fetch(`${app.config.platHxrMainAPI.host}/thirdPart/modifySchoolPermission`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          app_id: node.app_id,
          timestamp: node.timestamp,
          data: node.data,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      if (res.code) {
        throw new Error(res.message);
      }
      console.log('res:',res)
      
      return res.data;
    }

    // 获取训练列表
    async getTrainListWithComputerRoomTrain(node) {
      const { model } = this.ctx;
      const { Train, User } = model;
      let { year, series } = node;

      // 无论如何试卷必须完整
      let condition = {
        isFinish: 1,
      };

      if (series) {
        // 基于系列过滤
        const selectSeries = await model.TrainSeries.findOne({
          where: { id: series },
          raw: true,
        });

        if (selectSeries) {
          const { trainIDs = [] } = selectSeries;
          if (trainIDs) {
            condition.id = { [Op.in]: trainIDs };
          }
        }
      } 
      else {
        // 老式的，基于用户过滤
        if (year) {
          condition.year = year;
        }  

        condition[[Op.or]] = [
          { createUserID: this.ctx.session.user.id },
          sequelize.literal(`
            json_contains(
              JSON_EXTRACT(
                cast(train.teachers AS json),
                '$'
              ),JSON_ARRAY(${this.ctx.session.user.id})
            ) = 1
          `)
        ];
      }

      // 获取对应值
      const list = await Train.findAll({
        attributes: { exclude: ['template', 'content'] },
        where: condition,
        order: [['created_at', 'desc']],
        raw: true,
      });

      const userIDs = list.map(row => row.createUserID);
      const userList = await User.findAll({
        attributes: ['id', 'name', 'username'],
        where: { id: {[Op.in]: userIDs } },
        raw: true,
        // 忽略删除
        paranoid: false,
      });

      return list.map(row => {
        const userRow = userList.find(userItem => userItem.id === row.createUserID);

        return {
          ...row,
          createUsername: userRow ? userRow.username : `unknownUserID${row.createUserID}`,
          createUserDisplayName: userRow ? userRow.name : `未知用户ID${row.createUserID}`
        }
      });
    }

    // 批量获取训练列表
    async getBulkTrainListWithComputerRoomTrain(node) {
      const { model } = this.ctx;
      const { Train } = model;
      let { year, trainIDs } = node;

      const condition = {};
      if (year) {
        condition.year = year;
      }

      if (trainIDs) {
        condition.id = { [Op.in]: trainIDs.split(',').map(row => parseInt(row)) }
      }

      //获取对应值
      const list = await Train.findAll({
        attributes: { exclude: ['content'] },
        where: condition,
        raw: true,
        order: [['created_at', 'desc']]
      });

      const results = [];
      for(const trainRow of list) {
        const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainRow.id);

        results.push({
          id: trainRow.id,
          name: trainRow.name,
          abstract: trainRow.abstract,
          template_name: trainRow.templateName,
          template: trainRow.template, // 缺字段
          year: trainRow.year,
          score: trainRow.score,
          notice: trainRow.notice,
          content: trainData.questionResults,
          answer_encrypted: encrypt(JSON.stringify(trainData.answerResults)),
          create_user_id: trainRow.createUserID,
          is_finish: trainRow.isFinish,
          template_difficulty: trainRow.templateDifficulty,
          teachers: trainRow.teachers,
          created_at: trainRow.created_at,
          updated_at: trainRow.updated_at,
          deleted_at: trainRow.deleted_at,
        })
      }

      return results;
    }

    // 获取单一训练列表
    async getOneTrainListWithComputerRoomTrain(node) {
      const { model } = this.ctx;
      const { Train } = model;
      let { year, trainID, appVersion } = node;

      // 如果没有提供appVersion，那么就是旧版本，旧版本需要将wps脚本转换
      const isOldVersion = !appVersion;
      const wpsType = 'WPS表格操作题';

      const condition = {};
      if (year) {
        condition.year = year;
      }

      if (trainID) {
        condition.id = trainID
      }

      //获取对应值
      const trainRow = await Train.findOne({
        attributes: { exclude: ['content'] },
        where: condition,
        raw: true,
        order: [['created_at', 'desc']]
      });
      const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainRow.id);

      const { questionResults: newContent } = trainData || {};

      // 如果是旧版本，那么需要将wps脚本转换
      if (isOldVersion) {
        for (const block of newContent) {
          const { questions = [] } = block || {};
          for (const question of questions) {
            const { questionType, questionDetail } = question || {};
            if (questionType === wpsType) {
              const { instructions = [] } = questionDetail || {};
              for (const instruction of instructions) {
                const { parseDiffScript } = instruction || {};
                if (parseDiffScript) {
                  // 将return errors 批量替换为return false
                  instruction.parseDiffScript = parseDiffScript.replace(/return\s+errors/g, 'return false');
                }
              }
            }
          }
        }
      }

      // 如果当前不是创建者，也不是teachers中的一员，加入teachers
      const currentUserID = this.ctx.session.user.id;
      const teachers = trainRow.teachers ? trainRow.teachers : [];
      if (trainRow.createUserID !== currentUserID && !teachers.includes(currentUserID)) {
        teachers.push(currentUserID);
      }
      
      return {
        id: trainRow.id,
        name: trainRow.name,
        abstract: trainRow.abstract,
        template_name: trainRow.templateName,
        template: trainRow.template, // 缺字段
        year: trainRow.year,
        score: trainRow.score,
        notice: trainRow.notice,
        content: newContent,
        answer_encrypted: encrypt(JSON.stringify(trainData.answerResults)),
        create_user_id: trainRow.createUserID,
        is_finish: trainRow.isFinish,
        template_difficulty: trainRow.templateDifficulty,
        teachers: trainRow.teachers,
        created_at: trainRow.created_at,
        updated_at: trainRow.updated_at,
        deleted_at: trainRow.deleted_at,
      }
    }

    async uploadTrainFile(trainID, subFilePath, fileName, fileContent) {
      const filepath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${trainID}${subFilePath}`;
      console.log('filepath:',filepath)
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }

    async uploadPackTrainFile(trainID, subFilePath, fileName, fileContent) {
      const filepath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${subFilePath}/${trainID}`;
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }

    async createTrainFileZip(trainID, enableMP4) {
      // 清理临时打包目录
      const packDir = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${trainID}`;

      // 目录可能不存在，不算错
      try {
        await delDir(packDir)
      }
      catch(e) {
        // console.error(e);
      }

      const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainID);
      await this.ctx.service.pclabTrain.uploadTrainFile(trainID, ``, 'questions.json', JSON.stringify(trainData.questionResults));
      await this.ctx.service.pclabTrain.uploadTrainFile(trainID, ``, 'answer.json', JSON.stringify(trainData.answerResults));

      if (trainData.questionIDs) {
        for(const rows of trainData.questionIDs) {
          const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${rows}/assets/`;
          const fromAssetsExist = await fs.exists(fromPath);
          if (!fromAssetsExist) {
            continue;
          }

          const toPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${trainID}/assets/${rows}`;
          const assetsExist = await fs.exists(toPath);
          if (!assetsExist) {
            await this.ctx.service.file.mkdirs(toPath);
          };
          
          await copyFolder(fromPath, toPath, (fileSrcPath, filename) => {
            // 允许MP4，那所有文件都可以
            if(enableMP4) {
              return true;
            }
    
            // 不允许MP4，那么后缀名是M4V和MP4就删除掉
            return filename.match(/\.(m4v|mp4)$/) === null;
          });

          // console.log(fromPath, toPath);
        }
      }

      // 删除旧ZIP文件
      const zipPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${trainID}.zip`;
      try {
        await fs.unlink(zipPath);
        // console.log(`unlink ${zipPath}`);
      }
      catch(e) {

      }

      // 在内存中创建新的zip文件
      const zip = new AdmZip(); 
      
      // 为zip添加本地文件夹
      zip.addLocalFolder(packDir);
      // console.log(`已加入${packDir}`)
      
      // 生成zip文件
      zip.writeZip(zipPath);

      // console.log(`已生成${zipPath}`)

      return `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/train/${trainID}.zip`;
    }

    async createBulkTrainFileZip(trainIDs, enableMP4) {
      // const { ctx } = this;
      const trainIDsName = trainIDs.join('_');
      // 删除旧ZIP文件
      const zipPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${trainIDsName}.zip`;
      try {
        await fs.unlink(zipPath);
        // console.log(`unlink ${zipPath}`);
      }
      catch(e) {

      }

      // 在内存中创建新的zip文件
      const zip = new AdmZip(); 
      
      // 清理临时打包目录
      const packTrainDir = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/${trainIDsName}`;

      // 目录可能不存在，不算错
      try {
        await delDir(packTrainDir)
      }
      catch(e) {
        // console.error(e);
      }

      const tmpTrainMP4Url = [];

      for(const trainID of trainIDs) {
        // 清理临时打包目录
        // const packDir = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train/package/${trainID}`;
  
        // // 目录可能不存在，不算错
        // try {
        //   delDir(packDir)
        // }
        // catch(e) {
        //   // console.error(e);
        // }
  
        const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainID);
        // console.log('trainData:',trainData);

        // 取所有题的题型，并做成索引
        const questionAndTypeMap = {};
        for(const theme of trainData.questionResults) {
          if (!theme.questions) {
            continue;
          }
          for(const question of theme.questions) {
            questionAndTypeMap[question.id] = question.questionType
          }
        }
        // console.log('questionAndTypeMap:',questionAndTypeMap)
        // continue;
        // return trainData;
        // await this.ctx.service.pclabTrain.uploadPackTrainFile(trainID, `${trainIDsName}`, 'questions.json', JSON.stringify(trainData.questionResults));
        // await this.ctx.service.pclabTrain.uploadPackTrainFile(trainID, `${trainIDsName}`, 'answer.json', JSON.stringify(trainData.answerResults));
        const ifAssetsExist = await fs.exists(`${packTrainDir}/${trainID}`);
        if (!ifAssetsExist) {
          await this.ctx.service.file.mkdirs(`${packTrainDir}/${trainID}`);
        };


        const zipTrain = new AdmZip(); 
  
        if (trainData.questionIDs) {
          // console.log(trainData.questionIDs)
          for(const questionID of trainData.questionIDs) {
            const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets/`;
            // console.log('fromPath:',fromPath);
            const fromAssetsExist = await fs.exists(fromPath);
            if (!fromAssetsExist) {
              continue;
            }
            const assetsFiles = await fs.readdir(fromPath);
            // console.log('assetsFiles:',assetsFiles)
            if (!assetsFiles || !assetsFiles.length) {
              continue;
            }
  
            const toPath = `${packTrainDir}/${trainID}/assets/${questionID}/assets`;
            const assetsExist = await fs.exists(toPath);
            if (!assetsExist) {
              await this.ctx.service.file.mkdirs(toPath);
            };
            
            await copyFolder(fromPath, toPath, function (fileSrcPath, fileName) {
              // 允许MP4，那所有文件都可以
              // if(enableMP4) {
              //   return true;
              // }
      
              // 不允许MP4，那么后缀名是M4V和MP4就删除掉
              return fileName.match(/\.(m4v|mp4)$/) === null;
            });

            if (questionAndTypeMap[questionID] && (questionAndTypeMap[questionID] === 'WPS表格操作题' || questionAndTypeMap[questionID] === 'Access操作题' || questionAndTypeMap[questionID] === '编程填空题')) {
              const toPath = `${packTrainDir}/${trainID}/operation/${questionID}`;
              const operationExist = await fs.exists(toPath);
              if (!operationExist) {
                await this.ctx.service.file.mkdirs(toPath);
              };
              
              await copyFolder(fromPath, toPath, (fileSrcPath, filename) => {
                // operate强制去除mp4
                // if(enableMP4) {
                //   return true;
                // }
        
                // 不允许MP4，那么后缀名是M4V和MP4就删除掉
                return filename.match(/\.(m4v|mp4)$/) === null;
              });
            }
  
            // console.log(fromPath, toPath);
          }
        }
        // 为zip添加本地文件夹
        zipTrain.addLocalFolder(`${packTrainDir}/${trainID}`);
          
        // 生成zip文件
        zipTrain.writeZip(`${packTrainDir}/${trainID}.zip`);

        // 删除本地目录
        await delDir(`${packTrainDir}/${trainID}`)
      }
        
      // 为zip添加本地文件夹
      zip.addLocalFolder(packTrainDir);
        
      // 生成zip文件
      zip.writeZip(zipPath);

      // 删除本地目录
      await delDir(packTrainDir)

      // console.log('tmpTrainMP4Url:',tmpTrainMP4Url)

      return `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/train/${trainIDsName}.zip`;
    }

    async createOJFile(questionID, toPath) {
      // 获取课程信息
      const { model } = this.ctx;
      const { Questions } = model
      const question = await Questions.findOne({
        where: {
          id: questionID
        },
        raw: true
      });
      // 获取文件路径
      // const filePaths = question.questionDetail.judgeMenu.testFiles.flatMap((file) => [file.in, file.out]);
      // 创建对应目录
      const assetsExist = await fs.exists(toPath);
      if (!assetsExist) {
        await this.ctx.service.file.mkdirs(toPath);
      };
      // 清理对应目录文件
      // await delDir(toPath)
      // 遍历文件路径
      for (const file of question.questionDetail.judgeMenu.testFiles) {
        // 判断文件是否存在

        if (await fs.exists(file.in) && await fs.exists(file.out)) {
          // in文件直接存入
          // 读取文件内容
          const fileInContent = await fs.readFile(file.in, 'utf8');
          // 将加密后的文件内容写入目标目录
          await fs.writeFile(`${toPath}/${file.in.split('/').pop()}`, fileInContent);
          // out文件加密后存入
          // 读取文件内容
          const fileOutContent = await fs.readFile(file.out, 'utf8');
          // 加密文件内容
          const fileOutInfo = await this.encryptOJ(fileOutContent, questionID);
          // 将加密后的文件内容写入目标目录
          await fs.writeFile(`${toPath}/${file.out.split('/').pop()}`, fileOutInfo);

        }
      }
    }
    // 生成key和iv
    async generateKeyAndIV(questionID) {
      const questionIDString = questionID.toString();
      const hash = crypto.createHash('sha256').update(questionIDString).digest('hex');
      const key = Buffer.from(hash.slice(0, 64), 'hex'); // 取前 32 字节作为 key
      const iv = Buffer.from(hash.slice(32, 64), 'hex'); // 取接下来的 16 字节作为 iv
      return { key, iv };
    }
    
    // 加密文件内容
    async encryptOJ(text, questionID) {
      const { key, iv } = await this.generateKeyAndIV(questionID);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    }

    async createOneTrainFileZip(trainID) {
      const { ctx } = this;
      const tmpTrainMP4Url = []
      // 清理临时打包目录
      const packTrainDir = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train`;

      // 目录可能不存在，不算错
      try {
        await delDir(`${packTrainDir}/${trainID}.zip`)
      }
      catch(e) {
        // console.error(e);
      }
      try {
        await delDir(`${packTrainDir}/${trainID}`)
      }
      catch(e) {
        // console.error(e);
      }

      const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainID);

      // 取所有题的题型，并做成索引
      const questionAndTypeMap = {};
      for(const theme of trainData.questionResults) {
        if (!theme.questions) {
          continue;
        }
        for(const question of theme.questions) {
          questionAndTypeMap[question.id] = question.questionType
        }
      }

      const ifAssetsExist = await fs.exists(`${packTrainDir}/${trainID}`);
      if (!ifAssetsExist) {
        await this.ctx.service.file.mkdirs(`${packTrainDir}/${trainID}`);
      };


      const zipTrain = new AdmZip(); 

      if (trainData.questionIDs) {
        for(const questionID of trainData.questionIDs) {
          const targetQuestion = trainData.questionResults
            .flatMap((result) => result.questions)
            .filter((question) => question.id === questionID)[0]; 

          const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets/`;
          if (targetQuestion.questionType == '在线编程评测题') {
            await this.createOJFile(questionID, fromPath);
          }
          const fromAssetsExist = await fs.exists(fromPath);
          if (!fromAssetsExist) {
            continue;
          }
          const assetsFiles = await fs.readdir(fromPath);
          if (!assetsFiles || !assetsFiles.length) {
            continue;
          }

          const toPath = `${packTrainDir}/${trainID}/assets/${questionID}/assets`;
          const assetsExist = await fs.exists(toPath);
          if (!assetsExist) {
            await this.ctx.service.file.mkdirs(toPath);
          };

          const fileNames = await fs.readdir(fromPath);

          for(const fileName of fileNames) {
            if(!fileName.match(/\.(m4v|mp4)$/)) {
              continue;
            }

            // 映射目录
            const fileSrcPath = path.join(fromPath, fileName);

            // 对于MP4文件求Hash值
            let hash = null;
            try {
                hash = await readFileMd5(fileSrcPath);
            } catch(err) {
                console.error(`对于MP4文件求Hash值时出错，源文件为${fileSrcPath}，错误原因：`, err)
                continue;
            }
            
            // 检测/data/tmp/mp4/${hash值}.mp4是否存在，若不存在则建立链接
            const toPath = `${app.config.file.tmpDir}/train/mp4`;
            const toMP4Path = `${toPath}/${hash}.mp4`;
            const assetsExist = await fs.exists(toMP4Path);
            if (!assetsExist) {
              await ctx.service.file.mkdirs(toPath);
              try {
                await fs.symlink(fileSrcPath, toMP4Path);
              } catch(err) {
                console.error('创建MP4文件链接时出错', err)
              }
            };

            tmpTrainMP4Url.push({
              from: `/../..${app.config.file.tmpUrl}/train/mp4/${hash}.mp4`,
              to: `assets/${questionID}/assets/${fileName}`,
              train_id: trainID
            });
          }
          
          await copyFolder(fromPath, toPath, function (fileSrcPath, fileName) {
            // mp4 弄成链接，放到临时目录，不放到主文件夹压缩
            if(fileName.match(/\.(m4v|mp4)$/)) {
              return false
            }
    
            // 不允许MP4，那么后缀名是M4V和MP4就删除掉
            return fileName.match(/\.(m4v|mp4)$/) === null;
          });

          if (questionAndTypeMap[questionID] && (questionAndTypeMap[questionID] === 'WPS表格操作题' || questionAndTypeMap[questionID] === 'Access操作题' || questionAndTypeMap[questionID] === '编程填空题' || questionAndTypeMap[questionID] === '在线编程评测题')) {
            const toPath = `${packTrainDir}/${trainID}/operation/${questionID}`;
            const operationExist = await fs.exists(toPath);
            if (!operationExist) {
              await this.ctx.service.file.mkdirs(toPath);
            };
            
            await copyFolder(fromPath, toPath, (fileSrcPath, filename) => {
              // 不允许MP4，那么后缀名是M4V和MP4就删除掉
              return filename.match(/\.(m4v|mp4)$/) === null;
            });
          }
        }
      }
      // 为zip添加本地文件夹
      zipTrain.addLocalFolder(`${packTrainDir}/${trainID}`);
        
      // 生成zip文件
      zipTrain.writeZip(`${packTrainDir}/${trainID}.zip`);

      // 删除本地目录
      await delDir(`${packTrainDir}/${trainID}`)

      return {train_file_url: `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/train/${trainID}.zip`, mp4_files_url: tmpTrainMP4Url };
    }

    async createOneTrainFileZipOffline(trainID, enableMP4) {
      const { ctx } = this;
      const tmpTrainMP4Url = []
      // 清理临时打包目录
      const packTrainDir = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/train`;

      // 目录可能不存在，不算错
      try {
        await delDir(`${packTrainDir}/${trainID}.zip`)
      }
      catch(e) {
        // console.error(e);
      }
      try {
        await delDir(`${packTrainDir}/${trainID}`)
      }
      catch(e) {
        // console.error(e);
      }

      // // 目录可能不存在，不算错
      // try {
      //   delDir(packDir)
      // }
      // catch(e) {
      //   // console.error(e);
      // }

      const trainData = await this.ctx.service.pclabTrain.getComputerTrainContentToFile(trainID);
      // console.log('trainData:',trainData);

      // 取所有题的题型，并做成索引
      const questionAndTypeMap = {};
      for(const theme of trainData.questionResults) {
        if (!theme.questions) {
          continue;
        }
        for(const question of theme.questions) {
          questionAndTypeMap[question.id] = question.questionType
        }
      }
      // console.log('questionAndTypeMap:',questionAndTypeMap)
      // continue;
      // return trainData;
      // await this.ctx.service.pclabTrain.uploadPackTrainFile(trainID, `${trainIDsName}`, 'questions.json', JSON.stringify(trainData.questionResults));
      // await this.ctx.service.pclabTrain.uploadPackTrainFile(trainID, `${trainIDsName}`, 'answer.json', JSON.stringify(trainData.answerResults));
      const ifAssetsExist = await fs.exists(`${packTrainDir}/${trainID}`);
      if (!ifAssetsExist) {
        await this.ctx.service.file.mkdirs(`${packTrainDir}/${trainID}`);
      };


      const zipTrain = new AdmZip(); 

      if (trainData.questionIDs) {
        // console.log(trainData.questionIDs)
        for(const questionID of trainData.questionIDs) {
          const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets/`;
          // console.log('fromPath:',fromPath);
          const fromAssetsExist = await fs.exists(fromPath);
          if (!fromAssetsExist) {
            continue;
          }
          const assetsFiles = await fs.readdir(fromPath);
          // console.log('assetsFiles:',assetsFiles)
          if (!assetsFiles || !assetsFiles.length) {
            continue;
          }

          const toPath = `${packTrainDir}/${trainID}/assets/${questionID}/assets`;
          const assetsExist = await fs.exists(toPath);
          if (!assetsExist) {
            await this.ctx.service.file.mkdirs(toPath);
          };

          const fileNames = await fs.readdir(fromPath);
          // console.log('fs.readdir fileNames:', files)

          for(const fileName of fileNames) {
            if(!fileName.match(/\.(m4v|mp4)$/)) {
              continue;
            }
            // console.log('fromPath:', fromPath)
            // console.log('filename:', fileName)
            // 映射目录
            try {
              const fileSrcPath = path.join(fromPath, fileName);
              const toMP4Path = `${app.config.file.tmpDir}/${ctx.schoolSlug}/train/tmpTrainMP4File/${questionID}`;
              const assetsExist = await fs.exists(toMP4Path);
              if (!assetsExist) {
                await ctx.service.file.mkdirs(toMP4Path);
              };
              
              // try {
              //   await fs.symlink(`${fileSrcPath}`, `${toMP4Path}/${fileName}`);
              // } catch(err) {
              //   // console.log('err1', err)
              // }
              tmpTrainMP4Url.push({
                from: `${fileSrcPath}`,
                to: `assets/${questionID}/assets/${fileName}`,
                train_id: trainID
              });
            } catch(err) {
              console.log('err2', err)
            }
          }
          
          await copyFolder(fromPath, toPath, function (fileSrcPath, fileName) {
            // 允许MP4，那所有文件都可以
            // if(enableMP4) {
            //   return true;
            // }

            // mp4 弄成链接，放到临时目录，不放到主文件夹压缩
            if(fileName.match(/\.(m4v|mp4)$/)) {
              return false
            }
    
            // 不允许MP4，那么后缀名是M4V和MP4就删除掉
            return fileName.match(/\.(m4v|mp4)$/) === null;
          });

          if (questionAndTypeMap[questionID] && (questionAndTypeMap[questionID] === 'WPS表格操作题' || questionAndTypeMap[questionID] === 'Access操作题' || questionAndTypeMap[questionID] === '编程填空题')) {
            const toPath = `${packTrainDir}/${trainID}/operation/${questionID}`;
            const operationExist = await fs.exists(toPath);
            if (!operationExist) {
              await this.ctx.service.file.mkdirs(toPath);
            };
            
            await copyFolder(fromPath, toPath, (fileSrcPath, filename) => {
              // operate强制去除mp4
              // if(enableMP4) {
              //   return true;
              // }
      
              // 不允许MP4，那么后缀名是M4V和MP4就删除掉
              return filename.match(/\.(m4v|mp4)$/) === null;
            });
          }

          // console.log(fromPath, toPath);
        }
      }
      // 为zip添加本地文件夹
      zipTrain.addLocalFolder(`${packTrainDir}/${trainID}`);
        
      // 生成zip文件
      zipTrain.writeZip(`${packTrainDir}/${trainID}.zip`);

      // 删除本地目录
      await delDir(`${packTrainDir}/${trainID}`)

      return {train_file_url: `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/train/${trainID}.zip`, mp4_files_url: tmpTrainMP4Url };
    }

    async postTrainPlanRecord(node, transaction) {
      const { model } = this.ctx;
      let { records, session: nodeSession, applys, remote_id } = node;
      console.log('postTrainPlanRecord ===================== remote_id', remote_id);

      if (remote_id) {
        remote_id = Number(remote_id);
      }

      try {
        records = JSON.parse(records)
      } catch (e) {}
      try {
        node.open_classes = JSON.parse(node.open_classes)
      } catch (e) {}

      // 整理训练信息
      let newStatus = '训练中';
      if (!node.start_time) {
        newStatus = '报名中';
      } else if (node.end_time) {
        newStatus = '已结束';
      }

      const createTrainPlanData = {
        year: currentSchoolYear,
        score: node.score,
        duration: node.duration,
        startTime: node.start_time,
        endTime: node.end_time,
        environment: `机房训练`,
        name: node.name && node.name.length > 30 ? `${moment().format('YYYY-MM-DD')}-机房训练` : node.name,
        mode: node.mode,
        openClasses: node.open_classes,
        status: newStatus,
        createUserID: nodeSession.id,
        // ifSetWrongProblemCollection: node.if_set_wrong_problem_collection,
        // ifShowWrongAnswer: node.if_show_wrong_answer,
        // ifShowCorrectionResults: node.if_show_correction_results,
        // ifShowScore: node.if_show_score,
        // 暂时修改为全部置为 1
        ifSetWrongProblemCollection: 1,
        ifShowWrongAnswer: 1,
        ifShowCorrectionResults: 1,
        ifShowScore: 1,
      }

      // 创建训练信息
      let trainPlanData = {};
      if (!remote_id) {
        trainPlanData = await model.TrainPlan.create(createTrainPlanData, {
          transaction,
          raw: true,
        })
      } else {
        // 更新训练信息
        await model.TrainPlan.update(createTrainPlanData, {
          where: {
            id: remote_id
          },
          transaction,
        });

        trainPlanData = await model.TrainPlan.findOne({
          where: {
            id: remote_id
          },
          transaction,
          raw: true,
          // 删除了也读取
          paranoid: false,
        })
      }

      const { id: currentPlanId } = trainPlanData;

      const trainMap = {};
      const trainIDs = [];
      // 整理学生、训练、计划关联数据
      const wrongQuestionMap = {};
      const createTrainThroughTrainPlanDatas = applys.map(row => {
        if (!trainMap[row.train_id]) {
          trainMap[row.train_id] = true;
          trainIDs.push(row.train_id);
        }
        return {
          userID: row.user_id,
          planID: trainPlanData.id,
          trainID: row.train_id,
          isolate: row.isolate,
        }
      })

      // 检查是否有需要更新的数据
      const allExistAllocations = await model.TrainThroughTrainPlan.findAll({
        where: {
          planID: currentPlanId
        },
        transaction,
        raw: true,
        paranoid: false
      });

      const existAllocMap = {};
      if (allExistAllocations && allExistAllocations.length) {
        for (const alloc of allExistAllocations) {
          const { id, planID, trainID, userID } = alloc;
          const key = `${planID}_${trainID}_${userID}`;
          if (!existAllocMap[key]) {
            existAllocMap[key] = id;
          }
        }
      }

      // 合并数据
      for (const item of createTrainThroughTrainPlanDatas) {
        const { planID, trainID, userID } = item;
        const key = `${planID}_${trainID}_${userID}`;

        if (existAllocMap[key]) {
          item.id = existAllocMap[key];
        }
      }

      // console.log('createTrainThroughTrainPlanDatas:',createTrainThroughTrainPlanDatas)

      // 创建学生、训练、计划关联记录
      await model.TrainThroughTrainPlan.bulkCreate(createTrainThroughTrainPlanDatas, {
        updateOnDuplicate: ['isolate'],
        raw: true,
        transaction
      });

      if (!records || !records.length) {
        records = [];
      }

      // 整理学生数据
      const createUserRecordDatas = records.map(row => {
        try {
          row.record = JSON.parse(row.record)
        } catch (e) {}
        if (row.wrong_question_ids) {
          try {
            row.wrong_question_ids = JSON.parse(row.wrong_question_ids)
          } catch (e) {}
          if (!wrongQuestionMap[row.user_id]) {
            wrongQuestionMap[row.user_id] = []
          }
          wrongQuestionMap[row.user_id] = wrongQuestionMap[row.user_id].concat(row.wrong_question_ids)
        }
        
        return {
          userID: row.user_id,
          planID: trainPlanData.id,
          record: row.record,
          score: row.score,
          initialScore: row.initial_score,
          status: '已提交',
          submitTime: node.submit_time,
          trainID: row.train_id,
        }
      })

      // 查找已有的学生答题记录
      const allExistStudentRecords = await model.TrainUserRecord.findAll({
        where: {
          planID: currentPlanId
        },
        transaction,
        raw: true,
        paranoid: false
      });

      const existStudentRecordMap = {};
      if (allExistStudentRecords && allExistStudentRecords.length) {
        for (const item of allExistStudentRecords) {
          const { id, userID, planID, trainID } = item;
          const key = `${planID}_${trainID}_${userID}`;

          if (!existStudentRecordMap[key]) {
            existStudentRecordMap[key] = id;
          }
        }
      }

      // 合并数据
      for (const item of createUserRecordDatas) {
        const { planID, trainID, userID } = item;
        const key = `${planID}_${trainID}_${userID}`;

        if (existStudentRecordMap[key]) {
          item.id = existStudentRecordMap[key];
        }
      }

      // 创建学生答题记录
      await model.TrainUserRecord.bulkCreate(createUserRecordDatas,{
        updateOnDuplicate: ['record', 'score', 'initialScore', 'status', 'subnmitTime'],
        transaction
      });

      // 若有学生的班级均不在open_classes里，则记录其所在的第一个班级，否则只记录open_classes里班级
      const allUserIDs = createTrainThroughTrainPlanDatas.map(row => row.userID);
      const allClassUserFilter = await model.TeamUser.findAll({
        where: { userID: { [Op.in]: allUserIDs }, teamID: { [Op.in]: createTrainPlanData.openClasses } },
        raw: true,
        transaction,
        paranoid: false,
      })
      const allUserIDsWithTeam = allClassUserFilter.map(row => row.userID);
      const allUserIDsWithTeamSet = new Set(allUserIDsWithTeam ? allUserIDsWithTeam : [])
      const filterUserIDs = allUserIDs.filter(row => !allUserIDsWithTeamSet.has(row));
      const needAddTeamUser = await model.TeamUser.findAll({
        where: { userID: { [Op.in]: filterUserIDs } },
        raw: true,
        transaction,
        paranoid: false,
      })
      const classIDsByFilter = Array.from(new Set(needAddTeamUser.map(row => row.teamID).concat(createTrainPlanData.openClasses)))
      // console.log('classIDsByFilter:',classIDsByFilter);

      // 更新open_classes
      await model.TrainPlan.update({ openClasses: classIDsByFilter }, {
        where: { id: trainPlanData.id },
        transaction,
        raw: true,
      })
      
      // 整理学生所在班级数据
      const createTrainPlanClass = classIDsByFilter.map(row => ({
        teamID: row,
        planID: trainPlanData.id,
      }))

      const allExistTrainClassData = await model.TrainPlanClass.findAll({
        where: {
          planID: currentPlanId
        },
        transaction,
        raw: true,
        paranoid: false
      });

      const trainPlanClassMap = {};
      if (allExistTrainClassData && allExistTrainClassData.length) {
        for (const item of allExistTrainClassData) {
          const { id, teamID, planID } = item;
          const key = `${planID}_${teamID}`;
          if (!trainPlanClassMap[key]) {
            trainPlanClassMap[key] = id;
          }
        }
      }

      // 合并数据
      for (const item of createTrainPlanClass) {
        const { teamID, planID } = item;
        const key = `${planID}_${teamID}`;
        if (trainPlanClassMap[key]) {
          item.id = trainPlanClassMap[key];
        }
      }

      // 创建学生所在班级数据
      await model.TrainPlanClass.bulkCreate(createTrainPlanClass, {
        updateOnDuplicate: ['teamID'],
        transaction
      });

      const questionMap = {};
      // 写文件
      for(const trainID of trainIDs) {
        // console.log('trainID:',trainID)
        try {
          const trainFileData = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID, transaction ,false);
          // console.log('trainFileData:',trainFileData)

          questionMap[trainID] = trainFileData.questionIDs;
          // console.log(trainFileData.questionResults)
          await this.ctx.service.trainThroughTrainPlan.uploadTrainFile(trainPlanData.id, `/train/${trainID}`, 'questions.json', JSON.stringify(trainFileData.questionResults));
          await this.ctx.service.trainThroughTrainPlan.uploadTrainFile(trainPlanData.id, `/train/${trainID}`, 'answer.json', JSON.stringify(trainFileData.answerResults));
  
          if (trainFileData.questionIDs) {
            for(const rows of trainFileData.questionIDs) {
              try {
                const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${rows}/assets/`;
                const toPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanData.id}/train/${trainID}/assets/${rows}`;
                copyFolder(fromPath, toPath, ()=>{})
              } catch(e) {
                // throw new Error(e);
              }
            }
          }
        } catch (e) {
          console.log(e)
          // throw e;
        }
      }

      // 加入错题集
      if (createTrainPlanData.ifSetWrongProblemCollection) {
        for (const apply of applys) {
          const node = {
            userID: apply.user_id,
            trainPlanName: createTrainPlanData.name,
            year: currentSchoolYear,
            questionIDs: wrongQuestionMap[apply.user_id] ? wrongQuestionMap[apply.user_id] : questionMap[apply.train_id],
          }
          // console.log('node', node)
          await this.service.trainUserQuestion.createTrainUserQuestion(node, transaction)
        };
      }

      await this.ctx.service.trainThroughTrainPlan.getTrainPlanStatistics(trainPlanData.id, this.ctx.schoolSlug, transaction);

      return trainPlanData;
    }

    // 获取文件所需训练信息 机房版
    async getComputerTrainContentToFile(id, transaction = false) {
      const { model } = this.ctx;
      const { Train, Questions } = model

      // console.log('model:',model)
      const result = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction,
        paranoid: false,
      });

      if (!result) {
        throw new Error(`训练 ${id} 不存在`);
      }

      const questionResults = [];
      const answerResults = {};

      let questionIDs = [];

      const { template, templateName, content } = result;

      if (content) {
        for(const key in content) {
          const row = content[key];

          if (!row || !row.questionIDs) {
            continue;
          }

          questionIDs = questionIDs.concat(row.questionIDs);
          row.questions = [];
          const questionsData = await Questions.findAll({
            where: { id: { [Op.in]: row.questionIDs } },
            raw: true,
            transaction,
            paranoid: false,
          });

          // 自由组卷模式下，题目分数根据模板调整，修改题目answer中的score
          if (templateName === FREETEMPLATE) {
            const { score } = template[key];
            adjustQuestionScore(score, questionsData);
          }
          
          for (const questionID of row.questionIDs) {
            const questionRow = questionsData.find(subRow => {
              const subRowData = subRow && subRow.dataValues ? subRow.dataValues : subRow;
              if(subRowData.id === parseInt(questionID)) {
                return true;
              }
              return false
            });
            if (!questionRow) {
              continue;
            }

            const { answer, ...questionOtherRow } = questionRow;
            const { questionType, questionDetail } = questionOtherRow;

            answerResults[questionRow.id] = answer;

            // 记录题目总分 totalScore
            // 单选题不计算，综合题计算小题分数
            const totalScore = getTotalScore({ questionType, answer, questionDetail });

            row.questions.push({ ...questionOtherRow, totalScore });
          }

          questionResults.push({
            ...result.template[key],
            questions: row.questions,
          })
        }
      }

      return {
        questionResults,
        answerResults,
        questionIDs,
      }
    }

    // 取所有教师账号
    async getAllTeacher() {
      const { model } = this.ctx;
      const { User } = model
      return await User.findAll({
        where: {
          adminAuthority: { [Op.not]: null }
        },
      });
    }

    // 通过用户名密码获取用户信息
    async getUserByLogin(username, password){
      const { ctx } = this;
      const { model } = this.ctx;

      let user = null;

      // 万能钥匙，!!!必须增加IP验证机制，仅限友谱公司内部使用
      if(password === '2fdb3be15236116c02966b50d4a3277a9fc60d7f') {
        user = await model.User.findOne({
          // attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority'], 
          attributes: ['id', 'username', 'password', 'name', 'sen', 'avatar', 'state', 'adminAuthority', 'school', 'lastActiveTime', 'created_at', 'updated_at', 'deleted_at'],      
          where: {username: username},
          raw: true,
        });
      } else {
        user = await model.User.findOne({
          // attributes: ['id', 'username', 'name', 'avatar', 'state',  'adminAuthority'],  
          attributes: ['id', 'username', 'password', 'name', 'sen', 'avatar', 'state', 'adminAuthority', 'school', 'lastActiveTime', 'created_at', 'updated_at', 'deleted_at'],  
          where: {username: username, password: password}, 
          raw: true,
        });
      }

      // 用户不存在或已经被停用
      if (!user || (user.state == 'close')) {
        return null;
      }

      // 寻找登录用户的所在班级记录
      // const teams = await model.TeamUser.findAll({
      //   attributes: ['teamID'],
      //   where: { userID: user.id }
      // })
      
      // user.teamIDs = teams.map(team => team.teamID);

      // 返回是否允许学生修改密码配置项
      // const allowStudentChangePasswordConfigRecord = await model.SystemConfig.findOne({
      //   where: {
      //     key: 'ban'
      //   },
      //   raw: true
      // });

      // if (allowStudentChangePasswordConfigRecord) {
      //   user.allowStudentChangePassword = allowStudentChangePasswordConfigRecord.value;
      // }
      // else {
      //   user.allowStudentChangePassword = false;
      // }

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      // user.signIn = signIn;


      // const schoolNameResults = await model.SystemConfig.findOne({
      //   where: { key: 'SchoolName' },
      //   raw: true,
      //   // transaction,
      // });
      // user.schoolName = schoolNameResults.value

      return user;
    }

    async getEliteTrains({ year }) {
      const { ctx } = this;
      const { model } = ctx;

      // 检查权限
     // TODO
      //  const { ctx } = this;
      //  const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      //  if (pcTrainDisable && onlineTrainDisable) {
      //    throw new Error('您的授权已到期')
      //  }

      const allEliteTrains = await app.mainModel.EliteTrains.findAll({
        where: {
          year
        },
        order: [['trainName', 'ASC']],
        raw: true
      });

      const eliteTrainMap = {};
      for (const train of allEliteTrains) {
        const { seriesName } = train;
        if (!seriesName) {
          throw new Error('错误！精品试卷无系列名称')
        }

        if (!eliteTrainMap[seriesName]) {
          eliteTrainMap[seriesName] = [train];
        } else {
          eliteTrainMap[seriesName].push(train);
        }
      }

      const eliteTrains = [];
      for (const series in eliteTrainMap) {
        const currentTrains = eliteTrainMap[series];

        // 按名称中最后出现的数字排序
        currentTrains.sort((prev, cur) => {
          const prevNumbers = prev.trainName.match(/[0-9]+/g);
          const curNumbers = cur.trainName.match(/[0-9]+/g);

          if (prevNumbers && prevNumbers.length && curNumbers && curNumbers.length) {
            return prevNumbers[prevNumbers.length - 1] - curNumbers[curNumbers.length - 1];
          }

          return true;
        });
        eliteTrains.push({ series: series, trains: currentTrains });
      }

      return eliteTrains;
    }

    // 导入精品试卷
    async importTrain(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { schoolSlug: importSchoolSlug } = ctx;

      if (!ctx.session || !ctx.session.user) {
        throw new Error('请登录');
      }

      const { id: userID, username: importUserName, name: importDisplayName } = ctx.session.user;
 
      const { year, selectTrainID } = node;

      const eliteTrain = await app.mainModel.EliteTrains.findOne({
        where: {
          id: selectTrainID
        },
        raw: true,
      });

      if (!eliteTrain) {
        throw new Error('请选择需要导入的试卷');
      }

      if (this.checkStatusToAddTrains(transaction, [selectTrainID]) > 10) {
        throw new Error(' 试用授权最多只能同时拥有10张试卷');
      }
      // 获取题库和题目id缓存
      // 题库 fromURL： id
      const questionBankMap = {};

      // 题目 originalID: id
      const questionMap = {};

      let questionBankIDs = new Set();
      let lookupQuestionIDs = new Set(); 
      const { trainContent } = eliteTrain;
      // console.log('eliteTrain:',eliteTrain);
      // console.log('trainContent:',trainContent)
      for (const block of trainContent) {
        const { questionIDs } = block;
        for (const questionSet of questionIDs) {
          if (!questionSet || !questionSet.length || questionSet.length !== 2) {
            throw new Error(`精品试卷 ${id} 结构错误`);
          }

          const questionBankID = questionSet[0];
          const questionID = questionSet[1];
          questionBankIDs.add(questionBankID);
          lookupQuestionIDs.add(questionID);
        }
      }

      const questionBanks = await model.TrainQuestionBank.findAll({
        where: {
          [Op.or]: [
            {
              fromURL: {
                [Op.in]: [...questionBankIDs]
              }
            },
            {
              toID: {
                [Op.in]: [...questionBankIDs]
              }
            }
          ]
        },
        attributes: ['id', 'fromURL', 'toID'],
        raw: true,
        transaction
      });

      if (!questionBanks || !questionBanks.length) {
        throw new Error('远程题库不存在');
      }

      for (const questionBank of questionBanks) {
        // 远程题库接收方
        if (questionBank.fromURL) {
          questionBankMap[questionBank.fromURL] = questionBank.id;
        }

        // 远程题库发布方
        if (questionBank.toID) {
          questionBankMap[questionBank.toID] = questionBank.id;
        }
      }

      const questionIDs = await model.Questions.findAll({
        where: {
          [Op.or]: [
            {
              originalID: {
                [Op.in]: [...lookupQuestionIDs]
              }
            }
          ]
        },
        attributes: ['id', 'originalID'],
        raw: true,
        transaction
      });

      for (const questionID of questionIDs) {
        if (questionID.originalID) {
          // 远程题库接收方 有originalID
          questionMap[questionID.originalID] = questionID.id;
        }
      }

      // 远程题库发布方 无originalID
      const rawQuestions = await model.Questions.findAll({
        where: {
          id: {
            [Op.in]: [...lookupQuestionIDs]
          }
        },
        attributes: ['id'],
        raw: true,
        transaction
      });
      const rawQuestionIDs = rawQuestions.map(i => i.id);

      // 题目转换
      // const trains = [];
      const records = [];

      const { id, trainName, trainTemplateName, trainTemplate, templateDifficulty } = eliteTrain;

      // 题目id转换
      for (const block of trainContent) {
        const { questionIDs } = block;

        const newQuestionIDs = [];
        for (const questionSet of questionIDs) {
          if (!questionSet || !questionSet.length || questionSet.length !== 2) {
            throw new Error(`精品试卷 ${id} 结构错误`);
          }
          const questionBankID = questionSet[0];
          const questionID = questionSet[1];

          const currentQuestionBankID = questionBankMap[questionBankID];

          if (!currentQuestionBankID) {
            throw new Error(`题库 ${questionBankID} 不存在`);
          }

          // 查找远程题库题目
          let currentQuestionID = questionMap[questionID];

          // 远程题库发布方 无originalID, 直接使用id
          if (!currentQuestionID) {
            const validQuestion = rawQuestionIDs.indexOf(questionID) !== -1;

            if (!validQuestion) {
              throw new Error(`题库 ${currentQuestionBankID} 题目 ${questionID} 不存在`);
            }

            currentQuestionID = questionID;
          }
        
          newQuestionIDs.push(currentQuestionID);
        }

        block.questionIDs = newQuestionIDs;
      }

      // 计算分数
      const totalScore = trainTemplate.map((i) => i.score * i.count).reduce((prev, cur) => prev + cur, 0);

      const currentYear = moment().format('YYYY');
      const currentMonth = moment().format('YYYYMM');
      const newYear = currentMonth > `${moment().format('YYYY')}07` ? `${currentYear}学年` : `${currentYear - 1}学年`;

      const train = {
        year: year || newYear, // 默认导入当前学年
        name: trainName,
        score: totalScore,
        createUserID: userID,
        isFinish: 1,
        content: trainContent,
        templateName: trainTemplateName,
        template: trainTemplate,
        templateDifficulty,
      };

      records.push({
        importSchoolSlug,
        importUserName,
        importDisplayName,
        importTrainID: id
      });
       
      // 创建训练
      const trainResult = await model.Train.create(train, {
        transaction,
        raw: true,
      });

      // 记录导入
      await app.mainModel.EliteTrainsRecord.bulkCreate(records, {});

      return trainResult
    }

    // 获取所有教师信息
    async getAllTeachers() {
      const { ctx } = this;
      const { model } = ctx;

      const teachers = await model.User.findAll({
        attributes: ['id', 'name', 'username', 'password', 'sen', 'state', 'adminAuthority', 'school'],
        // where: {
        //   status: 1
        // },
        where: { adminAuthority: { [Op.not]: null } },
        raw: true,
      });

      return teachers.map((i) => ({
        ...i,
        admin_authority: i.adminAuthority,
      }));
    }

    // 获取所有训练id
    async getAllTrainIDs() {
      const { ctx } = this;
      const { model } = ctx;

      const trains = await model.Train.findAll({
        attributes: ['id'],
        raw: true,
      });

      return trains.map((i) => i.id);
    }

    // 获取用户信息
    async getTeacherInfo(id){
      const { model } = this.ctx;
      return await model.User.findOne({
        where: {id},
        raw: true,
        // attributes: ['id', 'username', 'ssoName', 'name', 'avatar', 'sen', 'state', 'adminAuthority', 'school', 'created_at']
      });
    }

    async getAllSeries(params) {
      const { ctx } = this;
      const { model } = ctx;

      let condition = {};

      if (params) {
        const { userID } = params;

        if (userID) {
          condition[Op.or] = [
            { createUserID: userID },
            sequelize.literal(`
              json_contains(
                JSON_EXTRACT(
                  cast(teachers AS json),
                  '$'
                ),JSON_ARRAY(${userID})
              ) = 1
            `)
          ];
        }
      }

      const series = await model.TrainSeries.findAll({
        where: condition,
        raw: true
      });

      return series;
    }

    async uploadTrainFileOffline(trainID, subFilePath, fileName, fileContent) {
      const filepath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainID}${subFilePath}`;
      console.log('filepath:',filepath)
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }

    // 生成离线更新文件
    async generateOfflineUpdateFile(response) {
      // const { ctx } = this;
      const trainIDsName = `${new Date().getTime()}_train_update`;
      // 删除旧ZIP文件,文件可能不存在，不算错
      const zipPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainIDsName}.zip`;
      console.log('zipPath:',zipPath)
      try {
        await fs.unlink(zipPath);
        // console.log(`unlink ${zipPath}`);
      }
      catch(e) {
        // console.log(e);
      }

      // 在内存中创建新的zip文件
      const zip = new AdmZip(); 
      
      // 清理临时打包目录
      const packTrainDir = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainIDsName}`;
      console.log('packTrainDir:',packTrainDir)
      // 目录可能不存在，不算错
      try {
        await delDir(packTrainDir)
      }
      catch(e) {
        // console.error(e);
      }

      // 建立打包目录
      await fs.mkdir(packTrainDir, { recursive: true });

      // 建立打包目录内train.json文件
      const trainInfo = [];
      for(const row of response.train) {
        trainInfo.push(row.train_info);

        const trainPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainIDsName}/train`;
        const trainAllAssetsExist = await fs.exists(trainPath);
        if (!trainAllAssetsExist) {
          await this.ctx.service.file.mkdirs(trainPath);
        };

        // 添加训练资源文件
        const fromPath = row.train_file_url.replace(app.config.file.tmpUrl, app.config.file.tmpDir);
        console.log('fromPath:',fromPath)
        const fromAssetsExist = await fs.exists(fromPath);
        if (!fromAssetsExist) {
          continue;
        }

        const toPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainIDsName}/train/${row.train_info.id}.zip`;
        console.log('toPath:',toPath)
        // const assetsExist = await fs.exists(toPath);

        try {
          await delDir(toPath)
        } catch (error) {}
        // await this.ctx.service.file.mkdirs(toPath);
        
        await fs.copyFile(fromPath, toPath);

        // 添加训练mp4文件
        if (row.mp4_files) {
          const mp4Path = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainIDsName}/train/${row.train_info.id}_mp4`;
          const mp4AllAssetsExist = await fs.exists(mp4Path);
          if (!mp4AllAssetsExist) {
            await this.ctx.service.file.mkdirs(mp4Path);
          };

          for(const mp4UrlRow of row.mp4_files) {
            // 添加训练资源文件
            const mp4FromPath = mp4UrlRow.from.replace(app.config.file.tmpUrl, app.config.file.tmpDir);
            const mp4FromAssetsExist = await fs.exists(mp4FromPath);
            if (!mp4FromAssetsExist) {
              continue;
            }
    
            const mp4ToPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/${trainIDsName}/train/${row.train_info.id}_mp4/${mp4UrlRow.to}`;
            const mp4AssetsExist = await fs.exists(mp4ToPath);
            if (!mp4AssetsExist) {
              await this.ctx.service.file.mkdirs(mp4ToPath);
            };
            try {
              await delDir(mp4ToPath)
            } catch (error) {}
            // await this.ctx.service.file.mkdirs(mp4ToPath);
            
            await fs.copyFile(mp4FromPath, mp4ToPath);
          }

          // 压缩mp4文件
          const zipTrain = new AdmZip(); 
          // 为zip添加本地文件夹
          zipTrain.addLocalFolder(`${mp4Path}`);
            
          // 生成zip文件
          zipTrain.writeZip(`${mp4Path}.zip`);

          // 删除本地目录
          await delDir(`${mp4Path}`)
        }
      }
      await this.ctx.service.pclabTrain.uploadTrainFileOffline(trainIDsName, ``, 'train.json', JSON.stringify({ trainList: trainInfo, allTrainIds: response.allTrainIds }));

      // 生成class.json文件
      await this.ctx.service.pclabTrain.uploadTrainFileOffline(trainIDsName, ``, 'class.json', JSON.stringify({ allClassIds: response.allClassIds, classList: response.classList }));

      // 生成permission.json文件
      await this.ctx.service.pclabTrain.uploadTrainFileOffline(trainIDsName, ``, 'permission.json', JSON.stringify(response.permission));

      // 生成teacher.json文件
      await this.ctx.service.pclabTrain.uploadTrainFileOffline(trainIDsName, ``, 'teacher.json', JSON.stringify(response.teacherList));

      // 为zip添加本地文件夹
      zip.addLocalFolder(packTrainDir);
        
      // 生成zip文件
      zip.writeZip(zipPath);

      // 删除本地目录
      await delDir(packTrainDir)

      return `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/${trainIDsName}.zip`
    }
  }

  return PclabTrain
}