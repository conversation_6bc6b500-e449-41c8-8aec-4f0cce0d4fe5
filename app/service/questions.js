const sequelize = require('sequelize');
const { Op, QueryTypes } = require('sequelize');
const levenshtein = require('js-levenshtein');
const fsExtra = require('fs-extra');
const { fs } = require('mz');
const xml2js = require('xml2js');
const md5 = require('md5');
const similarity = require('similarity');

// 时间单位映射
const timeUnitMap = {
  's': 1,
  'm': 60,
  'h': 60 * 60,
};

// 内存单位映射
const memoryUnitMap = {
  'mb': 1,
  'gb': 1024,
}

const languageMap = {
  c: 'C',
  cpp: 'C++',
  py: 'Python',
  pas: 'Pascal',
  java: 'Java',
  cs: 'C#',
  m: 'Obj-C',
  cl: 'CLang',
  cp: 'Clang++',
  js: 'JavaScript',
  go: 'Go',
};

// 可用编程语言
const availableLanguages = ['C', 'C++', 'Python', 'Pascal', 'Java', 'C#', 'Obj-C', 'Clang', 'Clang++', 'JavaScript', 'Go'];

const getParentName = (row, allTags) => {
  if (row.parentID) {
    const parentRow = allTags.find(item => item.id === row.parentID);
    const resultName = getParentName(parentRow, allTags)

    return `${resultName} > ${row.tagName}`
  }

  return `${row.tagName}`
}

// 预编译正则表达式
const DEFAULT_REGEX = /[^\u4e00-\u9fa5]/g;

// 过滤掉汉字、字母、数字、和+-*/[]{}()以外的字符
const filterText = (content = '', len = 100, reg = DEFAULT_REGEX) => {
  if (typeof content !== 'string') return '';
  
  let text = content.replace(reg, '');
  if (len !== 0) {
    return text.length <= len * 2 ? text : (text.slice(0, len) + text.slice(-len));
  }
  return text;
};

//快速字符串长度差异检查函数
const isLengthSimilarOptimized = (curJsonDetail, otherJsonDetail, similar) => {
  const lenDiff = Math.abs(curJsonDetail.length - otherJsonDetail.length);
  const sumLength = curJsonDetail.length + otherJsonDetail.length;
  
  // 避免除零，同时检查长度差异
  // 等价于 diffPercentage = lenDiff / avgLength <= 0.3
  // 转换为 lenDiff <= 0.3 * avgLength
  // 再转换为 lenDiff <= 0.15 * sumLength
  return lenDiff <= sumLength * ((1-similar)/2);
}

// 计算两个单选题的相似度
const findSimilarSingleChoiceQuestionObjects = (data, similar) => {
  const result = []; // 最终结果
  //提前格式化比较内容
  data.forEach(cur => {
    const current = cur;
    const { content: curContent, options: curOptions } = current.questionDetail;
    // 正则表达式：匹配汉字、字母、数字
    // const reg = /[ 　]+/gi;
    current.jsonDetail = filterText(JSON.stringify({
      // content: (curContent || '').slice(0, 10) + (curContent || '').slice(-5),
      content: curContent,
      options: curOptions
    }), 0);
    current.optionsLength = (curOptions || []).length;
    // current.jsonDetail = JSON.stringify({
    //   // content: (curContent || '').slice(0, 10) + (curContent || '').slice(-5),
    //   content: curContent,
    //   options: curOptions
    // }, 0);

    if(current.questionDetail?.options?.[0]?.text) {
      current.firstOption = filterText(current.questionDetail.options[0].text, 0);
    }
    else{
      current.firstOption = '';
    }
  });

  // 遍历当前批次的数据
  for (let i = 0; i < data.length; i++) {
    const current = data[i];
    const curType = current.questionType;
    const curJsonDetail = current.jsonDetail;
    const curOptionsLength = current.optionsLength;
    // 存储当前对象的相似对象
    const similarObjects = {
      ...current,
      others: [],
      corrections: [],
    };
    // 内层循环：比较当前对象与它后面的对象
    for (let j = i + 1; j < data.length; j++) {
      const other = data[j];
      const otherType = other.questionType;
      const otherJsonDetail = other.jsonDetail;
      const otherOptionsLength = other.optionsLength;
      // 计算 JsonDetail 的相似度
      let jsonSimilarity = 0;
      if (curType === otherType) {
        let text = '';
        if(current.questionDetail?.options?.[0]?.text) {
          // const reg = /[ 　]+/gi;
          // text = filterText(current.questionDetail.options[0].text, 0);
          // text = current.questionDetail.options[0].text;
          text = current.firstOption;
          if(
            (curOptionsLength === otherOptionsLength)
            && isLengthSimilarOptimized(curJsonDetail, otherJsonDetail, similar)
            && otherJsonDetail.includes(text)
          ){
            jsonSimilarity = similarity(curJsonDetail, otherJsonDetail);
            // if(i===3){
            //   jsonSimilarity = 0.8;
            // }
            // jsonSimilarity = 0.8;
          }
        }
      }

      // similar 如果相似度大于 0.75
      if (jsonSimilarity >= similar) {
        const matchedOther = {
          ...other,
          similarity: jsonSimilarity.toFixed(2),
          aa: [current.id, curJsonDetail, other.id, otherJsonDetail]
        };
        similarObjects.others.push(matchedOther.id);
        similarObjects.corrections.push(matchedOther);
        // similarObjects.curtext = filterText(curJsonDetail);
        // similarObjects.othtext = filterText(otherJsonDetail);
      }
    }

    // 如果当前对象有相似对象，则加入结果
    if (similarObjects.others.length > 0) {
      result.push(similarObjects);
    }
  }
  
  return result;
}

// 计算两个非单选题的相似度
const findSimilarOtherQuestionObjects = (data, similar) => {
  const result = []; // 最终结果
  //提前格式化比较内容
  data.forEach(cur => {
    const current = cur;
    const {questionType} = current;
    if(questionType === '综合题') {
      if(current.questionDetail && current.questionDetail.length) {
        current.questionDetail.forEach(item => {
          // 如果对象有 solution 属性（题解），则删除它，因为会影响相似度比较
          if (item.hasOwnProperty('solution')) {
            delete item.solution;
          }
        });
      }
      current.jsonDetail = filterText(JSON.stringify(current.questionDetail));
    }
    else if(questionType === 'WPS表格操作题') {
      // 如果对象有 solution 属性（题解），则删除它，因为会影响相似度比较
      if(current.questionDetail && current.questionDetail.hasOwnProperty('solution')) {
         delete current.questionDetail.solution;
      }
      //删除脚本检测代码-parseDiffScript
      if(current.questionDetail && current.questionDetail.instructions && current.questionDetail.instructions.length) {
        current.questionDetail.instructions.forEach(instruction => {
          if(instruction.hasOwnProperty('parseDiffScript')){
            delete instruction.parseDiffScript;
          }
        });
     }
      current.jsonDetail = filterText(JSON.stringify(current.questionDetail));
    }
    else if(questionType === 'Access操作题') {
      // 如果对象有 solution 属性（题解），则删除它，因为会影响相似度比较
      if(current.questionDetail && current.questionDetail.hasOwnProperty('solution')) {
         delete current.questionDetail.solution;
      }
      //删除脚本检测代码-parseDiffScript 文件judgeFiles
      if(current.questionDetail && current.questionDetail.instructions && current.questionDetail.instructions.length) {
        current.questionDetail.instructions.forEach(instruction => {
          if(instruction.hasOwnProperty('parseDiffScript')){
            delete instruction.parseDiffScript;
          }
          if(instruction.hasOwnProperty('judgeFiles')){
            delete instruction.judgeFiles;
          }
        });
     }
      current.jsonDetail = filterText(JSON.stringify(current.questionDetail));
    }
    else { // 其他题型(编程填空题、在线编程测评题)
      if(current.questionDetail && current.questionDetail.hasOwnProperty('solution')) {
         // 如果对象有 solution 属性（题解），则删除它，因为会影响相似度比较
         delete current.questionDetail.solution;
      }
      current.jsonDetail = filterText(JSON.stringify(current.questionDetail));
    }
  });

  // 遍历当前批次的数据
  for (let i = 0; i < data.length; i++) {
    const current = data[i];
    const curType = current.questionType;
    const curJsonDetail = current.jsonDetail;
    // 存储当前对象的相似对象
    const similarObjects = {
      ...current,
      others: [],
      corrections: [],
    };
    // 内层循环：比较当前对象与它后面的对象
    for (let j = i + 1; j < data.length; j++) {
      const other = data[j];
      const otherType = other.questionType;
      const otherJsonDetail = other.jsonDetail;
      // 计算 JsonDetail 的相似度
      let jsonSimilarity = 0;
      if (curType === otherType) {
        jsonSimilarity = similarity(curJsonDetail, otherJsonDetail);
      }

      // similar 如果相似度大于 0.75
      if (jsonSimilarity >= similar) {
        const matchedOther = {
          ...other,
          similarity: jsonSimilarity.toFixed(2),
          aa: [current.id, curJsonDetail, other.id, otherJsonDetail]
        };
        similarObjects.others.push(matchedOther.id);
        similarObjects.corrections.push(matchedOther);
        // similarObjects.curtext = filterText(curJsonDetail);
        // similarObjects.othtext = filterText(otherJsonDetail);
      }
    }

    // 如果当前对象有相似对象，则加入结果
    if (similarObjects.others.length > 0) {
      result.push(similarObjects);
    }
  }
  
  return result;
}

module.exports = app => {

  class QuestionsService extends app.Service {
    // 创建训练
    async createQuestions(node, transaction) {
      const { model } = this.ctx;
      const { Questions, QuestionTag} = model;
      const { tags, mode, ...other } = node;
      
      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      const result = await Questions.create(other, { transaction });
      // console.log('result.dataValues.id:',result.dataValues.id)

      if (tags && tags.length) {
        await QuestionTag.bulkCreate(tags.map(row => ({
          questionID: result.dataValues.id,
          tagID: row,
        })), {
          transaction
        });

        // result.dataValues.tagList = await QuestionTag.findAll({
        //   transaction,
        //   raw: true,
        // });
      }

      return await this.ctx.service.questions.getQuestions(result.id, transaction)
    }

    // 查找重复题目
    async checkDuplicateQuestions(question, transaction = null) {
      const { model } = this.ctx;
      const { Questions } = model;

      const { id, title, questionBankID, questionDetail, questionType } = question;

      if (!questionBankID) {
        throw new Error('请选择题库！');
      }

      const { content } = questionDetail || {};

      const querys = [];

      if (content) {
        querys.push(
          sequelize.where(
            sequelize.col('questionDetail'),
            Op.like,
            `%${content}%`
          ),
        );
      }
      
      if (title) {
        querys.push(
          sequelize.where(
            sequelize.col('questionDetail'),
            Op.like,
            `%${title}%`
          ),
        );
      }

      function getQuerys(type, details = {}) {
        const { options, code, instructions } = details;

        switch (type) {
          case '单选题':
            if (options && options[0] && options[0].text) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${options[0].text}%`
                ),
              );
            }
            break;
          case '综合题':
            if (questionDetail && questionDetail.length) {
              for (const subQuestion of questionDetail) {
                const { questionType: subType, questionDetail: subDetail } = subQuestion;
                getQuerys(subType, subDetail);
              }
            }
            break;
          case '文本':
            if (content) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${content}%`
                ),
              );
            }
            break;
          case '多选题':
            if (content) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${content}%`
                ),
              );
            }

            if (options && options[0] && options[0].text) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${options[0].text}%`
                ),
              );
            }
            break;
          case '填空题':
            if (content) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${content}%`
                ),
              );
            }
            break;
          case '选择填空题':
            if (content) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${content}%`
                ),
              );
            }

            if (options && options[0] && options[0].text) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${options[0].text}%`
                ),
              );
            }
            break;
          case 'WPS表格操作题':
          case 'Access操作题':
            if (instructions && instructions[0] && instructions[0].description) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${instructions[0].description}%`
                ),
              );
            }
  
            break;
          case '编程填空题':
          case '在线编程评测题':
            if (code) {
              querys.push(
                sequelize.where(
                  sequelize.col('questionDetail'),
                  Op.like,
                  `%${code}%`
                ),
              );
            }
            break;
          default:
            throw new Error(`获取题库检索条件，不支持的题型${type}`);
        }
      }

      getQuerys(questionType, questionDetail);

      let findQuestions = [];

      const request = {
        [Op.or]: querys,
        questionType,
        questionBankID,
      };

      if (id) {
        request['id'] = {
          [Op.not]: id,
        }
      }
      
      try {
        findQuestions = await Questions.findAll({
          where: request,
          raw: true,
          transaction,
        });

      } catch (e) {
        throw e;
      }

      if (findQuestions && findQuestions.length) {
        // 重复度计算
        for (const findQuestion of findQuestions) {
          const { id, questionDetail: detail } = findQuestion;

          function getString(obj) {
            let result = '';

            if (!obj) {
              return result;
            }

            // 如果对象有 solution 属性（题解），则删除它，因为会影响相似度比较
            if (obj.hasOwnProperty('solution')) {
              delete obj.solution;
            }

            const keys = Object.keys(obj).sort();
            for (const key of keys) {
              if (typeof obj[key] !== 'object') {
                result += obj[key];
              } else {
                result += getString(obj[key]);
              }
            }

            return result;
          }

          const currentString = getString(questionDetail);
          const nextString = getString(detail);
          const steps = levenshtein(currentString, nextString);
         

          const maxLen = Math.max(currentString.length, nextString.length);
          const similarity = 1 - (steps / maxLen);
          findQuestion.similarity = similarity;

          // console.log(currentString, nextString, steps, similarity, 'similarity')
        }

        // 筛选
        findQuestions = findQuestions.filter(i => i.similarity >= 0.85);
        
        if (findQuestions && findQuestions.length) {
          // 排序
          findQuestions.sort((a, b) => b.similarity - a.similarity);
          const findIDs = findQuestions.map(i => i.id);
          return { duplicate: true, duplicateIDs: findIDs, duplicateQuestions: findQuestions };
        }
      }
    }

    // 修改题目
    async putQuestions(id, node, transaction) {
      const { model } = this.ctx;
      const { Questions, QuestionTag } = model;
      let { tags, ...other } = node;

      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      // TODO 如果修改source和author，检查题库是否自建，如果不是，不允许修改
      
      await Questions.update(other, {
        where: {
          id
        },
        transaction
      });

      // 查找饮用该题目的试卷，刷新试卷对于updated_at
      // 取需要更新的试卷
      const needUpdateTrains = await model.Train.findAll({
        attributes: ['id', 'name'],
        where: sequelize.literal(`
          JSON_CONTAINS(content,JSON_OBJECT('questionIDs', JSON_ARRAY(${id})))
        `),
        transaction,
        raw: true,
      })
      for(const row of needUpdateTrains) {
        // 更新时间
        await model.Train.update(row,{
          where: { id: row.id },
          transaction,
        })
      }

      if (tags === undefined) {
        return await this.ctx.service.questions.getQuestions(id, transaction)
      }

      if (!tags) {
        tags = [];
      }

      const nowTags = await QuestionTag.findAll({
        where: {
          questionID: id,
        },
        transaction,
        raw: true,
      })

      const needAdds = [];
      const needDelete = [];
      nowTags.forEach(element => {
        const findRowIndex = tags.findIndex(row => parseInt(row) === element.tagID);

        if (findRowIndex === -1) {
          needDelete.push(element.tagID)
        }
      });

      tags.forEach(row => {
        const findRowIndex = nowTags.findIndex(element => parseInt(row) === element.tagID);

        if (findRowIndex === -1) {
          needAdds.push(row)
        }
      });

      await QuestionTag.destroy({
        where: {
          questionID: id,
          tagID: { [Op.in]: needDelete } 
        },
        transaction,
      });

      await QuestionTag.bulkCreate(needAdds.map(row => ({
        questionID: id,
        tagID: row,
      })), {
        transaction,
      });

      return await this.ctx.service.questions.getQuestions(id, transaction)
    }

    // 获取训练
    async getQuestions(id, transaction = false) {
      const { model } = this.ctx;
      const { Questions, Train, Tag } = model

      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      const result = await Questions.findOne({
        where: {
          id
        },
        include: [{
          model: model.Tag,
          as: 'tags',
          attributes: ['id', 'tagName', 'tagType', 'parentID', 'originalID'],
        }, {
          model: model.User,
          as: 'createUser',
          attributes: ['id', 'name', 'avatar'],
        }],
        transaction
      });

      const resultData = result.dataValues ? result.dataValues : result;
      const rowTrainsUsed = resultData.trainsUsed ? resultData.trainsUsed.map(row => parseInt(row)) : [];

      const tagAllDatas = await Tag.findAll({
        where: { tagType: 'train' },
        attributes: ['id', 'tagName', 'parentID'],
        raw: true,
        transaction,
      });
      resultData.tags = resultData.tags ? resultData.tags.map(row => {
        const rowData = row.dataValues ? row.dataValues : row;
        // console.log('rowData:',rowData)
        return {
          ...rowData,
          // parentName: getParentName(rowData, tagAllDatas)
        }
      }) : []

      const trainsUsedData = await Train.findAll({
        where: {
          id: { [Op.in]: rowTrainsUsed },
        },
        attributes: ['id', 'name'],
        transaction
      });

      resultData.trainsUsedData = trainsUsedData;
      return resultData
    }

    // 删除训练
    async destoryQuestions(id, transaction = false) {
      const { model } = this.ctx;
      const { Questions, QuestionTag } = model

      // 删除标签关联
      await QuestionTag.destroy({
        where: {
          questionID: id
        },
        transaction
      });

      return await Questions.destroy({
        where: {
          id
        },
        transaction
      });
    }

    // 获取训练列表
    async getQuestionsList(node) {
      const { model } = this.ctx;
      const { Questions } = model;
      let { pageSize, pageNum, createUserID, questionType, difficulty, discriminative, notAllTrainsUsed, notTrainsUsed, search } = node;
      pageNum = pageNum ? parseInt(pageNum, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;

      const condition = {};

      // 创建人
      if (createUserID) {
        condition.createUserID = createUserID;
      }

      // 题目类型
      if (questionType) {
        condition.questionType = questionType;
      }

      // 难度  [0, 1]
      if (difficulty) {
        condition.difficulty = { [Op.between]: difficulty }
      }

      // 区分度  [0, 1]
      if (discriminative) {
        condition.discriminative = { [Op.between]: discriminative }
      }

      condition[Op.and] = [];
      // 未引用
      if (notAllTrainsUsed) {
        condition[Op.and].push(
          {
            [Op.or]: [
              { trainsUsed: JSON.stringify([]) },
              { trainsUsed: { [Op.eq]: null } },
            ]
          }
        );
      } else if (notTrainsUsed) {
        condition.notTrainsUsed = {
          [Op.and]: [
            { [Op.notLike]: `[%${trainsUsed}%]` },
            { [Op.notLike]: `[%${trainsUsed}%,` },
            { [Op.notLike]: `,%${trainsUsed}%]` },
            { [Op.notLike]: `,%${trainsUsed}%,` },
          ]
        }
      }

      // 题干或标签
      if (search) {
        const tags = await model.Tags.findAll({
          where: { tagName: { [Op.like]: `%${search}%` } },
          attributes: ['id'],
          raw: true,
        });
        const tagIDs = tags.map(row => row.id);
        const questionsWithTag = await model.QuestionTag.findAll({
          where: { tagID: { [Op.in]: tagIDs } },
          raw: true,
        })
        const questionIDsWithTag = questionsWithTag.map(row => row.id);

        condition[Op.and].push({
          [Op.or]: [
            { name: { [Op.like]: `%${search}%` } },
            { id: { [Op.in]: questionIDsWithTag } },
          ]
        })
      }

      //获取对应值
      const list = await Questions.findAll({
        where: condition,
        limit: pageSize, 
        offset: (parseInt(pageNum) - 1) * (parseInt(pageSize)),
      });
      
      const total = await Questions.count({
        where: condition
      });

      return { list, total };
    }

    async getQuestionBankStatistics(id) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions } = model;

      const typeCount = await Questions.count({
        where: {
          questionBankID: id,
        },
        group: ['questionType']
      });

      // 命题时间统计
      const timeCount = await model.query(`select DATE_FORMAT(created_at,'%Y-%m') months, count(*) count from train_questions where questionBankID = ${id} group by months`);

      // 知识点难度分布
      const difficultyCount = await model.query(`SELECT difficultyConfig, COUNT(train_questions.id) as questionCount, train_tag.tagName FROM train_questions INNER JOIN train_question_tag ON train_questions.id = train_question_tag.questionID LEFT JOIN train_tag ON train_tag.id = train_question_tag.tagID WHERE train_questions.questionBankID = ${id} GROUP BY train_questions.difficultyConfig, train_tag.id`);

      return {
        typeCount,
        timeCount: timeCount[0],
        difficultyCount: difficultyCount[0],
      };
    }

     // 记录题库勘误
     async createQuestionCorrect(data, transaction) {
      const { ctx } = this;
      const { model, session, schoolSlug } = ctx;
      const { TrainQuestionCorrect, TrainQuestionBank } = model;
      const { mainModel } = app;
      const { questionBankID, questionID, originalID, applyReason, title, questionType, questionDetail, answer, difficultyConfig, allTags, acceptUpdate } = data;
      
      const { user } = session;
      if (!user) {
        throw new Error('请先登录');
      }
      const { name, username } = user;

      // 校验题库类型
      const currentQuestionBank = await TrainQuestionBank.findOne({
        where: {
          id: questionBankID
        },
        attributes: ['ifSelfBuilding', 'toID', 'fromURL'],
        raw: true,
      });

      if (!currentQuestionBank) {
        throw new Error(`题库不存在`);
      }

      const { ifSelfBuilding } = currentQuestionBank;
      if (ifSelfBuilding) {
        throw new Error('非远程题库，不记录题目修改');
      }

      const applyTime = Date.now();
      const applyUserName = username;
      const applyDisplayName = name;

      const currentSchool = await mainModel.School.findOne({
        where: {
          slug: schoolSlug
        },
        attributes: ['fullName'],
        raw: true,
      });
      if (!currentSchool) {
        throw new Error(`未查询到当前学校记录 ${schoolSlug} `);
      }
      const { fullName } = currentSchool;

      // 查询远程题库
      const { fromURL } = currentQuestionBank;
      const questionRecord = await ctx.service.thirdPart.thirdPartFetch(
        // 'thridParty/getAllCorrectQuestions', // !!TEST 本地测试
        'management-api/thridParty/getAllCorrectQuestions',
        {
          toID: fromURL
        }
      );

      if (questionRecord.code !== 0) {
        throw new Error(questionRecord.message);
      }

      const { data: currentRecord } = questionRecord;
      const { schoolSlug: reviewSchoolSlug, fromID } = currentRecord;

      const remoteSchool = await mainModel.School.findOne({
        where: {
          slug: reviewSchoolSlug,
        },
        attributes: ['slug', 'fullName', 'dbConfig'],
        raw: true,
      });

      if (!remoteSchool) {
        throw new Error(`远程题库学校 ${reviewSchoolSlug} 不存在`);
      }

      const { fullName: reviewSchoolName } = remoteSchool;

      // 记录数据
      const currentStatus = '待审核';
      const newData = {
        questionBankID,
        remoteQuestionBankID: fromID,

        applyUserName,
        applyDisplayName,
        applyTime,
        applySchoolSlug: schoolSlug,
        applySchoolName: fullName,
        applyReason,

        reviewSchoolSlug,
        reviewSchoolName,

        questionID,
        originalID,
        title,
        questionType,
        questionDetail,
        answer,
        difficultyConfig,

        tags: allTags,
        status: currentStatus,
        acceptUpdate,
      };

      // ******************记录本校勘误*******************
      const exist = await TrainQuestionCorrect.findOne({
        where: {
          questionID,
          originalID,
        },
        raw: true,
        transaction
      });

      if (exist) {
        await TrainQuestionCorrect.update(newData, {
          where: {
            questionID,
            originalID,
          },
          transaction
        });
      } else {
        await TrainQuestionCorrect.create(newData, {
          transaction
        });
      }

      // 如果远程题库为本校发布，不重复记录
      if (reviewSchoolSlug === schoolSlug) {
        return;
      }
      
      // 在远程题库发布学校数据库记录勘误
      const { dbConfig } = remoteSchool;
      if (!dbConfig) {
        throw new Error(`远程题库学校 ${reviewSchoolSlug} 无法获取数据库配置`);
      }

      // 连接学校数据库
      const sequelizeNode = new app.Sequelize(reviewSchoolSlug, dbConfig.username, dbConfig.password, { ...app.config.sequelize, ...dbConfig });

      let questionBank = await sequelizeNode.query(
        `SELECT id from train_question_bank WHERE id = ? AND deleted_at is null;`,
        { type: QueryTypes.SELECT, replacements: [fromID] },
      );

      if (!questionBank || !questionBank.length) {
        throw new Error(`学校${reviewSchoolSlug} 远程题库 ${fromID} 不存在`)
      }

      const { STRING, INTEGER, TEXT, DATE, JSON, BOOLEAN } = sequelize;
      const TrainQuestionCorrectRemote = sequelizeNode.define('train_question_correct', {
        questionBankID: { type: INTEGER, comment: '题库ID' },
        remoteQuestionBankID: { type: INTEGER, comment: '远程题库ID' },

        applyDisplayName: { type: STRING(32), comment: '修改人昵称' },
        applyUserName: { type: STRING(32), comment: '修改人用户名' },
        applySchoolName: { type: STRING(128), comment: '修改人学校名称' },
        applySchoolSlug: { type: STRING(16), comment: '修改人学校slug' },
        applyReason: { type: TEXT, comment: '送审理由' },
        applyTime: { type: DATE, comment: '送审时间' },
    
        reviewDisplayName: { type: STRING(32), comment: '审核人昵称' },
        reviewUserName: { type: STRING(32), comment: '审核人用户名' },
        reviewSchoolName: { type: STRING(128), comment: '审核人学校名称' },
        reviewSchoolSlug: { type: STRING(16), comment: '审核人学校slug' },
        reviewReason: { type: TEXT, comment: '审核备注' },
        reviewTime: { type: DATE, comment: '审核时间' },
    
        questionID: { type: INTEGER, comment: '题目ID' },
        originalID: { type: INTEGER, comment: '发布方题目对应ID' },
        questionType: { type: STRING(16), comment: '题目类型' },
        title: { type: STRING(128), comment: '标题' },
        questionDetail: { type: JSON, comment: '题目详情' },
        answer: { type: JSON, comment: '题目答案' },
        difficultyConfig: { type: STRING(16), comment: '难度设置' },
        tags: { type: JSON, comment: '标签' },
    
        status: { type: STRING(16), comment: '审核状态：待审核/审核通过/审核不通过' },
        acceptUpdate: { type: BOOLEAN, comment: '本题是否接收更新' },
      }, {
        comment: '题库勘误',
        paranoid: true,
        underscored: false,
        freezeTableName: true, // Model tableName will be the same as the model name
        createdAt: 'created_at',
        updatedAt: 'updated_at',
        deletedAt: 'deleted_at'
      });

      // 记录错题勘误
      const existRemote = await TrainQuestionCorrectRemote.findOne({
        where: {
          applySchoolSlug: schoolSlug,
          questionID,
          originalID,
        },
        raw: true,
      });

      if (existRemote) {
        await TrainQuestionCorrectRemote.update(newData, {
          where: {
            questionID,
            originalID,
            applySchoolSlug: schoolSlug,
          },
        });
      } else {
        await TrainQuestionCorrectRemote.create(newData, {
        });
      }
    }

    // 获取题目勘误
    async getAllCorrectQuestions(id) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainQuestionBank, TrainQuestionCorrect, Questions } = model;

      // 获取题库类型
      const currentQuestionBank = await TrainQuestionBank.findOne({
        where: {
          id: id
        },
        attributes: ['ifSelfBuilding', 'toID'],
        raw: true,
      });

      if (!currentQuestionBank) {
        throw new Error(`题库不存在`);
      }

      const { ifSelfBuilding, toID } = currentQuestionBank;

      let allQuestions = [];

      // 学校自建题库
      if (ifSelfBuilding && !toID) {
        return allQuestions;
      }

      // 远程题库，获取题库题目勘误记录
      if (!ifSelfBuilding) {
        allQuestions = await TrainQuestionCorrect.findAll({
          where: {
            questionBankID: id
          },
          raw: true
        });

        return allQuestions;
      }

      // 远程题库发布方
      if (toID) {
        // 查询远程题库题目
        // 全部题目记录
        let correctQuestions = await TrainQuestionCorrect.findAll({
          where: {
            remoteQuestionBankID: id,
            status: '待审核',
          },
          raw: true
        });

        const allOriginalIDs = [... new Set(correctQuestions.map(i => i.originalID))];
        const originQuestions = await Questions.findAll({
          where: {
            id: {
              [Op.in]: allOriginalIDs
            }
          },
          include: [{
            model: model.Tag,
            as: 'tags',
            attributes: ['id', 'tagName', 'tagType', 'parentID', 'originalID'],
          }],
        });

        // 聚合原题与勘误
        const correctMap = {};
        for (const correctQuestion of correctQuestions) {
          let { originalID } = correctQuestion;
          originalID = parseInt(originalID, 10);
          if (!correctMap[originalID]) {
            correctMap[originalID] = [correctQuestion];
          } else {
            correctMap[originalID].push(correctQuestion);
          }
        }

        for (const originQuestion of originQuestions) {
          const { id } = originQuestion;
          originQuestion.dataValues ? originQuestion.dataValues.corrections = correctMap[id] : originQuestion.corrections = correctMap[id];
        }

        return originQuestions;
      }
    }

    // 获取题库中重复的题库
    async getTrainDuplicateQuestions(id, type, similar) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions } = model;

      const results = await Questions.findAll({
        // attributes: ['id', 'questionDetail'], // 指定查询的字段
        where: {
          questionBankID: id,
          // id: {
          //   [Op.in]: [4350, 4504, 5824] 
          // },
          [Op.or]: [
            // { questionType: '单选题' }, // 筛选单选题
            { questionType: type }  // 筛选综合题
            // { questionType: '编程填空题' }, // 筛选单选题
          ],
          // questionType: {
          //   [Op.ne]: '单选题' // 排除 questionType 为 '单选题' 的记录
          // },
          deleted_at: null, // 添加条件，筛选未被删除的题目
          questionDetail: {
            [Op.ne]: null // 过滤掉 questionDetail 为 null 的记录
          },
        },
        // limit: 1000, // 每页 *条
        raw: true // 返回纯 JSON 对象
      });

      // 执行并输出结果
      const resultSims = type === '单选题' ? findSimilarSingleChoiceQuestionObjects(results, similar) : findSimilarOtherQuestionObjects(results, similar);

      return resultSims;
    }

    // 是否接收远程题库此题更新
    async updateCorrectQuestion(node, transaction) {
      const { model } = this.ctx;
      const { TrainQuestionCorrect } = model;
      const { questionID, originalID } = node;

      const condition = {
        questionID,
        originalID,
      };

      const exist = await TrainQuestionCorrect.findOne({
        where: condition,
        raw: true,
        transaction
      });

      if (!exist) {
        throw new Error('未获取到当前题目修改记录')
      }

      await TrainQuestionCorrect.update(node, {
        where: condition,
        transaction
      });

      return await TrainQuestionCorrect.findOne({
        where: condition,
        raw: true,
        transaction
      });
    }
    
    // 更新学校题库勘误审核结果
    async updateSchoolCorrection(info, transaction = null) {
      const { TrainQuestionCorrect } = this.ctx.model;
      const { applySchoolSlug, reviewDisplayName, reviewUserName, reviewSchoolName, reviewSchoolSlug, reviewReason, reviewResult, reviewTime, id, originalID, questionID } = info;

      const schoolInfo = await app.mainModel.School.findOne({
        where: {
          slug: applySchoolSlug,
        },
        attributes: ['slug', 'fullName', 'dbConfig'],
        raw: true,
        transaction,
      });

      if (!schoolInfo) {
        throw new Error(`未查询到学校 ${slug} ${fullName}!`)
      }

      const { dbConfig, slug, fullName } = schoolInfo;

      if (!dbConfig) {
        throw new Error(`未查询到学校 ${slug} ${fullName} 数据库配置`)
      }

      const sequelizeNode = new app.Sequelize(slug, dbConfig.username, dbConfig.password, { ...app.config.sequelize, ...dbConfig });

      const terms = ['reviewDisplayName', 'reviewUserName', 'reviewSchoolName', 'reviewSchoolSlug', 'reviewReason', 'reviewTime', 'status'].map(i => `${i} = ?`);
      const query = `UPDATE train_question_correct SET ${terms.join(', ')} WHERE originalID = ? AND questionID = ?;`;

      // 申请学校更新
      await sequelizeNode.query(
        query,
        { type: QueryTypes.UPDATE, replacements: [reviewDisplayName, reviewUserName, reviewSchoolName, reviewSchoolSlug, reviewReason, reviewTime, reviewResult, originalID, questionID] },
      );

      // 审核学校更新
      await TrainQuestionCorrect.update({
        reviewDisplayName,
        reviewUserName,
        reviewReason,
        reviewTime,
        status: reviewResult,
      }, {
        where: {
          applySchoolSlug,
          originalID,
          questionID,
        }
      })
    }

    async reviewQuestionCorrect(node, transaction) {
      const { ctx } = this;
      const { model, schoolSlug: reviewSchoolSlug } = ctx;
      const { Questions } = model;
      const { reviewQuestion, reviewResult, reviewReason } = node;

      const { questionID, applySchoolSlug } = reviewQuestion;

      // 更新学校题库勘误审核结果
      await ctx.service.questions.reviewQuestionCorrectManual({ reviewQuestion: [reviewQuestion], reviewResult, reviewReason });

      // 如果审核通过，更新题库题目
      if (reviewResult !== '审核通过') {
        return;
      }

      const { originalID, questionDetail, answer, title, difficultyConfig, tags } = reviewQuestion;
      const currentQuestion = await Questions.findOne({
        where: {
          id: originalID
        },
        raw: true,
        transaction
      });

      if (!currentQuestion) {
        throw new Error(`题目id为 ${originalID} 的题目不存在`);
      }

      await Questions.update({
        questionDetail,
        answer,
        title,
        difficultyConfig,
      }, {
        where: {
          id: originalID
        },
        transaction
      });

      // TODO 更新标签

      // 更新assets文件
      const remoteDir = `${app.config.file.dir}/${applySchoolSlug}/trainQuestion/${questionID}/assets/`;
      const currentDir = `${app.config.file.dir}/${reviewSchoolSlug}/trainQuestion/${originalID}/assets/`;
      const tmpDir = `/data/tmp/trash/${reviewSchoolSlug}/trainQuestion/${originalID}/assets/`;

      const dirs = [tmpDir, remoteDir, currentDir];

      for (const dir of dirs) {
        const exist = await fs.exists(dir);
        if (!exist) {
          await ctx.service.file.mkdirs(dir);
        }
      }

      try {
        // move current to tmp
        await fsExtra.move(currentDir, tmpDir, { overwrite: true })
                .then(() => console.log(`File from ${currentDir} moved to the destination ${tmpDir} folder successfully`))
                .catch((e) => console.log(e, 'move'));

        // copy remote to current
        await fsExtra.copy(remoteDir, currentDir)
                .then(() => console.log(`File from ${remoteDir} copy to the destination ${currentDir} folder successfully`))
                .catch((e) => console.log(e, 'copy'));
        
        // remove tmp
        await fsExtra.remove(tmpDir)
                .then(() => console.log(`File ${tmpDir} removed successfully`))
                .catch((e) => console.log(e, 'remove'));
      } catch (e) {
        console.error(e, 'reviewQuestionCorrect');
        throw e;
      }
    }

    async reviewQuestionCorrectManual(node, transaction = null) {
      const { ctx } = this;
      const { session, schoolSlug: reviewSchoolSlug } = ctx;
      const { reviewQuestion, reviewResult, reviewReason } = node;

      // 审核结果回写数据库
      const { user } = session;
      if (!user) {
        throw new Error('请登录后重试');
      }

      const { username: reviewUserName, name: reviewDisplayName } = user;

      const currentSchool = await app.mainModel.School.findOne({
        where: {
          slug: reviewSchoolSlug
        },
        attributes: ['fullName'],
        raw: true,
        transaction,
      });

      if (!currentSchool) {
        throw new Error(`未查询到学校 ${reviewSchoolSlug} !`)
      }

      const { fullName: reviewSchoolName } = currentSchool;

      // 更新学校题库勘误审核结果
      for (const question of reviewQuestion) {
        const { id, applySchoolSlug, originalID, questionID } = question;

        const reviewTime = new Date();
        await ctx.service.questions.updateSchoolCorrection({ applySchoolSlug, reviewDisplayName, reviewUserName, reviewSchoolName, reviewSchoolSlug, reviewReason, reviewResult, reviewTime, id, originalID, questionID }, transaction);
      }
    }

    // 复制题目
    async copyQuestion(questionID, questionBankID, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions, TrainQuestionBank, QuestionTag } = model;

      const currentQuestion = await Questions.findOne({
        where: {
          id: questionID
        },
        raw: true,
        transaction
      });

      if (!currentQuestion) {
        throw new Error(`题目 ${questionID} 不存在`); 
      }

      const { id, questionBankID: prevQuestionBankID,  ...info } = currentQuestion;

      // 检查是否为自建题库
      const questionBank = await TrainQuestionBank.findOne({
        where: {
          id: prevQuestionBankID
        },
        raw: true,
        transaction
      });

      if (!questionBank) {
        throw new Error(`题目 ${questionID} 题库不存在`)
      }

      if (!questionBank.ifSelfBuilding) {
        throw new Error(`题目 ${questionID} 题库 ${prevQuestionBankID} 非自建题库, 不可编辑`);
      }

      const newQuestion = await Questions.create({
          ...info,
          questionBankID
        },
        {
          transaction
        }
      );

      const { id: newID } = newQuestion;

      // 复制资源文件
      const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets`;
      const newDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${newID}/assets`;

      const pathExist = await fs.exists(pathDir);
      if (pathExist) {
        await fsExtra.copy(pathDir, newDir)
          .then(() => {
            console.log(`${pathDir} copied to ${newDir} successfully`);
          })
          .catch(e => console.error('copyQuestion error', e));
      }

      // 复制标签
      const questionTags = await QuestionTag.findAll({
        where: {
          questionID,
        },
        raw: true,
        transaction
      });

      if (!questionTags || !questionTags.length) {
        return newQuestion;
      }

      const newTags = questionTags.map(i => { return { tagID: i.tagID, questionID: newID } });
      await QuestionTag.bulkCreate(newTags, {
        transaction
      });

      return newQuestion;
    }

    // 跨题库复制题目
    async copyQuestionCrossBank(questionID, questionBankID, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions, TrainQuestionBank, QuestionTag } = model;

      const currentQuestion = await Questions.findOne({
        where: {
          id: questionID
        },
        raw: true,
        transaction
      });

      if (!currentQuestion) {
        throw new Error(`题目 ${questionID} 不存在`); 
      }

      const { id, ...info } = currentQuestion;

      const newQuestion = await Questions.create({
          ...info,
          questionBankID
        },
        {
          transaction
        }
      );

      const { id: newID } = newQuestion;

      // 复制资源文件
      const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}/assets`;
      const newDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${newID}/assets`;

      const pathExist = await fs.exists(pathDir);
      if (pathExist) {
        await fsExtra.copy(pathDir, newDir)
          .then(() => {
            console.log(`${pathDir} copied to ${newDir} successfully`);
          })
          .catch(e => console.error('copyQuestion error', e));
      }

      // 复制标签
      const questionTags = await QuestionTag.findAll({
        where: {
          questionID,
        },
        raw: true,
        transaction
      });

      if (!questionTags || !questionTags.length) {
        return newQuestion;
      }

      const newTags = questionTags.map(i => { return { tagID: i.tagID, questionID: newID } });
      await QuestionTag.bulkCreate(newTags, {
        transaction
      });

      return newQuestion;
    }

    // 批量修改题目状态
    async changeQuestionStatus(selectQuestionIDs, status, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions } = model;

      await Questions.update(
        {
          status
        }, {
          where: {
            id: {
              [Op.in]: selectQuestionIDs
            }
          },
          transaction
        }
      );
    }

    // 批量修改题目标签
    async modifyQuestionTags({ selectQuestionIDs, modifyTagType, allSelectTagIDs }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { QuestionTag } = model;

      // alter 修改标签，删除所有题目标签记录，再写入新记录
      if (modifyTagType === 'alter') {
        await QuestionTag.destroy({
          where: {
            questionID: {
              [Op.in]: selectQuestionIDs
            }
          },
          transaction
        });

        let createQuestionTags = [];
        for (const questionID of selectQuestionIDs) {
          for (const tagID of allSelectTagIDs) {
            createQuestionTags.push({ questionID, tagID });
          }
        }
        await QuestionTag.bulkCreate(createQuestionTags, {
          transaction
        });
      }

      // add 添加标签，获取题目标签记录，如果有此标签，跳过；如果没有此标签，添加记录
      if (modifyTagType === 'add') {
        const existTags = await QuestionTag.findAll({
          where: {
            questionID: {
              [Op.in]: selectQuestionIDs
            }
          },
          transaction,
          raw: true,
        });

        let createQuestionTags = [];
        for (const questionID of selectQuestionIDs) {
          const existRecords = existTags.filter(i => i.questionID === questionID);
          if (!existRecords || !existRecords.length) {
            for (const tagID of allSelectTagIDs) {
              createQuestionTags.push({ questionID, tagID });
            }
            continue;
          }
          
          for (const tagID of allSelectTagIDs) {
            const existTagRecord = existRecords.find(i => i.tagID === tagID);
            if (!existTagRecord) {
              createQuestionTags.push({ questionID, tagID });
            }
          }
        }

        await QuestionTag.bulkCreate(createQuestionTags, {
          transaction
        });
      }
    }

    // 检查引用此题目的所有试卷
    async checkQuestionReferInTrains(questionID) {
      const { ctx } = this;
      const { model } = ctx;

      if (!questionID) {
        throw new Error('请提供题目ID');
      }

      let referredTrains = [];
      // SELECT * FROM `train` WHERE JSON_CONTAINS(`content`, JSON_OBJECT('questionIDs', 2519)) AND `deleted_at` is null
      const checkResult = await model.query(`SELECT * FROM train WHERE JSON_CONTAINS(content, JSON_OBJECT('questionIDs', ${questionID})) AND deleted_at is null`);
      if (checkResult && checkResult.length) {
        referredTrains = checkResult[0];
      }

      return referredTrains;
    }

    // 删除题目及题目在试卷中的引用
    async removeQuestionInTrains({ trainIDs, deleteQuestionID }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Train } = model;

      // 删除题目
      if (!deleteQuestionID) {
        throw new Error('请提供需要删除的题目id');
      }

      await ctx.service.questions.destoryQuestions(deleteQuestionID, transaction);

      if (!trainIDs || !trainIDs.length) {
        return;
      }

      // 移除试卷中对此题的引用
      const trains = await Train.findAll({
        where: {
          id: {
            [Op.in]: trainIDs
          }
        },
        raw: true,
        transaction
      });

      if (!trains || !trains.length) {
        throw new Error(`未查询到试卷信息 ${trainIDs.join(', ')}`);
      }

      for (const train of trains) {
        const { content = [] } = train;
        const referredBlockIndex = content.findIndex(i => i && i.questionIDs && i.questionIDs.indexOf(deleteQuestionID) !== -1);
        const referredBlock = content[referredBlockIndex].questionIDs;
        content[referredBlockIndex].questionIDs.splice(referredBlock.findIndex(i => i === deleteQuestionID), 1);

        train.isFinish = false;
      }

      await Train.bulkCreate(trains, {
        updateOnDuplicate: ['content', 'isFinish'],
        transaction
      });
    }

    // 批量根据题目id修改题解
    async updateBulkQuestionSolution(nodes, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      // 0. 建立ID数组和题解映射表
      const idSolutionMap = {};
      const questionIDs = [];

      for (const node of nodes) {
        const { id, solution } = node;
        idSolutionMap[id] = solution;
        questionIDs.push(id);
      }
      
      // 1. 取出本次需要的更新的单选题、编程填空题题型
      const questions = await model.Questions.findAll({
        attributes: ['id', 'questionType', 'questionDetail'],
        where: {
          id: {
            [Op.in]: questionIDs
          },
          questionType: {
            [Op.in]: ['单选题', '编程填空题']
          },
          questionDetail: {
            [Op.not]: null
          }
        },
        raw: true,
        transaction
      });

      // 2. 题解的存储的位置在questionDetail JSON字段里的solution中。
      const updatedQuestions = [];
      for (const question of questions) {
        const { id, questionDetail } = question;

        // // 3. 现状的题目分析字段必须为空或者空字符串，否则不更新。
        // // 目的避免老师已有的题解被覆盖。
        // const { solution } = questionDetail;
        // if (solution && solution.trim()) {
        //   continue;
        // }

        // 3. 本次更新的题解字段前加上：
        // ！！！ 请注意：以下题解由大语言模型输出，仅供参考，欢迎勘误！！！
        const newQuestionDetail = { 
          ...questionDetail, 
          // solution: `！！！ 请注意：以下题解由大语言模型输出，仅供参考，欢迎勘误！！！\n\n${idSolutionMap[id]}`
          solution: idSolutionMap[id]
        };

        updatedQuestions.push({ id, questionDetail: newQuestionDetail });
      }

      // 4. 批量更新题目题解
      await model.Questions.bulkCreate(updatedQuestions, {
        updateOnDuplicate: ['questionDetail'],
        transaction
      });
    }

    // 获取指定试卷来源中全部试卷的全部不重复题目ID
    async getTrainsQuestionIDsInSourceSeries(sourceSeriesID, questionIDSet = new Set(), transaction = null) {
      const { ctx } = this;
      const { model } = ctx;

      // 查询该系列中所有试卷ID
      const sourceSeries = await model.TrainSeries.findOne({
        where: { id: sourceSeriesID },
        attributes: ['trainIDs'],
        raw: true,
        transaction
      });

      const trainIDs = sourceSeries.trainIDs.length ? sourceSeries.trainIDs : [];
      
      // 查询该系列中所有试卷中包含的题目ID
      if(trainIDs.length) {
        const trains = await model.Train.findAll({
          where: {
            id: { [Op.in]: trainIDs },
          },
          attributes: ['content'],
          raw: true,
          transaction
        });

        for (const train of trains) {
          const bigQuestions = train.content;

          if(bigQuestions && bigQuestions.length) {
            for (const bigQuestion of bigQuestions) {
              const questionIDs = bigQuestion.questionIDs;
              questionIDs.forEach(row => questionIDSet.add(row));
            }
          }
        }
      }

      return Array.from(questionIDSet);
    }
    // 解析批量上传的xml文件（针对训练的OJ题型）
    async parseFPSXMLForTrain(xmlString, user, transaction) {
      const { service } = this;
      const parser = new xml2js.Parser();
      const xmlContent = await parser.parseStringPromise(xmlString);
      const { fps } = xmlContent;
      if (!fps) {
        throw new Error('文件格式不是FPS格式，请检查！');
      }

      if (!fps.item) {
        return [];
      }

      const result = [];
      // const currentDatePath = getCurrentDatePath(); // 获取当前日期路径

      // 遍历每个 item
      for (const item of fps.item) {
        const { time_limit: timeLimits = [], memory_limit: memoryLimits = [], spj_file: spjs = [], test_input: rawTestInputs = [], test_output: rawTestOutputs = [], source_file: rawSourceFiles = [], title: titles = [], defaultCode: defaultCodes = [] } = item;

        if (!timeLimits || timeLimits.length === 0) {
          throw new Error('必要参数time_limit未设定!');
        }

        if (!memoryLimits || memoryLimits.length === 0) {
          throw new Error('必要参数memory_limit未设定!');
        }

        if (rawTestInputs && rawTestOutputs) {
          console.log('rawTestInputs', typeof rawTestInputs);
          console.log('rawTestOutputs', typeof rawTestOutputs);


          if (Array(rawTestInputs).length !== Array(rawTestOutputs).length) {
            throw new Error('必要参数test_inputs 数量与 test_outputs不一致!');
          }
        }

        // 解析时间限制
        let timeLimit = '-1'; // 默认值
        for (const { $ } of timeLimits) {
          const { unit = 's', language } = $;
          if (language && availableLanguages.indexOf(languageMap[language]) === -1) {
            throw new Error(`${language} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
          }
          const unitToSecond = timeUnitMap[unit];
          if (!unitToSecond) {
            throw new Error(`无法识别的时间单位 ${unit}!`);
          }
          timeLimit = parseFloat(timeLimits[0]._).toFixed(2); // 取第一个时间限制
        }

        // 解析内存限制
        let memoryLimit = '-1'; // 默认值
        for (const { $ } of memoryLimits) {
          const { unit = 'mb', language } = $;
          if (language && availableLanguages.indexOf(languageMap[language]) === -1) {
            throw new Error(`${language} 目标语言不在可用语言列表中 ${availableLanguages.join(', ')}，请检查！`);
          }
          const unitToMegabytes = memoryUnitMap[unit];
          if (!unitToMegabytes) {
            throw new Error(`无法识别的容量单位 ${unit}!`);
          }
          memoryLimit = memoryLimits[0]._; // 取第一个内存限制
        }

        // 解析测试用例
        const testFiles = [];
        if (rawTestInputs && rawTestInputs.length) {
          for (let i = 0; i < rawTestInputs.length; i++) {
            const rawTestInput = rawTestInputs[i];
            const rawTestOutput = rawTestOutputs[i];
            const score = (rawTestInput.$ && rawTestInput.$.score) ? rawTestInput.$.score : 5; // 默认分数为 5

            // 将输入和输出的 CDATA 内容写入文件
            const inputContent = (typeof rawTestInput === 'object') ? rawTestInput._ || '' : rawTestInput || '';
            const outputContent = rawTestOutput|| '';
            // 计算size
            const inputSize = Buffer.byteLength(inputContent, 'utf8');
            const outputSize = Buffer.byteLength(outputContent, 'utf8');
            // 计算hash
            const inputHash = md5(inputContent || '');
            const outputHash = md5(outputContent || '');

            const inputFilePath = await service.file.uploadHashJudgeFile(inputContent, `${i + 1}.in`, inputHash, inputSize, transaction);
            const outputFilePath = await service.file.uploadHashJudgeFile(outputContent, `${i + 1}.out`, outputHash, outputSize, transaction);

            testFiles.push({
              in: inputFilePath,
              out: outputFilePath,
              type: 'inout',
              score: parseInt(score, 10),
              insize: inputContent.length,
              outsize: outputContent.length,
              fileName: i + 1,
            });
          }
        }

        // 获取 title 的 CDATA 内容作为文件名
        const title = titles[0] ? titles[0] : '未知标题';
        const extraCodePath = `${title}.py`; // 使用 title 作为文件名

        // 对 defaultCode 的 CDATA 内容进行特殊处理
        const defaultCode = (item.defaultCode && item.defaultCode[0]) ? item.defaultCode[0] : '';

        const answerKeys = Array.from({ length: testFiles.length }, (_, i) => i + 1);


        // 解析题目详情
        const questionDetail = {
          title,
          content: (item.description && item.description[0]) ? item.description[0] : '',
          inFormat: (item.input && item.input[0]) ? item.input[0] : '',
          solution: (item.analysis && item.analysis[0]) ? item.analysis[0] : '',
          judgeMenu: {
            file: [],
            spjFiles: [],
            testFiles,
          },
          outFormat: (item.output && item.output[0]) ? item.output[0] : '',
          timeLimit,
          answerKeys,
          originCode: defaultCode, // 使用处理后的 defaultCode
          defaultCode: defaultCode, // 使用处理后的 defaultCode
          memoryLimit,
          inOutExample: [
            {
              inExample: (item.sample_input && item.sample_input[0]) ? item.sample_input[0] : '',
              outExample: (item.sample_output && item.sample_output[0]) ? item.sample_output[0] : '',
              description: (item.sample_description && item.sample_description[0]) ? item.sample_description[0] : '',
            },
          ],
          extraCodePath, // 使用 title 作为文件名
          dataRangePrompts: (item.hint && item.hint[0]) ? item.hint[0] : '',
          exampleSolutions: {
            solutionList: [
              {
                code: (item.solution && item.solution[0]._) ? item.solution[0]._ : '',
                note: (item.solution && item.solution[0] && item.solution[0].$ && item.solution[0].$.note) ? item.solution[0].$.note : '无',
                time: (item.solution && item.solution[0] && item.solution[0].$ && item.solution[0].$.time) ? item.solution[0].$.time : '2025-01-17 15:47:54',
                index: 1,
                avatar: user.avatar,
                username: user.username,
                publishUser: (item.solution && item.solution[0] && item.solution[0].$ && item.solution[0].$.publishUser) ? item.solution[0].$.publishUser : 'datouxia',
              },
            ],
          },
        };

        // 添加到结果中
        result.push(questionDetail);
      }

      return result;
    }
  }

  return QuestionsService;
}