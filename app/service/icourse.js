module.exports = app => {

  class ICourseService extends app.Service {
    // 向Icourse-worker发起生成Kernel请求
    async requestKernelAllocate(containerName, schoolSlug, courseName, courseSlug, chapterName, sectionName, pageClientID, section, containerSetting, userID) {
      const { ctx } = this;

      // 获取当前课程节基本信息
      const { ext } = section;
      const path = `${app.config.file.dir}/${schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;

      // 获取交互式课程对应章节要求的Kernel参数
      const aiFileContent = await ctx.service.course.getFile(path, true);
      const { kernelSetting } = aiFileContent.metadata;

      // 整理并提交请求，写入任务队列
      // const queue = app.queue['icourse-resource'];
      // await queue.add({
      //   type: 'requestICourseKernelAllocate',
      //   containerName, schoolSlug, courseName, courseSlug, chapterName, sectionName, pageClientID, containerSetting, kernelSetting, userID
      // }, { 
      //   removeOnComplete: true 
      // });
      app.client.publish('/icourse-resource', {
         type: 'requestICourseKernelAllocate',
         containerName, schoolSlug, courseName, courseSlug, chapterName, sectionName, pageClientID, containerSetting, kernelSetting, userID
      });
    }

    // 向Icourse-worker发起释放Kernel请求
    async requestKernelFree(containerName, chapterName, sectionName, pageClientID) {
      // 整理并提交请求，写入任务队列
      // const queue = app.queue['icourse-resource'];
      // await queue.add({
      //   type: 'requestICourseKernelFree',
      //   containerName, chapterName, sectionName, pageClientID,
      // }, { 
      //   removeOnComplete: true 
      // });
      app.client.publish('/icourse-resource', {
        type: 'requestICourseKernelFree',
        containerName, chapterName, sectionName, pageClientID,
      });
    }

    // 删除容器
    async clearContainer(schoolSlug, courseSlug, exitMessage) {
      // // 释放容器
      // const queue = app.queue['icourse-resource'];
      // await queue.add({
      //   type: 'clearContainer',
      //   schoolSlug,
      //   courseSlug,
      //   exitMessage
      // }, { 
      //   removeOnComplete: true 
      // });
      app.client.publish('/icourse-resource', {
        type: 'clearContainer',
        schoolSlug,
        courseSlug,
        exitMessage
      });
    }

  }

  return ICourseService;
};
