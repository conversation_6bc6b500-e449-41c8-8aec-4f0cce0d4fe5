const moment = require('moment');
const md5 = require('md5');
const fetch = require('node-fetch');
const fs = require("mz/fs");
const { Op } = require('sequelize');
const path = require('path');
const AdmZip = require("adm-zip");
const iconv  = require('iconv-lite');

// const FormData = require('form-data');

const { wgetPromised } = require('../utils/wget');

// 分拣新增、修改、删除数组
const filterData = (hasArr, inputArr, hasField = 'originalID', inputField = 'originalID') => {
  const result = {
    needDeleteDataIDs: [],
    needUpdateDatas: [],
    needAddDatas: [],
  };
  for(const row of hasArr) {
    const fileRow = inputArr.find(subRow => parseInt(subRow[inputField]) == parseInt(row[hasField]));

    if(!fileRow) {
      result.needDeleteDataIDs.push(row.id);
      continue;
    }

    result.needUpdateDatas.push(fileRow);
  }
  for(const row of inputArr) {
    const fileRow = hasArr.find(subRow => parseInt(subRow[hasField]) == parseInt(row[inputField]));

    if(!fileRow) {
      result.needAddDatas.push(row);
    }
  }

  return result;
}

// 处理数组，使其格式正确
const getArr = (arr, questionBankID, createUserID, field = 'id', toField = 'originalID') => {
  return arr.map(row => {
    const { [field]: noFieldValue, ...other } = row;
    const code = {
      ...other,
      [toField]: row[field],
      questionBankID,
      createUserID,
    }

    return code;
  })
}

//! 将srcDir文件下的文件、文件夹递归的复制到tarDir下
const copyFolder = async function(srcDir, tarDir, cb) {
  // console.log('copyFolder:', srcDir, tarDir)
  const files = await fs.readdir(srcDir);
  // console.log('fs.readdir files:', files)

  for(const file of files) {
    const srcPath = path.join(srcDir, file);
    const tarPath = path.join(tarDir, file);
    // console.log('copyFolder file:', srcPath, tarPath)

    const stats = await fs.stat(srcPath)
    // console.log('stat file:', err, stats)
    if (stats.isDirectory()) {
      // console.log('mkdir', tarPath);
      await fs.mkdir(tarPath);

      await copyFolder(srcPath, tarPath);
    } else {
      // console.log('mkdir', tarPath);
      await fs.copyFile(srcPath, tarPath);
    }
  }

  //为空时直接回调
  // files && files.length && files.length === 0 && cb && cb();
}

function unzip(zipFile, destFolder){
  const zip = new AdmZip(zipFile);
  
  const zipEntries = zip.getEntries();
  for(let i=0; i<zipEntries.length; i++){
    const entry = zipEntries[i];
    entry.entryName = iconv.decode(entry.rawEntryName, 'gbk');
  }
  
  zip.extractAllTo(destFolder, true);
}

module.exports = app => {

  class ThirdPartService extends app.Service {
    async getSign(object) {
      const keys = Object.keys(object);

      keys.sort((a, b) => {
        return a < b ? -1 : 1;
      })

      let resultArr = []
  
      for (const key of keys) {
        if (!object[key] || object[key] === "") {
          continue;
        }
        resultArr.push(`${key}=${object[key]}`);
      }

      return resultArr.join('&')
    }

    async signKey(data) {
      let signValue = data;
      // 加密
      const md5Value = md5(signValue);
      return md5Value.toLowerCase();
    }

    async getTimestampAsSecondFormat(date) {
      if (null === date) {
        return 0;
      }
      return `${new Date(date).getTime()}`;
    }

    async getSingAndNode(node, timestamp, secretField = 'platHxrMainAPI') {
      const signNode = {
        app_id: app.config[secretField].app_id,
        secret: app.config[secretField].secret,
        ...node,
      }
      const { service } = this.ctx;
      const signValue = await service.thirdPart.getSign(signNode);
      return await service.thirdPart.signKey(signValue, timestamp);
    }

    // 获取权限
    async getComputerRoomPermission(mac, transaction = false) {
      const { ctx } = this;
      const { SystemConfig } = ctx.model;

      // console.log('ctx.model:',ctx.model)
      const permissionResults = await SystemConfig.findOne({
        where: { key: 'enableFunction' },
        raw: true,
        transaction,
      });
      let permissionDatas = permissionResults.value;
      try {
        permissionDatas = JSON.parse(permissionDatas)
      } catch (e) {}
      // console.log('permissionDatas:',permissionDatas);

      if (!permissionDatas || !permissionDatas['机房训练'] || !permissionDatas.trainPermissionData || !permissionDatas.trainPermissionData.length) {
        return { status: 'empty' }
      }

      let result = {
        totalLicenseCount: 0,
        used: 0,
        labs: [],
      };

      const lastRow = {};
      let hasPer = false; 
      
      for (const row of permissionDatas.trainPermissionData) {
        if (row.target !== '机房训练') {
          continue;
        }
        if(row.status === '启用') {
          if((row.startTime && moment().isBefore(moment(row.startTime))) || (row.endTime && moment().isAfter(moment(row.endTime)))) {
            continue;
          }

          result.fromDate = moment(row.startTime).format('YYYY-MM-DD');
          result.toDate = moment(row.endTime).format('YYYY-MM-DD');
          result.totalLicenseCount = row.info.teacherPC;
          hasPer = true;
          continue;
        }

        if (row.info && row.info.computerRoomName) {
          result.used += 1;
          result.labs.push(row.info.computerRoomName)
        }

        if (row.info && row.info.teacherPC) {
          lastRow.totalLicenseCount = row.info.teacherPC;
          // lastRow.fromDate = moment(row.startTime).format('YYYY-MM-DD');
          // lastRow.toDate = moment(row.endTime).format('YYYY-MM-DD');
        }

        // if((row.startTime && moment().isBefore(moment(row.startTime))) || (row.endTime && moment().isAfter(moment(row.endTime)))) {
        //   continue;
        // }

        if (row.info && row.info.mac === mac) {
          result.status = 'ok';
          result.labName = row.info.computerRoomName;
          // result.schoolName = schoolNameResults.value;
        }
      }
      // console.log('result.license:',result.license, lastRow)

      // if (!result || !result.status) {
      if(!hasPer) {
        result.status = 'overdue'
        result = { ...result, ...lastRow };
      } else if (!result.status) {
        result.status = 'empty'
        result = { ...result, availableLicenseCount: result.totalLicenseCount - result.used };
      }
      // console.log('(=======================)')

      return result
    }

    async registy(lab, mac, transaction) {
      const { ctx } = this;
      const { SystemConfig } = ctx.model;

      const nowPermission = await this.ctx.service.thirdPart.getComputerRoomPermission(mac, transaction);

      if (nowPermission.status !== 'empty') {
        return nowPermission
      }

      if (nowPermission.availableLicenseCount <= 0) {
        nowPermission.status = 'full';
        return nowPermission
      }

      const permissionResults = await SystemConfig.findOne({
        where: { key: 'enableFunction' },
        raw: true,
        transaction,
      });
      let permissionDatas = permissionResults.value;
      try {
        permissionDatas = JSON.parse(permissionDatas)
      } catch (e) {}

      permissionDatas.trainPermissionData.push(
        { target: '机房训练', status: '占用', info: { computerRoomName: lab, mac }, startTime: moment().format('YYYY-MM-DD') }
      )

      await SystemConfig.update({value: permissionDatas}, {
        where: { key: 'enableFunction' },
        raw: true,
        transaction,
      });

      // console.log('---------------------------')
      await this.ctx.service.thirdPart.uploadToHxrMain(JSON.stringify({slug: this.ctx.schoolSlug, permissionDatas}));
      // console.log('---------------------------')

      return await this.ctx.service.thirdPart.getComputerRoomPermission(transaction);
    }

    // 权限回写
    async uploadToHxrMain(condition) {
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const node = {
        app_id: app.config.platHxrMainAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        data: condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp);
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.thirdPart.getSingAndNode(cookieNode, timestamp);

      // response = await service.thirdPart.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      const loginResponse = await fetch(`${app.config.platHxrMainAPI.host}/thirdPart/modifySchoolPermission`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          app_id: node.app_id,
          timestamp: node.timestamp,
          data: node.data,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      if (res.code) {
        throw new Error(res.message);
      }
      // console.log('res:',res)
      
      return res.data;
    }

    async uploadTmpQuestionFile(questionBankID, subFilePath, fileName, fileContent) {
      const filepath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankID}${subFilePath}`;
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }

    // 发布远程题库
    async uploadQuestionBank(questionBankID, transaction) {
      const { ctx } = this;
      const { schoolSlug, session } = ctx;
      const { Questions, Tag, QuestionTag, TrainQuestionBank, SystemConfig } = ctx.model;

      const { id: userID } = session.user;

      // 取出题库信息
      const questionBankInfo = await TrainQuestionBank.findOne({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { id: questionBankID },
        raw: true,
        transaction,
      })

      if (!questionBankInfo || questionBankInfo.ifSelfBuilding === 0 || !questionBankInfo.toID) {
        throw new Error('您没有该题库发布权限');
      }

      // 取出该题库知识点
      const allTags = await Tag.findAll({
        attributes: { exclude: ['created_at', 'deleted_at'] },
        where: { questionBankID },
        raw: true,
        transaction,
      })

      // 取出该题库所有题目
      const questions = await Questions.findAll({
        attributes: { exclude: ['created_at', 'deleted_at'] },
        where: { questionBankID, status: { [Op.not]: '待审核' } },
        // limit: 1,  // 只返回前  调试用
        raw: true,
        transaction,
      });
      console.log('所有题目长度-len:',questions.length);
      let qid = 0;
      const allQuestionIDs = questions.map(row => {
        if((qid + 1) !== row.id){
          console.log('所有中断题目id-:', row.id-1);
        }
        qid = row.id;
        return row.id;
      });
      
      // 取出题目关联知识点
      const allQuestionTags = await QuestionTag.findAll({
        attributes: { exclude: ['created_at', 'deleted_at'] },
        where: { questionID: { [Op.in]: allQuestionIDs } },
        raw: true,
        transaction,
      });

      // 取出试卷所有的去重记录
      const record = await SystemConfig.findOne({
        where: {
          key: 'trainUpdateRecord'
        },
        transaction // 如果有事务
      });
      const allTrainUpdateRecords =  record.value || []; //去重记录
      console.log('查询试卷去重记录', allTrainUpdateRecords);
      const questionResultArr = [];
      // const updatedQuestionIds = [];
      // 将题库内题目和资源打包
      for (const question of questions) {
        // 按照update_at查看是否需要更新
        // if(moment(question.updated_at).isAfter(moment(questionUpdateAt))) {
        //   updatedQuestionIds.push(question.id);
        // }

        // 取出该题与标签联系
        let questionTags = await allQuestionTags.filter(row => row.questionID === question.id);
        if (!questionTags || !questionTags.length) {
          questionTags = [];
        }

        // 复制资源文件
        const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${question.id}/assets/`;
        const toPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankInfo.toID}/question/${question.id}/assets/`;

        questionResultArr.push({
          questionTags,
          question,
          fromPath,
          toPath,
        })

        // 将题目和知识点管理写入文件
        // await this.ctx.service.thirdPart.uploadTmpQuestionFile(questionBankInfo.toID, `/question/${question.id}`, 'questionTag.json', JSON.stringify(questionTags));

        // // 将题目信息写入文件
        // await this.ctx.service.thirdPart.uploadTmpQuestionFile(questionBankInfo.toID, `/question/${question.id}`, 'questions.json', JSON.stringify(question));

        // const fromAssetsExist = await fs.exists(fromPath);
        // if (!fromAssetsExist) {
        //   continue;
        // };

        // const assetsExist = await fs.exists(toPath);
        // if (!assetsExist) {
        //   await this.ctx.service.file.mkdirs(toPath);
        // };
        
        // await copyFolder(fromPath, toPath)
      }

      // 提交至队列，各分片轮询
      const queue = app.queue['train-task'];
      // const waitingCount = await queue.getWaitingCount();
      // console.log(`${new Date()} 执行异步任务：${'uploadQuestionBankFileToHxrManager'}, 当前等待任务个数：${waitingCount}`);
      const add = {
        condition: {
          //"/file/tmp/csxx/tmpQuestionBank/3.zip" tmpUrl:/file/tmp
          url: `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankInfo.toID}.zip`,
          schoolSlug, //csxx
          deploymentSlug: app.config.hxr.deploymentSlug, //hxr.i51cy.com
          fromID: questionBankID,
        },
        userID,
        schoolSlug,
        //data/tmp/csxx/tmpQuestionBank/3.zip tmpDir:data/tmp
        // filePath: `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankInfo.toID}`,
        filePath: `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankInfo.toID}`,
        questionBankInfo,
        allTags,
        questionResultArr,
        allTrainUpdateRecords,
        managerment: app.config.managerment,
        managermentAPI: app.config.managermentAPI,

        // updatedQuestionIds,
        syncAt: moment().format('YYYY-MM-DD HH:mm:ss'),

        type: 'uploadQuestionBankFileToHxrManager',
      };
      console.log('执行异步任务配置：uploadQuestionBankFileToHxrManager', add);
      await queue.add(add, {
        // 控制不重复
        attempts: 1,
        removeOnComplete: true,
        removeOnFail: true,
        // removeOnComplete: true,
      });

      // return updatedQuestionIds;

      // await this.ctx.service.thirdPart.uploadQuestionBankFileToHxrManager({
      //   url: `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankInfo.toID}.zip`,
      //   schoolSlug: this.ctx.schoolSlug,
      //   deploymentSlug: app.config.hxr.deploymentSlug,
      //   fromID: questionBankID,
      // })

      // this.ctx.service.thirdPart.uploadQuestionBankFileToHxrManager({
      //   url: `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/11.zip`,
      //   schoolSlug: this.ctx.schoolSlug,
      //   deploymentSlug: 'localhost',
      //   fromID: 1,
      // })

      // 记录题库发送时间

      // return `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankID}.zip`
      return `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/tmpQuestionBank/${questionBankInfo.toID}.zip`
      // return `${app.config.file.tmpUrl}/${this.ctx.schoolSlug}/tmpQuestionBank/11.zip`
    }

    // 发送文件地址
    async uploadQuestionBankFileToHxrManager(condition) {
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const node = {
        app_id: app.config.managermentAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        ...condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp, 'managermentAPI');
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.thirdPart.getSingAndNode(cookieNode, timestamp);

      // response = await service.thirdPart.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      // const loginResponse = await fetch(`http://127.0.0.1:7002/thridParty/uploadQuestionBankFile`, {
      const loginResponse = await fetch(`${app.config.managerment}/management-api/thridParty/uploadQuestionBankFile`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          ...condition,
          app_id: node.app_id,
          timestamp: node.timestamp,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      // console.log('res:',res)
      if (res.code) {
        throw new Error(res.message);
      }
      
      return res.data;
    }

    async getOrCreateQuestionBank({name, fromURL, needDelete}, transaction) {
      const { ctx } = this;
      const { TrainQuestionBank } = ctx.model;
      let ifOne = await TrainQuestionBank.findOne({
        where: { fromURL, ifSelfBuilding: 0 },
        transaction,
        raw: true,
      })

      if(!ifOne) {
        ifOne = await TrainQuestionBank.create({
          name,
          ifSelfBuilding: 0,
          fromURL,
        }, {
          transaction,
          raw: true,
        })
      }
  
      return await this.ctx.service.thirdPart.getQuestionBank(ifOne.id, needDelete, transaction)
    }

    // 获取远程题库，试卷修改记录中题目id，对应的学校本地题库id
    async generateLocalUpdateRecord(updateRecord, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions } = model;
    
      // 1. 收集所有需要查询的originalID
      const allOriginalIDs = [];
      for (const key in updateRecord) {
        allOriginalIDs.push(parseInt(key));
        allOriginalIDs.push(...updateRecord[key]);
      }
    
      // 2. 去重
      const uniqueOriginalIDs = [...new Set(allOriginalIDs)];
      // console.log('远程id', uniqueOriginalIDs);
      // 3. 批量查询这些originalID对应的questionBankID
      const questions = await Questions.findAll({
        where: {
          originalID: {
            [Op.in]: uniqueOriginalIDs
          }
        },
        attributes: ['originalID', 'id'],
        raw: true,
        transaction
      });
    // console.log('本地1', questions);
      // 4. 创建映射关系 {originalID: id}
      const idMap = {};
      questions.forEach(q => {
        idMap[q.originalID] = q.id;
      });
      // console.log('本地2', idMap);
      // 5. 构建新的localUpdateRecord
      const localUpdateRecord = [];
      for (const originalKey in updateRecord) {
        const originalID = parseInt(originalKey);
        const id = idMap[originalID];
        
        if (!id) {
          ctx.logger.warn(`未找到originalID为${originalID}的记录`);
          continue;
        }
    
        const valueIDs = updateRecord[originalKey];
        const mappedValues = valueIDs.map(id => idMap[id]).filter(Boolean);
    
        if (mappedValues.length > 0) {
          localUpdateRecord.push({
            [id]: mappedValues
          });
        }
      }
    
      return localUpdateRecord;
    }

    async getQuestionBank(questionBankID, needDelete, transaction) {
      const { ctx } = this;
      const { TrainQuestionBank, User, Train, Questions } = ctx.model;

      // 查找当前库中远程题库对应originalID
      const questionBankInfo = await TrainQuestionBank.findOne({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { id: questionBankID, ifSelfBuilding: 0 },
        raw: true,
        transaction,
      });
      if (!questionBankInfo || !questionBankInfo.fromURL) {
        return null;
      }

      const first = await User.findOne({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { adminAuthority: { [Op.not]: null } },
        raw: true,
        transaction,
      });
      const originalID = questionBankInfo.fromURL;

      // 用originalID取远程题库
                        ///data/tmp/csxx/tmpQuestionBank/3
      const filePath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${originalID}`;

      // 提交至队列，各分片轮询
      const queue = app.queue['train-task'];
      const add = {
        //https://manager.hxr.i51cy.com/file/questionBank/3.zip
        fromPath: `${app.config.managerment}/file/questionBank/${originalID}.zip`,
        toPath: filePath,
        questionBankID,
        needDelete,
        // clientID,
        schoolSlug: this.ctx.schoolSlug,
        userID: ctx.session && ctx.session.user ? ctx.session.user.id : first.id,
        noMonitior: true,
        fileDir: app.config.file.dir,
        fileTmpDir: app.config.file.tmpDir,
        ifForceupdate: true,
        type: 'downloadQuestionBankFileToHxrManager',
      };
      console.log('下载', add);
      await queue.add(add, {
        // 控制不重复
        attempts: 1,
        removeOnComplete: true,
        removeOnFail: true,
        // removeOnComplete: true,
      });
    }

    async getQuestionBankCallBack(questionBankID, needDelete, transaction) {
      const { ctx } = this;
      const { Questions, Tag, QuestionTag, TrainQuestionBank } = ctx.model;

      const userID = ctx.session.user.id;

      // 查找当前库中远程题库对应originalID
      const questionBankInfo = await TrainQuestionBank.findOne({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { id: questionBankID, ifSelfBuilding: 0 },
        raw: true,
        transaction,
      });
      if (!questionBankInfo || !questionBankInfo.fromURL) {
        return null;
      }

      await TrainQuestionBank.update({ abstract: `更新时间：${moment().format('YYYY-MM-DD')}`}, {
        where: { id: questionBankID, ifSelfBuilding: 0 },
        raw: true,
        transaction,
      }); 

      const originalID = questionBankInfo.fromURL;

      // 用originalID取远程题库

      const filePath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${originalID}`;

      // await wgetPromised(
      //   `${app.config.managerment}/file/questionBank/${originalID}.zip`,
      //   `${filePath}.zip`
      // );
      
      // unzip(`${filePath}.zip`, filePath);
      
      // 取出题库信息
      // const questionBankInfoResult = await this.ctx.service.trainThroughTrainPlan.getFile(`${filePath}/questionBank.json`);
      // // 更新题库信息
      // await TrainQuestionBank.update({}, {
      //   attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
      //   where: { id: questionBankID, ifSelfBuilding: 0 },
      //   raw: true,
      //   transaction,
      // });

      // 取出知识点
      const tagResult = await this.ctx.service.trainThroughTrainPlan.getFile(`${filePath}/tag.json`);
      // 编辑知识点数组，并取出originalID
      const tagDatas = getArr(tagResult, questionBankID, userID);
      const fileOriginalIDs = tagDatas.map(row => row.originalID);
      // 查找数据库内已有知识点
      const hasTag = await Tag.findAll({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { originalID: {[Op.in]: fileOriginalIDs }, questionBankID },
        raw: true,
        transaction,
      })
      // const needDeleteTagIDs = [];
      // const needUpdateTags = [];
      // const needAddTags = [];
      const { needDeleteDataIDs: needDeleteTagIDs, needUpdateDatas: needUpdateTags, needAddDatas: needAddTags } = filterData(hasTag, tagDatas);

      // 处理知识点
      await Tag.bulkCreate(needAddTags, { transaction });
      if(needDelete) {
        await QuestionTag.destroy({ where: { tagID: { [Op.in]: needDeleteTagIDs } }, transaction });
        await Tag.destroy({ where: { id: { [Op.in]: needDeleteTagIDs }, questionBankID }, transaction });
      }
      for(const row of needUpdateTags) {
        await Tag.update(row, { transaction, where: { originalID: row.originalID, questionBankID } })
      }

      // 获取处理后的知识点
      const nowTag = await Tag.findAll({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { originalID: {[Op.in]: fileOriginalIDs }, questionBankID },
        raw: true,
        transaction,
      })
      const nowTagMap = {};
      nowTag.forEach(element => {
        nowTagMap[element.originalID] = element.id
      });
      // 调整psrentID
      for(const row of nowTag) {
        await Tag.update({
          parentID: nowTagMap[row.parentID]
        }, { transaction, where: { id: row.id } })
      }

      // 取题目数据
      let resultFile = [];

      const questionDatas = [];
      let questionTagDatas = [];

      // console.log('fs.readdir begin ____________________________________________________');
      // 读文件，取题目、题目和知识点联系
      const files = await fs.readdir(`${filePath}/question/`)
      // console.log('fs.readdir files:', JSON.stringify(files));
      resultFile = files.map(row => parseInt(row));

      // 查找数据库内已有题目
      const hasQuestions = await Questions.findAll({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { originalID: {[Op.in]: resultFile }, questionBankID },
        raw: true,
        transaction,
      })

      let max = 0;
      for(const file of files) {
        if (parseInt(file) > max) {
          max = file
        }
        const srcPath = path.join(`${filePath}/question/`, file);
        // console.log('fs.readdir srcPath:', srcPath);

        // 取题目信息
        const questionInfoResult = await this.ctx.service.trainThroughTrainPlan.getFile(`${srcPath}/questions.json`);
        questionDatas.push(questionInfoResult);

        // 取题目与知识点信息
        const questionTagInfoResult = await this.ctx.service.trainThroughTrainPlan.getFile(`${srcPath}/questionTag.json`);
        questionTagDatas = questionTagDatas.concat(questionInfoResult ? questionTagInfoResult : []);
      }
      // console.log('fs.readdir end');
      // console.log('fs.readdir end ______________________________________________________');
      // 编辑知识点数组，并取出originalID
      const questionFilterDatas = getArr(questionDatas, questionBankID, userID);
      const questionOriginalIDs = questionFilterDatas.map(row => row.originalID);

      const { needDeleteDataIDs: needDeleteQuestionIDs, needUpdateDatas: needUpdateQuestions, needAddDatas: needAddQuestions } = filterData(hasQuestions, questionFilterDatas);

      // 处理知识点
      await Questions.bulkCreate(needAddQuestions, { transaction });
      if(needDelete) {
        await QuestionTag.destroy({ where: { questionID: { [Op.in]: needDeleteQuestionIDs } }, transaction });
        await Questions.destroy({ where: { id: { [Op.in]: needDeleteQuestionIDs }, questionBankID }, transaction });
      }
      for(const row of needUpdateQuestions) {
        await Questions.update(row, { transaction, where: { originalID: row.originalID, questionBankID } })
      }

      // 获取处理后的知识点
      const nowQuestions = await Questions.findAll({
        attributes: { exclude: ['created_at', 'updated_at', 'deleted_at'] },
        where: { originalID: {[Op.in]: questionOriginalIDs }, questionBankID },
        raw: true,
        transaction,
      })
      
      const nowQuestionsMap = {};

      // 拷贝资源
      for(const question of nowQuestions) {
        nowQuestionsMap[question.originalID] = question.id;

        // 复制资源文件
        const toPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${question.id}/assets/`;
        const fromPath = `${app.config.file.tmpDir}/${this.ctx.schoolSlug}/tmpQuestionBank/${originalID}/question/${question.originalID}/assets/`;

        const fromAssetsExist = await fs.exists(fromPath);
        if (!fromAssetsExist) {
          continue;
        };

        const assetsExist = await fs.exists(toPath);
        if (!assetsExist) {
          await this.ctx.service.file.mkdirs(toPath);
        };
        
        await copyFolder(fromPath, toPath)
      }
      
      questionTagDatas = questionTagDatas.map(row => {
        return {
          questionID: nowQuestionsMap[row.questionID],
          tagID: nowTagMap[row.tagID],
        }
      }).filter(row => row.questionID && row.tagID);
      // console.log('nowQuestionsMap:',nowQuestionsMap);
      // console.log('questionTagDatas:',questionTagDatas);

      // 处理知识点和标签关联
      await QuestionTag.destroy({ where: { questionID: { [Op.in]: hasQuestions.map(row => row.id) } }, transaction })
      await QuestionTag.bulkCreate(questionTagDatas, { transaction });

      questionFilterDatas.sort((a,b) => a.originalID - b.originalID);
      questionDatas.sort((a,b) => a.id - b.id);
      hasQuestions.sort((a,b) => a.id - b.id);

      // return {
      //   needAddQuestions: needAddQuestions.length,
      //   needUpdateQuestions: needUpdateQuestions.length,
      //   needAddQuestions: needAddQuestions.length,
      //   questionFilterDatas: questionFilterDatas && questionFilterDatas.length - 1 ? questionFilterDatas[questionFilterDatas.length - 1] : {},
      //   questionDatas: questionDatas && questionDatas.length - 1 ? questionDatas[questionDatas.length - 1] : {},
      //   hasQuestions: hasQuestions && hasQuestions.length - 1 ? hasQuestions[hasQuestions.length - 1] : {},
      //   max,
      //   questionPath: `${filePath}/question/`,
      //   files,
      // }

      // return needDelete
      // return { filePath, tagResult, resultFile, tagDatas, needDeleteTagIDs, needUpdateTags, needAddTags, needDeleteQuestionIDs, needUpdateQuestions, needAddQuestions, questionTagDatas, questionDatas }
    }

    // 获取各校可获取题库列表
    async getSchoolTrainQuestionBankList() {
      const { ctx } = this;
      const { TrainQuestionBank } = ctx.model;
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const node = {
        app_id: app.config.managermentAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        // ...condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp, 'managermentAPI');
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.thirdPart.getSingAndNode(cookieNode, timestamp);

      // response = await service.thirdPart.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      // const loginResponse = await fetch(`http://127.0.0.1:7002/thridParty/uploadQuestionBankFile`, {
      // const loginResponse = await fetch(`http://127.0.0.1:7002/thridParty/getSchoolTrainQuestionBankList/${this.ctx.schoolSlug}`, {
      const loginResponse = await fetch(`${app.config.managerment}/management-api/thridParty/getSchoolTrainQuestionBankList/${this.ctx.schoolSlug}`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          // ...condition,
          app_id: node.app_id,
          timestamp: node.timestamp,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      console.log('res:',res)
      if (res.code) {
        throw new Error(res.message);
      }

      if (!res.data) {
        return res.data
      }

      const originalIDs = res.data.map(row => `${row.id}`);

      const localData = await TrainQuestionBank.findAll({
        where: { fromURL: { [Op.in]: originalIDs } }
      })

      res.data = res.data.map(row => {
        const findRow = localData.find(item => item.fromURL === `${row.id}`);

        if(findRow) {
          return {
            ...row,
            localUpdateTime: findRow.updated_at,
            lastSyncAt: findRow.lastSyncAt,
            findRow
          }
        }

        return row
      })
      
      return res.data;
    }

    async thirdPartFetch(path, condition) {
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());

      const node = {
        app_id: app.config.managermentAPI.app_id,
        ...condition,
        timestamp
      };

      const { csrfToken } = this.ctx.session;

      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp, 'managermentAPI');

      const body = JSON.stringify({
        ...condition,
        app_id: node.app_id,
        timestamp: node.timestamp,
        sign,
      });

      const queryPath = `${app.config.managerment}/${path}`;

      const response = await fetch(queryPath, {
        method: 'POST',
        credentials: 'include',
        body: body,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          _csrf: csrfToken,
        }
      });

      const res = await response.json();

      return res;
    }

    // 获取题库中重复的题库(异步任务)
    async getTrainDuplicateQuestionsBytaskTrain(id, questionType, similar, transaction) {
      const { ctx } = this;
      const { session } = ctx;
      const { id: userID } = session.user;

      // 提交至队列，各分片轮询
      const queue = app.queue['train-task'];
    
      const add = {
        id, 
        questionType, 
        similar,
        type: 'getTrainDuplicateQuestions',
      };
      console.log('执行异步去重任务配置：getTrainDuplicateQuestions', add);
      const res = await queue.add(add, {
        // 控制不重复
        attempts: 1,
        removeOnComplete: true,
        removeOnFail: true,
        // removeOnComplete: true,
      });

      return res;
    }
  }

  return ThirdPartService
}