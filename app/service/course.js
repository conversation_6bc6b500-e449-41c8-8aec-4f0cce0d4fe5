const archiver = require('archiver');
const Sequelize = require('sequelize');
const { Op, QueryTypes } = require('sequelize');
const child_process = require('child_process');
const fs = require("mz/fs");
const fsExtra = require("fs-extra");
const moment = require('moment');
const cheerio = require('cheerio');
const AdmZip = require("adm-zip");
const { makeUUID, getOJAnswer, getOIQuestion, getOIQuestionOld, changeToOJ, changeToOI } = require('../utils/fps');
const { microTypeNameMap } = require('../utils/microapps');
const { readFileMd5 } = require('../utils/file');
const { getOIAndOJResources, getAIResources, getAccessResources, getScratchAssets, getPPTAssets, getCodeBlankAssets } = require('../utils/tool');

const isTeamMember = (teams, userTeams) => {
  for(let each of userTeams){
    if(teams.indexOf(parseInt(each.teamID, 10)) !== -1 || teams.indexOf(`${each.teamID}`) !== -1){
      return true;
    }
  }

  return false;
}


module.exports = app => {
    class CourseService extends app.Service {
      // 调用服务，创建课程数据库
      async createCourse(item, transaction) {
        const { model } = this.ctx;
        const { Course } = model;
        // 查重 courseSlug
        if(item.courseSlug){
          const isExist = await Course.findOne({
            where: {
              courseSlug: item.courseSlug,
            },
            transaction
          })

          if(isExist){
            throw Error('课程标识不允许重复！')
          }
        }

        // 查重 courseName
        if(item.courseName){
          const isExist = await Course.findOne({
            where: {
              courseName: item.courseName,
              createrID: item.createrID
            },
            transaction
          })

          if(isExist){
            throw Error('课程名称不允许重复！')
          }
        }

        // 设置路径
        const coursePath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${item.courseSlug}`;
        const courseAssetsPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${item.courseSlug}/assets`;
        const courseInputPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${item.courseSlug}/input`;

        const courseExist = await fs.exists(coursePath);
        if (!courseExist) {
          await this.ctx.service.file.mkdirs(coursePath);
        };

        const courseAssetsExist = await fs.exists(courseAssetsPath);
        if (!courseAssetsExist) {
          await this.ctx.service.file.mkdirs(courseAssetsPath);
        }

        const courseInputExist = await fs.exists(courseInputPath);
        if (!courseInputExist) {
          await this.ctx.service.file.mkdirs(courseInputPath);
        }

        // 增加章统计
        if(!item.statist) {
          item.statist = {}
        }

        if(item.indics) {
          item.statist.chapters = item.indics.length;
        }

        const courseDetail = await Course.create(item, {
          transaction
        });

        // 获取当前课程数量存储在学校中
        const courseNum = await Course.count({ transaction });
            
        // 数据库存储hash
        const { mainModel } = app;

        // 启用事务
        const transactionMain = await mainModel.transaction({autocommit: false});
        try{
            await mainModel.query(`UPDATE school SET courseNum = ? where slug = ?`,
            { replacements: [courseNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });
        }catch(e){
            console.log(e, 'UPDATE courseNum')
            await transactionMain.rollback();
            return;
        }

        await transactionMain.commit();

        return courseDetail;
      }
      
      // CourseSlug查重
      async ifCourseSlug(item, transaction) {
        const { model } = this.ctx;
        const { Course } = model;
        // 查重 courseSlug
        if(item.courseSlug){
          const isExist = await Course.findOne({
            where: {
              courseSlug: item.courseSlug,
            },
            transaction
          })

          if(isExist){
            throw Error('课程标识不允许重复！')
          }
        }

        // 查重 courseName
        if(item.courseName){
          const isExist = await Course.findOne({
            where: {
              courseName: item.courseName,
              createrID: item.createrID
            },
            transaction
          })

          if(isExist){
            throw Error('课程名称不允许重复！')
          }
        }

        return '不重复！'
      }

      // 修改课程
      async putCourse(item, courseSlug, transaction) {
        const { model } = this.ctx;
        const { Course } = model;
        const { courseName } = item;
        // 查重课程名称
        if(courseName){
          const isExist = await Course.findOne({
            where: { 
              courseName, 
              courseSlug: { [Op.not]: courseSlug },
              createrID: item.createrID
            },
            transaction
          })
          if(isExist){
            throw Error('课程名称不允许重复！');
          }
        }
        if(courseName && courseName.length>20){
          throw Error('课程名称超过20字！');
        }
        const existOne = await Course.findOne({
          where: {
            courseSlug
          },
          raw: true,
          attributes: ['teams', 'historyTeams'],
          transaction
        })

        if (!existOne) {
          return await Course.update(item, {
            where: { courseSlug },
            transaction
          });
        }

        return await Course.update(item, {
          where: { courseSlug },
          transaction
        })
      }

      // 改变统计数据
      async changeCount(courseSlug, field, transaction = false) {
        const { model } = this.ctx;
        const { Course } = model;

        return await Course.update({
          [field]: Sequelize.literal(`${field} + 1`)
        }, {
          where: { courseSlug },
          transaction
        })
      }

      // 查询学习人数
      // async getLearnNumber(courses) {

      //   // 去redis中获取
      //   const redis = app.redis.get('session');
      //   const { model } = this.ctx;
      //   const { Section } = model;

      //   // 查询每门课的学习人数
      //   const courseIDs = courses.map(item=>item.id);
      //   for (const courseID of courseIDs) {
      //     // 查缓存 去redis中获取
      //     const redisData = await redis.get(`${this.ctx.schoolSlug}_${courseID}`);
      //     if(redisData){
      //       const learnNumber = redisData;
      //       courses.forEach(i=>{
      //         if(i.id === courseID){
      //           i.dataValues.students = learnNumber;
      //         }
      //       })
      //     } else {
      //       const sections = await Section.findAll({
      //         where: {courseID},
      //         attributes: ['id', 'courseID']
      //       })

      //       const sectionIDs = sections.map(item=>item.id);
      //       const rows = await model.query(
      //         'SELECT count(distinct userID) AS `count` FROM `section_record` AS `section_record` WHERE (`section_record`.`deleted_at` IS NULL AND `section_record`.`sectionID` IN (?));',
      //         { 
      //           replacements: [sectionIDs && sectionIDs.length ? sectionIDs: 'NULL'], 
      //           type: QueryTypes.SELECT
      //         }
      //       )
       
      //       const learnNumber = rows[0].count;

      //       // 缓存两分钟
      //       await redis.set(`${this.ctx.schoolSlug}_${courseID}`, learnNumber, 'EX', 60*2);
      //       courses.forEach(i => {
      //         if(i.id === courseID){
      //           i.dataValues.students = learnNumber;
      //         }
      //       });
      //     }

      //    }
         
      //    return courses
      // }

      // 拉取课程列表
      async queryCourseList(userID) {
        const { model } = this.ctx;
        const { Course, User, TeamUser } = model;

        const courseList = await Course.findAll({
          where: { publish: 1 },
          // attributes: ['teams', 'teachers', 'allowPaste', 'courseDescription', 'courseImg', 'courseName', 'courseSlug', 'saveCode', 'saveRunResult', 'courseType', 'statist'],
          attributes: ['id', 'updated_at', 'createrID', 'teams', 'teachers', 'allowPaste', 'allowCopy', 'courseDescription',  'courseName', 'courseSlug', 'saveCode', 'saveRunResult', 'courseType', 'statist'],
          include: [{
              model: User,
              as: 'creater',
              attributes: ['name', 'avatar']
          }],
          order:[['updated_at', 'DESC']],
        });

        const userTeams = await TeamUser.findAll({
          where: { userID },
          attributes: ['teamID'],
        })

        // 筛选用户开放的班级或者对应的管理员的课程
        let res = courseList.filter(e =>e.createrID==userID || (e.teams && isTeamMember(e.teams, userTeams)) || (e.teachers && e.teachers.includes(userID)||e.teachers && e.teachers.includes(`${userID}`)))

        return res;
        // return await this.ctx.service.course.getLearnNumber(res)
      }

      // 后台拉取课程列表
      async queryCourseListAdmin(userID) {
        const { model } = this.ctx;
        const { Course, User } = model;
        const courseList = await Course.findAll({
          // where: {
          //   [Op.or]: [
          //     { createrID: userID },
          //     { teachers: administrationID }
          //   ]
          // },
          attributes: ['id', 'created_at', 'createrID', 'teams', 'teachers', 'allowPaste', 'allowCopy', 'courseDescription',  'courseName', 'courseSlug', 'saveCode', 'saveRunResult', 'courseType', 'statist'],
          include: [{
              model: User,
              as: 'creater',
              attributes: ['name', 'avatar']
          }],
          order:[['updated_at', 'DESC']],
        });

        // 过滤
        return courseList.filter(e=>e.createrID==userID || (e.teachers && e.teachers.indexOf(userID) !== -1||e.teachers && e.teachers.indexOf(`${userID}`) !== -1));

        // return await this.ctx.service.course.getLearnNumber(res);
        
      }

      // 拉取课程章节目录
      async queryCourseDirectory(userID, courseSlug) {
        const { ctx } = this;
        const { model } = this.ctx;
        const { Course, TeamUser, User } = model;
        const courseReponse = await Course.findOne({
          where: { courseSlug },
          attributes: ['id', 'courseName', 'courseType', 'statist', 'courseDescription', 'indics', 'teams', 'teachers', 'courseSlug', 'createrID', 'saveCode', 'saveRunResult', 'allowPaste', 'allowCopy', 'publish', 'questionAnswer', 'programLanguage'],
          include: [{
            model: User,
            as: 'creater',
            attributes: ['name', 'avatar']
          }],
        });

        if(!courseReponse){
          // return null;
          throw Error('当前课程不存在！')
        }
        const { publish } = courseReponse;
        if(!publish){
          throw Error('当前课程未开放！');
        }
        
        const userTeams = await TeamUser.findAll({
          where: { userID },
          attributes: ['teamID'],
        });

        let indics = courseReponse.indics;

        if(!indics){
          return null;
        }
        indics = indics.sort((a, b)=>a.chapterIndex - b.chapterIndex)

        const isTest = ctx.session.user && ctx.session.user.id === -1;

        const directory = indics.map(chapter => {
            chapter.sections = chapter.sections.filter(i=>i.status !==false );
            chapter.sections = chapter.sections.sort((a, b) => {return a.sectionIndex - b.sectionIndex} )
       
            const fullContent = {chapterName: chapter.chapterName, chapterTitle: chapter.chapterTitle, sections: chapter.sections, isOpen: true, cheched: chapter.checked};

            if (isTest) {
              return fullContent;
            }

            if((chapter.openTeam && isTeamMember(chapter.openTeam, userTeams))|| (courseReponse.createrID ==userID) || (courseReponse.teachers&&courseReponse.teachers.includes(userID))){
              return fullContent;
            }

            return { ...fullContent, sections: [], isOpen: false, isNew: chapter.studyMember && chapter.studyMember.indexOf(userID) === -1 ? true : false };
        })

        // 筛选用户开放的班级或者对应的管理员的课程
        const { courseName, saveCode, saveRunResult, allowPaste, allowCopy, statist, courseDescription, creater, courseType, programLanguage } = courseReponse;
        
        // 先查询课程id
        const courseData = courseReponse.dataValues ? courseReponse.dataValues : courseReponse;
        const { id } = courseData;
        const sectionData = await model.Section.findAll({
          where: { courseID: id },
          attributes: ['id', 'chapterName', 'sectionName'],
          include: [{
            model: model.SectionRecord,
            as: 'sectionForeign',
            attributes: ['sectionID', 'record', 'passCount', 'totalScore'],
            required: true,
            where: {
              userID
            }
          }],
        });

        return {
          courseName,
          directory,
          saveCode,
          saveRunResult,
          allowPaste,
          allowCopy,
          statist,
          courseDescription,
          creater,
          courseType,
          sectionData,
          programLanguage,
          questionAnswer: courseData ? courseData.questionAnswer : null,
        }
      }

      // 获取节的内容
      async querySectionContentAndType(courseSlug, chapterName, sectionName, ifLoadCode, userID, courseReponse, section, ifShowOIAnswer){
        const { ctx } =this;
        const { model } = this.ctx;
        const { Section, SectionRecord } = model;

        // 获取当前课程节基本信息
        const { sectionID, sectionType, ext, mustBeDone, status } = section;
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;

        try {
          // 获取文件系统信息
          const stat = await fs.stat(path);

          // 获取课程信息
          const sectionDetail = await Section.findOne({
            where: { id: sectionID },
            attributes: ['id', 'record', 'created_at', 'updated_at'],
            raw: true
          });

          // 获取学生提交记录 对于AI课程，是项目书链接记录
          const submitRecord = sectionDetail && sectionDetail.record ? sectionDetail.record : {};

          // 获取学生运行记录
          const sectionRecord = await SectionRecord.findOne({
            where: {
              userID,
              sectionID: sectionID,
            },
            attributes: ['record', 'updated_at', 'created_at', 'totalScore', 'passCount'],
            raw: true
          });
          const record = sectionRecord && sectionRecord.record ? sectionRecord.record : { };

          // 记录学生课程记录时间
          const recordTime = sectionRecord ? {
            create: sectionRecord.created_at,
            update: sectionRecord.updated_at
          } : {} 

          // 分类返回相应课程数据
          switch(sectionType) {
            case 'AI':
            {
              const AIRsponse = await ctx.service.course.getAIContentFromFile(courseReponse, chapterName, sectionName, sectionType, path, ifLoadCode, userID, sectionDetail, sectionRecord);
              return { ...AIRsponse, stat, recordTime, status, submitRecord };
            }
            case 'OI':
            {
              // ifShowOIAnswer: 后台默认为true传递全部数据，前台为false不传递答案数据
              const { questions, ifShowAnswer, ifSubmitLimit, submitLimit, title } = await ctx.service.course.getOIContentFromFile(courseSlug, chapterName, section, ifShowOIAnswer);
              record.totalScore = sectionRecord ? sectionRecord.totalScore : 0;         
              record.passCount = sectionRecord ?sectionRecord.passCount : 0;
              return {type: sectionType, content: questions, title, submitRecord, record, stat, recordTime, ifShowAnswer, ifSubmitLimit, submitLimit, mustBeDone, status };
            }
            case 'OJ':
            {
              const { questionOJ, answerListMsg } = await ctx.service.course.getOJContentFromFile(path, courseSlug, courseReponse.questionAnswer, ifShowOIAnswer);
              const { judgeMenu: { file = [] }, ...rest } = questionOJ; // 过滤判决文件
              const sourceFiles = file && file.length ? file.map(item => { return { name: item.name }; }) : [];
              return {type: sectionType, content: { judgeMenu: { file: sourceFiles }, ...rest }, submitRecord, record, stat, recordTime, answerListMsg, mustBeDone, status };
            }

            case 'Excel':
            case 'Access':
            case 'PPT':
              {
                const accessQuery = { courseReponse, chapterName, sectionName, sectionType, path, sectionDetail };
                const AccessRsponse = await ctx.service.course.getAccessContentFromFile(accessQuery);
                return { type: sectionType, ...AccessRsponse, record, stat, status };
              }
            case 'MicroBit':
            case 'Scratch':
              {
                const query = { courseReponse, courseSlug, chapterName, sectionName, sectionType, path, userID, sectionDetail, record, sectionRecord };
                const courseContent = await ctx.service.course.getScratchContentFromFile(query);
                return { type: sectionType, ...courseContent, stat, status };
              }

            case 'CodeBlank':
              {
                const query = { courseReponse, chapterName, sectionName, sectionType, path, sectionDetail };
                
                // 根据设置传回答案
                const { content, answer, showAnswerPolicy: ifShowAnswer } = await ctx.service.course.getCodeBlankContentFromFile(query);

                if (ifShowAnswer === '公开答案') {
                  return { type: sectionType, content, answer, record, stat, status };
                }

                return { type: sectionType, content, record, stat, status };
              }
            default: 
              break;
          }
        } catch(e) {
          throw new Error(`加载数据文件${path}时出现异常${e.message}`);
        }
      }

      // 后台获取节内容
      async querySectionContentAndTypeAdmin(courseSlug, chapterName, sectionName, ifLoadCode, userID, courseReponse, section, ifShowOIAnswer){
        const { ctx } =this;
        const { model } = this.ctx;
        const { Section } = model;

        // 获取当前课程节基本信息
        const { sectionID, sectionType, ext, mustBeDone, status, isProjectBook } = section;
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;

        try {
          // 获取文件系统信息
          const stat = await fs.stat(path);

          // 获取课程信息
          const sectionDetail = await Section.findOne({
            where: { id: sectionID },
            attributes: ['id', 'record', 'historyRecords', 'created_at', 'updated_at'],
            raw: true
          });

          // 分类返回相应课程数据
          switch(sectionType) {
            case 'AI':
            {
              const AIRsponse = await ctx.service.course.getAIContentFromFileAdmin(courseReponse, chapterName, sectionName, sectionType, path, sectionDetail);
              return { ...AIRsponse, isProjectBook, stat, status };
            }
            case 'OI':
            {
              // ifShowOIAnswer: 后台默认为true传递全部数据，前台为false不传递答案数据
              const { questions, ifShowAnswer, ifSubmitLimit, submitLimit, title } = await ctx.service.course.getOIContentFromFileAdmin(courseSlug, chapterName, section, ifShowOIAnswer, sectionDetail);
              return {type: sectionType, content: questions, title, stat, ifShowAnswer, ifSubmitLimit, submitLimit, mustBeDone, status };
            }
            case 'OJ':
            {
              const { questionOJ } = await ctx.service.course.getOJContentFromFileAdmin(path, courseSlug, chapterName, section, sectionDetail);
              return {type: sectionType, content: questionOJ, stat, mustBeDone, status };
            }
            case 'Excel':
            case 'Access':
            case 'Scratch':
            case 'PPT':
            case 'CodeBlank':
            case 'MicroBit':
              {
                const jsonParams = { courseReponse, chapterName, sectionName, sectionType, path, ext, sectionDetail };
                const jsonResponse = await ctx.service.course.getJsonContentFromFileAdmin(jsonParams);
                return { type: sectionType, ...jsonResponse, stat, status };
              }
            default: 
              break;
          }
        } catch(e) {
          console.error('querySectionContentAndTypeAdmin', e);
          throw new Error(`加载数据文件${path}时出现异常${e.message}`);
        }
      }

      async getAIContentFromFileAdmin(courseReponse, chapterName, sectionName, sectionType, path, sectionDetail) {
        const { ctx } =this;
        let response = {};
        const { id:courseID, courseSlug, containerInfo, indics } = courseReponse;
        const currentChapter = indics.find(chapter => chapter.chapterName === chapterName);
        const currentSections = currentChapter.sections;

        try {
          // 查询课程历史记录
          const { historyRecords } = sectionDetail;
          if (historyRecords && historyRecords.length) {
            const newestRecords = historyRecords[historyRecords.length -1];
            const { version } = newestRecords;
            const fileQuery = { courseSlug, chapterName, sectionName, sectionType, version, ext: 'ipynb' };
            try {
              response = await ctx.service.section.getHistoryFile(fileQuery);
              // 获取最新版本内容
              if (!response) {
                response = await ctx.service.course.getFile(path, true);
              }
            } catch(e) {
              console.log(e, path);
              response = await ctx.service.course.getFile(path, true);
            }
          } else {
            try {
              response = await ctx.service.course.getFile(path, true);
            } catch(e) {
              console.log(e, path, 'getAIContentFromFileAdmin');
            }
          }


          // 查询项目书结构
          const projectTreeAndLink = await ctx.service.section.getProjectBookData(courseSlug, courseID, chapterName, sectionName, currentSections);
          const isLinked = projectTreeAndLink.isLinked;
          const projectBookData = projectTreeAndLink.results;

          return {type: sectionType, content: response, projectBookData, isLinked, containerInfo };
        } catch(e){
          console.error(e, 'getAIContentFromFileAdmin');
          throw new Error(e.message);
        }
      }

      // 自文件加载交互式课程文件内容
      async getAIContentFromFile(courseReponse, chapterName, sectionName, sectionType, path, ifLoadCode, userID, sectionDetail, sectionRecord){
        const { ctx } =this;
        let response = {};
        const { id:courseID, courseSlug, saveCode, saveRunResult, containerInfo, indics } = courseReponse;
        const currentChapter = indics.find(chapter => chapter.chapterName === chapterName);
        const currentSections = currentChapter.sections;

        try {
          response = await ctx.service.course.getFile(path, true);

          // 获取学生提交记录
          const submitRecord = sectionDetail && sectionDetail.record ? sectionDetail.record : {};
        

          // 不需要加载保存代码以及运行结果
          if (!saveCode && !saveRunResult) {
            return {type: sectionType, content: response, submitRecord, containerInfo };
          }

          // 获取学生运行记录
          const record = sectionRecord && sectionRecord.record && sectionRecord.record.UUIDsMap ? sectionRecord.record.UUIDsMap  : { };
          // 标识：前端需定义是否加载保存的代码以及运行结果
          if (!ifLoadCode) {
            let record = {}
            let projectBookData = []
            let isLinked = {};

            // 查询项目书结构
            const projectTreeAndLink = await ctx.service.section.getProjectBookData(courseSlug, courseID, chapterName, sectionName, currentSections);
            isLinked = projectTreeAndLink.isLinked;
            projectBookData = projectTreeAndLink.results;

            if (record.isProjectBook) {
              const { projectBookMap } = projectTreeAndLink;
              for (const key in record) {
                if (key === 'isProjectBook') {
                  continue;
                }

                const recordValue = record[key];
                if (!recordValue) {
                  continue;
                }

                if (!recordValue.project) {
                  continue;
                }

                const { sectionName: SN, UUID } = recordValue.project;

                const mapKey = `${SN}_${UUID}`;
                if (!projectBookMap[mapKey]) {
                  delete recordValue.project;
                }
              }
            }
            // 获取本章的项目书
            return { type: sectionType, content: response, projectBookData, record, isLinked, containerInfo };
          }

          // !! TODO !! 待修改为微应用从同一文件读取
          // 获取对应章节微应用数据
          if (response) {
            const { cells } = response;
            if (cells && cells.length) {
              for (const cell of cells) {
                // 只处理代码
                if (cell['cell_type'] === 'markdown') {
                  continue;
                }

                const { metadata, cell_type } = cell;
                const { UUID, type: fileType, fileName, config: sourceParams } = metadata;
                // 是否为微应用
                if(cell_type === 'code' && fileType && fileType !== 'resources' && fileType !== 'filepreviewer') {
                  try {
                    const { extName, binary = false } = microTypeNameMap[fileType];
                    const fileFullName = `${fileName}.${extName}`;
                    
                    // 拼接主目录
                    const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${fileFullName}`;
                    let FileValueResponse = null;
                    try {
                      // 按照二进制Buffer读取
                      if(binary) {
                        FileValueResponse = await fs.readFile(pathDir);
                      }
                      else {
                        // 安装UTF-8编码读取
                        FileValueResponse = await ctx.service.course.getFile(pathDir, false);
                      }
                    } catch(err) {
                      console.log(err, `${fileFullName}文件不存在`)
                    }
                    const fileStat = await fs.stat(pathDir)
                    record[UUID] = {
                      fullName: fileFullName,
                      result: {
                        codeResult: [],
                        turtleModels: []
                      },
                      status: "运行通过",
                      type: "success",
                      fileStat
                    }
  
  
                    switch(fileType) {
                      case 'text':
                        record[UUID].result.codeResult[0] = {
                          fileName,
                          message: FileValueResponse,
                          sourceParams,
                          type: fileType
                        }
                        break;
                      case 'table':
                        let message = [];
                        if (FileValueResponse) {
                          message = FileValueResponse.split('\n');
                          const tableCellValue = sourceParams.cell && sourceParams.cell.length ? sourceParams.cell : [];
                          if (message && message.length) {
                            let iIndex = -1;
                            message = message.map((data) => {
                              iIndex += 1;
                              let jIndex = -1;
                              return data.split(',').map((v) => {
                                jIndex += 1;
                                return {
                                  readOnly: tableCellValue[iIndex] && tableCellValue[iIndex].length ? (tableCellValue[iIndex][jIndex] ? true : false) : false,
                                  style: {background: "rgb(238, 238, 238)"},
                                  value: v,
                                }
                              });
                            })
                          }
                        }
                        record[UUID].result.codeResult[0] = {
                          fileName,
                          message: message,
                          sourceParams,
                          type: fileType
                        }
                        break;
                      case 'mind':
                      case 'flow':
                      case 'spreadsheet':
                      case 'networksimulator':
                      case 'drawio':
                        record[UUID].result.codeResult[0] = {
                          fileName,
                          message: JSON.parse(FileValueResponse),
                          sourceParams,
                          type: fileType
                        }
                        break;
                      default:
                        throw new Error(`不支持的微应用类型${fileType}`);
                    }
                  } catch(e) {
                    console.log('querySectionContentAndType---service', e)
                    continue;
                  }
                } 
              }
            }
          }
          return {type: sectionType, content: response, submitRecord, record, containerInfo };
        } catch(e){
          console.error(e, 'getAIContent');
          throw new Error(e.message);
        }
      }

      // 向前端，加载并解析客观题文件内容
      async getOIContentFromFile(courseSlug, chapterName, section, ifShowOIAnswer){
        const { ctx } = this;
        const { sectionName, ext } = section;
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const fileContent = await ctx.service.course.getFile(path, false);

        // 对于图片地址进行预处理：将../assets 替换为 file/course/${courseSlug}/assets/
        const fileContentAsssetURLAdjuseted = fileContent.replace(/\.\.\/assets\//g, `file/course/${courseSlug}/assets/`);
        
        // ifShowOIAnswer: 后台默认为true传递全部数据，前台为false不传递答案数据
        return getOIQuestion(fileContentAsssetURLAdjuseted, ifShowOIAnswer);
      }

      // 后台加载
      async getOIContentFromFileAdmin(courseSlug, chapterName, section, ifShowOIAnswer, sectionDetail){
        const { ctx } = this;
        const { sectionName, ext } = section;
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
        let fileContent;

        // 查询课程历史记录
        const { historyRecords } = sectionDetail;
        if (historyRecords && historyRecords.length) {
          const newestRecords = historyRecords[historyRecords.length -1];
          const { version } = newestRecords;
          const fileQuery = { fileDir, sectionName, version, ext };
          // 获取最新版本内容
          try {
            fileContent = await ctx.service.section.getHistoryFileContent(fileQuery);
          } catch (e) {
            console.log(e, path, 'getOIContentFromFileAdmin getHistoryFileContent');
            fileContent = await ctx.service.course.getFile(path, false);
          }
          
        } else {
          fileContent = await ctx.service.course.getFile(path, false);
        }

        // 对于图片地址进行预处理：将../assets 替换为 file/course/${courseSlug}/assets/
        // const fileContentAsssetURLAdjuseted = fileContent.replace(/\.\.\/assets\//g, `file/course/${courseSlug}/assets/`);
        
        // ifShowOIAnswer: 后台默认为true传递全部数据，前台为false不传递答案数据
        return getOIQuestion(fileContent, ifShowOIAnswer);
      }

      // 根据路径加载OJ题内容
      async getOJContentFromFile(path, courseSlug, questionAnswer, ifShowOIAnswer) {
        const { ctx } = this;

        // OJ题前台题解提示信息
        let answerListMsg = '';
        const fileContentOJ = await ctx.service.course.getFile(path, false);

        // 对于图片地址进行预处理：将../assets 替换为 file/course/${courseSlug}/assets/
        const fileContentAsssetURLAdjuseted = fileContentOJ.replace(/\.\.\/assets\//g, `file/course/${courseSlug}/assets/`);

        const questionsOJ = getOJAnswer(fileContentAsssetURLAdjuseted);

        let questionOJ = questionsOJ;
        // 在这里判断是否有权限查看题解，防止有的学生直接查看network就可以看到题解
        // 后台默认可以查看题解
        if (!questionAnswer && !ifShowOIAnswer) {
          if (questionOJ) {
            questionOJ.exampleSolutions.solutionList = [];
            answerListMsg = '当前题解暂不对外开放';
          }
        }

        return { answerListMsg, questionOJ };
      }

      // 后台加载
      async getOJContentFromFileAdmin(path, courseSlug, chapterName, section, sectionDetail) {
        const { ctx } = this;
        const { sectionName, ext } = section;

        let fileContent;
        // 查询课程历史记录
        const { historyRecords } = sectionDetail;
        if (historyRecords && historyRecords.length) {
          const newestRecords = historyRecords[historyRecords.length -1];
          const { version } = newestRecords;
          const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
          const fileQuery = { fileDir, sectionName, version, ext };
          // 获取最新版本内容
          try {
            fileContent = await ctx.service.section.getHistoryFileContent(fileQuery);
          } catch (e) {
            console.log(e, path, 'getOJContentFromFileAdmin getHistoryFileContent');
            fileContent = await ctx.service.course.getFile(path, false);
          }
         
        } else {
          fileContent = await ctx.service.course.getFile(path, false);
        }

        const questionOJ = getOJAnswer(fileContent);

        return { questionOJ };
      }

      async getAccessContentFromFile(params) {
        const { path } = params;

        const { ctx } =this;
        let response = null;

        try {
          response = await ctx.service.course.getFile(path, true);
        } catch(e) {
          console.error('getAccessContentFromFile', e);
          throw new Error(e.message);
        }

        return { content: response };

      }

      async getScratchContentFromFile(params) {
        let { path, courseSlug, userID, chapterName, sectionName, record } = params;

        const { ctx } =this;
        let response = null;

        try {
          response = await ctx.service.course.getFile(path, true);
          try {
            const { fileName } = record;
            const fileFullName = `${sectionName}.json`;
            
            // 拼接主目录
            const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${fileName}`;
            let FileValueResponse = null;
            try {
              // 按照UTF-8编码读取
              FileValueResponse = await ctx.service.course.getFile(pathDir, false);
            } catch(err) {
              console.error(err, `${fileName}文件不存在`)
            }
            const fileStat = await fs.stat(pathDir)
            record = {
              fullName: fileFullName,
              result: {
                fileName: sectionName,
                message: JSON.parse(FileValueResponse),
              },
              status: "运行通过",
              type: "success",
              fileStat
            }

          } catch(e) {
            console.log('scratch web', e)
          }

        } catch(e) {
          console.error('getScratchContentFromFile', e);
          throw new Error(e.message);
        }

        return { content: response, record };

      }

      async getCodeBlankContentFromFile(params) {
        const { path } = params;

        const { ctx } =this;
        let response = null;
        let setAnswer = null;
        let showAnswerPolicy = null;

        try {
          response = await ctx.service.course.getFile(path, true);
          const { questions } = response;

          const question = questions[0] || {};

          const { ifShowAnswer } = question;
          
          const { answer = {}, ...items } = question;
          const answerKeys = Object.keys(answer);
          response.questions[0] = { ...items, answerKeys };

          setAnswer = answer;
          showAnswerPolicy = ifShowAnswer;

        } catch(e) {
          console.error('getCodeBlankContentFromFile', e);
          throw new Error(e.message);
        }

        return { content: response, answer: setAnswer, showAnswerPolicy };

      }

      async getJsonContentFromFileAdmin(params) {
        const { courseReponse, chapterName, sectionName, sectionType, ext, path, sectionDetail } = params;

        const { ctx } =this;
        let response = null;
        const { courseSlug } = courseReponse;

        try {
          // 查询课程历史记录
          const { historyRecords } = sectionDetail;
          if (historyRecords && historyRecords.length) {
            const newestRecords = historyRecords[historyRecords.length -1];
            const { version } = newestRecords;
            const fileQuery = { courseSlug, chapterName, sectionName, sectionType, version, ext };
            try {
              const fileContent = await ctx.service.section.getHistoryFile(fileQuery);
              // 获取最新版本内容
              response = fileContent;
            } catch(e) {
              console.log(e, sectionType, 'getJsonContentFromFileAdmin history');
              response = await ctx.service.course.getFile(path, true);
            }
          } else {
            response = await ctx.service.course.getFile(path, true);
          }
        } catch(e) {
          console.error(sectionType, 'getJsonContentFromFileAdmin', e);
          throw new Error(e.message);
        }

        return { content: response };

      }
      
      // 加载文件数据，可以按需解JSON
      async getFile(pathDir, isJson) {
        const result = await fs.readFile(pathDir, 'utf-8');
        // 检查当前文件是文件还是目录
        return isJson ? JSON.parse(result) : result;
      }

      // 根据 courseSlug 查询 课程 getCourseBycourseSlug
      async getCourseBycourseSlug(courseSlug) {
        const { model } = this.ctx;
        const { Course, User } = model;
        return Course.findOne({
          where: { courseSlug },
          attributes: [ 'createrID', 'courseName', 'courseSlug', 'courseDescription', 'publish', 'indics', 'courseType', 'saveCode', 'saveRunResult', 'allowPaste', 'allowCopy', 'questionAnswer', 'teams', 'historyTeams', 'teachers', 'containerInfo', 'programLanguage', 'updated_at', 'deleted_at'],
          include: [{
            model: User,
            as: 'creater',
            attributes: ['name', 'username', 'avatar']
          }],
        })
      }

      async getCourseAndSectionBycourseSlug(courseSlug) {
        const { model } = this.ctx;
        const { Course, User, Section } = model;
        let course = await Course.findOne({
          where: { courseSlug },
          attributes: [ 'createrID', 'courseName', 'courseSlug', 'courseDescription', 'publish', 'indics', 'courseType', 'saveCode', 'saveRunResult', 'allowPaste', 'allowCopy', 'questionAnswer', 'teams', 'historyTeams', 'teachers', 'containerInfo', 'programLanguage', 'updated_at', 'deleted_at'],
          include: [{
            model: User,
            as: 'creater',
            attributes: ['name', 'username', 'avatar']
          }],
        });

        // 1. 提取所有 sectionID
        const allSectionIDs = [];
        course.indics.forEach(item => {
          item.sections.forEach(sectionItem => {
            if (sectionItem.sectionID) {
              allSectionIDs.push(sectionItem.sectionID);
            }
          });
        });

        // 去重（可选，但推荐）
        const uniqueSectionIDs = [...new Set(allSectionIDs)];

        // 2. 一次性查询所有 Section
        let sectionsMap = {};
        if (uniqueSectionIDs.length > 0) {
          const sections = await Section.findAll({
            where: {
              id: uniqueSectionIDs // 批量查询
            },
            attributes: ['id', 'created_at', 'updated_at', 'deleted_at']
          });

          // 3. 构建映射：{ sectionID: sectionData }
          sectionsMap = sections.reduce((map, sec) => {
            map[sec.id] = {
              created_at: sec.created_at,
              updated_at: sec.updated_at,
              deleted_at: sec.deleted_at
            };
            return map;
          }, {});
        }

        // 4. 将时间信息附加到每个 sectionItem
        course.indics = course.indics.map(item => {
          return {
            ...item,
            sections: item.sections.map(sectionItem => {
              const timeInfo = sectionsMap[sectionItem.id] || {};
              return {
                ...sectionItem,
                ...timeInfo // 合并时间字段
              };
            })
          };
        });
        return course;
      }

      // 根据 courseSlug 查询 课程 getCourseInfoBycourseSlug
      async getCourseInfoBycourseSlug(courseSlug) {
        const { model } = this.ctx;
        const { Course, User } = model;
        return Course.findOne({
          where: { courseSlug },
          attributes: [ 'createrID',  'publish', 'courseType'],
          include: [{
            model: User,
            as: 'creater',
            attributes: ['name', 'username', 'avatar']
          }],
        })
      }
      // 根据 courseSlug 查询 课程 indics
      async getIndicsData(courseSlug) {
        const { model } = this.ctx;
        const { Course, User } = model;
        return Course.findOne({
          where: { courseSlug },
          attributes: ['indics'],
          include: [{
            model: User,
            as: 'creater',
            attributes: ['name', 'username', 'avatar']
          }],
        })
      }

      // 根据 courseSlug 查询重复课程
      async findExitCourseSlug(courseSlug, courseName) {
        const { model } = this.ctx;
        const { ctx } = this;
        const { Course, User } = model;
        return Course.findOne({
          where: { [Op.or]: [{
            courseSlug,
          }, {
            createrID: ctx.session.user.id,
            courseName
          }]},
          include: [
            {
              model: User,
              as: 'creater',
              attributes: ['name']
            }
          ],
          attributes: ['id', 'courseSlug', 'courseName']
        })
      }

      // 获取课程内容目录
      async getCourseDirectory(courseSlug) {
        const { model } = this.ctx;
        const { Course } = model;
        const datas = await Course.findOne({
          where: { courseSlug },
          attributes: ['indics']
        })

        if(!datas){
          throw Error('当前课程不存在！')
        }

        datas.indics.sort((a, b)=>a.chapterIndex-b.chapterIndex);
        for (const data of datas.indics) {
          if(data && data.sections && data.sections.length){
            data.sections.sort((a,b)=>a.sectionIndex-b.sectionIndex);
          }
        }
        return datas;
      }

      // 校验用户对于课节的权限，如果无权限，抛出异常告知原因，如果有权限给出课程配置
      async checkUserPermissionGetConfig(userID, teamIDs, courseSlug, chapterName, sectionName, transaction = undefined) {
        // 获取课程信息
        const { model } = this.ctx;
        const { Course } = model;
        const course = await Course.findOne({
          // attributes: ['id', 'courseName', 'courseSlug', 'indics', 'publish', 'createrID', 'saveCode', 'saveRunResult', 'teams', 'teachers', 'containerInfo', 'questionAnswer'], // 课程信息字段在多处使用，并且几乎会使用到所有字段，因此暂时不过滤字段
          where: { courseSlug },
          raw: true,
          transaction
        });

        const { indics, courseName, publish, createrID, teams: courseAllowTeamIDs, teachers: courseAllowTeacherIDs, containerInfo } = course;

        // 查询当前课程的sectionID
        const currentChapter = indics.find(chapter => chapter.chapterName === chapterName);
        if(!currentChapter) {
          throw new Error(`本章 ${chapterName} 不存在，可能老师已经修改，请尝试返回课程列表页面！`);
        }

        const currentSection = currentChapter.sections.find(section => section.sectionName === sectionName);
        if(!currentSection) {
          throw new Error(`本课程${sectionName}不存在，可能老师已经修改，请尝试返回课程列表页面！`);
        }
        
        // 判断是否为本课程老师
        courseAllowTeacherIDs.push(createrID);

        // 若为压力测试，视为老师操作
        if(userID === -1) {
          courseAllowTeacherIDs.push(userID);
        }

        const isCourseTeacher = courseAllowTeacherIDs.indexOf(userID) !== -1;

        // 如果不是本课程教师，则按照同学标准进行权限检查
        if(!isCourseTeacher) {
          if(1 !== publish) {
            throw new Error(`课程尚未开放，请联系老师！`);
          }

          // 课程尚未向班级开放，禁止提交
          let courseAllowTeamIn = false;
          for(const teamID of teamIDs) {
            if(courseAllowTeamIDs.indexOf(teamID) !== -1) {
              courseAllowTeamIn = true;
              break;
            }
          }

          if(!courseAllowTeamIn) {
            throw new Error(`课程尚未向班级开放，请联系老师！`);
          }

          // 本章未向班级开放，禁止提交
          const { openTeam: chaperOpenTeam } = currentChapter;
          let chapterAllowTeamIn = false;

          if(chaperOpenTeam && chaperOpenTeam.length) {
            for(const teamID of teamIDs) {
              if(chaperOpenTeam.indexOf(teamID) !== -1) {
                chapterAllowTeamIn = true;
                break;
              }
            }
          }

          if(!chapterAllowTeamIn) {
            throw new Error(`本章 ${chapterName} 未向班级开放，请联系老师！`);
          }
        }

        return {
          course,
          courseName,
          currentSection,
          isCourseTeacher,
          containerInfo
         };
      }

      // 课程内容目录排序后保存 
      async saveCourseSort(courseSlug, indics, acrossChapter, transaction) {
        const { model } = this.ctx;
        const { Course, Section } = model;

        const courseData = await Course.findOne({
          where: {courseSlug},
          attributes: ['id', 'indics', 'courseSlug'],
          transaction
        })
        if(!courseData){
          throw new Error('当前课程不存在！');
        }

        // 修改缓存
        await Course.update({indics}, {
          where: { courseSlug },
          transaction
        })

        // 修改section表
        if(acrossChapter && acrossChapter.length){
          const sectionList = await Section.findAll({
            where: {
              courseID: courseData.id
            },
            attributes: ['id', 'record', 'chapterName', 'sectionName'],
            raw: true,
            transaction,
          });
          const updateSectionList = [];
          try {
            for (const item of acrossChapter) {
              const [oldSection, newSection] = item;
              const sectionName = oldSection[1]
              const oldChapterName = oldSection[0];
              const newChapterName = newSection[0];

              const section = sectionList.find((sValue) => sValue.chapterName === oldChapterName && sValue.sectionName === sectionName );
              if(!section){
                throw Error('当前课程不存在！');
              }
  
              let { id, record } = section;
  
              if (!record) {
                updateSectionList.push({
                  id,
                  chapterName: newChapterName, 
                  record
                });
                continue;
              }
  
              // 项目书
              if (record.isProjectBook) {
                // 自身无需处理record
                updateSectionList.push({
                  id,
                  chapterName: newChapterName, 
                  record
                });
  
                const sameChapterSection = sectionList.filter((sValue) => sValue.chapterName === oldChapterName);
                if (!sameChapterSection || !sameChapterSection.length) {
                  continue;
                }
  
                for (const sameChapterSectionValue of sameChapterSection) {
                  const { chapterName: SCSCharpterName, id: SCSID  } = sameChapterSectionValue;
                  if (id === SCSID) {
                    continue;
                  }
                  // 排除是否有其他更新记录
                  const ifUpdateIndex = updateSectionList.findIndex((data) => data.id === SCSID);
  
                  const SCSRecord = ifUpdateIndex !== -1 ? updateSectionList[ifUpdateIndex].record : sameChapterSectionValue.record;
                  if (!SCSRecord || SCSRecord.isProjectBook) {
                    continue;
                  }
  
                  let editor = false;
                  // 非项目书，删除对于项目书链接
                  for (const key in SCSRecord) {
                    if(key === 'isProjectBook') {
                      continue;
                    }

                    if (!SCSRecord[key] || !SCSRecord[key].project) {
                      continue;
                    }
  
                    const { project } = SCSRecord[key];
                    if(project && project.sectionName === sectionName){
                      editor = true;
                      delete SCSRecord[key].project;
                    }
                  }

                  // 不包含当前项目书的链接，不处理
                  if (!editor) {
                    continue;
                  }
  
                  if (ifUpdateIndex === -1) {
                    updateSectionList.push({
                      id: SCSID,
                      chapterName: SCSCharpterName, 
                      record: SCSRecord
                    });
                    continue;
                  }
  
                  updateSectionList[ifUpdateIndex].record = SCSRecord;
                }
                continue;
              }
  
              // 非项目书，删除对于项目书链接
              for (const key in record) {
                if(key === 'isProjectBook' || !record[key] || Object.prototype.toString.call(record[key]) !== '[object Object]') {
                  continue;
                }
  
                const { project } = record[key];
                if(project){
                  delete record[key].project;
                }
              }
  
              updateSectionList.push({
                id,
                chapterName: newChapterName, 
                record
              });
            }
          } catch(e) {
            console.log(e, 'saveCourseSort');
            throw new Error(e);
          }

          await Section.bulkCreate(updateSectionList, { 
            updateOnDuplicate: ['chapterName', 'record', 'updated_at'],
            transaction
          });
        }

        // 移动文件
        if(acrossChapter && acrossChapter.length){
          for (const item of acrossChapter) {
              const oldPathDir = item[0][0]+'/'+item[0][1]+item[0][2];
              const movePathDir = item[1][0]+'/'+item[1][1]+item[1][2];

              const oldHistoryFile = item[0][0]+'/'+item[0][1]+'.zip';
              const historyFile = item[1][0]+'/'+item[1][1] + '.zip';
              const oldPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${oldPathDir}`;
              const oldHistoryPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${oldHistoryFile}`;
              
              const isExist  = await fs.exists(oldPath);
              if(!isExist) {
                  throw new Error('当前文件不存在');
              }
              const movePath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${movePathDir}`;
              const checkDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${item[1][0]}`;
              const historyPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${historyFile}`;

              const isExistDir = await fs.exists(checkDir);
              if(!isExistDir){
                await this.ctx.service.file.mkdirs(checkDir);
              }
              fs.renameSync(oldPath, movePath, function(err){
                  if(err){
                    throw err;
                  }
              });
              // 移动历史文件
              const isExistHistory = await fs.exists(oldHistoryPath);
              if (isExistHistory) {
                await fs.rename(oldHistoryPath, historyPath);
              }
            }
        }

        return '成功';
      }

      // 删除课程
      async deleteCourse(courseSlug, userID, transaction) {
        const { model } = this.ctx;
        const { Course, Section, SectionRecord } = model;
        const { service } = this.ctx;

        const courseResponse = await Course.findOne({
          where: {courseSlug},
          attributes: ['id', 'indics', 'createrID', 'teachers', 'containerInfo'],
          transaction
        });

        // 课程不存在说明删除了
        if(!courseResponse){
          return;
        }

        // 无管理者或者非创建者，那么解除关联 !!! 应当单独开设函数unbind解绑 !!!
        if(!courseResponse.createrID || userID !== parseInt(courseResponse.createrID, 10)){
          let teachers = courseResponse.teachers ? courseResponse.teachers : [];
          const userIndex = teachers.findIndex(e => parseInt(e, 10) === userID);
          if(userIndex !== -1){
            teachers.splice(userIndex, 1);

            return await Course.update({teachers}, {
              where: { courseSlug },
              transaction
            });
          }

          return;
        }

        // 获取课程章节id
        const sectionResponse = await Section.findAll({
          where: {
            courseID: courseResponse.id
          },
          attributes: ['id'],
          transaction
        });

        // 当前节不存在
        if (!sectionResponse) {
          return;
        }
  
        const sectionIDList = sectionResponse.map(
          section => {
            const sectionValue = section.dataValues;
            return sectionValue.id
          }
        );

        // 删除课程运行记录
        await SectionRecord.destroy({
          where: {
            sectionID: { [Op.in]: sectionIDList }
          },
          transaction
        });
      

        // 删除章节
        await Section.destroy({
          where: {courseID: courseResponse.id},
          transaction
        });

        // 删除课程
        await Course.destroy({
            where: {courseSlug},
            transaction
        });

        await service.icourse.clearContainer(this.ctx.schoolSlug, courseSlug);

        // 拼接主目录
        const pathCourseDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}`;
        const pathInputDir = `${pathCourseDir}/input`;
        const pathAssetsDir = `${pathCourseDir}/assets`;

        // 获取资源link文件，去数据库剔除数量
        const inputList = await service.course.list(pathInputDir);
        const assetsList = await service.course.list(pathAssetsDir);
        const indics = courseResponse.indics;

        // 需要释放的Hash值
        let hashs = [];

        // 释放每种课程资源的数据
        for(const chapter of indics) {
          const chapterDir = `${pathCourseDir}/${chapter.chapterName}`;
          for(let section of chapter.sections){
            switch(section.sectionType) {
              // FPS格式的XML文件，获取其引用的资源Hash
              case 'OJ':
                {
                  const path = `${chapterDir}/${section.sectionName}.xml`;
                  const fpsHashs = await service.file.getFPSXMLSharedFileHashs(path);
                  hashs = hashs.concat(fpsHashs);
                }
                break;
              case 'AI':
              case 'OI':
                break;
              default:
                console.error(`无法识别的${section.sectionType}`);
                break;
            }
          }
        }

        const types = {
          input: inputList,
          assets: assetsList
        }

        for(let type in types){
          const items = types[type];
          for(let item of items){
            const path = `${pathCourseDir}/${type}/${item.name}`;
            let linkString = null;
            try {
              linkString = await fs.readlink(path);
            }
            catch(e) {
              console.debug(`删除课程：尝试获取共享文件引用失败，${path}，${e.message}！`);
              continue;
            }
            
            // 找到对应hash
            const linkParts = linkString.split('/');
            const linkName = linkParts[linkParts.length - 1];
            const matchFileName = linkName.match(/(^[^\.]+)\.(.+)$/);
            hashs.push(matchFileName[1]);
          }
        }
        
        // 删除数据库存储hash
        const { mainModel } = app;

        try{
          // 启用事务
          const transactionMain = await mainModel.transaction({autocommit: false});

          // 获取当前课程数量存储在学校中
          const courseNum = await model.Course.count({ transaction });  

          await mainModel.query(`UPDATE school SET courseNum = ? where slug = ?`,
            { replacements: [courseNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });

          await service.file.removeSharedFilesRef(hashs, transactionMain);

          await transactionMain.commit();
        }catch(e){
          console.error('deleteCourse', e.message);
          if(transactionMain) {
            await transactionMain.rollback();
          }

          throw e;
        }

        // 移除课程包及学生数据
        await fsExtra.remove(`${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}`);
        await fsExtra.remove(`${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}`);
      }

      // 删除课节
      async deleteSection(courseSlug, chapterName, sectionName, transaction){
        const { mainModel } = app;
        const { service, model } = this.ctx;
        const { Course, Section, SectionRecord } = model;

        const courseResponse = await Course.findOne({
          where: {courseSlug},
          attributes: ['id', 'indics'],
          transaction,
          raw: true
        });

        // 课程不存在说明已经删除了
        if(!courseResponse){
          return;
        }

        const { indics } = courseResponse;

        // 删除缓存对应节， 获取文件名
        const chapter = indics.find(c => c.chapterName === chapterName);
        const { sections } = chapter;
        const sectionIndex = sections.findIndex(s => s.sectionName === sectionName);

        const section = sections[sectionIndex];
        sections.splice(sectionIndex, 1);

        for(let i = 1; i <= sections.length; i++) {
          sections[i - 1].sectionIndex = i;
        }

        let path = null;
        let hashs = [];

        const chapterDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
        if (section) {
          switch(section.sectionType) {
            // FPS格式的XML文件，获取其引用的资源Hash
            case 'OJ':
              {
                path = `${chapterDir}/${sectionName}.xml`;
                try {
                  const fpsHashs = await service.file.getFPSXMLSharedFileHashs(path);
                  hashs = hashs.concat(fpsHashs);
                } catch(e) {
                  console.error(`OJ课程文件解析失败：${path}！`);
                  console.error(e);
                }
              }
              break;
            case 'AI':
              path = `${chapterDir}/${sectionName}.ipynb`;
              // 如果为项目书获取同章的所有数据
              if (section.isProjectBook) {
                // 获取其他节信息
                const otherSections = await Section.findAll({
                  where: {
                    courseID: courseResponse.id,
                    chapterName: chapterName
                  },
                  transaction,
                  raw: true
                });

                const updateSectionList = [];

                // 2.若前后类型一致，则无需判断，直接整理
                // 3.若之前为项目书，设置当前为AI课程时，需清除绑定他的记录
                for (const sectionValue of otherSections) {
                  let { id, record, sectionName: sN } = sectionValue;

                  // 本次更新的节，合并之前更新内容进行存储
                  if (sN === sectionName) {
                    continue;
                  }

                  // 不属于本次更新的节，检查其他节是否需要更新，不存在记录的不需要检测
                  if (!record) {
                    continue;
                  }
                  
                  // 不属于本次更新的节，检查其他节是否需要更新
                  let ifEdit = false;
                  for (const key in record) {
                    if(key === 'isProjectBook'){
                      continue;
                    }

                    if (!record[key] || !record[key].project) {
                      continue;
                    }
                    
                    const { project } = record[key];
                    if (!project || JSON.stringify(project) === '{}') {
                      continue;
                    }

                    const { sectionName: PSN } = project;
                    if (!PSN || PSN !== sectionName) {
                      continue;
                    }

                    // 告诉本节需更新
                    ifEdit = true;
                    delete record[key].project;
                  }
                  // 本节不更新，则放弃
                  if (!ifEdit) {
                    continue;
                  }

                  updateSectionList.push({
                    'id': id,
                    'record': record,
                    'updated_at': moment().format('YYYY-MM-DD HH:mm:ss')
                  });
                }

                if (updateSectionList && updateSectionList.length) {
                  await Section.bulkCreate(updateSectionList, { 
                    updateOnDuplicate: ['record', 'updated_at'],
                    transaction
                  });
                }

              }
              break;
            case 'OI':
              path = `${chapterDir}/${sectionName}.xml`;
              break;
            case 'Access':
            case 'Excel':
            case 'Scratch':
            case 'MicroBit':
              path = `${chapterDir}/${sectionName}.json`;
              break;
            case 'CodeBlank':
              path = `${chapterDir}/${sectionName}.code`;
              break;
            case 'PPT':
              path = `${chapterDir}/${sectionName}.slide`;
              
              // 删除资源文件
              try {
                const fileContent = await this.ctx.service.course.getFile(path, true);
                const { assetDir, fileName, fileExt } = fileContent;
                const name = `${fileName}.${fileExt}`;
                await this.ctx.service.course.deleteSourceFile(courseSlug, assetDir, name);
              } catch(e) {
                console.error(`删除PPT资源文件失败：${path}！`);
                console.error(e);
              }
              break;
            default:
              throw new Error(`无法识别的${section.sectionType}`);
          }
        }

        // 更新课程记录
        await Course.update({
          indics
        },{
          where: {courseSlug},
          transaction
        });

        // 获取课程章节id
        const sectionResponse = await Section.findOne({
          where: {
            courseID: courseResponse.id,
            chapterName,
            sectionName
          },
          attributes: ['id'],
          transaction
        });

        // 当前节不存在
        if (!sectionResponse) {
          return;
        }

        // 删除课程运行记录
        await SectionRecord.destroy({
          where: {
            sectionID: sectionResponse.id
          },
          transaction
        });

        // 删除数据库节记录
        await Section.destroy({
          where: {courseID: courseResponse.id, chapterName, sectionName},
          transaction
        });

        // 如果需要清理引用文件则启用主库事务
        if(hashs.length) {
          let transactionMain = null;
          try{
            transactionMain = await mainModel.transaction({autocommit: false});
            await service.file.removeSharedFilesRef(hashs, transactionMain);
            await transactionMain.commit();
          }catch(e){
            console.error('deleteCourse', e.message);
            if(transactionMain) {
              await transactionMain.rollback();
            }
            
            throw e;
          }
        }

        const exist = await fs.exists(path);
        if (!exist) {
          return;
        }

        // 删除节数据文件
        await fsExtra.remove(path);

        // 删除节历史记录文件
        const zipPath = `${chapterDir}/${sectionName}.zip`;

        fs.access(zipPath, fs.constants.F_OK, async (err) => {
          if (err) {
            // 如果压缩文件不存在忽略
          } else {
            // 如果存在压缩文件，删除
            await fsExtra.remove(zipPath);
          }
        });
      }

      // 删除章
      async deleteChapter(courseSlug, chapterName, transaction){
        const { mainModel } = app;
        const { model, service } = this.ctx;
        const { Course, Section, SectionRecord } = model;
        const courseResponse = await Course.findOne({
          where: {courseSlug},
          attributes: ['id', 'indics', 'statist'],
          raw: true,
          transaction
        });

        // 课程不存在说明删除了
        if(!courseResponse){
          return;
        }

        const { indics } = courseResponse;
        const chapterIndex = indics.findIndex(c => c.chapterName === chapterName);
        const chapter = indics[chapterIndex];
        indics.splice(chapterIndex, 1);
        const { sections } = chapter;
        if (indics && indics.length) {
          for(let i = 1; i <= indics.length; i++) {
            indics[i - 1].chapterIndex = i;
          }
        }
  
        // 需要释放的Hash值
        let hashs = [];

        // 章节目录
        const chapterDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;

        for(let section of sections){
          switch(section.sectionType) {
            // FPS格式的XML文件，获取其引用的资源Hash
            case 'OJ':
              {
                try {
                  const { sectionName } = section;
                  const path = `${chapterDir}/${sectionName}.xml`;
                  const fpsHashs = await service.file.getFPSXMLSharedFileHashs(path);
                  hashs = hashs.concat(fpsHashs);
                } catch(e) {
                  console.error(`获取FPSXML引用资源Hash失败：${section.sectionName}！`);
                  console.error(e);
                }
              }
              break;
            case 'Access':
            case 'Excel':
            case 'AI':
            case 'OI':
            case 'Scratch':
            case 'MicroBit':
            case 'CodeBlank':
            case 'PPT':
              break;
            default:
              throw new Error(`无法识别的${section.sectionType}`);
          }
        }

        await Course.update({
          indics,
          statist: {
            ...courseResponse.statist,
            chapters: indics && indics.length ? indics.length : 0
          }
        },{
          where: {courseSlug},
          transaction
        });

        // 获取课程章节id
        const sectionResponse = await Section.findAll({
          where: {
            courseID: courseResponse.id,
            chapterName
          },
          attributes: ['id'],
          transaction
        });

        // 当前节不存在
        if (!sectionResponse) {
          return;
        }
  
        const sectionIDList = sectionResponse.map(
          section => {
            const sectionValue = section.dataValues;
            return sectionValue.id
          }
        );

        // 删除课程运行记录
        await SectionRecord.destroy({
          where: {
            sectionID: { [Op.in]: sectionIDList }
          },
          transaction
        });
        
        // 删除数据库节记录
        await Section.destroy({
          where: {courseID: courseResponse.id, chapterName},
          transaction
        });
        
        // 如果需要清理引用文件则启用主库事务
        if(hashs && hashs.length) {
          try{
            const transactionMain = await mainModel.transaction({autocommit: false});
            await service.file.removeSharedFilesRef(hashs, transactionMain);
            await transactionMain.commit();
          }catch(e){
            console.error('deleteCourse', e.message);
            if(transactionMain) {
              await transactionMain.rollback();
            }
            
            throw e;
          }
        }

        const exist = await fs.exists(chapterDir);
        if (!exist) {
          return;
        }

        // 删除章数据文件
        await fsExtra.remove(chapterDir);

      }

      // 删除资源文件
      async deleteSourceFile(courseSlug, type, name){

        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${type}/${name}`;

        const exist = await fs.exists(path);
        if (!exist) {
          return;
        }

        let linkString = null;
        try {
          linkString = await fs.readlink(path);
        }
        catch(e) {
          console.debug(`删除课程：尝试获取共享文件引用失败，${path}，${e.message}！`);
          return;
        }

        // 找到对应hash
        const linkParts = linkString.split('/');
        const linkName = linkParts[linkParts.length - 1];
        const matchFileName = linkName.match(/(^[^\.]+)\.(.+)$/);
        const hash = matchFileName[1];

        // 干掉文件
        await fsExtra.remove(path);

        // 找不到引用文件，打住
        if(!hash){
          return;
        }

        let transaction = null;

        try{
          // 数据库存储hash
          const { mainModel } = app;

          // 启用事务
          transaction = await mainModel.transaction({autocommit: false});
          const result = await mainModel.SharedFiles.findOne({
            where: { hash },
            attributes: ['ref_count'],
            transaction
          });

          await mainModel.SharedFiles.update({
            ref_count: result['ref_count'] - 1,
          }, {
            where: {
              hash
            },
            transaction
          });
  
          await transaction.commit();
        }catch(e){
          console.error('deleteSourceFile', e.message);
          if(transaction) {

            await transaction.rollback();
          }

          throw e;
        }
      }

      // 获取项目书详情
      async getSectionProjectDetail(courseSlug, chapterName, sectionName, ext, projectBindProjectMap, transaction) {
        const { ctx } = this;
        let content = null;
        try {
          const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
          content = await ctx.service.course.getFile(path, true);
        } catch(e) {
          throw new Error(e.message);
        }

        let { cells, metadata } = content;
        let { isProjectBook } = metadata;
        const record = { isProjectBook: isProjectBook };

        let ifSave = false;
        // 不是项目书,整理相关的关联记录
        if (!isProjectBook && cells && cells.length) {
          for (const cell of cells) {
            const { cell_type, metadata } = cell;
            const { linkProject, UUID, type } = metadata;
            if (cell_type !== 'code' || type) {
              continue;
            }

            // 不存在绑定项目书返回
            if (!linkProject) {
              continue;
            }

            // 检查该章节下是否存在该项目书文件,不存在直接返回
            let response = null;
            try {
              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${linkProject.sectionName}.${ext}`;
              response = await ctx.service.course.getFile(path, true);
            } catch(e) {
              continue;
            }
            // 以下对项目书进行检测
            if (!response.cells || !response.cells.length) {
              continue;
            }

            const cellProject = response.cells.find((cValue) => cValue.metadata.UUID === linkProject.UUID);
            if (!cellProject || cellProject.cell_type !== 'code') {
              continue;
            }

            const sourceText = cellProject.source && cellProject.source.length ? `${cellProject.source.join('')} ` : '';
            // 隐藏并自动执行不允许关联
            if (sourceText.match(/^# >隐藏并自动执行/)) {
              continue;
            }

            if (!record[UUID]) {
              record[UUID] = {};
            }

            const { UUID: PSUUID, sectionName: PSN } = linkProject;
            if (!PSN) {
              continue;
            }
            // 判断是否在需绑定中，无则跳过
            const cKey = `${PSN}_${PSUUID}`;
            if (projectBindProjectMap[cKey]) {
              continue;
            }

            record[UUID].project = linkProject;

            projectBindProjectMap[cKey] = true;
          }
        }

        if (ifSave) {
          await ctx.service.course.updateAIFile(courseSlug, chapterName, sectionName, content);
        }
      
        return { record, projectBindProjectMap, isProjectBook };
      }

      // 上传课程，创建课程及节
      async createCourseSections(item, transaction, defaultUserID) {
        const { ctx } = this;
        const { user } = ctx.session;
        const { model } = this.ctx;
        const { Course, Section } = model;

        // 查重 courseSlug
        if(item.courseSlug){
          const isExist = await Course.findOne({
            where: {courseSlug: item.courseSlug},
            transaction
          })

          if(isExist){
            throw Error('课程标识不允许重复！')
          }
        }

        // 查重 courseName
        let createrID = null;
        
        // 兼容课程预安装
        if (defaultUserID) {
          createrID = defaultUserID;
        }
        
        if (user && user.id) {
          createrID = user.id;
        }

        if(item.courseName){
          const isExist = await Course.findOne({
            where: {courseName: item.courseName, createrID },
            transaction
          })

          if(isExist){
            throw Error('课程名称不允许重复！')
          }
        }

        const courseResponse = await Course.create(item, {
          transaction
        });

        const allSection = await Section.findAll({
          where: {
            courseID: courseResponse.id,
          },
          attributes: ['id','chapterName', 'sectionName', 'sectionType', 'record'],
          transaction
        });

        
        // 创建章节信息
        const chapterList = item.indics;
        const { chapterNameMap } = item;
        const sections = [];

        try {
          for(let chapter of chapterList){
            let projectBindProjectMap = {};
            const { chapterName } = chapter;
            // 从course.json中读取章节名称，如果章节没有对应名称，则记录章节文件夹名称
            chapter.chapterTitle = (chapterNameMap && chapterNameMap[chapterName]) ? chapterNameMap[chapterName] : chapterName; 
            for(let section of chapter.sections){
              // 查重
              const isExist = sections.find(i=>i.chapterName===chapter.chapterName && i.sectionName===section.sectionName);
              const isSectionExit = allSection.find(i=>i.chapterName===chapter.chapterName && i.sectionName===section.sectionName);
              // 获取文件信息并整理相关资料
              if (section.sectionType === 'AI') {
                const sectionRecord = await ctx.service.course.getSectionProjectDetail(item.courseSlug, chapter.chapterName, section.sectionName, section.ext, projectBindProjectMap, transaction);
                projectBindProjectMap = sectionRecord.projectBindProjectMap;
                section.isProjectBook = sectionRecord.isProjectBook;
              }
              // 不重复且当前章节不存在
              if(!isExist && !isSectionExit){
                sections.push({
                  chapterName: chapter.chapterName,
                  courseID: courseResponse.id,
                  sectionName: section.sectionName,
                  sectionType: section.sectionType,
                  ext: section.ext,
                });
                continue;
              }

              if(!isSectionExit){
                sections.push({
                  chapterName: chapter.chapterName,
                  courseID: courseResponse.id,
                  sectionName: section.sectionName,
                  sectionType: section.sectionType,
                  ext: section.ext,
                  record,
                });
                continue;
              }

              // 存在同一文件，进行替换
              if (section.sectionType === 'AI' && isSectionExit.record) {
                // 不属于本次更新的节，检查其他节是否需要更新
                for (const key in isSectionExit.record) {
                  if(key === 'isProjectBook'){
                    continue;
                  }

                  const recordValue = isSectionExit.record[key];
                  if (!recordValue) {
                    continue;
                  }
                  if (!record[key]) {
                    record[key] = recordValue
                  } else {
                    record[key] ={
                      ...recordValue,
                      ...record[key],
                    }
                  }
                }
              }
              
              sections.push({
                id: isSectionExit.id,
                chapterName: chapter.chapterName,
                courseID: courseResponse.id,
                sectionName: section.sectionName,
                sectionType: section.sectionType,
                ext: section.ext,
              });
            }
          }
        } catch(e) {
          console.error(e);
          throw new Error(e.message);
        }

        // 创建课程
        const sectionList = await Section.bulkCreate(sections, { 
          updateOnDuplicate: ['record', 'updated_at'],
          raw: true,
          transaction
        });

        // section ID和草稿发布状态存入缓存
        for (const newSection of sectionList) {
          const {id: sectionID, chapterName, sectionName, sectionType, ext } = newSection;
          // 从文件读取课程名称并更新到缓存中
          let content = null;
          let path = null;
          let sectionTitle = null;
          try {
            path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${item.courseSlug}/${chapterName}/${sectionName}.${ext}`;
            const isParse = ['AI', 'Access', 'Excel', 'Scratch', 'MicroBit', 'PPT', 'CodeBlank'].includes(sectionType);
            content = await ctx.service.course.getFile(path, isParse);
          } catch(e) {
            console.log('上传课程时读取文件失败', path);
            throw new Error(e.message);
          }
          if(sectionType === 'AI') {
            sectionTitle = content ? content.metadata.title : null;
          } else if (['Access', 'Excel', 'Scratch', 'MicroBit', 'PPT', 'CodeBlank'].includes(sectionType)) {
            sectionTitle = content ? content.title : null;
          } else {
            const $xml = cheerio.load(content, {
              xmlMode: true,
              decodeEntities: true,
            });
            sectionTitle = $xml('title').text() || null;
          }

          const currentChapter = chapterList.find(chapter => chapter.chapterName === chapterName);
          const currentSection = currentChapter.sections.find(section => section.sectionName === sectionName && section.sectionType === sectionType && section.ext === ext);
          currentSection.sectionID = sectionID;
          currentSection.status = true;
          currentSection.sectionTitle = sectionTitle ? sectionTitle : sectionName;
        }
        
        await Course.update({
          indics: chapterList
        }, {
          where: { 
            courseSlug: item.courseSlug
          },
          transaction
        });

        // 设置路径
        const courseAssetsPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${item.courseSlug}/assets`;
        const courseInputPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${item.courseSlug}/input`;

        const courseAssetsExist = await fs.exists(courseAssetsPath);
        if (!courseAssetsExist) {
          await this.ctx.service.file.mkdirs(courseAssetsPath);
        }

        const courseInputExist = await fs.exists(courseInputPath);
        if (!courseInputExist) {
          await this.ctx.service.file.mkdirs(courseInputPath);
        }

      }
      
      // 课程 内容目录保存 saveCourseDirectory
      async saveCourseDirectory(courseSlug, { newChapterName, newChapterTitle, chapterIndex }, transaction) {
        const { model } = this.ctx;
        const { Course } = model;

        if (!newChapterName) {
          throw new Error('请输入章文件夹名称');
        }
  
        if (!newChapterTitle) {
          throw new Error('请输入章名称');
        }

        const result = await Course.findOne({
          where: { courseSlug },
          attributes: ['indics', 'statist'],
          raw: true,
          transaction
        });

        if (!result) {
          throw new Error('课程不存在');
        }

        let { indics = [], statist = {} } = result;

        if (indics.find((i) => i.chapterName === newChapterName)) {
          throw new Error('章名称不允许重复！');
        }

        const chapter = {
          sections: [],
          chapterName: newChapterName,
          chapterTitle: newChapterTitle,
          chapterIndex: chapterIndex,
        };

         // 没有章，则作为第一章
        if (!indics || !indics.length) {
          indics = [chapter];
        } else {
          // 已有章，作为选中章后一章
          indics.splice(chapterIndex - 1, 0, chapter);

          for (let i = 1; i <= indics.length; i++) {
            indics[i - 1].chapterIndex = i;
          }
        }

        const item = {
          indics,
          statist: {
            ...statist,
            chapters: indics.length
          }
        };

        return await Course.update(item, {
          where: { courseSlug },
          transaction
        });
      }

      // 修改课程章节目录
      async modifyCourseDirectory(courseSlug, { chapterName, sectionName, modifyType, modifyValue }, transaction) {
        const { ctx } = this;
        const { model } = ctx;
        const { Course } = model;

        if (modifyValue === undefined) {
          throw new Error(`请提供目录调整参数`)
        }

        const result = await Course.findOne({
          where: { courseSlug },
          attributes: ['indics', 'statist'],
          raw: true,
          transaction
        });

        if (!result) {
          throw new Error(`课程 ${courseSlug} 不存在`)
        }

        const { indics = [] } = result;
        let newIndics = indics;

        switch (modifyType) {
          case 'collapse':
            const selectChapter = newIndics.find((i) => i.chapterName === chapterName);
            if (!selectChapter) {
              throw new Error(`课程章节 ${chapterName} 不存在`)
            }
            selectChapter[modifyType] = modifyValue;
            break;
          case 'mustBeDone':
          case 'status':
            {
              const selectChapter = newIndics.find((i) => i.chapterName === chapterName);
              if (!selectChapter || !selectChapter.sections) {
                throw new Error(`课程章节 ${chapterName} 不存在`)
              }

              const selectSection = selectChapter.sections.find((i) => i.sectionName === sectionName);
              if (!selectSection) {
                throw new Error(`课程章节 ${chapterName} 下的小节 ${sectionName} 不存在`)
              }

              selectSection[modifyType] = modifyValue;
            }
            break;
          default:
            throw new Error(`未处理的修改类型${modifyType}`);
        }

        await Course.update({
          indics: newIndics
        }, {
          where: { courseSlug },
          transaction
        });

        return newIndics;
      }

      // 课程 内容目录上传后调用 存缓存 存 section
      async contentDirectory(node, transaction){
        const { ctx } = this;
        const { model } = this.ctx;
        const { Course, Section, SectionRecord, TeamUser } = model;
        const { courseSlug, section, indics } = node;
        // 查询课程是否存在
        const course = await Course.findOne({where: { courseSlug }, transaction})
        if(!course){
          throw Error('当前课程不存在！')
        }

        const { chapterName, sectionName, ext } = section;

        // 查询Section，更新之，如果不存在就需要创建
        const existedSection = await Section.findOne({
          where: {
            courseID: course.id,
            chapterName: chapterName,
            sectionName: sectionName
          },
          transaction,
          raw: true
        });

        let sectionTitle = sectionName;
        // ipynb文件是否关联项目书
        if (ext === 'ipynb') {
          let content = null;
          try {
            const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
            content = await ctx.service.course.getFile(path, section.sectionType === 'AI');
          } catch(e) {
            throw new Error(e.message);
          }

          let { isProjectBook, cells } = content;
          cells = cells.value ? cells.value : cells;
          // 循环赋值UUID
          let index = 0;
          let ifSave = false;
          for (const cell of cells) {
            index += 1;
            if (!cell.metadata.UUID) {
              ifSave = true;
              cell.metadata.UUID = makeUUID(index);
            }
          }

          if (ifSave) {
            await ctx.service.course.updateAIFile(courseSlug, chapterName, sectionName, content);
          }

          const oldIsProjectBook = existedSection && existedSection.record ? existedSection.record.isProjectBook : false;

          section.record = existedSection && existedSection.record ? { ...existedSection.record, isProjectBook } : { isProjectBook };

          // 1.若之前为AI课程，设置当前为项目书时，需清除record的绑定记录
          if (!oldIsProjectBook && isProjectBook) {
            for (const sKey in section.record) {
              if (sKey === 'isProjectBook') {
                continue;
              }

              if (!section.record[sKey] || !section.record[sKey].project) {
                continue;
              }

              const { project } = section.record[sKey];
              if (!project || JSON.stringify(project) === '{}') {
                continue;
              }

              delete section.record[sKey].project;
            }
          }
          
          // 当当前为AI课程时，判断是否有链接项目书记录，有则去替换同章节的绑定记录，没有则进行其他判断
          const projectBindProjectMap = {};
          if (!isProjectBook && cells && cells.length) {
            for (const cell of cells) {
              const { cell_type, linkProject } = cell;
              const { UUID } = cell.metadata;

              if (cell_type !== 'code') {
                continue;
              }

              if (!linkProject || !linkProject.UUID) {
                if (section.record[UUID] && section.record[UUID].project) {
                  delete section.record[UUID].project;
                }
                continue;
              }

              // 检查该章节下是否存在该项目书文件,不存在直接返回
              let response = null;
              try {
                const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${linkProject.sectionName}.${ext}`;
                response = await ctx.service.course.getFile(path, true);
              } catch(e) {
                continue;
              }
              if (response.cells) {
                response.cells = response.cells.value ? response.cells.value : response.cells;
              }
              // 以下对项目书进行检测
              if (!response.cells || !response.cells.length) {
                continue;
              }

              const cellProject = response.cells.find((cellValue) => cellValue.UUID === linkProject.UUID);
              if (!cellProject || cellProject.cell_type !== 'code') {
                continue;
              }

              const sourceText = cellProject.source && cellProject.source.length ? `${cellProject.source.join('')} ` : '';
              // 隐藏并自动执行不允许关联
              if (sourceText.match(/^# >隐藏并自动执行/)) {
                continue;
              }

              // 微应用不允许关联
              if (sourceText.match(/YopuWidget\((.+)\)/)) {
                continue;
              }

              if (sourceText.match(/(?<=Video\()(.*?)(?=\))/g)) {
                continue;
              }
              
              if (sourceText.match(/(?<=Image\()(.*?)(?=\))/g)) {
                continue;
              }

              if (!section.record[UUID]) {
                section.record[UUID] = {};
              }

              section.record[UUID].project = linkProject;

              const { UUID: PSUUID, sectionName: PSN } = linkProject;
              if (!PSN) {
                continue;
              }
              // 判断是否在需绑定中，无则跳过
              const cKey = `${PSN}_${PSUUID}`;
              if (projectBindProjectMap[cKey]) {
                continue;
              }

              projectBindProjectMap[cKey] = true;
            }
          }

          // 获取其他节信息
          const otherSections = await Section.findAll({
            where: {
              courseID: course.id,
              chapterName: chapterName
            },
            transaction,
            raw: true
          });

          const updateSectionList = [];

          // 2.若前后类型一致，则无需判断，直接整理
          // 3.若之前为项目书，设置当前为AI课程时，需清除绑定他的记录
          for (const sectionValue of otherSections) {
            let { id, record, sectionName: sN } = sectionValue;

            // 本次更新的节，合并之前更新内容进行存储
            if (sN === sectionName) {
              continue;
            }

            // 不属于本次更新的节，检查其他节是否需要更新，不存在记录的不需要检测
            if (!record) {
              continue;
            }
            
            // 不属于本次更新的节，检查其他节是否需要更新
            let ifEdit = false;
            for (const key in record) {
              if(key === 'isProjectBook'){
                continue;
              }

              if (!record[key] || !record[key].prject) {
                continue;
              }
              
              const { project } = record[key];
              if (!project || JSON.stringify(project) === '{}') {
                continue;
              }

              const { UUID: PSUUID, sectionName: PSN } = project;
              if (!PSN) {
                continue;
              }

              // 如果同一章节，有同名绑定，需再次检测当前节是否为项目书，否则去除
              if (PSN === sectionName && !isProjectBook) {
                // 告诉本节需更新
                ifEdit = true;
                delete record[key].project;
                continue;
              }

              // 判断是否在需绑定中，无则跳过
              const cKey = `${PSN}_${PSUUID}`;
              if (!projectBindProjectMap[cKey]) {
                continue;
              }

              // 告诉本节需更新
              ifEdit = true;
              delete record[key].project;
            }
            // 本节不更新，则放弃
            if (!ifEdit) {
              continue;
            }

            updateSectionList.push({
              'id': id,
              'record': record,
              'updated_at': moment().format('YYYY-MM-DD HH:mm:ss')
            });
          }

          if (updateSectionList && updateSectionList.length) {
            await Section.bulkCreate(updateSectionList, { 
              updateOnDuplicate: ['record', 'updated_at'],
              transaction
            });
          }

          // 覆盖项目书显示
          for (const chapter of indics) {
            if (chapter.chapterName !== chapterName) {
              continue;
            }

            for (const section of chapter.sections) {
              if (section.sectionName !== sectionName) {
                continue;
              }

              section.isProjectBook = isProjectBook;
            }
          }

          // 获取文件中保存的课程名称字段
          sectionTitle = content.metadata.title ? content.metadata.title : null;
        }

        // 客观题和OJ题获取课程名称
        if (ext === 'xml') {
          let content = null;
          try {
            const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
            content = await ctx.service.course.getFile(path, false);
          } catch(e) {
            throw new Error(e.message);
          }
          const $xml = cheerio.load(content, {
              xmlMode: true,
              decodeEntities: true,
          });
          sectionTitle = $xml('title').text() ? $xml('title').text() : sectionName;
        }

        if (ext === 'json' || ext === 'slide' || ext === 'code') {
          let content = null;
          try {
            const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
            content = await ctx.service.course.getFile(path, true);
          } catch(e) {
            throw new Error(e.message);
          }

          sectionTitle = content.title || sectionName;
        }

        let newSectionID = null;
        
        // 如果课程已存在更新课程
        if(existedSection) {
          // 更新课程
          const newSection = { ...section, historyRecords: null }; // 清空历史记录数据
          await Section.update(newSection, {
            where: {
              id: existedSection.id,
            },
            transaction
          });
          newSectionID = existedSection.id;

          // 如果存在历史记录文件则删除
          const zipPath = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.zip`;

          fs.access(zipPath, fs.constants.F_OK, async (err) => {
            if (err) {
              // 如果压缩文件不存在忽略
            } else {
              // 如果存在压缩文件，删除
              await fsExtra.remove(zipPath);
            }
          });

        } else {
          // 创建课程
          const newSection = await Section.create({
            ...section, courseID: course.id
          }, { transaction });
          newSectionID = newSection.id;
        }
        
        // 上传课程需在缓存中存入课程ID和草稿、发布状态
        const currentChapter = indics.find(chapter => chapter.chapterName ===  chapterName);
        const currentSection = currentChapter.sections.find(section => section.sectionName === sectionName);
        currentSection.sectionID = newSectionID;
        currentSection.status = true;
        currentSection.sectionTitle = sectionTitle ? sectionTitle : sectionName;
        
        // 删除多余字段，TODO: 考虑在前端上传时删除冗余字段
        if (currentSection.index) delete currentSection.index;
        if (currentSection.chapterName) delete currentSection.chapterName;

        // 更新缓存
        await Course.update({
          indics
        }, {
          where: { courseSlug },
          transaction
        });
      }

      // 课程 数据管理 获取文件列表
      // 列举指定目录中的文件 !!!请调用者负责检查用户是否有权限进行该操作
      async list(targetDirectory, type) {
        // console.log('targetDirectory :>> ', targetDirectory);
        const { ctx } = this;
        // 读之前检查目录是否存在
        const ifExist = await fs.exists(targetDirectory);

        if (!ifExist) {
            return [];
        }
        
        // 获取目录文件
        const fileList = await fs.readdir(targetDirectory);

        if (!fileList || !fileList.length) {
            return [];
        }

        // 定义返回数组
        const fileDirList = [];
        for (const dir of fileList) {
          try {

            // 获取对应数据
            const pathDir = `${targetDirectory}/${dir}`;
            const fileInfo = await fs.stat(pathDir);
            // 获取文件信息错误，则视为无该文件
            if (!fileInfo) {
                continue;
            }

            // 判断是否为目录
            const ifDirectory = fileInfo.isDirectory();

            // 若为目录则递归获取目录文件
            if (ifDirectory) {
                // 递归
                const childFileDir = await ctx.service.course.list(pathDir, type);

                  fileDirList.push({
                    name: dir,
                    ...fileInfo,
                    childFile: childFileDir.concat()
                });


                continue;
            }

            // 返回值处理
            fileDirList.push({
                name: dir,
                ...fileInfo
            })
          } catch(err) {
            continue;
          }
        }
        return fileDirList;
      }

      // 课程 学生 数据 班级列表
      async getStudentDataList(courseSlug) {
        const { model } = this.ctx;
        const { Course, Team } = model;
        const course = await Course.findOne({
          where: {courseSlug},
          attributes: ['id', 'teams', 'historyTeams']
        })

        const { teams, /*historyTeams*/ } = course;
        const response = {};

        if(!teams || !teams.length){
          response.now = [];
        } else {
          response.now =  await Team.findAll({
            where: {id: { [Op.in]: teams } },
            attributes: ['id', 'name', 'type', 'year']
          })
        }

        // 强制去除历史班级
        response.history = [];

        // if(!historyTeams || !historyTeams.length){
        //   response.history = [];
        // } else {
        //   response.history = await  Team.findAll({
        //     where: {id: { [Op.in]: historyTeams } },
        //     attributes: ['id', 'name', 'type', 'year']
        //   })
        // }
        return response;
      }

      // 查询课程开放班级
      async queryCourseOpenTeams(courseSlug){
        const { model } = this.ctx;
        const { Course, Team } = model;
        const course = await Course.findOne({
          where: { courseSlug },
          attributes: ['teams'],
        });

        if(!course){
          return;
        }
        let { teams } = course;

        teams = teams && typeof teams === 'string' ? JSON.parse(teams) : teams;
        return await Team.findAll({
          where: {id: {[Op.in]: teams}},
          attributes: ['id', 'name']
        })
      }

      async updateAIFile(courseSlug, chapterName, sectionName, content) {
        const { ctx } = this;
        const path = `${courseSlug}/${chapterName}`;
        const fileName = `${sectionName}.ipynb`;
        // 新建一个文件插入
        await ctx.service.file.uploadXMLFile(path, fileName, JSON.stringify(content));
      }

      // 修改非 ipynb 文件
      async updateXMLFile(xmlArgs, transaction) {
        const { sectionType, courseSlug, chapterName, sectionName, content: question, ifShowAnswer, ifSubmitLimit, submitLimit, userID } = xmlArgs;
        const { ctx } =this;

        question.title = question.title ? question.title : sectionName;

        const extMap = {
          OI: 'xml',
          OJ: 'xml',
          Access: 'json',
          Excel: 'json',
          Scratch: 'json',
          MicroBit: 'json',
          PPT: 'slide',
          CodeBlank: 'code',
        }

        let xmlContent = null;
        switch(sectionType) {
          case 'OJ':
            xmlContent = changeToOJ(question);
            break;
          case 'OI':
            xmlContent = changeToOI(question, ifShowAnswer, ifSubmitLimit, submitLimit);
            break;
          case 'Access':
          case 'Excel':
          case 'Scratch':
          case 'MicroBit':
          case 'PPT':
          case 'CodeBlank':
            xmlContent = JSON.stringify(question);
            break;
          default:
            throw new Error(`不支持的课程类型${sectionType}`);
        }
        
        if (!extMap[sectionType]) {
          throw new Error(`不支持的课程类型${sectionType}`);
        }

        const path = `${courseSlug}/${chapterName}`;
        const fileName = `${sectionName}.${extMap[sectionType]}`;
        // 新建一个文件插入
        await ctx.service.file.uploadXMLFile(path, fileName, xmlContent);
        // 保存历史记录
        if (sectionType !== 'PPT') {
          const query = { userID, courseSlug, chapterName, sectionName, fileContent: xmlContent, modifiedType: '发布' };
          await ctx.service.section.saveHistoryRecords(query, transaction);
        }
      }

      // 列举题目全部数据文件 listXmlQuestionData
      async listXmlQuestionData(paths) {
        const result = []
        for (const path of paths) {
           let obj = {...path}
            // 文件文件是否存在
            if(path['in']) {
              const pathExist = await fs.exists(path['in']);
              if(pathExist){
                const stat = await fs.stat(path['in']);
                obj={...obj, insize: stat.size}
              }  
            }

            if(path['out'])
            {
              const outPathExist = await fs.exists(path['out']);
              if(outPathExist){
                const outstat = await fs.stat(path['out']);
                obj={...obj, outsize: outstat.size }
              }
            }

            result.push(obj)
        }

        return result;
      }

      async getOJSourceFilePath({ schoolSlug, courseSlug, chapterName, sectionName, filename, ext }) {
        const { ctx } = this;
        const { app } = ctx;

        // 获取课程文件内容
        let fileContentOJ = null;
        const coursePath = `${app.config.file.dir}/${schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        
        try {
          fileContentOJ = await ctx.service.course.getFile(coursePath, false);
        } catch (error) {
          console.error(error);
          throw new Error('课程文件读取出错');
        }
        
        if (!fileContentOJ) {
          throw new Error('课程文件不存在');
        }

        const questionOJ = getOJAnswer(fileContentOJ);
        if (!questionOJ) {
          throw new Error('课程文件内容不合法');
        }

        const { judgeMenu: { file = [] } = {} } = questionOJ;
        
        // 获取文件路径
        const curFile = file.find(item => item.name === filename);
        if (!curFile) {
          throw new Error(`${filename}文件不存在`);
        }

        const path = curFile.path;

        return path;
      }

      // 获取文件内容 
      async getxmldata(path, size = 10){  // size -> KB
        const { ctx } = this;
        const { app } = ctx;

        // 文件夹不存在
        if (!path) {
          console.error(`${path}文件不存在`);
          throw new Error(`${path}文件不存在`);
        }
       
        let filePath = path;
        const { tmpUrl, tmpDir } = app.config.file;
        const flag = filePath.startsWith(tmpUrl);
        if (flag) {
          filePath = filePath.replace(tmpUrl, tmpDir)
        }
        const exist = await fs.exists(filePath);
        if(!exist)
        {
          if (flag) {
            return '该文件已被定时清除'
          }
          return null;
        }
        let content = await fs.readFile(filePath, 'utf8');
        const fileInfo = await fs.stat(filePath);
        const fileSize = fileInfo.size ? fileInfo.size / 8192 : 0;
        content = fileSize > size  ? content.slice(0, parseInt(size * 1024, 10)) + '...(文件大于10KB，已省略剩下内容)' : content;
        return content;
      }
      
      async checkTeacherPermission(userID, courseSlug) {
        const { model } = this.ctx;
        const { Course } = model;
        const course = await Course.findOne({
          where: { courseSlug },
          raw: true
        });

        if(!course){
          return false;
        }

        const { teachers, createrID } = course;
        
        const perssion = (userID === createrID || (teachers && teachers.length && teachers.indexOf(userID) !== -1));
        if (!perssion) {
          return false;
        }

        return course;
      }

      // 获取交互式课程中所有需要提交的块
      async getAICourseSubmitBlocks(courseSlug, chapterName, sectionName) {
        const submitBlocks = [];
        const filePath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.ipynb`;
        let textContent = await fs.readFile(filePath, { encoding: 'utf-8' });
        const content = JSON.parse(textContent);
        const { cells } = content;
        if (cells && cells.length) {
          for (const cell of cells) {
            // 非代码类不要
            if(cell['cell_type'] !== 'code') {
              continue;
            }

            // 隐藏代码块不要
            if (cell.source && cell.source[0] && cell.source[0].match(/^# >隐藏并自动执行/)) {
              continue;
            }

            // 资源不要
            if(cell['metadata']['type'] === 'resources' || cell['metadata']['type'] === 'filepreviewer') {
              continue;
            }

            // 代码 + 微应用
            submitBlocks.push(cell['metadata']);
          }
        }
        return submitBlocks;
      }

      // 将学生运行结果做成答题记录格式,（某一节）
      async getMicroAppsFileRecord( microApps = [], courseSlug, userID ) {
        const { ctx } = this;
        let microAppUUIDMap = {};
        for (let i = 0; i < microApps.length; i += 1) {
          const microApp = microApps[i];
          try {
            const { UUID, type: fileType, fileName, config:sourceParams } = microApp; 
            const { extName, binary = false } = microTypeNameMap[fileType];
            const fileFullName = `${fileName}.${extName}`;

            // 拼接主目录
            const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${fileFullName}`;
            let FileValueResponse = null;
            try {
              // 按照二进制Buffer读取
              if(binary) {
                FileValueResponse = await fs.readFile(pathDir);
              }
              else {
                // 安装UTF-8编码读取
                FileValueResponse = await ctx.service.course.getFile(pathDir, false);
              }
            } catch(err) {
              console.log(err, `${fileFullName}文件不存在`);
              continue;
            }

            const fileStat = await fs.stat(pathDir)

            const record = {
              fullName: fileFullName,
              result: {
                codeResult: [],
              },
              status: "运行通过",
              type: "success",
              fileStat
            }

            switch(fileType) {
              case 'text':
                record.result.codeResult[0] = {
                  fileName,
                  message: FileValueResponse,
                  sourceParams,
                  type: fileType
                }
                break;
              case 'table':
                let message = [];
                if (FileValueResponse) {
                  message = FileValueResponse.split('\n');
                  const tableCellValue = sourceParams && sourceParams.cell && sourceParams.cell.length ? sourceParams.cell : [];
                  if (message && message.length) {
                    let iIndex = -1;
                    message = message.map((data) => {
                      iIndex += 1;
                      let jIndex = -1;
                      return data.split(',').map((v) => {
                        jIndex += 1;
                        return {
                          readOnly: tableCellValue[iIndex] && tableCellValue[iIndex].length ? (tableCellValue[iIndex][jIndex] ? true : false) : false,
                          style: {background: "rgb(238, 238, 238)"},
                          value: v,
                        }
                      });
                    })
                  }
                }
                record.result.codeResult[0] = {
                  fileName,
                  message: message,
                  sourceParams,
                  type: fileType
                }
                break;
              case 'mind':
              case 'flow':
              case 'spreadsheet':
              case 'networksimulator':
              case 'drawio':
                record.result.codeResult[0] = {
                  fileName,
                  message: JSON.parse(FileValueResponse),
                  sourceParams,
                  type: fileType
                }
                break;
              // case 'drawio':
              //   record.result.codeResult[0] = {
              //     fileName,
              //     message: pngBase64Header + FileValueResponse.toString('base64'),
              //     sourceParams,
              //     type: fileType
              //   }
              //   break;
              default:
                throw new Error(`未识别的微应用类型${fileType}`);
            }

            microAppUUIDMap[UUID] = record;
          } catch(e) {
            console.log('querySectionContentAndType---service', e)
            continue;
          }
        }

        return microAppUUIDMap;
      }

      // 后台课程进度获取代码记录
      async getCodeRecord(userID, courseSlug, chapterName, sectionName, section, index){
        const { ctx } = this;
        const { model, service } = ctx;
        const { SectionRecord, User } = model

        let result = await SectionRecord.findOne({
          where: {
            sectionID: section.sectionID,
            userID
          },
          attributes: ['record'],
          raw: true
        });

        // 获取用户信息
        const userInfo = await User.findOne({
          where: {
            id: userID
          },
          attributes: ['username', 'name'],
          raw: true
        });

        // 获取相关答题章节类型
        const { sectionType } = section;
        
        // 用户没有答题记录仅返回用户信息和课程基本信息
        if (!result) {
         
          return { sectionType, userInfo };
          // throw Error('数据尚未写入请稍后尝试')
        }
        
        const record = result.record;    
        if(!record){
          throw Error('暂无提交记录！')
        }

        switch(sectionType) {
          case 'AI':
            {
              // 取出数据库中的得分记录
              const { UUIDsMap = {} } = record; 

              // 加载文件中所有需要提交的块
              const submitBlocks = await service.course.getAICourseSubmitBlocks(courseSlug, chapterName, sectionName);
              // 整理微应用块
              const microAppBlocks = submitBlocks.filter(submitBlock => submitBlock['type']);

              // 获取微应用的文件系统记录
              let fileUUIDMap = await service.course.getMicroAppsFileRecord(microAppBlocks, courseSlug, userID);
              // 按照所有需要提交的块进行整理
              const results = [];
              let codeCounter = 1;
    
              for(const submitBlock of submitBlocks) {
                const { UUID, type = 'code' } = submitBlock;

                let item = {
                  ...submitBlock,
                  ...UUIDsMap[UUID],
                  history: UUIDsMap[UUID] && !fileUUIDMap[UUID] ? true : false // 如果答题记录中存在，并且没有文件，就是曾经被清掉
                };

                // 代码块
                if(type === 'code') {
                  item.UUID = UUID;
                  item.name = `代码${codeCounter++}`
                }
                else {
                  // 获取微应用命名
                  const microCodeConfig = microTypeNameMap[type]
                  if (!microCodeConfig) {
                    throw new Error(`不支持的微应用${type}!` );
                  }  
      
                  const microTypeName = microCodeConfig.type;
                  item.name = `${microTypeName} ${item.fileName}`

                  const fileItem = fileUUIDMap[UUID];
                  if(fileItem) {
                    item = { ...item, ...fileItem };
                  }
                }

                results.push(item);
              }
              
              return { results, sectionType, userInfo};
            }
          case 'Access':
          case 'Excel':
            {
              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.json`;
              const fileContent = await ctx.service.course.getFile(path, true);
              if (!fileContent) {
                throw new Error(`当前课程文件 ${sectionName} 不存在`);
              }

              const { instructions } = fileContent;

              if (sectionType === 'Access') {
                instructions.shift(); // 去除首个初始文件
              }

              return { result: record, sectionType, userInfo, instructions };
            }
          case 'OJ':
          case 'PPT':
            {
              // oj题目
              return { result: record, sectionType, userInfo};
            }
          case 'OI':
            {
              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.xml`;
              const fileContent = await ctx.service.course.getFile(path, false);
              if (!fileContent) {
                throw new Error(`当前课程文件 ${sectionName} 不存在`);
              }

              const { questions } = getOIQuestion(fileContent, true);

              // 合并题面和答案
              const questionList = [];
              for (const question of questions) {
                const { UUID, questionType } = question;
                let { answer: rawAnswer } = question;

                if (questionType === '文本'){
                  continue;
                }

                switch(questionType) {
                  case '填空题':
                    rawAnswer = rawAnswer.map(i => i.text).join('、');
                    break;
                  case '多选题':
                    rawAnswer = rawAnswer.join('、');
                    break;
                  default:
                    break;
                }

                questionList.push({
                  ...question,
                  studentAnswer: record[UUID] ? record[UUID].answer : '',
                  status: record[UUID] ? record[UUID].status : false,
                })
              }
              return { sectionType, userInfo, results: questionList };
            }
          case 'Scratch':
          case 'MicroBit':
            // 拼接主目录
            const { fileName } = record;
            const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${fileName}`;
            const results = await ctx.service.course.getFile(pathDir, false);
            return { sectionType, userInfo, result: JSON.parse(results) };
          case 'CodeBlank':
            {
              const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.code`;
              const fileContent = await ctx.service.course.getFile(path, true);
              if (!fileContent) {
                throw new Error(`当前课程文件 ${sectionName} 不存在`);
              }

              const { questions } = fileContent;
              if (!questions) {
                throw new Error(`当前课程题目不存在`);
              }

              const question = questions[0];
              if (!question) {
                throw new Error(`当前课程题目不存在`);
              }

              // 合并题面和答案
              const result = {
                ...question,
                studentAnswer: record
              };

              return { sectionType, userInfo, result };
            }
          default:
            throw new Error(`未支持的节格式${sectionType}`);
        }
      }

      async createCourseFile({ courseSlug, chapterName, sectionName, sectionTitle, sectionType, sectionIndex, ext, fileExt, newFileContent }, transaction) {
        const { ctx } = this;
        const { model } = this.ctx;
        const { Section, Course } = model;

        // 根据 courseSlug找到 courseID
        const course = await Course.findOne({
          where: {courseSlug},
          attributes: ['id', 'indics'],
          raw: true,
          transaction
        });

        if(!course){
          throw Error('当前课程不存在！')
        }

        const { id: courseID, indics } = course;

        // 新建节
        const newSection = await Section.create({
          courseID,
          chapterName,
          sectionName,
          sectionType,
          ext,
          record: null
        }, {
          transaction
        });

        const { id: sectionID } = newSection;

        // 查询当前是否有重名的节
        const newIndics = indics.map((chapter) => {
          // 寻找特定的章，如果不是的话，复制到新的Indics
          if (chapter.chapterName !== chapterName) {
            return chapter;
          }

          // 对应章的对应节是否重名
          chapter.sections = chapter.sections.map((section) => {
            if (section.sectionName === sectionName) {
              throw Error('当前节已存在，请勿重复创建！')
            }

            return section;
          });

          // 在对应章位置插入新的节
          chapter.sections.splice(sectionIndex, 0, {
            ext,
            serial: null,
            sectionName,
            sectionTitle,
            sectionType,
            sectionID,
            status: true,
            sectionIndex
          });

          return chapter;
        });

        // 更新章节
        await Course.update({
          indics: newIndics
        }, {
          where: {courseSlug},
          transaction
        });

        const path = `${courseSlug}/${chapterName}`;
        const fileName = `${sectionName}.${ext}`;

        // 新建文件
        let fileContent = null;
        switch(sectionType) {
          case 'OJ':
            if (newFileContent) {
              fileContent = newFileContent;
            } else {
              fileContent = [
                '<?xml version="1.0" encoding="UTF-8"?>',
                '<?xml-stylesheet type="text/css" href="book.css"?>',
                '<fps version="1.2" url="https://github.com/zhblue/freeproblemset/">',
                '<generator name="HXR" url="http://www.nfls.com.cn:10443/"/>',
                '<item>',
                `<title><![CDATA[${sectionTitle}]]></title>`,
                '<time_limit unit="s">1</time_limit>',
                '<memory_limit unit="mb">256</memory_limit>',
                `<defaultCode><![CDATA[#a,b=map(int,input().split())  键盘输入两个整数，以空格隔开，分别放在变量 a和b 中]]></defaultCode>`,
                '</item>',
                '</fps>'
              ].join('\n');
            }
            break;
          case 'OI':
            if (newFileContent) {
              fileContent = newFileContent;
            } else {
              fileContent = [
                '<?xml version="1.0" encoding="UTF-8"?>',
                '<?xml-stylesheet type="text/css" href="book.css"?>',
                `<title><![CDATA[${sectionTitle}]]></title>`
              ].join('\n');
            }
            break;
          case 'AI':
            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify({
                "cells": [],
                "metadata": {
                  "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3"
                  },
                  "kernelSetting": {
                    "kernelType": "browser",
                    "kernelMemoryLimit": "128",
                    "kernelRecursiveLevel": "1000",
                    "internetAccess": "true",
                  },
                  "language_info": {
                    "codemirror_mode": {
                      "name": "ipython",
                      "version": 3
                    },
                    "file_extension": ".py",
                    "mimetype": "text/x-python",
                    "name": "python",
                    "nbconvert_exporter": "python",
                    "pygments_lexer": "ipython3",
                    "version": "3.7.7"
                  },
                  "toc-autonumbering": false,
                  "toc-showcode": true,
                  "toc-showmarkdowntxt": true,
                  "title": sectionTitle,
                },
                "nbformat": 4,
                "nbformat_minor": 5
              });
            }
            break;
          case 'Access':
            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify({
                title: sectionTitle,
                type: 'Access'
              });
            }
            break;
          case 'Excel':
            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify({
                title: sectionTitle,
                type: 'Excel'
              });
            }
            break;
          case 'Scratch':
            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify({
                "title": sectionTitle,
                "type": "Scratch",
                "judgeSteps": [],
                "targets": [
                  {
                      "isStage": true,
                      "name": "Stage",
                      "variables": {
                          "`jEk@4|i[#Fk?(8x)AV.-my variable": [
                              "我的变量",
                              0
                          ]
                      },
                      "lists": {},
                      "broadcasts": {},
                      "blocks": {},
                      "comments": {},
                      "currentCostume": 0,
                      "costumes": [
                          {
                              "assetId": "cd21514d0531fdffb22204e0ec5ed84a",
                              "name": "背景1",
                              "md5ext": "cd21514d0531fdffb22204e0ec5ed84a.svg",
                              "dataFormat": "svg",
                              "rotationCenterX": 240,
                              "rotationCenterY": 180
                          }
                      ],
                      "sounds": [],
                      "volume": 100,
                      "layerOrder": 0,
                      "tempo": 60,
                      "videoTransparency": 50,
                      "videoState": "on",
                      "textToSpeechLanguage": null
                  },
                  {
                      "isStage": false,
                      "name": "角色1",
                      "variables": {},
                      "lists": {},
                      "broadcasts": {},
                      "blocks": {},
                      "comments": {},
                      "currentCostume": 0,
                      "costumes": [
                          {
                              "assetId": "b4ab6b3b69de1bc3ed6a94ace172a0b0",
                              "name": "造型1",
                              "bitmapResolution": 1,
                              "md5ext": "b4ab6b3b69de1bc3ed6a94ace172a0b0.svg",
                              "dataFormat": "svg",
                              "rotationCenterX": 44,
                              "rotationCenterY": 44
                          }
                      ],
                      "sounds": [],
                      "volume": 100,
                      "layerOrder": 1,
                      "visible": true,
                      "x": 0,
                      "y": 0,
                      "size": 100,
                      "direction": 90,
                      "draggable": false,
                      "rotationStyle": "all around"
                  }
              ],
              "monitors": [],
              "extensions": [],
              "meta": {
                  "semver": "3.0.0",
                  "vm": "0.2.0",
                  "agent": ""
              }
              });
            }
            break;
          case 'MicroBit':
            const defaultContent = {
              "name": "Flashing Heart",
              "type": "MicroBit",
              "title": sectionTitle,
              "header": {
                "name": sectionTitle,
                "meta": {},
                "editor": "blocksprj",
                "pubId": "",
                "pubCurrent": false,
                "target": "microbit",
                "targetVersion": "5.1.3",
                "recentUse": 1679472501,
                "modificationTime": Date.now(),
                "path": "Flashing-Heart",
                "cloudCurrent": false,
                "saveId": null,
                "githubCurrent": false,
                "type": "MicroBit",
                "title": sectionTitle,
                "temporary": false,
                "tutorial": {
                  "tutorial": "/projects/flashing-heart",
                  "tutorialName": "示例教程",
                  "tutorialReportId": "",
                  "tutorialStep": 0,
                  "tutorialReady": true,
                  "tutorialHintCounter": 0,
                  "tutorialActivityInfo": null,
                  "tutorialMd": "# 示例教程\n\n## 步骤 1\n\n这是步骤1.\n\n## 步骤 2\n\n恭喜，你已完成!\n    ",
                  "tutorialCode": [],
                  "tutorialRecipe": false,
                  "autoexpandStep": true,
                  "metadata": {}
                },
                "isSkillmapProject": false,
            },
            "text": {
              "main.blocks": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"pxt-on-start\" x=\"20\" y=\"20\"></block><block type=\"device_forever\" x=\"225\" y=\"20\"></block></xml>",
              "main.ts": "basic.forever(function () {\n\t\n})\n",
              "README.md": "",
              "pxt.json": `{\n    \"name\": \"${sectionTitle}\",\n    \"description\": \"\",\n    \"dependencies\": {\n        \"core\": \"*\",\n        \"radio\": \"*\",\n        \"microphone\": \"*\"\n    },\n    \"files\": [\n        \"main.blocks\",\n        \"main.ts\",\n        \"README.md\"\n    ],\n    \"preferredEditor\": \"blocksprj\"\n}\n`
            },
            };

            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify(defaultContent);
            }
            break;
          case 'PPT':
            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify({
                title: sectionTitle,
                fileName: sectionName,
                fileExt: fileExt || 'pdf',
                assetDir: 'assets',
                type: 'PPT'
              });
            }
            break;
          case 'CodeBlank':
            if (newFileContent) {
              fileContent = JSON.stringify(newFileContent);
            } else {
              fileContent = JSON.stringify({
                title: sectionTitle,
                type: 'CodeBlank',
                questions: [{
                  content: '',
                  code: '',
                  answer: {},
                  ifShowAnswer: '公开答案',
                  ifSubmitLimit: false,
                  submitLimit: null,
                }],
              });
            }
            break;
          default:
            throw new Error(`未知的节类型${sectionType}`);
        }
        
        // 新建一个文件插入
        await ctx.service.file.uploadXMLFile(path, fileName, fileContent, transaction);

        return true;
      }

      // 重命名章
      async renameChapter(courseSlug, chapterName, newChapterName, newChapterTitle, transaction) {
        const { model } = this.ctx;
        const { Course, Section } = model;
        const course = await Course.findOne({
          where: { courseSlug },
          attributes: ['id', 'indics'],
          transaction,
        })
        if(!course){
          throw Error('当前课程不存在！');
        }

        let { indics, id } = course;

        // 仅修改章文件夹名称，更新课程章节缓存
        if (chapterName === newChapterName) {
          indics.forEach((chapter)=>{
            const { chapterName: cpn } = chapter;
            if(cpn === chapterName){
              chapter.chapterTitle = newChapterTitle;
            }
          });
          await Course.update({indics},{
            where: {id},
            transaction
          });
          return;
        }
        
        // 修改章文件夹名称，或同时修改章及章文件夹名称，更新课程章节缓存，修改文件夹名称
        indics.forEach((chapter)=>{
          const { chapterName: cpn } = chapter;
          if(cpn === newChapterName){
            throw Error('章名称重复！')
          }
          if(cpn === chapterName){
            chapter.chapterName = newChapterName;
            chapter.chapterTitle = newChapterTitle;
          }
        })

        await Course.update({indics},{
          where: {id},
          transaction
        })

        await Section.update({chapterName: newChapterName},{
          where: { courseID: id, chapterName },
          transaction
        })
        const oldPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
        const isExist  = await fs.exists(oldPath);
        if(!isExist) {
          return;
        }
        const movePath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${newChapterName}`;
        fs.renameSync(oldPath, movePath, function(err){
            if(err){
              throw err;
            }
        });
        return '成功';
      }

      // 重命名节
      async renameSection(courseSlug, chapterName, sectionName, newSectionName, newSectionTitle, transaction) {
        const { ctx } = this;
        if (!newSectionName && !newSectionTitle) {
          throw new Error('请输入课程名称和文件名称！');
        }

        try {
          // 同时修改课程文件名称及课程显示名称
          if (newSectionName && newSectionName !== sectionName && newSectionTitle) {
            await ctx.service.course.renameSectionFileName(courseSlug, chapterName, sectionName, newSectionName, transaction);
            await ctx.service.course.updateSectionFileContent(courseSlug, chapterName, newSectionName, newSectionTitle, transaction);
            return '成功';
          }

          // 仅修改课程文件名
          if (newSectionName && newSectionName !== sectionName) {
            await ctx.service.course.renameSectionFileName(courseSlug, chapterName, sectionName, newSectionName, transaction);
            return '成功';
          } else if (newSectionTitle) {
            // 课程显示名称修改：更新文件内容
            await ctx.service.course.updateSectionFileContent(courseSlug, chapterName, sectionName, newSectionTitle, transaction);
            return '成功';
          }
        } catch(e) {
          throw new Error(e)
        }
      }

      // 仅修改课程文件名
      async renameSectionFileName(courseSlug, chapterName, sectionName, newSectionName, transaction) {
        const { model } = this.ctx;
        const { Course, Section } = model;
        const course = await Course.findOne({
          where: { courseSlug },
          attributes: ['id', 'indics'],
          transaction,
        })
        
        if(!course){
          throw Error('当前课程不存在！');
        }

        let { indics, id } = course;

        // 更新课程章节缓存
        indics.forEach((chapter)=>{
          const { chapterName: cpn, sections } = chapter;
          if(cpn !==chapterName){
            return chapter;
          }
          sections.forEach((section)=>{
            const { sectionName: sn } = section;
            if(sn===newSectionName){
              throw Error('节名称重复！');
            }
            if(sn===sectionName){
              section.sectionName = newSectionName;
            }
            return section;
          })
        })


        await Course.update({indics},{
          where: {id},
          transaction
        })

        const section = await Section.findOne({
          where: { courseID: id, chapterName, sectionName },
          attributes: ['id', 'ext', 'record'],
          transaction
        })

        if(!section) {
          throw Error('当前节不存在！')
        }

        const { id: sectionID, ext, record = {} } = section;
        // 如果是项目书 修改关联信息
        const isProjectBook = record && record.isProjectBook ? record.isProjectBook : false;
        if(isProjectBook) {
          const sections = await Section.findAll({
            where: {courseID: id, chapterName,},
            attributes: ['id', 'record'],
            transaction
          })

          for (const section of sections) {
            const { record } = section;
            if(record){
              for (const key in record) {
                if(key === 'isProjectBook') {
                  continue;
                }

                if (!record[key] || !record[key].project) {
                  continue;
                }

                const { project } = record[key]
                if(project && project.sectionName === sectionName) {
                  project.sectionName = newSectionName;
                  await Section.update({record},{
                    where: { id: section.id },
                    transaction
                  })
                }
              }
            }
          }
        }

        // 更新数据库课程文件名
        await Section.update({sectionName: newSectionName},{
          where: { id: sectionID },
          transaction
        })

        const oldPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const movePath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${newSectionName}.${ext}`;

        // 重命名文件
        fs.renameSync(oldPath, movePath, function(err){
          if(err){
            throw err;
          }
        });

        // 重命名历史记录压缩文件名称
        // 判断文件是否存在
        const oldZipPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.zip`;
        fs.access(oldZipPath, fs.constants.F_OK, (err) => {
          if (err) {
            // 如果压缩文件不存在忽略
          } else {
            // 如果存在压缩文件，修改文件名称 
            const moveZipPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${newSectionName}.zip`;
            fs.renameSync(oldZipPath, moveZipPath, function(err){
              if(err){
                throw err;
              }
            });
          }
        });

        return '成功';
      }

      // 仅修改课程显示名称，更新课程章节缓存，更新课程文件内容，更新所有历史记录文件的课程标题字段
      async updateSectionFileContent(courseSlug, chapterName, newSectionName, newSectionTitle, transaction) {
        const { ctx } = this;
        const { model } = ctx;
        const { Course } = model;
        const course = await Course.findOne({
          where: { courseSlug },
          attributes: ['id', 'indics'],
          transaction,
        })
        
        if(!course){
          throw Error('当前课程不存在！');
        }

        let { indics, id } = course;

        // 更新课程章节缓存
        indics.forEach((chapter)=>{
          const { chapterName: cpn, sections } = chapter;
          if(cpn !==chapterName){
            return chapter;
          }
          sections.forEach((section)=>{
            const { sectionName: sn } = section;
            if(sn===newSectionName){
              section.sectionTitle = newSectionTitle;
            }
          })
        })

        await Course.update({indics},{
          where: {id},
          transaction
        })

        const currentChapter = indics.find(chapter => chapter.chapterName === chapterName);
        const currentSection = currentChapter.sections.find(section => section.sectionName === newSectionName);
        const ext = currentSection.ext;
        const sectionType = currentSection.sectionType;

        let fileContent = null;
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${newSectionName}.${ext}`;
        try {
          // 获取文件
          const isParse = ['AI', 'Access', 'Excel', 'Scratch', 'MicroBit', 'PPT', 'CodeBlank'].includes(sectionType);
          fileContent = await ctx.service.course.getFile(path, isParse);

          switch(sectionType) {
            case 'AI':
              {
                if (!fileContent.metadata) {
                  fileContent.metadata = {};
                }
                fileContent.metadata.title = newSectionTitle;
                // 更新文件
                await ctx.service.course.updateAIFile(courseSlug, chapterName, newSectionName, fileContent);
                break;
              }
            case 'Access':
            case 'Excel':
            case 'Scratch':
            case 'MicroBit':
            case 'PPT':
            case 'CodeBlank':
              {
                fileContent.title = newSectionTitle;
                const path = `${courseSlug}/${chapterName}`;
                const fileName = `${newSectionName}.${ext}`;
                // 新建一个文件插入
                await ctx.service.file.uploadXMLFile(path, fileName, JSON.stringify(fileContent));
                break;
              }
            case 'OI':
              { 
                const result = getOIQuestion(fileContent, true);
                const question = { ...result, title: newSectionTitle };
                question.questionList = question.questions;
                const xmlContent = changeToOI(question, question.ifShowAnswer, question.ifSubmitLimit, question.submitLimit);
                const path = `${courseSlug}/${chapterName}`;
                const fileName = `${newSectionName}.${ext}`;
                // 新建一个文件插入
                await ctx.service.file.uploadXMLFile(path, fileName, xmlContent);
                break;
              }
            case 'OJ':
              {
                const oldQuestions = getOJAnswer(fileContent);
                const question = { ...oldQuestions, title: newSectionTitle };
                const OJContent = changeToOJ(question);
                const path = `${courseSlug}/${chapterName}`;
                const fileName = `${newSectionName}.${ext}`;
                // 新建一个文件插入
                await ctx.service.file.uploadXMLFile(path, fileName, OJContent);
                break;
              }
            default:
              throw new Error(`未识别的课程类型${sectionType}`);
          }

          // 更新历史记录文件
          // 读取课程历史版本记录
          const historyQuery = { courseSlug, chapterName, sectionName: newSectionName };
          const { historyRecords } = await ctx.service.section.getHistoryRecords(historyQuery, transaction);
          if (!historyRecords) {
            return;
          }

          const newestVersion = historyRecords.length;
          const fileDir = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
          const fileInfo = { fileDir, sectionName: newSectionName, sectionType, fileContent, newSectionTitle, ext, version: newestVersion };

          await ctx.service.section.compressHistoryFile(fileInfo, 'renameTitle');

        } catch(e) {
          console.log(e, '获取文件error');
          throw new Error('获取文件error', path);
        }
      }

      // 根据课程名称获取课程章节缓存
      async getCourseSlugIndicsMap(courseSlugs, transaction) {
        const { model } = this.ctx;
        const { Course } = model;

        const courseList = await Course.findAll({
          where: { 
            publish: 1, 
            courseSlug: { [Op.in]: courseSlugs }
          },
          attributes: ['courseSlug', 'indics'],
          raw: true,
          transaction
        });

        const courseSlugToIDMap = {};
        for(let course of courseList) {
          const { courseSlug, indics } = course;
          courseSlugToIDMap[courseSlug] = indics;
        }

        return courseSlugToIDMap;
      }

      async saveStayTime(sectionID, userID, duration, transaction) {
        const { model } = this.ctx;

        const sectionRecord = await model.SectionRecord.findOne({
          where: {
            userID,
            sectionID,
          },
          attributes: ['id', 'stayTime'],
          transaction
        });

        if(sectionRecord) {
          sectionRecord.stayTime += duration;
          await sectionRecord.save({
            transaction
          });
        }
        else {
          await model.SectionRecord.create({
            sectionID, stayTime: duration, userID
          }, { transaction });
        }
      }

      // 跨课程复制粘贴
      async pasteSectionCrossCourse(sectionQuery, transaction) {
        const { ctx } = this;
        const { schoolSlug, courseSlug, chapterName, copySection, sourceSection } = sectionQuery;
        const { copyCourseSlug, copyChapterName, copySectionName } = copySection;
        
        // 1. 解析课程中所有的引用资源
        const resourcesQuery = { schoolSlug, courseSlug: copyCourseSlug, chapterName: copyChapterName, sectionName: copySectionName, sourceSection };
        const { newResources } = await ctx.service.course.getResourceFiles(resourcesQuery);

        // 如果有资源文件，检测是否有重复资源文件
        let hasDuplicateResources = false;
        let pasteSectionName = '';
        let assetsDuplicateList = [];
        let inputDuplicateList = [];
        const duplicateResources = [];

        if (newResources && newResources.length) {
          // 2. 获取复制课程与目标课程的重名文件，并让用户选择保留哪一个
          const targetCourse = { schoolSlug, courseSlug, chapterName };
          const { assetsDuplicateResources, inputDuplicateResources } = await ctx.service.course.checkDuplicateResources(newResources, targetCourse);
          hasDuplicateResources = assetsDuplicateResources.length || inputDuplicateResources.length;
          assetsDuplicateList = assetsDuplicateResources;
          inputDuplicateList = inputDuplicateResources;

          // 如果没有重复资源文件, 上传资源文件并粘贴课程
          if(!hasDuplicateResources) {
            const sectionQuery = { courseSlug, chapterName, sourceSection, copySection, resources: newResources };
            const { newSectionName } = await ctx.service.course.pasteSection(sectionQuery, transaction);
            pasteSectionName = newSectionName;
          } else {
             // 如果有重复资源文件，让用户选择
            assetsDuplicateList.forEach((item) => {
              duplicateResources.push({
                fileType: 'assets',
                resourceName: item,
              });
            });
            inputDuplicateList.forEach((item) => {
              duplicateResources.push({
                fileType: 'input',
                resourceName: item,
              });
            });
            return { 
              hasDuplicateResources,
              duplicateResources,
              newResources,
            };
          }

        } else {
          // 如果没有资源文件，直接粘贴课程
          const sectionQueryPlain = { courseSlug, chapterName, sourceSection, copySection };
          const { newSectionName } = await ctx.service.course.pasteSection(sectionQueryPlain, transaction);
          pasteSectionName = newSectionName;
        }

        return { 
          hasDuplicateResources, 
          newSectionName: pasteSectionName,
          newResources,
         };
      }

      /*
        1. 解析课程中所有的引用资源
          
        @params 复制课程信息
          { schoolSlug, courseSlug, chapterName, sectionName, sectionType, ext }
          
        @return 文件资源列表
          [{fileType: 'assets', resourceName: 'xxx.png' }]
      */
      async getResourceFiles(sectionQuery) {
        const { ctx } = this;
        const { schoolSlug, courseSlug, chapterName, sectionName, sourceSection } = sectionQuery;
        const { sectionType, ext } = sourceSection;

        let resources = [];

        const path = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const fileContent = await ctx.service.course.getFile(path, ['ipynb', 'json', 'slide', 'code'].includes(ext));

        switch(sectionType) {
          case 'AI':
            /*
              资源类型：
              1. AI
                1.1 markdown
                  ![]() []()
                  <img /> <a/>

                1.2 code type: resources
                  Image('../assets/1.png')
                  Video('../assets/1-1模拟信号与数字信号.mp4', embed=True, width=800, height=450)

                1.3 code type filePreviewer

                1.4 code
                (../assets/xxx.png)
                (../input/xxx.csv)
            */
            resources = getAIResources(fileContent);
            break;
          case 'OI':
          case 'OJ':
            resources = getOIAndOJResources(fileContent);
            break;
          case 'Access':
          case 'Excel':
            resources = getAccessResources(fileContent);
            break;
          case 'Scratch':
            resources = getScratchAssets(fileContent);
            break;
          case 'PPT':
            resources = getPPTAssets(fileContent);
            break;
          case 'CodeBlank':
            resources = getCodeBlankAssets(fileContent);
            break;
          case 'MicroBit':
            break;
          default:
            throw new Error('未识别的课程类型！');
        }

        const resourceList = Array.from(new Set(resources));
        const newResources = resourceList.map(resource => {
          return {
            fileType: resource.split('/')[1],
            resourceName: resource.split('/')[2],
          }
        });

        return { newResources };
      }
      
      /* 
      2. 获取复制课程与目标课程的重名文件，并让用户选择保留哪一个
        
      @params 目标课程与复制课程信息，文件资源列表
        { schoolSlug, courseSlug, chapterName, sectionName, sectionType, ext }
        [{fileType: 'assets', resourceName: 'xxx.png' }]

      @return 文件原名称与新名称表
        [{fileType: 'assets', resourceName: 'xxx.png', newResourceName: 'xxx(1).png' }] 
      */ 
      async checkDuplicateResources(resources, targetCourse) {
        const { ctx } = this;
        const { courseSlug: targetCourseSlug } = targetCourse;

        // 拼接主目录
        const pathCourseDir = `${app.config.file.dir}/${ctx.schoolSlug}/course/${targetCourseSlug}`;
        const pathInputDir = `${pathCourseDir}/input`;
        const pathAssetsDir = `${pathCourseDir}/assets`;

        // 获取资源link文件
        const inputList = await ctx.service.course.list(pathInputDir);
        const assetsList = await ctx.service.course.list(pathAssetsDir);
        const inputs = inputList.map(item => item.name);
        const assets = assetsList.map(item => item.name);

        const inputSet = new Set(inputs);
        const assetsSet = new Set(assets);

        const assetsResourceNames = resources.map((item) => {
          if (item.fileType === 'assets') {
            return item.resourceName
          }
        });
        const assetsResourceNameSet = Array.from(new Set(assetsResourceNames));

        const inputResourceNames = resources.map((item) => {
          if (item.fileType === 'input') {
            return item.resourceName
          }
        });
        const inputResourceNameSet = Array.from(new Set(inputResourceNames));
        
        // 资源表
        const assetsDuplicateResources = assetsResourceNameSet.filter(item => assetsSet.has(item));
        const inputDuplicateResources = inputResourceNameSet.filter(item => inputSet.has(item));

        return { assetsDuplicateResources, inputDuplicateResources };
      }

      /*
      3. 上传资源文件，新建复制课程

      @params 目标课程与复制课程信息, 资源文件列表
        { schoolSlug, courseSlug, chapterName, sectionName, sectionType, ext }
        { schoolSlug, courseSlug, chapterName }
        
      @return 新建课程名称
        { newSectionName }
      */
      async pasteSection(sectionQuery, transaction) {
        const { ctx } = this;
        const { model } = ctx;

        const { courseSlug, chapterName, copySection, sourceSection, resources } = sectionQuery;
        const { copyCourseSlug, copyChapterName, copySectionName } = copySection;
        const { sectionTitle, sectionType, ext } = sourceSection;

        const course = await model.Course.findOne({
          where: {
            courseSlug
          },
          raw: true,
          transaction
        });

        const { indics } = course;

        const targetChapter = indics.find(chapter => chapter.chapterName === chapterName);
        const exists = targetChapter.sections.find(section => section.sectionName === copySectionName);

        // 递归查找重复课程
        function findDuplicateSection(sectionName) {
          let exists = targetChapter.sections.find(section => section.sectionName === sectionName);
          
          if (!exists) {
            return;
          }

          if (exists) {
            const newSectionName = `${exists.sectionName}-复制`;
            const nextExists = findDuplicateSection(newSectionName);
            return nextExists || exists;
          }
        }

        // 如果该章节下不存在复制的课程，直接复制，默认添加在最后一个
        let newSectionName = copySectionName;
        let newSectionTitle = sectionTitle;
        let sectionIndex = targetChapter.sections.length + 1 || 1;

        // 如果该章节下已存在复制的课程，课程名称添加后缀
        if (exists) {
          const lastDuplicateSection = findDuplicateSection(copySectionName);
          newSectionName = `${lastDuplicateSection.sectionName}-复制`;
          newSectionTitle = `${lastDuplicateSection.sectionTitle}-复制`;
        }

        const path = `${app.config.file.dir}/${ctx.schoolSlug}/course/${copyCourseSlug}/${copyChapterName}/${copySectionName}.${ext}`;
        const fileContent = await ctx.service.course.getFile(path, (['ipynb', 'json', 'slide', 'code'].includes(ext)));

        // 同课程复制粘贴
        if (courseSlug === copyCourseSlug) {
          // 创建课程
          await ctx.service.course.createCourseFile({ courseSlug, chapterName, sectionName: newSectionName, sectionTitle: newSectionTitle, sectionType, sectionIndex, ext, newFileContent: fileContent }, transaction);

          return {
            newSectionName
          };
        }

        // 跨课程复制粘贴
        const params = { resources, copyCourseSlug, courseSlug, chapterName, newSectionName, newSectionTitle, sectionType, sectionIndex, ext, fileContent };
        await ctx.service.course.uploadResourcesAndSectionFile(params, transaction);

        return {
          newSectionName
        }
      }

      // 跨课程复制，上传资源
      async uploadResourcesAndSectionFile(params, transaction) {
        const { ctx } = this;
        const { resources, copyCourseSlug, courseSlug, chapterName, newSectionName, newSectionTitle, sectionType, sectionIndex, ext, fileContent } = params;

        let newFileContent = fileContent;

        try {
          if (resources && resources.length) {
            for (const resource of resources) {
              if (!resource) {
                continue;
              }
              
              const { fileType, resourceName, newResourceName, isCover } = resource;
              const resourceURL = `${app.config.file.dir}/${ctx.schoolSlug}/course/${copyCourseSlug}/${fileType}/${resourceName}`;
              const resourceExist = await fs.exists(resourceURL);
              if (!resourceExist) {
                console.error(resourceURL, '==========文件不存在========== uploadResourcesAndSectionFile');
                continue;
              }

              const fileName = newResourceName || resourceName;
    
              // 获取文件流
              const stream = fs.createReadStream(resourceURL);
              // 获取文件 hash
              const hash = await readFileMd5(resourceURL);
              // 获取文件size
              const size = (await fs.stat(resourceURL)).size.toString();
              // 文件是否覆盖
              const ifCover = isCover || false;
              // 如果有修改名称后的新增文件，修改课程内容里对应的文件名称
              if (newResourceName) {
                const reg = new RegExp(resourceName, "g");
    
                const isParse = ['AI', 'Access', 'Excel', 'Scratch', 'PPT', 'CodeBlank'].includes(sectionType);
                if (sectionType !== 'Scratch') {
                  if (isParse) {
                    const rawFileContent = JSON.stringify(newFileContent);
                    const tranFile = rawFileContent.replace(reg, newResourceName);
                    newFileContent = JSON.parse(tranFile);
                  } else {
                    newFileContent = newFileContent.replace(reg, newResourceName);
                  }
                }
              }

              await ctx.service.file.uploadHashFile(stream, hash, size, courseSlug, fileType, ifCover, fileName);

            }

            // 兼容旧版课程数据：OI OJ 替换文件路径里的courseSlug --> file/course/:courseSlug/xxx.png
            if (sectionType === 'OI' || sectionType === 'OJ') {
              const slugReg = new RegExp(copyCourseSlug, "g");
              newFileContent = newFileContent.replace(slugReg, courseSlug);
            }
          }

          // 创建课程
          await ctx.service.course.createCourseFile({ courseSlug, chapterName, sectionName: newSectionName, sectionTitle: newSectionTitle, sectionType, sectionIndex, ext, newFileContent }, transaction);

        } catch(e) {
          throw new Error(e);
        }
      }

      async clearStudentsRecord(sectionIDs, classID, clearInfo, courseSlug, transaction, sectionType) {

        // 找到要当前班级所有的用户id
        const { ctx } = this;
        const { model } = ctx;
        const students = await model.TeamUser.findAll({
          where: {
            teamID: classID
          },
          attributes: ['userID'],
          raw: true,
          transaction
        })

        const studentsIDs = [];
        // 删除学生的课程目录
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}`;
        // 从答题记录中获取运行文件名（微应用），然后删除
        for (let i = 0; i < students.length; i += 1) {
          const studentID = students[i].userID;
          studentsIDs.push(studentID);
          const currentStudentRecord = await model.SectionRecord.findAll({
            where: {
              userID: studentID,
              sectionID: {
                [Op.in]: sectionIDs
              }
            },
            attributes: ['record'],
            raw: true,
            transaction
          })
          for (let i = 0; i < currentStudentRecord.length; i += 1) {
            const record = currentStudentRecord[i].record;
            if (!record) {
              continue;
            }

            // 清除scratch文件
            if (sectionType === 'Scratch') {
              const { fileName } = record;
              const deletePath = `${path}/${studentID}/${fileName}`;
              await fsExtra.remove(deletePath);
              continue;
            }

            const { UUIDsMap } = record;
            if (!UUIDsMap) {
              continue;
            }
            for (const key in UUIDsMap) {
              if (UUIDsMap[key].microAppFileType) {
                // 删除微应用运行记录
                const deletePath = `${path}/${studentID}/${UUIDsMap[key].fileName}`;
                await fsExtra.remove(deletePath);
              }
            }
          }
        }
        if(clearInfo !== 1) {
          return;
        }
        // 如果clearInfo是1的话代表数据库也要清除
        await model.SectionRecord.destroy({
          where: {
            sectionID: {
              [Op.in]: sectionIDs
            },
            userID: {
              [Op.in]: studentsIDs
            }
          },
          transaction
        })
      }

      // 清除学生运行记录及文件
      async clearStudentRecord(courseSlug, chapterName, sectionName, userID, transaction) {
        const { ctx } = this;
        const { model } = ctx;

        // 删除学生的课程文件
        const course = await model.Course.findOne({
          where: {
            courseSlug
          },
          transaction
        });

        const { id: courseID } = course;

        const section = await model.Section.findOne({
          where: {
            courseID,
            chapterName,
            sectionName,
          }
        });

        const { id: sectionID } = section;
        const fullFilename = `${sectionName}.json`;
        const pathDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/student/course/${courseSlug}/${userID}/${chapterName}/${fullFilename}`;
        await fsExtra.remove(pathDir);

        // 数据库也要清除
        await model.SectionRecord.destroy({
          where: {
            sectionID,
            userID,
          },
          transaction
        })
      }

      async batchSwitchChapter(courseSlug, chapterName, classID, batchSwitchChapterOption, batchSwitchChapterType, transaction) {
        const { ctx } = this;
        const { model } = ctx;

        const currentCourse = await model.Course.findOne({
          where: {
            courseSlug
          },
          raw: true,
          transaction
        });

        let {
          id,
          indics,
          teams
        } = currentCourse;

        switch(batchSwitchChapterOption) {
          // 当前班级的全部章节
          case 'currentClassAndAllChapter':
            // 循环每个章节
            indics = indics.map(indicsItem => {
              if(!indicsItem.openTeam) {
                indicsItem.openTeam = [];
              }

              // 打开，加入班级ID
              if (batchSwitchChapterType === 'open') {
                indicsItem.openTeam = Array.from(new Set([...indicsItem.openTeam, classID]));
              } else {
                // 关闭，滤除班级ID
                indicsItem.openTeam = indicsItem.openTeam.filter(item => item !== classID);
              }
              return indicsItem;
            })
            break;
          case 'AllClassAndCurrentChapter':
            indics = indics.map(indicsItem => {
              // 非当前章节，不做处理
              if (chapterName !== indicsItem.chapterName) {
                return indicsItem;
              }

              if (batchSwitchChapterType === 'open') {  
                // 打开，加入全部班级
                indicsItem.openTeam = teams;
              }
              else {
                // 关闭，清空全部班级
                indicsItem.openTeam = [];
              }

              return indicsItem;
            })
            break;
          case 'AllClassAndAllCahpter':
            indics = indics.map(indicsItem => {
              if (batchSwitchChapterType === 'open') {
                // 打开，加入全部班级
                indicsItem.openTeam = teams;
              } else {
                 // 关闭，清空全部班级
                indicsItem.openTeam = [];
              }
              return indicsItem;
            })
            break;
          default:
            break;
        }
        await model.Course.update({
          indics
        }, {
          where: {
            id,
          },
          transaction
        })
      }

      // change the structure of Objective-Questions-section file
      async updateOIStructure({ courseSlug, chapterName, sectionID, sectionName, ext }, transaction){
        const { ctx } = this;
        const { model } = ctx;

        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const fileContent = await ctx.service.course.getFile(path, false);

        // parsing the former objective-question xml files
        const parsedFileContentOld = getOIQuestionOld(fileContent, true); // the second param represents passing the answers of questions
        const { questions: questionsOld, ifShowAnswer, ifSubmitLimit, submitLimit, title } = parsedFileContentOld;

        // transform the records of the section
        const allRecords = await model.SectionRecord.findAll({
          where: {
            sectionID,
          },
          raw: true,
          transaction
        });

        // new section file
        const newQuestions = [];

        for (let i = 0; i < questionsOld.length; i++) {
          const outerQuestion = questionsOld[i];
          const secondQuestions = outerQuestion.question;

          // set the former label and content as question type is content
          if (outerQuestion.label) {
            const newLabel = {
              UUID: makeUUID(outerQuestion.label),
              content: outerQuestion.label,
              questionType: '文本'
            }
            newQuestions.push(newLabel);
          }

          if (outerQuestion.content) {
            const newContent = {
              UUID: makeUUID(outerQuestion.content),
              content: outerQuestion.content,
              questionType: '文本'
            }
            newQuestions.push(newContent);
          }

          // newRecord: { UUID: { answer, status ... } }
          for (let j = 0; j < secondQuestions.length; j++) {
            newQuestions.push(secondQuestions[j]);
          }
        }

        // transform the records of students
        if (allRecords && allRecords.length) {
          for (const studentRecord of allRecords) {
            const { record } = studentRecord;

            if (!record) {
              continue;
            }

            const { submitTimes } = record;

            // new student record
            const newRecord = { submitTimes };
            
            for (let i = 0; i < questionsOld.length; i++) {
              const outerQuestion = questionsOld[i];
              const secondQuestions = outerQuestion.question;

              // newRecord: { UUID: { answer, status ... } }
              for (let j = 0; j < secondQuestions.length; j++) {
                // newQuestions.push(secondQuestions[j]);
                const { UUID } = secondQuestions[j];

                if (record[i] && record[i][j]) {
                  newRecord[UUID] = record[i][j];
                }
              }
            }

            studentRecord.record = newRecord;
          }

          // update the records of students
          await model.SectionRecord.bulkCreate(allRecords, { 
            updateOnDuplicate: ['record', 'updated_at'],
            transaction
          });
        }

        // generate the new xml file
        const question = { questionList: newQuestions, title };
        const newFileContent = changeToOI(question, ifShowAnswer, ifSubmitLimit, submitLimit);

        // create a new file with the same old title
        const chapterPath = `${courseSlug}/${chapterName}`;
        const fileName = `${sectionName}.xml`;
        await ctx.service.file.uploadXMLFile(chapterPath, fileName, newFileContent);

        // update the old history files
        // const filePath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}`;
        // await ctx.service.course.updateOIHistoryFile({ filePath, ext });

        // delete historyRecords
        await model.Section.update({
          historyRecords: null
        }, {
          where: {
            id: sectionID
          },
          transaction
        })

        const zipPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.zip`;

        fs.access(zipPath, fs.constants.F_OK, async (err) => {
          if (err) {
            // 如果压缩文件不存在忽略
          } else {
            // 如果存在压缩文件，删除
            await fsExtra.remove(zipPath);
          }
        });
      }

      /*
      * update the file of objective-question
      * @params fileContent: content of the old file
      * @return 
      * newFileContent: new xml content of file
      * isNew: if the data is new then do nothing
      */
      async updateOISectionFile(fileContent) {
        // parsing the former objective-question xml files
        const parsedFileContentOld = getOIQuestionOld(fileContent, true); // the second param represents passing the answers of questions
        const { questions: questionsOld, ifShowAnswer, ifSubmitLimit, submitLimit, title } = parsedFileContentOld;

        const newQuestions = [];
        let isNew = false;
        for (let i = 0; i < questionsOld.length; i++) {
          const outerQuestion = questionsOld[i];
          const secondQuestions = outerQuestion.question;

          // if no label then file is new
          if (!outerQuestion.label) {
            isNew = true;
            break;
          }

          // set the former label and content as question type is content
          if (outerQuestion.label) {
            const newLabel = {
              UUID: makeUUID(outerQuestion.label),
              content: outerQuestion.label,
              questionType: '文本'
            }
            newQuestions.push(newLabel);
          }

          if (outerQuestion.content) {
            const newContent = {
              UUID: makeUUID(outerQuestion.content),
              content: outerQuestion.content,
              questionType: '文本'
            }
            newQuestions.push(newContent);
          }

          // newRecord: { UUID: { answer, status ... } }
          for (let j = 0; j < secondQuestions.length; j++) {
            newQuestions.push(secondQuestions[j]);
          }
        }

         // generate the new xml file
         const question = { questionList: newQuestions, title };
         const newFileContent = changeToOI(question, ifShowAnswer, ifSubmitLimit, submitLimit);

         return { newFileContent, isNew };
      }

      async publishEliteCourse(params, transaction) {
        const { ctx } = this;
        const { schoolSlug, session } = ctx;
        const { mainModel } = app;

        const { courseSlug, status, publishCourseInfo, detailList, clientID, requestID } = params;

        const { id: userID } = session.user;

        try {
          const indicsData = await ctx.service.course.getIndicsData(courseSlug);
          const indics = indicsData.dataValues ? indicsData.dataValues.indics : indicsData.indics;

          // 导出课程章节名称与文件夹名称映射表
          const chapterNameMap = {};
          indics.forEach(chapter => {
            const { chapterName, chapterTitle } = chapter;
            if (chapterTitle) {
              chapterNameMap[chapterName] = chapterTitle;
            }
          });

          const course = await ctx.service.course.checkTeacherPermission(ctx.session.user.id, courseSlug);
          if (!course) {
            throw new Error('您没有发布精品课程的权限');
          }
          const { allowCopy, allowPaste, courseDescription, courseName, saveCode, saveRunResult } = course;
          const courseJson = {
            allowPaste: allowPaste ? true : false,
            allowCopy: allowCopy ? true : false,
            courseDescription: courseDescription,
            courseName: courseName,
            courseSlug: courseSlug,
            saveCode: saveCode ? true : false,
            saveRunResult: saveRunResult ? true : false,
            chapterNameMap
          };
            
          // 存储课程字段到主库
          const existCourse = await mainModel.EliteCourses.findOne({
            where: {
              schoolSlug,
              courseSlug,
            },
            transaction
          });

          if (existCourse) {
            await mainModel.EliteCourses.update({
              schoolSlug,
              courseSlug,
              courseInfo: detailList,
              publishInfo: publishCourseInfo,
              indics,
              status,
            }, {
              where: {
                schoolSlug,
                courseSlug
              },
              transaction
            });
          } else {
            await mainModel.EliteCourses.create({
              schoolSlug,
              courseSlug,
              courseInfo: detailList,
              indics,
              publishInfo: publishCourseInfo,
              status,
            }, {
              transaction
            });
          }

          // 提交至队列，各分片轮询
          const queue = app.queue['course-task'];

          await ctx.service.course.getQueueWaiting({ queue, schoolSlug, userID });

          await queue.add({
            clientID,
            requestID,
            originDir: app.config.file.dir,
            eliteDir: app.config.file.eliteCoursesDir,
            courseJson,
            schoolSlug,
            userID,
            courseSlug,
            type: 'publishEliteCourses'
          }, {
            removeOnComplete: true,
            removeOnFail: true,
          });
        } catch(e) {
          throw new Error(e)
        }

      }

      async uploadAndCreateCourse(demoCoursesPath, course, defaultUserID, newCourseSlug, newCourseName, transaction) {
        const { ctx } = this;

        // 每节课的文件
        const sourceCourseDir = `${demoCoursesPath}/${course}`;
        const chapterDirs = await fs.readdir(`${sourceCourseDir}`);

        // 1. 读取课程信息配置文件
        const courseConfigPath = `${sourceCourseDir}/course.json`;
        const courseInfo = await ctx.service.demo.checkCourseInfo(courseConfigPath);

        courseInfo.createrID = defaultUserID; // 初始上传课程用户id默认为1
        courseInfo.courseSlug = newCourseSlug;
        courseInfo.courseName = newCourseName;

        // 2. 上传课程包
        let indics = null;
        try {
            // 创建课程文件夹
            const targetCourseDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${newCourseSlug}`;
            const courseExist = await fs.exists(targetCourseDir);
            if (!courseExist) {
                fs.mkdir(targetCourseDir, { recursive: true });
            }

            // 创建课程基本信息文件
            fs.createReadStream(courseConfigPath).pipe(fs.createWriteStream(`${targetCourseDir}/course.json`));

            // 创建章节文件
            indics = await ctx.service.demo.uploadChapterAndGetIndics(chapterDirs, courseInfo, sourceCourseDir, targetCourseDir);
        } catch (err) {
            console.error(err);
            return;
        }

        // 3. 写库 admin/createCourseSections
        const { jupyter } = app.config;
        courseInfo.indics = indics;
        courseInfo.publish = 1;
        courseInfo.statist = { chapters: indics.length, students: 0 };
        courseInfo.teachers = [courseInfo.createrID];
        courseInfo.teams = [];
        courseInfo.courseType = '必修课';
        courseInfo.containerInfo = {
            image: jupyter.image,
            cpuLimit: jupyter.cpuRequest,
            memoryRequest: jupyter.memoryRequest,
        },

        await ctx.service.course.createCourseSections(courseInfo, transaction, defaultUserID);
      }

      async getChapterResult({ classID, courseSlug, chapterName }) {
        const { ctx } = this;
        let sectionNumbers = 0;
        let staticsResult = {};
         // 获取该班级名单
         const userIDList = await ctx.service.team.getTeamUser(classID);
         const userIDs = userIDList.map((userValue) => userValue.userID);
 
         const userList = await ctx.service.user.getUsersByID(userIDs);
 
         // 获取相关答题章节
         const sectionList = await ctx.service.section.getSectionList(courseSlug, chapterName);
         const sectionNames = sectionList.map((i) => i.sectionName);
         sectionNumbers = sectionList.length;
 
         const AISectionNameList = [];
         for (const section of sectionList) {
           if (section.sectionType !== 'AI') {
             continue;
           }
 
           AISectionNameList.push(section.sectionName);
         }
 
         const sectionIDs = sectionList.map((section) => section.sectionID);
 
         // 获取答题记录
         const recordList = await ctx.service.section.getSectionRecordList(userIDs, sectionIDs);
 
         // 合并数据
         staticsResult = { 全部: userList.length, 未答题: 0, 已答题: 0, 运行错误: 0, 全部通过: 0 };
         for (let i = 0; i < userList.length; i += 1) {
           const userValue = userList[i];
           const userID = userValue.id;
 
           userValue.scoreList = [];
 
           // 获取该用户的答题记录
           const records = recordList.filter(value => value.userID === userID);
 
           // 提交时间
           userValue.submitTime = records && records.length ? records[records.length - 1].updated_at : null;
 
           for (const section of sectionList) {
             const { sectionID, sectionName, sectionType } = section;
             
             let record = records && records.length ? records.find((data) => data.sectionID === sectionID) : null;
             if (!record) {
               userValue[sectionName] = '未答题';
               continue;
             }
             
             record = record.dataValues ? record.dataValues : record;
 
             if (sectionType === 'Scratch' && record.record) {
               const studentRecord = record.record;
               const { judgeSteps } = studentRecord;
               // 计算学生总分
               const scores = judgeSteps.map(i => i.manual ? (i.rate ? i.rate : 0) : (i.pass ? i.score : 0));
               const totalScore = scores.reduce((pre, cur) => { return pre + cur }, 0);
               userValue[sectionName] = totalScore;
 
               // 计算课程总分
               const courseScores = judgeSteps.map(i => i.score);
               const totalCourseScores = courseScores.reduce((pre, cur) => { return pre + cur }, 0);
               userValue[`${sectionName}-total`] = totalCourseScores;
 
               // 记录课程是否满分
               userValue.scoreList.push(totalCourseScores <= totalScore);
               continue;
             }
 
             let { totalScore, passCount } = record;
             userValue[sectionName] = (passCount && totalScore) ? parseInt((passCount / totalScore) * 10000 + 0.5, 10) / 100 : 0;    
 
             userValue.scoreList.push(userValue[sectionName] >= 100);
           }
 
           if (!userValue.scoreList.length) {
             staticsResult['未答题'] += 1;
           } else {
             staticsResult['已答题'] += 1;
           }
 
           if (userValue.scoreList.some(i => !i)) {
             staticsResult['运行错误'] += 1;
           }
 
           if (userValue.scoreList.length >= sectionNumbers && userValue.scoreList.every(i => i)) {
             staticsResult['全部通过'] += 1;
           }
         }
 
        return { userList, staticsResult, sectionNames, sectionNumbers };
      }

      async exportCourseProgressExcel(props) {
        const { ctx } = this;
        const { courseSlug, classIDs } = props;

        let res = {};
        for (const classID of classIDs) {
          const classRes = await ctx.service.course.exportSingleClassProgressExcel({ classID, courseSlug });
          res[classID] = classRes;
        }

        return res;
      }

      async exportSingleClassProgressExcel(props) {
        const { classID, courseSlug } = props;
        const { ctx } = this;
        const { model } = ctx;

        const course = await ctx.model.Course.findOne({
          where: {
            courseSlug
          },
          attributes: ['indics'],
          raw: true,
        });

        const { indics } = course;
        const chapterNames = indics.map(chapter => chapter.chapterName);

        const userIDList = await ctx.service.team.getTeamUser(classID);
        const userIDs = userIDList.map((userValue) => userValue.userID);
        const userList = await ctx.service.user.getUsersByID(userIDs);

        const recordMap = {};
        for (const chapterName of chapterNames) {
          const currentChapter = indics.find(i => i.chapterName === chapterName);
          const currentSections = currentChapter.sections;
          const sectionIDs = currentSections.map(i => i.sectionID);

          const userRecord = await model.SectionRecord.findAll({
            where: {
              userID: {
                [Op.in] : userIDs
              },
              sectionID: {
                [Op.in]: sectionIDs
              },
              totalScore: { 
                [Op.not]: 0,
                [Op.eq]: Sequelize.col('passCount')
              },
            },
            attributes: ['userID', 'passCount', [Sequelize.fn('COUNT', 'userID'), 'userCount']],
            group: ['userID'],
            raw: true,
          });

          recordMap[chapterName] = userRecord;
        }

        let num = 1;
        for (const user of userList) {
          for (const chapterName of chapterNames) {
            const currentChapter = indics.find(i => i.chapterName === chapterName);
            const currentSections = currentChapter.sections;
            const sectionIDs = currentSections.map(i => i.sectionID);

            const currentRecords = recordMap[chapterName].find(i => i.userID === user.id) || {};
            const { userCount = 0 } = currentRecords;

            user.serialNumber = num;
            user[`章节 ${chapterName} 通过数(共${sectionIDs.length}节)`] = userCount;
          }
          num++;
        }

        return userList;
      }

      async getQueueWaiting({ queue, schoolSlug, userID }) {
        const count = await queue.getJobCounts();
        console.log(count, 'count')
    
        const { waiting: waitingCount, active: activeCount, completed: completedCount } = count;

        const allCount = waitingCount + activeCount;

        console.log(`学校：${schoolSlug} 发起人：${userID} 执行异步任务, 当前等待任务个数：${waitingCount}，执行中 ${activeCount}`);

        const messageChannel = `/global/message/${schoolSlug}/${userID}`;

        if (allCount > 0) {
          app.client.publish(messageChannel, { 
            state: 200,
            message: `任务提交成功，当前还需等待${allCount}个任务执行`,
          });
        }
      }
    }
    return CourseService;
  }
