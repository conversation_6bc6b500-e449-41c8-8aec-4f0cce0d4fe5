const md5 = require('md5');
const moment = require('moment');
const { fs } = require('mz');
const fetch = require('node-fetch');
const json2xls = require('json2xls');
const cnchar = require('cnchar');
const { Op } = require('sequelize');
const { Hmac } = require('../utils/crypto');

module.exports = app => {
  const querySession = {};

  async function request(requestURL, method, userReqData) {
    const headers = {
      'Accept': 'application/json',
      'Content-type': 'application/json',
    };

    const params = {
      method: method,
      headers,
    }

    if (userReqData) {
      params['body'] = JSON.stringify(userReqData);
    }

    let response;
    try {
      const responseRaw = await fetch(requestURL, params);
      response = await responseRaw.json();
    } catch (e) {
      console.error('request', e)
      throw e;
    }

    return response;
  }

  class SSOService extends app.Service {
    async getSSOConfig(transaction = null) {
      const { ctx } = this;
      const { model } = ctx;

      const currentConfigs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      if (!currentConfigs || !currentConfigs.value) {
        return null;
      }

      const configs = currentConfigs.value;
      const ssoConfig = configs['单点登录配置'];
      return ssoConfig;
    }

    async getSSOLoginUrl({ allSSO, schoolSlug, field, ssoSlug }) {
      const { ctx } = this;

      let res = null;
      const deploymentSlug = await ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }

      const sso = allSSO[deploymentSlug];
      if (!sso) {
        throw new Error(`当前学校未配置单点登录 ${deploymentSlug}，请检查系统地址`);
      }

      if (deploymentSlug === 'njyz') {
        if (field === 'admin') {
          res = sso.teacherLoginUrl;
        } else {
          res = sso.studentLoginUrl;
        }
        return res;
      }

      const TimeSpan = Math.round(new Date().getTime() / 1000);
      const EncryptString = md5(`${sso.AppCode}${TimeSpan}${sso.AppKey}`);

      const returnURL = `https://${ssoSlug}/sso/validate`;
      const conditionQuery = `schoolSlug=${schoolSlug}&field=${field}&deploymentSlug=${deploymentSlug}&ssoSlug=${ssoSlug}`;
      const ReturnUrl = `${returnURL}?${encodeURIComponent(conditionQuery)}`;

      res = `${sso.path}/Authorization.aspx?AppCode=${sso.AppCode}&EncryptString=${EncryptString}&TimeSpan=${TimeSpan}&ReturnUrl=${encodeURI(ReturnUrl)}`;

      return res;
    }

    async getClassTree(query) {
      const { UserName, ZZJGM } = query;
      const { sso: allSSO } = app.config;

      const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }
      const sso = allSSO[deploymentSlug];

      const TimeSpan = Math.round(new Date().getTime() / 1000);
      const EncryptString = md5(`${sso.AppCode}${UserName}${ZZJGM}${TimeSpan}${sso.AppKey}`);

      const classURL = `${sso.path}/api/ssoauth/GetClassInfoList?AppCode=${sso.AppCode}&EncryptString=${EncryptString}&TimeSpan=${TimeSpan}&UserName=${UserName}&ZZJGM=${ZZJGM}`;

      const classResults = await request(classURL, 'POST');

      if (classResults.Code !== 200) {
        throw new Error(classResults.Description)
      }

      return classResults;
    }

    async getClassTreeNje({ user, VerificationCode, PageIndex, PageSize }) {
      const { service } = this;
      let classResponse = {};
      try {
        classResponse = await service.sso.getClassTree({ UserName: user.ssoName, VerificationCode, ZZJGM: user.OrgCode, PageIndex, PageSize });
      } catch(e) {
        throw new Error(e);
      }
     
      const { Result: classResults } = classResponse;
      // const classResults = [
      //   {
      //     "ClassID": "d2679a6a-0754-484c-926b-0057a2285bce",
      //     "Stage": "2",
      //     "Grade": "2021",
      //     "StageName": "初中",
      //     "GradeName": "七年级",
      //     "ClassCode": "9"
      //   },
      //   {
      //     "ClassID": "d2679a6a-0754-484c-926b-1111111111111",
      //     "Stage": "2",
      //     "Grade": "2021",
      //     "StageName": "初中",
      //     "GradeName": "七年级",
      //     "ClassCode": "1"
      //   },
      //   {
      //     "ClassID": "d2679a6a-0754-484c-926b-22222222222",
      //     "Stage": "2",
      //     "Grade": "2021",
      //     "StageName": "初中",
      //     "GradeName": "八年级",
      //     "ClassCode": "1"
      //   },
      //   {
      //     "ClassID": "4b82333b-427f-4446-a533-04e6f8500359",
      //     "Stage": "1",
      //     "Grade": "2020",
      //     "StageName": "小学",
      //     "GradeName": "五年级",
      //     "ClassCode": "1"
      //   } 
      // ];

      const grades = [...(new Set(classResults.map(i => i.Grade)))];

      const classDatas = [];
      for (const grade of grades) {
        const gradeData = {
          title: `${grade} 年入学`,
          key: grade,
          children: [],
          level: 'year'
        };

        const gradeDataChildren = classResults.filter(i => i.Grade === grade);

        const gradeNames = [...(new Set(gradeDataChildren.map(i => i.GradeName)))];
        const classNameData = [];
        for (const gradeName of gradeNames) {
          const classItem = {
            title: `${gradeName}`,
            key: `${grade}${gradeName}`,
            level: 'grade',
            children: [],
          }
          classItem.children = gradeDataChildren.filter(c => c.GradeName === gradeName);
          classItem.children.forEach(i => {
            i.title = `${i.ClassCode} 班`;
            i.key = `${i.ClassID}`
            i.level = 'class';
          });

          classNameData.push(classItem);    
        }
        gradeData.children = classNameData;

        classDatas.push(gradeData);
      }

      return classDatas;
    }
    
    // 南京一中单点登录api请求接口
    async requestNJYZ({ path, data }) {
      const { ctx } = this;

      let res;

      const deploymentSlug = await ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }

      const { sso: allSSO } = app.config;
      const sso = allSSO[deploymentSlug];
      if (!sso) {
        throw new Error(`当前学校未配置单点登录 ${deploymentSlug}，请检查系统地址`);
      }

      const { appid, appkey, apiPath: apipath } = sso;

      // 请求参数
      const timestamp = new Date().getTime();
      const content = `${appid}${JSON.stringify(data)}${timestamp}`;
      const sign = Hmac(appkey, content);

      const body = {
        appId: appid,
        data: data,
        timestamp: timestamp,
        sign: sign 
      };

      let apiPath = `${apipath}/${path}`;

      console.log(`请求接口：${apiPath}`);
      // console.log(apiPath, body, 'request')
      try {
        res = await request(apiPath, 'POST', body);
        // console.log(res, 'res')
      } catch(e) {
        console.error(`错误！请求接口：${apiPath}，请求参数：${JSON.stringify(body)}`);
        console.error('返回结果:', res)
        console.error('报错：', e);
        throw e;
      }

      if (!res) {
        throw new Error(`请求接口：${apiPath}，请求参数：${JSON.stringify(body)}, 返回结果: ${JSON.stringify(res)}`);
      }

      if (res.code !== '200') {
        console.error(`请求接口：${apiPath}，请求参数：${JSON.stringify(body)}, 返回结果: ${JSON.stringify(res)}`);
        throw new Error(res.msg);
      }

      const { data: resData } = res;
      if (!resData) {
        throw new Error(`请求接口：${apiPath}，请求参数：${JSON.stringify(body)}, 出错! ${msg}`);
      }

      return resData;
    }

    // njyz 查询列表全部信息
    async queryAllList({ path, query, logInfo }) {
      const { ctx } = this;

      let allData = await ctx.service.sso.requestNJYZ({ path, data: query });
      
      if (!allData) {
        console.error(`${logInfo} 没有数据`);
        return [];
      }

      const { list, total } = allData;
      if (!list.length || !total) {
        console.error(`${logInfo} 没有数据`);
        return [];
      }

      // 获取全部数据
      if (list.length < total) {
        // 根据文档《智慧校园接口规范V1.3.2》 当前页展示数据条数，不传参默认 10 条,最大 1000条,超过 1000 条默认 1000 条
        if (total > 1000) {
          // TODO
          console.error(`${logInfo} 数据超过1000`);
        }

        allData = await ctx.service.sso.requestNJYZ({ path: path, data: { ...query, pageSize: total } });
      }

      return allData;
    }

    // 获取教师基本信息，获取userName
    async getTeacherInfo({ userID, schoolID }) {
      const { ctx } = this;

      const query = {
        schoolId: schoolID,
        teacherId: userID,
      }

      const teacherInfo = await ctx.service.sso.requestNJYZ({ path: 'teacher/searchAllTeacherMessage', data: query });

      const { list = [] } = teacherInfo;
      if (!list || !list.length) {
        throw new Error(`无法获取教师用户 ${userID} 基本信息`);
      }

      const currentUser = list.find(i => i.teacherId === userID);
      if (!currentUser) {
        throw new Error(`无法获取教师用户 ${userID} 基本信息`);
      }

      return currentUser;
    }

    // 获取学生基本信息，获取userName
    async getStudentInfo({ userID, schoolID }) {
      const { ctx } = this;

      const query = {
        schoolId: schoolID,
        studentId: userID,
      }

      const teacherInfo = await ctx.service.sso.requestNJYZ({ path: 'student/searchAllStudent', data: query });

      const { list = [] } = teacherInfo;
      if (!list || !list.length) {
        throw new Error(`无法获取学生用户 ${userID} 基本信息`);
      }

      const currentUser = list.find(i => i.studentId === userID);
      if (!currentUser) {
        throw new Error(`无法获取学生用户 ${userID} 基本信息`);
      }

      return currentUser;
    }

    // 获取校区
    async getAllSchoolNJYZ() {
      const { ctx } = this;

      const allSchool = await ctx.service.sso.requestNJYZ({ path: 'getAllSchool', data: {} });
      if (!allSchool) {
        throw new Error('获取全部校区信息失败');
      }

      return allSchool;
    }

    // 获取教师所有校区教授班级
    async getTeacherClassListAllSchool({ list, ssoName }) {
      const { ctx } = this;

      let allClass = [];
      for (const item of list) {
        const { schoolId } = item;

        const path = 'queryTeacherTeachingMessage';
        const logInfo = `校区：${schoolId}，教师：${ssoName}`;
        const query = {
          schoolId: schoolId,
          teacherId: ssoName,
        };

        const { list: currentClasses = [] } = await ctx.service.sso.queryAllList({ path, query, logInfo });

        allClass.push(...currentClasses);
      }

      return allClass;
    }
    
    // 获取所有校区所有年级
    async getAllGradesNJYZ({ list }) {
      const { ctx } = this;

      const allCurrentGrades = [];
      for (const item of list) {
        const { schoolId } = item;

        const path = 'grade/searchGradeMessage';
        const logInfo = `校区：${schoolId}`;
        const query = {
          schoolId,
        };

        const { list: allGrades = [] } = await ctx.service.sso.queryAllList({ path, query, logInfo });

        allCurrentGrades.push(...allGrades);
      }

      return allCurrentGrades;
    }

    // 获取教师教授班级
    async getTeacherClassListNJYZ({ user }) {
      const { ctx } = this;

      const allSchool = await ctx.service.sso.getAllSchoolNJYZ();
      const { list } = allSchool;
      if (!list || !list.length) {
        throw new Error('获取全部校区信息失败');
      }

      const schoolNameMap = {};
      for (const item of list) {
        if (!schoolNameMap[item.schoolId]) {
          schoolNameMap[item.schoolId] = item.schoolName;
        }
      }

      // 获取所有年级
      const allCurrentGrades = await ctx.service.sso.getAllGradesNJYZ({ list });
      if (!allCurrentGrades || !allCurrentGrades.length) {
        throw new Error(`错误：学校没有年级`);
      }       

      // 获取教师所有校区教授班级
      const { ssoName } = user;
      let allTeacherClasses = await ctx.service.sso.getTeacherClassListAllSchool({ list, ssoName });
      if (!allTeacherClasses || !allTeacherClasses.length) {
        console.error(`教师：${ssoName} 没有班级`);
        return { selectClassData: [], allTeacherClasses: [] };
      }

      // 合并班级学年
      const yearList = Array.from(new Set(allCurrentGrades.map(i => i.enrollmentYear)));
      const selectClassData = [];

      for (const year of yearList) {
        // 当前学年下的年级
        const currentGrades = [];
        const gradeDataChildren = allCurrentGrades.filter(i => i.enrollmentYear === year);

        const gradeIds = Array.from(new Set(gradeDataChildren.map(i => i.gradeId)));
        for (const gradeId of gradeIds) {
          const currentGrade = gradeDataChildren.find(i => i.gradeId === gradeId);
          if (!currentGrade) {
            continue;
          }

          // 当前年级下的班级
          const gradeClasses = allTeacherClasses.filter(i => i.gradeId === gradeId);
          if (!gradeClasses.length) {
            continue;
          }

          const { gradeName } = currentGrade;

          const gradeItem = {
            title: `${gradeName}`,
            key: `${year}${gradeName}`,
            level: 'grade',
            children: [],
            id: gradeId,
          }

          gradeItem.children = gradeClasses;
          gradeItem.children.forEach(i => {
            i.title = i.schoolId && schoolNameMap[i.schoolId] ? `${schoolNameMap[i.schoolId]} ${i.className}` : i.className;
            i.key = i.classId;
            i.level = 'class';
            i.GradeName = gradeName;
            i.ClassID = i.classId;
            i.schoolName = schoolNameMap[i.schoolId];
          });

          currentGrades.push(gradeItem);    
        }

        // 合并年级
        if (!currentGrades.length) {
          continue;
        }
        
        const yearData = {
          title: `${year} 年入学`,
          key: year,
          children: [],
          level: 'year'
        };

        yearData.children = currentGrades;
        selectClassData.push(yearData);
      }

      return { selectClassData, allTeacherClasses };
    }

    // 获取班级全部学生信息
    async getStudentsByClassIDNJYZ({ selectPullClassIDs, Page, PageSize }) {
      const { ctx } = this;

      if (!selectPullClassIDs.length) {
        return [];
      }

      const studentsClass = [];
      for (const item of selectPullClassIDs) {
        const { classId, schoolId } = item;
        const studentMap = await ctx.service.sso.getAllStudentsByClassID({ classId, schoolId, Page, PageSize });
        if (!studentMap) {
          continue;
        }

        studentsClass.push(studentMap);
      }

      return studentsClass;
    }

    // 获取单个班级全部学生信息
    async getAllStudentsByClassID({ classId, schoolId }) {
      const { ctx } = this;

      const path = 'query/allStudent';
      const logInfo = `校区：${schoolId} 班级 ${classId}`;
      const query = {
        schoolId,
        classId,
      };

      const { list: allStudents = [], total } = await ctx.service.sso.queryAllList({ path, query, logInfo });

      allStudents.forEach((student, index) => {
        const { studentId } = student;

        student.index = index + 1;

        student.ssoName = studentId; // 单点绑定id
        student.XM = student.studentName; // 学生姓名 name
        student.XSZH = `${schoolId}-${student.studentAccount || student.studentCode}`; // 学生账号 username
        student.Gender = student.sex;
      });

      const studentMap = {
        classID: classId,
        students: allStudents,
        total: total,
      };
      
      return studentMap;
    }

    async getInfoKS({ path, reqBody }) {
      const { sso: allSSO } = app.config;

      const deploymentSlug = await this.ctx.service.sso.getSSOConfig();
      // const deploymentSlug = 'ai.ksedu.cn';
      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }

      const sso = allSSO[deploymentSlug];
      const { AccountId, AccountPassword, apiPath } = sso;

      const formatTime = moment().format('YYYY-MM-DD');

      const date = new Date(formatTime);
      const ReqTime = date.getTime() / 1000;
      const DigitalSign = md5(`${AccountId}&${AccountPassword}&${ReqTime}`).toLocaleUpperCase();

      const buffer = Buffer.from(JSON.stringify(reqBody), 'utf-8');
      const encodeUserBody = buffer.toString('base64');

      const userReqData = {
        Header: {
          "ServiceName": path,
          "AccountId": AccountId,
          "DigitalSign": DigitalSign,
          "ReqTime": ReqTime
        },
        Body: encodeUserBody
      }

      const userInfo = await request(apiPath, 'POST', userReqData);
      
      const { Body } = userInfo;
      const resBuffer = Buffer.from(Body, 'base64');
      const response = resBuffer.toString('utf-8');

      const parseResponse = response ? JSON.parse(response) : {};

      return parseResponse;
    }

    async checkUserKS(userName) {
      const { ctx } = this;

      if (!userName) {
        throw new Error('用户名不存在');
      }

      const userBody = {
        LoginIds: userName,
      };

      const userResponse = await ctx.service.sso.getInfoKS({ path: 'GetUserList', reqBody: userBody });

      if (!userResponse) {
        throw new Error(`用户 ${userName} 不存在`);
      }

      const { ResultList } = userResponse;

      if (!ResultList || !ResultList.length) {
        throw new Error(`用户 ${userName} 不存在`);
      }

      const currentUser = ResultList.find(u => u.LoginId === userName);
      if (!currentUser) {
        throw new Error(`用户 ${userName} 不存在`);
      }

      const { SchoolId } = currentUser;
      if (!SchoolId) {
        throw new Error(`当前账号 ${userName} 无法获取学校信息`);
      }

      const { mainModel } = app;
      const findSlug = await mainModel.SchoolIdMap.findOne({
        where: {
          SchoolId,
        },
        raw: true,
      });

      if (!findSlug) {
        throw new Error('用户所在学校与当前学校地址不一致，请检查登录地址');
      }

      const { schoolSlug } = findSlug;

      if (schoolSlug !== ctx.schoolSlug) {
        throw new Error('用户所在学校与当前学校地址不一致，请检查登录地址');
      }

      return currentUser;
    }

    async getClassTreeKS({ user, PageIndex, PageSize }) {
      const { ctx } = this;

      const currentUser = await ctx.service.sso.checkUserKS(user.ssoName);
      // const currentUser = await ctx.service.sso.checkUserKS('JIANGJIE');
      // JIANGJIE
      // ZHOUYI201
      // LoginIds: 'ZHUYH367',
      // LoginIds: 'wangll16'
      // LoginIds: 'JJD007051'

      const { SchoolId, UserRoleId, RelationId } = currentUser;

      const isTeacher = parseInt(UserRoleId, 10) !== 0 && parseInt(UserRoleId, 10) !== -4;

      if (!isTeacher) {
        throw new Error('非教师账号，无法获取班级！');
      }

      if (!SchoolId) {
        throw new Error(`当前账号 ${user.ssoName} 无法获取学校班级`);
      }

      const gradeBody = {
        SchoolId,
        Status: 99,
        Page: PageIndex,
        PageSize,
      };
      const gradeResponse = await ctx.service.sso.getInfoKS({ path: 'GetGradeList', reqBody: gradeBody });

      const { ResultList: GradeList } = gradeResponse;

      for (const grade of GradeList) {
        const classBody = {
          GradeId: grade.Id,
          Status: 99,
          Page: PageIndex,
          PageSize
        };
        const classResponse = await ctx.service.sso.getInfoKS({ path: 'GetClassList', reqBody: classBody });

        const { ResultList: ClassList } = classResponse;
        grade.class = ClassList;
      }

      const years = GradeList.map(i => i.EnterYear);
      const yearList = Array.from(new Set(years));

      const classDatas = [];
      for (const year of yearList) {
        const gradeData = {
          title: `${year} 年入学`,
          key: year,
          children: [],
          level: 'year'
        };

        const gradeDataChildren = GradeList.filter(i => i.EnterYear === year);

        const gradeNames = [...(new Set(gradeDataChildren.map(i => i.Name)))];
        const classNameData = [];
        for (const gradeName of gradeNames) {
          const currentGrade = gradeDataChildren.find(i => i.Name === gradeName);
          if (!currentGrade || !currentGrade.class || !currentGrade.class.length) {
            continue;
          }

          const currentClasses = currentGrade.class;

          if (!currentClasses.length) {
            continue;
          }

          const classItem = {
            title: `${gradeName}`,
            key: `${year}${gradeName}`,
            level: 'grade',
            children: [],
          }
          classItem.children = currentClasses;
          classItem.children.forEach(i => {
            i.title = i.Name;
            i.key = i.Id;
            i.level = 'class';
            i.GradeName = gradeName;
            i.ClassID = i.Id;
          });

          classNameData.push(classItem);    
        }

        if (classNameData.length) {
          gradeData.children = classNameData;
          classDatas.push(gradeData);
        }

      }

      return { classDatas, SchoolId, TeacherId: RelationId };
    }

    async getTeacherClassList({ SchoolId, TeacherId }) {
      const { ctx } = this;
      const classBody = {
        SchoolId,
        TeacherId,
        Page: 1,
        PageSize: 100,
      };
      const classRes = await ctx.service.sso.getInfoKS({ path: 'GetTeacherClassList', reqBody: classBody });

      const { ResultList } = classRes;
      return ResultList;
    }

    async getAllStudentsBySchoolID({ SchoolId }) {
      const { ctx } = this;

      let studentsList = [];
      const userBody = {
        SchoolId,
        pageSize: 100
      };
      const userResponse = await ctx.service.sso.getInfoKS({ path: 'GetUserList', reqBody: userBody });

      const { ResultList, TotalRecords } = userResponse;
      studentsList = ResultList;

      if (TotalRecords && ResultList.length && ResultList.length < TotalRecords) {

        const { user } = ctx.session;
        const queryKey = md5(`${user.userName}${SchoolId}${TotalRecords}`);

        if (querySession[queryKey]) {
          studentsList = querySession[queryKey];
          return studentsList;
        }

        const userBody = {
          SchoolId,
          pageSize: TotalRecords
        };
        const userResponse = await ctx.service.sso.getInfoKS({ path: 'GetUserList', reqBody: userBody });
  
        const { ResultList } = userResponse;
        studentsList = ResultList;
        
        querySession[queryKey] = studentsList;
      }

      return studentsList;
    }

    async getStudentsByClassIDKS({ selectPullClassIDs, SchoolId, Page, PageSize }) {
      const { ctx } = this;

      if (!selectPullClassIDs.length) {
        return [];
      }

      const studentsClass = [];
      for (const classID of selectPullClassIDs) {
        const studentMap = await ctx.service.sso.getStudentsByClassIDSingleKS({ classID, SchoolId, Page, PageSize });
        studentsClass.push(studentMap);
      }

      return studentsClass;
    }

    async getStudentsByClassIDSingleKS({ classID, SchoolId, Page, PageSize }) {
      const { ctx } = this;

      const studentsBody = {
        classID: parseInt(classID),
        Page,
        PageSize,
      };
      const studentResultsRaw = await ctx.service.sso.getInfoKS({ path: 'GetStudentList', reqBody: studentsBody });

      if(!studentResultsRaw) {
        throw new Error('未能获取当前班级学生');
      }

      const { TotalRecords: studentsTotal } = studentResultsRaw;

      const newStudentsBody = {
        classID: parseInt(classID),
        PageSize: studentsTotal,
      };

      const studentResults = await ctx.service.sso.getInfoKS({ path: 'GetStudentList', reqBody: newStudentsBody });
      if(!studentResults) {
        throw new Error('未能获取当前班级全部学生');
      }

      const { ResultList: studentList = [], CurrentPage, TotalPages, TotalRecords, PageSize: currentPageSize } = studentResults;

      const newList = [];
      const length = studentList.length;

      let j = 0;
      for (let i = 0; i < length; i++) {
        const item = studentList[i];
        const isLegal = item.Status === 99 || item.Status === 1 || item.Status === 2;
        if (!isLegal) {
          continue;
        }

        // 查询用户登录名
        const userBody = {
          SchoolId,
          RelationId: item.Id,
        };

        const userResponse = await ctx.service.sso.getInfoKS({ path: 'GetUserList', reqBody: userBody });
        const { ResultList } = userResponse;

        if (!ResultList || !ResultList.length) {
          console.error(item, 'item================GetUserList')
          throw new Error(`未查找到学生 ${item.Name} 用户信息`);
        }

        const studentMap = ResultList.find(i => parseInt(i.RelationId, 10) === item.Id);

        if (!studentMap) {
          console.error(item, 'item================studentMap')
          throw new Error('无法查找到学生  ${item.Name} 的账号')
        }

        item.ssoName = studentMap.LoginId;
        item.index = j + 1;
        item.XM = item.Name;
        item.XSZH = item.ssoName || item.Id;
        item.Gender = item.Sex;

        newList.push(item);

        j++;
      }

      const studentMap = {
        classID: classID,
        students: newList,
        CurrentPage,
        TotalPages,
        TotalRecords,
        PageSize: currentPageSize,
      };

      return studentMap;
    }

    // 检查教师账号是否已存在
    async checkSSOExist(checkName, UserType, schoolID) {
      const { model } = this.ctx;

      const condition = {
        ssoName: checkName
      };

      if (schoolID) {
        condition['school'] = schoolID;
      }

      const user = await model.User.findOne({
        where: condition,
      });

      if (!user) {
        if (UserType !== '教师') {
          throw new Error(`当前账号 ${checkName} 不存在，请联系教师在后台批量导入班级学生账号，待教师导入账号成功后再次尝试登录`)
        } else {
          return;
        }
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      });
      
      user.teamIDs = teams.map(team => team.teamID);

      // 记录用户登录session
      const signIn = await this.ctx.service.user.recordUserSession(user.id);
      if (user.dataValues) {
        user.dataValues.signIn = signIn;
      }

      user.signIn = signIn;

      return user;
    }

    // 绑定账号
    async bindHxrAccount({ bindName: userName, UserName: ssoName, OrgCode, UserType, schoolID, newPassword, Email, QQ }, transaction) {
      const { ctx } = this;
      const { session, model, schoolSlug } = ctx;

      // 查找已存在账号
      let user = await model.User.findOne({
        where: {
          username: userName
        },
        transaction
      });

      // 如果不存在创建新账号
      if (!user) {
        throw new Error('用户名不存在！');
      }

      // 如果存在则绑定
      const { password } = user;
      if (newPassword !== password) {
        throw new Error('账号密码不正确');
      }

      const ssoInfo = {}

      if (Email) {
        ssoInfo['Email'] = Email;
      }

      if (QQ) {
        ssoInfo['QQ'] = QQ;
      }

      const updateObj = {
        ssoName: ssoName,
        sso: ssoInfo
      }

      if (schoolID) {
        updateObj['school'] = schoolID;
      }

      await model.User.update(updateObj, {
        where: {
          username: userName
        },
        transaction
      });

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      });
      
      user.teamIDs = teams.map(team => team.teamID);

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        ssoName,
        OrgCode,
        name: user.name,
        isAdmin: user.adminAuthority? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug ? schoolSlug : this.ctx.schoolSlug,
        teamIDs: user.teamIDs || [], // 用户所属班级
      };
    }

    // 新建账号
    async createHxrAccount({ bindName: userName, UserName: ssoName, RealName, schoolID, OrgCode, UserType, newPassword, Email, QQ }, transaction) {
      const { ctx } = this;
      const { session, model, schoolSlug } = ctx;

      // 查找已存在账号
      let user = await model.User.findOne({
        where: {
          username: userName
        },
        transaction
      });

      if (user) {
        throw new Error('用户名已存在！');
      }

      const ssoInfo = {};

      if (Email) {
        ssoInfo['Email'] = Email;
      }

      if (QQ) {
        ssoInfo['QQ'] = QQ;
      }

      const creatObj = {
        username: userName,
        password: newPassword,
        name: RealName || userName,
        adminAuthority: UserType === '教师' ? { "team":true,"user":true,"course":true,"homePage":true,"train":true } : null,
        sso: ssoInfo,
        ssoName: ssoName,
      }

      if (schoolID) {
        creatObj['school'] = schoolID;
      }

      user = await model.User.create(creatObj, { transaction });

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      });
      
      user.teamIDs = teams.map(team => team.teamID);

      // 写入sesion
      session.user = {
        id: user.id,
        username: user.username,
        ssoName,
        OrgCode,
        name: user.name,
        isAdmin: user.adminAuthority ? 1 : 0,
        avatar: user.avatar,
        adminAuthority: user.adminAuthority,
        lastInteractionAt: (new Date()).getTime(),
        schoolSlug: schoolSlug,
        teamIDs: user.teamIDs || [], // 用户所属班级
      };
    }

    async bindHxrAccountAuto({ UserName, RealName, UserType, Email, QQ, OrgCode, field }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { sso: allSSO } = app.config;

      if (UserType !== '教师' && field === 'admin') {
        throw new Error('学生账号请至前台登录！');
      }

      const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }

      const sso = allSSO[deploymentSlug];

      const ssoInfo = {}

      if (Email) {
        ssoInfo['Email'] = Email;
      }

      if (QQ) {
        ssoInfo['QQ'] = QQ;
      }

      const newPassword = UserType === '教师' ? sso.teacherPassword : sso.studentPassword;

      let user = await model.User.findOne({
        where: {
          ssoName: UserName
        },
        raw: true,
      });

      if (!user) {
        // 查找已存在账号
        user = await model.User.findOne({
          where: {
            username: UserName
          },
          raw: true,
          transaction
        });

        // 如果不存在创建新账号
        if (!user) {
          const creatObj = {
            username: UserName,
            password: newPassword,
            name: RealName || UserName,
            adminAuthority: UserType === '教师' ? { "team":true,"user":true,"course":true,"homePage":true } : null,
            sso: ssoInfo,
            ssoName: UserName,
          }
    
          user = await model.User.create(creatObj, { transaction });
        } else {
          // 如果存在则绑定
          const updateObj = {
            ssoName: UserName,
            sso: ssoInfo
          }

          user.ssoName = UserName;
          user.sso = ssoInfo;

          await model.User.update(updateObj, {
            where: {
              username: UserName
            },
            transaction
          });
        }
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      });
      
      user.teamIDs = teams.map(team => team.teamID);

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      if (user.dataValues) {
        user.dataValues.signIn = signIn;
      }

      user.signIn = signIn;

      return user;
    }

    async getVerificationCode() {
      const { ctx } = this;
      const { session } = ctx;
      const { user } = ctx.session;
      const { sso: allSSO } = app.config;

      const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }
      
      const sso = allSSO[deploymentSlug];

      const TimeSpan = Math.round(new Date().getTime() / 1000);
      const EncryptString = md5(`${sso.AppCode}${user.ssoName}${TimeSpan}${sso.AppKey}`);

      const getMessageURL = `${sso.path}/api/ssoauth/SMSSend?AppCode=${sso.AppCode}&EncryptString=${EncryptString}&TimeSpan=${TimeSpan}&UserName=${user.ssoName}`;

      // 默认验证码等待时间
      const DEFAULTCODECOUNT = sso.DEFAULTCODECOUNT;
       // 查询是否到达等待时间
       if (session.phoneCodeSendTime) {
        const sendTimeBetween =  Date.now() - session.phoneCodeSendTime;
        if( sendTimeBetween / 1000 <= DEFAULTCODECOUNT) {
          throw new Error('验证码已发送，请勿重复提交！')
        }
      }
      session.phoneCodeSendTime = Date.now();

      const response = await request(getMessageURL, 'POST');

      if (response.Code !== 200) {
        throw new Error(response.Description)
      }
      
      return response;
    }

    async getStudentsByClassID(query) {
      const { VerificationCode, selectPullClassIDs } = query;
      const { sso: allSSO } = app.config;
      const { user } = this.ctx.session;

      const deploymentSlug = await this.ctx.service.sso.getSSOConfig();

      if (!deploymentSlug) {
        throw new Error('当前学校未配置单点登录，请检查系统地址');
      }

      const sso = allSSO[deploymentSlug];

      const studentsClass = [];

      if (!selectPullClassIDs.length) {
        return [];
      }

      for (const BJBH of selectPullClassIDs) {
        const TimeSpan = Math.round(new Date().getTime() / 1000);
        const EncryptString = md5(`${sso.AppCode}${user.ssoName}${VerificationCode}${BJBH}${TimeSpan}${sso.AppKey}`);
  
        const studentURL = `${sso.path}/api/ssoauth/GetXSList?AppCode=${sso.AppCode}&EncryptString=${EncryptString}&TimeSpan=${TimeSpan}&UserName=${user.ssoName}&VerificationCode=${VerificationCode}&BJBH=${BJBH}`;
  
        const studentResults = await request(studentURL, 'POST');
  
        if (studentResults.Code !== 200) {
          throw new Error(studentResults.Description)
        }

        const studentList = studentResults.Result;
        const newList = studentList.map((item, index) => { 
          item.index = index + 1;
          item.Gender = item.XBM;
          return item; 
        })

        const studentMap = {
          classID: BJBH,
          students: newList,
        };

        studentsClass.push(studentMap);
      }

      return studentsClass;
    }

    async importClassAndStudentsNje({ selectClasses, deploymentSlug, currentYear, studentPassword }, transaction) {
      const { ctx } = this;
      // 上传班级和学生
      for (const item of selectClasses) {
        const { schoolId, schoolName, GradeName, Name, ClassCode, className: ClassName, customClassName } = item;

        const newArr = [];
        const defaultNameMap = {
          'hxr.jsnje.cn': `${GradeName}${ClassCode}班`,
          'ai.ksedu.cn': `${GradeName}${Name}`,
          'hxr.i51cy.com': `${GradeName}${Name}`,
          'njyz': `${schoolName}${GradeName}${ClassName}`,
        };

        const className = customClassName || defaultNameMap[deploymentSlug];

        item.studentList.forEach(stu => {
          const { studentCode, ssoName, XM, XSZH } = stu;

          const newStu = {
            username: XSZH,
            name: XM,
            ssoName: ssoName,
            password: studentPassword,
            year: currentYear,
            className,
            school: schoolId,
            sen: studentCode, // 学籍号
          }

          newArr.push(newStu);
        });

        const userArr = JSON.stringify(newArr);
        await ctx.service.user.newcreateUserList(userArr, transaction);
      }
    }

    // deprecated
    async importClassAndStudentsKS({ selectClasses, SchoolId, sso, deploymentSlug, currentYear }, transaction) {
      const { ctx } = this;

      // 上传班级和学生
      for (const item of selectClasses) {
        const newArr = [];
        const defaultNameMap = {
          'hxr.jsnje.cn': `${item.GradeName}${item.ClassCode}班`,
          'ai.ksedu.cn': `${item.GradeName}${item.Name}`,
          'hxr.i51cy.com': `${item.GradeName}${item.Name}`,
        }
        const className = item.customClassName ? item.customClassName : defaultNameMap[deploymentSlug];

        const classID = item.ClassID;

        const studentsBody = {
          classID: parseInt(classID),
          PageSize: item.TotalRecords,
        };
        const studentResults = await ctx.service.sso.getInfoKS({ path: 'GetStudentList', reqBody: studentsBody });
        const { ResultList: studentList = [] } = studentResults;

        for (const stu of studentList) {
          const isLegal = stu.Status === 99 || stu.Status === 1 || stu.Status === 2;
          if (!isLegal) {
            continue;
          }

          const userBody = {
            SchoolId,
            RelationId: stu.Id,
          };
          const userResponse = await ctx.service.sso.getInfoKS({ path: 'GetUserList', reqBody: userBody });
          const { ResultList } = userResponse;
  
          const studentMap = ResultList.find(i => parseInt(i.RelationId, 10) === stu.Id);
  
          if (!studentMap) {
            throw new Error('无法查找到学生的账号')
          }

          stu.ssoName = studentMap.LoginId;
          stu.XM = stu.Name;
          stu.XSZH = stu.ssoName || stu.Id;
          stu.Gender = stu.Sex;

          const newStu = {
            username: stu.XSZH,
            name: stu.XM,
            ssoName: stu.ssoName || stu.XSZH,
            password: sso.studentPassword,
            year: currentYear, //item.Grade,
            className,
            school: null,
            sen: null,
          }
          newArr.push(newStu);
        }

        const userArr = JSON.stringify(newArr);
        await ctx.service.user.newcreateUserList(userArr, transaction);
      }
    }

    // 获取昆山需要更新的学校列表
    // 参数说明见文档 昆山智慧教育平台接口规范2.0.pdf
    async getAllSchoolKS() {
      const { ctx } = this;
      const { mainModel } = app;

      const body = {
        PageSize: 1000,
      };

      const response = await ctx.service.sso.getInfoKS({ path: 'GetSchoolList', reqBody: body });

      const { ResultList = [] } = response;

      // 过滤已删除学校
      // 状态，99 为正常，-1 为删除
      let schoolList = ResultList.filter(i => i.Status !== -1);

      // 过滤教育类型，除去幼儿园
      schoolList = schoolList.filter(i => parseInt(i.EducationType, 10) !== 0);

      // 筛选需要更新的学校
      const allSchool = await mainModel.SchoolIdMap.findAll();

      const existSchoolNames = allSchool.map(i => i.SchoolId);
      schoolList = schoolList.filter(i => !existSchoolNames.includes(i.Id));

      // 生成拼音
      schoolList.forEach((item) => {
        item.slug = item.Name.split('').map(i => cnchar.spell(i)[0]).join('').toLowerCase();
      })

      await fs.writeFile('/data/tmp/ksSchool.json', JSON.stringify(schoolList));

      // 存入excel
      const xls = json2xls(schoolList);
      fs.writeFileSync('/data/tmp/ksSchool.xlsx', xls, 'binary');

      // 查询slug是否有重复
      const currentSlug = schoolList.map(i => i.slug);
      const existSlugs = await mainModel.SchoolIdMap.findAll({
        where: {
          schoolSlug: {
            [Op.in]: currentSlug
          }
        }
      });

      if (existSlugs && existSlugs.length) {
        await fs.writeFile('/data/tmp/existSlugs.json', JSON.stringify(existSlugs));

        // 存入excel
        const xls = json2xls(existSlugs);
        fs.writeFileSync('/data/tmp/existSlugs.xlsx', xls, 'binary');
      }

      // return schoolList;
    }
  }

  return SSOService;
 } 
