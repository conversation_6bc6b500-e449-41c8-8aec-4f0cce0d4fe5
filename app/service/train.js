const { Op, QueryTypes } = require('sequelize');
const sequelize = require('sequelize');
const fs = require("mz/fs");
const fsExtra = require("fs-extra");
const moment = require('moment');
const path = require('path');

function safePipe(readStream, writeStream)
{
  return new Promise(function(resolve, reject){
    readStream.pipe(writeStream);
    writeStream.on('finish', function(){
      resolve();
    });
  });
}

module.exports = app => {

  class TrainService extends app.Service {
    // 课程 数据管理 获取文件列表
    async list(targetDirectory) {
      // console.log('targetDirectory :>> ', targetDirectory);
      const { ctx } = this;
      // 读之前检查目录是否存在
      const ifExist = await fs.exists(targetDirectory);

      if (!ifExist) {
        return [];
      }
      
      // 获取目录文件
      const fileList = await fs.readdir(targetDirectory);

      if (!fileList || !fileList.length) {
        return [];
      }

      // 定义返回数组
      const fileDirList = [];
      for (const dir of fileList) {
        try {
          // 获取对应数据
          const pathDir = `${targetDirectory}/${dir}`;
          const fileInfo = await fs.stat(pathDir);
          // 获取文件信息错误，则视为无该文件
          if (!fileInfo) {
            continue;
          }

          // 判断是否为目录
          const ifDirectory = fileInfo.isDirectory();

          // 若为目录则递归获取目录文件
          if (ifDirectory) {
              // 递归
            const childFileDir = await ctx.service.course.list(pathDir);

            fileDirList.push({
              name: dir,
              ...fileInfo,
              childFile: childFileDir.concat()
            });

            continue;
          }

          // 返回值处理
          fileDirList.push({
            name: dir,
            ...fileInfo
          })
        } catch(err) {
          continue;
        }
      }
      return fileDirList;
    }

    // 删除资源文件
    async deleteSourceFile(path){

      const exist = await fs.exists(path);
      if (!exist) {
        return;
      }

      // 干掉文件
      await fsExtra.remove(path);
    }

    // 训练题目资源文件上传
    async trainQuestionAssetsUpload(readStream, questionID, subFilePath, isCover){
      const { ctx } = this;
      const fullName = readStream.filename;
        
      // 设置路径
      const dirPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${questionID}${subFilePath}`;
      const path = `${dirPath}/${fullName}`;
      if (!isCover) {
        const fileExist = await fs.exists(path);
        if (fileExist) {
          throw new Error('文件已存在');
        }
      }
      // 训练资源原始目录是否存在，不存在则创建
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        await ctx.service.file.mkdirs(dirPath);
      }

      // 记录URL
      try {
        const writeStream = fs.createWriteStream(path);
        // 记录转发
        await safePipe(readStream, writeStream);
      } catch(err) {
        console.log('err :>> ', err);
        throw new Error(err.message);
      }

      // 更新题目updated_at
      let newQuestionID = parseInt(questionID, 10);
      await ctx.model.query(
        `UPDATE train_questions SET updated_at = ? WHERE id = ${newQuestionID}`,
        { 
          replacements: [moment().format('YYYY-MM-DD HH:mm:ss')], 
          type: QueryTypes.UPDATE
        }
      )

      return `${app.config.file.url}/trainQuestion/${questionID}${subFilePath}/${fullName}`;;
    }

    // 训练资源文件上传
    async trainAssetsUpload(readStream, trainID, subFilePath, isCover){
      const { ctx } = this;
      const fullName = readStream.filename;
      // const matchFileName = fullName.match(/(^[^\.]+)\.(.+)$/);
      // const fileName = matchFileName ? matchFileName[1]: fullName;
      // const extName = matchFileName ? matchFileName[2]: 'ext';
        
      // 设置路径
      const dirPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/train/${trainID}${subFilePath}`;
      const path = `${dirPath}/${fullName}`;
      if (!isCover) {
        const fileExist = await fs.exists(path);
        if (fileExist) {
          throw new Error('文件已存在');
        }
      }
      // 训练资源原始目录是否存在，不存在则创建
      const dirPathExist = await fs.exists(dirPath);
      if (!dirPathExist) {
        try {
          await ctx.service.file.mkdirs(dirPath);
        } catch(err) {
          console.log('err :>> ', err);
          throw new Error(err.message);
        }
      }

      // 记录URL
      try {
        const writeStream = fs.createWriteStream(path);
        // 记录转发
        await safePipe(readStream, writeStream);
      } catch(err) {
        console.log('err :>> ', err);
        throw new Error(err.message);
      }

      return `${app.config.file.url}/train/${trainID}${subFilePath}/${fullName}`;
    }

    // 创建训练
    async createTrain(node, transaction) {
      const { model } = this.ctx;
      const { Train } = model;
      const result = await Train.create(node, {
        transaction,
        raw: true,
      });

      const { id } = result;

      // 创建训练时，创建训练系列
      const { series } = node;
      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: series
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      const { trainIDs } = selectSeries;
      const newTrainIDs = trainIDs ? [...new Set([...trainIDs, id])] : [id];

      if (this.checkStatusToAddTrains(transaction, newTrainIDs) > 10) {
        throw new Error(' 试用授权最多只能同时拥有10张试卷');
      }

      await model.TrainSeries.update({
        trainIDs: newTrainIDs
      }, {
        where: {
          id: series
        },
        transaction
      });

      return result;
    }
    // 创建训练
    async questionInfo(questionID, transaction) {
      const { model } = this.ctx;
      const questionDetail = await model.Questions.findOne({
        where: {
          id: questionID
        },
        attributes: ['questionDetail'],
        raw: true,
        transaction
      });

      return questionDetail;
    }

    async copyTrain(node, transaction) {
      const { model } = this.ctx;
      const { Train } = model;
      const { id, selectNewSeries, ...info } = node;
      
      // 查询原有content
      const originTrain = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      if (!originTrain) {
        throw new Error(`未查找到原有训练`)
      }

      const { content, year, duration, score, notice, difficulty, discriminative, 
        status,
        ifShowScore, 
        ifShowCorrectionResults,
        ifShowWrongAnswer,
        ifSetWrongProblemCollection,
        templateDifficulty,
        isFinish } = originTrain;

      const result = await Train.create({ ...info, content, isFinish, year, duration, score, notice, difficulty, discriminative,
        status,
        ifShowScore, 
        ifShowCorrectionResults,
        ifShowWrongAnswer,
        ifSetWrongProblemCollection,
        templateDifficulty,
      }, {
        transaction,
        raw: true,
      });

      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: selectNewSeries
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      const { trainIDs } = selectSeries;
      const newTrainIDs = trainIDs ? [...new Set([...trainIDs, result.id])] : [result.id];

      await model.TrainSeries.update({
        trainIDs: newTrainIDs
      }, {
        where: {
          id: selectNewSeries
        },
        transaction
      });

      return result;
    }

    // 创建训练
    async createTrains(nodes, transaction) {
      const { model } = this.ctx;
      const { Train } = model;
      const result = await Train.bulkCreate(nodes, {
        transaction,
        raw: true,
      });

      const seriesId = nodes.map(row => row.series)[0];
      if (!seriesId) {
        throw new Error('请选择系列');
      }

      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: seriesId
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      const { trainIDs } = selectSeries;

      const newTrainIDs = trainIDs && trainIDs.length ? [...new Set([...trainIDs, ...result.map(row => row.id)])] : result.map(row => row.id);

      if (this.checkStatusToAddTrains(transaction, newTrainIDs) > 10) {
        throw new Error(' 试用授权最多只能同时拥有10张试卷');
      }

      await model.TrainSeries.update({
        trainIDs: newTrainIDs
      }, {
        where: {
          id: seriesId
        },
        transaction
      });

      return result;
    }

    // 修改训练
    async putTrain(id, node, transaction) {
      const { model } = this.ctx;
      const { ctx } = this;
      const { Train } = model;
      
      // 检查权限
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      const { teachers = [] } = node;

      const originTrain = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      if (!originTrain) {
        throw new Error(`未查找到原有训练`)
      }
           
      await Train.update(node, {
        where: {
          id
        },
        transaction
      });

      // 如果分享给了其他教师，则建立对应的系列
      if (teachers && teachers.length) {
        const allDefaultSharedSeries = await model.TrainSeries.findAll({
          where: {
            name: '默认分享系列',
            createUserID: {
              [Op.in]: teachers
            }
          },
          raw: true,
          transaction
        });

        const allNewSharedSeries = [];
        for (const teacherUserID of teachers) {
          const defaultSharedSeries = allDefaultSharedSeries.find(row => row.createUserID === teacherUserID);
          if (!defaultSharedSeries) {
            allNewSharedSeries.push({
              name: '默认分享系列',
              createUserID: teacherUserID,
              priority: 0,
              trainIDs: [id]
            });
          } else {
            const { trainIDs } = defaultSharedSeries;
            const newTrainIDs = trainIDs ? [...new Set([...trainIDs, id])] : [id];

            allNewSharedSeries.push({
              ...defaultSharedSeries,
              trainIDs: newTrainIDs
            });
          }
        }

        if (allNewSharedSeries && allNewSharedSeries.length) {
          await model.TrainSeries.bulkCreate(allNewSharedSeries, {
            updateOnDuplicate: ['trainIDs'],
            transaction
          });
        }
      }

      // 如果是取消分享的教师，则删除对应的系列
      const { teachers : originTeachers = [] } = originTrain;
      if (originTeachers && originTeachers.length) {
        const removeTeachers = originTeachers.filter(row => !teachers.includes(row));
        
        const allRemoveSharedSeries = await model.TrainSeries.findAll({
          where: {
            createUserID: {
              [Op.in]: removeTeachers
            },
            [Op.or]: [
              sequelize.literal(`
              json_contains(
                JSON_EXTRACT(
                  cast(train_series.trainIDs AS json),
                  '$'
                ),JSON_ARRAY(${id})
              ) = 1
            `)
            ]
          },
          raw: true,
          transaction
        });

        const removeSeries = [];
        for (const removeSharedSeries of allRemoveSharedSeries) {
          const { trainIDs } = removeSharedSeries;
          const newTrainIDs = trainIDs.filter(row => row !== id);

          removeSeries.push({
            ...removeSharedSeries,
            trainIDs: newTrainIDs
          });
        }

        if (removeSeries && removeSeries.length) {
          await model.TrainSeries.bulkCreate(removeSeries, {
            updateOnDuplicate: ['trainIDs'],
            transaction
          });
        }
      }

      const result = await Train.findOne({
        where: {
          id
        },
        transaction
      });

      return result;
    }

    // 替换题目在试卷中的引用
    async replaceQuestionInTrains({ trainIDs, deleteQuestionID, addQuestionID }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Train } = model;

      // 删除题目
      if (!deleteQuestionID) {
        throw new Error('请提供需要删除的题目id');
      }

      // await ctx.service.questions.destoryQuestions(deleteQuestionID, transaction);

      if (!trainIDs || !trainIDs.length) {
        return;
      }

      // 移除试卷中对此题的引用
      const trains = await Train.findAll({
        where: {
          id: {
            [Op.in]: trainIDs.map(i => parseInt(i))
          }
        },
        raw: true,
        transaction
      });

      if (!trains || !trains.length) {
        throw new Error(`未查询到试卷信息 ${trainIDs.join(', ')}`);
      }

      // 处理每个试卷
      for (const train of trains) {
        const { content = [], template = [] } = train;
        let hasChanged = false;

        // 遍历试卷的每个内容块 - 使用 for 循环替代 for...of
        for (let i = 0; i < content.length; i++) {
          const block = content[i];
          const templateBlock = template[i];
          if (block && block.questionIDs) {
              const deleteIndex = block.questionIDs.indexOf(deleteQuestionID);
              
              // 如果找到要删除的题目
              if (deleteIndex !== -1) {
                  // 情况1：同时存在addQuestionID和deleteQuestionID
                  if (addQuestionID && block.questionIDs.includes(addQuestionID)) {
                      // 只删除deleteQuestionID
                      block.questionIDs.splice(deleteIndex, 1);
                      hasChanged = true;
                      templateBlock?.count && templateBlock.count--;
                      block.count--;
                  }
                  // 情况2：只有deleteQuestionID
                  else if (addQuestionID) {
                      // 替换为addQuestionID
                      block.questionIDs.splice(deleteIndex, 1, addQuestionID);
                      hasChanged = false;
                  }
              }
          }
        }
        // 如果试卷内容有变动，标记为未完成
        if (hasChanged) {
          train.isFinish = 0;
        }
      }

      return await Train.bulkCreate(trains, {
        updateOnDuplicate: ['content', 'template', 'isFinish'],
        transaction
      });
    }

    // 记录训练修改情况
    async recordTrainUpdate(requestBody, transaction) {
    const { model } = this.ctx;
    const { SystemConfig } = model;
    const { trainIDs = [], deleteQuestionID, keepId } = requestBody;

    // 先查找原有的去重记录
    const record = await SystemConfig.findOne({
      where: {
        key: 'trainUpdateRecord'
      },
      transaction // 如果有事务
    });
    
    if (!record) {
      throw new Error('记录不存在'); // 或自行处理
    }
    
    const bulkCreateArr = record.value || []; 
    trainIDs.forEach(id => {
      bulkCreateArr.push({
        trainID: id,
        deleteQuestionID,
        keepId
      }); 
    });
    
    const newRecord = await SystemConfig.update(
      {
        value: bulkCreateArr
      },
      {
        where: {
          key: 'trainUpdateRecord'
        },
        transaction
      }
    );

    return newRecord;
    }

    async putTrainTemplate(id, node, transaction) {
      const { model } = this.ctx;
      const { ctx } = this;
      const { Train } = model;

      // 检查权限
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期');
      }

      const { templateName } = node;
      if (templateName !== '自由组卷') {
        throw new Error('只可以调整为自由组卷');
      }

      await Train.update(node, {
        where: {
          id
        },
        transaction
      });

      const result = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      return result;
    }

    // 获取训练
    async getTrain(id) {
      const { ctx } = this;
      const { model } = this.ctx;
      const { Train, Questions, Tag } = model

      // 检查权限
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      // console.log('model:',model)
      const result = await Train.findOne({
        where: {
          id
        },
        raw: true,
        // transaction
      });
      const tagAllDatas = await Tag.findAll({
        where: { tagType: 'train' },
        attributes: ['id', 'tagName', 'parentID'],
        raw: true,
        // transaction,
      });

      if (result.content) {
        for(const key in result.content) {
          const row = result.content[key];

          if (!row || !row.questionIDs) {
            continue;
          }
          row.questions = [];
          const questionsDataResult = await Questions.findAll({
            where: { id: { [Op.in]: row.questionIDs } },
            include: [{
              model: model.Tag,
              as: 'tags',
              attributes: ['id', 'tagName', 'tagType', 'parentID'],
            }, {
              model: model.User,
              as: 'createUser',
              attributes: ['id', 'name', 'avatar'],
            }],
          })
          
          const questionsData = questionsDataResult && questionsDataResult.dataValues ? questionsDataResult.dataValues : questionsDataResult;
          for (const questionID of row.questionIDs) {
            const questionRow = questionsData.find(subRow => {
              const subRowData = subRow && subRow.dataValues ? subRow.dataValues : subRow;
              if(subRowData.id === parseInt(questionID)) {
                return true;
              }
              return false
            });
            if (!questionRow) {
              continue;
            }

            row.questions.push(questionRow);
          }

          let questionsRequests = []
          for(const questionsRow of row.questions) {
            const rowData = questionsRow.dataValues ? questionsRow.dataValues : questionsRow;
            rowData.tagIDs = rowData.tags ? rowData.tags.map(tag => tag.id) : [];

            let queryCount = 0;
            const getParentName = (row, allTags) => {
              // console.log(row, 'alltags')
              queryCount++;

              // 限制递归深度
              if (queryCount > 1000) {
                return `${row.tagName}`
              }

              if (row.parentID && row.parentID !== row.id) {
                const parentRow = allTags.find(item => item.id === row.parentID);
                const resultName = getParentName(parentRow, allTags)
            
                return `${resultName} > ${row.tagName}`
              }
            
              return `${row.tagName}`
            }

            rowData.tags ? rowData.tags.map(rowTag => {
              const tagRowData = rowTag.dataValues ? rowTag.dataValues : rowTag;
              delete tagRowData.train_question_tag;
              // console.log('tagRowData:', tagRowData)
              tagRowData.parentName = getParentName(tagRowData, tagAllDatas);
              return tagRowData
            }) : []

            if (!rowData || !rowData.trainsUsed || !rowData.trainsUsed.length) {
              continue;
            }

            questionsRequests = questionsRequests.concat(rowData.trainsUsed);
          }
          // console.log('questionsRequests:',questionsRequests);

          const trainUsedResults = await Train.findAll({
            attributes: ['id', 'name'],
            where: {
              id: { [Op.in]: questionsRequests }
            },
            raw: true,
            // transaction
          });

          // const tagResults = await QuestionTag.findAll({
          //   // attributes: ['id', 'questionID', 'tagID'],
          //   where: {
          //     questionID: { [Op.in]: row.questionIDs }
          //   },
          //   include: [{
          //     model: model.Tag,
          //     as: 'tags'
          //   }],
          //   raw: true,
          // });
          // console.log('tagResults:',tagResults);

          for(const questionsRow of row.questions) {
            const rowData = questionsRow.dataValues ? questionsRow.dataValues : questionsRow;
            // console.log('rowData:',rowData)
            // console.log('=========================================================')
            // console.log('rowData:',{...rowData})
            // console.log('=========================================================')

            if (!rowData || !rowData.trainsUsed || !rowData.trainsUsed.length) {
              continue;
            }

            rowData.trainsUsedList = rowData.trainsUsed.map(item => trainUsedResults.find(subRow => subRow.id === parseInt(item)));
          }
        }
      }
      // console.log('=========================================================')
      // console.log(result)
      // console.log('=========================================================')

      return result
    }

    // 删除训练
    async destoryTrain(id, seriesId, transaction = false) {
      const { model } = this.ctx;
      const { Train } = model

      const nowTrainData = await Train.findOne({
        where: {
          id
        },
        transaction,
        raw: true,
      });

      // 删除试卷如果是别人分享的，则只是去除分享，不真的删除试卷
      const currentUserID = this.ctx.session.user.id;
      if (nowTrainData.createUserID !== currentUserID) {
        // 如果是通过试卷中的分享给教师功能分享的，则去除记录中的当前用户ID
        if(nowTrainData.teachers && nowTrainData.teachers.includes(currentUserID)) {
          const teacherNewArr = nowTrainData.teachers.filter(row => row !== this.ctx.session.user.id);
          await Train.update({
            teachers: teacherNewArr
          }, {
            where: {
              id
            },
            transaction
          });
        }
        else {
          // 删除系列中的试卷
          const selectSeries = await model.TrainSeries.findOne({
            where: {
              id: seriesId
            },
            raw: true,
            transaction
          });

          if (!selectSeries) {
            throw new Error('系列不存在');
          }

          const { trainIDs } = selectSeries;
          const newTrainIDs = trainIDs.filter(row => row !== id);

          await model.TrainSeries.update({
            trainIDs: newTrainIDs
          }, {
            where: {
              id: seriesId
            },
            transaction
          });
        }
        return;
      }

      // 去除在所有系列中对此试卷的引用
      const allSeries = await model.TrainSeries.findAll({
        where: {
          [Op.or]: [
            sequelize.literal(`
            json_contains(
              JSON_EXTRACT(
                cast(train_series.trainIDs AS json),
                '$'
              ),JSON_ARRAY(${id})
            ) = 1
          `)
          ]
        },
        raw: true,
        transaction
      });

      const newAllSeries = [];
      for (const series of allSeries) {
        const { trainIDs } = series;
        const newTrainIDs = trainIDs.filter(row => row !== id);

        newAllSeries.push({
          ...series,
          trainIDs: newTrainIDs
        });
      }

      if (newAllSeries && newAllSeries.length) {
        await model.TrainSeries.bulkCreate(newAllSeries, {
          updateOnDuplicate: ['trainIDs'],
          transaction
        });
      }

      return await Train.destroy({
        where: {
          id
        },
        transaction
      });
    }

    async destoryTrains(trainIDs, seriesId, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Train } = model;

      if (!ctx.session || !ctx.session.user || !ctx.session.user.id) {
        throw new Error('请先登录');
      }

      const userID = ctx.session.user.id;

      const trains = await Train.findAll({
        where: {
          id: {
            [Op.in]: trainIDs
          }
        },
        transaction,
        raw: true,
      });

      let deleteIDs = [];
      let removeShareTrains = [];
      for (const train of trains) {
        const { id, createUserID, teachers = [] } = train;

        if (createUserID === userID) {
          deleteIDs.push(id);
          continue;
        }

        // 删除试卷如果是别人分享的，则只是去除分享，不真的删除试卷
        removeShareTrains.push({ ...train, teachers: teachers.filter(row => row !== userID) });
      }

      if (removeShareTrains && removeShareTrains.length) {
        await Train.bulkCreate(removeShareTrains, {
          updateOnDuplicate: ['teachers'],
          transaction
        })

        // 删除系列中的试卷
        const selectSeries = await model.TrainSeries.findOne({
          where: {
            id: seriesId
          },
          raw: true,
          transaction
        });

        if (!selectSeries) {
          throw new Error('系列不存在');
        }

        const { trainIDs: seriesTrainIds } = selectSeries;
        const removeTrainIDs = removeShareTrains.map(row => row.id);
        const newTrainIDs = seriesTrainIds.filter(row => !removeTrainIDs.includes(row));

        await model.TrainSeries.update({
          trainIDs: newTrainIDs
        }, {
          where: {
            id: seriesId
          },
          transaction
        });
      }

      if (deleteIDs && deleteIDs.length) {
        await Train.destroy({
          where: {
            id: {
              [Op.in]: deleteIDs
            }
          },
          transaction
        });

        // 如果是自己创建的试卷，需要去除在所有系列中对此试卷的引用
        const allSeries = await model.TrainSeries.findAll({
          raw: true,
          transaction
        });

        const newAllSeries = [];
        for (const series of allSeries) {
          const { trainIDs } = series;
          const needUpdate = trainIDs && trainIDs.length && trainIDs.some(row => deleteIDs.includes(row));
          if (!needUpdate) {
            continue;
          }
          
          const newTrainIDs = trainIDs.filter(row => !deleteIDs.includes(row));

          newAllSeries.push({
            ...series,
            trainIDs: newTrainIDs
          });
        }

        if (newAllSeries && newAllSeries.length) {
          await model.TrainSeries.bulkCreate(newAllSeries, {
            updateOnDuplicate: ['trainIDs'],
            transaction
          });
        }
      }
    }

    // 获取训练列表
    async getTrainList(node) {
      const { model } = this.ctx;
      const { Train } = model;
      let { pageSize, pageNum, year, series } = node;
      pageNum = pageNum ? parseInt(pageNum, 10) : 1;
      pageSize = pageSize ? parseInt(pageSize, 10) : 10;

      const condition = {
        [Op.or]: [
          { createUserID: this.ctx.session.user.id },
          sequelize.literal(`
            json_contains(
              JSON_EXTRACT(
                cast(train.teachers AS json),
                '$'
              ),JSON_ARRAY(${this.ctx.session.user.id})
            ) = 1
          `)
        ]
      };
      if (year) {
        condition.year = year;
      }

      if (series) {
        const selectSeries = await model.TrainSeries.findOne({
          where: {
            id: series
          },
          raw: true
        });

        if (!selectSeries) {
          throw new Error('系列不存在');
        }

        const { trainIDs = [] } = selectSeries;
        condition.id = {
          [Op.in]: trainIDs
        }
      }

      //获取对应值
      const list = await Train.findAll({
        where: condition,
        include: [{
          model: model.User,
          as: 'createUser',
          attributes: ['id', 'name', 'avatar'],
        }],
        order: [['created_at', 'desc']]
      });
      
      const total = await Train.count({
        where: condition
      });

      return { list, total };
    }

    // 获取训练列表
    async getTrainListWithComputerRoomTrain(node) {
      const { model } = this.ctx;
      const { Train } = model;
      let { year } = node;

      const condition = {};
      if (year) {
        condition.year = year;
      }

      //获取对应值
      const list = await Train.findAll({
        where: condition,
        order: [['created_at', 'desc']]
      });

      return list;
    }

    // 发布精品试卷
    async publishTrain(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      // 检查权限
      const configs = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction',
        },
        attributes: ['value'],
        raw: true,
        transaction
      });

      if (!configs) {
        throw new Error('没有发布精品试卷权限');
      }

      const { value } = configs;
      if (!value || !value['训练管理-精品试卷-发布']) {
        throw new Error('没有发布精品试卷权限');
      }

      // 写入精品试卷
      const { session, schoolSlug: publishSchoolSlug } = ctx;
      if (!session || !session.user) {
        throw new Error('请登录');
      }

      const { username: publishUserName, name: publishDisplayName } = session.user;
      const { mainModel } = app;

      const { year, trainName, seriesName, trainContent = [], trainTemplate, trainTemplateName, templateDifficulty, note } = node;
      
      // 检查每道题都属于远程题库
      const questionIDs = [];
      trainContent.forEach(i => questionIDs.push(...i.questionIDs));
      const allQuestions = await model.Questions.findAll({
        where: {
          id: {
            [Op.in]: questionIDs
          }
        },
        attributes: ['id', 'questionBankID', 'originalID'],
        transaction
      });

      // 题目id:题库id
      const questionBankMap = {};

      // 题目id: 远程题库题目id originalID
      const questionRemoteIDMap = {};

      // 题库id
      let bankIDs = [];
      let bankIDQuestionIDMap = {};
      for (const question of allQuestions) {
        const { id, questionBankID, originalID } = question;

        if (!questionBankID) {
          throw new Error(`题目 ${id} 所在题库没有记录`);
        }

        questionBankMap[id] = questionBankID;
        questionRemoteIDMap[id] = originalID;
        bankIDs.push(questionBankID);

        if (!bankIDQuestionIDMap[questionBankID]) {
          bankIDQuestionIDMap[questionBankID] = [id];
        } else {
          bankIDQuestionIDMap[questionBankID].push(id);
        }
      }

      bankIDs = [...new Set(bankIDs)];
      const currentQuestionBanks = await model.TrainQuestionBank.findAll({
        where: {
          id: {
            [Op.in]: bankIDs
          }
        },
        raw: true,
        transaction
      });

      if (!currentQuestionBanks || !currentQuestionBanks.length) {
        throw new Error('题库不存在');
      }

      // 题库id: toID/fromURL
      const questionBankIDMap = {};
      for (const questionBank of currentQuestionBanks) {
        const { id, ifSelfBuilding, fromURL, toID } = questionBank;

        // 远程题库发布学校
        const isPublish = ifSelfBuilding && toID;

        if (isPublish) {
          questionBankIDMap[id] = toID;
          continue;
        }

        // 远程题库接收学校
        const accept = !ifSelfBuilding && fromURL;
        if (accept) {
          questionBankIDMap[id] = parseInt(fromURL, 10);
          continue;
        }

        if (!isPublish && !accept) {
          let quesionIDs = bankIDQuestionIDMap[id];
          throw new Error(`题库 ${id} 非远程题库,无法发布为精品试卷，题目ID：${quesionIDs.join(',')}`);
        }
      }

      // 更新content
      trainContent.forEach((block) => {
        const { questionIDs } = block;

        const newQuestionIDs = [];
        questionIDs.forEach((questionID) => {
          const currentBankID = questionBankMap[questionID];

          // 如果有originalID, 记录[fromURL, originalID]
          if (questionRemoteIDMap[questionID]) {
            newQuestionIDs.push([questionBankIDMap[currentBankID], questionRemoteIDMap[questionID]]);
          } else {
            // 如果没有originalID, 记录[toID, questionID]
            newQuestionIDs.push([questionBankIDMap[currentBankID], questionID]);
          }
        });

        block.questionIDs = newQuestionIDs;
      });

      // 入库
      await mainModel.EliteTrains.create({
        publishSchoolSlug,
        publishUserName,
        publishDisplayName,
        year,

        seriesName,
        trainName,
        trainContent,
        trainTemplate,
        trainTemplateName,
        templateDifficulty,
        
        publishTime: new Date(),
        updateTime: new Date(),

        note,
      });
    }

    async getEliteTrains({ year }) {
      const { ctx } = this;
      const { model } = ctx;

      // 检查权限
      // const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();

      const allEliteTrains = await app.mainModel.EliteTrains.findAll({
        where: {
          year
        },
        order: [['trainName', 'ASC']],
        raw: true
      });

      const eliteTrainMap = {};
      for (const train of allEliteTrains) {
        const { seriesName } = train;
        if (!seriesName) {
          throw new Error('错误！精品试卷无系列名称')
        }

        if (!eliteTrainMap[seriesName]) {
          eliteTrainMap[seriesName] = [train];
        } else {
          eliteTrainMap[seriesName].push(train);
        }
      }

      const eliteTrains = [];
      for (const series in eliteTrainMap) {
        const currentTrains = eliteTrainMap[series];

        // 按名称中最后出现的数字排序
        currentTrains.sort((prev, cur) => {
          // 首先按短横分割的前部分排序，然后按后部分数字排序，如 精品Access-1, 精品Wps-2，精品Word-1
          const prevName = prev.trainName;
          const curName = cur.trainName;
          const prevNameSplit = prevName.split('-');
          const curNameSplit = curName.split('-');

          const prevNameSplitFirst = prevNameSplit[0];
          const curNameSplitFirst = curNameSplit[0];

          if (prevNameSplitFirst !== curNameSplitFirst) {
            return prevNameSplitFirst.charCodeAt(0) - curNameSplitFirst.charCodeAt(0);
          }

          const prevNameSplitLast = prevNameSplit[prevNameSplit.length - 1];
          const curNameSplitLast = curNameSplit[curNameSplit.length - 1];

          const prevNumbers = prevNameSplitLast.match(/[0-9]+/g);
          const curNumbers = curNameSplitLast.match(/[0-9]+/g);

          if (prevNumbers && prevNumbers.length && curNumbers && curNumbers.length) {
            return prevNumbers[prevNumbers.length - 1] - curNumbers[curNumbers.length - 1];
          }

          return true;
        });
        eliteTrains.push({ series: series, trains: currentTrains });
      }

      return eliteTrains;
    }

    // 导入精品试卷
    async importTrains(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { schoolSlug: importSchoolSlug } = ctx;

      if (!ctx.session || !ctx.session.user) {
        throw new Error('请登录');
      }

      const { id: userID, username: importUserName, name: importDisplayName } = ctx.session.user;
 
      const { year, seriesID, selectTrainIDs } = node;

      const allEliteTrains = await app.mainModel.EliteTrains.findAll({
        where: {
          id: {
            [Op.in]: selectTrainIDs
          }
        },
        raw: true,
      });

      if (!allEliteTrains || !allEliteTrains.length) {
        throw new Error('请选择需要导入的试卷');
      }

      if (this.checkStatusToAddTrains(transaction, selectTrainIDs) > 10) {
        throw new Error(' 试用授权最多只能同时拥有10张试卷');
      }
      // 获取题库和题目id缓存
      // 题库 fromURL： id
      const questionBankMap = {};

      // 题目 originalID: id
      const questionMap = {};

      let questionBankIDs = new Set();
      let lookupQuestionIDs = new Set(); 
      for (const eliteTrain of allEliteTrains) {
        const { trainContent } = eliteTrain;
        for (const block of trainContent) {
          const { questionIDs } = block;
          for (const questionSet of questionIDs) {
            if (!questionSet || !questionSet.length || questionSet.length !== 2) {
              throw new Error(`精品试卷 ${id} 结构错误`);
            }

            const questionBankID = questionSet[0];
            const questionID = questionSet[1];
            questionBankIDs.add(questionBankID);
            lookupQuestionIDs.add(questionID);
          }
        }
      }

      const questionBanks = await model.TrainQuestionBank.findAll({
        where: {
          [Op.or]: [
            {
              fromURL: {
                [Op.in]: [...questionBankIDs]
              }
            },
            {
              toID: {
                [Op.in]: [...questionBankIDs]
              }
            }
          ]
        },
        attributes: ['id', 'fromURL', 'toID'],
        raw: true,
        transaction
      });

      if (!questionBanks || !questionBanks.length) {
        throw new Error('远程题库不存在');
      }

      for (const questionBank of questionBanks) {
        // 远程题库接收方
        if (questionBank.fromURL) {
          questionBankMap[questionBank.fromURL] = questionBank.id;
        }

        // 远程题库发布方
        if (questionBank.toID) {
          questionBankMap[questionBank.toID] = questionBank.id;
        }
      }

      const questionIDs = await model.Questions.findAll({
        where: {
          [Op.or]: [
            {
              originalID: {
                [Op.in]: [...lookupQuestionIDs]
              }
            }
          ]
        },
        attributes: ['id', 'originalID'],
        raw: true,
        transaction
      });

      for (const questionID of questionIDs) {
        if (questionID.originalID) {
          // 远程题库接收方 有originalID
          questionMap[questionID.originalID] = questionID.id;
        }
      }

      // 远程题库发布方 无originalID
      const rawQuestions = await model.Questions.findAll({
        where: {
          id: {
            [Op.in]: [...lookupQuestionIDs]
          }
        },
        attributes: ['id'],
        raw: true,
        transaction
      });
      const rawQuestionIDs = rawQuestions.map(i => i.id);

      // 题目转换
      const trains = [];
      const records = [];
      for (const eliteTrain of allEliteTrains) {
        const { id, trainName, trainContent, trainTemplateName, trainTemplate, templateDifficulty } = eliteTrain;

        // 题目id转换
        for (const block of trainContent) {
          const { questionIDs } = block;

          const newQuestionIDs = [];
          for (const questionSet of questionIDs) {
            if (!questionSet || !questionSet.length || questionSet.length !== 2) {
              throw new Error(`精品试卷 ${id} 结构错误`);
            }
            const questionBankID = questionSet[0];
            const questionID = questionSet[1];

            const currentQuestionBankID = questionBankMap[questionBankID];

            if (!currentQuestionBankID) {
              throw new Error(`题库 ${questionBankID} 不存在`);
            }

            // 查找远程题库题目
            let currentQuestionID = questionMap[questionID];

            // 远程题库发布方 无originalID, 直接使用id
            if (!currentQuestionID) {
              const validQuestion = rawQuestionIDs.indexOf(questionID) !== -1;

              if (!validQuestion) {
                throw new Error(`题库 ${currentQuestionBankID} 题目 ${questionID} 不存在`);
              }

              currentQuestionID = questionID;
            }
          
            newQuestionIDs.push(currentQuestionID);
          }

          block.questionIDs = newQuestionIDs;
        }

        // 计算分数
        const totalScore = trainTemplate.map((i) => i.score * i.count).reduce((prev, cur) => prev + cur, 0);

        const currentYear = moment().format('YYYY');
        const currentMonth = moment().format('YYYYMM');
        const newYear = currentMonth > `${moment().format('YYYY')}07` ? `${currentYear}学年` : `${currentYear - 1}学年`;

        trains.push({
          year: year || newYear, // 默认导入当前学年
          series: seriesID,
          name: trainName,
          score: totalScore,
          createUserID: userID,
          isFinish: 1,
          content: trainContent,
          templateName: trainTemplateName,
          template: trainTemplate,
          templateDifficulty,
        });

        records.push({
          importSchoolSlug,
          importUserName,
          importDisplayName,
          importTrainID: id
        });
      }
       
      // 创建训练
      const result = await model.Train.bulkCreate(trains, {
        transaction
      });

      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: seriesID
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      const { trainIDs } = selectSeries;

      const newTrainIDs = trainIDs && trainIDs.length ? [...new Set([...trainIDs, ...result.map(row => row.id)])] : result.map(row => row.id);

      await model.TrainSeries.update({
        trainIDs: newTrainIDs
      }, {
        where: {
          id: seriesID
        },
        transaction
      });

      // 记录导入
      await app.mainModel.EliteTrainsRecord.bulkCreate(records, {});
    }

    async checkStatusToAddTrains(transaction, tranids = []) {

      // 取当前用户当前时间下的授权，并整理
      const permissionData = await ctx.service.systemConfig.getInfomationByKey('enableFunction');
      const permissionResult = permissionData ? permissionData.value : {};

      if (permissionResult.trainWebTime && permissionResult.trainWebTime.length) {
        for (const permissionRow of permissionResult.trainWebTime) {
          // 首先确认是否为在当前时间段内
          if (!permissionRow.endTime || moment().isAfter(permissionRow.endTime)) {
            continue;
          }
          if (!permissionRow.startTime || moment().isBefore(permissionRow.startTime)) {
            continue;
          }
  
          // 查看是否有在线训练权限
          if (permissionRow.permission && permissionRow.permission.indexOf('在线训练') !== -1) {
            // 判断是否为试用
            if (permissionRow.origin != '试用') {
              return 0;
            }
          }

          
        }
      }
  

      let tranidsArray = Array.isArray(tranids) ? tranids : [tranids];
      let count = tranidsArray.filter(item => item != null).length;
  
      const trains = await model.Train.findAll({
        where: {
          id: {
            [Op.notIn]: tranidsArray
          }
        },
        raw: true,
        transaction
      });

      count += trains.length;

      return count; // 返回 count 值
  }

    // 批量删除精品试卷
    async destoryEliteTrains(node, transaction) {
      const { trainIDs } = node;

      if (!trainIDs || !trainIDs.length) {
        throw new Error('试卷不存在');
      }

      await app.mainModel.EliteTrains.destroy({
        where: {
          id: {
            [Op.in]: trainIDs
          }
        },
        raw: true,
        transaction
      });
    }

    // 检查权限
    async checkPermission(type, name) {
      const { ctx } = this;
      const { model } = ctx;

      const permission = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true
      });

      if (!permission) {
        throw new Error('权限不存在');
      }

      const { value = {} } = permission;

      const permissionData = value[type];

      const acceptEnabledObject = permissionData ? permissionData.filter((row) => {
        const { startTime, endTime, permission = [] } = row;
        if (moment(startTime).isBefore(moment()) && moment(endTime).isAfter(moment()) && permission.indexOf(name) !== -1) {
          return true;
        }
    
        return false;
      }) : [];

      const acceptEnabled = acceptEnabledObject.length > 0;
      if (!acceptEnabled) {
        throw new Error('权限不足');
      }
    }

    async getTrainSeries(userID) {
      const { ctx } = this;
      const { model } = ctx;

      const series = await model.TrainSeries.findAll({
        attributes: ['id', 'name', 'createUserID', 'teachers', 'trainIDs', 'priority'],
        order: [['priority', 'DESC']],
        where: {
          [Op.or]: [
            { createUserID: userID },
            sequelize.literal(`
              json_contains(
                JSON_EXTRACT(
                  cast(teachers AS json),
                  '$'
                ),JSON_ARRAY(${userID})
              ) = 1
            `)
          ]  
        },
        include: [{
          model: model.User,
          as: 'createUser',
          attributes: ['name'],
        }],
        raw: true
      });

      // 循环各系列，获取相关试卷
      for(const serial of series) {
        //获取对应值
        const trains = await model.Train.findAll({
          attributes: ['id', 'name', 'year', 'templateName', 'template', 'isFinish',
          [sequelize.col('createUser.id'), 'createUserId'],
          [sequelize.col('createUser.name'), 'createUserName'],
          [sequelize.col('createUser.avatar'), 'createUserAvatar'], 'created_at', 'updated_at'],
          where: {
            id: serial.trainIDs
          },
          include: [{
            model: model.User,
            as: 'createUser',
            attributes: [],
          }],
          // raw: true
        });

        const trainMap = {};
        for (const train of trains) {
          const { id } = train;
          trainMap[id] = train;
        }

        serial.trains = serial.trainIDs.map(trainID => trainMap[trainID]);
        delete serial.trainIDs;
      }

      // 如果没有系列，则创建默认系列
      if (!series || !series.length) {
        const defaultSeries = await model.TrainSeries.create({
          name: '默认系列',
          priority: 0,
          createUserID: userID,
          teachers: null,
          trainIDs: []
        });

        return [defaultSeries];
      }

      return series;
    }

    async updateSeriesName(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const { seriesId, series } = node;

      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: seriesId
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      await model.TrainSeries.update({
        name: series
      }, {
        where: {
          id: seriesId
        },
        transaction
      });

      return true;
    }

    // 试卷系列分享教师ID集合
    async shareTrainSeries(currentUserID, trainSeriesID, teachers, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const updateCount = await model.TrainSeries.update({
        teachers
      }, {
        where: {
          id: trainSeriesID,
          createUserID: currentUserID
        },
        transaction
      });

      if(0 === updateCount) {
        throw new Error('您不是该试卷系列创建者或该试卷系列不存在！');
      }

      return true;
    }

    // 删除试卷系列分享教师ID集合中的当前用户ID
    async unshareTrainSeries(currentUserID, id, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      // 检查系列是否存在
      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      // 剔除当前教师
      const { teachers } = selectSeries;
      const newTeachers = teachers.filter(row => row !== currentUserID);
      await model.TrainSeries.update({
        teachers: newTeachers
      }, {
        where: {
          id
        },
        transaction
      });

      return true;
    }

    async createTrainSeries(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const { series, userID } = node;

      // 查重
      const selectSeries = await model.TrainSeries.findOne({
        where: {
          name: series,
          createUserID: userID
        },
        raw: true,
        transaction
      });

      if (selectSeries) {
        throw new Error('系列已存在');
      }

      await model.TrainSeries.create({
        name: series,
        priority: 0,
        createUserID: userID,
        trainIDs: []
      }, {
        transaction
      });

      return await model.TrainSeries.findAll({
        where: {
          createUserID: userID
        },
        raw: true,
        transaction
      });
    }

    async destoryTrainSeries(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const { seriesId, userID } = node;

      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: seriesId
        },  
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      // 检查系列下是否有试卷
      const { trainIDs } = selectSeries;

      if (trainIDs && trainIDs.length) {
        throw new Error('该系列下有试卷，无法删除');
      }

      // 检查是否最后一个系列，如果是则提示不能删除
      const series = await model.TrainSeries.findAll({
        where: {
          createUserID: userID
        },
        raw: true,
        transaction
      });

      if (series && series.length === 1) {
        throw new Error('至少保留一个系列');
      }

      await model.TrainSeries.destroy({
        where: {
          id: seriesId
        },
        transaction
      });

      return true;
    }

    async bulkUpdateTrainSeries(originSeriesId, series, addTrainIDs, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const selectSeries = await model.TrainSeries.findOne({
        where: {
          id: series
        },
        raw: true,
        transaction
      });

      if (!selectSeries) {
        throw new Error('系列不存在');
      }

      const { trainIDs = [] } = selectSeries;

      const newTrainIDs = [...new Set([...trainIDs, ...addTrainIDs])];

      await model.TrainSeries.update({
        trainIDs: newTrainIDs
      }, {
        where: {
          id: series
        },
        transaction
      });

      // 删除原系列下的试卷
      const originSeries = await model.TrainSeries.findOne({
        where: {
          id: originSeriesId
        },
        raw: true,
        transaction
      });

      if (!originSeries) {
        throw new Error('系列不存在');
      }

      const { trainIDs: originTrainIDs = [] } = originSeries;

      const newOriginTrainIDs = originTrainIDs.filter(i => !addTrainIDs.includes(i));

      await model.TrainSeries.update({
        trainIDs: newOriginTrainIDs
      }, {
        where: {
          id: originSeriesId
        },
        transaction
      });
    }

    async changeSeriesPriority(fromID, toID, userID, transaction) {
      const { ctx } = this;
      const { model } = ctx;

      const allSeries = await model.TrainSeries.findAll({
        attributes: ['id', 'priority'],
        order: [['priority', 'DESC']],
        where: {
          [Op.or]: [
            { createUserID: userID },
            sequelize.literal(`
              json_contains(
                JSON_EXTRACT(
                  cast(teachers AS json),
                  '$'
                ),JSON_ARRAY(${userID})
              ) = 1
            `)
          ]  
        },
        raw: true,
        transaction
      });

      const startIndex = allSeries.findIndex(i => i.id === fromID);
      const toIndex = allSeries.findIndex(i => i.id === toID);

      // 检查目标来源是否存在
      if (startIndex === -1) {
        throw new Error('发起系列不存在');
      }
 
      // 检查源来源是否存在
      if (toIndex === -1) {
        throw new Error('目标系列不存在');
      }

      // 将全部来源优先级按照数组索引排序批量修改优先级，数组索引越小优先级越高
      // 排序
      allSeries.splice(toIndex, 0, allSeries.splice(startIndex, 1)[0]);

      // 修改优先级
      const length = allSeries.length;
      const newSeries = allSeries.map((i, index) => {
        i.priority = length - index - 1;
        return i;
      });

      // 批量修改优先级
      await model.TrainSeries.bulkCreate(newSeries, {
        updateOnDuplicate: ['priority'],
        transaction
      });

      return await model.TrainSeries.findAll({
        attributes: ['id', 'name', 'priority'],
        order: [['priority', 'DESC']],
        where: {
          createUserID: userID,
        },
        raw: true,
        transaction
      });
    }

    // 训练系统恢复
    async initSystem(transaction) {
      const { ctx } = this;
      const { model, service } = ctx;

      // 1. 确认有删除标记，清除删除标记
      const deleteFlag = await model.SystemConfig.findOne({
        attributes: ['value'],
        where: {
          key: 'trainDeleted'
        },
        raw: true,
        transaction
      });

      if (!deleteFlag) {
        throw new Error('无法恢复未标记删除的训练系统');
      }

      if(deleteFlag.value !== 'true') {
        throw new Error('无法恢复未标记删除的训练系统');
      }
      
      // console.log(deleteFlag.value);

      // 清除删除标记
      await model.SystemConfig.update({
        value: 'false'
      }, {
        where: {
          key: 'trainDeleted'
        },
        transaction
      });

      // 2. 恢复数据库表结构
      // 删除时指令为：DROP TABLE train, train_plan, train_plan_class, train_questions, train_question_bank, train_question_correct, train_question_source, train_question_tag, train_series, train_tag, train_template, train_through_train_plan, train_user_question, train_user_record;
      const { Train, TrainPlan, TrainPlanClass, Questions, TrainQuestionBank, TrainQuestionCorrect, Tag, QuestionTag, TrainSeries, Template, TrainThroughTrainPlan, TrainUserQuestion, TrainUserRecord } = model;
      const tables = [
        TrainQuestionBank, Tag, Questions, QuestionTag, TrainQuestionCorrect, Template, TrainSeries, Train, TrainPlan, TrainPlanClass, TrainThroughTrainPlan, TrainUserQuestion, TrainUserRecord
      ];

      for (const table of tables) {
        await table.sync({ transaction });
      }

      // 3. 初始化表结构，初始化题库
      await model.Template.create({
        name: '江苏省普通高中学业水平合格性考试（信息技术）',
        content:  [
          { name: '单选题', type: ['单选题'], count: 25, score: 2 },
          { name: '操作题', type: ['编程填空题', 'WPS表格操作题', 'Access操作题'], count: 3, score: 10, limit: [2, 1, 1] },
          { name: '综合题', type: ['综合题'], count: 1, score: 20 },
        ],
        templateDifficulty: {"简单": "12", "较难": "2", "适中": "15"},
        duration: 60,
        score: 100,
      }, { transaction })

      const questionBankList = await service.thirdPart.getSchoolTrainQuestionBankList(transaction);

      for(const row of questionBankList) {
        await service.thirdPart.getOrCreateQuestionBank({ name: row.name, fromURL: `${row.id}`, needDelete: true }, transaction);
      }
    }

    // 训练系统清除
    async cleanSystem(transaction) {
      const { ctx } = this;
      const { model, service } = ctx;

      // 1. 增加或更新删除标记
      const deleteFlag = await model.SystemConfig.findOne({
        where: {
          key: 'trainDeleted'
        },
        raw: true,
        transaction
      });

      if(deleteFlag && deleteFlag.value === 'true') {
        return '已经标记删除';
      }

      // 2. 确认是否满足清除条件，学校train表中无数据
      const trainCount = await model.Train.count({
        raw: true,
        transaction
      });

      if (trainCount) {
        return '该校训练系统中有数据，不被清除';
      }

      // 3. 确认是否满足清除条件，当前或未来没有机房训练或在线训练记录
      const enableFunction = await model.SystemConfig.findOne({
        where: {
          key: 'enableFunction'
        },
        raw: true,
        transaction
      });

      let hasValidPermission = false;
      const now = new Date();

      if (enableFunction) {
        const { value } = enableFunction;
        if(value['trainPermissionData']) {
          const trainPermissions = value['trainPermissionData'];
          // 参考格式：[{"info": null, "origin": "试用", "status": "启用", "target": "在线训练", "endTime": "2023-03-01T06:21:20.525Z", "startTime": "2023-02-15T06:21:17.926Z", "trainPeople": 600}, {"info": {"teacherPC": 10}, "origin": "试用", "status": "启用", "target": "机房训练", "endTime": "2023-03-01T06:21:20.525Z", "startTime": "2023-02-15T06:21:17.926Z"}]
       
          for (const permission of trainPermissions) {
            const { target, startTime, endTime, status } = permission;
            if(status !== '启用') {
              continue;
            }
  
            // 不是在线训练或机房训练
            if(target !== '在线训练' && target !== '机房训练') {
              continue;
            }
  
            // 过期的授权跳过
            if(moment(endTime).isBefore(now)) {
              continue;
            }
  
            hasValidPermission = true;
            break;
          }
        }

      }

      if(hasValidPermission) {
        return '拥有有效授权，不被清除';
      }

      if (!deleteFlag) {
        await model.SystemConfig.create({
          key: 'trainDeleted',
          value: 'true'
        }, { transaction });
      } else {
        if(deleteFlag.value === 'true') {
          return '已经标记删除';
        }

        console.log(deleteFlag.value);
        await model.SystemConfig.update({
          value: 'true'
        }, {
          where: {
            key: 'trainDeleted'
          },
          transaction
        });
      }

      // 4. 执行SQL删除表结构
      try {
        await model.query('SET foreign_key_checks = 0', { transaction });
        await model.query('DROP TABLE train, train_plan, train_plan_class, train_questions, train_question_bank, train_question_correct, train_question_source, train_question_tag, train_series, train_tag, train_template, train_through_train_plan, train_user_question, train_user_record;', { transaction });
        await model.query('SET foreign_key_checks = 1', { transaction });
      }
      catch(e) {
        console.log(e);
      }
      // console.log(`DROP TABLE train, train_plan, train_plan_class, train_questions, train_question_bank, train_question_correct, train_question_, train_question_tag, train_series, train_tag, train_template, train_through_train_plan, train_user_question, train_user_record;`)

      // 5. 删除school目录中的train、trainPlan、trainQuestion
      // 生成学校基本路径
      const { schoolSlug } = ctx;
      const schoolPath = path.join(app.config.file.dir, schoolSlug);

      for (const dir of ['train', 'trainPlan', 'trainQuestion']) {
        const targetPath = path.join(schoolPath, dir);
        // console.log(targetPath);
        service.train.deleteSourceFile(targetPath);
      }

      return true;
    }
  }
  return TrainService;
}