const { Op, QueryTypes } = require('sequelize');
const fs = require('mz/fs');

const isTeamMember = (teams, userTeams) => {
  for(let each of userTeams){
    if(teams.indexOf(parseInt(each.teamID, 10)) !== -1 || teams.indexOf(`${each.teamID}`) !== -1){
      return true;
    }
  }

  return false;
}

module.exports = app => {
  
  class Directory extends app.Service {
    async checkLectureAuthDirectory(courseSlug, userID) {
      const { model } = this.ctx;
      // 查找courseID
      const courseData = await model.Course.findOne({
        where: { courseSlug, publish: 1 },
        attributes:['teams', 'containerInfo', 'teachers', 'createrID']
      });

      if (!courseData) {
        return false;
      }

      const userTeams = await model.TeamUser.findAll({
        where: { userID },
        attributes: ['teamID'],
      })

      if (courseData.teams && (isTeamMember(courseData.teams, userTeams) || courseData.teachers.indexOf(userID) !== -1) || parseInt(courseData.createrID, 10) === parseInt(userID, 10)) {
        return courseData.containerInfo;
      }

      return false;
    }

    async checkClassAuth(directoryID, userID) {
      const { model } = this.ctx;
      const { Directory, ClassUser } = model;
      // 获取对应班级
      const classList = await ClassUser.findAll({
        where: { userID },
        attributes: ['classID']
      });

      // 不存在章节返回空
      if (!classList || !classList) {
        return false;
      }

      // 获取课程ID
      const directoryDetail = await Directory.findOne({
        where: {
          id: directoryID
        },
        attributes: ['classCount']
      });
      

      // 不存在章节返回空
      if (!directoryDetail) {
        return false;
      }

      const classCount = directoryDetail.classCount ? JSON.parse(directoryDetail.classCount) : [];
      let ifExist = false;
      classList.map((data) => {
        if (ifExist) {
          return true;
        }

        if (classCount.indexOf(data.classID) !== -1) {
          ifExist = true;
        }

        return ifExist;
      })

      if (!ifExist) {
        return false;
      }

      return true;
    }

    // 查询是否是该课程的开放老师
    async checkCourseAuth(courseSlug, userID) {
      const { model } = this.ctx;
      // 查找courseID
      const courseData = await model.Course.findOne({
        where: { courseSlug, publish: 1 },
        attributes:['teachers', 'createrID']
      });

      if (!courseData) {
        return false;
      }

      if(courseData.createrID===userID || courseData.teachers && courseData.teachers.length &&courseData.teachers.includes(userID)) {
        return true
      }

      return false

    }

    // 后台查询是否有课程权限
    async adminCheckLectureAuthDirectory(courseSlug, userID) {
      const { model } = this.ctx;
      // 查找courseID
      const courseData = await model.Course.findOne({
        where: { courseSlug },
        attributes:['teachers', 'createrID']
      });

      if (!courseData) {
        return false;
      }

      // // courseData.teachers为空全部开放
      // if(!courseData || !courseData.teachers || !courseData.teachers.length){
      //   return true;
      // }
      
      if(courseData.createrID===userID || (courseData.teachers && courseData.teachers.length &&courseData.teachers.includes(userID)||courseData.teachers.includes(`${userID}`))) {
        return true
      }

      return false

    }
  }

  return Directory;
};
