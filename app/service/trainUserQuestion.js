const { Op } = require('sequelize');
const Sequelize = require('sequelize');

const getChildren = (rows, parentID) => {
  if(!rows || !rows.length) {
    return null
  }

  return rows.filter(row => row.parentID === parentID).map(row => {
    // console.log('row:',row)
    const children = getChildren(rows, row.id);
    if (children && children.length) {
      return {
        tagID: row.id,
        id: `${row.id}`,
        text: row.tagName,
        parentID: parentID,
        questionBankID: row.questionBankID,
        showChildren: true,
        children,
      }
    }

    return {
      tagID: row.id,
      id: `${row.id}`,
      text: row.tagName,
      parentID: parentID,
      questionBankID: row.questionBankID,
      children: [],
    }
  })
}

module.exports = app => {

  class TrainUserQuestionService extends app.Service {
    // 创建训练
    async createTrainUserQuestion(node, transaction) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model;

      const { trainPlanName } = node;

      // 删除重复加入的
      const ifHas = await TrainUserQuestion.findAll({
        where: {
          questionID: { [Op.in]: node.questionIDs },
          userID: node.userID ? node.userID : this.ctx.session.user.id,
          year: node.year,
        },
        raw: true,
        transaction,
      })

      let questionIDs = [];
      if (ifHas && ifHas.length) {
        for(const row of node.questionIDs) {
          const index = ifHas.findIndex(item => item.questionID === row);

          if (index !== -1) {
            continue;
          }

          questionIDs.push(row)
        }
      } else {
        questionIDs = node.questionIDs
      }

      const code = questionIDs.map(row => ({
        questionID: row,
        userID: node.userID ? node.userID : this.ctx.session.user.id,
        year: node.year,
        trainPlanName,
      }))

      return await TrainUserQuestion.bulkCreate(code, { transaction });
    }

    // 修改训练
    async putTrainUserQuestion(id, node, transaction) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model;
      await TrainUserQuestion.update(node, {
        where: {
          id
        },
        transaction
      });

      return this.ctx.service.trainUserQuestion.getTrainUserQuestion(id, transaction)
    }

    // 获取训练
    async getTrainUserQuestion(id, transaction = false) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model

      const result = await TrainUserQuestion.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      const tagData = await this.ctx.service.tag.getTrainTagList(id, transaction);
      // console.log('tagData:',tagData);
      result.nodes = getChildren(tagData.train, 0)

      return result 
    }

    // 删除训练
    async destoryTrainUserQuestion(questionID, transaction = false) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model

      return await TrainUserQuestion.destroy({
        where: {
          questionID,
          userID: this.ctx.session.user.id
        },
        transaction
      });
    }

    // 删除训练
    async destoryBulkTrainUserQuestion(questionIDs, transaction = false) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model;

      return await TrainUserQuestion.destroy({
        where: {
          questionID: { [Op.in]: questionIDs },
          userID: this.ctx.session.user.id
        },
        transaction
      });
    }

    // 删除训练
    async destoryMyTrainUserQuestion(transaction = false) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model;

      return await TrainUserQuestion.destroy({
        where: {
          // questionID: { [Op.in]: questionIDs },
          userID: this.ctx.session.user.id
        },
        transaction
      });
    }

    // 获取训练列表
    async getTrainUserQuestionList(node) {
      const { model } = this.ctx;
      const { TrainUserQuestion, Questions, QuestionTag } = model;
      let { questionType, trainsUseds, tagIDs, year, sources: trainPlanName } = node;

      const questionCondition = {};
      const condition = {
        userID: this.ctx.session.user.id
      };

      if(year) {
        condition.year = year;
      }

      if (trainPlanName && trainPlanName.length) {
        condition.trainPlanName = {
          [Op.in]: trainPlanName,
        };
      }

      // 题目类型
      if (questionType) {
        questionCondition.questionType = questionType;
      }

      if (trainsUseds && trainsUseds.length) {
        questionCondition[Op.or] = [];
        for(const trainsUsed of trainsUseds) {
          questionCondition[Op.or].push({
            trainsUsed: {
              [Op.or]: [
                { [Op.notLike]: `[%${trainsUsed}%]` },
                { [Op.notLike]: `[%${trainsUsed}%,` },
                { [Op.notLike]: `,%${trainsUsed}%]` },
                { [Op.notLike]: `,%${trainsUsed}%,` },
              ]
            }
          })
        }
      }

      if(tagIDs) {
        const questionsWithTag = await QuestionTag.findAll({
          where: { tagID: { [Op.in]: tagIDs } },
          raw: true,
        })
        
        questionCondition.id = { [Op.in]: questionsWithTag.map(row => row.questionID) }
      }

      const result = await TrainUserQuestion.findAll({
        where: condition,
        include: [{
          model: Questions,
          as: 'question',
          required: true,
          where: questionCondition,
        }],
      })

      return result;
    }

    async getTrainUserQuestionTrain(ids) {
      const { model } = this.ctx;
      const { Questions } = model;
      const allQuestions = await Questions.findAll({
        where: {
          id: { [Op.in]: ids }
        },
        raw: true,
      })

      const resultMaps = {}
      for(const row of allQuestions) {
        if(!resultMaps[row.questionType]) {
          resultMaps[row.questionType] = {
            questions: [],
            name: row.questionType,
          }
        }

        resultMaps[row.questionType].questions.push(row);
      }

      const results = [];
      for(const key in resultMaps) {
        if (!resultMaps[key] || !resultMaps[key].questions || !resultMaps[key].questions.length) {
          continue;
        }

        results.push(resultMaps[key])
      }

      return results;
    }

    async getTagChartData(node){
      const { model } = this.ctx;
      const questionRequest = { questionBankID: { [Op.not]: null } };
      if (node.questionType) {
        try {
          node.questionType = JSON.parse(node.questionType)
        } catch (e) {}
        // console.log('node.questionType:',node.questionType, typeof node.questionType)
        questionRequest.questionType = { [Op.in]: node.questionType };
      }

      const trainUserQuestionCondition = {};
      if (node.year) {
        trainUserQuestionCondition.year = node.year;
      }

      const allQuestionArr = await model.TrainUserQuestion.findAll({
        raw: true,
        where: {
          ...trainUserQuestionCondition,
          userID: this.ctx.session.user.id
        }
      });

      if (!allQuestionArr) {
        return []
      }
      // console.log('allQuestionArr:',allQuestionArr)

      questionRequest.id = { [Op.in]: allQuestionArr.map(row => row.questionID) }

      return await model.QuestionTag.count({
        include: [{
          model: model.Questions,
          attributes: ['id'],
          as: 'questions',
          where: questionRequest,
          required: true,
        }],
        attributes: ['tagID'],
        group: ['tagID'],
        raw: true,
      });
    }

    async getQuestionsCountByTagAndType(node) {
      const { model } = this.ctx;
      let { sources: trainPlanName } = node;
      trainPlanName = JSON.parse(trainPlanName);

      const questionRequest = {
      };

      questionRequest[Op.and] = []
      
      if (node.tagIDs) {
        let tags = node.tagIDs;
        try {
          tags = JSON.parse(tags);
        } catch (e) {}

        const tagResults = await model.QuestionTag.findAll({
          include: [{
            attributes: ['id'],
            model: model.Tag,
            as: 'tag',
            request: true,
          }],
          where: { tagID: {[Op.in]: tags} },
          raw: true,
          group: ['questionID'],
        });

        questionRequest[Op.and].push({ id: { [Op.in]: tagResults.map(row => row.questionID) } })
      }

      if (node.trainsUseds && node.trainsUseds.length) {
        questionRequest[Op.or] = [];
        for(const trainsUsed of node.trainsUseds) {
          questionRequest[Op.or].push({
            trainsUsed: {
              [Op.or]: [
                { [Op.notLike]: `[%${trainsUsed}%]` },
                { [Op.notLike]: `[%${trainsUsed}%,` },
                { [Op.notLike]: `,%${trainsUsed}%]` },
                { [Op.notLike]: `,%${trainsUsed}%,` },
              ]
            }
          })
        }
      }

      const trainUserQuestionRequest = {
        userID: this.ctx.session.user.id
      }

      if (node.year) {
        trainUserQuestionRequest.year = node.year
      }

      if (trainPlanName && trainPlanName.length) {
        trainUserQuestionRequest.trainPlanName = {
          [Op.in]: trainPlanName
        }
      }

      const allQuestionArr = await model.TrainUserQuestion.findAll({
        raw: true,
        where: trainUserQuestionRequest
      });

      if (!allQuestionArr) {
        return []
      }

      questionRequest[Op.and].push({ id: { [Op.in]: allQuestionArr.map(row => row.questionID) } })
      
      const result = await model.Questions.count({
        where: questionRequest,
        group: ['questionType']
      });

      return result;
    }

    async getQuestionSource(params) {
      const { model } = this.ctx;

      const { year, userID } = params;
      const condition = {
        userID
      };
      if (year) {
        condition.year = year;
      }

      const result = await model.TrainUserQuestion.findAll({
        where: condition,
        attributes: ['trainPlanName', [Sequelize.fn('count', Sequelize.col('id')), 'questionCount']],
        group: ['trainPlanName']
      });

      return result;
    }

    async getAllUserQuestions({ userID }) {
      const { model } = this.ctx;
      const { TrainUserQuestion } = model;

      return await TrainUserQuestion.findAll({
        where: {
          userID,
        },
        attributes: ['questionID'],
        raw: true,
      });
    }
  }

  return TrainUserQuestionService;
}