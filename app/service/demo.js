const { fs } = require('mz');
const md5 = require('md5');
const { getOJAnswer, stringifyXML, getStringByteLength, parseFPSXML, getOIQuestion } = require('../utils/fps');

function parseFileIndex(file, type) {
    const nameWithPositionParts = type === 'chapter' ? file.match(/^(\d+)\.\s*(.*)$/) : file.match(/^(\d+)\.\s*(.*)\.([^.]+)$/);

    if (nameWithPositionParts && nameWithPositionParts[1] && nameWithPositionParts[2]) {
        const index = nameWithPositionParts[1];
        const name = nameWithPositionParts[2];
        return { index, name, ext: nameWithPositionParts[3] ? nameWithPositionParts[3].toLowerCase() : null };
    }
}

module.exports = app => {

    class DemoService extends app.Service {
  
        async initDemoCourses(userID, transaction) {
            const { ctx } = this;
            // 从指定demo文件夹下获取上传课程基本信息
            const demoCoursesPath = `${app.config.file.demoDir}/demoCourses`;

            // 读之前检查目录是否存在
            const ifExist = await fs.exists(demoCoursesPath);

            if (!ifExist) {
                throw Error(`目录不存在${demoCoursesPath}`);
            }
            
            // 获取目录文件
            const courseList = await fs.readdir(demoCoursesPath);

            if (!courseList || !courseList.length) {
                throw Error(`目录下无课程文件夹${demoCoursesPath}`);
            }

            // 初始上传课程用户id默认为1
            const defaultUserID = userID || 1;

            try {
                // 逐个处理课程
                for (const course of courseList) {
                    await ctx.service.demo.uploadAndCreateCourse(demoCoursesPath, course, defaultUserID, transaction);
                }
            } catch(e) {
                throw new Error(e);
            }
        }

        // 加载课程配置
        async checkCourseInfo(courseConfigPath) {
            const courseConfigExist = fs.exists(courseConfigPath);
            if (!courseConfigExist) {
                return {
                  allowPaste: 1,
                  allowCopy: 1,
                  saveCode: 1,
                  saveRunResult: 1,
                  courseDescription: '',
                };
            }

            // 自文件加载课程配置
            let courseInfoContent = null;
            try {
                let courseInfoString = await fs.readFile(courseConfigPath, 'utf8');
                courseInfoContent = JSON.parse(courseInfoString);
            } catch (e) {
                throw new Error(`在读取 ${courseConfigPath} 时出现错误，${e.message}！`);
            }

            // 初始化课程信息
            let courseInfo = {
                allowPaste: true,
                allowCopy: true,
                saveCode: true,
                saveRunResult: true,
                courseDescription: '',
                ...courseInfoContent,
            };

            // 检查关键参数
            const { courseDescription } = courseInfo;

            if (courseDescription && courseDescription.length > 100) {
                throw new Error('课程简介长度不应超过100字符！');
            }

            // 转换为1、0形式
            ['allowCopy', 'allowPaste', 'saveCode', 'saveRunResult'].forEach((field) => {
                courseInfo[field] = courseInfo[field] ? 1 : 0;
            });

            return courseInfo;
        }

        // 处理每节课的文件
        async uploadAndCreateCourse(demoCoursesPath, course, defaultUserID, transaction) {
            const { ctx } = this;

            // 每节课的文件
            const sourceCourseDir = `${demoCoursesPath}/${course}`;
            const chapterDirs = await fs.readdir(`${sourceCourseDir}`);

            // 1. 读取课程信息配置文件
            const courseConfigPath = `${sourceCourseDir}/course.json`;
            const courseInfo = await ctx.service.demo.checkCourseInfo(courseConfigPath);
            const { courseSlug } = courseInfo;

            courseInfo.createrID = defaultUserID; // 初始上传课程用户id默认为1

            // 2. 上传课程包
            let indics = null;
            try {
                // 创建课程文件夹
                const targetCourseDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}`;
                const courseExist = await fs.exists(targetCourseDir);
                if (!courseExist) {
                    fs.mkdir(targetCourseDir, { recursive: true });
                }

                // 创建课程基本信息文件
                fs.createReadStream(courseConfigPath).pipe(fs.createWriteStream(`${targetCourseDir}/course.json`));

                // 创建章节文件
                indics = await ctx.service.demo.uploadChapterAndGetIndics(chapterDirs, courseInfo, sourceCourseDir, targetCourseDir);
            } catch (err) {
                console.error(err);
                return;
            }

            // 3. 写库 admin/createCourseSections
            const { jupyter } = app.config;
            courseInfo.indics = indics;
            courseInfo.publish = 1;
            courseInfo.statist = { chapters: indics.length, students: 0 };
            courseInfo.teachers = [courseInfo.createrID];
            courseInfo.teams = [];
            courseInfo.courseType = '必修课';
            courseInfo.containerInfo = {
                image: jupyter.image,
                cpuLimit: jupyter.cpuRequest,
                memoryRequest: jupyter.memoryRequest,
            },

            await ctx.service.course.createCourseSections(courseInfo, transaction, defaultUserID);
        }

        // 获取课程章节目录结构并上传文件
        async uploadChapterAndGetIndics(chapterDirs, courseInfo, sourceCourseDir, targetCourseDir) {
            const { ctx } = this;
            // 入参检查
            if (!chapterDirs || !chapterDirs.length) {
                return;
            }

            const { courseSlug } = courseInfo;
            // 读取章文件
            const indics = [];
            for (const chapter of chapterDirs) {
                const sourceChapterDir = `${sourceCourseDir}/${chapter}`;
                const fileInfo = await fs.stat(sourceChapterDir);

                // 处理课程章节文件
                if (fileInfo.isDirectory()) {
                    if (chapter !== 'assets' && chapter !== 'input') {
                        //  2.1 上传章节实体和判决数据文件 admin/file/uploadChapterFile
                        const chapterInfo = await ctx.service.demo.handleChapter(chapter, sourceChapterDir, targetCourseDir, courseInfo);
                        indics.push(chapterInfo);
                    }

                    // 2.2 上传共享文件 /admin/file/uploadSharedFile
                    // tips: oj课的共享文件上传由课程base64内容转换
                    if (chapter === 'assets' || chapter === 'input') {
                        const hashPath = `${sourceCourseDir}/${chapter}`;
                        const ifExist = await fs.exists(hashPath);

                        if (ifExist) {
                            const hashFiles = await fs.readdir(hashPath);

                            const uploadHashFiles = [];
                            for (const hashFile of hashFiles) {
                                uploadHashFiles.push({ fullName: hashFile });  
                            }

                            // 批量上传共享文件并写库
                            await ctx.service.file.bulkUploadHashFile({ hashPath, uploadHashFiles, courseSlug, fileType: chapter, isCover: 0 });
                        }
                        
                    }
                }
            }

            return indics;
        }

        // 处理章节文件
        async handleChapter(chapter, sourceChapterDir, targetCourseDir, courseInfo) {
            const { ctx } = this;
            const { chapterNameMap = {}, courseSlug } = courseInfo; // 章节名称与文件夹名称映射
            // 解析文件名开头的序号
            const chapterInfo = { chapterName: '', chapterIndex: 1, chapterTitle: '', sections: [] };
            const parseResult = parseFileIndex(chapter, 'chapter');

            let targetChapterDir = null;
            if (parseResult) {
                // 记录indics
                chapterInfo.chapterName = parseResult.name;
                chapterInfo.chapterIndex = parseInt(parseResult.index, 10);
                chapterInfo.chapterTitle = chapterNameMap[parseResult.name] || parseResult.name;

                // 创建章节文件夹
                targetChapterDir = `${targetCourseDir}/${parseResult.name}`;
                const chapterExist = await fs.exists(targetChapterDir);
                if (!chapterExist) {
                    fs.mkdir(targetChapterDir, { recursive: true });
                }
            }

            // 章文件列表
            const sectionFiles = await fs.readdir(`${sourceChapterDir}`);
            for (const section of sectionFiles) {
                // 解析文件序号、文件名、后缀名
                const parseSectionResult = parseFileIndex(section);
                const { ext } = parseSectionResult || {};

                // 如果是章节模式下，仅允许上传ipynb和xml
                if (['xml', 'ipynb', 'json'].indexOf(ext) === -1) {
                    console.error('不是.ipynb或.xml格式文件，请检查！');
                    continue;
                }

                // 从文件读取课程类型和课程显示标题
                let sectionType = 'AI';
                let sectionTitle = null;
                const fileContent = await fs.readFile(`${sourceChapterDir}/${section}`, 'utf-8');

                if (ext === 'xml') {
                    // 继续检查XML格式
                    if (!fileContent.match(/\<fps version="1.2" url="https:\/\/github.com\/zhblue\/freeproblemset\/"\>/)) {
                        sectionType = 'OI';
                        const { title } = getOIQuestion(fileContent, true);
                        sectionTitle = title;
                    } else {
                        const { title } = getOJAnswer(fileContent);
                        sectionTitle = title;
                        sectionType = 'OJ'; 
                    }
                } else if (ext === 'json') {
                    // 检查课程类型
                    const { type, title } = JSON.parse(fileContent);
                    sectionTitle = title;

                    if (type === 'Excel') {
                        sectionType = 'Excel';
                    } else {
                        sectionType = 'Access';
                    }
                } else {
                    const { metadata } = JSON.parse(fileContent);
                    const { title } = metadata;
                    sectionTitle = title;
                    sectionType = 'AI';
                }

                
                if (parseSectionResult) {
                    // 记录indics
                    chapterInfo.sections.push({
                        sectionName: parseSectionResult.name,
                        sectionIndex: parseInt(parseSectionResult.index, 10),
                        sectionTitle: sectionTitle || parseSectionResult.name,
                        ext,
                        sectionType,
                        status: true,
                    });

                    // 上传课程文件
                    const sourceSectionPath = `${sourceChapterDir}/${section}`;
                    const targetSectionPath = `${targetChapterDir}/${parseSectionResult.name}.${ext}`;

                    if (sectionType === 'OJ') {
                        // 处理判决数据
                        await ctx.service.demo.parseFPSXMLFile({ fileContent, chapterName: chapterInfo.chapterName, sectionName: parseSectionResult.name, courseSlug, fileName: `${parseSectionResult.name}.${ext}` });
                    } else {
                        fs.createReadStream(sourceSectionPath).pipe(fs.createWriteStream(targetSectionPath));
                    }
                }
            }

            return chapterInfo;
        }

        // 解析判决数据
        async parseFPSXMLFile({ fileContent, chapterName, sectionName, courseSlug, fileName }) {
            const { ctx } = this;
            // 解析XML格式
            const { xmlContent, spjJudgers, testInputs, testOutputs, sourceFiles } = await parseFPSXML(fileContent);
            // 所有需要上传的hash内容
            const uploadParts = [];
            const uploadPart$ = [];

            // 整理xml和判决数据
            for (const spjJudger of spjJudgers) {
                const { code, $ } = spjJudger;
                const hash = md5(code);
                uploadPart$.push($);
                uploadParts.push({ size: getStringByteLength(code), hash, fileName: $.name, content: code });
            }

            let testInputIndex = 1;
            for (const testInput of testInputs) {
                const { content, $ } = testInput;

                const hash = md5(content);
                uploadPart$.push($);
                uploadParts.push({ size: getStringByteLength(content), hash, fileName: `${testInputIndex++}.in`, content });
            }

            let testOutputIndex = 1;
            for (const testOutput of testOutputs) {
                const { content, $ } = testOutput;

                const hash = md5(content);
                uploadPart$.push($);
                uploadParts.push({ size: getStringByteLength(content), hash, fileName: `${testOutputIndex++}.out`, content });
            }

            for (const sourceFile of sourceFiles) {
                const { content, $ } = sourceFile;
                const hash = md5(content);
                uploadPart$.push($);
                uploadParts.push({ size: getStringByteLength(content), hash, fileName: $.name, content });
            }

            // 上传判决数据 api/admin/file/bulkUploadHashJudgeFile
            const paths = await ctx.service.demo.handleJudgeFiles(uploadParts);

            // 回写路径
            for (let i = 0; i < paths.length; i++) {
              const path = paths[i];
              uploadPart$[i].src = path;
            }

            // 生成XML字符串内容
            const stripedXMLString = stringifyXML(xmlContent);

            // 上传xml文件 content, fileName, courseSlug, chapterName, sectionName
            await ctx.service.file.uploadChapterFPSXMLContent(stripedXMLString, fileName, courseSlug, chapterName, sectionName);
        }

        // 上传判决数据
        async handleJudgeFiles(files) {
            const { ctx } = this;
            const { mainModel } = app;

            // 接受返回数据
            let result = [];
            let mainTransaction = null;
      
            try {
              // 启用事务
              mainTransaction = await mainModel.transaction({autocommit: false});

             // 批量上传判决数据文件
              result =  await ctx.service.file.bulkUploadHashJudgeFile(files, mainTransaction);
              
              await mainTransaction.commit();
            } catch (err) {
              if(mainTransaction) {
                await mainTransaction.rollback();
              }
      
              throw new Error(err);
            }

            return result;
        }
    }   
    return DemoService;
}