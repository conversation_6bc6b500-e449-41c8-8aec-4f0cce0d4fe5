const moment = require('moment');


module.exports = app => {
    class Authority extends app.Service {

        async getTrainAuth(transaction = null) {
            const { ctx } = this;
            const { model } = ctx;
            const configs = await model.SystemConfig.findOne({
                where: {
                    key: 'enableFunction'
                },
                raw: true,
                transaction
            });

            if (!configs) {
                throw new Error('没有授权配置')
            }

            const { value: permissionData = {} } = configs;

            let leftPCEndTime = -1;
            let leftWebEndTime = -1;

            const { trainPermissionData = [] } = permissionData;
    
            // 获取训练板块授权期限
            for (const node of trainPermissionData) {
                // 计算日期差时，过期当天和过期一天，算时间差值时，都会算作一天，该处注意去除
                if (moment(node.endTime).endOf('days').isBefore(moment())) {
                    continue;
                }
                if (node.status === '启用' && node.endTime && moment().isBefore(node.endTime) && (node.target === '机房训练' || node.target === '在线训练')) {
                    const dayDiff = moment(node.endTime).endOf('days').diff(moment().startOf('days'), 'day');
                    if (node.target === '机房训练' && dayDiff > leftPCEndTime) {
                        leftPCEndTime = dayDiff;
                    }
                    if (node.target === '在线训练' && dayDiff > leftWebEndTime) {
                        leftWebEndTime = dayDiff;
                    }
                }
            }

            let pcTrainDisable = leftPCEndTime === -1;
            let onlineTrainDisable = leftWebEndTime === -1;

            return {
                pcTrainDisable,
                onlineTrainDisable,
            }
        }
    }

    return Authority;
}