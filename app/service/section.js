const sequelize = require('sequelize');
const moment  = require('moment');
const fs = require('fs');
const md5 = require('md5');
const AdmZip = require("adm-zip");
const { Op } = sequelize;
const { getOJAnswer, getOIQuestion, changeToOI, changeToOJ } = require('../utils/fps');
const { handleTrimStr } = require('../utils/tool');

function handleRenameHistoryTitle(renameArgs) {
  const { sectionType, newSectionTitle, fileContent } = renameArgs;

  let newFileContent;

  switch (sectionType) {
    case 'AI':
      {
        const newFile = JSON.parse(fileContent);
        newFile.metadata.title = newSectionTitle;
        newFileContent = JSON.stringify(newFile);
      }
      break;
    case 'OI':
      const oldOI = getOIQuestion(fileContent, true);
      const questionOI = { ...oldOI, title: newSectionTitle };
      questionOI.questionList = questionOI.questions;
      newFileContent = changeToOI(questionOI, questionOI.ifShowAnswer, questionOI.ifSubmitLimit, questionOI.submitLimit);
      break;
    case 'OJ':
      const oldOJ = getOJAnswer(fileContent);
      const questionOJ = { ...oldOJ, title: newSectionTitle };
      newFileContent = changeToOJ(questionOJ);
      break;
    case 'Access':
    case 'Excel':
    case 'Scratch':
    case 'MicroBit':
    case 'CodeBlank':
      {
        const newFile = JSON.parse(fileContent);
        newFile.title = newSectionTitle;
        newFileContent = JSON.stringify(newFile);
      }
      break;
    default:
      throw new Error(`未识别的类型${sectionType}`);
  }
  return newFileContent;
}

function handleCompressFile(compressArgs) {
  const { type, sectionType, fileContentMap, fileContent, version, newSectionTitle, ext } = compressArgs;

  switch (type) {
    case 'add':
      {
        // 将新的课程文件添加进压缩文件
        fileContentMap[`${version}.${ext}`] = fileContent;
      }
      break;
    case 'update':
      {
        // 更新最新一条草稿版本的压缩文件
        if (!fileContentMap[`${version}.${ext}`]) {
          throw new Error(`未查询到版本${version}的文件！`)
        }
        fileContentMap[`${version}.${ext}`] = fileContent;
      }
      break;
    case 'renameTitle':
      {
        // 重命名课程，更新最新一条历史文件的课程名称字段
        if (!fileContentMap[`${version}.${ext}`]) {
          throw new Error(`未查询到历史文件！`)
        }

        const renameArgs = { version, sectionType, newSectionTitle, fileContent: fileContentMap[`${version}.${ext}`] };
        const newFileContent = handleRenameHistoryTitle(renameArgs);
        fileContentMap[`${version}.${ext}`] = newFileContent;
      }
      break;
    default:
      console.error(`未知的存储类型${type}`);
      break;
  }
  return fileContentMap;
}

module.exports = app => {
  class SectionService extends app.Service {
    async getAiProgressByUser(sectionID, userID, transaction) {
      const { model } = this.ctx;

      const sectionRecord = await model.SectionRecord.findOne({
        where: {
          userID,
          sectionID: sectionID,
        },
        attributes: ['sectionID', 'record', 'passCount'],
        transaction
      });

      if (!sectionRecord) {
        return { sectionID: sectionID, record: null }
      }

      return sectionRecord;
    }

    async createRecord(userID, state, record, transaction) {
      const { model } = this.ctx;
      const { courseSlug, chapterName, sectionName } = state;

      // 获取课程信息
      const course = await model.Course.findOne({
        where: {
          courseSlug
        },
        attributes: ['id'],
        transaction
      });

      if (!course) {
        throw new Error('目前查找不到相关课程')
      }
      // 课程ID
      const courseID = course.id;

      // 获取sectionID
      const section = await model.Section.findOne({
        where: {
          courseID,
          chapterName,
          sectionName
        },
        attributes: ['id'],
        transaction
      });

      if (!section) {
        throw new Error('目前查找不到相关课程章节')
      }

      return await model.SectionRecord.create({
        userID,
        sectionID: section.id,
        record
      }, { transaction });
    }

    async updateRecord(userID, state, record, transaction) {
      const { model } = this.ctx;
      const { courseSlug, chapterName, sectionName } = state;

      // 获取课程信息
      const course = await model.Course.findOne({
        where: {
          courseSlug
        },
        attributes: ['id'],
        transaction
      });

      if (!course) {
        throw new Error('目前查找不到相关课程')
      }
      // 课程ID
      const courseID = course.id;

      // 获取sectionID
      const section = await model.Section.findOne({
        where: {
          courseID,
          chapterName,
          sectionName
        },
        attributes: ['id'],
        transaction
      });

      if (!section) {
        throw new Error('目前查找不到相关课程章节')
      }

      // 获取当前答题记录 
      const sectionRecord = await model.SectionRecord.findOne({
        where: {
          userID,
          sectionID: section.id
        },
        attributes: ['record'],
        transaction
      });

      // 更新
      return await model.SectionRecord.update({
        record: {
          ...sectionRecord.dataValues.record,
          ...record
        }
      }, {
        where: {
          userID,
          sectionID: section.id
        },
        transaction
      });
    }

    async getSectionList(courseSlug, chapterName) {
      const { model } = this.ctx;

      // 查询课程缓存
      const course = await model.Course.findOne({
        where: {
          courseSlug
        },
        attributes: ['id', 'indics']
      });

      if (!course) {
        throw new Error('目前查找不到相关课程')
      }
      const { indics } = course;

      // 从课程缓存中查询当前课节信息
      const currentChapter = indics.find(chapter => chapter.chapterName === chapterName);
      if(!currentChapter) {
        throw new Error(`未查询到章节${chapterName}`);
      }
      
      // 获取当前章所有的节列表
      return currentChapter.sections; // 如果章节没有课程，返回[]
    }

    async getSection(courseSlug, chapterName, sectionName, transaction = false) {
      const { model } = this.ctx;

      // 获取课程信息
      const course = await model.Course.findOne({
        where: {
          courseSlug
        },
        attributes: ['id'],
        transaction
      });

      if (!course) {
        throw new Error('目前查找不到相关课程')
      }
      // 课程ID
      const courseID = course.id;

      return await model.Section.findOne({
        where: {
          courseID,
          chapterName,
          sectionName
        },
        attributes: ['id', 'sectionName', 'sectionType', 'record'],
        transaction
      });
    }

    async getSectionRecordList(userIDs, sectionIDs) {
      const { model } = this.ctx;

      return await model.SectionRecord.findAll({
        where: {
          userID: { [Op.in]: userIDs },
          sectionID: { [Op.in]: sectionIDs },
          record: { [Op.not]: null }
        },
        attributes: [
          'userID',
          'sectionID',
          'record',
          'updated_at',
          // [sequelize.fn('JSON_EXTRACT', sequelize.col('record'), '$.score'), 'score'],
          // [sequelize.fn('JSON_EXTRACT', sequelize.col('record'), '$.passCount'), 'passCount'],
          // [sequelize.fn('JSON_EXTRACT', sequelize.col('record'), '$.codeCount'), 'codeCount']
          'passCount',
          'totalScore'
        ],
        raw: true
      });
    }

    async getRecordData(userID, sectionID, codeIndex) {
      const { model } = this.ctx;

      return await model.SectionRecord.findOne({
        where: {
          userID,
          sectionID,
        },
        attributes: [
          [sequelize.fn('JSON_EXTRACT', sequelize.col('record'), `$."${codeIndex}"`), 'result']
        ],
        raw: true
      });
    }

    async getRecordMessage(userIDs, sectionID) {
      const { model } = this.ctx;

      return await model.SectionRecord.findAll({
        where: {
          userID: { [Op.in]: userIDs },
          sectionID,
        },
        attributes: [
          'userID',
          'sectionID',
          'record',
          'stayTime',
          'updated_at'
        ]
      });
    }

    async recordAIProgress(sectionID, userID, record, totalScore, passCount, transaction) {
      const { model } = this.ctx;
      const sectionRecord = await model.SectionRecord.findOne({
        where: {
          userID,
          sectionID,
        },
        attributes: ['id'],
        transaction
      });

      if (!sectionRecord) {
        await model.SectionRecord.create({
          userID,
          sectionID,
          record, totalScore, passCount
        }, { transaction });
      } else {
        await model.SectionRecord.update({
          record, totalScore, passCount
        }, {
          where: {
            userID,
            sectionID,
          },
          transaction
        });
      }

    }

    async sectionStatisResultAI(sectionID, teamID, content) {
      const { model } = this.ctx;
      const { SectionRecord, TeamUser } = model;

      // 计算有多少代码块，不计算markdown
      const UUIDList = [];
      const { cells } = content;
      if (cells && cells.length) {
        cells.forEach(cell => {
          if (cell['cell_type'] === 'code') {
            UUIDList.push(cell.metadata.UUID);
          }
        })
      }

      // 根据teamID 获取userID
      const users = await TeamUser.findAll({
        where: { teamID },
        attributes: ['userID']
      })
      const userIDs = users.map(i => i.userID);
      
      // 查询提交记录
      const records = await SectionRecord.findAll({
        where: { userID: { [Op.in]: userIDs }, sectionID: sectionID },
        attributes: ['record', 'userID']
      });
      const statisResult = {};
      
      // 根据代码块初始化
      for (let i of UUIDList) {
        statisResult[i] = { 'notPass': [...userIDs], 'pass': [], 'error': [] }
      }

      for (const record of records) {
        const { record: rd, userID } = record;
        const UUIDsMap = rd && rd.UUIDsMap ? rd && rd.UUIDsMap : {}
        for (const key in UUIDsMap) {
          if (key === 'isProjectBook') {
            continue;
          }
          if (!statisResult[key]) {
            statisResult[key] = { 'notPass': [...userIDs], 'pass': [], 'error': [] }
          }

          if (!UUIDsMap[key] || !UUIDsMap[key].type) {
            continue;
          }

          const { type } = UUIDsMap[key];
          if (type === 'success') {
            statisResult[key].pass.push(parseInt(userID));
            // 删除未通过中的下标
            const index = statisResult[key].notPass.findIndex(i => parseInt(i, 10) === parseInt(userID));
            if (index > -1) {
              statisResult[key].notPass.splice(index, 1);
            }
          }

          if (type === 'error') {
            statisResult[key].error.push(parseInt(userID));
            const index = statisResult[key].notPass.findIndex(i => parseInt(i, 10) === parseInt(userID));
            if (index > -1) {
              statisResult[key].notPass.splice(index, 1);
            }
          }
        }
      }


      return { statisResult, students: users.length };
    }

    // OJ题目数据统计
    async sectionStatisResultOJ(sectionID, teamID) {
      const { model } = this.ctx;
      const { SectionRecord, TeamUser } = model

      // 根据teamID 获取userID
      const users = await TeamUser.findAll({
        where: { teamID },
        attributes: ['userID']
      })
      const userIDs = users.map(i => i.userID);
      
      // 查询提交记录
      const records = await SectionRecord.findAll({
        where: { userID: { [Op.in]: userIDs }, sectionID: sectionID },
        attributes: ['record', 'userID', 'totalScore', 'passCount']
      });

      const statisResult = { 'notPass': [...userIDs], 'pass': [], 'error': [] };
      for (const record of records) {
        const { record: red, userID, totalScore, passCount } = record;
        if (red) {
          // const { score } = red;
          const score = (passCount / totalScore) * 100;
          // 未通过
          if ((score || score === 0) && score < 100) {
            statisResult.error.push(parseInt(userID));
            // 删除未通过中的下标
            const index = statisResult.notPass.findIndex(i => parseInt(i, 10) === parseInt(userID));
            if (index > -1) {
              statisResult.notPass.splice(index, 1);
            }
          }
          if (score >= 100) {
            statisResult.pass.push(parseInt(userID));
            // 删除未通过中的下标
            const index = statisResult.notPass.findIndex(i => parseInt(i, 10) === parseInt(userID));
            if (index > -1) {
              statisResult.notPass.splice(index, 1);
            }
          }
        }

      }

      return { statisResult, students: users.length };
    }

    async sectionStatisResultOI(sectionID, teamID, content) {
      const { model } = this.ctx;
      const { SectionRecord, TeamUser } = model

      // 根据teamID 获取userID
      const users = await TeamUser.findAll({
        where: { teamID },
        attributes: ['userID']
      })
      const userIDs = users.map(i => i.userID);
      
      // 查询提交记录
      const records = await SectionRecord.findAll({
        where: { userID: { [Op.in]: userIDs }, sectionID: sectionID },
        attributes: ['record', 'userID']
      });

      const statisResult = {};
      content.forEach((contentValue, i) => {
        const { UUID, questionType } = contentValue;

        // 仅获取题目结果
        if (questionType !== '文本') {
          if (!statisResult[UUID]) {
            statisResult[UUID] = { 'notPass': new Set(userIDs), 'pass': new Set(), 'error': new Set() };
          }
        }
      });
      
      for (const record of records) {
        const { record: userRecord, userID } = record;

        if (!userRecord) {
          continue;
        }
        
        const { submitTimes, ...studentRecords } = userRecord;
        for (const UUID in studentRecords) {
          const userData = userRecord[UUID];

          let { pass = new Set(userIDs), error = new Set(), notPass = new Set() } = statisResult[UUID] || {};

          // 通过用户是否有answer字段判断是否已答题
          if (userData.answer) {
            if (userData.status) {
              pass.add(userID);
            } else {
              error.add(userID);
            }

            // 删除未通过中的下标
            if (notPass.has(userID)) {
              notPass.delete(userID);
            }
          }
        }
      }

      // 集合转为数组
      for (const UUID in statisResult) { 
          statisResult[UUID].pass = [...statisResult[UUID].pass];
          statisResult[UUID].error = [...statisResult[UUID].error];
          statisResult[UUID].notPass = [...statisResult[UUID].notPass];
      }

      return { statisResult, students: users.length };
    }

    async sectionStatisResultScratch(sectionID, teamID, content) {
      const { model } = this.ctx;
      const { SectionRecord, TeamUser } = model;

      // 计算有多少代码块，不计算markdown
      const UUIDList = [];
      const { judgeSteps } = content;
      if (judgeSteps && judgeSteps.length) {
        judgeSteps.forEach(step => {
          UUIDList.push(step.uuid);
        });
      }

      // 根据teamID 获取userID
      const users = await TeamUser.findAll({
        where: { teamID },
        attributes: ['userID']
      })
      const userIDs = users.map(i => i.userID);
      
      // 查询提交记录
      const records = await SectionRecord.findAll({
        where: { userID: { [Op.in]: userIDs }, sectionID: sectionID },
        attributes: ['record', 'userID']
      });
      const statisResult = {};
      
      // 根据代码块初始化
      for (let i of UUIDList) {
        statisResult[i] = { 'notPass': [...userIDs], 'pass': [], 'error': [] }
      }

      for (const record of records) {
        const { record: rd, userID } = record;
        const { judgeSteps } = rd || {};
        if (!judgeSteps || !judgeSteps.length) {
          continue;
        }
        for (const step of judgeSteps) {
          if (!step.pass) {
            statisResult[step.uuid] = { 'notPass': [...userIDs], 'pass': [], 'error': [] }
          }

          if (step.pass) {
            statisResult[step.uuid].pass.push(parseInt(userID));
            // 删除未通过中的下标
            const index = statisResult[step.uuid].notPass.findIndex(i => parseInt(i, 10) === parseInt(userID));
            if (index > -1) {
              statisResult[step.uuid].notPass.splice(index, 1);
            }
          }
          
          // else {
          //   statisResult[step.uuid].error.push(parseInt(userID));
          //   const index = statisResult[step.uuid].notPass.findIndex(i => parseInt(i, 10) === parseInt(userID));
          //   if (index > -1) {
          //     statisResult[step.uuid].notPass.splice(index, 1);
          //   }
          // }
        }
      }


      return { statisResult, students: users.length };
    }

    async sectionStatisResultCodeBlank(sectionID, teamID, content) {
      const { model } = this.ctx;
      const { SectionRecord, TeamUser } = model

      // 根据teamID 获取userID
      const users = await TeamUser.findAll({
        where: { teamID },
        attributes: ['userID']
      })
      const userIDs = users.map(i => i.userID);
      
      // 查询提交记录
      const records = await SectionRecord.findAll({
        where: { userID: { [Op.in]: userIDs }, sectionID: sectionID },
        raw: true,
        attributes: ['record', 'userID']
      });

      const statisResult = {};
      const { questions } = content;
      const question = questions[0];
      if (!question) {
        throw new Error('未获取编程题内容')
      }

      const { answerKeys } = question;

      answerKeys.forEach((key) => {
        // 仅获取题目结果
        if (!statisResult[key]) {
          statisResult[key] = { 'notPass': new Set(userIDs), 'pass': new Set(), 'error': new Set() };
        }
      });
      
      for (const record of records) {
        const { record: userRecord, userID } = record;

        if (!userRecord) {
          continue;
        }
        
        const { submitTimes, ...studentRecords } = userRecord;
        for (const key in studentRecords) {
          const userData = userRecord[key];

          let { pass = new Set(userIDs), error = new Set(), notPass = new Set() } = statisResult[key] || {};

          // 通过用户是否有answer字段判断是否已答题
          if (userData.studentAnswer) {
            if (userData.status) {
              pass.add(userID);
            } else {
              error.add(userID);
            }

            // 删除未通过中的下标
            if (notPass.has(userID)) {
              notPass.delete(userID);
            }
          }
        }
      }

      // 集合转为数组
      for (const key in statisResult) { 
          statisResult[key].pass = [...statisResult[key].pass];
          statisResult[key].error = [...statisResult[key].error];
          statisResult[key].notPass = [...statisResult[key].notPass];
      }

      return { statisResult, students: users.length };
    }

    async querySectionRecord(courseSlug, chapterName, sectionName) {
      const { model } = this.ctx;
      const { Course, Section } = model;

      const course = await Course.findOne({
        where: { courseSlug },
        attributes: ['id']
      });
      if (!course) {
        throw Error('当前课程不存在！');
      }

      const section = await Section.findOne({
        where: {
          courseID: course.id,
          chapterName,
          sectionName
        },
        attributes: ['record'],
      })

      if (!section) {
        throw Error('当前节不存在！')
      }
      return section.record;
    }

    // 记录AI课测提交记录
    async recordSubmit(sectionID, codeIndex, isPass) {
      const { model } = this.ctx;
      const { Section } = model;

      // 启用事务
      let transaction = null;

      try {
        transaction = await model.transaction({ autocommit: false });
        // 查记录
        const result = await Section.findOne({
          where: {
            id: sectionID
          },
          raw: true,
          attributes: ['record'],
          transaction
        })

        if (!result) {
          throw Error('当前节不存在！');
        }
        let { record } = result;
        if (!record) {
          record = {}
        }
        if (!record[codeIndex]) {
          record[codeIndex] = { 'submit': 1, 'pass': isPass ? 1 : 0 }
        } else {
          let { submit, pass, project } = record[codeIndex];
          submit++;
          if (isPass) {
            pass++
          }
          record[codeIndex] = { submit, pass, project }
        }

        await Section.update({
          record
        }, {
          where: {
            id: sectionID
          },
          transaction
        })

        await transaction.commit();

      }catch(e){
        console.log(e, 'UPDATE courseNum')
        await transaction.rollback();
        return;
      }
     
    }

    async changeInternel(courseSlug, chapterName, sectionName, checked, transaction) {
      const { model } = this.ctx;
      const { Section, Course } = model;
      const course = await Course.findOne({
        where: { courseSlug },
        attributes: ['id', 'indics'],
        raw: true,
        transaction,
      })

      if(!course) {
        throw Error('当前课程不存在！');
      }

      const { indics } = course;
      indics.map(data=>{
        if (data.chapterName !== chapterName) {
          return data;
        }

        if (!data.sections || !data.sections.length) {
          return data;
        }

        data.sections.map((section) => {
          if (section.sectionName === sectionName) {
            section.ifInternel = checked;
          }

          return section;
        });

        return data;
      })
      await Course.update({indics}, {
        where: {id: course.id},
        transaction,
      })
      const section = await Section.findOne({
        where: {
          courseID: course.id,
          chapterName,
          sectionName,
        },
        attributes: ['id', 'record'],
        transaction,
      })

      if(!section) {
        throw Error('当前节不存在！');
      }


      let { record } = section;
      if(!record){
        record = { ifInternel: checked };
      }else{
        record = { ...record, ifInternel: checked }
      }
      return await Section.update({record}, {
        where: {id: section.id},
        transaction,
      })
    }

    // 做为项目书
    async projectBook(courseSlug, chapterName, sectionName, checked, transaction) {
      const { model } = this.ctx;
      const { Section, Course } = model;
      const course = await Course.findOne({
        where: { courseSlug },
        attributes: ['id', 'indics'],
        raw: true,
        transaction,
      })

      if(!course) {
        throw Error('当前课程不存在！');
      }

      const { indics } = course;
      indics.forEach(data=>{
        if (data.chapterName !== chapterName) {
          return;
        }

        if (!data.sections || !data.sections.length) {
          return;
        }

        data.sections.forEach((section) => {
          if (section.sectionName === sectionName) {
            section.isProjectBook = checked;
          }

          return;
        });

        return;
      })
      await Course.update({indics}, {
        where: {id: course.id},
        transaction,
      })
      const section = await Section.findOne({
        where: {
          courseID: course.id,
          chapterName,
          sectionName,
        },
        attributes: ['id', 'record'],
        transaction,
      })

      if(!section) {
        throw Error('当前节不存在！');
      }


      let { record } = section;
      if(!record){
        record = { isProjectBook: checked };
      }else{
        record = { ...record, isProjectBook: checked }

        // 设置为项目书 
        if(checked){
          for (const key in record) {
            if(key === 'isProjectBook'){
              continue;
            }

            if (!record[key]) {
              continue;
            }

            const { project } = record[key];
            if(project){
              delete record[key].project;
            }
          }
        }else{
          // 取消项目书时候 把其它链接到这个项目书的链接删除
          const otherSections = await Section.findAll({
            where: {
              courseID: course.id,
              chapterName,
            },
            attributes: ['id', 'record', 'sectionName'],
            raw: true,
            transaction,
          })

          // 删除其他的链接
          for (const section of otherSections) {
            let { id, record } =section;
            if(record) {
              for (const key in record) {
                if(key === 'isProjectBook'){
                  continue;
                }

                if (!record[key]) {
                  continue;
                }  

                const { project } = record[key];
                
                if(project && project.sectionName===sectionName ){
                  delete record[key].project;
                  await Section.update({record}, {
                    where: {id},
                    transaction,
                  })
                }
              }
            }

          }

        }
      }
      return await Section.update({record}, {
        where: {id: section.id},
        transaction,
      })

    }

    // 获取项目书结构
    async getProjectBookData(courseSlug, courseID, chapterName, currentSectionName, currentSections) {
      const { model } = this.ctx;
      const { Section } = model;
      const sections = await Section.findAll({
        where: {
          courseID,
          chapterName
        },
        raw: true,
        attributes: ['id', 'record', 'sectionName', 'ext']
      })

      // 过滤 
      const arrs = currentSections.filter(i=>i.isProjectBook);
      const results = [];

      if (!arrs || !arrs.length) {
        return results;
      }

      const projectBookBindMap = {};
      const projectBookBindKeyMap = {};
      const isLinked = {};
      // 循环判断项目书是否已经链接出去 !!此处为兼容 项目书链接数据保存到数据库却未保存到文件的课程!!
      // sections.forEach((section) => {
      //   const { record, sectionName } = section;
      //   if (!record) {
      //     return;
      //   }

      //   // 循环判断各个代码块记录，是否已绑定项目书，没有返回
      //   for (const key in record) {
      //     if (key === 'isProjectBook') {
      //       continue;
      //     }

      //     const recordValue = record[key];
      //     if (!recordValue) {
      //       continue;
      //     }

      //     if (!recordValue.hasOwnProperty('project')) {
      //       continue;
      //     }

      //     if (!recordValue.project) {
      //       continue;
      //     }
          
      //     const { codeIndex, sectionName: projectSectionName, UUID } = recordValue.project;
      //     const mapKey = `${projectSectionName}_${UUID || codeIndex}`;
      //     if (projectBookBindMap[mapKey]) {
      //       continue;
      //     }
      //     projectBookBindMap[mapKey] = `${sectionName} 代码${key}`;
      //     projectBookBindKeyMap[mapKey] = `${sectionName} 代码${UUID}`;
      //     if(projectSectionName === currentSectionName){
      //       isLinked[UUID || codeIndex] = { index: key, sectionName };
      //     }
      //   }
      // });

      // 循环代码块获取项目书链接
      for (const section of currentSections) {
        const { sectionName, ext } = section;
        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        let fileResponse = null;
        try {
          fileResponse = await this.ctx.service.course.getFile(path);
        } catch(e) {
          console.log(e);
          continue;
        }

        if (!fileResponse) {
          continue;
        }
        if (ext !== 'ipynb') {
          continue;
        }

        const content = JSON.parse(fileResponse);
        if (content.cells && content.cells.length) {
          for (const cell of content.cells) {
            const { cell_type, metadata } = cell;
            const { UUID, type, linkProject } = metadata;
            if(cell_type==='code' && linkProject && !type){
              
              const { sectionName: projectSectionName, UUID: linkUUID } = linkProject;
              const mapKey = `${projectSectionName}_${linkUUID}`;
              if (projectBookBindMap[mapKey]) {
                continue;
              }
              projectBookBindMap[mapKey] = `${sectionName} 代码${UUID}`;
              projectBookBindKeyMap[mapKey] = `${sectionName} 代码${UUID}`;
              if(projectSectionName === currentSectionName){
                isLinked[linkUUID] = { index: UUID, sectionName };
              }
            }
          }
        }
      }

      const projectBookMap = {};
      for (const arr of arrs) {
        const obj = {title: arr.sectionName, key: arr.sectionName,  children: []}
        try{
          const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${arr.sectionName}.${arr.ext}`;
          
          let response = null;
          try {
            response = await this.ctx.service.course.getFile(path);
          } catch(e) {
            console.log(e);
            continue;
          }

          if (!response) {
            continue;
          }
          const content = JSON.parse(response);
          const cells = content.cells && content.cells.value ? content.cells.value : content.cells;
          if (cells && cells.length) {
            let index = 0
            let codeIndex = 0
            for (const cell of cells) {
              ++index;
              const { cell_type, source, metadata } = cell;
              const { UUID, type } = metadata;
              // 仅包括代码块
              if(cell_type==='code' && !type){
                ++codeIndex;
                const sourceText = source && source.length ? `${source.join('')} ` : '';
                // 隐藏并自动执行不允许关联
                if (sourceText.match(/^# >隐藏并自动执行/)) {
                  continue;
                }
  
                const [code] = source;
  
                const mapKey = `${arr.sectionName}_${UUID || codeIndex}`;
  
                if (!projectBookMap[mapKey]) {
                  projectBookMap[mapKey] = true;
                }
  
                obj.children.push({
                  title: `${code ?code.replace(/^\s*\#/, ''): ''} 代码${UUID}`,
                  key: `${arr.sectionName}_${UUID || codeIndex}`, 
                  index, 
                  codeIndex, 
                  sectionName: arr.sectionName,
                  ifLink: projectBookBindMap[mapKey],
                  showTitle: projectBookBindKeyMap[mapKey],
                  UUID
                });
              }
            }
          }
          
        }catch(e) {
          throw new Error(e.message);
        }
        results.push(obj)
      }
      return { isLinked, results, projectBookMap };
    }

    // 链接项目书文件
    async linkSectionProject(courseSlug, chapterName, sectionName, sectionRecord, content) {
      const { model } = this.ctx;
      const { Section, Course } = model;
      const transaction = await model.transaction({autocommit: false});
      try{
        const course = await Course.findOne({
          where: { courseSlug },
          attributes: ['id'],
          raw: true,
          transaction,
        })
  
        if(!course) {
          throw Error('当前课程不存在！');
        }

        const section = await Section.findOne({
          where: {
            courseID: course.id,
            chapterName,
            sectionName,
          },
          attributes: ['id', 'record'],
          raw: true,
          transaction,
        })
  
        if(!section) {
          throw Error('当前节不存在！');
        }
        const currentRecord = section.record;

        const otherSections = await Section.findAll({
          where: {
            courseID: course.id,
            chapterName,
          },
          attributes: ['id', 'record', 'sectionName'],
          raw: true,
          transaction,
        });

        const updateSectionList = [];
        // 是项目书则需判定是否替换UUID，是则取消绑定
        if (currentRecord && currentRecord.isProjectBook) {
          const UUIDMap = {};
          if (content && content.cells && content.cells.length) {
            for (const cell of content.cells) {
              if (cell.cell_type !== 'code') {
                continue;
              }

              UUIDMap[cell.metadata.UUID] = true;
            }
          }
          // 删除其他的链接
          for (const section of otherSections) {
            let { id, record, sectionName: otherSectionName } = section;

            // 项目书record不需要更新
            if (otherSectionName === sectionName) {
              continue;
            }

            // 检查其他节是否需要更新，不存在记录或者项目书的记录不需要更新
            if (!record || record.isProjectBook) {
              continue;
            }

            // 不属于本次更新的节，检查其他节是否需要更新
            let ifEdit = false;
            for (const key in record) {
              if (key === 'isProjectBook') {
                continue;
              }

              if (!record[key] || !record[key].project) {
                continue;
              }

              const { project } = record[key];
              if (!project || JSON.stringify(project) === '{}') {
                continue;
              }

              const { sectionName: PSN, UUID } = project;
              // 如果没有绑定当前项目书，则不处理
              if (!PSN || PSN !== sectionName || UUIDMap[UUID]) {
                continue;
              }
              // 告诉本节需更新
              ifEdit = true;
              delete record[key].project;
            }

            // 检测记录不需要更新，则放弃
            if (!ifEdit) {
              continue;
            }
            updateSectionList.push({
              'id': id,
              'record': record,
              'updated_at': moment().format('YYYY-MM-DD HH:mm:ss')
            });
          }
        } else {
          // 存在绑定记录，则检查是否重复绑定并取消上一个绑定记录；不存在绑定则，更新自己的记录
          // 整理当前需绑定的数组
          const projectBindProjectMap = {};
          for (const key in sectionRecord) {
            // 绑定数组，以下标为准，排除其他参数
            if (key === 'isProjectBook') {
              continue;
            }

            if (!sectionRecord[key]) {
              continue;
            }
            const { project } = sectionRecord[key];
            if (!project || JSON.stringify(project) === '{}') {
              continue;
            }

            const { sectionName: PSN, UUID } = project;
            if (!PSN) {
              continue;
            }

            const cKey = `${PSN}_${UUID}`;
            if (projectBindProjectMap[cKey]) {
              continue;
            }
            projectBindProjectMap[cKey] = true;
          }

          // 存在绑定记录，则检查是否重复绑定并取消上一个绑定记录；不存在绑定则，更新自己的记录
          for (const section of otherSections) {
            let { id, record, sectionName: sN } = section;

            // 合并之前更新内容进行存储
            if (sN === sectionName) {
              // 新绑定的加入
              for (const key in sectionRecord) {
                // 绑定数组，以下标为准，排除其他参数
                if (key === 'isProjectBook') {
                  continue;
                }
                if (!sectionRecord[key]) {
                  continue;
                }
                const { project } = sectionRecord[key];
                if(!record || !record[key]){
                  record = { ...record, [key]: { project }};
                } else {
                  record[key] = { ...record[key], project };
                }
              }
              // 旧绑定的去除
              for (const key in record) {
                // 绑定数组，以下标为准，排除其他参数
                if (key === 'isProjectBook') {
                  continue;
                }
                
                if (!record[key] || !record[key].project) {
                  continue;
                }

                if (sectionRecord[key]) {
                  continue;
                }
                
                delete record[key].project;
              }

              updateSectionList.push({
                'id': id,
                'record': record,
                'updated_at': moment().format('YYYY-MM-DD HH:mm:ss')
              });
              continue;
            }

            // 不属于本次更新的节，检查其他节是否需要更新，不存在记录的不需要检测
            if (!record || record.isProjectBook) {
              continue;
            }

            // 不属于本次更新的节，检查其他节是否需要更新
            let ifEdit = false;
            for (const key in record) {
              if (key === 'isProjectBook') {
                continue;
              }

              if (!record[key]) {
                continue;
              }

              const { project } = record[key];
              if (!project || JSON.stringify(project) === '{}') {
                continue;
              }

              const { codeIndex, sectionName: PSN, UUID } = project;
              if (!PSN) {
                continue;
              }
              // 判断是否在需绑定中，无则跳过
              const cKey = `${PSN}_${UUID || codeIndex}`;
              if (!projectBindProjectMap[cKey]) {
                continue;
              }

              // 告诉本节需更新
              ifEdit = true;
              delete record[key].project;
            }
            // 本节不更新，则放弃
            if (!ifEdit) {
              continue;
            }

            updateSectionList.push({
              'id': id,
              'record': record,
              'updated_at': moment().format('YYYY-MM-DD HH:mm:ss')
            });
          }
        }

        await Section.bulkCreate(updateSectionList, { 
          updateOnDuplicate: ['record', 'updated_at'],
          transaction
        });
      }
      catch(e) {
        await transaction.rollback();
        throw new Error(e.message);
      }

      // 提交事务
      await transaction.commit();
      return true;
    }

    // 链接至项目书
    async linkProject(courseSlug, chapterName, sectionName, projectSectionName, projectIndex, codeIndex, projectCodeIndex, transaction) {
      const { model } = this.ctx;
      const { Section, Course } = model;
      const course = await Course.findOne({
        where: { courseSlug },
        attributes: ['id'],
        raw: true,
        transaction,
      })

      if(!course) {
        throw Error('当前课程不存在！');
      }
      // 替换其他的
      const otherSections = await Section.findAll({
        where: {
          courseID: course.id,
          chapterName,
        },
        attributes: ['id', 'record', 'sectionName'],
        raw: true,
        transaction,
      })
      // 删除其他的链接
      for (const section of otherSections) {
        let { id, record } =section;
        if(record) {
          for (const key in record) {
            if(parseInt(key)){
              const { project } = record[key];
              
              if(project && project.codeIndex && project.sectionName && parseInt(project.codeIndex, 10)===parseInt(projectCodeIndex, 10) && projectSectionName===project.sectionName ){
                delete record[key].project;
                await Section.update({record}, {
                  where: {id},
                  transaction,
                })
              }
            }
          }
        }

      }

      const section = await Section.findOne({
        where: {
          courseID: course.id,
          chapterName,
          sectionName,
        },
        attributes: ['id', 'record'],
        transaction,
      })

      if(!section) {
        throw Error('当前节不存在！');
      }
      let { record } = section;
      if(!record || !record[codeIndex]){
        record = {...record, [codeIndex]: {project: { sectionName: projectSectionName, index: projectIndex, codeIndex: projectCodeIndex }}}
      }else{
        const { submit, pass, project } = record[codeIndex];
        record[codeIndex] = {submit, pass, project: { ...project,  sectionName: projectSectionName, index: projectIndex , codeIndex: projectCodeIndex }}
      }
      // return await Section.update({record}, {
      //   where: {id: section.id},
      //   transaction,
      // })
    }

    // 取消链接项目书
    async cancelLinkProject(courseSlug, chapterName, sectionName, projectSectionName, projectIndex, codeIndex, projectCodeIndex, transaction) {
      const { model } = this.ctx;
      const { Section, Course } = model;
      const course = await Course.findOne({
        where: { courseSlug },
        attributes: ['id'],
        raw: true,
        transaction,
      })

      if(!course) {
        throw Error('当前课程不存在！');
      }

      const section = await Section.findOne({
        where: {
          courseID: course.id,
          chapterName,
          sectionName,
        },
        attributes: ['id', 'record'],
        transaction,
      })

      if(!section) {
        throw Error('当前节不存在！');
      }
      let { record } = section;
      if(!record || !record[codeIndex]){
        return;
      }
      const { project } = record[codeIndex];
      if(project.sectionName===projectSectionName && project.codeIndex ===projectCodeIndex){
        delete record[codeIndex].project;
      }
      return await Section.update({record}, {
        where: {id: section.id},
        transaction,
      })

    }

    async getSectionAIFile(courseSlug, chapterName, sectionNameList) {
      const { ctx } = this;
      const sectionMap = {};
      for (const name of sectionNameList) {
        let content = null;
        try {
          const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${name}.ipynb`;
          content = await ctx.service.course.getFile(path, true);
        } catch(e) {
          continue;
        }

        sectionMap[name] = content;
      }
      
      return sectionMap;
    }

    // 读取某个班在某个章节的答题记录
    async getSectionRecord(courseSlug, chapterName, sectionName, teamID, transaction) {
      const { model } = this.ctx;
      const { Section, Course, TeamUser, SectionRecord } = model;
      // 先读取courseID
      let courseData = await Course.findOne({
        where: {
          courseSlug
        },
        attributes: ['id'],
        transaction
      })
      if (!courseData) {
        throw new Error('当前章节不存在')
      }
      courseData = courseData.dataValues ? courseData.dataValues : courseData;
      // 再读取sectionID
      let sectionData = await Section.findOne({
        where: {
          courseID: courseData.id,
          chapterName,
          sectionName
        },
        transaction
      })
      if (!sectionData) {
        throw new Error('当前章节不存在')
      }
      sectionData = sectionData.dataValues ? sectionData.dataValues : sectionData;

      // 读取某个班的所有学生
      let students = await TeamUser.findAll({
        where: {
          teamID
        },
        attributes: ['userID'],
        transaction
      });
      const studentIDList = [];
      for(let i = 0; i < students.length; i += 1) {
        const student = students[i].dataValues ? students[i].dataValues : students[i];
        studentIDList.push(student.userID);
      }
      // 根据sectionID和studentID查找答题记录
      const records = await SectionRecord.findAll({
        where: {
          userID: {
            [Op.in]: studentIDList
          },
          sectionID: sectionData.id
        },
        transaction
      })
      return { records, studentsNum: students.length };
    }

    async hasRecord(courseSlug, chapterName, sectionName) {
      const { model } = this.ctx;
      const { Section, Course, SectionRecord } = model;
      // 先读取courseID
      let courseData = await Course.findOne({
        where: {
          courseSlug
        },
        attributes: ['id'],
      })
      if (!courseData) {
        throw new Error('当前章节不存在')
      }
      courseData = courseData.dataValues ? courseData.dataValues : courseData;
      // 再读取sectionID
      let sectionData = await Section.findOne({
        where: {
          courseID: courseData.id,
          chapterName,
          sectionName
        },
      })
      if (!sectionData) {
        throw new Error('当前章节不存在')
      }
      // 根据sectionID和studentID查找答题记录
      const records = await SectionRecord.findAll({
        where: {
          sectionID: sectionData.id
        },
      })
      return records.length;
    }

    // 统计用，获取当前章和节的记录
    async getCurrentChapterAndClassSectionRecord({ classID, courseSlug, chapterName }) {
      const { model, service } = this.ctx;
      // 获取当前班级的userID
      const teamUsers = await model.TeamUser.findAll({
        where: {
          teamID: classID
        },
        attributes: ['userID']
      })
      const userIDList = teamUsers.map(teamUser => teamUser.userID);
      // 获取当前章所有的节列表
      const sectionList = await service.section.getSectionList(courseSlug, chapterName);
      // 获取当前章的所有节id和节名称列表
      const sectionIDList = sectionList.map(section => section.sectionID);
      const sectionNameList = sectionList.map(section => section.sectionName);

      const result = [];
      // 获取当前班级，当前章所有的答题记录
      const sectionRecordList = await model.SectionRecord.findAll({
        attributes: ['userID', 'sectionID', 'record', 'stayTime'],
        where: {
          sectionID: {
            [Op.in]: sectionIDList
          },
          userID: {
            [Op.in]: userIDList
          }
        },
        raw: true,
      });
      // 获取当前章下的AI文件内容，因为答题记录passCount中有微应用和资源的UUID，需要对比排除，拿到代码块的通过个数
      const AIContents = await this.ctx.service.section.getSectionAIFile(courseSlug, chapterName, sectionNameList);
      for (let i = 0; i < sectionRecordList.length; i++) {
        const { record, stayTime, sectionID, userID } = sectionRecordList[i];
        // 根据课程ID从课程缓存中查询课程类型
        const section = sectionList.find(section => section.sectionID === sectionID);
        if (!section) {
          console.error(`未查询到课程，课程ID为${sectionID}`);
          return;
        }
        const type = section.sectionType;
        let count = 0;
        // 计算得分
        if (record) {
          switch(type) {
            case 'OJ':
              // 全部通过计分
              if (parseInt(record.score, 10) === 100) {
                count = 1;
              }
              break;
            case 'AI':
              const AIItem = sectionList.find(sectionItem => sectionItem.sectionID === sectionID);
              const AIContent = AIContents[AIItem.sectionName];
              if (!AIContent) {
                continue;
              }
              const { cells } = AIContent;
              if (!cells || !cells.length ) {
                continue;
              }
              const UUIDList = [];
              cells.forEach((cell) => {
                // 必须是代码块
                if (cell['cell_type'] !== 'code') {
                  return;
                }

                // 微应用不计算
                if(cell.metadata['type']) {
                  return;
                }
    
                const code = cell.source && cell.source.length ? cell.source[0] : '';
    
                // 教师自动执行代码块不显示
                if (code.match(/^# >隐藏并自动执行/)) {
                  return cell;
                } 
    
                // 记录代码块的UUID
                UUIDList.push(cell.metadata.UUID);
                return cell;
              });
              
              // 通过记录生成UUID
              const passCount = [];
              for(const uuid in record.UUIDsMap) {
                const uuidRecord = record.UUIDsMap[uuid];
                if(uuidRecord.status === '运行通过') {
                  passCount.push(uuid);
                }
              }

              // 只有代码块才计算分数
              count = passCount.filter(passCountItem => UUIDList.includes(passCountItem)).length;
              break;
            case 'OI':
              // 外面的大题
              for (let bigQustionIndex in record) {
                // 小题
                for (let smallQustionIndex in record[bigQustionIndex]) {
                  if (record[bigQustionIndex][smallQustionIndex].status) {
                    // 每个小题正确计分
                    count += 1;
                  }
                }
              }
              break;
            default:
              break;
          }
          
          result.push({
            type,
            count,
            stayTime,
            userID
          })
        }
      }

      return result;

    }

    // 获取指定版本课程文件内容
    // @returns String
    async getHistoryFileContent(fileInfo) {
      const { fileDir, sectionName, version, ext } = fileInfo;
      const filePath = `${fileDir}/${sectionName}`;

      let fileContent = null;
      // 读取压缩文件
      try {
        const zip = new AdmZip(`${filePath}.zip`);
        const zipEntries = zip.getEntries(); // an array of ZipEntry records
  
        zipEntries.forEach(function (zipEntry) {
          // 获取指定版本文件内容
          if (zipEntry.entryName === `${version}.${ext}`) {
            fileContent = zipEntry.getData().toString("utf8"); // type: String
          }
        });
      } catch (e) {
        console.error('getHistoryFileContent', e);
        throw new Error(`未查找到版本${version}的历史文件记录！`);
      }

      return fileContent;
    }

    // 将课程文件添加进课程历史压缩文件
    async compressHistoryFile(fileInfo, type) {
      const { fileDir, sectionName, sectionType, fileContent, version, ext, newSectionTitle } = fileInfo;
      const filePath = `${fileDir}/${sectionName}`;

      // 判断文件是否存在
      fs.access(`${filePath}.zip`, fs.constants.F_OK, async (err) => {
        if (err) {
          // 如果不是初次创建压缩文件内容，输出日志，并重新创建压缩包
          if (type !== 'create') {
            console.error('compressHistoryFile 历史记录压缩包文件不存在，重新创建', err);
          }

          // 创建课程历史版本文件压缩文件
          const zip = new AdmZip();   
          zip.addFile(`${version}.${ext}`, Buffer.from(fileContent), `version: ${version}`);
          zip.writeZip(`${filePath}.zip`);

        } else {
          const zip = new AdmZip(`${filePath}.zip`);
          const zipEntries = zip.getEntries();

          const fileContentMap = {};
          zipEntries.forEach(function (zipEntry) {
            fileContentMap[zipEntry.entryName] = zipEntry.getData().toString("utf8");
          });

          const compressArgs = { ext, type, fileContentMap, fileContent, version, newSectionTitle, sectionType };
          const newFileMap = handleCompressFile(compressArgs);

          // 压缩文件
          for (const newFile in newFileMap) {
            zip.addFile(newFile, Buffer.from(newFileMap[newFile]), `version: ${newFile}`);
          }

          zip.writeZip(`${filePath}.zip`);
        }
      });
    }

    // 保存历史记录
    async saveHistoryRecords(query, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Section, User } = model;
      const { userID, courseSlug, chapterName, sectionName, fileContent, modifiedType, comment } = query;
      const fileDir = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;

      // 初始版本为1
      let version = 1;
      let newHistoryRecords = [];

      const userInfo = await User.findOne({
        where: {
          id: userID
        },
        attributes: ['name'],
        raw: true,
        transaction
      })

      // 课程历史记录信息，课程类型相关信息
      const historyQuery = { courseSlug, chapterName, sectionName };
      const { historyRecords, sectionType, ext, id: sectionID } = await ctx.service.section.getHistoryRecords(historyQuery, transaction);
      const { name } = userInfo;
      
      // 如果没有历史记录，首次发布
      if (!historyRecords || !historyRecords.length) {
        newHistoryRecords.push({
          version: 1,
          userID: userID,
          userName: name,
          saveTime: Date.now(),
          modifiedType: modifiedType,
          comment: '首次发布',
        });

        // 创建压缩文件
        const fileInfo = { fileDir, sectionName, sectionType, fileContent, version: 1, ext };
        await ctx.service.section.compressHistoryFile(fileInfo, 'create');
      }
      
      // 如果有历史记录信息
      if (historyRecords && historyRecords.length) {
        const lastRecord = historyRecords[historyRecords.length -1];
        const { version: lastVersion, modifiedType: lastType, userID: lastUserID, saveTime: lastSaveTime } = lastRecord;

        const durationMinutes = (Date.now() - lastSaveTime) / 60000;

        // 最新一条记录为草稿,用户id相同，相隔时间不超过30分钟，更新版本记录信息 
        const updateCondition = (lastType === '草稿') && (userID === lastUserID) && (durationMinutes <= 30);

        if (updateCondition) {
          newHistoryRecords = historyRecords;
          newHistoryRecords.splice(historyRecords.length-1, 1, {
            version: lastVersion,
            userID: userID,
            userName: name,
            saveTime: Date.now(),
            modifiedType: modifiedType,
            comment: comment,
          });
          // 更新最新的压缩文件
          const fileInfo = { fileDir, sectionName, sectionType, fileContent, version: lastVersion, ext };
          await ctx.service.section.compressHistoryFile(fileInfo, 'update');
        } else {
         // 其他情况均增加新记录
          newHistoryRecords = [
            ...historyRecords,
            {
              version: lastVersion + 1,
              userID: userID,
              userName: name,
              saveTime: Date.now(),
              modifiedType: modifiedType,
              comment: comment,
            }
          ];   
          version = lastVersion + 1;
          // 添加压缩文件
          const fileInfo = { fileDir, sectionName, sectionType, fileContent, version, ext };
          await ctx.service.section.compressHistoryFile(fileInfo, 'add');
        }
      }

      // 更新 historyRecords 字段
      await Section.update(
        {
          historyRecords: newHistoryRecords
        },
        {
          where: { 
            id: sectionID,
          },
          transaction,
          lock: transaction.LOCK.UPDATE,
        }
      );

      return newHistoryRecords;
    }

    // 获取文件历史记录信息
    async getHistoryRecords(query, transaction = null) {
      const { ctx } = this;
      const { model } = ctx;
      const { Course, Section } = model;
      const { courseSlug, chapterName, sectionName } = query;
      const course = await Course.findOne({
        where: {
          courseSlug: courseSlug,
        },
        attributes: ['id'],
        raw: true,
        transaction,
      })
      const { id: courseID } = course;

      const response = await Section.findOne({
        where:{
          courseID: courseID,
          chapterName: chapterName,
          sectionName: sectionName,
        },        
        attributes: ['id', 'historyRecords', 'sectionType', 'ext'],
        raw: true,
        transaction,
        lock: transaction ? transaction.LOCK.UPDATE : null,
      })

      return response;
    }

    // 获取历史版本文件
    async getHistoryFile(query) {
      const { ctx } = this;

      const { courseSlug, chapterName, sectionName, sectionType, ext, version } = query;
      // 课程文件夹
      const fileDir = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;

      const fileInfo = { fileDir, sectionName, sectionType, version, ext };

      const rawFileContent = await ctx.service.section.getHistoryFileContent(fileInfo); // return String

      // 输出课程文件内容
      switch(sectionType) {
        case 'AI':
        case 'Access':
        case 'Excel':
        case 'Scratch':
        case 'MicroBit':
        case 'CodeBlank':
          try {
            return JSON.parse(rawFileContent);
          } catch (error) {
            console.error(error);
            return rawFileContent;
          }
        case 'OI':
          try {
            return getOIQuestion(rawFileContent, true);
          } catch (error) {
            console.error(error);
            return rawFileContent;
          }
        case 'OJ':
          try {
            return getOJAnswer(rawFileContent);
          } catch (error) {
            console.error(error);
            return rawFileContent;
          }
        default:
          throw new Error(`未识别的类型${sectionType}`);
      }
    }

    // 恢复历史版本文件内容
    async recoverHistoryFile(query, transaction){
      const { ctx } = this;
      const { model } = ctx;
      const { Section, User } = model;
      const { courseSlug, chapterName, sectionName, version, userID, sectionTitle } = query;

      // 新增一个版本，添加新版本记录，且类型为草稿
      let newHistoryRecords = [];
      const { historyRecords, sectionType, ext, id: sectionID }= await ctx.service.section.getHistoryRecords(query, transaction);
      const lastVersion = historyRecords[historyRecords.length -1].version;
      const newVersion = lastVersion + 1;

      const userInfo = await User.findOne({
        where: {
          id: userID
        },
        attributes: ['name'],
        raw: true,
        transaction
      })

      const { name } = userInfo;

      newHistoryRecords = [
        ...historyRecords,
        {
          version: newVersion,
          userID: userID,
          userName: name,
          saveTime: Date.now(),
          modifiedType: '草稿',
          comment: `恢复为版本${version}`,
        }
      ];

      // 更新 historyRecords 字段
      await Section.update(
        {
          historyRecords: newHistoryRecords
        },
        {
          where: { 
            id: sectionID,
          },
          transaction
        }
      );

      // 课程文件夹
      const fileDir = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;

      // 获取需要恢复的指定版本的课程文件内容
      const fileQuery = { fileDir, sectionName, version, ext };
      let rawfileContent = await ctx.service.section.getHistoryFileContent(fileQuery); // return String

      // AI 项目书链接不保存
      if (sectionType === 'AI') {
        const fileContent = JSON.parse(rawfileContent);
        const cells = fileContent.cells;
        if (cells && cells.length) {
          for (const cell of cells) {
            delete cell.metadata.linkProject;
          }
        }
        rawfileContent = JSON.stringify(fileContent);
      }

      // 将当前课程文件内容恢复为指定版本的课程文件内容
      const path = `${courseSlug}/${chapterName}`;
      const fileName = `${sectionName}.${ext}`; 

      /* 
      * 将此文件标题字段修改为当前课程标题
      * @params: rawfileContent, sectionTitle, sectionType
      * @returns: fileContent<String>
      */
      const renameArgs = { sectionType, newSectionTitle: sectionTitle, fileContent: rawfileContent };
      rawfileContent = handleRenameHistoryTitle(renameArgs);    

      // 新建一个文件插入
      await ctx.service.file.uploadXMLFile(path, fileName, rawfileContent);

      // 将恢复后的新版本文件添加到压缩文件
      const fileInfo = { fileDir, sectionName, sectionType, fileContent: rawfileContent, version: newVersion, ext };
      await ctx.service.section.compressHistoryFile(fileInfo, 'add');
    }

    // 将指定版本文件内容另存为新课程
    async saveHistoryFileAs(query, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Course } = model;
      const { courseSlug, chapterName, sectionName, version, newSectionName, newSectionTitle, sectionType, ext } = query;

      // 获取课程目录缓存
      const course = await Course.findOne({
        where: {
          courseSlug: courseSlug,
        },
        attributes: ['indics'],
        raw: true,
        transaction
      })
      const { indics } = course;

      let iExist = false;
      let sectionIndex = 0;

      indics.forEach((data) => {
        if (data.chapterName !== chapterName) {
          return;
        }

        if (!data.sections || !data.sections.length) {
          return;
        }

        data.sections.forEach((section) => {
          if (section.sectionName === newSectionName) {
            iExist = true;
          }
        });

        sectionIndex = data.sections.length;
      });

      if (iExist) {
        throw new Error('课程名称已重复，请重新填写');
      }

      sectionIndex += 1;

      // 课程文件夹
      const fileDir = `${app.config.file.dir}/${ctx.schoolSlug}/course/${courseSlug}/${chapterName}`;
      const fileInfo = { fileDir, sectionName, sectionType, version, ext };

      // 指定版本的课程文件内容
      let rawFileContent = await ctx.service.section.getHistoryFileContent(fileInfo); // @return String

      // ipynb json 文件需解析
      const isParse = ['AI', 'Access', 'Excel', 'Scratch', 'MicroBit'].includes(sectionType);
      if (isParse) {
        const fileContent = JSON.parse(rawFileContent);

        // 项目书链接不保存
        if (sectionType === 'AI') {
          const cells = fileContent.cells;
          if (cells && cells.length) {
            for (const cell of cells) {
              delete cell.metadata.linkProject;
            }
          }
        }
      
        rawFileContent = fileContent;
      }

      // 创建课程
      await ctx.service.course.createCourseFile({ courseSlug, chapterName, sectionName: newSectionName, sectionTitle: newSectionTitle, sectionType, sectionIndex, ext, newFileContent: rawFileContent }, transaction);
    }

    async updateHistoryComment(query, transaction){
      const { ctx } = this;
      const { model } = ctx;
      const { Course, Section } = model;
      const { courseSlug, chapterName, sectionName, version, comment } = query;

      // 课程历史记录信息，课程类型相关信息
      const historyQuery = { courseSlug, chapterName, sectionName };
      const { historyRecords, id: sectionID } = await ctx.service.section.getHistoryRecords(historyQuery, transaction);

      if (!historyRecords || !historyRecords.length) {
        throw new Error(`未查询到历史版本记录！`);
      }

      const selectRecord = historyRecords.find(record => record.version === version);
      if (!selectRecord) {
        throw new Error(`未查询到版本${version}的历史记录！`);
      }

      // 更新版本说明内容
      const newHistoryRecords = historyRecords.map(record => {
        if (record.version === version) {
          record.comment = comment;
        }
        return record;
      })

      // 更新 historyRecords 字段
      await Section.update(
        {
          historyRecords: newHistoryRecords
        },
        {
          where: { 
            id: sectionID,
          },
          transaction
        }
      );

      return newHistoryRecords;
    }

    async getRecordUUIDsAndContent(path) {
      const content = await this.ctx.service.course.getFile(path, true)

      let { cells } = content;
      if (!cells ) {
        return;
      }

      if (!cells.value && !cells.length ) {
        return;
      }

      if (cells.value && !cells.value.length ) {
        return;
      }
      // 统计需要计算分数的代码块
      let UUIDs = [];
      let codeCount = 0;
      cells = cells.value ? cells.value : cells;
      cells.forEach((cell) => {
        // 必须是代码块
        if (cell['cell_type'] !== 'code') {
          return;
        }

        // 图片视频资源不展示
        if(cell.metadata['type'] && ((cell.metadata['type'] === 'resources') || (cell.metadata['type'] === 'filepreviewer'))) {
          return;
        }

        const code = cell.source && cell.source.length ? cell.source[0] : '';

        // 教师自动执行代码块不显示
        if (code.match(/^# >隐藏并自动执行/)) {
          return;
        } 
        if (!cell.metadata.type) {
          codeCount += 1;
        }
        if (cell.metadata.unopened) {
          return;
        }
        UUIDs.push(cell.metadata.UUID);
      });
      return { UUIDs, content, codeCount };
    }

    async postMicroAppFile(userID, courseSlug, codeResult, chapterName) {
      const { ctx } = this;
      const { type, fileName, message } = codeResult[0];
      let content = null;
      let fullName = null;
      switch(type) {
        case 'text':
          content = message;
          fullName = `${fileName}.txt`;
          break;
        case 'table':
          fullName = `${fileName}.csv`;
          let csvList = [];
          if (message && message.length) {
            message.forEach((iValue) => {
              if (!iValue || !iValue.length) {
                return;
              }

              const grid = [];
              iValue.forEach((jValue) => {
                grid.push(jValue.value);
              })

              csvList.push(grid.join(','));
            })
          }
          content = "\ufeff" + csvList.join('\n');
          break;
        case 'mind':
        case 'flow':
        case 'networksimulator':
        case 'spreadsheet':
        case 'drawio':
        case 'Scratch':
        case 'MicroBit':
          // 编码为字符串
          content = JSON.stringify(message);
          fullName = `${fileName}.json`;
          break;
        // case 'drawio':
          // BASE64解码成二进制PNG格式
          // content = Buffer.from(message.slice(pngBase64Header.length), "base64");
          // fullName = `${fileName}.png`;
          // break;
        default:
          break;
      }

      await ctx.service.file.uplodaStudentFile(type === 'Scratch' || type === 'MicroBit' ? `${courseSlug}/${userID}/${chapterName}` : `${courseSlug}/${userID}`, fullName, content);
    }

    async commitSourceBlockRecord(params, transaction) {
      const { ctx } = this;
      const { UUID, schoolSlug, courseSlug, kernel, chapterName, sectionName, isRuned, userID, sectionID } = params;
      const { codeResult = [], code = '' } = kernel;

      // 去数据库中查找记录,存在修改，不存在创建
      const progressRecord = await ctx.service.section.getAiProgressByUser(sectionID, userID);
      const { record } = progressRecord;
      const progress = record ? record.UUIDsMap : {};

      const isError = codeResult.find(e => e.type === 'error');
      if (!isRuned) {
        progress[UUID] = { type: 'notRun', status: '未运行', code, result: codeResult };
      } else {
        if (isError) {
          progress[UUID] = { type: 'error', status: '运行报错', code, result: codeResult };
        } else {
          progress[UUID] = { type: 'success', status: '运行通过', code, result: codeResult };
        }
      }

      const path =`${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.ipynb`;
      const { UUIDs, content, codeCount } = await ctx.service.section.getRecordUUIDsAndContent(path);

      let passCount = 0;

      // 重新计算AI课程分数
      for (const progressKey in progress) {

        const progressValue = progress[progressKey];
        // 如果不是微应用并且没有通过的话
        if ((progressValue.type !== 'success' && !progressValue.microAppFileType)) {
          continue;
        }
        if (!UUIDs.includes(progressKey)) {
          continue;
        }
        // 要么代码块通过，要么是微应用
        passCount += 1;
      }
      // 获取当前答题index
      const filterArr = content.cells.filter(item => item['cell_type'] === 'code' && !item.metadata.type && (item.source && item.source.length ? (item.source && item.source[0] && !item.source[0].match(/^# >隐藏并自动执行/)) : true));
      const currentIndex = filterArr.findIndex(cell => cell.metadata.UUID === UUID)
      // 查询这之前有几个
      const count = filterArr.slice(0, currentIndex + 1).filter(item => item['cell_type'] === 'code' && !item.metadata.type && (item.source && item.source.length ? (item.source && item.source[0] && !item.source[0].match(/^# >隐藏并自动执行/)) : true))
      const response = { [`代码${count.length}`]: progress[UUID].status, UUID, totalScore: UUIDs.length, passCount, codeCount}
      // 储存答题结果
      await ctx.service.section.recordAIProgress(sectionID, userID, { UUIDsMap: {...progress} }, UUIDs.length, passCount, transaction);

      // 处理项目书的问题
      const currentCell = content.cells.find(cell => cell.metadata.UUID === UUID);
      if (!currentCell) {
        throw new Error('当前代码块不存在');
      }
      // 判断是否为链接了项目书
      const linkProject = currentCell.metadata.linkProject;

      let fileName = sectionName;
      // 不是项目书，需要获取当前代码是否链接至项目书
      if (linkProject) {
        if (!linkProject.sectionName) {
          return;
        }
        
        fileName = linkProject.sectionName;
        
        // 当前不是项目书且代码块链接至项目书
        // 1.获取对应项目书答题记录
        // 获取当前节的数据
        const sectionProjectAnswer = await ctx.service.section.getSection(courseSlug, chapterName, fileName, transaction);
        
        // 2.不存在链接的项目书直接返回，否则处理答题记录
        if (!sectionProjectAnswer) {
          return;
        }

        const sectionID = sectionProjectAnswer.id;
        // 3.答题记录不存在则创建，存在则修改
        const progressRecord = await ctx.service.section.getAiProgressByUser(sectionID, userID);
        let { record: hasAnswer } = progressRecord;
        const progress = hasAnswer ? hasAnswer.UUIDsMap : {  };
        const projectBookPath =`${app.config.file.dir}/${schoolSlug}/course/${courseSlug}/${chapterName}/${fileName}.ipynb`;
        const { UUIDs: projectBookUUIDs } = await ctx.service.section.getRecordUUIDsAndContent(projectBookPath);
        // 如果有不存在的uuid，清掉
        for (let key in progress) {
          if (!UUIDs.includes(key)) {
            delete process[key];
          }
        }
        const isErrorProject = codeResult.find(e => e.type === 'error');
        if (isErrorProject) {
          progress[linkProject.UUID] = { type: 'error', status: '运行报错', code, result: codeResult };
        } else {
          progress[linkProject.UUID] = { type: 'success', status: '运行通过', code, result: codeResult };
        }
        let projectBookPassCount = 0;
        for (let key in progress) {
          if (progress[key].microAppFileType || progress[key].type === 'success') {
            projectBookPassCount += 1;
          }
        }
        // 修改答题记录
        await ctx.service.section.recordAIProgress(sectionID, userID, { UUIDsMap: progress }, projectBookUUIDs.length, projectBookPassCount, transaction);
      }
      return response;
    }

    async submitAccessAnswer(sectionQuery, transaction) {
      const { ctx } = this;
      const { userID, chapterName, sectionName, courseSlug, studentAnswer, classID } = sectionQuery;

      const course = await ctx.model.Course.findOne({
        where: {
          courseSlug, 
        },
        raw: true,
        transaction
      });

      if (!course) {
        throw new Error(`当前课程 ${sectionName} 不存在`);
      }

      const { id: courseID } = course;
      const section = await ctx.model.Section.findOne({
        where: {
          courseID,
          chapterName,
          sectionName
        },
        raw: true,
        transaction
      });

      if (!section) {
        throw new Error(`当前课程 ${sectionName} 不存在`);
      }

      const { id: sectionID } = section;

      const { steps = [] } = studentAnswer;

      // 分数统计
      const totalScore = steps.length;
      const passCount = steps.filter(i => i.status === 'finish').length;

      // 更新记录
      const currentRecord = await ctx.model.SectionRecord.findOne({
        where: {
          userID,
          sectionID,
        },
        raw: true,
        transaction
      });

      if (!currentRecord) {
        await ctx.model.SectionRecord.create({
          userID,
          sectionID,
          record: studentAnswer,
          totalScore,
          passCount,
        }, {
          transaction
        });
      } else {
        await ctx.model.SectionRecord.update({
          record: studentAnswer,
          totalScore,
          passCount,
        }, {
          where: {
            userID,
            sectionID,
          },
          transaction
        });
      }

    }

    async submitPPTRecord(sectionQuery, transaction) {
      const { ctx } = this;
      const { userID, chapterName, sectionName, courseSlug, studentAnswer } = sectionQuery;

      const course = await ctx.model.Course.findOne({
        where: {
          courseSlug, 
        },
        raw: true,
        transaction
      });

      if (!course) {
        throw new Error(`当前课程 ${sectionName} 不存在`);
      }

      const { id: courseID } = course;
      const section = await ctx.model.Section.findOne({
        where: {
          courseID,
          chapterName,
          sectionName
        },
        raw: true,
        transaction
      });

      if (!section) {
        throw new Error(`当前课程 ${sectionName} 不存在`);
      }

      const { id: sectionID, ext } = section;

      const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
      const fileContent = await ctx.service.course.getFile(path, true);
      if (!fileContent) {
        throw new Error(`当前课程文件 ${sectionName} 不存在`);
      }

      const { page, totalPages } = studentAnswer;

      // 分数统计
      const totalScore = totalPages;
      const passCount = page;

      // 更新记录
      const currentRecord = await ctx.model.SectionRecord.findOne({
        where: {
          userID,
          sectionID,
        },
        raw: true,
        transaction
      });

      if (!currentRecord) {
        await ctx.model.SectionRecord.create({
          userID,
          sectionID,
          record: studentAnswer,
          totalScore,
          passCount,
        }, {
          transaction
        });
      } else {
        await ctx.model.SectionRecord.update({
          record: studentAnswer,
          totalScore,
          passCount,
        }, {
          where: {
            userID,
            sectionID,
          },
          transaction
        });
      }

    }

    // 客观题判分
    async OIJudge(params, transaction) {
      const { ctx } = this;
      const { SectionRecord } = ctx.model;
      const { questions, rawData, ifShowAnswer, userID, sectionID } = params;

      // 循环判分
      const judgeResult = {};
      let codeCount = 0;
      let passCount = 0;
      for (const rawQuestion of rawData) {
        // 每道题目的基本信息及标准答案
        const { questionType, UUID, answer: rawAnswer } = rawQuestion;

        const currentQuestion = questions[UUID];
        if (!currentQuestion) {
          throw new Error('题目不存在');
        }

        // 获取学生提交答案
        const { answer, ifSign } = currentQuestion;

        // 答题记录存储
        if (questionType !== '文本') {
          judgeResult[UUID] = { answer, rawAnswer, status: true, ifSign };
          codeCount += 1;
        }

        // 根据类型判决
        switch(questionType) {
          case '单选题':
            if (answer !== rawAnswer) {
              judgeResult[UUID].status = false;
            }
            break;
          case '多选题':
            if (!answer || !answer.length) {
              judgeResult[UUID].status = false;
              break;
            }
            rawAnswer.forEach((value) => {
              // 一旦错误，无需多判
              if (!judgeResult[UUID].status) {
                return;
              }

              if (answer.indexOf(value) !== -1) {
                return;
              }
              judgeResult[UUID].status = false;
            });
            if (answer.length !== rawAnswer.length) {
              judgeResult[UUID].status = false;
            }

            break;
          case '填空题':
            if (!answer || !answer.length) {
              judgeResult[UUID].status = false;
              break;
            }
            rawAnswer.forEach((value, keyIndex) => {
              // 一旦错误，无需多判
              if (!judgeResult[UUID].status) {
                return;
              }

              const { text } = value;
              const { text: answerText } = answer[keyIndex];
              if (text.indexOf(answerText) !== -1) {
                return;
              }
              judgeResult[UUID].status = false;
            });
            break;
          case '选择填空题':
            if (!answer || !Object.keys(answer).length) {
              judgeResult[UUID].status = false;
              break;
            }

            for (const answerKey in rawAnswer) {
              const answerList = answer[answerKey].split(',').sort();
              const rawAnswerList = rawAnswer[answerKey].split(',').sort();
              const isPass = JSON.stringify(answerList) === JSON.stringify(rawAnswerList);
              if (!answer[answerKey] || !isPass) {
                judgeResult[UUID].status = false;
                break;
              }
            }
            break;
          case '编程填空题':
            if (!answer || !Object.keys(answer).length) {
              judgeResult[UUID].status = false;
              break;
            }

            for (const answerKey in rawAnswer) {
              let answers = rawAnswer[answerKey];
              let studentAnswer = answer[answerKey];

              if (!studentAnswer) {
                judgeResult[UUID].status = false;
                break;
              }
              
              // 答案去除空格
              answers = answers.map(ans => {
                return ans.replace(/\s/g, '');
              });

              studentAnswer = studentAnswer.replace(/\s/g, '');

              const hasAnswer = answers.indexOf(studentAnswer) !== -1;
              if (!hasAnswer) {
                judgeResult[UUID].status = false;
                break;
              }
            }
            break;
          default:
            break;
        }

        if (judgeResult[UUID] && judgeResult[UUID].status) {
          passCount += 1;
        }
      }


      if (ifShowAnswer === '只公布对错') {
        for(let key in judgeResult) {
          if(key === 'codeCount' || key === 'passCount' || key === 'submitTimes') {
            continue;
          }
          const bigQuestion = judgeResult[key];
          delete bigQuestion.rawAnswer;
        }
      }

      let newJudgeResult = null;
      // 修改提交答案及提交次数
      const sectionRecord = await SectionRecord.findOne({
        where: {
          sectionID, 
          userID
        },
        attributes: ['record', 'stayTime'],
        // raw: true,
        transaction
      });

      if (!sectionRecord) {
        // 如果没有此答题记录创建答题记录
        newJudgeResult = {
          ...judgeResult,
          submitTimes: 1
        }
        await SectionRecord.create({
          userID,
          sectionID,
          record: newJudgeResult,
          totalScore: codeCount,
          passCount: passCount,
        },{
          transaction
        });
      } else {
        const { record } = sectionRecord;
        // 更新record
        if (record) {
          const { submitTimes = 0 } = record;
          newJudgeResult = {
            ...judgeResult,
            submitTimes: submitTimes + 1
          }
        } else {
          // 如果record为空
          newJudgeResult = {
            ...judgeResult,
            submitTimes: 1
          }
        }

        await SectionRecord.update({
          record: newJudgeResult,
          totalScore: codeCount,
          passCount: passCount,
        }, {
          where:  {
            sectionID,
            userID,
          },
          transaction
        });
      }
      
      return { judgeResult: newJudgeResult, totalScore: codeCount, passCount: passCount };
    }

    async checkSectionStatus(sectionQuery) {
      const { ctx } = this;
      
      // 课程历史记录信息
      const { historyRecords } = await ctx.service.section.getHistoryRecords(sectionQuery);
      
      let lastHistoryRecord = '发布';
      
      if (historyRecords && historyRecords.length) {
        const historyLength = historyRecords.length;
        lastHistoryRecord = historyRecords[historyLength - 1].modifiedType;
      }

      return lastHistoryRecord;
    }

    async submitCodeAnswer(sectionQuery, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { userID, chapterName, sectionName, courseSlug, studentAnswer } = sectionQuery;

      const course = await ctx.model.Course.findOne({
        where: {
          courseSlug, 
        },
        raw: true,
        transaction
      });

      if (!course) {
        throw new Error(`当前课程 ${sectionName} 不存在`);
      }

      const { id: courseID } = course;
      const section = await ctx.model.Section.findOne({
        where: {
          courseID,
          chapterName,
          sectionName
        },
        raw: true,
        transaction
      });

      if (!section) {
        throw new Error(`当前课程 ${sectionName} 不存在`);
      }

      const { id: sectionID, ext } = section;

      const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
      const fileContent = await ctx.service.course.getFile(path, true);
      if (!fileContent) {
        throw new Error(`当前课程文件 ${sectionName} 不存在`);
      }

      const { questions } = fileContent;
      const question = questions[0];
      if (!question) {
        throw new Error('当前课程题目不存在');
      }

      const judgeResult = {};
      const { answer = {} } = question;
      const answerKeys = Object.keys(answer);

      // 分数统计
      const totalScore = answerKeys.length;
      if (!totalScore) {
        throw new Error('需设置当前题目填空项');
      }

      let passCount = 0;

      for (const answerKey in answer) {
        if (!judgeResult[answerKey]) {
          judgeResult[answerKey] = {
            status: false,
            studentAnswer: null,
          };
        }

        if (!studentAnswer) {
          continue;
        }

        let answers = answer[answerKey];
        let studentAnswerContent = studentAnswer[answerKey];

        if (!studentAnswerContent) {
          continue;
        }

        // 记录学生答案
        judgeResult[answerKey].studentAnswer = studentAnswer[answerKey];
        
        // 判分去除空格，字符串内空格除外
        answers = answers.map(ans => {
          return handleTrimStr(ans);
        });

        studentAnswerContent = handleTrimStr(studentAnswerContent);

        const hasAnswer = answers.indexOf(studentAnswerContent) !== -1;

        // 记录判分结果
        if (hasAnswer) {
          judgeResult[answerKey].status = true;
          passCount++;
        }
      }

      // 更新记录
      let dataRecords = [{
        userID,
        sectionID,
        totalScore,
        passCount,
        record: null,
      }];

      let submitTimes = null;

      // 修改提交答案及提交次数
      const sectionRecord = await model.SectionRecord.findOne({
        where: {
          sectionID, 
          userID
        },
        raw: true,
        transaction
      });

      if (!sectionRecord) {
        submitTimes = 1;

        dataRecords[0].record = {
          ...judgeResult,
          submitTimes,
        };
      } else {
        let lastSubmitTimes = sectionRecord.record && sectionRecord.record.submitTimes ? sectionRecord.record.submitTimes : 0;
        submitTimes = lastSubmitTimes + 1;

        dataRecords[0] = {
          ...sectionRecord,
          totalScore,
          passCount,
          record: {
            ...judgeResult,
            submitTimes,
          }
        };
      }

      await model.SectionRecord.bulkCreate(dataRecords, {
        updateOnDuplicate: ['record', 'totalScore', 'passCount'],
        transaction
      });

      return { judgeResult, submitTimes, totalScore, passCount };
    }

    async getSectionContent(sectionIDs) {
      const { ctx } = this;
      const { model } = ctx;

      const sections = await model.Section.findAll({
        where: {
          id: {
            [Op.in]: sectionIDs
          }
        },
        raw: true
      });

      if (!sections || !sections.length) {
        throw new Error('当前课程章节不存在');
      }

      const courseIds = sections.map(section => section.courseID);

      const courses = await model.Course.findAll({
        where: {
          id: {
            [Op.in]: courseIds
          }
        },
        raw: true
      });

      if (!courses || !courses.length) {
        throw new Error('当前课程不存在');
      }

      let allContent = [];
      for (const section of sections) {
        const { courseID, chapterName, sectionName, ext, id: sectionID } = section;
        // 目前仅限ai课程
        if (ext !== 'ipynb') {
          continue;
        }

        const curCourse = courses.find(course => course.id === courseID);
        if (!curCourse) {
          console.error(`当前课程${courseID}-${sectionID}不存在`);
          continue;
        }

        const { courseSlug } = curCourse;

        const path = `${app.config.file.dir}/${this.ctx.schoolSlug}/course/${courseSlug}/${chapterName}/${sectionName}.${ext}`;
        const fileContent = await ctx.service.course.getFile(path, true);
        if (!fileContent) {
          throw new Error('当前课程文件不存在');
        }

        // 拼接所有markdown内容返回文本
        const { cells } = fileContent;
        let content = '';
        if (cells && cells.length) {
          for (const cell of cells) {
            const { source, cell_type } = cell;
            if (cell_type !== 'markdown') {
              continue;
            }

            content += source.join('');
          }
        }

        allContent.push(content);
      }

      const allContentStr = allContent.join('\n');

      return { content: allContentStr };
    }
  }

  return SectionService;
};