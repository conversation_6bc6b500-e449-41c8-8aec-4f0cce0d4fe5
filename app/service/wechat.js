
// const md5 = require('md5');
const fetch = require('node-fetch');
const moment = require('moment');

module.exports = app => {

  class WechtService extends app.Service {
    // 获取微信用户绑定信息
    async getWechatBindInfo(id, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;

      return await model.User.findOne({
        where: { id: id },
        transaction,
      });
    }

    // 设置微信用户绑定信息
    async setWechatBindInfo(id, openID, wechatInfo, transaction = false) {
      const { ctx } = this;
      const { model } = ctx;

      await model.User.update({ wechatInfo, openID }, {
        where: { id: id },
        transaction,
      });

      // 上报至management-api
      // const { managementApi } = ctx.app.config;

      const data = await model.User.findOne({
        where: { id: id },
        transaction,
      });
      
      // const wechatInfo = {
      //   openID: data.openid,
      //   name: data.wechatInfo.nickname,
      //   avatar: data.wechatInfo.headimgurl,
      //   province: data.wechatInfo.province,
      //   city: data.wechatInfo.city,
      //   content: data.wechatInfo.content,
      // }

      // const response = await ctx.curl(managementApi + '/api/wechat/setWechatBindInfo', {
      //   method: 'POST',
      //   data: wechatInfo,
      //   dataType: 'json',
      //   contentType: 'json',
      // });

      return data
    }

    // 发送绑定二维码申请
    async getWeChatCode(condition) {
      const { ctx, service } = this;
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const node = {
        app_id: app.config.managermentAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        ...condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp, 'managermentAPI');
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.thirdPart.getSingAndNode(cookieNode, timestamp);

      // response = await service.thirdPart.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      // const loginResponse = await fetch(`http://127.0.0.1:7006/thridParty/getWeChatCode`, {
      const loginResponse = await fetch(`${app.config.managerment}/management-api/thridParty/getWeChatCode`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          ...condition,
          app_id: node.app_id,
          timestamp: node.timestamp,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      console.log('res:',res, `${app.config.managerment}/management-api/thridParty/getWeChatCode`)
      if (res.code) {
        throw new Error(res.message);
      }

      this.ctx.session.wechatState = res.data.state;
      
      return res.data;
    }

    // 发送登录二维码申请
    async getWeChatLoginCode(condition) {
      const { ctx, service } = this;
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const node = {
        app_id: app.config.managermentAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        ...condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp, 'managermentAPI');
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.thirdPart.getSingAndNode(cookieNode, timestamp);

      // response = await service.thirdPart.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      // const loginResponse = await fetch(`http://127.0.0.1:7006/thridParty/getWeChatLoginCode`, {
      const loginResponse = await fetch(`${app.config.managerment}/management-api/thridParty/getWeChatLoginCode`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          ...condition,
          app_id: node.app_id,
          timestamp: node.timestamp,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      console.log('res:',res, `${app.config.managerment}/management-api/thridParty/getWeChatLoginCode`)
      if (res.code) {
        throw new Error(res.message);
      }

      ctx.session.wechatState = res.data;
      
      return res.data;
    }

    // 根据openID获取用户信息
    async getUserInfoByOpenID(openID, system, qrcodeState) {
      const { ctx } = this;
      const { model } = this.ctx;

      let user =  await model.User.findOne({
        attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority', 'openID', 'wechatInfo'], 
        where: {openID},
        raw: true,
      });

      // 用户不存在或已经被停用
      if (!user) {
        return null;
      }

      if (user.state == 'close') {
        throw new Error(`此账号已被停用！`)
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      })
      
      user.teamIDs = teams.map(team => team.teamID);

      // 返回是否允许学生修改密码配置项
      const allowStudentChangePasswordConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'ban'
        },
        raw: true
      });

      if (allowStudentChangePasswordConfigRecord) {
        user.allowStudentChangePassword = allowStudentChangePasswordConfigRecord.value;
      }
      else {
        user.allowStudentChangePassword = false;
      }

      // 返回是否允许学生修改名称配置项
      const allowStudentChangeNameConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'banName'
        },
        raw: true
      });

      if (allowStudentChangeNameConfigRecord) {
        user.allowStudentChangeName = allowStudentChangeNameConfigRecord.value;
      }
      else {
        user.allowStudentChangeName = false;
      }

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      user.signIn = signIn;


      const schoolNameResults = await model.SystemConfig.findOne({
        where: { key: 'SchoolName' },
        raw: true,
        // transaction,
      });
      user.schoolName = schoolNameResults.value;

      // 取当前用户当前时间下的授权，并整理
      const permissionData = await ctx.service.systemConfig.getInfomationByKey('enableFunction');
      const permissionResult = permissionData ? permissionData.value : {};
      // 按照授权，做成索引
      const permission = {
        train: {
          online: '未授权',
          offline: '未授权',
        },
        course: {
          online: '未授权' // '未授权' '试用' '已授权'
        }
      }

      for (const permissionRow of permissionResult.trainWebTime) {
        // 首先确认是否为在当前时间段内
        if (!permissionRow.endTime || moment().isAfter(permissionRow.endTime)) {
          continue;
        }
        if (!permissionRow.startTime || moment().isBefore(permissionRow.startTime)) {
          continue;
        }
        // 查看是否有课程权限
        if (permissionRow.permission && permissionRow.permission.indexOf('课程') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.course.online === '未授权') {
              permission.course.online = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.course.online = '已授权';
          }
        }

        // 查看是否有在线训练权限
        if (permissionRow.permission && permissionRow.permission.indexOf('在线训练') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.train.online === '未授权') {
              permission.train.online = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.train.online = '已授权';
          }
        }

        // 查看是否有机房训练权限
        if (permissionRow.permission && permissionRow.permission.indexOf('机房训练') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.train.offline === '未授权') {
              permission.train.offline = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.train.offline = '已授权';
          }
        }

        // if (permissionRow.permission) {
        //   for(const permissionRowItem of permissionRow.permission) {
        //     if (!allPermissionMap[permissionRowItem.permission]) {
        //       allPermissionMap[permissionRowItem.permission] = {};
        //     }
        //     const dayDiff = moment(node.endTime).endOf('days').diff(moment().startOf('days'), 'day');
        //     // 取结束时间最久的一个
        //     if (allPermissionMap[permissionRowItem.permission].dayDiff && allPermissionMap[permissionRowItem.permission].dayDiff < dayDiff) {
        //       continue;
        //     }

        //     allPermissionMap[permissionRowItem.permission] = {
        //       "origin": permissionRow.origin,
        //       "coursePeople": permissionRow.coursePeople,
        //       "trainPeople": permissionRow.trainPeople,
        //       "endTime": permissionRow.endTime,
        //       "startTime": permissionRow.startTime,
        //       "dayDiff": dayDiff,
        //     }
        //   }
        // }
      }

      user.permissionData = permissionResult;
      user.permission = permission;

      const MaintainNotice = await ctx.service.systemConfig.getInfomationByKey('MaintainNotice');
      user.MaintainNotice = MaintainNotice ? MaintainNotice.value : null;

      // 发送消息，通知前台登录成功
      app.client.publish(`/wechatLogin/${qrcodeState}`, {
        status: `${system}_success`,
      });

      return user;
    }

    // 根据用户名获取用户信息
    async getUserInfoByUsername(username) {
      const { ctx } = this;
      const { model } = this.ctx;

      let user =  await model.User.findOne({
        attributes: ['id', 'username', 'name', 'avatar', 'state', 'adminAuthority', 'openID', 'wechatInfo'], 
        where: {username},
        raw: true,
      });

      // 用户不存在或已经被停用
      if (!user) {
        return null;
      }

      if (user.state == 'close') {
        throw new Error(`此账号已被停用！`)
      }

      // 寻找登录用户的所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID: user.id }
      })
      
      user.teamIDs = teams.map(team => team.teamID);

      // 返回是否允许学生修改密码配置项
      const allowStudentChangePasswordConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'ban'
        },
        raw: true
      });

      if (allowStudentChangePasswordConfigRecord) {
        user.allowStudentChangePassword = allowStudentChangePasswordConfigRecord.value;
      }
      else {
        user.allowStudentChangePassword = false;
      }

      // 返回是否允许学生修改名称配置项
      const allowStudentChangeNameConfigRecord = await model.SystemConfig.findOne({
        where: {
          key: 'banName'
        },
        raw: true
      });

      if (allowStudentChangeNameConfigRecord) {
        user.allowStudentChangeName = allowStudentChangeNameConfigRecord.value;
      }
      else {
        user.allowStudentChangeName = false;
      }

      // 记录用户登录session
      const signIn = await ctx.service.user.recordUserSession(user.id);
      user.signIn = signIn;


      const schoolNameResults = await model.SystemConfig.findOne({
        where: { key: 'SchoolName' },
        raw: true,
        // transaction,
      });
      user.schoolName = schoolNameResults.value;

      // 取当前用户当前时间下的授权，并整理
      const permissionData = await ctx.service.systemConfig.getInfomationByKey('enableFunction');
      const permissionResult = permissionData ? permissionData.value : {};
      // 按照授权，做成索引
      const permission = {
        train: {
          online: '未授权',
          offline: '未授权',
        },
        course: {
          online: '未授权' // '未授权' '试用' '已授权'
        }
      }

      for (const permissionRow of permissionResult.trainWebTime) {
        // 首先确认是否为在当前时间段内
        if (!permissionRow.endTime || moment().isAfter(permissionRow.endTime)) {
          continue;
        }
        if (!permissionRow.startTime || moment().isBefore(permissionRow.startTime)) {
          continue;
        }
        // 查看是否有课程权限
        if (permissionRow.permission && permissionRow.permission.indexOf('课程') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.course.online === '未授权') {
              permission.course.online = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.course.online = '已授权';
          }
        }

        // 查看是否有在线训练权限
        if (permissionRow.permission && permissionRow.permission.indexOf('在线训练') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.train.online === '未授权') {
              permission.train.online = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.train.online = '已授权';
          }
        }

        // 查看是否有机房训练权限
        if (permissionRow.permission && permissionRow.permission.indexOf('机房训练') !== -1) {
          // 判断是否为试用
          if (permissionRow.origin === '试用') {
            // 只有在原有是未授权情况下，再改成试用
            if (permission.train.offline === '未授权') {
              permission.train.offline = '试用';
            }
          } else {
            // 这里，目前来源里，只有试用、合同、订单、测试，所以，只要不是试用，就是已授权
            permission.train.offline = '已授权';
          }
        }

        // if (permissionRow.permission) {
        //   for(const permissionRowItem of permissionRow.permission) {
        //     if (!allPermissionMap[permissionRowItem.permission]) {
        //       allPermissionMap[permissionRowItem.permission] = {};
        //     }
        //     const dayDiff = moment(node.endTime).endOf('days').diff(moment().startOf('days'), 'day');
        //     // 取结束时间最久的一个
        //     if (allPermissionMap[permissionRowItem.permission].dayDiff && allPermissionMap[permissionRowItem.permission].dayDiff < dayDiff) {
        //       continue;
        //     }

        //     allPermissionMap[permissionRowItem.permission] = {
        //       "origin": permissionRow.origin,
        //       "coursePeople": permissionRow.coursePeople,
        //       "trainPeople": permissionRow.trainPeople,
        //       "endTime": permissionRow.endTime,
        //       "startTime": permissionRow.startTime,
        //       "dayDiff": dayDiff,
        //     }
        //   }
        // }
      }

      user.permissionData = permissionResult;
      user.permission = permission;

      const MaintainNotice = await ctx.service.systemConfig.getInfomationByKey('MaintainNotice');
      user.MaintainNotice = MaintainNotice ? MaintainNotice.value : null;

      return user;
    }

    // 解绑微信
    async unbindWechat(userID, transaction = false) {
      const { ctx } = this;
      const { model } = this.ctx;

      const user = await model.User.findOne({
        where: { id: userID },
        raw: true,
        transaction,
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 上报解绑事件
      // 获取时间戳
      const timestamp = await this.ctx.service.thirdPart.getTimestampAsSecondFormat(new Date());
      // console.log('app.config:',JSON.stringify(app.config.platHxrMainAPI))

      const condition = {
        openID: user.openID,
        system: '氦星人信息技术教育平台管理后台'
      }

      const node = {
        app_id: app.config.managermentAPI.app_id,
        // secret: app.config.platHxrMainAPI.secret,
        ...condition,
        timestamp
      };
      // console.log('node:',node)
      // 获取键值对
      const sign = await this.ctx.service.thirdPart.getSingAndNode(node, timestamp, 'managermentAPI');
      
      // const cookieNode = {
      //   // app_id: app.config.platHxrMainAPI.app_id,
      //   // secret: app.config.platHxrMainAPI.secret,
      //   timestamp
      // };
      // // 获取键值对
      // const cookieSign = await service.thirdPart.getSingAndNode(cookieNode, timestamp);

      // response = await service.thirdPart.uploadToHxrMain({ ...node, sign });
      // const data = { ...node, sign };
      // console.log('node:',node)
        
      // const loginResponse = await fetch(`http://127.0.0.1:7006/thridParty/cancelOpenID`, {
      const loginResponse = await fetch(`${app.config.managerment}/management-api/thridParty/cancelOpenID`, {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({
          ...condition,
          app_id: node.app_id,
          timestamp: node.timestamp,
          sign,
          // secret,
          // user
        }),
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=utf-8',
          // 'x-csrf-token': csrfToken,
          // 'Cookie':`csrfToken=${csrfToken}`
        }
      });
      const res = await loginResponse.json();
      console.log('res:',res, `${app.config.managerment}/management-api/thridParty/cancelOpenID`)
      if (res.code) {
        throw new Error(res.message);
      }

      const updateResult = await model.User.update({
        openID: null,
        wechatInfo: null,
      }, {
        where: { id: userID },
        transaction,
      });

      // if (updateResult[0] == 0) {
      //   throw new Error('解绑失败');
      // }

      return true;
    }
  }

  return WechtService;
}