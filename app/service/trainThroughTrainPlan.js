const { Op, QueryTypes } = require('sequelize');
const sequelize = require('sequelize');
const fs = require("mz/fs");
const path = require('path');
const moment = require('moment');
const { encrypt } = require('../utils/crypto.js');
const { getTotalScore, adjustQuestionScore, getDiscriminative } = require('../utils/question');
const { FREETEMPLATE, orderMap } = require('../utils/refer');
const { judgeQuestion, judgeQuestionbyID, submitQuestion, getNewFreeModeRecord, getNewTrainModeRecord } = require('../utils/judge');

//! 将srcDir文件下的文件、文件夹递归的复制到tarDir下
const copyFolder = function(srcDir, tarDir, cb) {
  fs.readdir(srcDir, function(err, files) {
    let count = 0;
    const checkEnd = function() {
      ++count == files && files.length && cb && cb();
    }

    if (err) {
      checkEnd();
      return;
    }

    files.forEach(function(file) {
      const srcPath = path.join(srcDir, file);
      const tarPath = path.join(tarDir, file);

      fs.stat(srcPath, function(err, stats) {
        if (stats.isDirectory()) {
          // console.log('mkdir', tarPath);
          fs.mkdir(tarPath, function(err) {
            if (err) {
              console.log(err);
              return;
            }

            copyFolder(srcPath, tarPath, checkEnd);
          });
        } else {
          fs.copyFile(srcPath, tarPath, checkEnd);
        }
      });
    });

    //为空时直接回调
    files && files.length && files.length === 0 && cb && cb();
  });
}

function getQuestionStatistics({ questionID, questionType, questionDetail, studentAnswer, studentRecords, allAnswer }) {
  let answerMap = {};
  let jsonMap = [];
  if (!questionDetail) {
    return ['Access操作题', 'WPS表格操作题'].includes(questionType) ? jsonMap : answerMap;
  }
  switch(questionType) {
    case '单选题':
      {
        const { options = [] } = questionDetail;
        for (const option of options) {
          const selectAnswers = studentAnswer && studentAnswer.length && Array.isArray(studentAnswer) ? studentAnswer.filter(i => i && i.answer && i.answer === option.key) : [];
          answerMap[option.key] = selectAnswers ? selectAnswers.length : 0;
        }
      }
      break;
    case '多选题':
      {
        const { options = [] } = questionDetail;
        for (const option of options) {
          const selectAnswers = studentAnswer.filter(i => i && i.answer && i.answer.includes(option.key));
          answerMap[option.key] = selectAnswers ? selectAnswers.length : 0;
        }
      }
      break;
    case '填空题':
    case '编程填空题':
    case '选择填空题':
      {
        const { answerKeys = [] } = questionDetail;
        
        for (const answerKey of answerKeys) {
          if (!answerMap[answerKey]) {
            answerMap[answerKey] = {};
          }

          for (const record of studentAnswer) {
            if (!record || !record.answer || !record.answer[answerKey]) {
              continue;
            }

            if (!answerMap[answerKey][record.answer[answerKey]]) {
              answerMap[answerKey][record.answer[answerKey]] = 1;
            } else {
              answerMap[answerKey][record.answer[answerKey]]++;
            }
          }
        }
      }
      break;
    case 'Access操作题':
    case 'WPS表格操作题':
      {
        const steps = allAnswer[questionID];
        for (let i = 0; i < steps.length; i++) {
          const finishUsers = studentAnswer.filter(ans =>  ans.answer && ans.answer[i] && ans.answer[i].status && ans.answer[i].status === 'finish');
          const errorUsers = studentRecords.length - finishUsers.length;
          jsonMap.push({
            finish: finishUsers.length,
            error: errorUsers,
          });
        }
      }
      break;
    case '综合题':
      {
        for (const subQuestion of questionDetail) {
          const { UUID, questionType: subType, questionDetail: subDetail } = subQuestion;
          // console.log('-=======================subQuestion============================', subQuestion)
          if (subType === '文本') {
            continue;
          }
          const subAnswer = studentAnswer.map(i =>  i[UUID]);
          // console.log('-=======================studentAnswer============================', studentAnswer, subAnswer)
          answerMap[UUID] = getQuestionStatistics({ questionID: UUID, questionType: subType,  questionDetail: subDetail, studentAnswer: subAnswer });
        }
      }
      break;
    case '在线编程评测题':
      {
        
      }
      break;
    default:
      throw new Error(`获取题目统计结果，不支持的题型${questionType}`);
  }

  return ['Access操作题', 'WPS表格操作题'].includes(questionType) ? jsonMap : answerMap;
}

function getContentStatistics(content, allAnswer, studentRecords) {
  let errorQuestionCount = 0;

  for (const block of content) {
    const { questions } = block;
    for (const question of questions) {
      const { id: questionID, questionType, questionDetail } = question;
      if (!questionDetail) {
        continue;
      }
      // 获取题目错误人数
      let errorNumber = {};
      let allNumber = {};
      let noAnswerNumber = {};
      // const allNumberMap = {};

      // 综合题每小题未答题人数
      const noAnswerMap = {};

      const studentAnswer = [];
      studentRecords.forEach((studentRecord) => {
        const { record } = studentRecord;

        // console.log('studentRecord.userID:',studentRecord)
        // 全部人数
        // if (!allNumberMap[studentRecord.userID]) {
        //   allNumber++;
        //   allNumberMap[studentRecord.userID] = true
        // }
        if(!allNumber[studentRecord.trainID]) {
          allNumber[studentRecord.trainID] = 0
        }
        allNumber[studentRecord.trainID] += 1;

        if(!errorNumber[studentRecord.trainID]) {
          errorNumber[studentRecord.trainID] = 0
        }
        if(!noAnswerNumber[studentRecord.trainID]) {
          noAnswerNumber[studentRecord.trainID] = 0
        }

        // 未答题作为错误人数
        if (questionType !== '综合题' && (!record || !record[questionID])) {
          noAnswerNumber[studentRecord.trainID]++;
          return;
        }

        if (questionType === '综合题') {
          if (!record || !record[questionID]) {
            noAnswerNumber[studentRecord.trainID]++;

            // const answerKeys = Object.keys(allAnswer[questionID]);
            // answerKeys.forEach(uuid => {
            //   if (!noAnswerMap[uuid]) {
            //     noAnswerMap[uuid] = 1;
            //   } else {
            //     noAnswerMap[uuid]++;
            //   }
            // });
            return;
          }
          
          const questionKeys = Object.keys(allAnswer[questionID]);

          if (!questionKeys || !questionKeys.length) {
            noAnswerNumber[studentRecord.trainID]++;
            // 计算综合题每小题未答题人数
            questionKeys.forEach(uuid => {
              if (!noAnswerMap[uuid]) {
                noAnswerMap[uuid] = 1;
              } else {
                noAnswerMap[uuid]++;
              }
            });
          }

          // 计算综合题每小题未答题人数
          for (const uuid in allAnswer[questionID]) {
            if(!noAnswerMap[uuid]){
              noAnswerMap[uuid] = 0
            }
            if (!record || !record[questionID] || !record[questionID][uuid] || !record[questionID][uuid].answer) {
              noAnswerMap[uuid]++;
            }
          }

          const allPass = questionKeys.every(uuid => record && record[questionID] && record[questionID][uuid] && record[questionID][uuid].result);
          if (!allPass) {
            errorNumber[studentRecord.trainID]++;
          }
        } else {
          if (record && record[questionID] && !record[questionID].answer) {
            noAnswerNumber[studentRecord.trainID]++;
          }

          if (record && record[questionID] && record[questionID].answer && !record[questionID].result) {
            errorNumber[studentRecord.trainID]++;
          }
        }
        if(record && record[questionID]){
          studentAnswer.push(record[questionID]);
        }
        
      });

      // 未答题 答错 全部
      question.noAnswerNumber = noAnswerNumber[block.trainID];
      question.errorNumber = errorNumber[block.trainID];
      question.allNumber = allNumber[block.trainID];

      // 综合题每小题未答题人数
      question.noAnswerMap = noAnswerMap;
      // console.log('question:',question)

      // 计算总的错题个数
      if (errorNumber > 0) {
        errorQuestionCount++;
      }

      // 获取题目结果统计
      question.answerMap = getQuestionStatistics({ questionID, questionType, questionDetail, studentAnswer, studentRecords, allAnswer });

      // 合并参考答案
      if (questionType !== '综合题') {
        question.answer = allAnswer[questionID];
      } else {
        questionDetail.forEach((subQuestion) => {
          subQuestion.answer = allAnswer[questionID][subQuestion.UUID];
        });
      }
    }
  }

  return { content, errorQuestionCount };
}

function calculateBasicStatistics(statisticsDatas) {
  let totalScore = 0;
  let totalVarianceScore = 0;

  const allCount = statisticsDatas.length;

  if (!allCount) {
    return {
      average: 0,
      variance: 0,
    }
  }

  for(const row of statisticsDatas) {
    if(row.score) {
      totalScore += row.score ? parseFloat(row.score) : 0;
    }
  }


  const average = (totalScore ? parseFloat(totalScore) : 0) / allCount;
  for (const row of statisticsDatas) {
    if(row.score) {
      totalVarianceScore += (average - parseFloat(row.score)) * (average - parseFloat(row.score));
    }
  }
  const variance = totalVarianceScore / allCount;

  return {
    average: parseInt(average * 100 + 0.5, 10) / 100,
    variance: parseInt(variance * 100 + 0.5, 10) / 100,
  }
}

function getQuestionStatus(questionID, questionType, record) {
  if (!record || !record[questionID]) {
    return false;
  }

  if (questionType === '综合题') {
    const questionKeys = Object.keys(record[questionID]);
    const allPass = questionKeys.every(uuid => record[questionID][uuid] && record[questionID][uuid].result);
    if (!allPass) {
      return true;
    }
  } else {
    if (record && record[questionID] && record[questionID].answer && !record[questionID].result) {
      return true;
    }
  }
}

// 根据正确率决定题目的难度设置值 简单： 正确率 >= 0.85 适中： 0.85>正确率> 0.5 较难：正确率 < 0.5
function difficultyConfigValue(value){
  const num = parseFloat(value.toFixed(2));
  if(num >= 0.85){
    return '简单';
  }
  if(0.85 > num  && num >= 0.5){
    return '适中';
  }
  if(num < 0.5){
    return '较难';
  }
}


function getQuestionsStatistics(content, studentRecords) {
  for (const block of content) {
    const { questions } = block;
    for (const question of questions) {
      const { id: questionID, questionType } = question;

      // 获取题目错误人数
      let errorNumber = 0;
      let allNumber = 0;

      studentRecords.forEach((studentRecord) => {
        const { record } = studentRecord;

        // 全部人数
        allNumber++;
        let isError = getQuestionStatus(questionID, questionType, record);
        if (isError) {
          errorNumber++;
        }
      });

      if (allNumber < 50) {
        continue;
      }

      // 计算题目难度
      question.difficulty = errorNumber / allNumber;
      // 给题目难度未鉴定的题目设置难度
      if(question.difficultyConfig === '未鉴定'){
        question.difficultyConfig = difficultyConfigValue( (allNumber-errorNumber) / allNumber );
      }
      // 计算题目区分度 https://www.wsbookshow.com/uploads/bookfile/201202/9787508493381_1.pdf
      // 根据分数从高到低排序
      const studentScores = studentRecords.sort((a, b) => b.score - a.score);

      // 高分组为前27%，低分组为后27%
      const highIndex = Math.ceil(allNumber * 0.27);
      const lowIndex = Math.floor(allNumber * (1 - 0.27)) - 1;
      const highGroup = studentScores.slice(0, highIndex);
      const lowGroup = studentScores.slice(lowIndex, allNumber - 1);

      if (highGroup.length && lowGroup.length) {
        let highErrorNumber = 0;
        let lowErrorNumber = 0;
        highGroup.forEach((item) => {
          let isError = getQuestionStatus(questionID, questionType, item.record);
          if (isError) {
            highErrorNumber++;
          }
        });

        lowGroup.forEach((item) => {
          let isError = getQuestionStatus(questionID, questionType, item.record);
          if (isError) {
            lowErrorNumber++;
          }
        });

        const PHigh = highErrorNumber / highGroup.length;
        const PLow = lowErrorNumber / lowGroup.length;
        question.discriminative = PHigh - PLow;
      }
    }
  }

  return { content };
}

// 获取得分（有initScore用initScore，没有用score）
function getScore(contentRow) {
//   console.log(contentRow)
  if (!contentRow) {
    return 0
  }
  if(contentRow.initScore !== undefined) {
    return contentRow.initScore
  }
  if (contentRow.score) {
    return contentRow.score
  }
  return 0;
}

// 计数器相加
function addFunction(s, num) {
  if (!s) {
    s = 0;
  }

  s += num;
  return s
}

// 判断小题正确与否（有initResult用initResult，没有用result）
function getResult(contentRow) {
  if(contentRow.initResult !== undefined) {
    return contentRow.initResult
  }

  return !!contentRow.result
}

module.exports = app => {
  const redis = app.redis.get('train');

  class trainThroughTrainPlanService extends app.Service {
    async uploadTrainFile(trainPlanID, subFilePath, fileName, fileContent) {
      const filepath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}${subFilePath}`;
      const exist = await fs.exists(filepath);
      if (!exist) {
        await this.ctx.service.file.mkdirs(filepath);
      }
      
      return await fs.writeFile(`${filepath}/${fileName}`, fileContent);
    }
      
    // 加载文件数据
    async getFile(pathDir) {
      const result = await fs.readFile(pathDir, 'utf-8');

      let resultData = result;
      try {
        resultData = JSON.parse(result);
      } catch {
        resultData = result;
      }
      // 检查当前文件是文件还是目录
      return resultData;
    }

    // 获取文件所需训练信息
    async getTrainContentToFile(id, transaction = false ,paranoid = true) {
      const { model } = this.ctx;
      const { Train, Questions } = model

      // console.log('model:',model)
      const result = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction,
        paranoid
      });

      if (!result) {
        throw new Error(`训练 ${id} 不存在`);
      }

      const questionResults = [];
      const answerResults = {};

      let questionIDs = [];

      const { template, templateName, content } = result;

      if (content) {
        for(const key in content) {
          const row = content[key];

          if (!row || !row.questionIDs) {
            continue;
          }

          questionIDs = questionIDs.concat(row.questionIDs);
          row.questions = [];
          const questionsData = await Questions.findAll({
            where: { id: { [Op.in]: row.questionIDs } },
            raw: true,
            transaction,
          });

          // 自由组卷模式下，题目分数根据模板调整，修改题目answer中的score
          if (templateName === FREETEMPLATE) {
            const { score } = template[key];
            adjustQuestionScore(score, questionsData);
          }
          
          for (const questionID of row.questionIDs) {
            const questionRow = questionsData.find(subRow => {
              const subRowData = subRow && subRow.dataValues ? subRow.dataValues : subRow;
              if(subRowData.id === parseInt(questionID)) {
                return true;
              }
              return false
            });
            if (!questionRow) {
              continue;
            }

            const { answer, ...questionOtherRow } = questionRow;
            const { questionType, questionDetail } = questionOtherRow;

            answerResults[questionRow.id] = answer;

            // 记录题目总分 totalScore
            // 单选题不计算，综合题计算小题分数
            const totalScore = getTotalScore({ questionType, answer, questionDetail });

            row.questions.push({ ...questionOtherRow, totalScore });
          }

          questionResults.push({
            ...result.template[key],
            questions: row.questions,
          })
        }
      }

      return {
        questionResults,
        answerResults,
        questionIDs,
      }
    }

    // 获取文件所需训练信息 机房版
    async getComputerTrainContentToFile(id, transaction = false) {
      const { model } = this.ctx;
      const { Train, Questions } = model

      const result = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction,
      });

      if (!result) {
        throw new Error(`训练 ${id} 不存在`);
      }

      const { isFinish } = result;
      if (!isFinish) {
        throw new Error(`训练 ${id} 未完成题目配置`);
      }

      const questionResults = [];
      const answerResults = {};

      let questionIDs = [];

      const { content, template } = result;

      if (content) {
        for(const key in content) {
          const row = content[key];

          if (!row || !row.questionIDs) {
            continue;
          }

          questionIDs = questionIDs.concat(row.questionIDs);
          row.questions = [];
          const questionsData = await Questions.findAll({
            where: { id: { [Op.in]: row.questionIDs } },
            include: [{
              model: model.Tag,
              as: 'tags',
              attributes: ['id', 'tagName'],
            }],
            transaction,
          })
          
          for (const questionID of row.questionIDs) {
            let questionRow = questionsData.find(subRow => {
              const subRowData = subRow && subRow.dataValues ? subRow.dataValues : subRow;
              if(subRowData.id === parseInt(questionID)) {
                return true;
              }
              return false
            });
            if (!questionRow) {
              continue;
            }

            questionRow = questionRow.dataValues ? questionRow.dataValues : questionRow;

            // 获取标签
            questionRow.tags = questionRow.tags ? questionRow.tags.map(rowTag => {
              const tagRowData = rowTag.dataValues ? rowTag.dataValues : rowTag;
              const { id, tagName } = tagRowData;
              return { id, tagName };
            }) : [];

            const { answer, questionDetail, questionType, ...questionOtherRow } = questionRow;

            let newAnswer = { questionType, answer, questionDetail };
            switch(questionType) {
              case '单选题':
              case '多选题':
                newAnswer['score'] = template[key] && template[key].score ? template[key].score : 2;
                break;
              case 'Access操作题':
                {
                  const { instructions } = questionDetail;

                  const answerInstructions = [];
                  questionDetail.instructions = instructions.map((instruction, index) => {
                    const { description, judgeFiles, parseDiffScript, originFile } = instruction;

                    answerInstructions.push({ originFile, judgeFiles, parseDiffScript, description });

                    // 解析题解文件路径
                    let guide = null;
                    if (index > 0) {
                      guide = answer[index] && answer[index].guide ? answer[index].guide : null;
                    }

                    let extraQuestionAnalysis = [];
                    if (guide) {
                      extraQuestionAnalysis = guide.match(/\.\.\/assets\/[^\"]+/g);
                    }

                    return { description, extraQuestionAnalysis };
                  });
                  newAnswer['instructions'] = answerInstructions;
                }
                break;
              case 'WPS表格操作题':
                {
                  const { instructions } = questionDetail;

                  const answerInstructions = [];
                  questionDetail.instructions = instructions.map((instruction, index) => {
                    const { description, parseDiffScript } = instruction;

                    answerInstructions.push({ parseDiffScript, description });

                    const guide = answer[index] && answer[index].guide ? answer[index].guide : null;
                    let extraQuestionAnalysis = [];
                    if (guide) {
                      extraQuestionAnalysis = guide.match(/\.\.\/assets\/[^\"]+/g);
                    }

                    return { description, extraQuestionAnalysis };
                  });
                  newAnswer['instructions'] = answerInstructions;
                }
                break;
              case '综合题':
                {
                  for (const subQuestion of questionDetail) {
                    const { UUID, questionType } = subQuestion;

                    if (questionType === '文本') {
                      continue;
                    }

                    if (answer[UUID]) {
                      answer[UUID].questionType = questionType;
                    }
                  }
                }
                break;
              case '填空题':
              case '编程填空题':
                break;
              default:
                throw new Error(`获取机房文件训练信息，不支持的题型${questionType}`);
            }

            answerResults[questionRow.id] = newAnswer;

            const question = { ...questionOtherRow, answer, questionType, questionDetail };

            row.questions.push(question);
          }

          questionResults.push({
            ...template[key],
            questions: row.questions,
          })
        }
      }

      return {
        questionResults,
        answerResults: encrypt(JSON.stringify(answerResults)),
        // answerResults,
        questionIDs,
      }
    }

    // 从文件获取所需训练信息
    async getTrainContentFromQuestionFile(id, trainPlanID, transaction = false) {
      const { model } = this.ctx;
      const { Train, TrainPlan, TrainUserRecord } = model;

      // 获取当前用户的ID
      const userID = this.ctx.session.user.id;

      // 查询训练计划信息
      const trainPlanResult = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        raw: true,
        transaction
      });

      // 获取用户记录
      const trainUserRecord = await TrainUserRecord.findAll({
        where: {
          userID,
          planID: trainPlanID,
        },
        raw: true,
        transaction
      })

      // 查询训练信息
      const trainResult = await Train.findOne({
        where: {
          id
        },
        raw: true,
        transaction,
      });

      // 获取训练问题和答案文件
      let result = await this.ctx.service.trainThroughTrainPlan.getFile(`${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${id}/questions.json`);
      const answer = await this.ctx.service.trainThroughTrainPlan.getFile(`${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${id}/answer.json`);
      
      // 关联答案与问题
      for(let row of result) {
        for (let question of row.questions) {
          if (question.questionType === '在线编程评测题') {
            // 
            question.answer = answer[question.id];
          }
        }
      }
      const questionResults = {
        content: [],
        template: [],
      }
      for(const row of result) {
        const { questions, ...templateRow } = row;
        questionResults.content.push({questions});
        questionResults.template.push(templateRow);
      }

      // 是否显示答案
      const { mode, status } = trainPlanResult;
      let studentStatus = null;
      if (trainUserRecord && trainUserRecord[0]) {
        const { status: statusA } = trainUserRecord[0];
        studentStatus = statusA;
      }

      let showAnswer = false;
      if (mode === '考试模式') {
        showAnswer = status === '已结束';
      } else if (mode === '训练模式') {
        showAnswer = studentStatus === '已提交';
      } else if (mode === '自由模式') {
        showAnswer = true;
      }
      // 如果需要显示答案，则将答案与问题关联
      if (showAnswer) {
        const answerResults = await this.ctx.service.trainThroughTrainPlan.getFile(`${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${id}/answer.json`);
        for(const row of questionResults.content) {
          try {
            for(const questionRow of row.questions) {
              questionRow.answer = answerResults[questionRow.id]
            }
          } catch (e) {}
        }
      }

      return {
        trainUserRecord,
        train: {
          ...trainResult,
          ...questionResults,
        },
        currentTime: new Date(),
        ...trainPlanResult,
      }
    }

    // 创建训练
    async createTrainPlan(node, transaction) {
      const { ctx } = this;
      const { model, schoolSlug } = this.ctx;
      const { TrainPlan } = model;
      const { studentTrainLists, selectTrainIDs, ...other } = node;
      const { mode } = other;

      if (!ctx.session || !ctx.session.user) {
        throw new Error('请先登录');
      }

      const { user: { id: userID } = {} } = ctx.session;
      if (!userID) {
        throw new Error('请先登录');
      }

      const result = await TrainPlan.create(other, {
        transaction,
        raw: true,
      });

      const { id: planID, startTime, endTime } = result;

      // 学生、试卷、训练计划关联
      await ctx.service.trainThroughTrainPlan.linkTrainPlanAndStudents({ studentTrainLists, planID, selectTrainIDs, teacherID: userID, mode }, transaction);

      // 创建训练文件
      await ctx.service.trainThroughTrainPlan.createTrainPlanFile({ studentTrainLists, selectTrainIDs, planID, mode }, transaction);

      // redis中记录计划信息以供定时任务调用
      try {
        await ctx.service.trainThroughTrainPlan.redisTrainPlanRecord({ schoolSlug, startTime, endTime, planID });
      } catch(e) {
        throw e;
      }

      return result;
    }

    // 修改训练 TODO 待删
    async putTrainPlan(id, node, transaction) {
      const { model } = this.ctx;
      const { TrainPlan, TrainThroughTrainPlan } = model;
      const { studentTrainLists, ...other } = node;
      const { mode } = other;

      const originData = await TrainPlan.findOne({
        where: {
          id
        },
        raw: true,
        transaction
      });

      await TrainPlan.update(other, {
        where: {
          id
        },
        transaction
      });

      const result = await TrainPlan.findOne({
        where: {
          id
        },
        transaction
      });

      // 学生、试卷、训练计划关联
      await ctx.service.trainThroughTrainPlan.linkTrainPlanAndStudents({ studentTrainLists, planID: result.id, mode }, transaction);

      const nowStudentList = await TrainThroughTrainPlan.findAll({
        where: {
          planID: result.id
        },
        group: ['trainID'],
        raw: true,
        transaction
      });

      // 状态改变时
      if (originData && nowStudentList && nowStudentList.length && (originData.status === '草稿' || !originData.status) && result.status === '报名中') {
        await ctx.service.trainThroughTrainPlan.createTrainPlanFile({ studentTrainLists: nowStudentList, planID: result.id }, transaction);

        // redis中记录计划信息以供定时任务调用
        try {
          await ctx.service.trainThroughTrainPlan.redisTrainPlanRecord({ schoolSlug: this.ctx.schoolSlug, startTime: result.startTime, endTime: result.endTime, planID: result.id });
        } catch(e) {
          throw e;
        }
      }

      return result;
    }

    // 学生、试卷、训练计划关联
    async linkTrainPlanAndStudents({ studentTrainLists, planID, selectTrainIDs, teacherID, mode }, transaction) {
      const { model } = this.ctx;
      const { TrainPlanClass, TrainThroughTrainPlan, TeamUser } = model;

      if (!studentTrainLists || !studentTrainLists.length) {
        return;
      }

      const originStudentList = await TrainThroughTrainPlan.findAll({
        where: {
          planID: planID
        },
        raw: true,
        transaction
      });

      const needDeleteArr = [];
      const needCreateArr = [];

      if (needDeleteArr.length) {
        await TrainThroughTrainPlan.destroy({ where: { id: {[Op.in]: needDeleteArr }}, transaction });
      }

      for(const row of studentTrainLists) {
        if (!row.trainID || !row.id) {
          continue;
        }

        if (mode === '自由模式') {
          if (!row.trainID.length) {
            continue;
          }
        }
        // console.log('needCreateArr:',needCreateArr);

        const originStudentRow = originStudentList.find(item => item.userID === row.id);
        if (!originStudentRow) {
          const { id, trainID } = row;
          // 自由模式 trainID 为数组
          if (mode === '自由模式') {
            for (const item of row.trainID) {
              needCreateArr.push({ planID, trainID: item, userID: id, isolate: false });
            }
          } else {
            needCreateArr.push({ planID, trainID, userID: id, isolate: false });
          }
        }
      }

      // 如果创建人未添加记录，则增加教师本人记录
      const creatorExist = studentTrainLists.find(i => i.id === teacherID);

      if (!creatorExist) {
        for (const item of selectTrainIDs) {
          needCreateArr.push({ planID, trainID: item, userID: teacherID, isolate: true });
        }
      }

      if (needCreateArr.length) {
        await TrainThroughTrainPlan.bulkCreate(needCreateArr, { transaction });
      }

      const teamResult = await TeamUser.findAll({
        raw: true,
        where: { 
          userID: {
            [Op.in]: studentTrainLists.map(e => e.id)
          }
        },
        group: ['teamID'],
      })

      await TrainPlanClass.bulkCreate(teamResult.map(row => ({
        teamID: row.teamID,
        planID: planID,
        isolate: !!row.isolate,
      })), {
        transaction,
      });
    }
    
    // 创建训练文件
    async createTrainPlanFile({ selectTrainIDs, planID }, transaction) {
      if (!selectTrainIDs || !selectTrainIDs.length) {
        return;
      }

      try {
        for (const trainID of selectTrainIDs) {
          await this.ctx.service.trainThroughTrainPlan.uploadTrainPlanFiles({ planID, trainID }, transaction);
        }
      } catch (e) {
        console.log(e, 'createTrainPlanFile')
        throw e;
      }
    }

    // 创建训练文件
    async uploadTrainPlanFiles({ planID, trainID }, transaction) {
      const trainData = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID, transaction);
      await this.ctx.service.trainThroughTrainPlan.uploadTrainFile(planID, `/train/${trainID}`, 'questions.json', JSON.stringify(trainData.questionResults));
      await this.ctx.service.trainThroughTrainPlan.uploadTrainFile(planID, `/train/${trainID}`, 'answer.json', JSON.stringify(trainData.answerResults));
      
      // if ( trainData && trainData.questionIDs ) {
      //   for(const rows of trainData.questionIDs) {
      //     try {
      //       const fromPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainQuestion/${rows}/assets/`;
      //       const toPath = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${planID}/train/${trainID}/assets/${rows}`;
      //       copyFolder(fromPath, toPath, ()=>{})
      //     } catch(e) {
      //       console.log(e, 'uploadTrainPlanFiles')
      //       throw new Error(e);
      //     }
      //   }
      // }
    }

    // redis中记录计划信息以供定时任务调用
    async redisTrainPlanRecord({ schoolSlug, startTime, endTime, planID }) {
      if (!schoolSlug) {
        throw new Error('请提供学校前缀');
      }

      if (!planID) {
        throw new Error('请提供训练计划ID');
      }

      if (startTime) {
        await redis.zadd(`train_start_${schoolSlug}`, Date.parse(startTime), planID);
      }

      if (endTime) {
        await redis.zadd(`train_end_${schoolSlug}`, Date.parse(endTime), planID);
      }
    }

    // 获取训练
    async getTrainPlan(id, trainID) {
      const { model } = this.ctx;
      const { TrainPlan, Train, TrainUserRecord, TrainThroughTrainPlan } = model

      const userID = this.ctx.session.user.id;

      const condition = {
        planID: id,
        userID,
      };

      if (trainID) {
        condition.trainID = trainID;
      }

      const result = await TrainPlan.findOne({
        include: [{
          model: TrainUserRecord,
          as: 'trainUserRecord',
          where: condition,
          required: false,
        }],
        where: {
          id
        },
      });

      const resultData = result && result.dataValues ? result.dataValues : result;
      if (!resultData) {
        return null
      }

      const attributes = ['id', 'name', 'score', 'template', 'templateName', 'notice', 'ifShowScore', 'ifShowCorrectionResults', 'ifShowWrongAnswer', 'ifSetWrongProblemCollection'];

      let currentTrainID = null;
      const { mode } = resultData;

      if (trainID) {
        currentTrainID = trainID;
      } else {
        const localStudentThroughData = await TrainThroughTrainPlan.findOne({
          where: {
            planID: id,
            userID,
          },
          raw: true,
        });
  
        currentTrainID = localStudentThroughData.trainID;
      }
      
      // 开考之前不显示题目内容
      let showContent = mode === '训练模式' || mode === '自由模式' || moment().isAfter(moment(resultData.startTime));
      if (showContent) {
        return this.ctx.service.trainThroughTrainPlan.getTrainContentFromQuestionFile(currentTrainID, id)
      }

      const trainData = await Train.findOne({
        attributes,
        where: {
          id: currentTrainID
        },
        raw: true,
      })

      if (!trainData) {
        return null
      }

      resultData.train = trainData;
      resultData.currentTime = new Date();

      return resultData
    }

    // 获取训练计划：包含用户、用户训练记录和所有的相关训练
    async getTrainPlanInfo(trainPlanID) {
      const { ctx } = this;
      const { model, session } = ctx;
      const { Train, TrainPlan, TrainThroughTrainPlan, TrainUserRecord } = model;

      if (!session || !session.user) {
        throw new Error('请登录');
      }

      const { user: { id: userID } } = session;

      const result = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        include: [
          {
            model: model.User,
            as: 'createUser',
            attributes: ['id', 'name', 'avatar']
          },
          {
            model: TrainUserRecord,
            as: 'trainUserRecord',
            where: {
              userID: userID
            },
            required: false,
          },
          {
            model: TrainThroughTrainPlan,
            as: 'trainThroughTrainPlan',
            where: {
              userID,
            },
            include: [
              {
                model: Train,
                attributes: ['id', 'name'],
                as: 'train',
                required: true,
              }
            ]
          },
        ],
      });

      const record = await TrainUserRecord.findOne({
        where: {
          planID: trainPlanID,
          userID
        },
        raw: true,
      })

      if (record) {
        const { status, created_at } = record;
        result.dataValues.studentRecordStatus = status;
        result.dataValues.studentRecordCreateTime = created_at;
      }

      result.dataValues.records = result.dataValues.trainUserRecord;
      result.dataValues.currentTime = new Date();

      return result;
    }

    // 删除训练
    async destoryTrainPlan(id, transaction = false) {
      const { model, schoolSlug } = this.ctx;
      const { TrainPlan, TrainPlanClass } = model

      // await TrainPlanClass.destroy({
      //   where: {
      //     planID: id
      //   },
      //   transaction
      // });

      await redis.zrem(`train_start_${schoolSlug}`, id);
      await redis.zrem(`train_end_${schoolSlug}`, id);

      return await TrainPlan.destroy({
        where: {
          id
        },
        transaction
      });
    }

    // 批量删除训练
    async destoryTrainPlans(ids, transaction) {
      const { model, schoolSlug } = this.ctx;
      const { TrainPlan } = model

      for (const id of ids) {
        await redis.zrem(`train_start_${schoolSlug}`, id);
        await redis.zrem(`train_end_${schoolSlug}`, id);
      }

      return await TrainPlan.destroy({
        where: {
          id: {
            [Op.in]: ids
          }
        },
        transaction
      });
    }

    // 获取训练列表
    async getTrainPlanList(node) {
      const { model } = this.ctx;
      const { TrainPlan } = model;

      const { year, columnKey, order } = node;

      let queryOrder = [];
      
      const defaultOrder = ['created_at', 'DESC'];
      
      if (columnKey && order && orderMap[order]) {
        queryOrder.push([columnKey, orderMap[order]]);
      } else {
        queryOrder.push(defaultOrder);
      }

      const userID = this.ctx.session.user.id;

      const condition = { 
        [Op.or]: [
          { createUserID: userID },
          sequelize.literal(`
            json_contains(
              JSON_EXTRACT(
                cast(train_plan.teachers AS json),
                '$'
              ),JSON_ARRAY(${userID})
            ) = 1
          `)
        ]
      };

      if (year) {
        condition.year = year;
      }
      if (node.trainID) {
        condition.trainID = node.trainID;
      }

      //获取对应值
      const result = await TrainPlan.findAll({
        raw: true,
        where: condition,
        order: queryOrder,
        include: [
          {
            model: model.User,
            as: 'createUser',
            attributes: ['name'],
            required: true,
          }
        ],
      });

      const allTeamIDs = [];
      result.forEach((item) => {
        const { openClasses } = item;
        if (openClasses && openClasses.length) {
          allTeamIDs.push(...openClasses);
        }
      });

      const teams = await model.Team.findAll({
        where: {
          id: {
            [Op.in]: allTeamIDs
          }
        },
        attributes: ['id', 'name', 'year'],
        raw: true,
        paranoid:false,
      });

      for(const row of result){
        const { openClasses = [] } = row;
        if (openClasses && openClasses.length) {
          row.trainPlanClass = teams.filter(i => openClasses.indexOf(i.id) !== -1);
        }
      }

      return result;
    }

    async shareTrainPlan({ id, teachers, userID }, transaction) {
      const { model } = this.ctx;

      const { TrainPlan } = model;

      const results = await TrainPlan.findOne({
        where: {
          id,
          createUserID: userID,
        },
        raw: true,
        transaction,
      });

      if (!results) {
        throw new Error('没有找到对应的训练计划，请注意只有自己创建的训练计划才可以分享给其他人');
      }

      await TrainPlan.update({
        teachers: teachers,
      }, {
        where: {
          id,
        },
        transaction,
      });
    }

    // 获取训练列表
    async getTrainPlanListWithClass(year, columnKey, order) {
      const { model } = this.ctx;
      const { TrainPlan, Train, TrainUserRecord, TrainThroughTrainPlan } = model;

      // 过滤条件为学年一致，未配置训练编码，在线训练
      const condition = {
        year,
        code: { [Op.eq]: null },
        environment: '在线训练',
        openForAllClass: 1,
      };

      // 调整返回结果排序
      const defaultOrder = ['created_at', 'DESC'];

      let queryOrder = [];
      if (columnKey && order && orderMap[order]) {
        queryOrder.push([columnKey, orderMap[order]]);
      } else {
        queryOrder.push(defaultOrder);
      }

      // 查询当前用户所拥有的全部训练计划
      const user = this.ctx.session.user;
      const userID = user.id;
      const currentUserTrainPlans = await TrainThroughTrainPlan.findAll({
        attributes: ['planID'],
        where: {
          userID,
        },
        raw: true,
      })

      let currentUserTranPlanIDs = Array.from(new Set(currentUserTrainPlans.map(row => row.planID)));

      // 获取全部用户训练记录
      const trainUserRecords = await TrainUserRecord.findAll({
        attributes: ['planID', 'status'],
        where: { 
          userID
        },
        raw: true,
      })

      const trainPlanIDsWithUserRecord = trainUserRecords.map(row => row.planID).concat(currentUserTranPlanIDs);

      // 获取训练计划
      const trainPlanInstances = await TrainPlan.findAll({
        include: [
          {
            model: model.User,
            as: 'createUser',
            attributes: ['id', 'name', 'avatar']
          },
          {
            model: model.TrainThroughTrainPlan,
            as: 'trainThroughTrainPlan',
            where: {
              userID,
            },
            include: [
              {
                model: Train,
                as: 'train',
                required: true,
              }
            ]
          },
        ],
        // 符合条件的训练计划 或者 用户有记录的训练计划
        where: { [Op.or]: [condition, { id: { [Op.in]: trainPlanIDsWithUserRecord } }]},
        order: queryOrder,
      });
      const trainPlans = trainPlanInstances.map(trainPlanInstance => trainPlanInstance.get({ plain: true }));
      if(trainPlans) {
        for (const trainPlan of trainPlans) {
          trainPlan.currentTime = new Date();
        }
      }
      else {
        return [];
      }
      return trainPlans;
    }

    async getTrainPlanByCode(trainCode) {
      const { model } = this.ctx;
      const { TrainPlan, TrainUserRecord } = model;

      const userID = this.ctx.session.user.id;

      const result = await TrainPlan.findOne({
        where: {
          code: trainCode
        },
        raw: true
      });

      if (!result) {
        throw new Error(`未查找到训练码${trainCode}对应的训练！`);
      }

      const { id } = result;
      const trainUserRecord = await TrainUserRecord.findOne({
        attributes: ['status'],
        where: { 
          userID,
          planID: id
        },
        raw: true,
      });

      if (trainUserRecord) {
        result.studentRecordStatus = trainUserRecord.status;
      }

      result.currentTime = new Date();

      return result;
    }

    async createTrainRecordWithStudent(planID, trainID, transaction) {
      const { model } = this.ctx;
      const { TrainUserRecord } = model;

      const userID = this.ctx.session.user.id;

      const query = {
        userID,
        planID,
      };

      let queryTrainID = null;

      if (trainID) {
        queryTrainID = trainID;
      } else {
        // 获取trainID 
        const trainResult = await model.TrainThroughTrainPlan.findOne({
          where: query,
          raw: true,
          transaction,
        });

        if (!trainResult) {
          throw new Error(`错误！未查找到用户 ${userID} 在训练计划 ${planID} 中的记录`);
        }

        const { trainID } = trainResult;
        queryTrainID = trainID;
      }

      query['trainID'] = queryTrainID;

      //获取对应值
      const result = await TrainUserRecord.findOne({
        where: query,
        // required: false,
        raw: true,
        transaction
      });
      if (result) {
        return result;
      }

      const response = await TrainUserRecord.create({
        userID,
        planID,
        trainID: queryTrainID,
        status: '已报名',
      }, {
        // required: false,
        raw: true,
        transaction
      });

      // 学生报名此计划时，在trainplanclass增加学生所在的班级记录，如果学生所在的班级已记录，则忽略
      // 寻找用户所在班级记录
      const teams = await model.TeamUser.findAll({
        attributes: ['teamID'],
        where: { userID },
        raw: true,
        transaction
      });

      if (!teams || !teams.length) {
        return response;
      }

      const teamIDs = teams.map(team => team.teamID);

      const teamPlanRecord = await model.TrainPlanClass.findOne({
        where: {
          planID,
          teamID: { [Op.in]: teamIDs }
        }
      });

      if (!teamPlanRecord) {
        const plan = await model.TrainPlan.findOne({
          where: {
            id: planID
          },
          transaction
        });

        if (!plan) {
          throw new Error(`未查找到当前训练 ${planID}`);
        }

        const { trainID } = plan;

        const allTeamRecords = teamIDs.map(teamID => {
          return { teamID, trainID, planID }
        });

        await model.TrainPlanClass.bulkCreate(allTeamRecords, {
          updateOnDuplicate: ['teamID', 'trainID', 'planID'],
          transaction
        });
      }

      return response
    }

    async putTrainRecordWithStudent(planID, node, transaction) {
      const { model } = this.ctx;
      const { TrainPlan, TrainUserRecord } = model;

      // 这里传入的参数有trainID, trainPlanID和record
      const { trainID, record } = node;
      const userID = this.ctx.session.user.id;
      if (!userID) {
        throw new Error('请先登录！');
      }

      const trainPlan = await TrainPlan.findOne({
        attributes: ['id', 'status'],
        where: {
          id: planID,
        }
      });

      if (!trainPlan || (trainPlan.status !== '报名中' && trainPlan.status !== '训练中')) {
        throw new Error('训练计划已结束或未开始');
      }

      // 先在redis的trainUserRecord Hash中查找是否有对应的trainUserRecordID记录
      const key = `${planID}_${trainID}_${userID}`;
      let trainUserRecordID = await redis.hget('trainUserRecordIDMap', key);

      // 如果没有找到对应的trainUserRecordID记录，则在数据库中查找
      if(!trainUserRecordID) {
        const trainUserRecord = await TrainUserRecord.findOne({
          attributes: ['id'],
          where: {
            planID,
            userID,
            trainID,
            status: '已报名',
          },
          raw: true,
          transaction
        });

        if (!trainUserRecord) {
          throw new Error('未找到对应的已报名状态训练记录');
        }

        trainUserRecordID = trainUserRecord.id;

        // 写入redis，有效时间5分钟
        await redis.hset('trainUserRecordIDMap', key, trainUserRecordID, 'EX', 300);
      }

      //获取对应值
      await TrainUserRecord.update({
        record
      }, {
        where: {
          id: trainUserRecordID,
          status: '已报名',
        },
        transaction
      });

      return {
        id: trainUserRecordID,
        planID,
        trainID,
        userID,
        record,
      };
    }

    async submitExam({ trainPlanID, trainID, studentAnswer = {}, userID, schoolSlug, showAnswer }, transaction = null) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord, TrainThroughTrainPlan } = ctx.model;

      const plan = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        raw: true,
        attributes: ['mode', 'score', 'endTime', 'minTrainDuration'],
        transaction
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const query = {
        planID: trainPlanID, 
        userID: userID,
      };

      if (trainID) {
        query['trainID'] = trainID;
      }

      // 查找学生答题记录
      const trainUserRecord = await TrainUserRecord.findOne({
        where: query,
        attributes: ['status', 'initialScore', 'submitTimes', 'created_at'],
        raw: true,
        transaction
      });

      // 如果配置了最短作答时长，检查是否满足最短作答时长
      const { minTrainDuration } = plan;
      if (minTrainDuration && minTrainDuration !== 0) {
        // 如果没有开始时间，则使用创建时间
        const { created_at, submitTimes } = trainUserRecord;
        // 仅限制第一次作答
        if (submitTimes < 1) {
          const currentTime = moment();
          let studetnStartTime = moment(created_at);

          const duration = currentTime.diff(studetnStartTime, 'minutes');
          if (duration < minTrainDuration) {
            throw new Error(`请至少作答 ${minTrainDuration.toFixed(0)} 分钟后提交`);
          }
        }
      }

      // trainID
      let trainIDRecord = null;

      if (trainID) {
        trainIDRecord = trainID;
      } else {
        const planData = await TrainThroughTrainPlan.findOne({
          where: {
            planID: trainPlanID,
            userID,
          },
          transaction,
          raw: true,
        });

        if (!planData) {
          console.error('训练：', trainPlanID, '用户：', userID, '没有配置试卷')
          return;
        }

        trainIDRecord = planData.trainID;
      }

      if (!schoolSlug) {
        schoolSlug = ctx.schoolSlug;
      }

      const fileDir = `${app.config.file.dir}/${schoolSlug}/trainPlan/${trainPlanID}/train/${trainIDRecord}/`;
      const questionPath = `${fileDir}questions.json`;
      const answerPath = `${fileDir}answer.json`;

      const questions = await ctx.service.course.getFile(questionPath, true);
      const allAnswer = await ctx.service.course.getFile(answerPath, true);

      let initialScore = 0;
      if (trainUserRecord && trainUserRecord.initialScore) {
        initialScore = parseFloat(trainUserRecord.initialScore);
      }
      
      const freeMode = plan.mode === '自由模式';
      const { judgeResult, studentScore, initialScore: newInitScore } =  submitQuestion(questions, allAnswer, studentAnswer, freeMode, initialScore);

      const updateRecord = {
        record: { ...studentAnswer, ...judgeResult },
        planID: trainPlanID,
        trainID: trainIDRecord,
        userID: userID,
        score: studentScore,
        submitTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        status: '已提交',
        submitTimes: 1,
      };

      if (freeMode) {
        updateRecord['initialScore'] = newInitScore;
      }

      if (trainUserRecord) {
        const { status, submitTimes } = trainUserRecord;
        if (status === '已提交') {
          throw new Error('请勿重复提交');
        } else {
          await TrainUserRecord.update({
            ...updateRecord,
            submitTimes: submitTimes + 1,
          }, {
            where: query,
            transaction
          });
        }
      } else {
        await TrainUserRecord.create(updateRecord,{
          transaction
        });
      }

      if (showAnswer) {
        const record = await TrainUserRecord.findOne({
          where: query,
          raw: true,
          transaction
        });

        // TODO 根据trainplan配置处理返回数据
        return {
          ...record,
          answer: allAnswer,
          currentTime: new Date(),
          endTime: plan.endTime,
          duration: plan.duration,
        }
      }
    }

    async dynamicUpdateStudentRecord({ planID, trainID, userID, schoolSlug, studentAnswer = {} }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan, TrainUserRecord } = model;

      // 检查
      const plan = await TrainPlan.findOne({
        where: {
          id: planID
        },
        attributes: ['mode'],
        transaction
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const { mode } = plan;

      if (mode !== '自由模式') {
        throw new Error(`错误，当前训练模式为 ${mode}，非自由模式训练！`);
      }

      // 判分
      const query = {
        planID,
        trainID,
        userID,
      };

      const planRecord = await TrainUserRecord.findOne({
        where: query,
        transaction
      });

      if (!planRecord) {
        throw new Error('未报名当前训练，请报名后重试');
      }

      // 学生已提交，不修改内容
      let { status: lastStatus, record, score, initialScore } = planRecord;
      if (lastStatus === '已提交') {
        return { record, studentScore: score, initialScore };
      }

      const fileDir = `${app.config.file.dir}/${schoolSlug}/trainPlan/${planID}/train/${trainID}/`;
      const questionPath = `${fileDir}questions.json`;
      const answerPath = `${fileDir}answer.json`;

      const questions = await ctx.service.course.getFile(questionPath, true);
      const allAnswer = await ctx.service.course.getFile(answerPath, true);

      // 更新学生记录
      const { judgeResult, studentScore: newAllScore, initialScore: newInitScore } =  judgeQuestion(questions, allAnswer, studentAnswer, true);

      await TrainUserRecord.update({
        record: judgeResult,
        planID,
        trainID,
        userID: userID,
        score: newAllScore,
        initialScore: newInitScore,
        status: '训练中',
      }, {
        where: query,
        transaction
      });

      return { record: judgeResult, studentScore: newAllScore, initialScore: newInitScore };
    }

    async judgeQuestionByID({ planID, trainID, userID, schoolSlug, studentAnswer = {}, questionID, UUID }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan, TrainUserRecord } = model;

      // 检查
      const plan = await TrainPlan.findOne({
        where: {
          id: planID
        },
        attributes: ['mode'],
        transaction
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const { mode } = plan;

      if (mode !== '自由模式') {
        throw new Error(`错误，当前训练模式为 ${mode}，非自由模式训练！`);
      }

      // 判分
      const query = {
        planID,
        trainID,
        userID,
      };

      const planRecord = await TrainUserRecord.findOne({
        where: query,
        transaction
      });

      if (!planRecord) {
        throw new Error('未报名当前训练，请报名后重试');
      }

      // 学生已提交，不修改内容
      let { status: lastStatus } = planRecord;
      if (lastStatus === '已提交') {
        throw new Error('本场训练已提交！');
      }

      const fileDir = `${app.config.file.dir}/${schoolSlug}/trainPlan/${planID}/train/${trainID}/`;
      const questionPath = `${fileDir}questions.json`;
      const answerPath = `${fileDir}answer.json`;

      const questions = await ctx.service.course.getFile(questionPath, true);
      const allAnswer = await ctx.service.course.getFile(answerPath, true);

      // 更新初始得分
      let { initialScore = 0, score = 0, record } = planRecord;
      let newInitScore = parseFloat(initialScore, 10);
      let studentScore = parseFloat(score, 10);

      // 如果题目已测评，直接返回结果，以免重复测评
      let currentRecord = record && record[questionID];
      if (UUID) {
        currentRecord = currentRecord && currentRecord[UUID];
      }

      if (currentRecord && currentRecord.submitJudge) {
        return { record, studentScore, initialScore: newInitScore };
      }

      // 对指定的题目进行判分
      let question = null;
      let blockScore = 0;
      for (const block of questions) {
        question = block.questions.find(i => i.id === questionID);
        if (question) {
          blockScore = block.score;
          break;
        }
      }
      if (!question) {
        throw new Error(`题目 ${questionID} 不存在`);
      }

      const answer = allAnswer[questionID];
      if (!answer) {
        throw new Error(`题目 ${questionID} 答案不存在`);
      }

      const { noJudge, judgeResult } = judgeQuestionbyID({ question, UUID, blockScore, answer, studentAnswer });
      
      // 更新此题的record ，合并学生做题内容
      let newRecord = studentAnswer || {};
      newRecord[questionID] = judgeResult[questionID];
      
      // 记录是否评测过
      if (UUID) {
        newRecord[questionID][UUID].submitJudge = true;
      } else {
        newRecord[questionID].submitJudge = true;
      }

      const currentScore = UUID ? judgeResult[questionID][UUID].score : judgeResult[questionID].score;

      // 更新初始得分
      if (noJudge) {
        newInitScore += currentScore;
      }

      // 如果有之前计算的分数，需要从总分中去除这道题之前的分数，以便后续计算
      if (!noJudge && record && record[questionID]) {
        if (!UUID) {
          studentScore -= record[questionID].score;
        } else {
          studentScore -= record[questionID][UUID].score;
        }
      }

      // 更新得分
      studentScore += currentScore;

      await TrainUserRecord.update({
        record: newRecord,
        planID,
        trainID,
        userID: userID,
        score: studentScore,
        initialScore: newInitScore,
        status: lastStatus !== '已提交' ? '训练中' : '已提交',
      }, {
        where: query,
        transaction
      });

      return { record: newRecord, studentScore, initialScore: newInitScore };
    }

    async resetStudentAnswer({ planID, trainID, userID, studentAnswer = {}, questionID, UUID }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan, TrainUserRecord } = model;

      // 检查
      const plan = await TrainPlan.findOne({
        where: {
          id: planID
        },
        attributes: ['mode'],
        transaction
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const { mode } = plan;

      if (mode !== '自由模式') {
        throw new Error(`错误，当前训练模式为 ${mode}，非自由模式训练！`);
      }

      const query = {
        planID,
        trainID,
        userID,
      };

      const planRecord = await TrainUserRecord.findOne({
        where: query,
        transaction
      });

      if (!planRecord) {
        throw new Error('未报名当前训练，请报名后重试');
      }

      // 更新初始得分
      let { initialScore = 0, score = 0, record } = planRecord;
      let newInitScore = parseFloat(initialScore, 10);
      let studentScore = parseFloat(score, 10);

      // 更新此题的record ，合并学生做题内容
      let newRecord = studentAnswer || {};

      // 重置学生答案
      if (UUID) {
        const originalRecord = (record[questionID] && record[questionID][UUID]) ? record[questionID][UUID]: {};
        const studentRecord = (studentAnswer[questionID] && studentAnswer[questionID][UUID]) ? studentAnswer[questionID][UUID]: {};
        
        newRecord[questionID][UUID] = { 
          ...originalRecord,
          ...studentRecord,
          answer: null, 
          result: false, 
          score: 0, 
          submitJudge: false
        };
      } else {
        const originalRecord = record[questionID] ? record[questionID]: {};
        const studentRecord = newRecord[questionID] ? newRecord[questionID]: {};

        newRecord[questionID] = { 
          ...originalRecord, 
          ...studentRecord,
          answer: null, 
          result: false, 
          score: 0, 
          submitJudge: false };
      }

      // 如果有之前计算的分数，需要从总分中去除这道题之前的分数
      if (record && record[questionID]) {
        if (!UUID) {
          if (record[questionID].score) {
            studentScore -= record[questionID].score;
          }
        } else {
          if (record[questionID][UUID].score) {
            studentScore -= record[questionID][UUID].score;
          }
        }
      }

      // 如果有文件列表，清空上传文件列表
      if (record && record['fileList']) {
        if (record['fileList'][questionID]) {
          newRecord['fileList'][questionID] = [];
        }
      }

      await TrainUserRecord.update({
        record: newRecord,
        score: studentScore,
      }, {
        where: query,
        transaction
      });

      return { record: newRecord, studentScore, initialScore: newInitScore };
    }

    // 开考时间到，状态置为训练中
    async changeTrainStartStatus(plans, schoolSlug, transaction = null) {
      const { ctx } = this;
      const { TrainPlan } = ctx.model;

      if (!plans || !plans.length || !schoolSlug) {
        return;
      }

      await TrainPlan.update({
        status: '训练中',
      }, {
        where: {
          id: {
            [Op.in]: plans
          },
          startTime: { [Op.lte]: moment().format('YYYY-MM-DD HH:mm:ss') },
          endTime: { [Op.gt]: moment().format('YYYY-MM-DD HH:mm:ss') }
        },
        transaction,
      });

      for (const id of plans) {
        await redis.zrem(`train_start_${schoolSlug}`, id);
      }
    }

    // 考试时间结束，更新状态为已结束
    async changeTrainStatus(plans, schoolSlug, transaction = null) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord } = ctx.model;

      if (!plans || !plans.length || !schoolSlug) {
        return [];
      }

      await TrainPlan.update({
        status: '已结束',
      }, {
        where: {
          id: { [Op.in]: plans },
          status: { [Op.not]: '已结束' },
          endTime: { [Op.lte]: moment().format('YYYY-MM-DD HH:mm:ss') }
        },
        transaction,
      });

      for (const id of plans) {
        await redis.zrem(`train_end_${schoolSlug}`, id);
      }

      const needTrainUserRecord = await TrainUserRecord.findAll({
        where: {
          planID: { [Op.in]: plans }
        },
        transaction,
        raw: true,
      })

      return {
        allNeedTrainPlanIDs: plans,
        allNeedTrainPlanInfos: needTrainUserRecord.map(row => ({ trainPlanID: row.planID, studentAnswer: row.record, userID: row.userID, schoolSlug, trainID: row.trainID }))
      };
    }

    async getTrainPlanStatistics(planID, schoolSlug, transaction = null) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord, TrainPlanClass, TeamUser, User, Questions, TrainThroughTrainPlan } = ctx.model;

      // 全部报名记录
      let statisticsDatas = await TrainUserRecord.findAll({
        where: { planID },
        raw: true,
        transaction,
        include: [{
          attributes: ['id'],
          model: User,
          as: 'user',
          required: true,
          include: [
            {
              model: TeamUser,
              as: 'teamUser',
              attributes: ['teamID'],
              paranoid: false,
            },
            {
              attributes: ['isolate'],
              model: TrainThroughTrainPlan,
              as: 'trainThroughTrainPlan',
              where: {
                isolate: false,
                planID,
              },
              paranoid: false,
            },
          ]
        }],
      });

      if (!statisticsDatas || !statisticsDatas.length) {
        return;
      }

      // 获取当前训练开放班级
      const allTeams = await TrainPlanClass.findAll({
        where: {
          planID
        },
        raw: true,
        transaction
      });

      // 筛选statisticsDatas中参与训练却在不同班的重复数据
      const submitRecords = [];

      // 已答题用户
      for(const i of statisticsDatas){
        if(submitRecords.indexOf(i.id) === -1 && i.record !== null){
          submitRecords.push(i.id);
        }
      }

      // 统计每个班级数据
      const classUserMap = {};
      for (let team of allTeams) {
        const { teamID } = team;
        const teamDatas = statisticsDatas.filter(i => {
          return i['user.teamUser.teamID'] === teamID;
        });

        const validTeamDatas = teamDatas.filter(i => i.record !== null);

        const { average, variance } = calculateBasicStatistics(teamDatas);

        classUserMap[teamID] = {
          enrolledCount: teamDatas.length,
          submitCount: validTeamDatas.length,
          average,
          variance,
        }

        team.enrolledCount = teamDatas.length;
        team.submitCount = validTeamDatas.length;
        team.average = average;
        team.variance = variance;
      }
      // 更新班级统计数据
      if (allTeams && allTeams.length) {
        await TrainPlanClass.bulkCreate(allTeams, {
          updateOnDuplicate: ['variance', 'average', 'submitCount', 'enrolledCount'],
          transaction
        });
      }

      // 更新训练统计数据
      const { average, variance } = calculateBasicStatistics(statisticsDatas);

      await TrainPlan.update({
        enrolledCount: statisticsDatas.length,
        submitCount: submitRecords.length,
        average,
        variance,
        statisticsData: classUserMap,
      }, {
        where: {
          id: planID
        },
        transaction,
      });

      // 更新题目难度、区分度统计
      const plan = await TrainPlan.findOne({
        transaction,
        where: {
          id: planID
        },
        attributes: ['trainID', 'mode'],
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const { mode } = plan;
      if (mode !== '考试模式') {
        return;
      }

      // ===========================计算试卷区分度========================================
      const throughtData = await TrainThroughTrainPlan.findAll({
        transaction,
        where: { planID },
        raw: true,
        group: ['trainID']
      })

      const trainIDSet = new Set();
      for(const trainIDRow of throughtData) {
        const { trainID } = trainIDRow;
        if (!trainIDSet.has(trainID)) {
          trainIDSet.add(trainID);
        }
      }

      // 获取训练分数
      const allTrains = await ctx.model.Train.findAll({
        where: {
          id: {
            [Op.in]: [...trainIDSet]
          },
        },
        raw: true,
        attributes: ['id', 'score'],
        transaction
      });

      const trainScoreMap = {};
      // console.log('allTrains:',allTrains)
      for (const train of allTrains) {
        trainScoreMap[train.id] = train.score;
      }

      // 学生训练分数集合
      const trainRecordMap = {};
      const questionIDSet = new Set();
      const questionResultMap = new Map();
      // console.log('statisticsDatas:',statisticsDatas)

      for (const studentRecord of statisticsDatas) {
        // console.log('studentRecord:',studentRecord)
        const { score, trainID, record } = studentRecord;
        // console.log('questionIDs:',questionIDs)

        if (!trainID) {
          continue;
        }

        if (!trainRecordMap[trainID]) {
          trainRecordMap[trainID] = [];
        }

        trainRecordMap[trainID].push(score ? score : 0);

        if (!record) {
          continue;
        }

        // 统计题目答题次数
        const questionIDs = Object.keys(record);
        // console.log('questionIDs:',questionIDs)
        for (const questionID of questionIDs) {
          if (!questionIDSet.has(questionID)) {
            questionIDSet.add(questionID);
          }

          if (!questionResultMap[questionID]) {
            questionResultMap[questionID] = {
              answerCount: 0,
              passCount: 0,
            };
          }

          questionResultMap[questionID].answerCount++;
          if (record[questionID] && record[questionID].result) {
            questionResultMap[questionID].passCount++;
          }
        }
      }

      // 计算区分度
      const discriminative = getDiscriminative(trainRecordMap, trainScoreMap, planID);
      await TrainPlan.update({
        discriminative
      }, {
        where: {
          id: planID
        },
        transaction,
      });

      // ============================计算题目全部答题人数和通过人数=======================================
      const allQuestions = await ctx.model.Questions.findAll({
        where: {
          id: {
            [Op.in]: [...questionIDSet]
          }
        },
        raw: true,
        paranoid: false,
        transaction
      });

      for (const question of allQuestions) {
        const { id, answerCount, passCount } = question;
        const result = questionResultMap[id];

        if (!answerCount) {
          question.answerCount = 0;
        }

        if (!passCount) {
          question.passCount = 0;
        }

        question.answerCount += parseInt(result.answerCount);
        question.passCount += parseInt(result.passCount);

        if (question.answerCount) {
          question.difficulty = 1 - (question.passCount / question.answerCount);
        }
      }

      await ctx.model.Questions.bulkCreate(allQuestions, {
        updateOnDuplicate: ['answerCount', 'passCount', 'difficulty', 'updated_at'],
        transaction
      });
    }

    // 训练分析列表
    async getTrainPlanStatisticsList({ trainID, trainPlanID, classID }) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan, Team } = ctx.model;

      const condition = {
        // trainID
      };
      const trainThroughTrainPlanCondition = {};

      if (trainPlanID) {
        condition['id'] = trainPlanID;
        trainThroughTrainPlanCondition.planID = trainPlanID;
      }

      // 班级
      const trainPlans = await TrainPlan.findAll({
        raw: true,
        where: condition,
        paranoid:false,
      });

      const allTeamIDs = [];
      trainPlans.forEach((item) => {
        const { openClasses = [] } = item;
        if (openClasses && openClasses.length) {
          allTeamIDs.push(...openClasses);
        }
      });

      const teams = await model.Team.findAll({
        where: {
          id: {
            [Op.in]: allTeamIDs
          },
        },
        attributes: ['id', 'name', 'year'],
        raw: true,
        paranoid:false
      });

      for(const row of trainPlans){
        const { openClasses = [] } = row;
        if (openClasses && openClasses.length) {
          row.trainPlanClass = teams.filter(i => openClasses.indexOf(i.id) !== -1);
        }
      }

      const currentTrainPlan = trainPlans && trainPlans[0];
      const { openClasses = [] } = currentTrainPlan || {};

      let teamResult = [];
      if (openClasses && openClasses.length) {
        teamResult = await Team.findAll({
          raw: true,
          where: { 
            id: {
              [Op.in]: openClasses
            }
          },
          paranoid:false
        });
      }

      return {
        trainPlanClass: teamResult,
        trainPlans,
        currentTime: new Date(),
      };
    }

    // 按题目获取统计
    async getQuestionStatistics({ trainPlanID, classIDs, showError }) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord, TrainThroughTrainPlan, TeamUser, User } = ctx.model;

      // get questions
      const plan = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        attributes: ['trainID', 'score', 'mode'],
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const trainUserRecordRequest = {
        planID: trainPlanID,
      };

      if (classIDs) {
        try {
          classIDs = JSON.parse(classIDs)
        } catch (e) {}
        const userInClasses = await TeamUser.findAll({
          where: { teamID: { [Op.in]: classIDs } },
          group: ['userID'],
          raw: true,
          paranoid:false
        });
        
        trainUserRecordRequest.userID = { [Op.in]: userInClasses.map(item => item.userID) }
      }

      // get student statistics
      const studentRecords = await TrainThroughTrainPlan.findAll({
        where: {
          ...trainUserRecordRequest,
          isolate: false,
        },
        raw: true,
        paranoid:false
      });

      const currentRecords = await TrainUserRecord.findAll({
        where: trainUserRecordRequest,
        raw: true,
        paranoid:false
      });
      
      for (const studentRecord of studentRecords) {
        const { planID, trainID, userID } = studentRecord;
        const currentRecord = currentRecords.find(i => i.userID === userID && i.planID === planID && i.trainID === trainID);
        if (!currentRecord) {
          continue;
        }

        const { record } = currentRecord;
        studentRecord['record'] = record;
      }

      const throughtData = await TrainThroughTrainPlan.findAll({
        where: { planID: trainPlanID },
        raw: true,
        group: ['trainID'],
        paranoid:false
      })
      
      const resultData = [];

      for(const trainIDRow of throughtData) {
        const { trainID } = trainIDRow;

        const trainData = await ctx.model.query(
          'SELECT name from train where id = ?;',
          { 
            replacements: [trainID], 
            type: QueryTypes.SELECT
          }
        )

        if (!trainData || !trainData.length) {
          throw new Error(`训练 ${trainID} 不存在`)
        }

        const { name } = trainData[0];
        const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
        const questionPath = `${fileDir}questions.json`;
        const answerPath = `${fileDir}answer.json`;
  
        const questionExist = await fs.exists(questionPath);
        if (!questionExist) {
          throw new Error(`无法查找到训练${trainPlanID}的题目文件`);
        }
  
        const answerExist = await fs.exists(answerPath);
        if (!answerExist) {
          throw new Error(`无法查找到训练${trainPlanID}的答案文件`);
        }
  
        let content = await ctx.service.course.getFile(questionPath, true);
        const answer = await ctx.service.course.getFile(answerPath, true);


        // 调试代码！！！！！！！！！！！！！！！！！！！！！！！
        // const trainDataInof = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID);
        // let content = trainDataInof.questionResults;
        // const answer = trainDataInof.answerResults;

        const currentStudentRecords = studentRecords.filter(i => i.trainID === trainID);
        content = content.map(i => ({...i, trainID}));
        const { content: newContent, errorQuestionCount } = getContentStatistics(content, answer, currentStudentRecords);

        if (showError === 'true') {
          for (const block of newContent) {
            let { questions } = block;
            block.questions = questions.filter(i => i.errorNumber > 0);
          }
        }

        resultData.push({ trainID, name, content: newContent, errorQuestionCount })
      }

      return { resultData };
    }

    // 按知识点获取统计
    async getTagStatistics({ trainPlanID, classIDs, showError }) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord, QuestionTag, Tag, TrainThroughTrainPlan, User, TeamUser } = ctx.model;

      // get questions
      const plan = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        attributes: ['trainID', 'score'],
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const trainUserRecordRequest = {
        planID: trainPlanID,
      };

      if (classIDs) {
        try {
          classIDs = JSON.parse(classIDs)
        } catch (e) {}
        const userInClasses = await TeamUser.findAll({
          where: { teamID: { [Op.in]: classIDs } },
          group: ['userID'],
          raw: true,
        });
        
        trainUserRecordRequest.userID = { [Op.in]: userInClasses.map(item => item.userID) }
      }

      // get student statistics
      const studentRecords = await TrainThroughTrainPlan.findAll({
        where: {
          ...trainUserRecordRequest,
          isolate: false,
        },
        raw: true,
      });

      const currentRecords = await TrainUserRecord.findAll({
        where: trainUserRecordRequest,
        raw: true,
      });
      
      for (const studentRecord of studentRecords) {
        const { planID, trainID, userID } = studentRecord;
        const currentRecord = currentRecords.find(i => i.userID === userID && i.planID === planID && i.trainID === trainID);
        if (!currentRecord) {
          continue;
        }

        const { record } = currentRecord;
        studentRecord['record'] = record;
      }

      const throughtData = await TrainThroughTrainPlan.findAll({
        where: { planID: trainPlanID },
        raw: true,
        group: ['trainID']
      })

      const allContents = [];
      let allAnswers = {};
      for(const trainIDRow of throughtData) {
        const { trainID } = trainIDRow;

        // const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/train/${trainID}/plan/${trainPlanID}/`;
        const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
        const questionPath = `${fileDir}questions.json`;
        const answerPath = `${fileDir}answer.json`;

        const questionExist = await fs.exists(questionPath);
        if (!questionExist) {
          throw new Error(`无法查找到训练${trainPlanID}的题目文件`);
        }

        const answerExist = await fs.exists(answerPath);
        if (!answerExist) {
          throw new Error(`无法查找到训练${trainPlanID}的答案文件`);
        }

        const content = await ctx.service.course.getFile(questionPath, true);
        const answer = await ctx.service.course.getFile(answerPath, true);

        // 调试代码！！！！！！！！！！！！！！！！！！！！！！！
        // const trainData = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID);
        // const content = trainData.questionResults;
        // const answer = trainData.answerResults;
        
        // allContents = allContents.concat(content);
        for(const row of content) {
          // if(contentMap[row.id]) {
          //   continue;
          // }
          // contentMap[row.id] = true;
          allContents.push({ trainID, ...row})
        }
        allAnswers = { ...allAnswers, ...answer};
      }

      const { content: newContent, errorQuestionCount }  = getContentStatistics(allContents, allAnswers, studentRecords);
      // console.log('newContent:', newContent)

      if (showError === 'true') {
        for (const block of newContent) {
          let { questions, trainID } = block;
          // console.log('trainID:', trainID)
          block.questions = questions.filter(i => i.errorNumber > 0).map(row => ({...row, trainID}));
        }
      } else {
        for (const block of newContent) {
          let { questions, trainID } = block;
          // console.log('trainID:', trainID)
          block.questions = questions.map(row => ({...row, trainID}));
        }
      }

      const questionIDs = [];
      const questionList = [];
      for (const block of newContent) {
        const { questions } = block;
        for (const question of questions) {
          const { id: questionID } = question;
          questionIDs.push(questionID);
          questionList.push(question);
        }
      }

      const tagResults = await QuestionTag.findAll({
        include: [{
          attributes: ['id', 'tagName'],
          model: Tag,
          as: 'tag',
          request: true,
          where: {
            deleted_at: null
          }
        }],
        where: { questionID: {[Op.in]: questionIDs } },
        raw: true,
        group: ['tagID'],
      });

      for (const tagResult of tagResults) {
        const { questionID } = tagResult;
        tagResult.questions = questionList.filter(i => i.id === questionID);
        tagResult.errorNumber = tagResult.questions.map(i => i.errorNumber).reduce((prev, cur) => prev + cur, 0);
      }

      // 没有标签的题目统计
      const tagResultIDs = tagResults.map(i => i.questionID);
      const noTagQuestions = questionList.filter(i => !tagResultIDs.includes(i.id));

      if (noTagQuestions && noTagQuestions.length) {
        tagResults.push({
          type: '无标签',
          tag: { tagName: '无标签' },
          questions: noTagQuestions,
          errorNumber: noTagQuestions.map(i => i.errorNumber).reduce((prev, cur) => prev + cur, 0),
        })
      }

      return { content: tagResults, errorQuestionCount } ;
    }

    // 按学生获取统计
    async getStudentStatistics({ trainPlanID, classIDs }) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord, User, TrainThroughTrainPlan, TeamUser, Team } = ctx.model;

      // get questions
      const plan = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        attributes: ['trainID', 'score'],
      });

      if (!plan) {
        throw new Error('无法查找到训练');
      }

      const trainUserRecordRequest = {
        planID: trainPlanID,
      };

      if (classIDs) {
        try {
          classIDs = JSON.parse(classIDs)
        } catch (e) {}
        // console.log('classIDs:',classIDs)
        const userInClasses = await TeamUser.findAll({
          where: { teamID: { [Op.in]: classIDs } },
          group: ['userID'],
          raw: true,
          paranoid: false,
        });
        
        trainUserRecordRequest.userID = { [Op.in]: userInClasses.map(item => item.userID) }
      }
      
      // get student statistics
      const allStudents = await TrainThroughTrainPlan.findAll({
        where: {
          ...trainUserRecordRequest,
          isolate: false,
        },
        
        include: [{
          attributes: ['id', 'username', 'name'],
          model: User,
          as: 'user',
          request: false,
          // where: {
          //   deleted_at: null
          // },
          include: [{
            attributes: ['teamID'],
            model: TeamUser,
            as: 'teamUser',
            required: true,
            include: [{
              attributes: ['name', 'year'],
              model: Team,
              as: 'Team',
              required: true,
              paranoid: false,

            }],
            paranoid: false,

          }],
          paranoid: false,

        }],
        paranoid: false,
      });

      const studentRecords = await TrainUserRecord.findAll({
        where: trainUserRecordRequest,
        raw: true,
        paranoid: false,
      });

      const throughtData = await TrainThroughTrainPlan.findAll({
        where: { planID: trainPlanID },
        raw: true,
        group: ['trainID'],
        paranoid: false,
      })

      const allContents = [];

      let allAnswers = {};
      const trainList = {};
      for(const trainIDRow of throughtData) {
        const { trainID } = trainIDRow;
        trainList[trainID] = [];
        const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
        const questionPath = `${fileDir}questions.json`;
        const answerPath = `${fileDir}answer.json`;

        const questionExist = await fs.exists(questionPath);
        if (!questionExist) {
          throw new Error(`无法查找到训练${trainPlanID}的题目文件`);
        }

        const answerExist = await fs.exists(answerPath);
        if (!answerExist) {
          throw new Error(`无法查找到训练${trainPlanID}的答案文件`);
        }

        const content = await ctx.service.course.getFile(questionPath, true);
        const answer = await ctx.service.course.getFile(answerPath, true);

        // 调试代码！！！！！！！！！！！！！！！！！！！！！！！
        // const trainData = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID);
        // const content = trainData.questionResults;
        // const answer = trainData.answerResults;
        
        for(const row of content) {
          trainList[trainID].push(row);
          allContents.push({...row, trainID});
        }
        allAnswers = {...allAnswers, ...answer};
      }

      // calculate score of each block
      for (const studentRecord of allStudents) {
        let { userID, planID, trainID } = studentRecord;

        // 大题得分统计
        studentRecord.dataValues.partScores = [];

        const currentRecord = studentRecords.find(i => i.userID === userID && i.trainID === trainID && i.planID === planID);
        if (!currentRecord) {
          continue;
        }

        const { record } = currentRecord;
        studentRecord.dataValues = { ...studentRecord.dataValues, ...currentRecord };
        
        const scoreTrainMap = {};

        const currentContent = allContents.filter(i => i.trainID === trainID);

        for (const block of currentContent) {
          const { questions, score } = block;
          
          studentRecord.dataValues['trainID'] = trainID;

          let currentScore = 0;
          for (const question of questions) {
            if(scoreTrainMap[question.id]) {
              continue;
            }
            scoreTrainMap[question.id] = true;
            const { questionType = '' } = question;

            if (!(record && record[question.id])) {
              continue;
            }

            let currentResult = record[question.id];

            if (questionType === '单选题') {
               if (currentResult.result) {
                currentScore += score;
              }
            } else if (questionType === '综合题') {
              if (record && record[question.id]) {
                const complexScore = Object.keys(currentResult)
                .map(uuid => currentResult[uuid] && currentResult[uuid].score ? currentResult[uuid].score : 0)
                .reduce((prev, cur) => prev + cur, 0);

                currentScore += complexScore;
              }
            } else {
              if (currentResult.score) {
                currentScore += currentResult.score;
              }
            }
          }

          studentRecord.dataValues.partScores.push(currentScore);
        }
      }

      // get answer
      for (const block of allContents) {
        const { questions } = block;
        for (const question of questions) {
          const { id: questionID, questionType, questionDetail } = question;

          // 合并参考答案
          if (questionType !== '综合题') {
            question.answer = allAnswers[questionID];
          } else {
            questionDetail.forEach((subQuestion) => {
              subQuestion.answer = allAnswers[questionID][subQuestion.UUID];
            });
          }
        }
      }

      return {
        studentResult: allStudents,
        questionContent: allContents,
        trainList
      };
    }

    async stopTrainPlan(ids, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan } = model;

      const originResultList = await TrainPlan.findAll({
        where: {
          id: {
            [Op.in]: ids,
          },
        },
        transaction,
        raw: true
      })

      await TrainPlan.update({
        endTime: moment(),
        status: '收卷中',
      }, {
        where: {
          id: {
            [Op.in]: ids,
          },
        },
        transaction
      });

      const plans = ids;

      // 获取需要修改状态的训练计划并修改，返回需要判分的train_user_record
      const { allNeedTrainPlanIDs, allNeedTrainPlanInfos } = await ctx.service.trainThroughTrainPlan.changeTrainStatus(plans, this.ctx.schoolSlug, transaction, true);

      if (allNeedTrainPlanInfos && allNeedTrainPlanInfos.length) {
        // 判分
        for(const row of allNeedTrainPlanInfos) {
          try {
            await ctx.service.trainThroughTrainPlan.submitExam(row, transaction);
          } catch(e) {
            console.error('stopTrainPlan submitExam', e)
            if (e.message.indexOf('请勿重复提交')=== -1) {
              throw new Error(e);
            }
          }
        }

        // 统计
        for(const row of allNeedTrainPlanIDs) {
          await ctx.service.trainThroughTrainPlan.getTrainPlanStatistics(row, this.ctx.schoolSlug, transaction)
        }
      }

      // 发送消息，通知前台考试结束
      for (const originResult of originResultList){
        app.client.publish(`/school/${this.ctx.schoolSlug}/planStop/${originResult.id}`, {
          stop: true,
        });
        await redis.zrem(`train_end_${this.ctx.schoolSlug}`, originResult.id);
      }


      // await redis.zadd(`train_end_${this.ctx.schoolSlug}`, Date.parse(new Date()), originResult.id);
    }

    // 延期
    async delayTrainPlan({ planID, delayTime }, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan } = model;

      const currentPlan = await TrainPlan.findOne({
        where: {
          id: planID,
        },
        transaction,
        raw: true
      });

      if (!currentPlan) {
        throw new Error('当前训练不存在');
      }

      const { mode, endTime } = currentPlan;
      
      if (mode === '考试模式') {
        throw new Error('考试模式不可延期');
      }

      if (moment(delayTime) <= moment(endTime)) {
        throw new Error(`新的结束时间应在 ${moment(endTime).format('YYYY年MM月DD日 HH时mm分')} 之后`);
      }

      if (moment(delayTime) < moment()) {
        throw new Error(`新的结束时间应在 ${moment().format('YYYY年MM月DD日 HH时mm分')} 之后`);
      }

      await TrainPlan.update({
        endTime: delayTime,
        status: '训练中',
      }, {
        where: {
          id: planID,
        },
        transaction
      });

      // 删除旧redis记录，新建新时间记录
      const newEndTime = new Date(delayTime).getTime();
      await redis.zrem(`train_end_${this.ctx.schoolSlug}`, planID);
      await redis.zadd(`train_end_${this.ctx.schoolSlug}`, newEndTime, planID);
    }

    // 重命名
    async updateTrainPlan(params, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan } = model;

      const { planID, name, ifShowScore, ifShowCorrectionResults, ifShowWrongAnswer, ifSetWrongProblemCollection, ifRandomOrder, ifForbidViewPaper } = params;

      const currentPlan = await TrainPlan.findOne({
        where: {
          id: planID,
        },
        transaction,
        raw: true
      });

      if (!currentPlan) {
        throw new Error('当前训练不存在');
      }

      await TrainPlan.update({
        name,
        ifShowScore,
        ifShowCorrectionResults,
        ifShowWrongAnswer,
        ifSetWrongProblemCollection,
        ifRandomOrder,
        ifForbidViewPaper,
      }, {
        where: {
          id: planID,
        },
        transaction
      });
    }

    // 同步redis存储训练计划
    async syncTrainPlans(schoolSlug, allStartPlans, allEndPlans) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainPlan, SystemConfig } = model;

      // 读取SystemConfig配置判定学校训练计划已经结束了

      // 1. 增加或更新删除标记
      const deleteFlag = await SystemConfig.findOne({
        where: {
          key: 'trainDeleted'
        },
        raw: true
      });

      if(deleteFlag && deleteFlag.value === 'true') {
        return '已经标记删除';
      }

      // 取库里需处理的训练和训练时间
      const needTrainPlans = await TrainPlan.findAll({
        where: { 
          environment: '在线训练',
          status: { [Op.not]: '已结束' } 
        },
        raw: true,
      });

      const redis = app.redis.get('train');

      const needStartArr = [];
      const needEndArr = [];

      for(const row of allStartPlans) {
        redis.zrem(`train_start_${schoolSlug}`, row.id);
      }
      for(const row of allEndPlans) {
        redis.zrem(`train_end_${schoolSlug}`, row.id);
      }

      // TODO
      for(const row of needTrainPlans) {
        if (row.status === '已结束') {
          continue;
        }

        // 如果训练状态已变更，从记录中移除
        // const trainStartKey = `train_start_${schoolSlug}`;
        
        // const allPlans = await redis.zrange(trainStartKey, 0, -1, 'WITHSCORES');
  
        // const planList = list2Object(allPlans);
  
        // const plans = planList
        //   // .filter(i => i.time <= Date.now()) 
        //   .map(i => i.planID);
        // console.log(plans, trainStartKey, 'train_start')

        // 训练时间结束，记录在 train_start_schoolSlug
        if(row.endTime) {
          await redis.zadd(`train_end_${schoolSlug}`, Date.parse(row.endTime), row.id);
          needEndArr.push(row);
        }

        if (row.status === '训练中') {
          continue;
        }
  
        // 训练时间开始，记录在 train_start_schoolSlug
        if(row.startTime) {
          needStartArr.push(row);
          await redis.zadd(`train_start_${schoolSlug}`, Date.parse(row.startTime), row.id);
        }
      }

      // console.log('allStartPlans:',allStartPlans)
      // console.log('allEndPlans:',allEndPlans)
      // console.log('needStartArr:',needStartArr)
      // console.log('needEndArr:',needEndArr)

      // return;
      // 查看哪些是需要删除的
      // for(const row of allStartPlans) {
      //   if (needStartArr.indexOf(row.id) === -1) {
      //     redis.zrem(`train_start_${schoolSlug}`, row.id);
      //   }
      // }
      // for(const row of allEndPlans) {
      //   if (needEndArr.indexOf(row.id) === -1) {
      //     redis.zrem(`train_end_${schoolSlug}`, row.id);
      //   }
      // }

      // // 查看哪些需要增加
      // for(const row of needStartArr) {
      //   if (allStartPlans.indexOf(row.id) === -1) {
      //     redis.zadd(`train_start_${schoolSlug}`, Date.parse(row.startTime), row.id);
      //   }
      // }
      // for(const row of needEndArr) {
      //   if (allEndPlans.indexOf(row.id) === -1) {
      //     redis.zadd(`train_end_${schoolSlug}`, Date.parse(row.startTime), row.id);
      //   }
      // }

    }

    async getClassList(){
        const { ctx } = this;
        const { model } = ctx;
        const { Team, User, TeamUser } = model;

        // const userResult = await User.findAll({
        //     // attributes: ['id', 'name'],
        //     order: [['created_at', 'desc']],
        //     raw: true,
        //     include: [{
        //         model: Team,
        //         as: 'team'
        //     }]
        // });

        const classResult = await Team.findAll({
          attributes: ['id', 'name', 'year'],
          raw: true,
        })

        const yearClassMap = [];
        // 将班级按照年份处理
        for(const node of classResult){
          if(!yearClassMap.find(item => item.yearName===node.year)){
            yearClassMap.push({ yearName: node.year, classList: classResult.filter(item => item.year === node.year) });
          }
        }

        // for(const node of userResult){
        //    if( node && node['team.id'] && node['team.year'] ){
        //       if(yearClassMap.find(item=> item.yearName ===  node['team.year']).classList.find(item => item.id === node['team.id'])){
        //         yearClassMap.find(item=> item.yearName ===  node['team.year']).classList.find(item => item.id === node['team.id']).userList.push({ name: node.name, id: node.id,  });
        //       }
        //    }
        // }


        return yearClassMap;
    }

    async getUserListByClasses(classIDs){
        const { ctx } = this;
        const { model } = ctx;
        const { Team, User } = model;

        try {
          classIDs = JSON.parse(classIDs)
        } catch (e) {}

        return await User.findAll({
          attributes: ['id', 'username', 'name'],
          order: [['created_at', 'desc']],
          raw: true,
          include: [{
            attributes: ['id', 'name'],
            model: Team,
            as: 'team',
            where: { id: {[Op.in]: classIDs} },
            required: true,
          }]
        });
    }

    // 学生重做
    async redoTrain(node, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { TrainUserRecord } = model;

      const { userID, trainPlanID, trainID, correctMode } = node;

      const query = {
        userID,
        planID: trainPlanID,
      }

      if (trainID) {
        query['trainID'] = trainID;
      }

      const record = await TrainUserRecord.findOne({
        where: query,
        raw: true,
        transaction,
      });

      if (!record) {
        console.error(userID, trainPlanID, trainID, '没有查找到答题记录')
        throw new Error('没有查找到答题记录');
      }

      const { status, record: studentAnswer, score } = record;
      if (status !== '已提交') {
        console.error(userID, trainPlanID, trainID, '暂无提交记录,无法重做')
        throw new Error('暂无提交记录,无法重做');
      }

      // 自由模式，不清除初始得分，不清除学生初始答案
      const plan = await model.TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        raw: true,
        transaction
      });

      if (!plan) {
        throw new Error(`未查找到训练计划`);
      }

      const { mode } = plan;
      if (mode === '考试模式') {
        throw new Error(`考试模式无法重做！`);
      }
     
      let newRecord = {};
      let newScore = 0;
      let correctQuestionIDs = []; // 本次答对题目

      if (studentAnswer) {
        const fileDir = `${app.config.file.dir}/${ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
        const questionPath = `${fileDir}questions.json`;

        const questions = await ctx.service.course.getFile(questionPath, true);

        for (const block of questions) {
          const { questions } = block;
          for (const question of questions) {
            const { id, questionType, questionDetail } = question;

            // 订正模式 correctMode(true)：保留正确答案，清空错误答案
            // 自由模式：保留初始得分
            if (mode === '自由模式') {
              const { newRecord: freeRecord, finalResult } = getNewFreeModeRecord({ studentAnswer: studentAnswer[id], questionType, questionDetail, correctMode });

              newRecord[id] = freeRecord;
              
              // 答对题目
              if (correctMode && finalResult) {
                correctQuestionIDs.push(id);
              }
            } else if (mode === '训练模式') {
              if (!correctMode) {
                continue;
              }

              const { newRecord: trainRecord, finalResult } = getNewTrainModeRecord({ studentAnswer: studentAnswer[id], questionType, questionDetail });
              
              newRecord[id] = trainRecord;
              
              // 答对题目
              if (finalResult) {
                correctQuestionIDs.push(id);
              }
            }
          }
        }
      }

      await TrainUserRecord.update({
        created_at: new Date(),
        status: '已报名',
        record: newRecord,
        score: newScore,
        correctMode,
        correctQuestionIDs,
      }, {
        where: query,
        transaction
      });
    }

    // 获取多个训练计划导出结果
    async getBulkTrainPlanStatistics(trainPlanIDs, needDetail = false) {
      const { ctx } = this;
      const { model } = ctx;

      // 取所有牵扯训练计划信息,并形成索引
      const trainPlanMap = {};
      const trainPlanNameMap = {};
      let allTrainOpenClasses = [];
      const trainPlanDatas = await model.TrainPlan.findAll({
        where: { id: { [Op.in]: trainPlanIDs }},
        raw: true,
      });
      for(const trainPlanData of trainPlanDatas) {
        trainPlanMap[trainPlanData.id] = {};
        trainPlanNameMap[trainPlanData.id] = trainPlanData.name;
        if (trainPlanData.openClasses) {
          allTrainOpenClasses = allTrainOpenClasses.concat(trainPlanData.openClasses)
        }
      }

      // 获取所有牵扯到用户和训练
      const allTrainAndUser = await model.TrainThroughTrainPlan.findAll({
        where: { planID: { [Op.in]: trainPlanIDs } },
        raw: true,
      });

      const userIDsMap = {};
      const trainIDsMap = {};
      // 取出所有train,user,作出索引
      for(const trainAndUserRow of allTrainAndUser) {
        if (!userIDsMap[trainAndUserRow.userID]) {
          userIDsMap[trainAndUserRow.userID] = []
        }
        userIDsMap[trainAndUserRow.userID].push(trainAndUserRow);

        if (!trainIDsMap[trainAndUserRow.trainID]) {
          trainIDsMap[trainAndUserRow.trainID] = {}
        }
        trainPlanMap[trainAndUserRow.planID] = { ...trainPlanMap[trainAndUserRow.planID], [trainAndUserRow.trainID]: true }
        const trainIDsMapByTrainID = trainIDsMap[trainAndUserRow.trainID];
        trainIDsMapByTrainID[trainAndUserRow.userID] = trainAndUserRow;
      }

      // 取所有牵扯训练信息,并形成索引
      const trainIDs = Object.keys(trainIDsMap).map(row => parseInt(row));
      // const trainDatas = await model.Train.findAll({
      //   where: { id: { [Op.in]: trainIDs }},
      //   raw: true,
      // });

      const trainDatas = await ctx.model.query(
        'SELECT * from train where id in (?);',
        { 
          replacements: [trainIDs],
          type: QueryTypes.SELECT
        }
      )
      // console.log('trainDatas:',trainDatas)

      // 取所有牵扯学员信息,并形成索引
      const userIDs = Object.keys(userIDsMap).map(row => parseInt(row));
      const userMap = {};
      const userDatas = await model.User.findAll({
        where: { id: { [Op.in]: userIDs }},
        raw: true,
      });
      for(const userData of userDatas) {
        userMap[userData.id] = userData;
      }

      // 取所有班级与学员关联，作出索引
      const allUserClassDatas = await model.TeamUser.findAll({
        where: { userID: { [Op.in]: userIDs }},
        raw: true,
      });
      const teamIDsMap = {};
      for(const allUserClassData of allUserClassDatas) {
        if (!teamIDsMap[allUserClassData.teamID]) {
          teamIDsMap[allUserClassData.teamID] = []
        }
        teamIDsMap[allUserClassData.teamID].push(allUserClassData);
      }

      // 取班级信息，作出索引
      // 班级id取trainplan里开放班级的id，没有则取学生记录里的id
      let teamIDs = Object.keys(teamIDsMap).map(row => parseInt(row));
      if (allTrainOpenClasses && allTrainOpenClasses.length) {
        teamIDs = Array.from(new Set(allTrainOpenClasses))
      }
      // console.log('teamIDs:',teamIDs);

      const allClassDatas = await model.Team.findAll({
        where: { id: { [Op.in]: teamIDs }},
        raw: true,
      });
      const teamMap = {};
      for(const allClassData of allClassDatas) {
        teamMap[allClassData.id] = allClassData;
      }

      // 取牵扯训练所有成绩，作出索引
      const userRecords = await model.TrainUserRecord.findAll({
        where: { planID: { [Op.in]: trainPlanIDs } },
        group: ['trainID', 'userID', 'planID'],
        order: [['created_at', 'ASC']],
        raw: true,
      })
      // console.log('=========', userRecords.map(row => row.trainID))
      // return userRecords;
      const userRecordMap = {};
      for(const userRecord of userRecords) {
        if (!userRecordMap[userRecord.userID]) {
          userRecordMap[userRecord.userID] = {};
        }
        const userRecordMapByUser = userRecordMap[userRecord.userID];
        userRecordMapByUser[userRecord.trainID] = userRecord;
      }
      // console.log('==============1=============')
      // console.log('userRecords:',userRecords.length)

      // 取大题数据
      const throughtData = await model.TrainThroughTrainPlan.findAll({
        where: { planID: { [Op.in]: trainPlanIDs } },
        raw: true,
        group: ['trainID'],
      })

      const allContents = [];
      let allAnswers = {};
      const trainList = {};
      for(const trainIDRow of throughtData) {
        const { trainID, planID: trainPlanID } = trainIDRow;
        trainList[trainID] = [];
        const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
        const questionPath = `${fileDir}questions.json`;
        const answerPath = `${fileDir}answer.json`;
        // console.log('questionPath:',questionPath)

        const questionExist = await fs.exists(questionPath);
        if (!questionExist) {
          throw new Error(`无法查找到训练${trainPlanID}的题目文件`);
        }

        const answerExist = await fs.exists(answerPath);
        if (!answerExist) {
          throw new Error(`无法查找到训练${trainPlanID}的答案文件`);
        }

        const content = await ctx.service.course.getFile(questionPath, true);
        const answer = await ctx.service.course.getFile(answerPath, true);

        // 调试代码！！！！！！！！！！！！！！！！！！！！！！！
        // const trainData = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID);
        // const content = trainData.questionResults;
        // const answer = trainData.answerResults;
        
        for(const row of content) {
          trainList[trainID].push(row);
          allContents.push({...row, trainID});
        }
        allAnswers = {...allAnswers, ...answer};
      }
      // console.log('userRecords:',userRecords)

      // 获取大题得分，并作出索引
      const scoreMap = {};
      const nameMap = {};
      // console.log('==============2=============')
      // console.log('userRecords:',userRecords.length)
      for (const studentRecord of userRecords) {
        let { record, userID, planID, trainID } = studentRecord;
        // console.log(trainID d)
        if (!scoreMap[userID]) {
          scoreMap[userID] = {}
        }
        if (!nameMap[`${planID}_${trainID}`]) {
          nameMap[`${planID}_${trainID}`] = {};
        }
        const scoreMapByStudent = scoreMap[userID];
        const nameMapByTrain = nameMap[`${planID}_${trainID}`];

        if (!trainID) {
          const userTrainData = await TrainThroughTrainPlan.findOne({
            where: { 
              userID,
              planID
            },
            raw: true,
          });

          trainID = userTrainData.trainID;
        }
        // console.log(trainList[trainID])

        if(!scoreMapByStudent[`${planID}_${trainID}`]) {
          scoreMapByStudent[`${planID}_${trainID}`] = {};
        }
        const scoreMapByTrainID = scoreMapByStudent[`${planID}_${trainID}`];
        
        const scoreTrainMap = {};

        // const trainPlanData = trainPlanMap[planID];
        // let scoreField = 'score';
        // if (trainPlanData && trainPlanData.mode === '自由模式') {
        //   scoreField = 'initialScore'
        // }
        // console.log(trainID, 'scoreField:',scoreField)

        // 大题得分统计
        // studentRecord.partScores = [];
        
        for (const block of allContents) {
          const { questions, score } = block;
          
          studentRecord['trainID'] = trainID;

          // let currentScore = 0;
          
          for (const question of questions) {
            if(scoreTrainMap[`${userID}_${planID}_${trainID}_${question.id}`]) {
              // console.log('question:',question);
              continue;
            }
            scoreTrainMap[`${userID}_${planID}_${trainID}_${question.id}`] = true;
            const { questionType = '' } = question;

            if (!(record && record[question.id])) {
              continue;
            }

            let currentResult = record[question.id];
            // console.log(record)
            // console.log(questionType, question.id, userID)
            // console.log('currentResult:',currentResult);
            if (!currentResult) {
              continue;
            }
            // console.log('{ userID, planID, trainID }:',{ userID, planID, trainID })

            if(!scoreMapByTrainID[questionType] && scoreMapByTrainID[questionType] !== 0) {
              scoreMapByTrainID[questionType] = 0;
            }
            nameMapByTrain[questionType] = true;

            if (questionType === '单选题') {
              if (currentResult.result) {
                scoreMapByTrainID[questionType] += getScore(currentResult);
              }
            } else if (questionType === '综合题') {
              if (record && record[question.id]) {
                const complexScore = Object.keys(currentResult)
                .map(uuid => getScore(currentResult[uuid]))
                .reduce((prev, cur) => prev + cur, 0);

                scoreMapByTrainID[questionType] += complexScore;
                // console.log('complexScore', complexScore)
              }
            } else {
              scoreMapByTrainID[questionType] += getScore(currentResult);
                // console.log('currentResult[',scoreField,']', currentResult[scoreField])
            }
            // console.log('scoreMapByTrainID[questionType]:',scoreMapByTrainID[questionType])
          }

          // studentRecord.partScores.push(currentScore);
          // scoreMapByTrainID[block.name] = currentScore;
        }
      }
      // console.log('scoreMap:',scoreMap)
      // console.log('nameMap:',nameMap)

      // get answer
      // for (const block of allContents) {
      //   const { questions } = block;
      //   for (const question of questions) {
      //     const { id: questionID, questionType, questionDetail } = question;

      //     // 合并参考答案
      //     if (questionType !== '综合题') {
      //       question.answer = allAnswers[questionID];
      //     } else {
      //       questionDetail.forEach((subQuestion) => {
      //         subQuestion.answer = allAnswers[questionID][subQuestion.UUID];
      //       });
      //     }
      //   }
      // }
      // console.log('scoreMap:',scoreMap)
      // console.log('nameMap:',nameMap)

      // 整理最后导出字段
      const results = [];
      for(const teamID of teamIDs) {
        let exportData = [];
        // const nameMap = {};
        const exportTitleRow = {
          序号: '序号',
          学号: '学号',
          姓名: '姓名',
        };
        // 取考试名称
        for(const trainData of trainDatas) {
          for(const planID of trainPlanIDs) {
            if (!trainPlanMap[planID] || !trainPlanMap[planID][trainData.id]) {
              continue;
            }
            // console.log('trainData:',trainData)
            // const scoreMapByStudent = scoreMap[teamIDsMap[teamID][0].userID];
            // const scoreMapByTrainID = scoreMapByStudent[trainData.id];
            // nameMap[trainData.id] = [];
            // if (scoreMapByTrainID) {
            // const questionNames = Object.keys(scoreMapByTrainID);
            //   // nameMap[trainData.id] = questionNames;
            // }
            if(needDetail) {
              const nameMapByTrain = nameMap[`${planID}_${trainData.id}`];
              if (nameMapByTrain) {
                const questionNames = Object.keys(nameMapByTrain);
                for( const questionName of questionNames) {
                  exportTitleRow[`${planID}_${trainData.id}-${questionName}`] = `${trainPlanNameMap[planID]}-${trainData.name}-${questionName}`
                }
              }
            }
            exportTitleRow[`${planID}_${trainData.id}-总分`] = `${trainPlanNameMap[planID]}-${trainData.name}-总分`;
          }
        }
        // 插入标题行
        // exportData.push(exportTitleRow);

        // console.log('userMap:',userMap);
        // 循环该班级学员
        if (teamIDsMap[teamID] && teamIDsMap[teamID].length) {
          for(const userData of teamIDsMap[teamID]) {
            // console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
            // console.log('userRow:',userRow)
            const userRow = userMap[userData.userID];
            if (!userRow) {
              // console.log(userData, Object.keys(userMap).length)
              // console.log(teamIDs)
              continue;
            }
            const exportRow = {
              学号: userRow.username,
              姓名: userRow.name,
            }
  
            let totalScore = 0;
            // 取考试成绩
            for(const trainData of trainDatas) {
              for(const planID of trainPlanIDs) {
                if (!trainPlanMap[planID] || !trainPlanMap[planID][trainData.id]) {
                  continue;
                }
                const userRecordMapByUser = userRecordMap[userData.userID];
                if (!userRecordMapByUser) {
                  exportRow[`${planID}_${trainData.id}-总分`] = 0;
                  continue;
                }
                // console.log('userRecordMapByUser:',userRecordMapByUser)
                const userRecord = userRecordMapByUser[trainData.id];
                if (!userRecord) {
                  exportRow[`${planID}_${trainData.id}-总分`] = 0;
                  continue;
                }
                const scoreMapByStudent = scoreMap[userData.userID];
                const scoreMapByTrainID = scoreMapByStudent[`${planID}_${trainData.id}`];
                let exportRowTotal = 0;
                if (scoreMapByTrainID) {
                  const questionNames = Object.keys(scoreMapByTrainID);
                  // console.log('nameMap[',trainData.id,']:',nameMap[trainData.id])
                  // console.log('scoreMapByTrainID:',scoreMapByTrainID)
                  for(const questionName of questionNames) {
                    exportRow[`${planID}_${trainData.id}-${questionName}`] = scoreMapByTrainID[questionName] ? scoreMapByTrainID[questionName] : 0;
                    exportRowTotal += scoreMapByTrainID[questionName] ? scoreMapByTrainID[questionName] : 0;
                  }
                }
  
                exportRow[`${planID}_${trainData.id}-总分`] = exportRowTotal;
                // const trainIDsMapByTrainID = trainIDsMap[trainData.id];
                // const trainAndUserRow = trainIDsMapByTrainID[userData.userID];
  
                // console.log('trainAndUserRow.planID:',trainAndUserRow.planID)
                // const trainPlanData = trainPlanMap[trainAndUserRow.planID];
                // if (!trainPlanData) {
                //   exportRow[`${trainData.name}-总分`] = 0;
                // } else if (trainPlanData.mode === '自由模式') {
                //   // console.log('userRecord.initialScore:',userRecord.initialScore);
                //   // console.log('trainData.name:',trainData.name);
                //   exportRow[`${trainData.name}-总分`] = parseFloat(userRecord.initialScore);
                //   // console.log('exportRow[`${trainData.name}-总分`]:',exportRow[`${trainData.name}-总分`]);
                // } else {
                //   exportRow[`${trainData.name}-总分`] = parseFloat(userRecord.score);
                // }
                totalScore += exportRow[`${planID}_${trainData.id}-总分`];
              }
            }
            exportRow.总计 = totalScore;
            exportData.push(exportRow);
  
              // console.log('--------------------------------')
          }
        }
        
        exportTitleRow.总计 = '总计';
        exportData.sort((a, b) => a.总计 && b.总计 ? b.总计 - a.总计 : 0);
        exportData = exportData.map((row, index) => ({ ...row, 序号: index + 1 })).map(row => {
          const keys = Object.keys(exportTitleRow);
          const code = {};
          for(const key of keys) {
            code[key] = row[key] ? row[key] : 0;
          }

          return code;
        })

        exportData.unshift(exportTitleRow)
        // console.log('teamMap:',teamMap)

        results.push({ classList: exportData, className: teamMap[teamID] && teamMap[teamID].name });
      }

      return {
        exportList: results, 
        className: results.map(row => row.className)
      }
    }

    // 获取多个训练计划导出试题难度
    async getBulkTrainPlanStatisticsWithDifficulty(trainPlanIDs) {
      const { ctx } = this;
      const { model } = ctx;

      // 取所有牵扯训练计划信息,并形成索引
      const trainPlanMap = {};
      const trainPlanDatas = await model.TrainPlan.findAll({
        where: { id: { [Op.in]: trainPlanIDs }},
        raw: true,
      });
      for(const trainPlanData of trainPlanDatas) {
        trainPlanMap[trainPlanData.id] = trainPlanData;
      }

      // 获取所有牵扯到用户和训练
      const allTrainAndUser = await model.TrainThroughTrainPlan.findAll({
        where: { planID: { [Op.in]: trainPlanIDs } },
        raw: true,
      });

      const userIDsMap = {};
      const trainIDsMap = {};
      // 取出所有train,user,作出索引
      for(const trainAndUserRow of allTrainAndUser) {
        if (!userIDsMap[trainAndUserRow.userID]) {
          userIDsMap[trainAndUserRow.userID] = []
        }
        userIDsMap[trainAndUserRow.userID].push(trainAndUserRow);

        if (!trainIDsMap[trainAndUserRow.trainID]) {
          trainIDsMap[trainAndUserRow.trainID] = {}
        }
        const trainIDsMapByTrainID = trainIDsMap[trainAndUserRow.trainID];
        trainIDsMapByTrainID[trainAndUserRow.userID] = trainAndUserRow;
      }

      // 取所有牵扯训练信息,并形成索引
      const trainIDs = Object.keys(trainIDsMap).map(row => parseInt(row));
      // const trainDatas = await model.Train.findAll({
      //   where: { id: { [Op.in]: trainIDs }},
      //   raw: true,
      // });

      const trainDatas = await ctx.model.query(
        'SELECT * from train where id in (?);',
        { 
          replacements: [trainIDs],
          type: QueryTypes.SELECT
        }
      )

      // console.log('trainDatas:',trainDatas)

      // 取所有牵扯学员信息,并形成索引
      const userIDs = Object.keys(userIDsMap).map(row => parseInt(row));
      const userMap = {};
      const userDatas = await model.User.findAll({
        where: { id: { [Op.in]: userIDs }},
        raw: true,
      });
      for(const userData of userDatas) {
        userMap[userData.id] = userData;
      }

      // 取所有班级与学员关联，作出索引
      const allUserClassDatas = await model.TeamUser.findAll({
        where: { userID: { [Op.in]: userIDs }},
        raw: true,
      });
      const teamIDsMap = {};
      const userToTeamIDsMap = {};
      for(const allUserClassData of allUserClassDatas) {
        if (!teamIDsMap[allUserClassData.teamID]) {
          teamIDsMap[allUserClassData.teamID] = []
        }
        teamIDsMap[allUserClassData.teamID].push(allUserClassData);
        if (!userToTeamIDsMap[allUserClassData.userID]) {
          userToTeamIDsMap[allUserClassData.userID] = [];
        }
        userToTeamIDsMap[allUserClassData.userID].push(allUserClassData.teamID);
      }

      // 取班级信息，作出索引
      const teamIDs = Object.keys(teamIDsMap).map(row => parseInt(row));
      const allClassDatas = await model.Team.findAll({
        where: { id: { [Op.in]: teamIDs }},
        raw: true,
      });
      const teamMap = {};
      for(const allClassData of allClassDatas) {
        teamMap[allClassData.id] = allClassData;
      }

      // 取牵扯训练所有成绩，作出索引
      const userRecords = await model.TrainUserRecord.findAll({
        where: { planID: { [Op.in]: trainPlanIDs } },
        group: ['trainID', 'userID'],
        // order: [['created_at', 'DESC']],
        raw: true,
      })

      // 取大题数据
      const throughtData = await model.TrainThroughTrainPlan.findAll({
        where: { planID: { [Op.in]: trainPlanIDs } },
        raw: true,
        group: ['trainID'],
      })

      const allContents = [];
      let allAnswers = {};
      const trainList = {};
      for(const trainIDRow of throughtData) {
        const { trainID, planID: trainPlanID } = trainIDRow;
        trainList[trainID] = [];
        const fileDir = `${app.config.file.dir}/${this.ctx.schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
        const questionPath = `${fileDir}questions.json`;
        const answerPath = `${fileDir}answer.json`;
        // console.log('questionPath:',questionPath)

        const questionExist = await fs.exists(questionPath);
        if (!questionExist) {
          throw new Error(`无法查找到训练${trainPlanID}的题目文件`);
        }

        const answerExist = await fs.exists(answerPath);
        if (!answerExist) {
          throw new Error(`无法查找到训练${trainPlanID}的答案文件`);
        }

        const content = await ctx.service.course.getFile(questionPath, true);
        const answer = await ctx.service.course.getFile(answerPath, true);

        // 调试代码！！！！！！！！！！！！！！！！！！！！！！！
        // const trainData = await this.ctx.service.trainThroughTrainPlan.getTrainContentToFile(trainID);
        // const content = trainData.questionResults;
        // const answer = trainData.answerResults;
        
        for(const row of content) {
          trainList[trainID].push(row);
          allContents.push({...row, trainID});
        }
        allAnswers = {...allAnswers, ...answer};
      }

      // 获取每题难度，并作出索引
      const allQuestionErrorMap = {};
      const allQuestionPersonMap = {};
      for (const studentRecord of userRecords) {
        let { record, userID, planID, trainID } = studentRecord;
        if(!allQuestionErrorMap[trainID]) {
          allQuestionErrorMap[trainID] = {};
        }
        if(!allQuestionPersonMap[trainID]) {
          allQuestionPersonMap[trainID] = {};
        }
        const allQuestionErrorMapByTrain = allQuestionErrorMap[trainID];
        const allQuestionPersonMapByTrain = allQuestionPersonMap[trainID];

        if (!trainID) {
          const userTrainData = await TrainThroughTrainPlan.findOne({
            where: { 
              userID,
              planID
            },
            raw: true,
          });

          trainID = userTrainData.trainID;
        }
        
        const scoreTrainMap = {};

        // const trainPlanData = trainPlanMap[planID];
        // let scoreField = 'score';
        // if (trainPlanData && trainPlanData.mode === '自由模式') {
        //   scoreField = 'initialScore'
        // }
        // console.log(trainID, 'scoreField:',scoreField)

        // 大题得分统计
        // studentRecord.partScores = [];
        
        for (const block of allContents) {
          const { questions, score } = block;
          
          studentRecord['trainID'] = trainID;

          // let currentScore = 0;
          
          for (const question of questions) {
            if(scoreTrainMap[`${trainID}_${question.id}`]) {
              continue;
            }
            scoreTrainMap[`${trainID}_${question.id}`] = true;
            
            // console.log('question:', question)
            const { questionType = '' } = question;

            if (!(record && record[question.id])) {
              continue;
            }

            let currentResult = record[question.id];

            // if(!allQuestionErrorMapByTrain[`${question.id}_${userToTeamIDsMap[userID]}`] && allQuestionErrorMapByTrain[`${question.id}_${userToTeamIDsMap[userID]}`] !== 0) {
            //   allQuestionErrorMapByTrain[`${question.id}_${userToTeamIDsMap[userID]}`] = 0;
            // }
            // if(!allQuestionPersonMapByTrain[`${question.id}_${userToTeamIDsMap[userID]}`] && allQuestionPersonMapByTrain[`${question.id}_${userToTeamIDsMap[userID]}`] !== 0) {
            //   allQuestionPersonMapByTrain[`${question.id}_${userToTeamIDsMap[userID]}`] = 0;
            // }

            if (questionType === '单选题') {
              if (!getResult(currentResult)) {
                for(const userRow of userToTeamIDsMap[userID]) {
                  allQuestionErrorMapByTrain[`${question.id}_${userRow}`] = addFunction(allQuestionErrorMapByTrain[`${question.id}_${userRow}`], 1)
                }
                // console.log('block[',scoreField,']', block.score)
              }
            } else if (questionType === '综合题') {
              if (record && record[`${question.id}_${userToTeamIDsMap[userID]}`]) {
                let ifResult = true;
                Object.keys(currentResult)
                .forEach(uuid => {
                  if (!getResult(currentResult[uuid])) {
                    ifResult = false;
                  }
                })
                if (!ifResult) {
                  for(const userRow of userToTeamIDsMap[userID]) {
                    allQuestionErrorMapByTrain[`${question.id}_${userRow}`] = addFunction(allQuestionErrorMapByTrain[`${question.id}_${userRow}`], 1)
                  }
                }

                // scoreMap[`${question.id}_${userToTeamIDsMap[userID]}`] += complexScore;
                // console.log('complexScore', complexScore)
              }
            } else {
              if (!getResult(currentResult)) {
                for(const userRow of userToTeamIDsMap[userID]) {
                  allQuestionErrorMapByTrain[`${question.id}_${userRow}`] = addFunction(allQuestionErrorMapByTrain[`${question.id}_${userRow}`], 1)
                }
                // console.log('currentResult[',scoreField,']', currentResult[scoreField])
              }
            }
            for(const userRow of userToTeamIDsMap[userID]) {
              allQuestionPersonMapByTrain[`${question.id}_${userRow}`] = addFunction(allQuestionPersonMapByTrain[`${question.id}_${userRow}`], 1)
            }
            // console.log('scoreMap[`${question.id}_${userToTeamIDsMap[userID]}`]:',scoreMap[`${question.id}_${userToTeamIDsMap[userID]}`])
          }
          // console.log('block:',block);

          // studentRecord.partScores.push(currentScore);
          // scoreMapByTrainID[block.name] = currentScore;
        }
      }
      // console.log('scoreMap:',scoreMap)

      // get answer
      // for (const block of allContents) {
      //   const { questions } = block;
      //   for (const question of questions) {
      //     const { id: questionID, questionType, questionDetail } = question;

      //     // 合并参考答案
      //     if (questionType !== '综合题') {
      //       question.answer = allAnswers[questionID];
      //     } else {
      //       questionDetail.forEach((subQuestion) => {
      //         subQuestion.answer = allAnswers[questionID][subQuestion.UUID];
      //       });
      //     }
      //   }
      // }

      // 整理最后导出字段
      const results = [];
      let trainIndex = 0;
      for(const trainData of trainDatas) {
        trainIndex += 1;
        let exportData = [];

        const trainListByTrain = trainList[trainData.id];
        const exportTitleRow = {
          序号: '序号',
          大题名称: '大题名称',
          '小题名称/题号': '小题名称/题号',
        };
        for(const teamID of teamIDs) {
          exportTitleRow[`难度${teamMap[teamID].id}`] = `${teamMap[teamID].name}-难度`;
        }
        const allQuestionErrorMapByTrain = allQuestionErrorMap[trainData.id];
        const allQuestionPersonMapByTrain = allQuestionPersonMap[trainData.id];

        // console.log('trainListByTrain:',trainListByTrain);
        for(const content of trainListByTrain) {
          const contentName = content.name;
          let questionIndex = 0;
          for(const question of content.questions) {
            // console.log('question:',question)
            questionIndex += 1;
            const code = {
              大题名称: contentName,
              '小题名称/题号': `${question.originalID ? question.originalID : question.id}${question.title ? `/${question.title}` : ''}`
            }
            for(const teamID of teamIDs) {
              if (!allQuestionPersonMapByTrain || !allQuestionPersonMapByTrain[`${question.id}_${teamID}`]) {
                code[`难度${teamMap[teamID].id}`] = '/'
                continue;
              }
              if (!allQuestionErrorMapByTrain[`${question.id}_${teamID}`]) {
                allQuestionErrorMapByTrain[`${question.id}_${teamID}`] = 0;
              }
              code[`难度${teamMap[teamID].id}`] = parseInt(allQuestionErrorMapByTrain[`${question.id}_${teamID}`] / allQuestionPersonMapByTrain[`${question.id}_${teamID}`] * 100 + 0.5)/100;
            }
            exportData.push(code)
          }
        }

        exportData = exportData.map((row, index) => ({ ...row, 序号: index + 1 })).map(row => {
          const keys = Object.keys(exportTitleRow);
          const code = {};
          for(const key of keys) {
            code[key] = row[key] ? row[key] : 0;
          }

          return code;
        })

        exportData.unshift(exportTitleRow)
        // console.log('teamMap:',teamMap)

        results.push({ classList: exportData, className: `${trainIndex}、${trainData.name}` });
      }

      return {
        exportList: results, 
        className: results.map(row => row.className)
      }
    }

    // 实现增加填空题参考答案，并重新计算学生本题得分，并更新学生总分
    async updateFillBlankAnswer(trainPlanID, trainID, questionID, key, addAnswers, transaction) {
      const { ctx } = this;
      const { model } = ctx;
      const { Questions } = model;

      // 根据试题ID修改题目
      // 读取Question表对应题目的答案字段
      const question = await Questions.findOne({
        attributes: ['answer'],
        where: { id: questionID },
        raw: true,
        transaction
      });

      if(!question || !question.answer) {
        throw new Error('该题目不存在或者答案为空');
      }

      // 分别取出得分和答案
      const { score: scoreDict, answer: answerDict } = question.answer;

      // 验证得分和答案中都存在有填空索引
      if (!scoreDict[key] || !answerDict[key]) {
        throw new Error(`该题目不存在对应的填空索引${key}`);
      }

      // 取出填空题答案形成集合
      const answerSet = new Set(answerDict[key]);

      // 合并新答案
      addAnswers.forEach(row => answerSet.add(row));

      // 回写新答案
      answerDict[key] = Array.from(answerSet);

      // 更新题目的答案
      await Questions.update({
        answer: {
          score: scoreDict,
          answer: answerDict
        },
      }, {
        where: { id: questionID },
        transaction
      });

      // 重新生成训练计划的answer.json文件
      await ctx.service.trainThroughTrainPlan.uploadTrainPlanFiles({planID: trainPlanID, trainID}, transaction);

      // 根据训练计划ID和训练ID重新计算学生得分
      await ctx.service.trainThroughTrainPlan.recalculateStudentScore(trainPlanID, trainID, transaction);
    }

    // 根据训练计划ID和训练ID重新计算学生得分
    async recalculateStudentScore(trainPlanID, trainID, transaction) {
      const { ctx } = this;
      const { TrainPlan, TrainUserRecord } = ctx.model;

      const trainPlan = await TrainPlan.findOne({
        where: {
          id: trainPlanID
        },
        raw: true,
        attributes: ['mode', 'score', 'endTime', 'minTrainDuration'],
        transaction
      });

      if (!trainPlan) {
        throw new Error('无法查找到训练');
      }

      // 学校名称
      const schoolSlug = ctx.schoolSlug;

      const fileDir = `${app.config.file.dir}/${schoolSlug}/trainPlan/${trainPlanID}/train/${trainID}/`;
      const questionPath = `${fileDir}questions.json`;
      const answerPath = `${fileDir}answer.json`;

      const questions = await ctx.service.course.getFile(questionPath, true);
      const allAnswer = await ctx.service.course.getFile(answerPath, true);

      const freeMode = trainPlan.mode === '自由模式';

      // 查找学生答题记录
      const trainUserRecords = await TrainUserRecord.findAll({
        where: {
          planID: trainPlanID,
          trainID,
        },
        attributes: ['id', 'initialScore', 'record'],
        raw: true,
        transaction
      });

      // 循环学生答题记录
      for(const trainUserRecord of trainUserRecords) {
        let { id: trainUserRecordID, record: studentAnswerRecord, initialScore = 0 } = trainUserRecord;
        
        if(initialScore) {
          initialScore = parseFloat(initialScore);
        }

        // 从学生记录中分解出学生答案
        const studentAnswer = {};
        for(const questionID in studentAnswerRecord) {
          const questionRecord = studentAnswerRecord[questionID];
          studentAnswer[questionID] = { answer: questionRecord.answer };
        }

        const { judgeResult, studentScore, initialScore: newInitScore } = submitQuestion(questions, allAnswer, studentAnswer, freeMode, initialScore);
        
        const updateRecord = {
          record: { ...studentAnswer, ...judgeResult },
          score: studentScore,
        };

        if (freeMode) {
          updateRecord['initialScore'] = newInitScore;
        }

        await TrainUserRecord.update(updateRecord, {
          where: {
            id: trainUserRecordID
          },
          transaction
        });
      }
    }
  }

  return trainThroughTrainPlanService;
}