const { Op } = require('sequelize');
const sequelize = require('sequelize');
const moment = require('moment');
moment.locale("zh-CN");

module.exports = app => {
  class Tag extends app.Service {
    // 新增题目
    async postTag(tagName, tagType, parentID, questionBankID, transaction){
      const { model } = this.ctx;
      // 查重
      const isExist = await model.Tag.findOne({
        where: { tagName, tagType },
        transaction,
      })
      if(isExist){
        throw Error("已拥有相同名称标签！");
      }
      const result = await model.Tag.create({
        tagName,
        tagType,
        parentID,
        questionBankID,
      }, {transaction});

      return result;
    }

    // 批量新增、修改、删除题目知识点
    async bulkEditTag(questionBankID, treeNodes, transaction){
      const { model } = this.ctx;

      // 检测所传数据不重复
      const nameParentIDMap = {};

      const nodes = [];
      const localNodesMap = {};
      const getNodeObject = async (row, parentID) => {
        const code = {
          tagName: row.text,
          tagType: 'train',
          parentID,
          questionBankID,
        }
        if (nameParentIDMap[code.tagName]) {
          throw new Error(`节点：${code.tagName} 重复`);
        }
        nameParentIDMap[code.tagName] = true;
        if (row.tagID) {
          code.id = row.tagID;
          // console.log('row:',row.text, code.tagName);
          const localNode = localNodesMap[row.tagID];
          // if (row.text === '数据的概念1ddd') {
          //   console.log(row);
          //   // debugger;
          // }
          if (localNode.tagName !== code.tagName || localNode.parentID !== code.parentID) {
            await model.Tag.update({
              tagName: code.tagName,
              parentID: code.parentID,
            }, {
              where: { id: code.id, tagType: 'train', questionBankID },
              transaction,
            })
          }
        } else {
          const result = await model.Tag.create(code, {transaction, raw: true});
          code.id = result.id;
        }
        nodes.push(code.id)
        
        if(row.children && row.children.length) {
          for (const subRow of row.children) {
            await getNodeObject(subRow, code.id);
          }
        }
      }
      // console.log('nodes:',nodes);

      // 查重
      const localNodes = await model.Tag.findAll({
        where: { questionBankID, tagType: 'train' },
        raw: true,
        transaction,
      })
      localNodes.forEach(item => {
        localNodesMap[item.id] = item;
      })

      for (const row of treeNodes) {
        await getNodeObject(row, 0);
      }
      
      // const needAdds = [];
      // const needUpdate = [];

      // for(const row of nodes) {
      //   if (row.id && localNodes.findIndex(subRow => subRow.id === row.id) !== -1) {
      //     needUpdate.push({
      //       id: row.id,
      //       tagName: row.tagName,
      //       parentID: row.parentID,
      //     });
      //     continue;
      //   }

      //   needAdds.push({ tagName: row.tagName, parentID: row.parentID, });
      // }

      const needDelete = localNodes.filter(row => nodes.indexOf(row.id) === -1).map(row => row.id);
      // console.log('needDelete:',needDelete)
      
      // for(const row of needUpdate) {
      //   await model.Tag.update({
      //     tagName: row.tagName,
      //     parentID: row.parentID,
      //   }, {
      //     where: { id: row.id },
      //     transaction,
      //   })
      // }

      // await model.Tag.bulkCreate(needUpdate, {transaction});
      // await model.Tag.bulkCreate(needAdds, {transaction});

      await model.Tag.destroy({
        where: { id: { [Op.in]: needDelete } },
        transaction,
      })
    }

    // 删除标签
    async bulkDeleteTags(tagIDs, transaction) {
      const { model } = this.ctx;
      // 查询是否有题目应用该标签
      const isExist = await model.QuestionTag.count({where: { tagID: {[Op.in]: tagIDs} }, transaction});
      if(isExist) {
        throw Error('有题目引用标签，禁止删除！')
      }
      return await model.Tag.destroy({
        where: { id: {[Op.in]: tagIDs} },
        transaction
      });
    }

    // 获取训练标签
    async getTrainTagList(questionBankID, transaction = false){
      const { model } = this.ctx;
      const whereRequest = { tagType: 'train' }
      if (questionBankID) {
        whereRequest.questionBankID = questionBankID;
      }
      const result = await model.Tag.findAll({
        where: whereRequest,
        attributes: ['id', 'tagName', 'parentID', 'questionBankID'],
        raw: true,
        transaction,
      });

      return {
        train: result
      }
    }

    async getTagChartData(node){
      // !!! TOTEST !!!
      const { model, service } = this.ctx;

      const { questionBankID, sourceSeriesID } = node;

      const questionRequest = { questionBankID };

      if (node.status) {
        try {
          node.status = JSON.parse(node.status)
        } catch (e) {}
        if (node.status.length) {
          questionRequest.status = { [Op.in]: node.status };
        }
      }

      if (node.questionType) {
        try {
          node.questionType = JSON.parse(node.questionType)
        } catch (e) {}
        // console.log('node.questionType:',node.questionType, typeof node.questionType)
        questionRequest.questionType = { [Op.in]: node.questionType };
      }

      // 题目来源试卷系列：查询该系列中所有试卷中包含的题目的ID，限定题目ID在这些ID中
      if (sourceSeriesID) {
        const questionIDs = await service.questions.getTrainsQuestionIDsInSourceSeries(sourceSeriesID);
      
        if (questionIDs.length) {
          questionRequest.id = { [Op.in]: questionIDs };
        }
      }

      return await model.QuestionTag.count({
        include: [{
          model: model.Questions,
          attributes: ['id'],
          as: 'questions',
          where: questionRequest,
          required: true,
        }],
        attributes: ['tagID'],
        group: ['tagID'],
        raw: true,
      });
    }

    async getQuestionsListByTagAndType(node) {
      const { model } = this.ctx;
      const { pageIndex, pageSize } = node;
      const offset = (pageSize == -1) || (!pageSize) ? undefined: (pageIndex - 1) *  pageSize; // 页面大小为0或-1时不做过滤
      const limit = (pageSize == -1) || (!pageSize) ? undefined: pageSize;  // 页面大小为0或-1时不做过滤
      const questionRequest = {
        questionBankID: parseInt(node.questionBankID)
      };

      const includeOptions = [{
        model: model.User,
        as: 'createUser',
        attributes: ['id', 'name', 'avatar'],
      }]
      
      if (node.tagIDs) {
        let tags = node.tagIDs;

        includeOptions.push({
          model: model.Tag,
          as: 'tags',
          where: { id: {[Op.in]: tags} },
          request: true,
        })
      }

      if (node.questionType) {
        questionRequest.questionType = node.questionType;
      }
      if (node.status && node.status.length) {
        questionRequest.status = {[Op.in]: node.status};
      }

      if (node.sourceSeriesID) {
        const questionIDs = await this.ctx.service.questions.getTrainsQuestionIDsInSourceSeries(node.sourceSeriesID);
        if (questionIDs.length) {
          questionRequest.id = { [Op.in]: questionIDs };
        }
      }
     
      if (node.search) {
        questionRequest[Op.or] = [
          { 
            questionDetail: { [Op.substring]: `%${node.search}%` }
          },
          {
            id: { [Op.like]: `%${node.search}%`}
          },
          {
            originalID: { [Op.like]: `%${node.search}%`}
          },
        ];
      }

      const result = await model.Questions.findAll({
        where: questionRequest,
        include: includeOptions,
        offset,
        limit
      });
      // console.log('result:',result.length)

      let trainsUsedIDs = [];
      let resultData = result && result.dataValues ? result.dataValues : result;
      // console.log('resultData:',resultData.length)
      resultData.forEach(element => {
        if(element.trainsUsed && element.trainsUsed.length) {
          trainsUsedIDs = trainsUsedIDs.concat(element.trainsUsed)
        }
      });

      const trainsUsedData = await model.Train.findAll({
        where: {
          id: { [Op.in]: trainsUsedIDs.map(row => parseInt(row)) },
        },
        attributes: ['id', 'name'],
        raw: true,
      });
      // console.log('trainsUsedData:',trainsUsedData)
      
      resultData = resultData.map(row => {
        // console.log('row:',row);
        const rowData = row && row.dataValues ? row.dataValues : row;
        const rowTrainsUsed = rowData.trainsUsed ? rowData.trainsUsed.map(row => parseInt(row)) : []
        return {
          ...rowData,
          trainsUsedData: trainsUsedData.filter(subRow => rowTrainsUsed.indexOf(subRow.id) !== -1)
        }
      })

      return resultData;
    }

    async getQuestionsListByTagAndTypeAdmin(node) {
      const { model } = this.ctx;
      const { pageIndex, pageSize, sortOrder } = node; // 添加sortOrder
      const offset = (pageSize == -1) || (!pageSize) ? undefined: (pageIndex - 1) *  pageSize;
      const limit = (pageSize == -1) || (!pageSize) ? undefined: pageSize;
    
      // 检查权限
      const { ctx } = this;
      const { pcTrainDisable, onlineTrainDisable } = await ctx.service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }
    
      const { sourceSeriesID } = node;

      const questionRequest = {
        questionBankID: parseInt(node.questionBankID)
      };
    
      // 来源系列ID中试卷的题目ID集合
      if(sourceSeriesID) {
        const inSourceSeriesQuestionIDSet = new Set();
        await this.ctx.service.questions.getTrainsQuestionIDsInSourceSeries(sourceSeriesID, inSourceSeriesQuestionIDSet);    
        // 如果有选择tagID，则只显示该系列中的选中标签类型题目。
        questionRequest.id = { [Op.in]: Array.from(inSourceSeriesQuestionIDSet) };
      }

      const includeOptions = [{
        model: model.User,
        as: 'createUser',
        attributes: ['id', 'name', 'avatar'],
      }];
    
      if (node.tagIDs && node.tagIDs.length) {
        let tags = node.tagIDs;
        includeOptions.push({
          model: model.Tag,
          as: 'tags',
          where: { id: { [Op.in]: tags } },
          request: true,
        });

      } else {
        includeOptions.push({
          attributes: ['id', 'tagName'],
          model: model.Tag,
          as: 'tags',
          request: false,
        });
      }
    
      if (node.questionType) {
        questionRequest.questionType = node.questionType;
      }
      if (node.status && node.status.length) {
        questionRequest.status = { [Op.in]: node.status };
      }
    
      if (node.search) {
        questionRequest[Op.or] = [
          { questionDetail: { [Op.substring]: `%${node.search}%` } },
          { id: { [Op.like]: `%${node.search}%` } },
          { originalID: { [Op.like]: `%${node.search}%` } },
        ];
      }

      const result = await model.Questions.findAll({
        where: questionRequest,
        include: includeOptions,
        offset,
        limit,
        order: [['id', sortOrder ? 'ASC' : 'DESC']], // 根据id排序
      });
     
      let trainsUsedIDs = [];
      let resultData = result && result.dataValues ? result.dataValues : result;
    
      resultData.forEach(element => {
        if (element.trainsUsed && element.trainsUsed.length) {
          trainsUsedIDs = trainsUsedIDs.concat(element.trainsUsed);
        }
      });
    
      const trainsUsedData = await model.Train.findAll({
        where: {
          id: { [Op.in]: trainsUsedIDs.map(row => parseInt(row)) },
        },
        attributes: ['id', 'name'],
        raw: true,
      });
    
      resultData = resultData.map(row => {
        const rowData = row && row.dataValues ? row.dataValues : row;

        if(rowData.trainsUsed && !rowData.trainsUsed.map) {
          console.error('rowData.trainsUsed is not an array:', rowData.trainsUsed);
        }

        const rowTrainsUsed = rowData.trainsUsed ? rowData.trainsUsed.map(row => parseInt(row)) : [];
        return {
          ...rowData,
          trainsUsedData: trainsUsedData.filter(subRow => rowTrainsUsed.indexOf(subRow.id) !== -1)
        };
      });
    
      return resultData;
    }

    async getQuestionsCountByTagAndType(node) {
      const { model } = this.ctx;
      const questionRequest = {
        questionBankID: parseInt(node.questionBankID)
      };
      
      if (node.tagIDs) {
        let tags = node.tagIDs;
        const tagResults = await model.QuestionTag.findAll({
          include: [{
            attributes: ['id'],
            model: model.Tag,
            as: 'tag',
            request: true,
          }],
          where: { tagID: {[Op.in]: tags} },
          raw: true,
          group: ['questionID'],
        });

        questionRequest.id = { [Op.in]: tagResults.map(row => row.questionID) }
      }

      if (node.search) {
        questionRequest[Op.or] = [
          { 
            questionDetail: { [Op.substring]: `%${node.search}%` }
          },
          {
            id: { [Op.like]: `%${node.search}%`}
          },
          {
            originalID: { [Op.like]: `%${node.search}%`}
          },
        ];
      }

      if (node.status && node.status.length) {
        questionRequest.status = { [Op.in]: node.status };
      }
      const result = await model.Questions.count({
        where: questionRequest,
        group: ['questionType']
      });

      return result;
    }

    // 管理员根据标签和难度获取题目数量
    async getQuestionsCountByTagAndTypeAdmin(node) {
      const { model, service } = this.ctx;

      // 检查权限
      const { pcTrainDisable, onlineTrainDisable } = await service.authority.getTrainAuth();
      if (pcTrainDisable && onlineTrainDisable) {
        throw new Error('您的授权已到期')
      }

      const questionRequest = {
        questionBankID: parseInt(node.questionBankID)
      };
      
      const allQuestionIDSet = new Set();

      // 根据标签ID查找到全部题目ID
      if (node.tagIDs) {
        let tags = node.tagIDs;
        const tagResults = await model.QuestionTag.findAll({
          include: [{
            attributes: ['id'],
            model: model.Tag,
            as: 'tag',
            request: true,
          }],
          where: { tagID: {[Op.in]: tags} },
          raw: true,
          group: ['questionID'],
        });

        if(tagResults.length) {
          for (const row of tagResults) {
            allQuestionIDSet.add(row.questionID);
          }
        }
      }

      // 根据搜索标签
      if (node.search) {
        questionRequest[Op.or] = [
          { 
            questionDetail: { [Op.substring]: `%${node.search}%` }
          },
          {
            id: { [Op.like]: `%${node.search}%`}
          },
          {
            originalID: { [Op.like]: `%${node.search}%`}
          }
        ];
      }

      // 根据题目状态
      if (node.status && node.status.length) {
        questionRequest.status = { [Op.in]: node.status };
      }

      // 题目来源试卷系列：只有该系列试卷中出现的题才算
      let inSourceSeriesQuestionIDs = [];
      if(node.sourceSeriesID) {
        const inSourceSeriesQuestionIDSet = new Set();
        inSourceSeriesQuestionIDs = await service.questions.getTrainsQuestionIDsInSourceSeries(node.sourceSeriesID, inSourceSeriesQuestionIDSet);

        // 过滤全部题目ID，只保留在该系列中出现的题目ID
        for (const questionID of allQuestionIDSet) {
          if (!inSourceSeriesQuestionIDSet.has(questionID)) {
            allQuestionIDSet.delete(questionID);
          }
        }
      }

      const questionIDs = Array.from(allQuestionIDSet);
      if (questionIDs.length) {
        questionRequest.id = { [Op.in]: questionIDs };
      }
      else if(inSourceSeriesQuestionIDs.length) {
        questionRequest.id = { [Op.in]: inSourceSeriesQuestionIDs };
      }

      const result = await model.Questions.count({
        where: questionRequest,
        group: ['questionType']
      });

      return result;
    }

    async getQuestionCountByTagAndDifficulty(node) {
      const { model, service } = this.ctx;

      const { questionBankID, tagIDs } = node;

      const questionRequest = {
        questionBankID: parseInt(questionBankID)
      };

      const allQuestionIDSet = new Set();

      if (tagIDs) {
        let tags = tagIDs;
        const tagResults = await model.QuestionTag.findAll({
          include: [{
            attributes: ['id'],
            model: model.Tag,
            as: 'tag',
            request: true,
          }],
          where: { tagID: {[Op.in]: tags} },
          raw: true,
          group: ['questionID'],
        });

        if(tagResults.length) {
          for (const row of tagResults) {
            allQuestionIDSet.add(row.questionID);
          }
        }
      }

      // 搜索
      if (node.search) {
        questionRequest.questionDetail = { [Op.substring]: node.search }
      }

      // 题目状态
      if (node.status) {
        questionRequest.status = node.status;
      }

      // 题目来源试卷系列：只有该系列试卷中出现的题才算
      let inSourceSeriesQuestionIDs = [];
      if(node.sourceSeriesID) {
        const inSourceSeriesQuestionIDSet = new Set();
        inSourceSeriesQuestionIDs = await service.questions.getTrainsQuestionIDsInSourceSeries(node.sourceSeriesID, inSourceSeriesQuestionIDSet);

        // 过滤全部题目ID，只保留在该系列中出现的题目ID
        for (const questionID of allQuestionIDSet) {
          if (!inSourceSeriesQuestionIDSet.has(questionID)) {
            allQuestionIDSet.delete(questionID);
          }
        }
      }

      const questionIDs = Array.from(allQuestionIDSet);
      if (questionIDs.length) {
        questionRequest.id = { [Op.in]: questionIDs };
      }
      else if(inSourceSeriesQuestionIDs.length) {
        questionRequest.id = { [Op.in]: inSourceSeriesQuestionIDs };
      }
      
      const result = await model.Questions.count({
        where: questionRequest,
        group: ['difficultyConfig', 'questionType']
      });

      return result;
    }

    async getTag(){
      const { model } = this.ctx;
      return await model.Tag.findAll();
    }

    async findTag(tagName){
      const { model } = this.ctx;
      const result = await model.Tag.findOne({
        where: {tagName: tagName}
      })

      if(result){
        return '该标签已存在。'
      } else {
        return '该标签可以创建。'
      }
    }

    // 批量根据题目id与标签id修改标签关联
    async updateBulkQuestionTag(nodes, transaction) {
      for(const node of nodes) {
        const { id, tags, difficultyConfig } = node;

        await this.ctx.service.tag.updateQuestionTag(id, tags, difficultyConfig, transaction);
      }
    }

    // 根据题目id与标签id修改标签关联
    async updateQuestionTag(questionID, tags, difficultyConfig, transaction) {
      const { model } = this.ctx;
      const nowTags = await model.QuestionTag.findAll({
        where: {
          questionID,
        },
        transaction,
        raw: true,
      })

      if (difficultyConfig) {
        await model.Questions.update({
          difficultyConfig,
        }, {
          where: {
            id: questionID,
          },
          transaction,
        })
      }

      if (tags) {
        const needAdds = [];
        const needDelete = [];
        nowTags.forEach(element => {
          const findRowIndex = tags.findIndex(row => parseInt(row) === element.tagID);
  
          if (findRowIndex === -1) {
            needDelete.push(element.tagID)
          }
        });
  
        tags.forEach(row => {
          const findRowIndex = nowTags.findIndex(element => parseInt(row) === element.tagID);
  
          if (findRowIndex === -1) {
            needAdds.push(row)
          }
        });
        // console.log('needAdds', needAdds);
        // console.log('needDelete', needDelete);
  
        if (needDelete.length) {
          await model.QuestionTag.destroy({
            where: {
              questionID,
              tagID: { [Op.in]: needDelete } 
            },
            transaction,
          });
        }
        
        if (needAdds.length) {
          await model.QuestionTag.bulkCreate(needAdds.map(row => ({
            questionID,
            tagID: row,
          })), {
            transaction,
          });
        }
      }
    }
  }
  return Tag;
}  
