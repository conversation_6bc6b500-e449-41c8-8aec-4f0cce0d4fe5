const moment = require('moment');
const { Op, cast, col, QueryTypes } = require('sequelize');

const checkCount = (data) => {
   let obj = {};
   if (!Array.isArray(data) || !data.length) {
       return {};
   }
   data.forEach(dataItem => {
    dataItem = dataItem.dataValues ? dataItem.dataValues : dataItem;
       if(obj[dataItem.userID]) {
        obj[dataItem.userID] += 1;
       } else {
        obj[dataItem.userID] = 1;
       }
   })
   return obj;
}

module.exports = app => {
  
  class Team extends app.Service {
    // 获取去全部班级（精简）
    async getClassListAllLess(year) {
      const { model } = this.ctx;
      const { Team, TeamUser } = model;

      const condition = {};
      if(year){
        condition.year = {
          [Op.like]: '%' + year + '%'
        };
      }

      const teamDatas = await Team.findAll({
        attributes: ['id', 'name', 'year'],
        where: condition,
        order: [['created_at', 'desc']],
        raw: true,
      });
      // console.log('teamDatas:',teamDatas)

      const teamIDs = teamDatas.map(row => row.id);

      const counts = await TeamUser.count({
        where: { teamID: { [Op.in]: teamIDs } },
        raw: true,
        group: ['teamID']
      })

      const result = teamDatas.map(row => {
        const countRow = counts.find(subRow => subRow.teamID === row.id);
        return {
          ...row,
          count:  countRow ? countRow.count : 0
        }
      })

      return result
    }


    // 创建班级用户关联
    async userClassContact(userID, classIDs, transaction) {
      const { model } = this.ctx;
      const { TeamUser } = model;

      let createArrays = [];
      
      for(let classID of classIDs){
          classID = parseInt(classID, 10);
          // 查找是否重复
          const result = await TeamUser.findOne({
              where: { userID, teamID: classID },
              transaction
          });

          if (result) {
              continue;
          }

          createArrays.push({userID, teamID: classID });
      }
      await TeamUser.bulkCreate(createArrays, { transaction })

      return 1
      
    }

    // 搜索班级和班级列表
    async getClassList(offset, pageSize, search, year, transaction) {
      const { model } = this.ctx;
      const { Team, TeamUser, User } = model;

      const pageNum = (parseInt(offset) - 1) * (parseInt(pageSize));
      
      const condition = {};
      if (search) {
          condition.name = {
              [Op.like]: '%' + search + '%'
          };
      }

      if(year){
        condition.year = {
            [Op.like]: '%' + year + '%'
        };
      }

      const classList =  await Team.findAll({
          where: condition,
          include:[{
              model: TeamUser,
              as: 'teamUser',
            //   where:{schoolSlug: this.ctx.schoolSlug},
              required: false,
              include: [
                  {
                      model: User,
                      as: 'User',
                      attributes: ['id'],
                  }
              ],
          }],
          limit: pageSize, 
          offset: pageNum,
          order: [['id', 'desc']],
          transaction
      });

      const total = await Team.count({
          where: condition,
          transaction
      });

      return { classList, total };

    }
      // 获取班级列表和人员列表
      async filterUser(classID, offset, pageSize, search) {
        const { model } = this.ctx;
        const { User, TeamUser } = model;

        const pageNum = (parseInt(offset) - 1) * (parseInt(pageSize));

        const condition = {};
        if (search) {
            condition[Op.and] = {
                [Op.or]: {
                    name: {
                        [Op.like]: '%' + search + '%'
                    },
                    username: {
                        [Op.like]: '%' + search + '%'
                    }
                },
            };
        }

        let userList = null;
        let total = 0;

        if (classID) {
            const classUsersRep = await TeamUser.findAll({
                where: {teamID: classID },
                attributes: ['userID'],
            });

            const userIDMaps = [];
            for(let each of classUsersRep){
                userIDMaps.push(each.dataValues.userID);
            }

            userList = await User.findAll({
                attributes: ['id', 'name', 'avatar', 'state', 'username', 'school', 'sen'],
                where: {
                    ...condition,
                    id: {[Op.in]: userIDMaps}
                },
                order: [[cast(col('user.username'), 'CHAR'), 'asc']],
                limit: pageSize, 
                offset: pageNum,
            });

            userList = userList.map((data) =>({
                ...data.dataValues,
                 classID
            }));

            total = await model.TeamUser.count({
                where: { teamID: classID },
                include: [
                    {
                        model: User,
                        as: 'User',
                        attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'],
                        where: condition,
                        required: true,
                    }
                ],
            });

        } else {
            userList = await User.findAll({
                where: condition,
                attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'], 
                limit: pageSize, 
                offset: pageNum,
                order: [[cast(col('user.username'), 'CHAR'), 'asc']]
            });

            total = await model.User.count({
                where: condition,
            });
        }
        
        return { userList, total, pageNum: offset, pageSize, classID }
    }
    
    // 获取班级列表和人员列表,
    async filterUserWithComputerRoomTrain(classID) {
      const { model } = this.ctx;
      const { User, TeamUser } = model;

      const condition = {};

      let userList = null;
      let total = 0;

      if (classID) {
          const classUsersRep = await TeamUser.findAll({
              where: {teamID: classID },
              attributes: ['userID'],
          });

          const userIDMaps = [];
          for(let each of classUsersRep){
              userIDMaps.push(each.dataValues.userID);
          }

          userList = await User.findAll({
              // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school', 'sen'],
              where: {
                  ...condition,
                  id: {[Op.in]: userIDMaps}
              },
              order: [[cast(col('user.username'), 'CHAR'), 'asc']],
          });

          userList = userList.map((data) =>({
              ...data.dataValues,
               classID
          }));

          total = await model.TeamUser.count({
              where: { teamID: classID },
              include: [
                  {
                      model: User,
                      as: 'User',
                      // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'],
                      where: condition,
                      required: true,
                  }
              ],
          });

      } else {
          userList = await User.findAll({
              where: condition,
              // attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'], 
              order: [[cast(col('user.username'), 'CHAR'), 'asc']]
          });

          total = await model.User.count({
              where: condition,
          });
      }
      
      // return { userList, total, pageNum: offset, pageSize, classID }
      return userList
  }

    // 获取班级列表和所有人员列表
    async getClassAllUsers(classID, search) {
        const { model } = this.ctx;
        const { User, TeamUser } = model;

        const condition = {};
        if (search) {
            condition[Op.and] = {
                [Op.or]: {
                    name: {
                        [Op.like]: '%' + search + '%'
                    },
                    username: {
                        [Op.like]: '%' + search + '%'
                    }
                },
            };
        }

        let userList = null;
        let total = 0;

        if (classID) {
            const classUsersRep = await TeamUser.findAll({
                where: {teamID: classID },
                attributes: ['userID'],
            });

            const userIDMaps = [];
            for(let each of classUsersRep){
                userIDMaps.push(each.dataValues.userID);
            }

            userList = await User.findAll({
                attributes: ['id', 'name', 'avatar', 'state', 'username', 'school', 'sen'],
                where: {
                    ...condition,
                    id: {[Op.in]: userIDMaps}
                },
                order: [[cast(col('user.username'), 'CHAR'), 'asc']],
            });

            userList = userList.map((data) =>({
                ...data.dataValues,
                 classID
            }));

            total = await model.TeamUser.count({
                where: { teamID: classID },
                include: [
                    {
                        model: User,
                        as: 'User',
                        attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'],
                        where: condition,
                        required: true,
                    }
                ],
            });

        } else {
            userList = await User.findAll({
                where: condition,
                attributes: ['id', 'name', 'avatar', 'state', 'username', 'school'], 
                order: [[cast(col('user.username'), 'CHAR'), 'asc']]
            });

            total = await model.User.count({
                where: condition,
            });
        }
        
        return { userList, total, classID }
    }

    // 删除班级
    async deleteClass(id, deleteUser, transaction){
        const { model } = this.ctx;
        let deleteArray = [];
        let arr = [];
        const list = await model.TeamUser.findAll({
            where: {teamID: id },
            transaction
        });

        await model.Team.destroy({
            where: {id},
            transaction
        });

        // 取出班级关联id删除
        let cuId = [];
        
        for(let each of list){
            cuId.push(each.id)
            arr.push(each.userID);
        }

        // 查询学生在team_user中是否存在多个关联
        const associationArray = await model.TeamUser.findAll({
            where: {userID: {
                [Op.in]: arr
            }},
            transaction
        });

        const allCount = checkCount(associationArray);
        // associationArray
        for(let i = 0; i < arr.length; i += 1) {
            const count = allCount[arr[i]];
           if (!count || count === 1) {
                deleteArray.push(arr[i])
           }
        }

        await model.TeamUser.destroy({
            where: {id: {[Op.in]: cuId}},
            transaction
        });


        if(deleteUser){
            await model.User.destroy({
                where: {id: {[Op.in]: deleteArray}, adminAuthority: null },
                transaction
            });
        }

        // 获取当前班级数量存储在学校中
        const teamNum = await model.Team.count({ transaction });
        
        // 数据库存储hash
        const { mainModel } = app;
        // 启用事务
        const transactionMain = await mainModel.transaction({autocommit: false});
        try{
            // 减少次数
            await mainModel.query(`UPDATE school SET classNum = ? where slug = ?`,
            { replacements: [teamNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });
        }catch(e){
            console.log(e, 'UPDATE classNum')
            await transactionMain.rollback();
            return;
        }

        await transactionMain.commit();

        return '1';
    }


     // 批量删除班级
     async deleteClasses(ids, deleteUser, transaction){
        const { model } = this.ctx;
        let deleteArray = [];
        let arr = [];
      
        // 查找与这些班级关联的学生信息
        const list = await model.TeamUser.findAll({
          where: { teamID: { [Op.in]: ids } },
          transaction
        });
      
        // 取出所有关联的班级id和学生id
        let cuId = [];
        for (let each of list) {
          cuId.push(each.id);
          arr.push(each.userID);
        }
      
        // 删除班级
        await model.Team.destroy({
          where: { id: { [Op.in]: ids } },
          transaction
        });
      
        // 查询学生在 team_user 中是否存在多个关联
        const associationArray = await model.TeamUser.findAll({
          where: { userID: { [Op.in]: arr } },
          transaction
        });
      
        const allCount = checkCount(associationArray);
      
        for (let i = 0; i < arr.length; i += 1) {
          const count = allCount[arr[i]];
          if (!count || count === 1) {
            deleteArray.push(arr[i]);
          }
        }
      
        // 删除班级与学生关联的记录
        await model.TeamUser.destroy({
          where: { id: { [Op.in]: cuId } },
          transaction
        });
      
        // 如果 deleteUser 为 true，删除学生
        if (deleteUser) {
          await model.User.destroy({
            where: {
              id: { [Op.in]: deleteArray },
              adminAuthority: null
            },
            transaction
          });
        }
      
        // 获取当前班级数量存储在学校中
        const teamNum = await model.Team.count({ transaction });
      
        // 数据库存储 hash
        const { mainModel } = app;
        // 启用事务
        const transactionMain = await mainModel.transaction({ autocommit: false });
      
        try {
          // 更新学校班级数量
          await mainModel.query(
            `UPDATE school SET classNum = ? WHERE slug = ?`,
            { replacements: [teamNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE }
          );
        } catch (e) {
          console.log(e, 'UPDATE classNum');
          await transactionMain.rollback();
          return;
        }
      
        await transactionMain.commit();
      
        return '1';
    }

    // 检查班级名称
    async checkClassName(name, year) {
        const { model } = this.ctx;
        const { Team } = model;
        const classResult = await Team.count({
            where: {
                name: name,
                year
            }
        });

        if (!classResult) {
            return '没有重复值';
        }

        return classResult;
    }

      // 创建班级
      async createClass(name,year, type='class', transaction) {
        const { model } = this.ctx;
        const { Team } = model;
        // 查重
        const isExist = await Team.findOne({
            where: { name, year },
            transaction
        })
        if(isExist){
            throw Error(`${year}学年，班级名称${name}重复`)
        }
        const team = await Team.create({
            name,
            year,
            type,
        }, { transaction });

        // 获取当前班级数量存储在学校中
        const teamNum = await Team.count({ transaction });
        
        // 数据库存储hash
        const { mainModel } = app;
        // 启用事务
        const transactionMain = await mainModel.transaction({autocommit: false});
        try{
            // 减少次数
            await mainModel.query(`UPDATE school SET classNum = ? where slug = ?`,
            { replacements: [teamNum, this.ctx.schoolSlug], transaction: transactionMain, type: QueryTypes.UPDATE });
        }catch(e){
            console.log(e, 'UPDATE classNum')
            await transactionMain.rollback();
            return;
        }

        await transactionMain.commit();

        return team;
    }

    async putClassName(id, name, transaction) {
        const { model } = this.ctx;
        const { Team } = model;
        const data = await Team.findOne({
            where: { id },
            transaction
        })

        const isExist = await Team.findOne({
            where: { year: data.year, name },
            transaction
        })
        if(isExist){
            throw Error(`${data.year}，班级名称${name}重复`)
        }
        return await Team.update({
            name
        },{
            where:{id},
            transaction
        })
    }

    async changeClassYear(ids, year, transaction) {
        const { ctx } = this;
        const { model } = ctx;

        if (!year) {
            throw new Error('请选择要迁移的学年')
        }

        if (!ids || !ids.length) {
            throw new Error('请选择班级')
        }

        const ifExist = await model.Team.findAll({
            where: {
                id: {
                    [Op.in]: ids
                }
            },
            transaction
        });

        if (!ifExist || !ifExist.length) {
            throw new Error('班级不存在')
        }

        // 检查要迁移的学年里是否有名称重复的班级
        const duplicateClasses = await model.Team.findAll({
            where: {
                year,
                name: {
                    [Op.in]: ifExist.map(i => i.name)
                }
            },
            transaction
        });

        if (duplicateClasses && duplicateClasses.length) {
            const duplicateClassNames = duplicateClasses.map(i => i.name);
            throw new Error(`班级名称 ${duplicateClassNames.join('、')} 重复！`);
        }

        await model.Team.update({
            year
        }, {
            where: {
                id: {
                    [Op.in]: ids
                }
            },
            transaction
        });
    }

    // 删除班级用户关联
    async deleteUserClassContact(userID, classID, transaction) {
        const { model } = this.ctx;
        const { TeamUser } = model;
        return await TeamUser.destroy({
            where: {userID, teamID: classID},
            transaction
        });
    }

    async getTeamUser(classID) {
        const { model } = this.ctx;
        const { TeamUser } = model;
        return await TeamUser.findAll({
            where: { teamID: classID },
            attributes: ['userID']
        });
    }

    async getClassIDs(userID){
        const { model } = this.ctx;
        const { TeamUser } = model;
        const result = await TeamUser.findAll({
            where: { userID },
            attributes: ['teamID']
        });

        return result.map(i=>i.teamID);
    }

    async getTeamDetail(teamID) {
        const { model } = this.ctx;
        const { Team, TeamUser, User } = model;
        const result = await Team.findOne({
            where: { id: teamID },
            attributes: ['id','name'],
            include: [{
                model: TeamUser,
                as: 'teamUser'
            }]
        });

        const { name, teamUser } = result;

        if (!teamUser || !teamUser.length) {
            return { teamName: name, studentList: [] };
        }
        const userIDs = teamUser.map((data) => data.userID);

        const studentList = await User.findAll({
            where: { id: { [Op.in]: userIDs }},
            attributes: ['id', 'name', 'username'],
            raw: true
        })

        return { teamName: name, studentList };
    }

    
    async recoverTeamAndUsers(ids) {
        const { ctx } = this;
        const { model } = ctx;

        await model.query("UPDATE `Team` SET deleted_at = null WHERE `id` IN (?)",
        { replacements: [ids], type: QueryTypes.UPDATE }
        )

        await model.query("UPDATE `team_user` SET deleted_at = null WHERE teamID in (?)",
        { replacements: [ids], type: QueryTypes.UPDATE }
        )

        await model.query("UPDATE `user` SET deleted_at = null WHERE id in (Select userID from team_user where teamID in (?))",
        { replacements: [ids], type: QueryTypes.UPDATE })
    }

    async getClassListAndUser(){
        const { ctx } = this;
        const { model } = ctx;
        const { Team, User, TeamUser } = model;

        const userResult = await User.findAll({
            // attributes: ['id', 'name'],
            order: [['created_at', 'desc']],
            raw: true,
            include: [{
                model: Team,
                as: 'team'
            }]
        });

        const classResult = await Team.findAll({
            attributes: ['id', 'name', 'year'],
            raw: true,
        })

        const yearClassMap = [];
        // 将班级按照年份处理
        for(const node of classResult){
            if(!yearClassMap.find(item => item.yearName===node.year)){
                yearClassMap.push({ yearName: node.year, classList: classResult.filter(item => item.year === node.year).map(item =>{ return { ...item, userList: [] } }) });
            }
        }

        for(const node of userResult){
           if( node && node['team.id'] && node['team.year'] ){
              if(yearClassMap.find(item=> item.yearName ===  node['team.year']).classList.find(item => item.id === node['team.id'])){
                yearClassMap.find(item=> item.yearName ===  node['team.year']).classList.find(item => item.id === node['team.id']).userList.push({ name: node.name, id: node.id,  });
              }
           }
        }


        return yearClassMap;
    }

    async resetClassPassword({ selectClassIDs, resetPassword }, transaction) {
        const { ctx } = this;
        const { model } = ctx;

        if (!selectClassIDs || !selectClassIDs.length) {
            throw new Error('请选择班级');
        }

        if (!resetPassword) {
            throw new Error('请提供重置密码');
        }

        const allTeamUsers = await model.TeamUser.findAll({
            where: {
                teamID: {
                    [Op.in]: selectClassIDs
                }
            },
            raw: true,
            transaction
        });

        if (!allTeamUsers || !allTeamUsers.length) {
            throw new Error('班级内无用户');
        }

        const allUserIDs = allTeamUsers.map(i => i.userID);

        await model.User.update({
            password: resetPassword
        }, {
            where: {
                id: {
                    [Op.in]: allUserIDs
                }
            },
            transaction
        });
    }

    // 检查对应学年的班级名称是否有重复
    async checkDuplicateClassNames(params, transaction) {
        const { ctx } = this;
        const { model } = ctx;

        // 检查对应学年的班级名称是否重复
        const { uploadData = [], selectedOldClassIds, oldClassNameAffix, oldClassOption } = params;

        let conditionByYear = [];

        // 获取全部旧班级名称
        let allOldClasses = [];
        if (oldClassOption === 'rename') {
            allOldClasses = await model.Team.findAll({
                where: {
                    id: {
                        [Op.in]: selectedOldClassIds
                    },
                },
                raw: true,
                transaction
            });
        }

        for (const item of uploadData) {
            const { year, classList = [] } = item;

            if (!year) {
                throw new Error('班级没有学年');
            }

            if (!classList.length) {
                throw new Error(`${year}学年没有选择班级`);
            }

            let classNameList = classList.map(i => i.className); // 需检查的班级名称
            let queryCondition = {
                year: `${year}学年`
            };

            // 保留旧班级，检查新导入班级名称与现有班级名称是否重复
            if (oldClassOption === 'default') {
                queryCondition = {
                    ...queryCondition,
                    name: {
                        [Op.in]: classNameList
                    }
                };
            }

            // 检查新导入班级名称 与 现有班级，除去要删除班级，名称是否重复
            if (oldClassOption === 'delete') {
                if (!selectedOldClassIds || !selectedOldClassIds.length) {
                    throw new Error('请选择旧班级')
                }

                queryCondition = {
                    ...queryCondition,
                    [Op.and]: {
                        name: {
                            [Op.in]: classNameList
                        },
                        id: {
                            [Op.notIn]: selectedOldClassIds
                        },
                    }
                };
            }

            // 旧班级重命名后，是否仍有重复班级
            if (oldClassOption === 'rename') {
                if (!oldClassNameAffix) {
                    throw new Error('如需修改旧班级名称，请提供名称后缀');
                }

                if (!selectedOldClassIds || !selectedOldClassIds.length) {
                    throw new Error('请选择旧班级')
                }

                const oldClasses = allOldClasses.filter(i => i.year === `${year}学年`);

                if (!oldClasses || !oldClasses.length) {
                    throw new Error(`${year}学年选择的旧班级不存在`);
                }

                const oldClassNames = oldClasses.map(i => `${i.name}${oldClassNameAffix}`);
                // 检查新导入班级名称，与旧班级改名后的名称是否重复
                const findDuplicates = oldClassNames.filter(i => classNameList.indexOf(i) !== -1);
                if (findDuplicates && findDuplicates.length) {
                    throw new Error(`新导入班级名称 ${findDuplicates.join('、')} 与改名后的旧班级名称重复`);
                }
                
                // 检查新导入班级名称 加 旧班级改名后的名称 与 现有班级，除去要改名班级，是否重复
                classNameList = classNameList.concat(oldClassNames);
                queryCondition = {
                    ...queryCondition,
                    [Op.and]: {
                        name: {
                            [Op.in]: classNameList
                        },
                        id: {
                            [Op.notIn]: selectedOldClassIds
                        },
                    }
                };
            }

            if (!classNameList.length) {
                throw new Error(`${year}学年没有导入新班级`);
            }

            conditionByYear.push(queryCondition);
        }

        const allConditions = {
            [Op.or]: conditionByYear
        };

        const duplicateClasses = await model.Team.findAll({
            where: allConditions,
            raw: true,
            transaction
        });

        if (duplicateClasses && duplicateClasses.length) {
            const duplicateClassNames = duplicateClasses.map(i => i.name);
            throw new Error(`班级名称 ${duplicateClassNames.join('、')} 重复！`);
        }
    }

    // 导入班级数据检查，返回检查数据预览
    async checkRearrangeClasses(params, transaction) {
        const { ctx } = this;
        const { model } = ctx;
        
        const { uploadData, rearrangeMode, oldClassOption, oldClassNameAffix, selectedOldClassIds } = params;

        // 重新分班，改变学号
        if (rearrangeMode === 'changeUsername') {
            // 检查新账号与现存其他旧账号不重复
            let newUsernames = uploadData.map((item) => item.newUsername);

            const existAccounts = await model.User.findAll({
                where: {
                    username: {
                        [Op.in]: newUsernames
                    }
                },
                raw: true,
                transaction
            });
    
            if (existAccounts && existAccounts.length) {
                const existsUsername = existAccounts.map(i => i.username);
                throw new Error(`上传账号与现有账号 ${existsUsername.join()} 重复`);
            }
            
            // 旧账号设置为空时表达加入新学生，否则需检查旧账号是否都存在
            const oldAccounts = uploadData.filter(i => i.username);

            if (oldAccounts && oldAccounts.length) {
                const oldUsernames = oldAccounts.map((item) => item.username);

                const existAccounts = await model.User.findAll({
                    where: {
                        username: {
                            [Op.in]: oldUsernames
                        }
                    },
                    raw: true,
                    transaction
                });

                for (const oldname of oldUsernames) {
                    const exist = existAccounts.find(i => i.username === String(oldname));
                    if (!exist) {
                        throw new Error(`旧账号为${oldname}的账号不存在，请检查！如果是新账号，请不要填写账号这列的内容`);
                    }
                }
            }
        }

        // 转换数据格式为 { year, classList: { className, userList: { name, ... } } }
        const classMap = {};
        for (const item of uploadData) {
            const { className, year, ...rest } = item;

            if (!classMap[year]) {
            classMap[year] = {};
            }

            if (!classMap[year][className]) {
            classMap[year][className] = [];
            }

            classMap[year][className].push({ ...rest });
        }

        const newUploadData = [];
        for (const item in classMap) {
            const yearClasses = classMap[item];

            const yearClassList = [];
            for (const classItem in yearClasses) {
                const userList = yearClasses[classItem];
                if (!userList || !userList.length) {
                    continue;
                }

                yearClassList.push({
                    className: classItem,
                    userList: yearClasses[classItem],
                });
            }

            if (!yearClassList || !yearClassList.length) {
                continue;
            }

            newUploadData.push({
                year: item,
                classList: yearClassList,
            });
        }

        // 检查对应学年的班级名称是否重复
        await ctx.service.team.checkDuplicateClassNames({ uploadData: newUploadData, selectedOldClassIds, oldClassNameAffix, oldClassOption }, transaction);

        // 统计
        const importAccountNumber = uploadData.length;

        // 改名、删除班级学生数量
        const affectedStudentNumber = await model.TeamUser.count({
            where: {
                teamID: {
                    [Op.in]: selectedOldClassIds 
                }
            },
            raw: true,
            transaction
        });

        let totalNewAccountNumber = 0;

        
        const allUsernames = uploadData.filter(i => i.username).map((item) => item.username);

        const existAccounts = await model.User.findAll({
            where: {
                username: {
                    [Op.in]: allUsernames
                }
            },
            raw: true,
            transaction
        });

        // 如果是教师账号，给出提示，不修改密码
        let existAdminUsers = existAccounts.filter(i => i.adminAuthority).map(i => i.username);
        
        // 重新分班，不改变学号
        if (rearrangeMode === 'keepUsername') {
            for (const name of allUsernames) {
                const exist = existAccounts.find(i => i.username === name);
                if (!exist) {
                    totalNewAccountNumber++;
                }
            }
        } else {
            totalNewAccountNumber = uploadData.filter((item) => !item.username).length;
        }

        return {
            newUploadData,
            importAccountNumber,
            affectedStudentNumber,
            totalNewAccountNumber,
            existAdminUsers,
        };
    }

    // 导入新班级，处理旧班级
    async importRearrangeClasses(params, transaction) {
        const { ctx } = this;
        const { model } = ctx;
        
        // 参数校验
        const { 
            rearrangeMode, // keepUsername|changeUsername
            oldClassOption, // default|rename|delete
            oldClassNameAffix,
            selectedOldClassIds, // [teamID]
            // uploadData, // [{ year, className, userList: [] }]
            rawUploadData, // [{ year, classList: [{ className, userList: [] }] }]
            resetPassword // true/false
         } = params;

        if (!rearrangeMode) {
            throw new Error('请选择分班模式');
        }

        if (!oldClassOption) {
            throw new Error('请选择旧班级处理方式');
        }

        if (oldClassOption === 'rename' && !oldClassNameAffix) {
            throw new Error('请填写旧班级后缀');
        }

        if (oldClassOption !== 'default' && (!selectedOldClassIds || !selectedOldClassIds.length)) {
            throw new Error('请选择旧班级');
        }

        if (!rawUploadData || !rawUploadData.length) {
            throw new Error('请上传班级数据');
        }

        // 检查对应学年的班级名称是否重复
        await ctx.service.team.checkDuplicateClassNames({ uploadData: rawUploadData, selectedOldClassIds, oldClassNameAffix, oldClassOption }, transaction);

        // 整理数据格式
        const uploadData = []; //[{ year, className, userList: [] }]
        for (const item of rawUploadData) {
            const { year, classList = [] } = item;
            uploadData.push(...classList.map((i) => { return { ...i, year }; }));
        }

        // 处理旧班级
        if (oldClassOption !== 'default') {
            // 重命名
            if (oldClassOption === 'rename') {
                const oldClasses = await model.Team.findAll({
                    where: {
                        id: {
                            [Op.in]: selectedOldClassIds
                        }
                    },
                    raw: true,
                    transaction
                });
                
                if (oldClasses && oldClasses.length) {
                    const renameOldClasses = oldClasses.map(i => {
                        return {
                            ...i,
                            name: `${i.name}${oldClassNameAffix}`
                        }
                    });
    
                    await model.Team.bulkCreate(renameOldClasses, {
                        updateOnDuplicate: ['name', 'updated_at'],
                        transaction
                    });
                }
            }
            
            // 删除班级
            if (oldClassOption === 'delete') {
                await model.Team.destroy({
                    where: {
                        id: {
                            [Op.in]: selectedOldClassIds
                        }
                    },
                    transaction
                });

                // 删除班级关联
                await model.TeamUser.destroy({
                    where: {
                        teamID: {
                            [Op.in]: selectedOldClassIds
                        }
                    },
                    transaction
                });
            }
        }

        // 导入新班级
        let createUserList = []; // 新建用户
        let updateUserList = []; // 更新用户名
        let updateAdminUserList = []; // 更新用户名 教师账号 不修改密码
        let createClassList = []; // 创建班级
        let createUserClassLink = []; // 用户班级关联

        let createClassInfo = []; // 创建班级信息
        let createUserInfo = []; // 创建用户信息

        const allUserlist = [];
        for (const item of uploadData) {
            const { year, className, userList = [] } = item;
            createClassList.push({
                name: className,
                type: 'class',
                year: `${year}学年`,
            });

            allUserlist.push(...userList);
        }

        const existUsernames = allUserlist.filter(i => i.username).map(i => i.username);

        const existUsers = await model.User.findAll({
            where: {
                username: {
                    [Op.in]: existUsernames
                }
            },
            raw: true,
            transaction
        });

        if (rearrangeMode === 'keepUsername') {
            // 根据账号查找数据，没有找到的，为新账号，加入createuserlist
            for (const user of allUserlist) {
                const { username, password } = user;
                const exist = existUsers.find(i => String(i.username) === String(username));
                if (!exist) {
                    createUserList.push(user);
                }

                // 如果需要重置密码，加入updateUserList
                if (exist && resetPassword) {
                    const { adminAuthority } = exist;
        
                    // 仅重置学生账号
                    if (!adminAuthority) {
                        updateUserList.push({ ...exist, password });
                    }
                }
            }
        }
       
        if (rearrangeMode === 'changeUsername') {
             // 根据账号查找原有数据，账号替换为新账号，批量更新username => updateuserlist
            for (const user of existUsers) {
                const { username } = user;
                const oldUser = allUserlist.find(j => String(j.username) === String(username));
                if (!oldUser) {
                    continue;
                }

                const { newUsername } = oldUser;

                const { adminAuthority } = user;
                if (adminAuthority) {
                    updateAdminUserList.push({
                        ...user,
                        username: newUsername
                    });
                } else {
                    updateUserList.push({
                        ...user,
                        username: newUsername
                    });
                }
            }

            // 没有账号的，为新账号，加入createuserlist
            createUserList = allUserlist.filter(i => !i.username && i.newUsername).map(i => {
                return {
                    ...i,
                    username: i.newUsername
                }
            });
        }

        // 创建班级
        if (createClassList && createClassList.length) {
            createClassInfo = await model.Team.bulkCreate(
                createClassList,
                {
                    raw: true,
                    transaction
                }
            );

            createClassInfo = createClassInfo.map(i => i.dataValues);
        }

        // 创建新的学生账号
        if (createUserList && createUserList.length) {
            createUserInfo = await model.User.bulkCreate(
                createUserList,
                {
                    raw: true,
                    transaction
                }
            );

            createUserInfo = createUserInfo.map(i => i.dataValues);
        }

        // 更新学生账号
        if (updateUserList && updateUserList.length) {
            let updateCols = rearrangeMode === 'changeUsername' ? ['username'] : [];
            if (resetPassword) {
                updateCols.push('password');
            }

            await model.User.bulkCreate(
                updateUserList,
                {
                    updateOnDuplicate: updateCols,
                    transaction
                }
            );
        }

        // 更新教师账号
        if (updateAdminUserList && updateAdminUserList.length) {
            await model.User.bulkCreate(
                updateAdminUserList,
                {
                    updateOnDuplicate: ['username'],
                    transaction
                }
            );
        }

        // 获取班级与账号关联
        for (const item of uploadData) {
            const { year, className, userList = [] } = item;

            const findClass = createClassInfo.find(i => i.year === `${year}学年` && i.name === className);
            if (!findClass) {
                continue;
            }

            const { id: teamID } = findClass;
            const currentUsernames = userList.map(i => {
                const newAccount = !i.username && i.newUsername;
                const newName = newAccount ? String(i.newUsername) : String(i.username);
                return newName
            });

            // 原有账号与新账号均与班级创建关联关系
            const newExistusers = existUsers.filter(i => currentUsernames.indexOf(String(i.username)) !== -1);
            const newCreateusers = createUserInfo.filter(i => currentUsernames.indexOf(String(i.username)) !== -1);
            const allUsers = newExistusers.concat(newCreateusers);

            const newLink = allUsers.map(i => {
                    return {
                        userID: i.id,
                        teamID
                    }
                })
                .filter(i => {
                    return i.userID && i.teamID
                });
            
            createUserClassLink.push(...newLink);
        }

        // 创建账号与班级关联
        if (createUserClassLink && createUserClassLink.length) {
            await model.TeamUser.bulkCreate(
                createUserClassLink,
                {
                    transaction
                }
            );
        }
    }
  }

  return Team;
}