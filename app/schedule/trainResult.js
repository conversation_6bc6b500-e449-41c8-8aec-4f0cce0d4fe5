// const Subscription = require('egg').Subscription;
const { setupModel } = require('../utils/mysql');
const { list2Object } = require('../utils/tool');

module.exports = (app) => {
  async function reStart(ctx, schoolSlug, allStartPlans, allEndPlans) {
    const model = await setupModel(app, schoolSlug, ctx.service, ctx, true);
    
    // 启用事务
    const transaction = await model.transaction({autocommit: false});
 
    try {
      // 更改考试状态
      await ctx.service.trainThroughTrainPlan.syncTrainPlans(schoolSlug, allStartPlans, allEndPlans);
    } catch(e) {
      console.log(e, 'trainStart')
      await transaction.rollback();
      model.close();
      return;
    }

    await transaction.commit();
    model.close();
  }

  async function trainStart(ctx, schoolSlug, plans) {
    const model = await setupModel(app, schoolSlug, ctx.service, ctx, true);
    
    // 启用事务
    const transaction = await model.transaction({autocommit: false});
 
    try {
      // 更改考试状态
      await ctx.service.trainThroughTrainPlan.changeTrainStartStatus(plans, schoolSlug, transaction);
    } catch(e) {
      console.log(e, 'trainStart')
      await transaction.rollback();
      model.close();
      return;
    }

    await transaction.commit();
    model.close();
  }

  async function trainResult(ctx, schoolSlug, plans) {
    const model = await setupModel(app, schoolSlug, ctx.service, ctx, true);
    
     // 启用事务
    const transaction = await model.transaction({autocommit: false});

    // 更改考试状态
    try{
      // 获取需要修改状态的训练计划并修改，返回需要判分的train_user_record
      const { allNeedTrainPlanIDs, allNeedTrainPlanInfos } = await ctx.service.trainThroughTrainPlan.changeTrainStatus(plans, schoolSlug, transaction);

      if (allNeedTrainPlanInfos && allNeedTrainPlanInfos.length) {
        // 判分
        for(const row of allNeedTrainPlanInfos) {
          try {
            await ctx.service.trainThroughTrainPlan.submitExam(row, transaction);
          } catch(e) {
            if (e.message.indexOf('请勿重复提交')=== -1) {
              throw new Error(e);
            }
          }
        }

        // 统计
        for(const row of allNeedTrainPlanIDs) {
          await ctx.service.trainThroughTrainPlan.getTrainPlanStatistics(row, schoolSlug, transaction)
        }
      }

    } catch(e) {
      console.log(e, 'trainResult')
      await transaction.rollback();
      model.close();
      return;
    }

    await transaction.commit();
    model.close();
  }
  
  // console.log('app.config.ENABLE_TIMER_TASK:',app.config.ENABLE_TIMER_TASK)
  return {
    schedule: {
      interval: '1m',
      type: 'worker',
      // type: 'all',
      // 环境变量ENABLE_TIMER_TASK = true时，才能启动定时任务
      disable: !app.config.ENABLE_TIMER_TASK,
      // disable: false
    },
    async task(ctx) {
      const redis = app.redis.get('train');

      if (app.reStart) {
        app.reStart = false;
        console.log('------------begin-------------')
        const allSchools = await ctx.service.school.getAllSchoolDatabaseConfig();
        // console.log('allSchools:',allSchools);

        for (const row of allSchools) {
          // 调试代码
          if (app.config.allSchoolDebug && row.slug !== 'csxx') {
            continue;
          }

          try {
            const allStartPlans = await redis.zrange(`train_start_${row.slug}`, 0, -1, 'WITHSCORES');
            const allEndPlans = await redis.zrange(`train_end_${row.slug}`, 0, -1, 'WITHSCORES');
            await reStart(ctx, row.slug, allStartPlans, allEndPlans);
          } catch (e) {
            console.log('首次启动训练拉平报错', e)
          }
        }
        console.log('------------end---------------')
      }
      
      const trainKeys = await redis.keys('train_*');

      for (const trainKey of trainKeys) {
        // 考试时间开始，改变状态
        const schoolStartMatch = trainKey.match(/train_start_(.+)/);

        if (schoolStartMatch && schoolStartMatch.length && schoolStartMatch[1] && schoolStartMatch[1] !== 'undefined') {
          const planCount = await redis.zcard(trainKey);
          
          if (planCount) {
            const schoolSlug = schoolStartMatch[1];
            const allPlans = await redis.zrange(trainKey, 0, -1, 'WITHSCORES');

            const planList = list2Object(allPlans);

            const plans = planList
              .filter(i => i.time <= Date.now())
              .map(i => i.planID);
            console.log(plans, trainKey, 'train_start')
            if (plans.length) {
              await trainStart(ctx, schoolSlug, plans);
            }
          }
        }

        // 考试时间结束，改变状态、判分、统计
        const schoolMatch = trainKey.match(/train_end_(.+)/); 

        if (schoolMatch && schoolMatch.length && schoolMatch[1] && schoolMatch[1] !== 'undefined') {
          const planCount = await redis.zcard(trainKey);

          if (planCount) {
            const schoolSlug = schoolMatch[1];
            const allPlans = await redis.zrange(trainKey, 0, -1, 'WITHSCORES');

            const planList = list2Object(allPlans);

            const plans = planList
              .filter(i => i.time <= Date.now())
              .map(i => i.planID);
            console.log(plans, trainKey, 'train_end')
            if (plans.length) {
              await trainResult(ctx, schoolSlug, plans);
            }
          }
        }
      }
    },
  };
};
