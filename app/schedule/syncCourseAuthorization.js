const { Subscription } = require('egg');
const { setupModel } = require('../utils/mysql.js');

module.exports = app => {
  class SyncCourseAuthorizationTask extends Subscription {
    // 通过 schedule 属性来设置定时任务的执行间隔等配置
    static get schedule() {
      return {
        interval: '1d', // 每天1次
        type: 'all',
      };
    }

    // subscribe 是真正定时任务执行时被运行的函数
    async subscribe() {
      console.log('====================开始同步课程授权数据-定时任务====================')
      const noCache = true;
      const { ctx } = this;

      const allSchools = await ctx.service.school.getAllSchoolDatabaseConfig();

      for (const row of allSchools) {
        const { slug: schoolSlug } = row;
        const model = await setupModel(app, schoolSlug, ctx.service, ctx, noCache);
      
        // 启用事务
        const transaction = await model.transaction({ autocommit: false });
        const mainTransaction = await app.mainModel.transaction({ autocommit: false });
  
        try {
          await ctx.service.permissionPeople.refreshCoursePermission(schoolSlug, transaction, mainTransaction);
        } catch(e) {
          console.log(e, 'syncCourseAuthorization')
          await transaction.rollback();
          await mainTransaction.rollback();
          await model.close();
          continue;
        }
  
        await transaction.commit();
        await mainTransaction.commit();
        await model.close();
      }

      console.log(`====================同步课程授权数据-定时任务完成===================`)
    }
  }

  return SyncCourseAuthorizationTask;
}
