const { Subscription } = require('egg');
const { removeIdleDBConnection } = require('../utils/mysql.js');

module.exports = app => {
  class RemoveIdleDBConnectionTask extends Subscription {
    // 通过 schedule 属性来设置定时任务的执行间隔等配置
    static get schedule() {
      return {
        interval: '5m',
        type: 'all',
      };
    }

    // subscribe 是真正定时任务执行时被运行的函数
    async subscribe() {
      console.log('定时任务：开始清理空闲数据库连接')
      removeIdleDBConnection();
    }
  }

  return RemoveIdleDBConnectionTask;
}
