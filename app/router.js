'use strict';
// 根据权限做路由，egg里有一个
// app/router.js
module.exports = app => {
  // # 管理平台使用接口
  // 初始化数据库
  app.get('/back/database/init', 'database.init');

  app.get('/back/test/school', 'database.school');

  // 解除IP密码错误锁定
  app.get('/back/lock/release', 'database.releaseIPLock');

  // # 前台
  // 前台验证码支持
  app.get('/web/user/verify', 'user.verify');
  // 后台验证码支持
  app.get('/admin/user/verify', 'user.verify');

  // 用户登录，全部角色，记录Session，根据角色，返回前端页面可用功能
  app.post('/web/user/login','user.login');
  // 获取首页
  app.get('/web/home', 'home.getHomePage');
  // 调用用户会话
  app.get('/web/user/session','user.getSession');
  // 注销
  app.get('/web/user/logoff', 'user.logoff');
  
  // 标记当前弹出的内容已经阅读后，关闭掉
  app.get('/web/user/popupClosed', 'user.popupClosed')

  // 获取公告详情 
  app.get('/web/notice/:noticeID', 'notice.getNoticeMessage');

  // 验证密码跳转后台
  app.post('/web/user/checkPassword', 'user.checkPassword');

  //# 后台
  // 用户登录，全部角色，记录Session，根据角色，返回前端页面可用功能
  app.post('/admin/user/adminLogin','user.adminLogin');
  // 运维平台用户登录，全部角色，记录Session，根据角色，返回前端页面可用功能
  app.post('/admin/thirdPart/adminLogin','user.adminLoginFromManager');
  // 昆山demo登录专用
  app.post('/admin/user/loginDemo','user.loginDemo');
  // 注销
  app.get('/admin/user/adminLogoff', 'user.adminLogoff');
  // 调用缓存
  app.get('/admin/user/adminSession','user.getAdminSession');
  
  // 首页获取配置信息
  app.get('/admin/systemConfig', 'home.getInfomation');

  // 后台首页获取登录提示
  app.get('/admin/systemConfig/getWebModal', 'home.getWebModal')
  // 修改弹出公告
  app.put('/admin/systemConfig/updateWebModal/:id', 'home.updateWebModal')
  // 修改轮播图
  app.put('/admin/systemConfig/CarouselFigure', 'home.putCarouselFigure')
  // 修改是否前台开放
  app.put('/admin/systemConfig/NoticeOpen', 'home.putNoticeOpen')
  
  // 新增公告
  app.post('/admin/notice', 'notice.addNotice');
  // 修改公告信息
  app.put('/admin/notice/:id', 'notice.putNotice');
  // 删除公告
  app.delete('/admin/notice/:id', 'notice.deleteNotice');
  // 获取公告列表，可通过状态（草稿/已发布）查找
  app.get('/admin/notice', 'notice.getNoticeList');
  // 获取公告详情
  app.get('/admin/notice/:id', 'notice.getNoticeDetails');
  // 上传图片
  app.post('/admin/file/upload', 'file.upload');
  // 上传图片
  app.post('/web/file/upload', 'file.upload');
  // 修改头像
  app.put('/web/user/changeAvatar/:id', 'user.changeAvatarByWeb');

  // # 用户管理
  // 获取用户列表
  app.get('/admin/user/userList', 'user.getUserList');
  // 添加用户
  app.post('/admin/user/userList', 'user.createUserList')

  // 班级用户批量上传
  app.post('/admin/user/newuserList', 'user.newcreateUserList')

  // 批量添加用户 用户列表界面
  app.post('/admin/user/userList/createUserListUser', 'user.createUserListUser')

  // 获取所有班级(精简)
  app.get('/admin/class/getClassListAll', 'team.getClassListAllLess');

  // 班级挂钩
  app.post('/admin/class/userClassContact', 'team.userClassContact');

  // 批量删除用户
  app.delete('/admin/user/deleteAlluser', 'user.deleteAlluser');

  // 修改用户昵称
  app.put('/admin/user/changeUserNickName/:userID/:nickName', 'user.changeUserNickName');
  app.put('/web/user/changeUserNickName/:nickName', 'user.webChangeUserNickName');

  // 获取所有班级
  app.get('/admin/class/getClassList', 'team.getClassList');

  // 班级筛选用户
  app.get('/admin/class/filterUser', 'team.filterUser');

  // 获取班级所有用户
  app.get('/admin/class/getClassAllUsers', 'team.getClassAllUsers');

  // 删除班级
  app.delete('/admin/class/deleteClass/:id', 'team.deleteClass');

  // 批量删除班级
  app.delete('/admin/class/deleteClasses/:ids', 'team.deleteClasses');

  // 班级名称查重
  app.post('/admin/class/checkClassName', 'team.checkClassName');

  // 创建班级
  app.post('/admin/class/createClass', 'team.createClass');
    
  // 修改班级名称
  app.put('/admin/class/putClassName/:id', 'team.putClassName')

  // 迁移班级学年
  app.put('/admin/team/changeClassYear', 'team.changeClassYear')

  // 班级用户密码批量重置
  app.put('/admin/team/resetClassPassword', 'team.resetClassPassword')

  // 删除班级挂钩
  app.delete('/admin/class/userClassContact', 'team.deleteUserClassContact');

  // 根据用户 id 获取学生信息
  app.get('/admin/user/get/:id', 'user.adminGetUser');

  // 修改用户状态
  app.put('/admin/user/changeUserState/:userID/:state', 'user.changeUserState');

  // 修改管理员权限
  app.put('/admin/user/toAdmin/:id', 'user.changeUserAdmin');

  app.put('/admin/user/unbindsso/:id', 'user.unbindsso');

  // 修改密码
  app.put('/admin/user/changePassword/:id', 'user.changePassword');

  // 前台修改密码
  app.put('/web/user/changePassword', 'user.changePasswordByWeb');
  
  // 用户弱密码检测
	app.post('/admin/user/checkWeakPasswordUser', 'user.checkWeakPasswordUser');

	// 批量修改用户密码
	app.post('/admin/user/batchChangePassword', 'user.batchChangePassword');

  // # 课节
  app.post('/admin/course', 'course.createCourse');

  // CourseSlug查重
  app.post('/admin/ifCourseSlug', 'course.ifCourseSlug');

  // 导入课程文件包后创建课程体系
  app.post('/admin/createCourseSections', 'course.createCourseSections');

  // 删除课程
  app.delete('/admin/course/deleteCourse/:courseSlug', 'course.deleteCourse');

  // 删除节
  app.delete('/admin/course/deleteSection', 'course.deleteSection');

  // 删除章
  app.delete('/admin/course/deleteChapter', 'course.deleteChapter');

  // 删除课程资源文件 assets 和 input 目录
  app.delete('/admin/course/deleteSourceFile', 'course.deleteSourceFile');

  // 修改课程信息
  app.put('/admin/course/putCourse/:courseSlug', 'course.putCourse');

  // 查询所有教师
  app.get('/admin/user/getAllAdmins', 'user.getAllAdmins');

  // 根据 courseSlug 获取课程
  app.get('/admin/course/getCourseBycourseSlug/:courseSlug', 'course.getCourseBycourseSlug');

  // 根据 courseSlug 获取课程和章节信息
  app.get('/admin/course/getCourseAndSectionBycourseSlug/:courseSlug', 'course.getCourseAndSectionBycourseSlug');

  // 查询课程内容目录列表
  app.get('/admin/course/getCourseDirectory/:courseSlug', 'course.getCourseDirectory');

  // 课程内容目录排序后保存
  app.put('/admin/course/saveCourseSort', 'course.saveCourseSort');

  // 查询有无重复courseSlug
  app.get('/admin/course/findExitCourseSlug', 'course.findExitCourseSlug');

  // 课程 内容目录 上传

  // 课程 新建时存缓存
  app.put('/admin/course/saveCourseDirectory/:courseSlug', 'course.saveCourseDirectory');

  app.put('/admin/course/modifyCourseDirectory', 'course.modifyCourseDirectory');

  // 课程 数据管理 获取媒体资源列表
  app.get('/admin/course/getMediaFesources/:courseSlug', 'course.getMediaFesources');

  // 课程 数据管理 媒体资源下载
  app.get('/admin/course/:courseSlug/downMediaFesources/:fileName', 'course.downMediaFesources');

  // 课程 输入数据 获取输入数据列表
  app.get('/admin/course/getInputData/:courseSlug', 'course.getInputData');


  // 课程 输入数据 输入数据下载
  app.get('/admin/course/:courseSlug/downInputData/:fileName', 'course.downInputData');

  app.get('/admin/course/:courseSlug/downSectionData/:fileName', 'course.downSectionData');
  
  // 课程学生数据 获取学生数据列表
  app.get('/admin/course/getStudentData/:courseSlug', 'course.getStudentDataList');
  // 根据班级获取所有所有学生
  app.get('/admin/user/getAllStudentsByteam/:teamID', 'user.getAllStudentsByteam')
  // 课程 学生数据 获取学生数据
  app.get('/admin/course/:courseSlug/getStudentData/:studentID', 'course.getStudentData')
  // 课程 学生数据 预览学生数据
  app.get('/admin/course/previewStudentData/:courseSlug/:fileName/:studentID', 'course.previewStudentData')
  app.get('/web/getFileDir/:courseSlug', 'course.getFileDirWeb');

  // 前台下载学生数据(input)
  app.get('/web/course/:courseSlug/downStudentData/:fileName/input', 'course.webDownStudentDataInput');
  // 前台下载学生数据
  app.get('/web/course/:courseSlug/downStudentData/:fileName', 'course.webDownStudentData');
  // 前台删除学生数据
  app.delete('/web/deleteFileDirWeb', 'course.deleteFileDirWeb');

  // 后台删除学生数据
  app.delete('/admin/course/deleteStudentFile', 'course.deleteStudentFile');
  
  // 前台课程
  app.get('/web/course/queryCourseList', 'course.queryCourseList');

  // 后台查询课程列表
  app.get('/admin/course/queryCourseList', 'course.queryCourseListAdmin');

  // 前台课程章节目录
  app.get('/web/course/queryCourseDirectory', 'course.queryCourseDirectory');

  // 获取节内容及类型 querySectionContentAndType
  app.get('/web/course/querySectionContentAndType', 'course.querySectionContentAndType');

  // 后台获取节内容及类型 
  app.get('/admin/course/querySectionContentAndType', 'course.querySectionContentAndTypeAdmin');

  // 获取课程开放班级列表
  app.get('/web/course/getCourseTeams', 'course.getCourseTeams');

  // 前台AI节筛选班级统计
  app.get('/web/sectionStatisResult', 'course.sectionStatisResult');
  // 前台oj节筛选班级统计
  app.get('/web/sectionStatisResultOJ', 'course.sectionStatisResultOJ');
  // 前台oI节筛选班级统计
  app.get('/web/sectionStatisResultOI', 'course.sectionStatisResultOI');
  
  app.get('/web/sectionStatisResultScratch', 'course.sectionStatisResultScratch');
  
  app.get('/web/sectionStatisResultCodeBlank', 'course.sectionStatisResultCodeBlank');

  // 请求获取交互式课程Kernel分配
  app.post('/web/icourse/kernel/allocate', 'icourse.requestKernelAllocate');

  // 请求获取交互式课程Kernel释放
  app.delete('/web/icourse/kernel', 'icourse.requestKernelFree');

  // 删除交互式课程运行环境
  app.get('/back/badacaixi/xianzhuozi', 'icourse.clearContainer');

  // 后台
  // 文件系统
  // 获取已经存在的共享文件
  app.post('/admin/file/existSharedFiles', 'file.getExistSharedFiles');
  // 上传共享文件，需要自己建立映射，用于课程上传
  app.post('/admin/file/uploadSharedFile', 'file.uploadSharedFile');
  //  上传资源文件,并且建立映射
  app.post('/admin/file/uploadHashFile', 'file.uploadHashFile');

  // // 上传微应用资源文件
  app.post('/admin/file/uploadMicroAssets', 'file.uploadMicroAssets');
  
  // word图片转存
  app.post('/admin/file/uploadWordImage', 'file.uploadWordImage');
  // 上传判决内容
  app.post('/admin/file/uploadHashJudgeFile', 'file.uploadHashJudgeFile');
  // 批量上传判决内容
  app.post('/admin/file/bulkUploadHashJudgeFile', 'file.bulkUploadHashJudgeFile');
  // 映射文件
  app.post('/admin/file/bulkLinkHashFile', 'file.bulkLinkHashFile');
  // 上传课程文件
  app.post('/admin/file/uploadChapterFile', 'file.uploadChapterFile');
  // 上传课程内容
  app.post('/admin/file/uploadChapterFPSXMLContent', 'file.uploadChapterFPSXMLContent');
  // 获取资源文件
  app.get('/admin/file/filePreview', 'file.filePreview');
  // 获取资源文件
  app.get('/admin/file/getContent', 'file.getContent');
  app.get('/web/file/getContent', 'file.getContent');
  // 上传课程封面
  app.post('/admin/file/courseImgUpload', 'file.courseImgUpload');
  // 查询课程封面是否存在
  app.get('/admin/file/checkCourseImgExit', 'file.checkCourseImgExit');
  // 删除课程封面
  app.delete('/admin/file/delCourseImg', 'file.delCourseImg');
  // 更新课程内容目录
  app.put('/admin/course/contentDirectory',  'course.contentDirectory');

  // app.post('/oj/onWorkerFinish',  'course.onWorkerFinish');
  // 新增节内容
  app.post('/admin/course/file', 'course.createCourseFile');

  // 修改xml文件内容
  app.put('/admin/course/xmlFile', 'course.updateXMLFile');
  // 修改xml文件内容
  app.put('/admin/course/aiFile', 'course.updateAIFile');
  // 列举题目的全部数据文件
  app.post('/admin/listXmlQuestionData', 'course.listXmlQuestionData');
  // 重命名章
  app.put('/admin/course/renameChapter', 'course.renameChapter');
  // 重命名节
  app.put('/admin/course/renameSection', 'course.renameSection')

  // 获取文件内容
  app.get('/admin/getxmldata', 'course.getxmldata');
  // 获取文件内容
  app.get('/web/getxmldata', 'course.getxmldata');

  app.get('/web/getxmldatabycourse', 'course.getxmldataByCourse');

  // 课程进度
  app.get('/admin/course/Chapter',  'course.getChapterResult');
  app.get('/admin/course/section',  'course.getSectionResult');
  // 获取代码记录
  app.get('/admin/course/getCodeRecord',  'course.getCodeRecord');
  // 批量删除学生运行记录
  app.delete('/admin/clearStudentsRecord',  'course.clearStudentsRecord');

  app.delete('/web/section/clearStudentRecord',  'course.clearStudentRecord');
  // 后台设置节是否开放互联网选择
  app.put('/admin/section/changeInternel', 'section.changeInternel')
  // 后台设置节为项目书
  app.put('/admin/section/projectBook', 'section.projectBook')
  // 链接至项目书
  app.put('/admin/section/linkProject', 'section.linkProject')
  // 取消链接至项目书
  app.put('/admin/section/cancelLinkProject', 'section.cancelLinkProject')


  // 写入微应用文件
  app.post('/web/section/microAppFile', 'section.postMicroAppFile');
  // 提交微应用记录
  app.post('/web/section/commitMicroAppRecord', 'section.commitMicroAppRecord');

  app.post('/web/section/commitScratchRecord', 'section.commitScratchRecord');

  // 提交代码记录，处理项目书逻辑
  app.post('/web/section/commitSourceBlockRecord', 'section.commitSourceBlockRecord');

  // 删除特殊处理组件文件以及记录
  app.delete('/web/section/removeRecordFromSectionRecord', 'section.removeRecordFromSectionRecord');

  // 读取本人写入文件
  app.post('/web/section/readStudentFile', 'section.getStudentFileContent');

  // 前台老师查询某一个课程的某一个章节的oi大题记录，并处理成分数段
  app.get('/web/section/getSectionRecord', 'section.getSectionRecord');

  // 禁止学生用户修改密码
  app.put('/admin/user/ban', 'user.setBan')
  // 获取是否禁止学生用户修改密码
  app.get('/admin/user/getBan', 'user.getBan')

  // 禁止学生用户修改姓名
  app.put('/admin/user/banName', 'user.setBanName')
  // 获取是否禁止学生用户修改姓名
  app.get('/admin/user/getBanName', 'user.getBanName')

  // 测试运行程序
  app.post('/web/testrun', 'editorOj.testrun');
  // judge程序  
  app.post('/web/judge', 'editorOj.judge');

  // 客观题处理-提交
  app.post('/web/oi/submit', 'editorOI.submit');

  // 获取clientID  
  app.get('/web/clientID', 'user.getClientID');

  app.get('/admin/clientID', 'user.getClientID');

  // 按照班级下载学生数据文件
  app.post('/admin/file/downloadCourseDataByClass', 'file.downloadCourseDataByClass');

  // 按照指定学号下载学生数据文件
  app.post('/admin/file/downloadCourseDataByStudent', 'file.downloadCourseDataByStudent');
  
  // TODO: ！！！直接下载任意路径，安全风险！！！
  // 传入路径下载文件
  app.post('/admin/download_file_data', 'file.downloadFile');

  // 传入路径下载文件
  app.post('/web/download_file_data', 'file.downloadFile');

  app.post('/web/download_course_file_data', 'file.downloadCourseFile');

  // 下载课程文件
  app.post('/admin/file/downloadCourseContent', 'file.downloadCourseContent');
  // 为plt平台下载课程文件
  app.post('/admin/file/downloadCourseContentForPLT', 'file.downloadCourseContentForPLT');
  // 为plt平台下载课程文件
  app.post('/admin/file/updateCourseContentForPLT', 'file.updateCourseContentForPLT');
  
  // 下载课程节文件
  app.post('/admin/download_section_data', 'file.downloadSectionData');

  // 下载课程章文件
  app.post('/admin/download_chapter_data', 'file.downloadChapterData');

  app.post('/admin/download_inout_data', 'file.downloadInOutData');
  
  // 查询之前是否有学生答题记录
  app.get('/admin/section/hasRecord', 'section.hasRecord');

  
  // 获取当前章节某个班级所有的答题记录
  app.get('/admin/getCurrentChapterAndClassSectionRecord', 'section.getCurrentChapterAndClassSectionRecord')

  // 批量开放班级和章节
  app.post('/admin/batchSwitchChapter', 'course.batchSwitchChapter')

  // 根据uuid获取某一个微应用的初始内容
  app.get('/web/getMicroAppPrimaryContentByUUID/:UUID', 'section.getMicroAppPrimaryContentByUUID')

  //************Faye用户会话与资源管控************//

  // Faye页面上线报告 （每个页面1次）
  app.post('/faye/online', 'faye.online');

  // Faye页面停留与离线报告 （每30分钟、关闭页面）
  app.post('/faye/report', 'faye.report');

  //************历史版本************//

  // 记录历史课程版本
  app.post('/admin/section/saveHistoryRecords', 'section.saveHistoryRecords');

  // 获取历史课程版本
  app.get('/admin/section/getHistoryRecords', 'section.getHistoryRecords');

  // 获取历史版本课程文件
  app.get('/admin/section/getHistoryFile', 'section.getHistoryFile');

  // 恢复当前课程内容为指定版本历史课程内容
  app.post('/admin/section/recoverHistoryFile', 'section.recoverHistoryFile');

  // 将指定版本文件内容另存为新课程
  app.post('/admin/section/saveHistoryFileAs', 'section.saveHistoryFileAs');

  // 更新历史版本说明字段
  app.put('/admin/section/updateHistoryComment', 'section.updateHistoryComment');

  /***************课程复制粘贴 *************/  
  // 检测课程发布/草稿状态
  app.get('/admin/section/checkSectionStatus', 'section.checkSectionStatus');
  
  // 粘贴课节
  app.post('/admin/course/pasteSection', 'course.pasteSection');

  // 跨课程复制课节
  app.post('/admin/course/pasteSectionCrossCourse', 'course.pasteSectionCrossCourse');  

  // access 提交记录
  app.post('/web/section/submitAccessAnswer', 'section.submitAccessAnswer');

  app.post('/web/section/submitPPTRecord', 'section.submitPPTRecord');

  // 恢复被误删班级
  app.get('/back/team/recoverTeamAndUsers', 'team.recoverTeamAndUsers')

  /* ======================单点登录======================== */
  // 检查是否已有绑定账号
  app.get('/admin/user/sso/checkSSOExist', 'sso.checkSSOExist');

  app.get('/admin/user/sso/getSSOUrl', 'sso.getSSOUrl');
  
  // 绑定账号
  app.post('/admin/user/sso/bindHxrAccount', 'sso.bindHxrAccount');

  app.post('/admin/user/sso/bindHxrAccountAuto', 'sso.bindHxrAccountAuto');
  
  // 导入班级列表
  app.get('/admin/sso/getSSOClassTree', 'sso.getClassTree');

  app.get('/admin/sso/getSSOTeacherClassTree', 'sso.getTeacherClassList');

  // 获取短信验证码
  app.get('/admin/sso/getVerificationCode', 'sso.getVerificationCode');
  
  // 获取班级学生列表
  app.post('/admin/sso/getStudentsByClassID', 'sso.getStudentsByClassID');

  app.post('/admin/sso/getStudentsByClassIDSingleKS', 'sso.getStudentsByClassIDSingleKS');
  
  // 导入班级和学生
  app.post('/admin/sso/importClassAndStudents', 'sso.importClassAndStudents');

  // 查询昆山学校
  app.get('/admin/sso/getAllSchoolKS', 'sso.getAllSchoolKS');

  /* ======================单点登录 njyz ======================== */
  
  // 查询教师基本信息
  app.get('/admin/sso/njyz/getTeacherInfo', 'sso.getTeacherInfo');

  // 查询学生基本信息
  app.get('/admin/sso/njyz/getStudentInfo', 'sso.getStudentInfo');

  // 查询教师教授班级
  app.get('/admin/sso/njyz/getTeacherClassList', 'sso.getTeacherClassListNJYZ');

  // 获取班级学生列表
  app.post('/admin/sso/njyz/getStudentsByClassID', 'sso.getStudentsByClassIDNJYZ');

  //====================精品课程====================//
  
  app.post('/admin/course/publishEliteCourse', 'course.publishEliteCourse');

  app.post('/admin/course/importEliteCourse', 'course.importEliteCourse');
  
  // 查询课程封面是否存在
  app.get('/admin/file/checkSchoolCourseImgExit', 'file.checkSchoolCourseImgExit');

  app.post('/admin/user/extendSession', 'user.extendSession');
  
  app.get('/web/test/stressTestLogin', 'user.stressTestLogin');

  app.get('/admin/course/progress/exportCourseProgressExcel', 'course.exportCourseProgressExcel');

  app.post('/web/section/submitCodeAnswer', 'section.submitCodeAnswer');
  
  /******************************* 训练 *******************************/
    // 创建训练
  app.post('/admin/train/createTrain', 'train.createTrain');

  // 复制训练
  app.post('/admin/train/copyTrain', 'train.copyTrain');
    // 批量创建训练
  app.post('/admin/train/createTrains', 'train.createTrains');
    // 批量导入xml文件新增OJ题目
  app.post('/admin/train/upload/question/oj/xml', 'trainQuestionBank.uploadQuestionsForXmlFiles');
    // 修改训练
  app.put('/admin/train/putTrain/:id', 'train.putTrain');
    // 修改训练中的题目引用
  app.put('/admin/train/replaceQuestionInTrains', 'train.replaceQuestionInTrains');

  // 记录训练去重情况
  app.put('/admin/train/put/recordTrainUpdate', 'train.recordTrainUpdate');

  app.put('/admin/train/put/template', 'train.putTrainTemplate');

    // 获取训练
  app.get('/admin/train/getTrain/:id', 'train.getTrain');
    // 删除训练
  app.delete('/admin/train/destoryTrain/:id', 'train.destoryTrain');

  app.delete('/admin/train/destoryTrains', 'train.destoryTrains');
  
    // 获取训练列表
  app.get('/admin/train/getTrainList', 'train.getTrainList');
    // 创建题目
  app.post('/admin/questions/createQuestions', 'questions.createQuestions');
    // 批量创建题目
  app.post('/admin/questions/bulkCreateQuestions', 'questions.bulkCreateQuestions');
    // 修改题目
  app.put('/admin/questions/putQuestions/:id', 'questions.putQuestions');
    // 批量修改题目
  app.put('/admin/questions/bulkPutQuestions', 'questions.bulkPutQuestions');
    // 获取题目
  app.get('/admin/questions/getQuestions/:id', 'questions.getQuestions');
    // 删除题目
  app.delete('/admin/questions/destoryQuestions/:id', 'questions.destoryQuestions');
    // 获取题目列表
  app.get('/admin/questions/getQuestionsList', 'questions.getQuestionsList');

  // # 标签管理
  // 添加标签
  app.post('/admin/tag','tag.postTag');
  // 批量新增、修改、删除题目知识点
  app.post('/admin/bulkEditTag','tag.bulkEditTag');
  // 获取标签
  app.get('/admin/tag','tag.getTag');
  // 训练标签列表
  app.get('/admin/getTrainTagList','tag.getTrainTagList');
  // 训练标签树
  app.get('/admin/getTrainTagTree','tag.getTrainTagTree');
  app.get('/web/getTrainTagTree','tag.getTrainTagTree');

  // 按照标签或者题型筛选
  app.post('/admin/getQuestionsListByTagAndType','tag.getQuestionsListByTagAndTypeAdmin');
  app.post('/web/getQuestionsListByTagAndType','tag.getQuestionsListByTagAndType');
  // 按照标签或者题型统计
  app.post('/admin/getQuestionsCountByTagAndType','tag.getQuestionsCountByTagAndTypeAdmin');
  
  app.post('/admin/getQuestionCountByTagAndDifficulty','tag.getQuestionCountByTagAndDifficulty');

  app.post('/web/getQuestionsCountByTagAndType','tag.getQuestionsCountByTagAndType');
  // 标签查重
  app.get('/admin/tag/findTag','tag.findTag');

  // 批量根据题目id与标签id修改标签关联
  app.post('/admin/tag/updateBulkQuestionTag','tag.updateBulkQuestionTag');

  // 批量根据题目id修改题解
  app.post('/admin/questions/updateBulkQuestionSolution','questions.updateBulkQuestionSolution');


    // 创建模版
  app.post('/admin/template/createTemplate', 'template.createTemplate');
    // 修改模版
  app.put('/admin/template/putTemplate/:id', 'template.putTemplate');
    // 获取模版
  app.get('/admin/template/getTemplate/:id', 'template.getTemplate');
    // 删除模版
  app.delete('/admin/template/destoryTemplate/:id', 'template.destoryTemplate');
    // 获取模版列表
  app.get('/admin/template/getTemplateList', 'template.getTemplateList');


  // 新建接口，待调试！！！！！！！！！！！！！！！！！！！
  // 创建训练
  app.post('/admin/trainThroughTrainPlan/createTrainPlan', 'trainThroughTrainPlan.createTrainPlan');
  // 修改训练
  app.put('/admin/trainThroughTrainPlan/putTrainPlan/:id', 'trainThroughTrainPlan.putTrainPlan');
  // 获取训练
  app.get('/admin/trainThroughTrainPlan/getTrainPlan/:id', 'trainThroughTrainPlan.getTrainPlan');
  // 删除训练
  app.delete('/admin/trainThroughTrainPlan/destoryTrainPlan/:id', 'trainThroughTrainPlan.destoryTrainPlan');

  // 批量删除训练
  app.delete('/admin/trainThroughTrainPlan/destoryTrainPlans', 'trainThroughTrainPlan.destoryTrainPlans');

  // 获取训练列表
  app.get('/admin/trainThroughTrainPlan/getTrainPlanList', 'trainThroughTrainPlan.getTrainPlanList');
  // 获取训练列表(前台)
  app.get('/web/trainThroughTrainPlan/getTrainPlanListWithClass', 'trainThroughTrainPlan.getTrainPlanListWithClass');
  // 获取训练 通过训练码
  app.get('/web/trainThroughTrainPlan/getTrainPlanByCode', 'trainThroughTrainPlan.getTrainPlanByCode');
  // 获取训练
  app.get('/web/trainThroughTrainPlan/getTrainPlan/:id', 'trainThroughTrainPlan.getTrainPlan');
  // 获取训练计划：包含用户、用户训练记录和所有的相关训练
  app.get('/web/trainThroughTrainPlan/getTrainPlanInfo', 'trainThroughTrainPlan.getTrainPlanInfo');

  // 分享训练计划
  app.put('/admin/trainPlan/shareTrainPlan', 'trainThroughTrainPlan.shareTrainPlan');

  // 停止训练计划
  app.put('/admin/trainThroughTrainPlan/stopTrainPlan', 'trainThroughTrainPlan.stopTrainPlan');
  // 获取学年、班级树
  app.get('/admin/trainThroughTrainPlan/getClassList','trainThroughTrainPlan.getClassList');
  // 根据班级获取学生
  app.get('/admin/trainThroughTrainPlan/getUserListByClasses','trainThroughTrainPlan.getUserListByClasses');
  // 获取多个训练计划导出结果
  app.get('/admin/trainThroughTrainPlan/getBulkTrainPlanStatistics','trainThroughTrainPlan.getBulkTrainPlanStatistics');
  // 获取多个训练计划导出试题难度
  app.get('/admin/trainThroughTrainPlan/getBulkTrainPlanStatisticsWithDifficulty','trainThroughTrainPlan.getBulkTrainPlanStatisticsWithDifficulty');
  // 实现增加填空题参考答案，并重新计算学生本题得分，并更新学生总分
  app.put('/admin/trainThroughTrainPlan/updateFillBlankAnswer','trainThroughTrainPlan.updateFillBlankAnswer');

  // 训练资源文件上传
  app.post('/admin/train/trainAssetsUpload', 'train.trainAssetsUpload');
  // 训练封面上传
  app.post('/admin/train/trainCoverUpload', 'train.trainCoverUpload');

  // 延期
  app.put('/admin/trainPlan/delayTrainPlan', 'trainThroughTrainPlan.delayTrainPlan');

  // 重命名
  app.put('/admin/trainPlan/updateTrainPlan', 'trainThroughTrainPlan.updateTrainPlan');


  // 从文件获取所需训练信息
  // app.get('/admin/train/getTrainContentFromQuestionFile/:id/:trainPlanID', 'trainPlan.getTrainContentFromQuestionFile');

  // 训练 数据管理 获取媒体资源列表
  app.get('/admin/train/getMediaResources/:trainID', 'train.getMediaResources');
  // 删除训练资源文件 assets 和 input 目录
  app.delete('/admin/train/deleteSourceFile', 'train.deleteSourceFile');
  // 课程 数据管理 媒体资源下载
  app.get('/admin/train/:trainID/downMediaResources/:fileName', 'train.downMediaResources');


  // 学生提交
  // app.post('/web/trainPlan/submitExam', 'trainPlan.submitExam');
  app.post('/web/trainThroughTrainPlan/submitExam', 'trainThroughTrainPlan.submitExam');

  // 报名训练
  app.post('/admin/trainThroughTrainPlan/createTrainRecordWithStudent/:planID', 'trainThroughTrainPlan.createTrainRecordWithStudent')
  // 报名训练
  app.post('/web/trainThroughTrainPlan/createTrainRecordWithStudent/:planID', 'trainThroughTrainPlan.createTrainRecordWithStudent')

  // 创建题库
  app.post('/admin/trainQuestionBank/createTrainQuestionBank', 'trainQuestionBank.createTrainQuestionBank');
  // 修改题库
  app.put('/admin/trainQuestionBank/putTrainQuestionBank/:id', 'trainQuestionBank.putTrainQuestionBank');
  // 获取题库
  app.get('/admin/trainQuestionBank/getTrainQuestionBank/:id', 'trainQuestionBank.getTrainQuestionBank');
  // 删除题库
  app.delete('/admin/trainQuestionBank/destoryTrainQuestionBank/:id', 'trainQuestionBank.destoryTrainQuestionBank');
  // 获取题库列表
  app.get('/admin/trainQuestionBank/getTrainQuestionBankList', 'trainQuestionBank.getTrainQuestionBankListAdmin');
  app.get('/web/trainQuestionBank/getTrainQuestionBankList', 'trainQuestionBank.getTrainQuestionBankList');

  // 需要传questionID
  // 训练题目资源文件上传
  app.post('/admin/trainQuestion/trainAssetsUpload', 'train.trainQuestionAssetsUpload');
  // 训练题目 数据管理 获取媒体资源列表
  app.get('/admin/trainQuestion/getMediaResources/:questionID', 'train.getTrainQuestionMediaResources');
  // 删除训练题目资源文件 assets 和 input 目录
  app.delete('/admin/trainQuestion/deleteSourceFile', 'train.deleteTrainQuestionSourceFile');
  // 训练题目 数据管理 媒体资源下载
  app.get('/admin/trainQuestion/:questionID/downMediaResources/:fileName', 'train.downTrainQuestionMediaResources');

  // 修改学生提交数据
  // app.put('/admin/train/putTrainRecordWithStudent/:id', 'trainPlan.putTrainRecordWithStudent');
  app.put('/web/train/putTrainRecordWithStudent/:id', 'trainThroughTrainPlan.putTrainRecordWithStudent');

  // 学生重做
  app.put('/web/trainUserRecord/redoTrain', 'trainThroughTrainPlan.redoTrain');

  app.put('/web/train/dynamicUpdateStudentRecord', 'trainThroughTrainPlan.dynamicUpdateStudentRecord');

  app.put('/web/trainPlan/judgeQuestionByID', 'trainThroughTrainPlan.judgeQuestionByID');

  app.put('/web/trainPlan/resetStudentAnswer', 'trainThroughTrainPlan.resetStudentAnswer');

  /******************************* 机房训练------自建 *******************************/
  // 获取权限
  // app.get('/pclabTrain/license', 'pclabTrain.getComputerRoomPermission')
    // 占用机房
  // app.post('/pclabTrain/license/registy', 'pclabTrain.registy')
  // 登录
  app.post('/pclabTrain/teacherLogin', 'pclabTrain.teacherLogin')
  // 机房教学 登录
  app.post('/pclabTrain/teacherLoginForPLT', 'pclabTrain.teacherLoginForPLT')
  // 班级筛选用户（机房训练专用）（页面显示使用）
  app.get('/pclabTrain/filterUserWithComputerRoomTrain', 'pclabTrain.filterUserWithComputerRoomTrain');
  // 获取所有班级(精简)，可按照学年过滤（页面显示使用）
  app.get('/pclabTrain/getClassListAll', 'pclabTrain.getClassListAllLess');
  // 批量班级筛选用户（机房训练专用）（同步时使用）
  app.get('/pclabTrain/filterBulkUserWithComputerRoomTrain', 'pclabTrain.filterBulkUserWithComputerRoomTrain');
  // 批量获取所有班级(精简)（同步时使用）
  app.get('/pclabTrain/getBulkClassListAllLess', 'pclabTrain.getBulkClassListAllLess');
    // 获取训练列表
  app.get('/pclabTrain/getTrainList', 'pclabTrain.getTrainList');
  // 获取全部系列
  app.get('/pclabTrain/getTrainSeries', 'pclabTrain.getTrainSeries');

    // 批量获取训练列表
  // app.get('/pclabTrain/getBulkTrainList', 'pclabTrain.getBulkTrainList');
  // 获取批量训练临时文件
  app.get('/pclabTrain/createBulkTrainFileZip', 'pclabTrain.createBulkTrainFileZip');
  // 获取单一训练临时文件
  app.get('/pclabTrain/createTrainFileZip', 'pclabTrain.createOneTrainFileZip');
  // 上报学生训练结果
  app.post('/pclabTrain/postTrainPlanRecord', 'pclabTrain.postTrainPlanRecord');
  // hxr凭口令登录
  app.get('/pclabTrain/loginByCode', 'pclabTrain.loginByCode');
  // 获取训练临时文件
  // app.get('/pclabTrain/createTrainFileZip', 'pclabTrain.createTrainFileZip');
  // 获取精品试卷列表
  app.get('/pclabTrain/getEliteTrains', 'pclabTrain.getEliteTrains');
  // 同步精品试卷
  app.get('/pclabTrain/importTrain', 'pclabTrain.importTrain');

  // 生成远程更新文件
  app.post('/pclabTrain/getUpdateFileZip', 'pclabTrain.getUpdateFileZip');


  /******************************* 远程题库 *******************************/
    // 发布远程题库
  app.get('/thirdPart/uploadQuestionBank/:id', 'thirdPart.uploadQuestionBank')
    // 获取远程题库
  app.get('/thirdPart/getQuestionBank/:id', 'thirdPart.getQuestionBank')
    // 获取远程题库回调接口
  app.get('/thirdPart/getQuestionBankCallBack/:id', 'thirdPart.getQuestionBankCallBack')
    // 获取各校可获取题库列表
  app.get('/thirdPart/getSchoolTrainQuestionBankList', 'thirdPart.getSchoolTrainQuestionBankList')
    // 创建或获取题库、并更新
  app.post('/thirdPart/getOrCreateQuestionBank', 'thirdPart.getOrCreateQuestionBank')
  // 测试专用！！！！！！！！！！！！！！！手动触发评卷  上线请注释！！！！！！！！！！！！！！！！！！！
  // app.get('/test/getTrainResultWithTest', 'trainPlan.TESTstatistics');

  // 训练分析列表
  // app.get('/admin/train/analyze/getTrainPlanStatisticsList', 'trainPlan.getTrainPlanStatisticsList');

  // 按题目获取统计
  // app.get('/admin/train/analyze/getQuestionStatistics', 'trainPlan.getQuestionStatistics');

  // 按知识点获取统计
  // app.get('/admin/train/analyze/getTagStatistics', 'trainPlan.getTagStatistics');

  // 按学生获取统计
  // app.get('/admin/train/analyze/getStudentStatistics', 'trainPlan.getStudentStatistics');
  // 题库统计
  app.get('/admin/train/questionBank/getQuestionBankStatistics/:id', 'questions.getQuestionBankStatistics');

  // 新接口获取统计数据 **
  // 训练分析列表
  app.get('/admin/trainPlan/analyze/getTrainPlanStatisticsList', 'trainThroughTrainPlan.getTrainPlanStatisticsList');

  // 按题目获取统计
  app.get('/admin/trainPlan/analyze/getQuestionStatistics', 'trainThroughTrainPlan.getQuestionStatistics');

  // 按知识点获取统计
  app.get('/admin/trainPlan/analyze/getTagStatistics', 'trainThroughTrainPlan.getTagStatistics');

  // 按学生获取统计
  app.get('/admin/trainPlan/analyze/getStudentStatistics', 'trainThroughTrainPlan.getStudentStatistics');

    // 创建错题集
  app.post('/admin/trainUserQuestion/createTrainUserQuestion', 'trainUserQuestion.createTrainUserQuestion');
  app.post('/web/trainUserQuestion/createTrainUserQuestion', 'trainUserQuestion.createTrainUserQuestion');
    // 获取错题集
  app.get('/admin/trainUserQuestion/getTrainUserQuestion/:id', 'trainUserQuestion.getTrainUserQuestion');
  app.get('/web/trainUserQuestion/getTrainUserQuestion/:id', 'trainUserQuestion.getTrainUserQuestion');
    // 删除错题集
  app.delete('/admin/trainUserQuestion/destoryTrainUserQuestion/:questionID', 'trainUserQuestion.destoryTrainUserQuestion');
  app.delete('/web/trainUserQuestion/destoryTrainUserQuestion/:questionID', 'trainUserQuestion.destoryTrainUserQuestion');
    // 删除错题集
  app.delete('/admin/trainUserQuestion/destoryBulkTrainUserQuestion', 'trainUserQuestion.destoryBulkTrainUserQuestion');
  app.delete('/web/trainUserQuestion/destoryBulkTrainUserQuestion', 'trainUserQuestion.destoryBulkTrainUserQuestion');
    // 删除错题集
  app.delete('/admin/trainUserQuestion/destoryMyTrainUserQuestion', 'trainUserQuestion.destoryMyTrainUserQuestion');
  app.delete('/web/trainUserQuestion/destoryMyTrainUserQuestion', 'trainUserQuestion.destoryMyTrainUserQuestion');
    // 获取错题集列表
  app.post('/admin/trainUserQuestion/getTrainUserQuestionList', 'trainUserQuestion.getTrainUserQuestionList');
  app.post('/web/trainUserQuestion/getTrainUserQuestionList', 'trainUserQuestion.getTrainUserQuestionList');
    // 按id获取错题集列表
  app.post('/admin/trainUserQuestion/getTrainUserQuestionTrain', 'trainUserQuestion.getTrainUserQuestionTrain');
  app.post('/web/trainUserQuestion/getTrainUserQuestionTrain', 'trainUserQuestion.getTrainUserQuestionTrain');
    // 获取所有标签
  app.get('/admin/trainUserQuestion/getAllTagTree', 'trainUserQuestion.getAllTagTree');
  app.get('/web/trainUserQuestion/getAllTagTree', 'trainUserQuestion.getAllTagTree');
    // 获取错题集各题型名称与题目数
  app.get('/admin/trainUserQuestion/getQuestionsCountByTagAndType', 'trainUserQuestion.getQuestionsCountByTagAndType');
  app.get('/web/trainUserQuestion/getQuestionsCountByTagAndType', 'trainUserQuestion.getQuestionsCountByTagAndType');
  // 获取错题集训练来源
  app.get('/admin/trainUserQuestion/getQuestionSource', 'trainUserQuestion.getQuestionSource');
  app.get('/web/trainUserQuestion/getQuestionSource', 'trainUserQuestion.getQuestionSource');

  // 获取当前所有错题
  app.get('/web/trainUserQuestion/getAllUserQuestions', 'trainUserQuestion.getAllUserQuestions');

  // 获取班级及用户列表按年份分隔
  app.get('/admin/class/getClassListAndUser','team.getClassListAndUser');

  // app.get('/admin/trainPlan/syncTrainPlans', 'trainPlan.syncTrainPlans');

  // 题目查重
  app.post('/admin/questions/checkDuplicateQuestions', 'questions.checkDuplicateQuestions');

  //===============================题库勘误==============================

  // 记录题库题目勘误
  app.post('/admin/questions/applyQuestionCorrect', 'questions.applyQuestionCorrect');

  // 题库勘误列表
  app.get('/admin/questions/getAllCorrectQuestions', 'questions.getAllCorrectQuestions');

  // 修改勘误字段
  app.put('/admin/questions/updateCorrectQuestion', 'questions.updateCorrectQuestion');

  // 审核勘误
  app.post('/admin/questions/reviewQuestionCorrect', 'questions.reviewQuestionCorrect')

  // 手动审核修订
  app.post('/admin/questions/reviewQuestionCorrectManual', 'questions.reviewQuestionCorrectManual');

  // 获取题库中重复的题库
  app.get('/admin/questions/getTrainDuplicateQuestions', 'questions.getTrainDuplicateQuestions');

  // 获取题库中重复的题库（远程）
  app.get('/admin/questions/getTrainDuplicateQuestionsBytaskTrain', 'questions.getTrainDuplicateQuestionsBytaskTrain');
  //===============================题库勘误==============================

  // =============================精品试卷==============================
  app.post('/admin/train/publishTrain', 'train.publishTrain');

  app.get('/admin/train/getEliteTrains', 'train.getEliteTrains');

  app.post('/admin/train/importTrains', 'train.importTrains');

  app.delete('/admin/train/destoryEliteTrains', 'train.destoryEliteTrains');

  //======================================================================

  // 复制题目
  app.post('/admin/questions/copyQuestion', 'questions.copyQuestion');

  // 跨题库复制题目
  app.post('/admin/questions/copyQuestionCrossBank', 'questions.copyQuestionCrossBank');

  // 批量修改题目状态
  app.put('/admin/questions/changeQuestionStatus', 'questions.changeQuestionStatus');

  // 批量修改题目标签
  app.put('/admin/questions/modifyQuestionTags', 'questions.modifyQuestionTags');

  // 检查引用此题目的所有试卷
  app.get('/admin/questions/checkQuestionReferInTrains', 'questions.checkQuestionReferInTrains');

  // 删除题目及题目在试卷中的引用
  app.put('/admin/questions/removeQuestionInTrains', 'questions.removeQuestionInTrains');
  

  // ============================分班===========================================
  // 班级列表导入查重，返回结果数组预览
  app.post('/admin/team/checkRearrangeClasses', 'team.checkRearrangeClasses');

  // 班级列表导入
  app.post('/admin/team/importRearrangeClasses', 'team.importRearrangeClasses');

  // ============================分班===========================================
  
  // 批量停用/启用班级用户账号
  app.put('/admin/team/handleClassAccounts', 'user.handleClassAccounts');


  // ============================授权人数限制=====================================
  // 按照学生记录、权限开始时间、权限结束时间取现在使用人员
  app.get('/admin/permissionPeople/refreshLimitPeople', 'permissionPeople.refreshLimitPeople');

  // 按照学生记录、权限开始时间、权限结束时间取现在使用人员
  app.get('/admin/permissionPeople/refreshCourseLimitPeople', 'permissionPeople.refreshCourseLimitPeople');

  // 按照学生记录、权限开始时间、权限结束时间取现在使用人员
  app.get('/admin/permissionPeople/refreshTrainLimitPeople', 'permissionPeople.refreshTrainLimitPeople');

  // 获取占用情况
  app.get('/admin/permissionPeople/getLimitPeople', 'permissionPeople.getLimitPeople');

  // 根据占用人员ID获取人员所在班级树
  app.post('/admin/permissionPeople/getLimitPeopleClass', 'permissionPeople.getLimitPeopleClass');

  // 根据占用班级ID获取占用人员
  app.get('/admin/permissionPeople/getLimisUserByClassID', 'permissionPeople.getLimisUserByClassID');

  // 更新占用人员
  app.put('/admin/permissionPeople/updateLimitPeople', 'permissionPeople.updateLimitPeople');

  // 读取历史记录
  app.get('/admin/permissionPeople/getHistoryPermission','permissionPeople.getHistoryPermission');

  // 按照历史记录和班级记录，恢复记录
  app.put('/admin/permissionPeople/replaceWithHistory', 'permissionPeople.replaceWithHistory');

  // 按照历史记录和班级记录，恢复记录
  app.put('/admin/permissionPeople/replaceOneHistory', 'permissionPeople.replaceOneHistory');

  // 超管按照班级进行清理训练计划占用
  // app.post('/admin/permissionPeople/clearTrainUserByClass', 'permissionPeople.clearTrainUserByClass');
  // 超管按照班级批量进行清理训练计划占用
  app.post('/admin/permissionPeople/clearTrainUserByBulkClass', 'permissionPeople.clearTrainUserByBulkClass');

  // 超管清除所有占用
  app.post('/admin/permissionPeople/clearAllOccupations', 'permissionPeople.clearAllOccupations');

  // ===========================================================================

  // ============================微信=====================================
  // 获取微信信息
  app.get('/admin/wechat/getWechatBindInfo/:id', 'wechat.getWechatBindInfo');
  // 设置微信信息
  app.post('/thirdPart/wechat/setWechatBindInfo', 'wechat.setWechatBindInfo');
  // 发送绑定二维码申请
  app.post('/admin/wechat/getWeChatCode', 'wechat.getWeChatCode');
  // 发送登录二维码申请
  app.post('/admin/wechat/getWeChatLoginCode', 'wechat.getWeChatLoginCode');
  // 远程微信登录
  app.post('/thirdPart/wechat/adminLogin', 'wechat.adminLogin');
  // 根据state登录
  app.post('/admin/wechat/adminLoginByState', 'wechat.adminLoginByState');
  // 刷新绑定状态
  app.post('/admin/wechat/refreshBindStatus', 'wechat.refreshBindStatus');
  // 通知前端刷新状态
  app.post('/thirdPart/wechat/toWebRefreshBindStatus', 'wechat.toWebRefreshBindStatus');
  // 解绑微信
  app.post('/admin/wechat/unbindWechat', 'wechat.unbindWechat');

  // 下载文件
  app.post('/web/file/downloadRawFile', 'file.downloadRawFile');

  // ============================ AI ============================
  app.post('/admin/course/autoGenerateCourseContent', 'section.autoGenerateCourseContent');

  app.post('/admin/course/generateCourseContent', 'section.generateCourseContent');

  // 获取AI生成的题解
  app.get('/admin/train/question/:questionID/explainQuestion', 'trainQuestionBank.explainQuestionByAI');
  app.get('/web/train/question/:questionID/explainQuestion', 'trainQuestionBank.explainQuestionByAI');

  // 预热AI生成题解
  app.get('/admin/trainQuestionBank/:questionBankID/warmup', 'trainQuestionBank.warmupAIExplain');
  // =============================== 试卷系列 ===============================
  // 创建试卷系列
  app.post('/admin/train/createTrainSeries', 'train.createTrainSeries');

  // 修改试卷系列 
  app.put('/admin/train/updateSeries', 'train.updateSeriesName');

  // 修改试卷系列分享教师ID集合
  app.put('/admin/train/shareTrainSeries/:id', 'train.shareTrainSeries');

  // 删除试卷系列分享教师ID集合中的当前用户ID
  app.put('/admin/train/unshareTrainSeries/:id', 'train.unshareTrainSeries');

  // 获取试卷系列
  app.get('/admin/train/getTrainSeries', 'train.getTrainSeries');

  // 删除试卷系列
  app.delete('/admin/train/destoryTrainSeries/:id', 'train.destoryTrainSeries');

  // 批量修改试卷的系列
  app.put('/admin/train/bulkUpdateTrainSeries', 'train.bulkUpdateTrainSeries');

  // 修改系列优先级
  app.put('/admin/train/changeSeriesPriority', 'train.changeSeriesPriority');

  // =============================== 训练数据库及文件系统清除及恢复 ===============================
  // 训练系统恢复
  app.get('/back/train/system/init', 'train.initSystem');

  // 训练系统清除
  app.get('/back/train/system/clear', 'train.cleanSystem');

  // =============================== 内存泄露调试 ===============================
  app.get('/back/memory/dump', 'database.memoryDump');

  // ========================== OAuth2 Provider ==========================
  // 验证用户会话并返回code，用户已经登录后，请求参数client_id, 响应code
  app.post('/oauth2/authorize', 'oauth2.authorize');

  // 验证code并返回accessToken, 请求参数client_id, client_secret, code，响应accessToken
  app.post('/oauth2/token', 'oauth2.token');

  // 验证accessToken并返回用户信息，请求参数accessToken，响应用户信息(id, name, login, email)
  app.get('/oauth2/user', 'oauth2.user');

  // ========================== LLM 大语言模型 ==========================
  app.post('/admin/llm/inference', 'llm.inference');
};
