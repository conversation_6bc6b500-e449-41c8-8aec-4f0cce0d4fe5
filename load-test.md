# 氦星人模拟训练系统
## 压力测试操作流程
1. 将作为压测客户端的计算机上安装[K6软件](https://k6.io/docs/get-started/installation/)；
2. 将load-test-k6-client.js拷贝至作为压测客户端的计算机；
3. 分配每台计算机的模拟终端数量，一般建议每台计算机模拟5-10台终端；
4. 配置load-test-k6-client.js内的服务器地址和模拟客户端账号范围参数；
5. 各终端同时使用k6 run load-test-k6-client.js 执行脚本，执行压力测试。

## 服务器端压力测试脚本
1. 关闭已经运行的服务器程序
2. 删除static、tmp目录和hxr.db，让数据库重新出厂化
3. 运行教师端服务器程序
4. 创建训练计划，包含班级中全部人员
5. 各终端同时使用k6 run load-test-k6-client.js 执行脚本，执行压力测试。
6. 看见终端连接状态已连接之后，开始训练
7. 看见完成度100%之后，结束训练