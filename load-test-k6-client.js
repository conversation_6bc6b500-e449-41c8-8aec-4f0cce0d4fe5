/*
# 氦星人模拟训练系统
## 压测客户端仿真工具
*/

import http from "k6/http";
import ws from "k6/ws";
import { sleep as sleep_k6, check } from "k6";

// 标准答案
const stdAnswer = JSON.parse(open('./load-test-std-answer.json'));

// 服务器地址
const SERVER_URL = "http://127.0.0.1:36048";

// 模拟客户端账号起始
//  注意数据库中共有60个学生账号 from + count 不要超过了
//  注意不同客户端账号不要有重复
const STU_FROM = 1;
const STU_COUNT = 60;  // 单机并发建议最高5-10人
const STU_PASSWORD = "7c4a8d09ca3762af61e59520943dc26494f8941b"; // 默认密码123456

// 根据以上参数计算模拟用户数和迭代次数
export let options = {
    vus: STU_COUNT,
    iterations: STU_COUNT,
};

// 生成正态分布
function randomNormalDistribution(){
    let u=0.0, v=0.0, w=0.0, c=0.0;
    do{
        //获得两个（-1,1）的独立随机变量
        u=Math.random()*2-1.0;
        v=Math.random()*2-1.0;
        w=u*u+v*v;
    }while(w==0.0||w>=1.0)
    //这里就是 Box-Muller转换
    c=Math.sqrt((-2*Math.log(w))/w);
    //返回2个标准正态分布的随机数，封装进一个数组返回
    //当然，因为这个函数运行较快，也可以扔掉一个
    //return [u*c,v*c];
    return u*c;
}

// 休眠固定时间加正态分布随机偏移，默认偏移+-5秒
function sleep(second, std_dev = 5) {
    return;
    std_dev = (std_dev > (second / 2)) ? second / 2: std_dev;
    const duration = second + (randomNormalDistribution() * std_dev);
    sleep_k6(duration)
}

// 根据训练ID获取标准答案
function getStdAnswer(trainID) {
    // 转换为数组对象格式
    const { columns, rows } = stdAnswer;
    const results = [];
    for(let row of rows) {
        const result = {};
        for(let i = 0; i < columns.length; i++) {
            const columnName = columns[i].name;
            const columnValue = row[i];

            result[columnName] = columnValue;
        }

        results.push(result);
    }

    // 过滤出对应记录
    return results.find(result => result.train_id === trainID);
}

// 学生压力测试流程
export default function() {
    // 生成当前学生用户名
    const username = `stu${STU_FROM + __VU - 1}`;
    console.log(username);

    // 启动延时
    sleep(5);

    // 检查客户端版本
    let response = null;
    
    response = http.get(`${SERVER_URL}/api/server/ok`);
    let studentVersion = null;
    check(response, {"确认连接状态，获取版本号": (r) => {
        const message = JSON.parse(r.body);
        studentVersion = message.data.student_version;
        return message.code === 0
    }});

    // 只要版本号存在，则无条件模拟升级，下载升级包
    if(studentVersion) {
        // 拼接升级包完整路径后下载升级包
        let studentUpdateURL = `${SERVER_URL}/update/模拟训练系统(学生端)_${studentVersion}.exe`;
        response = http.get(studentUpdateURL);
        check(response, {"下载学生端升级程序": (r) => {
            return r.status === 200;
        }});

        // 启动延时
        sleep(5);

        // 重启软件后，重新验证版本号
        response = http.get(`${SERVER_URL}/api/server/ok`);
        check(response, {"重新确认连接状态，获取版本号": (r) => {
            const message = JSON.parse(r.body);
            return message.code === 0;
        }});
    }

    // 获取用户会话
    response = http.get(`${SERVER_URL}/api/web/train/student/session`);
    check(response, {"获取用户会话，此时用户尚未登录": (r) => {
        const message = JSON.parse(r.body);
        return message.code === 401;
    }});

    // 模拟账号输入登录延时
    sleep(10);

    // 使用学生账号登录系统
    response = http.post(`${SERVER_URL}/api/web/train/student/login`, JSON.stringify({
        username: username,
        password: STU_PASSWORD,
    }), { headers: { "Content-Type": "application/json" } });

    let trainPlan = null;
    let trains = null;
    check(response, {"学生账号登录系统": (r) => {
        const message = JSON.parse(r.body);
        trainPlan = message.data.train_plan;
        trains = message.data.trains;
        return message.code === 0;
    }});


    // 对于每套试卷
    for(let train of trains) {
        // 模拟账号选择试卷延时
        sleep(5);

        // 下载试卷数据文件
        let trainZipURL = `${SERVER_URL}/train/${train.id}.zip`;
        response = http.get(trainZipURL);
        check(response, {"下载试卷数据文件": (r) => {
            return r.status === 200;
        }});

        // 尚未开考，启动Websocket，等待开考指令
        if(!trainPlan.start_time) {
            // 连接学生端Websocket，等待开考指令
            ws.connect(`${SERVER_URL.replace("http", "ws")}/ws/student`, {}, function(socket) {
                socket.on('message', (msg) => {
                    if(msg === "{\"type\":\"trainplan/start\"}") {
                        // 继续后续流程
                        socket.close();
                    }
                    else {
                        console.error("Websocket收到未处理信息", msg);
                    }
                });
            });
        }

        // 报名
        let trainUserRecord = null;
        response = http.post(`${SERVER_URL}/api/web/train/train_plan/${trainPlan.id}/train/${train.id}/register`);
        check(response, {"学生报名训练": (r) => {
            const message = JSON.parse(r.body);
            trainPlan = message.data.train_plan;
            train = message.data.train;
            trainUserRecord = message.data.train_user_record;
            return message.code === 0;
        }});

        // 模拟账号答卷延时
        sleep(10);

        // 上传暂存结果
        const trainStdAnswer = getStdAnswer(train.id);

        // 没有答案就跳过了暂存环节
        if(!trainStdAnswer) {
            continue;
        }

        let { finish_rate, initial_score, question_type_stat, record, score, wrong_question_ids } = trainStdAnswer;
        question_type_stat = JSON.parse(question_type_stat);
        record = JSON.parse(record);
        wrong_question_ids = JSON.parse(wrong_question_ids)

        // 完成成绩上报
        response = http.put(`${SERVER_URL}/api/web/train_user_record/${trainUserRecord.id}`, JSON.stringify({
            finish_rate, initial_score, question_type_stat, record, score
        }), { headers: { "Content-Type": "application/json" } });

        check(response, {"完成成绩上报": (r) => {
            const message = JSON.parse(r.body);
            return message.code === 0;
        }});

        // 模拟账号交卷延时
        sleep(10);

        // 完成交卷
        response = http.post(`${SERVER_URL}/api/web/train_user_record/${trainUserRecord.id}/submit`, JSON.stringify({
            finish_rate, initial_score, question_type_stat, record, score, wrong_question_ids
        }), { headers: { "Content-Type": "application/json" } });

        check(response, {"完成交卷": (r) => {
            const message = JSON.parse(r.body);
            return message.code === 0;
        }});
    }

    // 尚未结束，启动Websocket，等待结束指令
    if(!trainPlan.end_time) {
        // 连接学生端Websocket，等待结束指令
        ws.connect(`${SERVER_URL.replace("http", "ws")}/ws/student`, {}, function(socket) {
            socket.on('message', (msg) => {
                if(msg === "{\"type\":\"trainplan/stop\"}") {
                    // 继续后续流程
                    socket.close();
                }
                else {
                    console.error("Websocket收到未处理信息", msg);
                }
            });
        });
    }
}