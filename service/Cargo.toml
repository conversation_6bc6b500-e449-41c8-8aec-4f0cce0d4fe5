[package]
name = "service"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
reqwest = { version = "0.11", features = ["json", "cookies", "gzip", "brotli", "deflate"] }
entity = { path = "../entity" }
chrono = "0.4.38"
serde = "1.0.143"
serde_json = "1.0.83"
serde_derive = "1.0"
Boa = "0.13.1"
quick-xml = "0.23"
url = "2.2.2"
http = "1.1.0"
urlencoding = "2.1.0"
zip = "2.2.0"
utf-8 = "0.7.6"
regex = "1"
rand = "0.8"
hex = "0.4"
rust-crypto = "0.2.36"
winapi = { version = "0.3", features = ["fileapi"] }
async-recursion = "1.0"
sysinfo = "0.35.2"
tokio = { version = "1.20.0", features = ["macros", "rt","full"] }
[dependencies.sea-orm]
version = "1.0.1" # sea-orm version
features = [
    "debug-print",
    "runtime-async-std-native-tls",
    #"sqlx-mysql",
    # "sqlx-postgres",
    "sqlx-sqlite",
]

[dev-dependencies]


[features]
mock = ["sea-orm/mock"]

[[test]]
name = "mock"
required-features = ["mock"]
