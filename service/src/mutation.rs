use std::collections::{HashMap, HashSet};
use chrono::{DateTime, Datelike, Local, Utc};
use ::entity::{
    course::{self, Entity as Course, UploadCourse}, 
    section::{self, Entity as Section, SectionItem}, 
    section_record::{self,Entity as SecctionRecordUpdateItem}, 
    system_config::{self, Entity as SystemConfig}, 
    team::{self, Entity as Team, TeamUsersRecord, UploadUserRecord}, 
    team_user::{self, Entity as TeamUser}, terminal::{self, Entity as Terminal},   
    user::{self, Entity as User, UserRecord}
};
use sea_orm::*;
use sea_orm::sea_query::Expr;
use serde_json::{json, Map};

use crate::{Query, DBBussinessError};

#[derive(FromQueryResult)]
struct SystemConfigIDKeyResult {
    id: i32,
    key: String
}


#[derive(FromQueryResult)]
struct UserExistResult {
    id: i32,
}


pub struct Mutation;

impl Mutation {
    // 将HashMap写入系统配置数据表
    pub async fn system_config_set_by_key_value_map(txn: &DatabaseTransaction, key_value_map: HashMap<String, Option<String>>, now: &chrono::prelude::NaiveDateTime) -> Result<i32, Box<dyn std::error::Error>> {
        let mut result_count = 0;
        let keys: Vec<String> = key_value_map.keys().map(|s| s.to_string()).collect();

        let exist_key_value_models = SystemConfig::find()
            .select_only()
            .column(system_config::Column::Id)
            .column(system_config::Column::Key)
            .filter(Condition::all()
                .add(system_config::Column::DeletedAt.is_null())
                .add(system_config::Column::Key.is_in(keys))
            )
            .into_model::<SystemConfigIDKeyResult>()
            .all(txn)
            .await?;

        let mut exist_key_id_map:HashMap<String, i32> = HashMap::new();

        for exist_key_value in exist_key_value_models {
            exist_key_id_map.insert( exist_key_value.key, exist_key_value.id);
        }

        // 循环本次需要保存的键值对，根据是否存在现有配置项目，决定哪些键值要插入，哪些需要更新
        for (key, value) in key_value_map.iter() {
            result_count += 1;

            // 插入
            if !exist_key_id_map.contains_key(key) {
                system_config::ActiveModel {
                    key: Set(key.to_owned()),
                    value: Set(value.to_owned()),
                    created_at: Set(now.to_owned()),
                    updated_at: Set(now.to_owned()),
                    ..Default::default()
                }
                .insert(txn)
                .await?;

                // 跳过更新逻辑
                continue;
            }

            // 更新
            let id = exist_key_id_map.get(key).unwrap();
            system_config::ActiveModel {
                id: Set(*id),
                key: Set(key.to_owned()),
                value: Set(value.to_owned()),
                created_at: Set(now.to_owned()),
                updated_at: Set(now.to_owned()),
                ..Default::default()
            }
            .save(txn)
            .await?;
        }

        Ok(result_count)
    }


    // 按照ID插入或更新用户到用户表
    pub async fn user_save(txn: &DatabaseTransaction, user:UserRecord, now: &chrono::prelude::NaiveDateTime) -> Result<(), Box<dyn std::error::Error>> {
        // 加载已经存在的用户和ID对照关系
        let user_id: i32 = user.id;

        let exist_users = User::find()
            .select_only()
            .column(user::Column::Id)
            .filter(Condition::all()
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::Id.eq(user_id))
            )
            .into_model::<UserExistResult>()
            .all(txn)
            .await?;

        let mut exist_user_id_set:HashSet<i32> = HashSet::new();

        for exist_user in exist_users {
            exist_user_id_set.insert(exist_user.id);
        }

        // 插入
        if !exist_user_id_set.contains(&user.id) {
            user::ActiveModel {
                id: Set(user.id.to_owned()),
                username: Set(user.username.to_owned()),
                password: Set(user.password.to_owned()),
                name: Set(user.name.to_owned()),
                sen: Set(user.sen.to_owned()),
                avatar: Set(user.avatar.to_owned()),
                admin_authority: Set(user.admin_authority.to_owned()),
                school: Set(user.school.to_owned()),
                last_active_time: Set(user.last_active_time.to_owned()),
                last_active_ip: Set(user.last_active_ip.to_owned()),
                created_at: Set(now.to_owned()),
                updated_at: Set(now.to_owned()),
                ..Default::default()
            }
            .insert(txn)
            .await?;

            return Ok(());
        }

        // 更新
        user::ActiveModel {
            id: Set(user.id.to_owned()),
            username: Set(user.username.to_owned()),
            password: Set(user.password.to_owned()),
            name: Set(user.name.to_owned()),
            sen: Set(user.sen.to_owned()),
            avatar: Set(user.avatar.to_owned()),
            admin_authority: Set(user.admin_authority.to_owned()),
            school: Set(user.school.to_owned()),
            last_active_time: Set(user.last_active_time.to_owned()),
            last_active_ip: Set(user.last_active_ip.to_owned()),
            created_at: NotSet,
            updated_at: Set(now.to_owned()),
            deleted_at: Set(Option::None),
            ..Default::default()
        }
        .save(txn)
        .await?;

        Ok(())
    }

    // 按照用户ID批量插入或更新用户到用户表
    pub async fn user_batch_save_by_teams(txn: &DatabaseTransaction, teams: &Vec<TeamUsersRecord>, now: &chrono::prelude::NaiveDateTime) -> Result<bool, Box<dyn std::error::Error>> {
        // 空密码提示字段
        let mut set_null_password = false;
        for team in teams {
            let users = &team.users;
            if users.len() == 0 {
                continue;
            }

            // 整理数据
            let mut user_models:Vec<user::ActiveModel> = Vec::new(); 
            for single_user in users {
                // 默认密码123456
                let mut password = String::from("7c4a8d09ca3762af61e59520943dc26494f8941b");

                // 用户密码为空
                if single_user.password.is_none() {
                    set_null_password = true;
                }
                else {
                    // 试用用户密码，而非默认密码
                    password = single_user.to_owned().password.unwrap();
                }

                let mut u = user::ActiveModel {
                    id: Set(single_user.id.to_owned()),
                    username: Set(single_user.username.to_owned()),
                    password: Set(password),
                    name: Set(single_user.name.to_owned()),
                    school: Set(single_user.school.to_owned()),
                    sen: Set(single_user.sen.to_owned()),
                    state: Set(single_user.state.to_owned()),
                    avatar: Set(single_user.avatar.to_owned()),
                    admin_authority: Set(single_user.admin_authority.to_owned()),
                    created_at: Set((*now).clone()),
                    updated_at: Set((*now).clone()),
                    ..Default::default()
                };

                // 同步被删除账号的软删除信息
                if single_user.deleted_at.is_some() {
                    u.deleted_at = Set(Some((*now).clone()));
                }
                else {
                    u.deleted_at = Set(None);
                }

                user_models.push(u);
            }
            
            // 批量插入，有重复ID时更新名称、学年和更新时间
            User::insert_many(user_models)
                .on_conflict (
                    sea_query::OnConflict::column(user::Column::Id)
                    .update_columns([
                        user::Column::Username, user::Column::Password, user::Column::Name,
                        user::Column::School, user::Column::Sen, user::Column::State,
                        user::Column::Avatar, user::Column::AdminAuthority,
                        user::Column::UpdatedAt, user::Column::DeletedAt
                    ])
                    .to_owned()
                )
                .exec(txn).await?;
        }
       
        Ok(set_null_password)
    }

    // 批量同步班级到班级表
    pub async fn team_batch_save(txn: &DatabaseTransaction, teams: &Vec<TeamUsersRecord>, now: &chrono::prelude::NaiveDateTime)-> Result<(), Box<dyn std::error::Error>> {
        // 整理数据
        let team_models = teams.into_iter().map(|t| 
            team::ActiveModel {
                id: Set(t.team_id.to_owned()),
                name: Set(t.team_name.to_owned()),
                year: Set(t.school_year.to_owned()),
                created_at: Set((*now).clone()),
                updated_at: Set((*now).clone()),
                ..Default::default()
            }
        );

        // 批量插入，有重复ID时更新名称、学年和更新时间
        Team::insert_many(team_models)
            .on_conflict (
                sea_query::OnConflict::column(team::Column::Id)
                .update_columns([team::Column::Name, team::Column::Year, team::Column::UpdatedAt])
                .to_owned()
            )
            .exec(txn).await?;

        Ok(())
    }

    // 按照ID批量同步班级、用户信息到班级用户表
    pub async fn team_user_batch_save_by_teams(txn: &DatabaseTransaction, teams: &Vec<TeamUsersRecord>, now: &chrono::prelude::NaiveDateTime) -> Result<(), Box<dyn std::error::Error>> {
        for team in teams {
            let team_id = team.team_id;
            let users = &team.users;

            // 整理数据
            let team_user_models = users.into_iter().map(|t| 
                {
                    let mut tu = team_user::ActiveModel {
                        id: Set(t.team_user_id.to_owned()),
                        team_id: Set(team_id.to_owned()),
                        user_id: Set(t.id.to_owned()),
                        created_at: Set((*now).clone()),
                        updated_at: Set((*now).clone()),
                        ..Default::default()
                    };
                    
                    // 如果队伍关联关系删除，也同步一下
                    if t.team_user_deleted_at.is_some() {
                        tu.deleted_at = Set(Some((*now).clone()));
                    }
                    else {
                        tu.deleted_at = Set(None);
                    }

                    tu
                }

            );

            // 批量插入，有重复ID时更新名称、学年和更新时间
            TeamUser::insert_many(team_user_models)
                .on_conflict (
                    sea_query::OnConflict::column(team_user::Column::Id)
                    .update_columns([
                        team_user::Column::TeamId, team_user::Column::UserId,
                        team_user::Column::UpdatedAt, team_user::Column::DeletedAt
                    ])
                    .to_owned()
                )
                .exec(txn).await?;
        }
       
        Ok(())
    }

    // 批量插入或更新用户到用户表
    pub async fn user_batch_save(txn: &DatabaseTransaction, users: &Vec<UploadUserRecord>, now: &chrono::prelude::NaiveDateTime) -> Result<bool, Box<dyn std::error::Error>> {
        
        let mut user_models:Vec<user::ActiveModel> = Vec::new(); 
        
        for single_user in users {
            // 默认密码123456
            let mut password = String::from("7c4a8d09ca3762af61e59520943dc26494f8941b");

            // 用户密码为空时，设置默认密码
            if !single_user.password.is_none() {
                password = single_user.to_owned().password.unwrap();
            }
            
            let mut u = user::ActiveModel {
                id: Set(single_user.id.to_owned()),
                username: Set(single_user.username.to_owned()),
                password: Set(password),
                name: Set(single_user.name.to_owned()),
                school: Set(single_user.school.to_owned()),
                sen: Set(single_user.sen.to_owned()),
                state: Set(single_user.state.to_owned()),
                avatar: Set(single_user.avatar.to_owned()),
                admin_authority: Set(single_user.admin_authority.to_owned()),
                created_at: Set((*now).clone()),
                updated_at: Set((*now).clone()),
                ..Default::default()
            };

            // 同步被删除账号的软删除信息
            if single_user.deleted_at.is_some() {
                u.deleted_at = Set(Some((*now).clone()));
            }
            else {
                u.deleted_at = Set(None);
            }

            user_models.push(u);
        }
        
        // 批量插入，有重复ID时更新全部字段
        User::insert_many(user_models)
            .on_conflict (
                sea_query::OnConflict::column(user::Column::Id)
                .update_columns([
                    user::Column::Username, user::Column::Password, user::Column::Name,
                    user::Column::School, user::Column::Sen, user::Column::State,
                    user::Column::Avatar, user::Column::AdminAuthority,
                    user::Column::UpdatedAt, user::Column::DeletedAt
                ])
                .to_owned()
            )
            .exec(txn).await?;
        
        Ok(true)
    }
    // 批量插入或更新课程表
    pub async fn course_save(txn: &DatabaseTransaction, course_info: &Vec<UploadCourse>, delete:bool , now: &chrono::prelude::NaiveDateTime) -> Result<bool, Box<dyn std::error::Error>> {
        
        let mut course_models:Vec<course::ActiveModel> = Vec::new(); 
        


            for single_course in course_info{
                
                let empty_value = json!([]);
                let mut c = course::ActiveModel {
                    course_name: Set(single_course.course_name.to_owned()),
                    course_slug: Set(single_course.course_slug.to_owned()),
                    course_description: Set(single_course.course_description.to_owned()),
                    publish: Set(single_course.publish.to_owned()),
                    statist: Set(single_course.statist.to_owned()),
                    indics: Set(single_course.indics.to_owned()),
                    container_info: Set(single_course.container_info.to_owned()),
                    creater: Set(single_course.creater.to_owned()),
                    course_type: Set(single_course.course_type.to_owned()),
                    save_code: Set(single_course.save_code.to_owned()),
                    save_run_result: Set(single_course.save_run_result.to_owned()),
                    allow_paste: Set(single_course.allow_paste.to_owned()),
                    allow_copy: Set(single_course.allow_copy.to_owned()),
                    teams: Set(Some(empty_value.clone())),
                    teachers: Set(Some(empty_value.clone())),
                    created_at: Set(single_course.created_at),
                    updated_at: Set(single_course.updated_at),
                    ..Default::default()
                };

                // 同步被删除账号的软删除信息
                if delete == true {
                    c.deleted_at = Set(Some((*now).clone()));
                }
                else {
                    c.deleted_at = Set(None);
                }

                course_models.push(c);
            }
        // 批量插入，有重复CourseSlug时更新全部字段
        Course::insert_many(course_models)
            .on_conflict (
                sea_query::OnConflict::column(course::Column::CourseSlug)
                .update_columns([
                    course::Column::CourseName,
                    course::Column::CourseSlug,
                    course::Column::CourseDescription,
                    course::Column::Publish,
                    course::Column::Statist,
                    course::Column::Indics,
                    course::Column::ContainerInfo,
                    course::Column::Creater,
                    course::Column::CourseType,
                    course::Column::SaveCode,
                    course::Column::SaveRunResult,
                    course::Column::AllowPaste, 
                    course::Column::AllowCopy,
                    course::Column::Teams,
                    course::Column::Teachers,
                    course::Column::CreatedAt,
                    course::Column::UpdatedAt,
                    course::Column::DeletedAt
                ])
                .to_owned()
            )
            .exec(txn).await.unwrap();
        
        Ok(true)
    }
    // 批量插入或更新课程表
    pub async fn section_save(txn: &DatabaseTransaction, section_info: &Vec<SectionItem>, delete:bool , now: &chrono::prelude::NaiveDateTime) -> Result<bool, Box<dyn std::error::Error>> {
        
        let mut section_models:Vec<section::ActiveModel> = Vec::new(); 
        


            for single_section in section_info{
                
        
                let mut c = section::ActiveModel {
                    id: Set(single_section.id.to_owned()),
                    course_id: Set(single_section.course_id.to_owned()),
                    chapter_name: Set(single_section.chapter_name.to_owned()),
                    section_name: Set(single_section.section_name.to_owned()),
                    section_type: Set(single_section.section_type.to_owned()),
                    ext: Set(single_section.ext.to_owned()),
                    created_at: Set(single_section.created_at),
                    updated_at: Set((*now).clone()),
                    ..Default::default()
                };

                // 同步被删除账号的软删除信息
                if delete == true {
                    c.deleted_at = Set(Some((*now).clone()));
                }
                else {
                    c.deleted_at = Set(None);
                }

                section_models.push(c);
            }
        
        // 批量插入，有重复CourseSlug时更新全部字段
        // TODO: 未修改
        Section::insert_many(section_models)
            .on_conflict (
                sea_query::OnConflict::column(section::Column::Id)
                .update_columns([
                    section::Column::Id,
                    section::Column::CourseId,
                    section::Column::ChapterName,
                    section::Column::SectionName,
                    section::Column::SectionType,
                    section::Column::Ext,
                    section::Column::Record,
                    section::Column::HistoryRecords,
                    section::Column::CreatedAt,
                    section::Column::UpdatedAt,
                    section::Column::DeletedAt
                ])
                .to_owned()
            )
            .exec(txn).await?;
        
        Ok(true)
    }
    // 根据slug修改章节权限
    pub async fn indics_save(txn: &DatabaseTransaction, indics: Option<JsonValue>, course_slug:String, now: &chrono::prelude::NaiveDateTime) -> Result<bool, Box<dyn std::error::Error>> {
        let result:Option<course::Model>  = course::Entity::find()
            .filter(
                Condition::all()
                .add(course::Column::CourseSlug.eq(course_slug))
                
            )
            .one(txn)
            .await?;

        // Into ActiveModel
        let mut result: course::ActiveModel = result.unwrap().into();

        // Update name attribute
        result.indics = Set(indics.to_owned());
        result.updated_at = Set((*now).clone());
        result.update(txn).await?;
        Ok(true)
    }

    // 按照ID集合批量删除班级/用户关联表记录
    pub async fn team_user_delete_by_id(txn: &DatabaseTransaction, team_user_ids: Vec<i32>) -> Result<(), Box<dyn std::error::Error>> {
        TeamUser::delete_many()
            .filter(team_user::Column::Id.is_in(team_user_ids))
            .exec(txn).await?;

        Ok(())
    }

    // 按照ID集合批量删除非管理员的用户表记录
    pub async fn user_delete_by_id(txn: &DatabaseTransaction, user_ids: Vec<i32>) -> Result<(), Box<dyn std::error::Error>> {
        User::delete_many()
            .filter(
                Condition::all()
                    .add(user::Column::Id.is_in(user_ids))
                    .add(user::Column::AdminAuthority.is_null())
            )
            .exec(txn).await?;

        Ok(())
    }

    // 按照ID集合批量更新学生密码
    pub async fn user_change_password_by_ids(txn: &DatabaseTransaction, user_ids: Vec<i32>, password: &str, now: &chrono::prelude::NaiveDateTime) -> Result<(), Box<dyn std::error::Error>> {
        User::update_many()
            .col_expr(user::Column::Password, Expr::value(password))
            .col_expr(user::Column::UpdatedAt, Expr::value(now.to_owned()))
            .filter(
                Condition::all()
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::AdminAuthority.is_null())
                .add(user::Column::Id.is_in(user_ids))
            )
            .exec(txn)
            .await?;

        Ok(())
    }
    
    // 按照ID删除班级记录
    pub async fn team_delete_by_id(txn: &DatabaseTransaction, team_id: i32) -> Result<(), Box<dyn std::error::Error>> {
        Team::delete_by_id(team_id)
            .exec(txn).await?;

        Ok(())
    }

    // 更新指定用户的最后登录时间
    pub async fn user_update_last_active_time_ip(txn: &DatabaseTransaction, user_id: i32, client_ip: &str, now: &chrono::prelude::NaiveDateTime) -> Result<(), Box<dyn std::error::Error>> {
        let target_user = User::find_by_id(user_id).one(txn).await?;
        let mut target_user: user::ActiveModel = target_user.unwrap().into();
        target_user.updated_at = Set((*now).clone());
        target_user.last_active_time = Set(Some((*now).clone()));
        target_user.last_active_ip = Set(Some(client_ip.to_string()));
        target_user.update(txn).await?;

        Ok(())
    }

    // 清空用户登录ip
    pub async fn clear_current_user_login_ip(txn: &DatabaseTransaction) -> Result<(), Box<dyn std::error::Error>> {
        // 更新用户表
        User::update_many()
            .col_expr(user::Column::LastActiveIp, Expr::value(None::<String>))
            .filter(
                Condition::all()
                .add(user::Column::DeletedAt.is_null())
            )
            .exec(txn)
            .await?;

        Ok(())
    }

    // 清除特定id的用户登录ip
    pub async fn clear_current_user_login_ip_by_id(txn: &DatabaseTransaction, user_ids: Vec<i32>) -> Result<(), Box<dyn std::error::Error>> {
        // 更新用户表
        User::update_many()
            .col_expr(user::Column::LastActiveIp, Expr::value(None::<String>))
            .filter(
                Condition::all()
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::Id.is_in(user_ids))
            )
            .exec(txn)
            .await?;

        Ok(())
    }

    // 学生训练记录刷新IP地址
    // pub async fn train_user_record_update_ip(txn: &DatabaseTransaction, user_id: i32, plan_id: i32, train_id: i32, client_ip: &str, now: &chrono::prelude::NaiveDateTime) -> Result<(), Box<dyn std::error::Error>> {
    //     let record = Query::train_user_record_by_otherid(txn, user_id, train_id, plan_id).await?;
    //     if record.is_none() {
    //         return Err(Box::new(DBBussinessError { reason: "学生记录不存在".to_owned() }));
    //     }

    //     let record = record.unwrap();
    //     if record.user_id != user_id {
    //         return Err(Box::new(DBBussinessError { reason: "非法访问，用户记录用户编号与登录用户不一致".to_owned() }));
    //     }

    //     let mut record = record.into_active_model();

    //     record.client_ip = Set(client_ip.to_owned());
    //     record.updated_at = Set(now.to_owned());

    //     record.update(txn).await?;
        
    //     Ok(())
    // }

    // 清理试用数据库
    pub async fn clear_demo_database(txn: &DatabaseTransaction) -> Result<(), Box<dyn std::error::Error>> {
        txn
        .execute(Statement::from_string(
            DatabaseBackend::Sqlite,
            r#"DELETE FROM team;
                    DELETE FROM team_user;
                    DELETE FROM user;
                    DELETE FROM system_config;
                    DELETE FROM `sqlite_sequence` where name in ("team", "team_user", "user", "system_config");
                    "#.to_owned(),
        ))
        .await?;
    
        Ok(())
    }

    // 当更换教师登录时，清除当前训练计划
    pub async fn clear_current_train_plan_when_orther_teacher_login(txn: &DatabaseTransaction, teacher_id: i32) -> Result<(), Box<dyn std::error::Error>> {
        txn
        .execute(Statement::from_sql_and_values(
            DatabaseBackend::Sqlite,
            r#"update system_config set value = null where key = 'current_train_plan_id' and deleted_at is null and $1 <> (select create_user_id from train_plan where id = (select value from system_config where key = 'current_train_plan_id' and deleted_at is null) and deleted_at is null)"#,
            vec![teacher_id.into()],
        ))
        .await?;
    
        Ok(())
        
    }

    // 更新终端表，如果不存在此IP记录，则新建一条记录
    pub async fn terminal_update_by_ip(txn: &DatabaseTransaction, ip: &str, status: &String, user_id: Option<i32>, train_id: Option<i32>) -> Result<(), Box<dyn std::error::Error>> {
        let exist_terminal = Query::find_terminal_by_ip(txn, ip).await?;
        
        let now = chrono::prelude::Utc::now().naive_utc();

        let user_id = match user_id {
            Some(user_id) => user_id,
            None => 0
        };

        let train_id = match train_id {
            Some(train_id) => train_id,
            None => 0
        };

        // println!("exist_terminal: {:?}", exist_terminal);
       
        if exist_terminal.is_none() {
            let terminal = terminal::ActiveModel {
                ip: Set(ip.to_owned()),
                user_id: Set(Some(user_id)),
                status: Set(Some(status.to_owned())),
                train_id: Set(Some(train_id)),
                created_at: Set(now.to_owned()),
                updated_at: Set(now.to_owned()),
                ..Default::default()
            };

            terminal.insert(txn).await?;
        }
        else {
            let mut terminal = exist_terminal.unwrap().into_active_model();
            terminal.user_id = Set(Some(user_id));
            terminal.status = Set(Some(status.to_owned()));
            terminal.train_id = Set(Some(train_id));
            terminal.updated_at = Set(now.to_owned());
            terminal.update(txn).await?;
        }

        Ok(())
    }

    // 更新终端表状态，如果不存在此IP记录，则新建一条记录
    pub async fn terminal_update_status_by_ip(txn: &DatabaseTransaction, ip: &str, status: &String) -> Result<(), Box<dyn std::error::Error>> {
        let exist_terminal = Query::find_terminal_by_ip(txn, ip).await?;
        
        let now = chrono::prelude::Utc::now().naive_utc();

        if exist_terminal.is_none() {
            let terminal = terminal::ActiveModel {
                ip: Set(ip.to_owned()),
                user_id: Set(None),
                status: Set(Some(status.to_owned())),
                train_id: Set(None),
                created_at: Set(now.to_owned()),
                updated_at: Set(now.to_owned()),
                ..Default::default()
            };

            terminal.insert(txn).await?;
        }
        else {
            let mut terminal = exist_terminal.unwrap().into_active_model();
            terminal.status = Set(Some(status.to_owned()));
            terminal.updated_at = Set(now.to_owned());
            terminal.update(txn).await?;
        }

        Ok(())
    }
    // 批量更新终端表状态，如果不存在此IP记录，则跳过
    pub async fn terminal_update_status_by_ips(txn: &DatabaseTransaction, ips: Vec<String>, status: &String) -> Result<(), Box<dyn std::error::Error>> {
        for ip in ips {
            let exist_terminal = Query::find_terminal_by_ip(txn, &ip).await?;
            
            let now = chrono::prelude::Utc::now().naive_utc();

            if exist_terminal.is_none() {
                continue;
            }
            else {
                let mut terminal = exist_terminal.unwrap().into_active_model();
                terminal.status = Set(Some(status.to_owned()));
                terminal.updated_at = Set(now.to_owned());
                terminal.update(txn).await?;
            }
        }
        Ok(())
    }

    // 根据IP地址批量更新终端表记录
    pub async fn reset_terminal_by_ips(txn: &DatabaseTransaction, ips: Vec<String>) -> Result<(), Box<dyn std::error::Error>> {
        // 将user_id、status、train_id设置为null
        Terminal::update_many()
            .col_expr(terminal::Column::UserId, Expr::value(None::<i32>))
            .col_expr(terminal::Column::Status, Expr::value(None::<String>))
            .col_expr(terminal::Column::TrainId, Expr::value(None::<i32>))
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Ip.is_in(ips))
            )
            .exec(txn)
            .await?;

        Ok(())
    }

    // 根据IP地址列表批量删除终端表记录
    pub async fn terminal_delete_by_ips(txn: &DatabaseTransaction, ips: Vec<String>) -> Result<(), Box<dyn std::error::Error>> {
        // 将deleted_at设置为当前时间
        Terminal::update_many()
            // .col_expr(terminal::Column::DeletedAt, Expr::value(chrono::prelude::Utc::now().naive_utc()))
            .col_expr(terminal::Column::UserId, Expr::value(None::<i32>))
            .col_expr(terminal::Column::Status, Expr::value(None::<String>))
            .col_expr(terminal::Column::TrainId, Expr::value(None::<i32>))
            .col_expr(terminal::Column::UpdatedAt, Expr::value(chrono::prelude::Utc::now().naive_utc()))
            .col_expr(terminal::Column::DeletedAt, Expr::value(chrono::prelude::Utc::now().naive_utc()))
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::Ip.is_in(ips))
            )
            .exec(txn)
            .await?;

        Ok(())
    }

    // 根据用户id批量删除终端表记录
    pub async fn terminal_delete_by_user_ids(txn: &DatabaseTransaction, user_ids: Vec<i32>) -> Result<(), Box<dyn std::error::Error>> {
        // 将deleted_at设置为当前时间
        Terminal::update_many()
            // .col_expr(terminal::Column::DeletedAt, Expr::value(chrono::prelude::Utc::now().naive_utc()))
            .col_expr(terminal::Column::UserId, Expr::value(None::<i32>))
            .col_expr(terminal::Column::Status, Expr::value(None::<String>))
            .col_expr(terminal::Column::TrainId, Expr::value(None::<i32>))
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
                .add(terminal::Column::UserId.is_in(user_ids))
            )
            .exec(txn)
            .await?;

        Ok(())
    }

    // 清空终端设备绑定记录
    pub async fn clear_terminals_user(txn: &DatabaseTransaction) -> Result<(), Box<dyn std::error::Error>> {
        // 将deleted_at设置为当前时间
        Terminal::update_many()
            // .col_expr(terminal::Column::DeletedAt, Expr::value(chrono::prelude::Utc::now().naive_utc()))
            .col_expr(terminal::Column::UserId, Expr::value(None::<i32>))
            .col_expr(terminal::Column::Status, Expr::value(None::<String>))
            .col_expr(terminal::Column::TrainId, Expr::value(None::<i32>))
            .filter(
                Condition::all()
                .add(terminal::Column::DeletedAt.is_null())
            )
            .exec(txn)
            .await?;

        Ok(())
    }
    
    // 清空终端设备绑定记录
    pub async fn clear_terminals_record(txn: &DatabaseTransaction) -> Result<(), Box<dyn std::error::Error>> {
        // 将deleted_at设置为当前时间
        Terminal::delete_many()
            .exec(txn)
            .await?;

        Ok(())
    }

    // 更新用户密码
    pub async fn update_user_password_by_id(txn: &DatabaseTransaction, user_id: i32, password: &str) -> Result<(), Box<dyn std::error::Error>> {
        User::update_many()
            .col_expr(user::Column::Password, Expr::value(password))
            .filter(
                Condition::all()
                .add(user::Column::DeletedAt.is_null())
                .add(user::Column::Id.eq(user_id))
            )
            .exec(txn)
            .await?;

        Ok(())
    }

    // 更新课程进度
    pub async fn update_progress_by_user(txn: &DatabaseTransaction, user_id: i32,section_id: i32, record:&serde_json::Value ,total_score: i32,pass_count: i32,start_time: DateTime<Utc>) ->  Result<(), Box<dyn std::error::Error>> {
        let record_json = json!(record).to_string();

        SecctionRecordUpdateItem::update_many()
        .col_expr(section_record::Column::Record, Expr::value(record_json))
        .col_expr(section_record::Column::TotalScore, Expr::value(total_score))
        .col_expr(section_record::Column::PassCount, Expr::value(pass_count))
        .col_expr(section_record::Column::UpdatedAt, Expr::value(start_time.naive_utc()))
        .filter(
            Condition::all()
            .add(section_record::Column::DeletedAt.is_null())
            .add(section_record::Column::SectionId.eq(section_id))
            .add(section_record::Column::UserId.eq(user_id))

        )
        .exec(txn)
        .await?;

        Ok(())
    }
    // 插入新的课程进度
    pub async fn create_progress_by_user(txn: &DatabaseTransaction, user_id: i32,section_id: i32, record:&serde_json::Value ,total_score: i32,pass_count: i32,start_time: DateTime<Utc>) ->  Result<(), Box<dyn std::error::Error>> {
        let record_json = json!(record);


        let train_correction = section_record::ActiveModel {
                        
            user_id: Set(user_id),
            section_id: Set(section_id),
            record: Set(record_json),
            total_score: Set(total_score),
            pass_count: Set(pass_count),
            created_at: Set(Some(start_time.naive_utc())),
            updated_at: Set(Some(start_time.naive_utc())),

            ..Default::default()
        };
        train_correction.insert(txn).await?;

        Ok(())
    }

    // course  mutations

    // delete

    // 按照couse_slug集合批量删除course表数据
    pub async fn course_delete_by_slug(txn: &DatabaseTransaction, course_slug_list: Vec<String>) -> Result<(), Box<dyn std::error::Error>> {
        Course::delete_many()
            .filter(
                Condition::all()
                    .add(course::Column::CourseSlug.is_in(course_slug_list))
                    .add(course::Column::DeletedAt.is_null())
            )
            .exec(txn).await?;
        Ok(())
    }
    // 按照couse_slug集合批量删除section表数据
    pub async fn section_delete_by_course_slug(txn: &DatabaseTransaction, course_slug_list: Vec<String>) -> Result<(), Box<dyn std::error::Error>> {
        Section::delete_many()
            .filter(
                Condition::all()
                    .add(section::Column::CourseId.is_in(course_slug_list))
                    .add(section::Column::DeletedAt.is_null())
            )
            .exec(txn).await?;
        Ok(())
    }
}
