use sysinfo::System;


// 获取系统版本
pub fn get_system_version() -> Result<String, String> {
    // windows
    // #[cfg(windows)]
    let os_version = System::os_version().unwrap_or_else(|| "Unknown".to_string());
    let version = match os_version.as_str() {
        os if os.starts_with("10") => {
            // info!("系统版本为 Windows 10 {}", os_version);
            "win10"
        }
        os if os.starts_with("11") => {
            // info!("系统版本为 Windows 11 {}", os_version);
            "win11"
        }
        os if os.starts_with("6.1") || os.contains("7601") || os.contains("Windows 7") => {
            // Windows 7 / Windows Server 2008 R2
            "win7"
        }
        // os if os.starts_with("6.2") => {
        //     // Windows 8 / Windows Server 2012
        //     "win8"
        // }
        // os if os.starts_with("6.3") => {
        //     // Windows 8.1 / Windows Server 2012 R2
        //     "win8.1"
        // }
        _ => {
            // error!("不支持的操作系统版本: {}", os_version);
            return Err(os_version);
        }
    };
    
    return Ok
    
    (version.to_string());
}