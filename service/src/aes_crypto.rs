use crypto::symmetriccipher::Encryptor;
use crypto::symmetriccipher::Decryptor;
use crypto::aes::KeySize::KeySize256;
use crypto::aes::ctr;
use crypto::buffer::{RefReadBuffer, RefWriteBuffer};
use rand::Rng;
use crate::sea_orm::prelude::Json;

const SECRET_KEY: &[u8] = b"s1283904iujoidsgvjskSDKUHJK!@#@$";

pub fn encrypt(text: &str) -> String {
    let mut iv = [0u8; 16];
    let mut rng = rand::thread_rng();
    rng.fill(&mut iv);

    let mut encrypted = vec![0; text.len()];
    let mut encryptor = ctr(KeySize256, SECRET_KEY, &iv);
    encryptor.encrypt(&mut RefReadBuffer::new(text.as_bytes()), &mut RefWriteBuffer::new(&mut encrypted), true).unwrap();

    let encrypted_hex = hex::encode(encrypted);
    let iv_hex = hex::encode(iv);

    format!("{{\"iv\":\"{}\",\"content\":\"{}\"}}", iv_hex, encrypted_hex)
}

pub fn decrypt(hash: &Json) -> String {
    let iv_hex = hash["iv"].as_str().unwrap();
    let content_hex = hash["content"].as_str().unwrap();

    let iv = hex::decode(iv_hex).unwrap();
    let content = hex::decode(content_hex).unwrap();

    let mut decrypted = vec![0; content.len()];
    let mut decryptor = ctr(KeySize256, SECRET_KEY, &iv);
    decryptor.decrypt(&mut RefReadBuffer::new(&content), &mut RefWriteBuffer::new(&mut decrypted), true).unwrap();

    // 捕获错误
    match String::from_utf8(decrypted) {
        Ok(v) => v,
        Err(e) => {
            println!("decrypt error: {}", e);
            String::from("")
        }
    }
}
