use std::{fmt, error::Error};

#[derive(Debug)]
pub struct DBBussinessError {
    pub reason: String,
}

impl Error for DBBussinessError {}

impl fmt::Display for DBBussinessError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "业务错误：{}", self.reason )
    }
}


#[derive(Debug, Clone)]
// 定义我们的错误类型，这种类型可以根据错误处理的实际情况定制。
// 我们可以完全自定义错误类型，也可以在类型中完全采用底层的错误实现，
// 也可以介于二者之间。
pub struct HxrError {
    pub message: String
}

// 错误的生成与它如何显示是完全没关系的。没有必要担心复杂的逻辑会导致混乱的显示。
//
// 注意我们没有储存关于错误的任何额外信息，也就是说，如果不修改我们的错误类型定义的话，
// 就无法指明是哪个字符串解析失败了。
impl fmt::Display for HxrError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Error:{}", self.message)
    }
}

// 为 `DoubleError` 实现 `Error` trait，这样其他错误可以包裹这个错误类型。
impl Error for HxrError {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        // 泛型错误，没有记录其内部原因。
        None
    }
}