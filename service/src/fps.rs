
use std::{collections::HashMap, io::Cursor};



use serde::{Deserialize, Serialize};




use quick_xml::events::Event;
use quick_xml::Reader;


// oi解析结构
#[derive(Deserialize, Serialize,Debug,Clone)]
pub struct Options {
    key: String,
    image: String,
    text: String,
}

#[derive(Deserialize, Serialize,Debug,Clone)]
struct Answer {
    key: String,
    text: Vec<String>,
}

#[derive(Deserialize, Serialize,Debug)]
// #[serde(rename_all = "camelCase")]
pub struct Question {
    pub index: String,
    pub UUID: String,
    pub content: String,
    pub options: Option<Vec<Options>>,
    pub questionType: String,
    pub answer: Option<serde_json::Value>,
}

#[derive(Deserialize, Serialize,Debug)]
pub struct OIData {
    pub r#type: String,
    pub content: Vec<Question>,
    pub title: String,
    pub stat: HashMap<String, serde_json::Value>,
    pub if_show_answer: String,
    pub if_submit_limit: String,
    pub submit_limit: String,
    pub status: bool,
}


// pub fn get_oi_question(file_content: String) -> OIData {
//     let mut reader = Reader::from_reader(Cursor::new(file_content));
//     reader.trim_text(true);
//     let mut buf = Vec::new();
//     let mut questions = Vec::new();
//     let mut title = String::new();
//     let mut if_show_answer = String::new();
//     let mut current_options = Vec::new();
//     let mut current_answer = None;
//     let mut current_choice_answer = None;
//     let mut current_content = String::new();
//     let mut current_uuid = String::new();
//     let mut current_index = String::new();
//     let mut current_question_type = String::new();
//     let mut current_name = String::new();
//     let mut key = String::new();
//     let mut image = String::new();

    
//     loop {
//         match reader.read_event(&mut buf) {
//             Ok(Event::Start(ref e)) => {
//                 let c_name = e.name();
//                 current_name = String::from_utf8_lossy(c_name).to_string();
//                 match e.name() {
//                     b"singleChoice" => {
//                         current_uuid =  String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();
//                         current_index = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[1].clone()).to_string();
//                         current_content = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[2].clone()).to_string();
//                         for attr in e.attributes() {
//                             let attr = attr.unwrap();
                            
//                             if attr.key == b"answer" {
//                                 current_choice_answer = Some(String::from_utf8_lossy(&attr.value).to_string());
//                             }
//                         }
//                         current_question_type = "单选题".to_string();
//                     }
//                     b"content" => {
//                         current_uuid =  String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();
//                         current_index = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[1].clone()).to_string();
//                         current_question_type = "文本".to_string();
//                     }
//                     b"completion" => {
//                         current_uuid =  String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();
//                         current_index = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[1].clone()).to_string();
//                         for attr in e.attributes() {
//                             let attr = attr.unwrap();
                            
//                             if attr.key == b"text" {
//                                 let current_completion_content = Some(String::from_utf8_lossy(&attr.value).to_string());                                        // 使用正则表达式来匹配和替换
//                                 let re = regex::Regex::new(r"【__([^【】]*)__】").unwrap();
//                                 if let Some(current_completion_content) = current_completion_content {
//                                     current_content = re.replace(&current_completion_content, "【____】").to_string();

//                                     // 提取匹配的内容
//                                     let text = re.captures(&current_completion_content).unwrap().get(1).unwrap().as_str();
//                                     current_answer = Some(Answer {
//                                         key: "1".to_string(),
//                                         text: vec![text.to_string()],
//                                     });
//                                 }
                                
//                             }
//                         }
//                         current_question_type = "填空题".to_string();
//                     }                   
//                     b"jumbledSentence" => {
//                         current_uuid =  String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();
//                         current_index = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[2].clone()).to_string();
//                         current_content = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[1].clone()).to_string();
//                         current_question_type = match e.name() {
//                             b"singleChoice" => "单选题",
//                             b"content" => "文本",
//                             b"completion" => "填空题",
//                             b"jumbledSentence" => "选择填空题",
//                             _ => "",
//                         }.to_string();
//                     }
//                     b"option" => {
//                         key = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();
//                         image = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[1].clone()).to_string();
                        
//                     }
//                     b"answer" => {
//                         key = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();  
//                     }
//                     _ => (),
//                 }
                
//             }
//             Ok(Event::CData(e)) => {
//                 // let cdata_content = e.into_inner();
                
//                 match current_name.as_str()   {
//                     "title" => {
//                         title =  String::from_utf8_lossy(&e.into_inner()).to_string();
//                     },
//                     "ifShowAnswer" => {
//                         if_show_answer = String::from_utf8_lossy(&e.into_inner()).to_string();

//                     },
//                     "option" => {
//                         let text = String::from_utf8_lossy(&e.into_inner()).to_string();
//                         current_options.push(Options {
//                             key: key.clone(),
//                             image: image.clone(),
//                             text,
//                         });

//                     },
//                     "content" => {
//                         current_content = String::from_utf8_lossy(&e.into_inner()).to_string();

//                     }
//                     "answer" => {
//                         let text = String::from_utf8_lossy(&e.into_inner()).to_string();
//                         current_answer = Some(Answer {
//                             key: key.clone(),
//                             text: vec![text],
//                         });
//                     }
//                     _ => {}
//                 }
//             },
//             Ok(Event::Text(e))=>{
//                 if current_name == "ifShowAnswer" {
//                     if_show_answer = String::from_utf8_lossy(&e.into_inner()).to_string();

//                 }
//             }
            
//             Ok(Event::End(ref e)) => {
//                 match e.name() {
//                     b"singleChoice" | b"content" | b"completion" | b"jumbledSentence" => {
//                         let answer = match current_question_type.as_str() {
//                             "单选题" => serde_json::Value::String(current_choice_answer.clone().unwrap()),
//                             "填空题" => serde_json::Value::Array(vec![serde_json::json!({
//                                 "key": 1,
//                                 "text": current_answer.clone().unwrap().text
//                             })]),
//                             "选择填空题" => serde_json::Value::Object(serde_json::Map::new()),
//                             _ => serde_json::Value::Null,
//                         };
//                         questions.push(Question {
//                             index: current_index.clone(),
//                             UUID: current_uuid.clone(),
//                             content: current_content.clone(),
//                             options: Some(current_options.clone()),
//                             questionType: current_question_type.clone(),
//                             answer,
//                         });
//                         current_options.clear();
//                         current_answer = None;
//                     }
//                     _ => (),
//                 }
//             }
//             Ok(Event::Eof) => break,
//             _ => (),
//         }
//         buf.clear();
//     }
//     let data = OIData {
//         r#type: "OI".to_string(),
//         content: questions,
//         title,
//         stat: HashMap::new(),
//         if_show_answer: if_show_answer,
//         if_submit_limit: "".to_string(),
//         submit_limit: "".to_string(),
//         status: true,
//     };
//     data
// }