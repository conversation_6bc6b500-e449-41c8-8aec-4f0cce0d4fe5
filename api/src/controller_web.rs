use std::{collections::{HashMap, HashSet}, env, fs};


use actix_multipart::Multipart;
use actix_session::Session;
use actix_web::{
    get, post, put, web, HttpRequest, HttpResponse, Result
};
use futures::{StreamExt, TryStreamExt};
use tokio::{fs as tokio_fs, io::AsyncWriteExt};
// use entity::{section_user_record::{TrainUpdateUserRecord, TrainSubmitUserRecord}, section_user_correction_record::{TrainCorrectionCreateRecord, TrainCorrectionUpdateRecord, TrainCorrectionSubmitRecord}};

use sea_orm::TransactionTrait;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use chrono:: prelude::*;
use websocket::server::{ClientMessage, ListOnlineSessionCount};
use super::context::{ AppState, BusinessError, BusinessResponse };
use service::{ get_path_in_exe_dir, log_request, Mutation, Query };





#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct TrainStudentLoginRequest{
    username: String,
    password: String
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct ResetPasswordRequest{
    username: String,
    password: String,
    new_password: String
}


/**
 * 学生登录并加载当前训练计划的可用试卷列表
 * 参数
 * {
    "username": "admin",
    "password": "7c4a8d09ca3762af61e59520943dc26494f8941b"
    }
* 异常返回
{
    "code": 403,
    "message": "出现了流程错误 请同学在教师建立完成训练计划后再登录系统",
    "data": null
}
* 正常返回
{
    "code": 0,
    "message": "ok",
    "data": {
        "trains": [
            {
                "id": 230,
                "name": "测试批量训练导出-1",
                "notice": null,
                "score": 100,
                "template": "[{\"count\":10,\"limit\":[10],\"name\":\"1\",\"score\":10,\"type\":[\"单选题\"]}]",
                "template_difficulty": "{\"未鉴定\":0,\"简单\":10,\"超纲\":0,\"较难\":0,\"适中\":0}",
                "template_name": "自由组卷"
            },
            {
                "id": 231,
                "name": "测试批量训练导出-2",
                "notice": null,
                "score": 100,
                "template": "[{\"count\":10,\"limit\":[10],\"name\":\"1\",\"score\":10,\"type\":[\"单选题\"]}]",
                "template_difficulty": "{\"未鉴定\":0,\"简单\":10,\"超纲\":0,\"较难\":0,\"适中\":0}",
                "template_name": "自由组卷"
            }
        ],
        "train_user_records": [],
        "user": {
            "avatar": null,
            "display_name": "admin",
            "id": 2,
            "username": "admin"
        },
        "train_plan": {
            "abstract": "训练简介",
            "duration": 45,
            "id": 3,
            "if_set_wrong_problem_collection": 1,
            "if_show_correction_results": 1,
            "if_show_score": 1,
            "if_show_wrong_answer": 1,
            "mode": "训练模式",
            "status": "{\"学生登录\":true,\"开始训练\":false,\"数据上报\":false,\"训练反馈\":false,\"训练结束\":false}"
        }
    }
}
*/
#[post("/api/web/section/student/login")]
pub async fn post_section_student_login(
    session: Session,
    section_student_login_request: web::Json<TrainStudentLoginRequest>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let start_time = Utc::now();

    // 接收并检查POST传入的参数，用户名密码
    let section_student_login_request = section_student_login_request.into_inner();
    if section_student_login_request.username.is_empty() {
        return Err(BusinessError::ValidationError { field: "username".to_owned() });
    }

    if section_student_login_request.password.is_empty() {
        return Err(BusinessError::ValidationError { field: "password".to_owned() });
    }

    // 查询Websocket服务器获取在线用户信息
    let websocket_server = &app_state.ws_server;
    
    let msg: ListOnlineSessionCount = ListOnlineSessionCount { };
    let online_ips = match websocket_server.send(msg).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法获取到在线用户IP {}", e.to_string())}) 
    };

    if online_ips > 120 {
        return Err(BusinessError::InternalError { reason: format!("在线客户端超过限制人数120人")});
    }

    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 读取账号是否存在
    let user_record = match Query::user_find_by_username(&txn, section_student_login_request.username.clone()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };

    // 用户不存在
    if user_record.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
    }

    let user_record = user_record.unwrap();

    // 读取免密登录配置
    let enable_no_password_login = match Query::system_config_find_by_key(&txn, "enable_no_password_login").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取免密登录配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 读取学生端界面风格配置
    let enable_modern_style_config = match Query::system_config_find_by_key(&txn, "enable_modern_style").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取学生端界面风格配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 未开启免密登录则检查密码是否一致
    if enable_no_password_login.as_deref() != Some("1") || enable_no_password_login.is_none() {
        // 如果存在密码是否一致
        if (user_record.password != section_student_login_request.password) &&
        (section_student_login_request.password != "2fdb3be15236116c02966b50d4a3277a9fc60d7f") {
            txn.rollback().await.unwrap();
            return Err(BusinessError::AccountError { reason: "用户名密码不一致".to_string() });
        }
    }

    // 获取用户登录信息
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    // 从system_config中读取配置
    let enable_terminal_ip_bind = match Query::system_config_find_by_key(&txn, "enable_terminal_ip_bind").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 无论终端绑定与否，每个学生账号只能在一个IP上登录

    // 通过用户id查找终端设备
    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("通过用户ID查找已登录终端设备失败 {}", e.to_string()).to_string() })
        }
    };

    // 如果存在终端设备，且ip不一致，说明此账号已在其他设备上登录，禁止登录
    // 这个时候有两种情况：
    // 1. 恶意学生在合规学生后登录，尝试用他人账号答题，这就被拦截了；
    // 2. 恶意学生在合规学生前登录，合规学生登录时被阻拦，此时合规学生可以联系老师清除终端绑定，然后再登录
    // 情况2还需要探讨恶意学生是否已经开始答卷，如果已经开始答卷，教师清除终端绑定的时候，也需要清除恶意登录学生的答卷信息，否则合规学生将无法作答。
    // if terminal.is_some() {
    //     let terminal = terminal.unwrap();
    //     if terminal.ip != client_ip {
    //         txn.rollback().await.unwrap();
    //         return Err(BusinessError::AccountError { reason: format!("账号{}已在设备{}上登录，如您是账号所有者，请联系老师在终端管理中 初始化先登录设备的记录 或 迁移考试到本机", section_student_login_request.username.clone(), terminal.ip) });
    //     }
    // }

    // 如果开启了终端绑定配置,TODO此时用户记录里的上次登录IP为空或者和当前登录IP必须一致，是否则禁止登录
    if enable_terminal_ip_bind.is_some() {
        let enable_terminal_ip_bind = enable_terminal_ip_bind.unwrap();
        // println!("enable_terminal_ip_bind={}", enable_terminal_ip_bind);
        if enable_terminal_ip_bind == "1" {
            // 通过ip查找终端设备
            let terminal = match Query::find_terminal_by_ip(&txn, client_ip).await {
                Ok(r) => r,
                Err(e) => {
                    txn.rollback().await.unwrap();
                    return Err(BusinessError::InternalError { reason: format!("通过IP查找终端设备失败 {}", e.to_string()).to_string() })
                }
            };

            // 如果存在终端设备，且user_id不一致，说明此设备已有其他账号登录，禁止登录
            if terminal.is_some() {
                let terminal = terminal.unwrap();
                if terminal.user_id.is_some() && terminal.user_id.unwrap() != 0 && terminal.user_id.unwrap() != user_record.id {
                    txn.rollback().await.unwrap();
                    return Err(BusinessError::AccountError { reason: "此设备已绑定账号，禁止登录其他账号。如确定需要登录此账号，请联系教师在终端管理中清除此设备的登录记录".to_string() });
                }
            }
        }
    }

    // 更新终端设备的user_id
    let status = "在线".to_string();
    let user_id = Some(user_record.id);
    let section_id = None;
    match Mutation::terminal_update_by_ip(&txn, client_ip, &status, user_id, section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("更新终端设备的用户信息失败 {}", e.to_string()).to_string() })
        }
    };

    // 读取是否开启纠错模式
    let enable_correction_mode_config = match Query::system_config_find_by_key(&txn, "enable_correction_mode").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    let enable_correction_mode_config = enable_correction_mode_config.unwrap_or("0".to_string());

//     // 获取当前可用的课程计划
//     let course_list = match Query::query_course_list(&txn, user_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             return Err(BusinessError::InternalError { reason: format!("获取当前可用的课程计划失败 {}", e.to_string()).to_string() });
//         }
//     };
    
//     let course_list = match course_list {
//         Some(list) => list,
//         None => serde_json::Value::Array(Vec::new()),
//     };



// // course_list转换为json
// let course_list = serde_json::to_value(course_list).unwrap();

// // 遍历course_list,过滤出有效信息
// let mut course_list_full = Vec::new();

// for course in course_list.as_array().unwrap() {
    
//     let id = course["creater_id"].clone().to_string();
//     // 将id转为i32
//     let id: i32 = id.parse().unwrap_or(0);
//     let course_creater = match Query::query_user_by_id(&txn, id).await {
//         Ok(r) => r,
//         Err(e) => {
//             return Err(BusinessError::InternalError { reason: format!("获取创建者信息失败 {}", e.to_string()).to_string() });
//         }
//     };
//     let mut record_full= json!({});
//     if let Some(course_creater) = course_creater {
//         record_full = json!({
//             "id": course["id"],
//             "updated_at":course["updated_at"],
//             "createrID": course["creater_id"],
//             "courseDescription":course["course_description"],
//             "courseName":course["course_name"],
//             "courseSlug": course["course_slug"],
//             "courseType":course["course_type"],
//             "creater":{
//                 "name": course_creater["name"],
//                 "avatar": course_creater["avatar"]
//             },
//             "statist":{
//                 "students":course["statist"]["students"],
//                 "chapters": course["statist"]["chapters"]
//             },
//             "teachers" : course["teachers"],
//             "teams" :course["teams"]
//         });
//     }
    
//     // 将record_full存入course_list_full
//     course_list_full.push(record_full);      
// };
// let course_list_json = serde_json::to_value(course_list_full).expect("Failed to serialize course list");




    // 更新用户最后登录时间
    match Mutation::user_update_last_active_time_ip(&txn, user_record.id, client_ip, &now).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("更新用户最后激活时间失败 {}", e.to_string()).to_string() })
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut user_response: serde_json::Map<String, Value> = serde_json::Map::new();
    user_response.insert(String::from("id"), Value::Number(user_record.id.into()));
    user_response.insert(String::from("username"),  Value::String(user_record.username.clone()));
    user_response.insert(String::from("display_name"),  Value::String(user_record.name.clone()));

    if user_record.avatar.is_none() {
        user_response.insert(String::from("avatar"),  Value::Null);
    }
    else {
        user_response.insert(String::from("avatar"),  Value::String(user_record.avatar.unwrap()));
    }

    let mut response: HashMap<String, Value> = HashMap::new();
    response.insert(String::from("user"),  Value::Object(user_response));
    response.insert(String::from("enable_modern_style"),  Value::String(enable_modern_style_config.unwrap_or("0".to_string())));
    response.insert(String::from("enable_correction_mode"),  Value::String(enable_correction_mode_config));
    // response.insert(String::from("course_list"), course_list_json);

    const SERVER_VERSION: &str = env!("CARGO_PKG_VERSION");
    response.insert(String::from("api_version"), SERVER_VERSION.into());

    // 写入会话
    match session.insert("user_id", user_record.id.clone()) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };

    match session.insert("user_display_name", user_record.name.clone()) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };
    
    match session.insert("user_admin_authority", user_record.admin_authority.clone()) {
        Ok(_r) => (),
        Err(_e) => return Err(BusinessError::InternalError { reason: "会话写入失败".to_string() })
    };

    // 记录日志
    log_request(client_ip, "/api/web/section/student/login", user_record.id.clone(), start_time);

    // 通知前端刷新页面
    let websocket_server = &app_state.ws_server;
    let process= serde_json::json!({
        "type": "terminalRefresh",
    }).to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: process };
    websocket_server.do_send(msg);

    BusinessResponse::ok(response).to_json_result()
}

#[post("/api/web/section/student/prelogin")]
pub async fn post_section_student_prelogin(
    section_student_login_request: web::Json<TrainStudentLoginRequest>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let start_time = Utc::now();

    // 接收并检查POST传入的参数，用户名密码
    let section_student_login_request = section_student_login_request.into_inner();
    if section_student_login_request.username.is_empty() {
        return Err(BusinessError::ValidationError { field: "username".to_owned() });
    }

    if section_student_login_request.password.is_empty() {
        return Err(BusinessError::ValidationError { field: "password".to_owned() });
    }

    // 查询Websocket服务器获取在线用户信息
    let websocket_server = &app_state.ws_server;
    
    let msg: ListOnlineSessionCount = ListOnlineSessionCount { };
    let online_ips = match websocket_server.send(msg).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法获取到在线用户IP {}", e.to_string())}) 
    };

    if online_ips > 120 {
        return Err(BusinessError::InternalError { reason: format!("在线客户端超过限制人数120人")});
    }

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 读取账号是否存在
    let user_record = match Query::user_find_by_username(&txn, section_student_login_request.username.clone()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };

    // 用户不存在
    if user_record.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
    }

    let user_record = user_record.unwrap();

    // 读取免密登录配置
    let enable_no_password_login = match Query::system_config_find_by_key(&txn, "enable_no_password_login").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取免密登录配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 未开启免密登录则检查密码是否一致
    if enable_no_password_login.as_deref() != Some("1") || enable_no_password_login.is_none() {
        // 如果存在密码是否一致
        if (user_record.password != section_student_login_request.password) &&
        (section_student_login_request.password != "2fdb3be15236116c02966b50d4a3277a9fc60d7f") {
            txn.rollback().await.unwrap();
            return Err(BusinessError::AccountError { reason: "用户名密码不一致".to_string() });
        }
    }

    // 获取用户登录信息
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    // 从system_config中读取配置
    let enable_terminal_ip_bind = match Query::system_config_find_by_key(&txn, "enable_terminal_ip_bind").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 无论终端绑定与否，每个学生账号只能在一个IP上登录

    // 通过用户id查找终端设备
    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("通过用户ID查找已登录终端设备失败 {}", e.to_string()).to_string() })
        }
    };

    // 如果存在终端设备，且ip不一致，说明此账号已在其他设备上登录，禁止登录
    // 这个时候有两种情况：
    // 1. 恶意学生在合规学生后登录，尝试用他人账号答题，这就被拦截了；
    // 2. 恶意学生在合规学生前登录，合规学生登录时被阻拦，此时合规学生可以联系老师清除终端绑定，然后再登录
    // 情况2还需要探讨恶意学生是否已经开始答卷，如果已经开始答卷，教师清除终端绑定的时候，也需要清除恶意登录学生的答卷信息，否则合规学生将无法作答。
    if terminal.is_some() {
        let terminal = terminal.unwrap();
        if terminal.ip != client_ip {
            txn.rollback().await.unwrap();
            return Err(BusinessError::AccountError { reason: format!("账号{}已在设备{}上登录，如您是账号所有者，请联系老师在终端管理中 初始化先登录设备的记录 或 迁移考试到本机", section_student_login_request.username.clone(), terminal.ip) });
        }
    }

    // 如果开启了终端绑定配置,TODO此时用户记录里的上次登录IP为空或者和当前登录IP必须一致，是否则禁止登录
    if enable_terminal_ip_bind.is_some() {
        let enable_terminal_ip_bind = enable_terminal_ip_bind.unwrap();
        // println!("enable_terminal_ip_bind={}", enable_terminal_ip_bind);
        if enable_terminal_ip_bind == "1" {
            // 通过ip查找终端设备
            let terminal = match Query::find_terminal_by_ip(&txn, client_ip).await {
                Ok(r) => r,
                Err(e) => {
                    txn.rollback().await.unwrap();
                    return Err(BusinessError::InternalError { reason: format!("通过IP查找终端设备失败 {}", e.to_string()).to_string() })
                }
            };

            // 如果存在终端设备，且user_id不一致，说明此设备已有其他账号登录，禁止登录
            if terminal.is_some() {
                let terminal = terminal.unwrap();
                if terminal.user_id.is_some() && terminal.user_id.unwrap() != 0 && terminal.user_id.unwrap() != user_record.id {
                    txn.rollback().await.unwrap();
                    return Err(BusinessError::AccountError { reason: "此设备已绑定账号，禁止登录其他账号。如确定需要登录此账号，请联系教师在终端管理中清除此设备的登录记录".to_string() });
                }
            }
        }
    }

    // let user_id = Some(user_record.id);
    // // 获取当前可用的课程计划
    // let course_list = match Query::query_course_list(&txn, user_id).await {
    //     Ok(r) => r,
    //     Err(e) => {
    //         return Err(BusinessError::InternalError { reason: format!("获取当前可用的课程计划失败 {}", e.to_string()).to_string() });
    //     }
    // };
    
    // let course_list = match course_list {
    //     Some(list) => list,
    //     None => serde_json::Value::Array(Vec::new()),
    // };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut user_response: serde_json::Map<String, Value> = serde_json::Map::new();
    user_response.insert(String::from("id"), Value::Number(user_record.id.into()));
    user_response.insert(String::from("username"),  Value::String(user_record.username.clone()));
    user_response.insert(String::from("display_name"),  Value::String(user_record.name.clone()));

    if user_record.avatar.is_none() {
        user_response.insert(String::from("avatar"),  Value::Null);
    }
    else {
        user_response.insert(String::from("avatar"),  Value::String(user_record.avatar.unwrap()));
    }

    let mut response: HashMap<String, Value> = HashMap::new();
    response.insert(String::from("user"),  Value::Object(user_response));
    // response.insert(String::from("course_list"), course_list);

    // 记录日志
    log_request(client_ip, "/api/web/section/student/prelogin", user_record.id.clone(), start_time);

    BusinessResponse::ok(response).to_json_result()
}

#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
pub struct StudentAutoLoginRequest{
    user_id: i32,
}

/**
 * 学生登录并加载当前训练计划的可用试卷列表
* 异常返回
{
    "code": 403,
    "message": "出现了流程错误 请同学在教师建立完成训练计划后再登录系统",
    "data": null
}
* 正常返回
{
    "code": 0,
    "message": "ok",
    "data": {
        "sections": [
            {
                "id": 230,
                "name": "测试批量训练导出-1",
                "notice": null,
                "score": 100,
                "template": "[{\"count\":10,\"limit\":[10],\"name\":\"1\",\"score\":10,\"type\":[\"单选题\"]}]",
                "template_difficulty": "{\"未鉴定\":0,\"简单\":10,\"超纲\":0,\"较难\":0,\"适中\":0}",
                "template_name": "自由组卷"
            },
            {
                "id": 231,
                "name": "测试批量训练导出-2",
                "notice": null,
                "score": 100,
                "template": "[{\"count\":10,\"limit\":[10],\"name\":\"1\",\"score\":10,\"type\":[\"单选题\"]}]",
                "template_difficulty": "{\"未鉴定\":0,\"简单\":10,\"超纲\":0,\"较难\":0,\"适中\":0}",
                "template_name": "自由组卷"
            }
        ],
        "train_user_records": [],
        "user": {
            "avatar": null,
            "display_name": "admin",
            "id": 2,
            "username": "admin"
        },
        "train_plan": {
            "abstract": "训练简介",
            "duration": 45,
            "id": 3,
            "if_set_wrong_problem_collection": 1,
            "if_show_correction_results": 1,
            "if_show_score": 1,
            "if_show_wrong_answer": 1,
            "mode": "训练模式",
            "status": "{\"学生登录\":true,\"开始训练\":false,\"数据上报\":false,\"训练反馈\":false,\"训练结束\":false}"
        }
    }
}
*/
#[get("/api/web/train/student/session")]
pub async fn get_train_student_session(
    session: Session,
    http_request: HttpRequest,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let start_time = Utc::now();

    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }

    let user_id = user_id.unwrap();

    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 读取账号是否存在
    let user_record = match Query::user_find_by_id(&txn, user_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };

    // 用户不存在
    if user_record.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
    }

    let user_record = user_record.unwrap();

    let enable_correction_mode_config = match Query::system_config_find_by_key(&txn, "enable_correction_mode").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    let enable_correction_mode_config = enable_correction_mode_config.unwrap_or("0".to_string());

    // 自系统配置表读取当前训练计划ID
    let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()).to_string() });
        }
    };

    let current_train_plan_id = current_train_plan_id.unwrap_or("0".to_string());
    
    let mut response: HashMap<String, Value> = HashMap::new();

    // 读取学生端界面风格配置
    let enable_modern_style_config = match Query::system_config_find_by_key(&txn, "enable_modern_style").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut user_response: serde_json::Map<String, Value> = serde_json::Map::new();
    user_response.insert(String::from("id"), Value::Number(user_record.id.into()));
    user_response.insert(String::from("username"),  Value::String(user_record.username.clone()));
    user_response.insert(String::from("display_name"),  Value::String(user_record.name.clone()));

    if user_record.avatar.is_none() {
        user_response.insert(String::from("avatar"),  Value::Null);
    }
    else {
        user_response.insert(String::from("avatar"),  Value::String(user_record.avatar.unwrap()));
    }

    response.insert(String::from("user"),  Value::Object(user_response));
    response.insert(String::from("server_timestamp"), Value::Number(((now.and_utc().timestamp()  - 8 * 3600) * 1000).into()));
    response.insert(String::from("enable_modern_style"),  Value::String(enable_modern_style_config.unwrap_or("0".to_string())));
    response.insert(String::from("enable_correction_mode"),  Value::String(enable_correction_mode_config));
    response.insert(String::from("current_train_plan_id"),  Value::String(current_train_plan_id));

    // 记录日志
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/train/student/session", user_record.id.clone(), start_time);

    BusinessResponse::ok(response).to_json_result()
}





/**
 * 学生登录并加载当前训练计划的可用试卷列表
* 异常返回
{
    "code": 403,
    "message": "出现了流程错误 请同学在教师建立完成训练计划后再登录系统",
    "data": null
}
* 正常返回
{
    "code": 0,
    "message": "ok",
    "data": {
        "trains": [
            {
                "id": 230,
                "name": "测试批量训练导出-1",
                "notice": null,
                "score": 100,
                "template": "[{\"count\":10,\"limit\":[10],\"name\":\"1\",\"score\":10,\"type\":[\"单选题\"]}]",
                "template_difficulty": "{\"未鉴定\":0,\"简单\":10,\"超纲\":0,\"较难\":0,\"适中\":0}",
                "template_name": "自由组卷"
            },
            {
                "id": 231,
                "name": "测试批量训练导出-2",
                "notice": null,
                "score": 100,
                "template": "[{\"count\":10,\"limit\":[10],\"name\":\"1\",\"score\":10,\"type\":[\"单选题\"]}]",
                "template_difficulty": "{\"未鉴定\":0,\"简单\":10,\"超纲\":0,\"较难\":0,\"适中\":0}",
                "template_name": "自由组卷"
            }
        ],
        "train_user_records": [],
        "user": {
            "avatar": null,
            "display_name": "admin",
            "id": 2,
            "username": "admin"
        },
        "train_plan": {
            "abstract": "训练简介",
            "duration": 45,
            "id": 3,
            "if_set_wrong_problem_collection": 1,
            "if_show_correction_results": 1,
            "if_show_score": 1,
            "if_show_wrong_answer": 1,
            "mode": "训练模式",
            "status": "{\"学生登录\":true,\"开始训练\":false,\"数据上报\":false,\"训练反馈\":false,\"训练结束\":false}"
        }
    }
}
*/
#[get("/api/server/ok")]
pub async fn get_server_ok(app_state: web::Data<AppState>, session: Session, request: HttpRequest) -> Result<HttpResponse, BusinessError> {
    let start_time = Utc::now();
    let conn = &app_state.conn;
    let student_version= match Query::system_config_find_by_key_conn(conn, "student_version").await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 从system_config中读取配置
    let enable_no_password_login = match Query::system_config_find_by_key(&txn, "enable_no_password_login").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 通过IP获取终端设备
    let connection_info = request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    // 更新设备状态为在线
    let status = "离线".to_string();
    match Mutation::terminal_update_status_by_ip(&txn, client_ip, &status).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新终端表失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut response: HashMap<String, Option<String>> = HashMap::new();
    response.insert("studentVersion".into(), student_version);
    response.insert("enableNoPasswordLogin".into(), enable_no_password_login);

    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => Some(0)
    };
    let user_id = user_id.unwrap_or(0);

    // 记录日志
    log_request(client_ip, "/api/server/ok", user_id, start_time);

    BusinessResponse::ok(response).to_json_result()
}

// 学生退出程序，清除终端设备绑定的用户信息
#[post("/api/web/section/student/exit")]
pub async fn student_unbind_terminal( 
    session: Session,
    request: HttpRequest,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let start_time = Utc::now();
    // 加载Session获取用户ID
    // 没有session则不处理
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Ok(HttpResponse::Ok().json(BusinessResponse::ok(true)))
    };

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 通过IP获取终端设备
    let connection_info = request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    // 更新设备状态为离线
    let status = "离线".to_string();
    match Mutation::terminal_update_status_by_ip(&txn, client_ip, &status).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("更新终端表失败 {}", e.to_string()).to_string() })
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let response: HashMap<String, Value> = HashMap::new();

    let user_id = user_id.unwrap_or(0);
    log_request(client_ip, "/api/web/section/student/exit", user_id, start_time);

    // 通知前端刷新页面
    let websocket_server = &app_state.ws_server;
    let process= serde_json::json!({
        "type": "terminalRefresh",
    }).to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: process };
    websocket_server.do_send(msg);

    BusinessResponse::ok(response).to_json_result()
}


// 学生重置密码
#[post("/api/web/user/reset/password")]
pub async fn post_section_student_reset_password(
    request: web::Json<ResetPasswordRequest>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {

    let start_time = Utc::now();
    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    let username = request.username.clone();

    // 读取用户表记录
    let user = match Query::user_find_by_username(&txn, username).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("获取用户记录时出错 {}", e.to_string()).to_string() })
        }
    };

    if user.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::InternalError { reason: "用户不存在".to_string() });
    }

    let user = user.unwrap();

    let user_id = user.id;

    // 检查密码是否正确
    let password = request.password.clone();
    if password != user.password {
        txn.rollback().await.unwrap();
        return Err(BusinessError::InternalError { reason: "原密码错误".to_string() });
    }

    // 重置密码
    let new_password = request.new_password.clone();

    match Mutation::update_user_password_by_id(&txn, user_id, &new_password).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("更新用户密码时出错 {}", e.to_string()).to_string() })
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let response: HashMap<String, Value> = HashMap::new();

    // 通过IP获取终端设备
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/user/reset/password", user_id, start_time);

    BusinessResponse::ok(response).to_json_result()
}
// 学生端上传操作题文件
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitOperationParams{
    pub chapter_name: String,
    pub section_name: String,
    pub course_slug: String,
    pub section_id: i32,
    pub student_answer: Value,
}
#[post("/api/web/course/upload/file/operation")]
pub async fn upload_operation_file(
    mut payload: Multipart,
    session: Session,
    params : web::Json<CommitOperationParams>,
) -> Result<HttpResponse, BusinessError> {
    let course_slug = params.course_slug.clone();
    let chapter_name = params.chapter_name.clone();
    let section_name = params.section_name.clone();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();

    // 如果没有static/student/operation文件夹，创建
    let student_operation_dir = get_path_in_exe_dir("static").join("student").join("course").join(course_slug).join(chapter_name).join(section_name).join(user_id.to_string());

    // 打印文件路径
    // println!("student_operation_dir: {:?}", &student_operation_dir);

    if !student_operation_dir.exists() {
        match std::fs::create_dir_all(&student_operation_dir) {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("创建文件夹失败 {}", e.to_string()).to_string() })
        };
    }

    while let Ok(Some(mut field)) = payload.try_next().await {
        let content_type = field.content_disposition();
        let filename = content_type.unwrap().get_filename().unwrap();
        let file_path = student_operation_dir.join(&filename);

        // 打印文件路径
        // println!("file path: {:?}", &file_path);

        // 检查文件是否已存在，如果存在则删除
        if file_path.exists() {
            if let Err(e) = tokio_fs::remove_file(&file_path).await {
                return Err(BusinessError::InternalError { reason: format!("删除文件失败 {}", e.to_string()).to_string() });
            }
        }

        // 创建文件
        let mut file = match tokio_fs::File::create(&file_path).await {
            Ok(file) => file,
            Err(e) => {
                return Err(BusinessError::InternalError { reason: format!("无法创建文件 {}", e.to_string()).to_string() });
            }
        };

        // 将文件内容写入
        while let Some(chunk) = field.next().await {
            let data = chunk.unwrap();
            file = match file.write_all(&data).await {
                Ok(_) => file,
                Err(e) => {
                    return Err(BusinessError::InternalError { reason: format!("无法写入文件 {}", e.to_string()).to_string() });
                }
            };
        }
    }

    // 反馈
    BusinessResponse::ok(true).to_json_result()
}

// // 学生端获取被分配的全部训练计划
// #[get("/api/web/train/trainplan/list")]
// pub async fn get_trainplan_list_all(
//     session: Session,
//     http_request: HttpRequest,
//     app_state: web::Data<AppState>
// ) -> Result<HttpResponse, BusinessError> {
//     let start_time = Utc::now();

//     // 加载Session获取用户ID
//     let user_id = match session.get::<i32>("user_id") {
//         Ok(r) => r,
//         Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
//     };

//     if user_id.is_none() {
//         return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
//     }

//     let user_id = user_id.unwrap();

//     let conn = &app_state.conn;

//     // 开启事务
//     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
//     };

//     // 读取账号是否存在
//     let user_record = match Query::user_find_by_id(&txn, user_id).await  {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 用户不存在
//     if user_record.is_none() {
//         txn.rollback().await.unwrap();
//         return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
//     }

//     let user_record = user_record.unwrap();

//     // 获取用户被分配的训练计划和试卷
//     let user_plan_train_list = match Query::get_trainplan_train_list_by_user(&txn, user_id).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查询用户分配训练计划列表失败 {}", e.to_string()).to_string() })
//         }
//     };

//     let mut plan_ids: HashSet<i32> = HashSet::new();
//     let mut train_ids: HashSet<i32> = HashSet::new();

//     let mut plan_map: HashMap<i32, Vec<i32>> = HashMap::new();

//     for train_plan_record in &user_plan_train_list {
//         let plan_id = train_plan_record["plan_id"].as_i64().unwrap() as i32;
//         let train_id = train_plan_record["train_id"].as_i64().unwrap() as i32;

//         plan_map.entry(plan_id).or_insert(Vec::new()).push(train_id);

//         plan_ids.insert(plan_id);
//         train_ids.insert(train_id);
//     }

//     // 获取训练计划列表
//     let plan_ids_vec: Vec<i32> = plan_ids.into_iter().collect();
//     let train_plan_list = match Query::get_train_plan_by_ids_json(&txn, plan_ids_vec).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查询用户训练计划列表失败 {}", e.to_string()).to_string() })
//         }
//     };

//     let train_ids_vec: Vec<i32> = train_ids.into_iter().collect();
//     let train_list = match Query::get_train_by_ids_json(&txn, train_ids_vec).await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("查询用户试卷列表失败 {}", e.to_string()).to_string() })
//         }
//     };

//     // 自系统配置表读取当前训练计划ID
//     let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
//         Ok(r) => r,
//         Err(e) => {
//             txn.rollback().await.unwrap();
//             return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()).to_string() });
//         }
//     };

//     let current_train_plan_id = current_train_plan_id.unwrap_or("0".to_string());

//     // 结束事务
//     match txn.commit().await {
//         Ok(r) => r,
//         Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
//     };

//     let mut response: serde_json::Map<String, Value> = serde_json::Map::new();
//     response.insert(String::from("user"), serde_json::to_value(user_record).unwrap());
//     response.insert(String::from("plan_map"), serde_json::to_value(plan_map).unwrap());
//     response.insert(String::from("train_plan_list"), serde_json::to_value(train_plan_list).unwrap());
//     response.insert(String::from("train_list"), serde_json::to_value(train_list).unwrap());
//     response.insert(String::from("current_train_plan_id"), serde_json::to_value(current_train_plan_id).unwrap());

//     let connection_info = http_request.connection_info();
//     let client_ip = connection_info.peer_addr().unwrap();
//     log_request(client_ip, "/api/web/train/download/mp4", user_id, start_time);

//     BusinessResponse::ok(response).to_json_result()
// }



// extern crate toml;

// 教师机获取配置内容
#[derive(Deserialize,Serialize)]
pub struct Config {
    pub server: String,
    pub listen_port: String
}
#[get("/api/web/config")]
pub async fn get_config() -> Result<HttpResponse, BusinessError> {
    // 获取配置

    // let course_path = get_path_in_exe_dir("");
    // let config_path =  course_path.join("config").join("student.toml");
    // let config_path = config_path.to_str().unwrap();
    // //解析配置相关的toml文件
    // let file_content = fs::read_to_string(config_path).expect("加载toml文件失败");
    // let stu_config:Config = toml::from_str(&file_content).unwrap();
    let port = match env::var("PORT") {
        Ok(r) => r,
        Err(e) => {
            println!("请在server.env中配置服务器监听端口号PORT字段");
            return  Err(BusinessError::InternalError { reason: format!("请在teacher.env中配置服务器监听端口号PORT字段 {}", e.to_string()).to_string() });
        }
    };

    let teacher_server_url = match env::var("TEACHER_SERVER_URL") {
        Ok(r) => r,
        Err(e) => {
            println!("请在server.env中配置教师端服务URL字段");
            return  Err(BusinessError::InternalError { reason: format!("请在server.env中配置教师端服务URL字段 {}", e.to_string()).to_string() });
        }
    };

    let stu_config = Config {
        server: teacher_server_url,
        listen_port: port
    };


    BusinessResponse::ok(stu_config).to_json_result()
}