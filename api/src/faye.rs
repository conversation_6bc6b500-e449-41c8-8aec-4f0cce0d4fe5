use futures_util::{SinkExt, Stream, StreamExt};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::pin::Pin;
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio_tungstenite::connect_async;
use tokio_tungstenite::tungstenite::Message;

#[derive(Serialize, Deserialize, Debug, Clone, Default)]
pub struct FayeMessage {
    pub channel: String,
    #[serde(rename = "clientId", skip_serializing_if = "Option::is_none")]
    pub client_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub subscription: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<serde_json::Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub advice: Option<Advice>,
    #[serde(flatten)]
    pub extra: HashMap<String, serde_json::Value>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Advice {
    #[serde(rename = "timeout")]
    pub timeout: Option<u64>,
    #[serde(rename = "interval")]
    pub interval: Option<u64>,
    #[serde(rename = "reconnect")]
    pub reconnect: Option<String>,
}

pub struct FayeServer {
    writer: futures_util::stream::SplitSink<tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>, Message>,
    client_id: String,
    reader: Option<FayeMessageStream>,
    subscribers: HashMap<String, UnboundedSender<FayeMessage>>,
}

pub type FayeMessageStream = Pin<Box<dyn Stream<Item = Result<FayeMessage, serde_json::Error>> + Send>>;

pub async fn connect_faye(
    faye_url: &str,
) -> Result<(futures_util::stream::SplitSink<tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>, Message>, FayeMessageStream), Box<dyn std::error::Error>> {
    let (ws_stream, response) = connect_async(faye_url).await?;
    println!("response: {:?}", response);
    let (ws_writer, ws_reader) = ws_stream.split();

    // Create a stream of Faye messages
    let message_stream = Box::pin(
        ws_reader
            .filter_map(|result| async move {
                match result {
                    Ok(Message::Text(text)) => {
                        match serde_json::from_str::<Vec<FayeMessage>>(&text) {
                            Ok(messages) => {
                                // Flatten array into stream of individual messages
                                let mut msgs = messages.into_iter().map(Ok);
                                msgs.next() // or use stream::iter
                            }
                            Err(e) => Some(Err(e)),
                        }
                    }
                    _ => None,
                }
            })
    );

    Ok((ws_writer, message_stream))
}

pub enum FayeMessageRequest {
    Handshake,
    Connect {
        id: String,
    },
    Subscribe {
        channel: String,
        tx: UnboundedSender<FayeMessage>,
    },
    Publish {
        channel: String,
        data: serde_json::Value,
    },
    Disconnect,
}

pub struct FayeClient {
    tx: UnboundedSender<FayeMessageRequest>,
    rx: UnboundedReceiver<FayeMessage>,
    client_id: String,
}

impl FayeServer {
    pub async fn bind(url: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let (mut writer, mut reader) = connect_faye(url).await?;
        let handshake_id = uuid::Uuid::new_v4().to_string();

        let handshake = serde_json::json!([{
            "channel": "/meta/handshake",
            "version": "1.0",
            "supportedConnectionTypes": ["websocket"],
            "id": handshake_id
        }]);
        writer
            .send(Message::Text(handshake.to_string().into()))
            .await?;

        // Wait for handshake response
        let client_id = loop {
            if let Some(Ok(msg)) = reader.next().await {
                println!("msg = {:?}", msg);
                if msg.channel == "/meta/handshake" && msg.client_id.is_some() {
                    println!("msg.client_id = {:?}", msg.client_id);
                    break msg.client_id.unwrap();
                }
            }
        };
        println!("✅ Handshake OK, client_id: {}", client_id);

        let connect = serde_json::json!([{
            "channel": "/meta/connect",
            "version": "1.0",
            "clientId": &client_id,
        }]);
        writer
            .send(Message::Text(connect.to_string().into()))
            .await?;

        println!("✅ Connect OK, client_id: {}", client_id);

        Ok(Self {
            writer,
            client_id: client_id.clone(),
            reader: Some(reader),
            subscribers: Default::default(),
        })

    }

    pub fn client_id(&self) -> &str {
        self.client_id.as_str()
    }

    pub async fn send(&mut self, text: impl Into<String>) -> Result<(), Box<dyn std::error::Error>> {
        self.writer
            .send(Message::Text(text.into().into()))
            .await?;

        Ok(())
    }

    pub async fn heartbeat(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let connect_msg = serde_json::json!([{
            "channel": "/meta/connect",
            "clientId": self.client_id.as_str(),
            "connectionType": "websocket",
            "id": uuid::Uuid::new_v4().to_string()
        }]);

        self.writer
            .send(Message::Text(connect_msg.to_string().into()))
            .await?;

        Ok(())
    }

    pub async fn connect(&mut self, id: String) -> Result<(), Box<dyn std::error::Error>> {
        let json_msg = serde_json::json!([{
            "channel": "/meta/connect",
            "clientId": self.client_id.as_str(),
            "connectionType": "websocket",
            "id": id
        }]);

        self
            .writer
            .send(Message::Text(json_msg.to_string().into()))
            .await?;

        Ok(())
    }

    pub async fn subscribe(&mut self, subscription: String, tx: UnboundedSender<FayeMessage>) -> Result<(), Box<dyn std::error::Error>> {
        let json_msg = serde_json::json!([{
            "channel": "/meta/subscribe",
            "clientId": self.client_id,
            "subscription": subscription
        }]);

        self
            .writer
            .send(Message::Text(json_msg.to_string().into()))
            .await?;
        self.subscribers.insert(subscription, tx);

        Ok(())
    }

    pub async fn unsubscribe(&mut self, subscription: String) -> Result<(), Box<dyn std::error::Error>> {
        let json_msg = serde_json::json!([{
            "channel": "/meta/unsubscribe",
            "clientId": self.client_id,
            "subscription": subscription
        }]);

        self
            .writer
            .send(Message::Text(json_msg.to_string().into()))
            .await?;

        Ok(())
    }

    pub async fn publish(&mut self, channel: String, data: serde_json::Value) -> Result<(), Box<dyn std::error::Error>> {
        let json_msg = serde_json::json!([{
            "channel": channel.as_str(),
            "data": data
        }]);

        self
            .writer
            .send(Message::Text(json_msg.to_string().into()))
            .await?;

        Ok(())
    }

    pub async fn listen(&mut self, mut rx: UnboundedReceiver<FayeMessageRequest>, tx: UnboundedSender<FayeMessage>) -> Result<FayeMessage, Box<dyn std::error::Error>> {
        let Some(mut reader) = self.reader.take() else {
            return Err("reader is null".to_string().into());
        };

        // let mut interval = tokio::time::interval(std::time::Duration::from_secs(1));

        loop {
            tokio::select! {
                // _ = interval.tick() => self.heartbeat().await?,

                Some(msg) = rx.recv() => {
                    match msg {
                        FayeMessageRequest::Handshake => {}
                        FayeMessageRequest::Connect { id} => self.connect(id).await?,
                        FayeMessageRequest::Subscribe { channel, tx } => self.subscribe(channel, tx).await?,
                        FayeMessageRequest::Publish { channel, data } => self.publish(channel, data).await?,
                        FayeMessageRequest::Disconnect => {
                            println!("stop server...");
                            // tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                            let _ = self.unsubscribe(format!("/client/{}", self.client_id)).await;
                            if let Err(e) = self.writer.close().await {
                                log::warn!("stop server: close writer: {e}");
                            }
                            break Ok(FayeMessage::default());
                        }
                    }
                },

                Some(Ok(msg)) = reader.next() => {
                    // 处理不同类型的 channel
                    match msg.channel.as_str() {
                        "/meta/handshake" => {
                            println!("⚠️ Unexpected handshake message (maybe duplicate)");
                        }
                        "/meta/subscribe" => {
                            if let Some(sub) = &msg.subscription {
                                println!("✅ Successfully subscribed to: {}", sub);
                            }
                        }
                        "/meta/connect" => {
                            println!("🔌 Connect acknowledged");
                        }
                        // 业务频道（非 /meta/ 开头）
                        channel if !channel.starts_with("/meta/") => {
                            println!("💬 Business message on {}: {:?}", channel, msg.data);
                            // 这里才是你想要的业务消息！
                            // break; // 如果只是测试收到一条就退出
                            if let Some(subscriber) = self.subscribers.get_mut(channel) {
                                if let Err(e) = subscriber.send(msg.clone()) {
                                    log::warn!("Failed to send {}", e);
                                }
                            }
                        }
                        _ => {
                            println!("❓ Unknown meta channel: {}", msg.channel);
                        }
                    }
                    // tx.send(msg.clone()).unwrap();
                }
            }
        }
    }
}


pub enum Error {
    Disconnect(String),
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_connect_faye() {
        // let (mut client, mut stream) = connect_faye("wss://hxr.iyopu.com/s/csxx/faye").await.unwrap();
        let mut server = FayeServer::bind("ws://localhost:8000/faye").await.unwrap();
        println!("connected...");

        let (tx1, rx1) = tokio::sync::mpsc::unbounded_channel();
        let (tx2, rx2) = tokio::sync::mpsc::unbounded_channel();
        tokio::spawn(async move {
            let _ = server.listen(rx1, tx2).await;
        });

        // Step 2: Send /meta/connect
        tx1.send(FayeMessageRequest::Connect {
            id: uuid::Uuid::new_v4().to_string(),
        }).unwrap();

        let (tx3, mut rx3) = tokio::sync::mpsc::unbounded_channel();
        tx1.send(FayeMessageRequest::Subscribe {
            channel: "/test".to_string(),
            tx: tx3,
        }).unwrap();

        println!("📤 Subscribed to /test");

        let tx11 = tx1.clone();
        tokio::spawn(async move {
            tokio::time::sleep(std::time::Duration::from_secs(5)).await;
            tx11.send(FayeMessageRequest::Publish {
                channel: "/test".to_string(),
                data: serde_json::json!({
                    "message": "Hello, world!!!!!!!!!!!!!!!!",
                })
            }).unwrap();

            tokio::time::sleep(std::time::Duration::from_secs(5)).await;
            tx11.send(FayeMessageRequest::Publish {
                channel: "/test".to_string(),
                data: serde_json::json!({
                    "message": "break",
                })
            }).unwrap();
        });

        // === Step 4: Now listen for messages (with timeout) ===
        // === 持续监听消息（关键！）===

        while let Some(msg) = rx3.recv().await {
            println!("receive subscription message: {:?}", msg);
            if let Some(val) = msg.data {
                if val["message"] == serde_json::Value::String("break".to_string()) {
                    break;
                }
            }
        }

        // assert!(connect_received, "Did not receive /meta/connect response");
        // assert!(false);
    }
}