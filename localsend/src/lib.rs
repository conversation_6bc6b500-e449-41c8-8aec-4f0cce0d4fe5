pub mod protos;
pub mod utils;
pub mod server;

const BUFFER_SIZE: u16 = 30 * 1024;
const FILE_MESSAGE_BUFFER_SIZE: usize = 64 * 1024;

pub const NUM_REPEAT: u8 = 2;

const DEVICE_MODEL: &str = "win";
const DEVICE_TYPE: &str = "desktop";

const MAX_PACKET_SIZE: usize = 60 * 1024; // 60KB
const CHUNK_SIZE: usize = 1 * 1024 * 1024; // 1MB
const HASH_SIZE: usize = 32; // SHA-256 hash size in bytes
const MAX_RETRANSMIT: i32 = 1; //重传次数

// 数据请与学生端保持同步
