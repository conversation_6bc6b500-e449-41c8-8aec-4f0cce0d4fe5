use std::{clone, collections::HashMap, default, sync::Arc, time::{Instant, SystemTime}};

use serde::{Deserialize, Serialize};
use tokio::sync::{
    mpsc::{UnboundedReceiver, UnboundedSender},
    Mutex
};
use std::collections::HashSet;
use std::net::{UdpSocket, SocketAddr};
use rand::{self, rngs::ThreadRng, Rng};
use std::thread;
use actix::prelude::*;
// use std::sync::{Arc, Mutex};
// 定义接收状态的类型别名
pub type ReceiveState = Arc<Mutex<AppState>>;
// 定义发送者和接收者的类型别名
pub type Sender<T> = UnboundedSender<T>;
pub type Receiver<T> = UnboundedReceiver<T>;

// 定义文件类型的枚举
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum FileType {
    Image,
    Video,
    Pdf,
    Text,
    Other,
}

// 定义接收状态的枚举
#[derive(Clone, PartialEq, Debug)]
pub enum ReceiveStatus {
    Waiting,            // 等待发送者发送文件
    Receiving,          // 正在接收文件
    Finished,           // 所有文件接收完成
    FinishedWithErrors, // 接收完成但有些文件未能接收
}

// 定义客户端消息的枚举
#[derive(Clone, Debug)]
pub enum ClientMessage {
    Allow(Vec<String>), // 允许接收文件
    Decline,            // 拒绝接收文件
}

// 定义服务器消息的枚举
#[derive(Clone, Debug)]
pub enum ServerMessage {
    SendRequest(SendRequest),          // 发送请求
    SendFileRequest((String, usize)),  // 发送文件请求
    CancelSession,                     // 取消会话
}

// 定义设备信息的结构体
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviceInfo {
    pub alias: String,                // 设备别名
    pub device_type: String,          // 设备类型
    pub device_model: Option<String>, // 设备型号
    #[serde(skip)]
    pub ip: String,                   // 设备IP地址
    #[serde(skip)]
    pub port: u16,                    // 设备端口号
}

// 实现 DeviceInfo 的 PartialEq trait
impl PartialEq for DeviceInfo {
    fn eq(&self, other: &Self) -> bool {
        self.ip == other.ip
    }
}

// 实现 DeviceInfo 的 Default trait
impl Default for DeviceInfo {
    fn default() -> Self {
        Self {
            alias: "".into(),
            device_type: "".into(),
            device_model: None,
            ip: "".into(),
            port: 0,
        }
    }
}

// 定义设备响应的结构体
#[derive(Clone, Debug, Serialize, Deserialize)]
#[derive(Default)]
pub struct DeviceResponse {
    #[serde(flatten)]
    pub device_info: DeviceInfo, // 设备信息
    pub announcement: bool,      // 是否为公告
    pub fingerprint: String,     // 设备指纹
    pub message: Option<String>,
}

// 实现 DeviceInfo 到 DeviceResponse 的转换
impl From<DeviceInfo> for DeviceResponse {
    fn from(device: DeviceInfo) -> Self {
        Self {
            device_info: device,
            fingerprint: "".into(),
            announcement: false,
            message: None,
        }
    }
}

// 实现 DeviceResponse 的 PartialEq trait
impl PartialEq for DeviceResponse {
    fn eq(&self, other: &Self) -> bool {
        self.fingerprint == other.fingerprint
    }
}

// 定义文件信息的结构体
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct FileInfo {
    pub id: String,          // 文件ID
    pub size: usize,         // 文件大小（字节）
    pub file_name: String,   // 文件名
    pub file_type: FileType, // 文件类型
}

// 定义发送请求的结构体
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SendRequest {
    #[serde(rename = "info")]
    pub device_info: DeviceInfo, // 设备信息
    pub files: HashMap<String, FileInfo>, // 文件列表
}

// 定义发送信息的结构体
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SendInfo {
    pub file_id: String, // 文件ID
    pub token: String,   // 令牌
}

// 定义接收会话的结构体
#[derive(Clone)]
pub struct ReceiveSession {
    pub sender: DeviceInfo,                     // 发送者信息
    pub files: HashMap<String, FileInfo>,       // 文件列表
    pub file_status: HashMap<String, ReceiveStatus>, // 文件状态
    pub destination_directory: String,          // 目标目录
    pub start_time: Instant,                    // 会话开始时间
    pub status: ReceiveStatus,                  // 会话状态
}

// 实现 ReceiveSession 的构造函数
impl ReceiveSession {
    pub fn new(sender: DeviceInfo, destination_directory: String) -> Self {
        Self {
            sender,
            destination_directory,
            files: HashMap::new(),
            file_status: HashMap::new(),
            start_time: Instant::now(),
            status: ReceiveStatus::Waiting,
        }
    }
}

// 定义应用状态的结构体
pub struct AppState {
    pub(crate) server_tx: Sender<ServerMessage>, // 服务器消息发送者
    pub(crate) client_rx: Receiver<ClientMessage>, // 客户端消息接收者
    pub(crate) receive_session: Option<ReceiveSession>, // 接收会话
}




// 定义消息类型并实现 Message trait
#[derive(Debug)]
pub struct Message(pub String);

impl actix::Message for Message {
    type Result = ();
}

#[derive(Debug)]
pub struct Connect {
    pub ip_addr: SocketAddr,
    pub room: String,
}

impl actix::Message for Connect {
    type Result = usize;
}

#[derive(Debug)]
pub struct Disconnect {
    pub ip_addr: SocketAddr,
    pub room: String,
    pub id: usize,
}

impl actix::Message for Disconnect {
    type Result = ();
}

#[derive(Debug)]
pub struct UDPClientMessage {
    pub id: usize,
    pub msg: String,
    pub room: String,
}

impl actix::Message for UDPClientMessage {
    type Result = ();
}

#[derive(Debug)]
pub struct ListOnlineIPs;

impl actix::Message for ListOnlineIPs {
    type Result = HashSet<SocketAddr>;
}

#[derive(Debug)]
pub struct ListOnlineSessionCount;

impl actix::Message for ListOnlineSessionCount {
    type Result = usize;
}

#[derive(Debug)]
pub struct Join {
    pub id: usize,
    pub name: String,
}

impl actix::Message for Join {
    type Result = ();
}

// 定义发送信息的结构体
#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UDPMessageInfo {
    pub alias: String,                // 设备别名
    pub device_type: String,          // 设备类型
    pub device_model: Option<String>, // 设备型号
    #[serde(skip)]
    pub ip: String,                   // 设备IP地址
    #[serde(skip)]
    pub port: u16,                    // 设备端口号
    pub last_refresh_time: SystemTime,      // 上次刷新时间
}
// 实现 UDPMessageInfo 的 Default trait
impl Default for UDPMessageInfo {
    fn default() -> Self {
        Self {
            alias: "".into(),
            device_type: "".into(),
            device_model: None,
            ip: "".into(),
            port: 0,
            last_refresh_time: SystemTime::now(),
        }
    }
}
// 实现 UDPMessageInfo 的 PartialEq trait
impl PartialEq for UDPMessageInfo {
    fn eq(&self, other: &Self) -> bool {
        self.ip == other.ip
    }
}

#[derive(Debug,Default,Deserialize,Clone,Serialize,PartialEq)]
pub enum UDPMessageType {
    Public,         // 所有学生接收文件
    Private,        // 仅限对该文件请求过的学生接收文件
    Announcement,    // 公告，广播设备信息
    Sending,
    FileEnd,            // 文件发送完成
    StartClass,       // 开始上课
    Setting,           // 修改配置
    StopClass,          // 停止上课
    #[default]
    Stop
}
#[derive(Debug,Deserialize,Serialize,Clone)]
pub struct Task {
    pub course_slug: String,
    pub first_request_time: SystemTime,
    pub download_count: u32,
    pub file_size: u64,
    pub file_id: [u8; 32],
    pub part_ids: Vec<u16>,
    pub reset: bool,
}

impl Task {
    pub fn new(course_slug: String, file_size: u64, file_hash:&[u8], download_count: u32 ) -> Self {
        let mut file_hash_array = [0u8; 32];
        let part_ids = Vec::new();
        file_hash_array.copy_from_slice(file_hash);
        Task {
            course_slug,
            first_request_time: SystemTime::now(),
            download_count,
            file_size,
            file_id: file_hash_array, // 文件的sha256哈希值
            part_ids,
            reset: false
        }
    }
}
#[derive(Debug,Default,Deserialize,Clone,Serialize,PartialEq)]
pub enum TaskState {
    Exist,          // 已存在该任务
    Create,        // 创建了新任务   
    #[default]
    Failed,            // 公告，广播设备信息
}

// 定义设备响应的结构体
#[derive(Clone, Debug, Serialize, Deserialize,PartialEq)]
#[derive(Default)]
pub struct UDPResponse {
    #[serde(flatten)]
    pub udp_message_info: UDPMessageInfo, // 设备信息
    pub message_type: UDPMessageType,      // 是否为公告
    pub fingerprint: String,     // 设备指纹
    pub message: Option<String>,
    pub file_id:Vec<u8>,
    pub file_is_end:bool,
    #[serde(default)]
    pub file_size:u64,

}
#[derive(Clone, Debug, Serialize, Deserialize,PartialEq)]
pub struct FileTransmitPacket {
    pub header: [u8; 1],
    pub data: Vec<u8>,
}