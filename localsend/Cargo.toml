[package]
name = "localsend"
version = "0.1.0"
edition = "2021"
publish = false

[lib]
name = "localsend"
path = "src/lib.rs"


[dependencies]
rcgen = "0.11"
tracing = "0.1"
network-interface = "1.0"
uuid = { version = "1.3", features = ["v4"] }

serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
log = "0.4"
log4rs = "1.3.0"


tokio = { version = "1.27", features = ["rt-multi-thread"] }
tokio-util = { version = "0.7", features = ["io"] }
futures = "0.3"
rand = "0.8"
actix = "0.13.0"
ring = "0.17.8"
sha1 = "0.10.6"
service = { path = "../service" }
migration = { path = "../migration" }
websocket = { path = "../websocket"}



# axum-macros = "0.3"
# axum = { version = "0.6", features = ["query"] }
# axum-server = { path = "../axum-server", package = "axum-server", features = [
#     "tls-rustls",
# ] } # reqwest 0.11 需要 tokio 作为异步运行时