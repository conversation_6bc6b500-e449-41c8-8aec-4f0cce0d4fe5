const Queue = require('bull');
const Faye = require('faye');

// 增加Warning调试代码
process.on('warning', e => {
  console.warn('!!!Warning:');
  console.warn(e.stack);
});

let client = null;


module.exports = app => {
  const redis = app.redis.get('session');

  function getClient() {
    if (!client) {
      client = new Faye.Client(app.config.fayeServerURL);
    }
  
    return client;
  }

  // 初始化会话存储
  app.sessionStore = {
    async get(key) {
      const res = await redis.get(key);
      if (!res) return null;
      return JSON.parse(res);
    },

    async set(key, value, maxAge) {
      if (value.user && value.user.id) {
        const userID = value.user.id;
        const schoolSlug = value.user.schoolSlug;

        // 用户多设备登录限制
        const userSessionIDsKey = `${schoolSlug}_${userID}_SESSIONID`;
        await redis.set(userSessionIDsKey, key, 'PX', maxAge);

        // 课程授权限制
        const loginKey = `${schoolSlug}_login_user_ids`;
        const sessionID = await redis.get(userSessionIDsKey)
        
        // 记录用户id对应的全部sessionid
        const allSessionIDs = [...new Set([sessionID, key])].join(',');
        await redis.hset(loginKey, userID, allSessionIDs);
      }

      value = JSON.stringify(value);
      await redis.set(key, value, 'PX', maxAge);
    },
    
    async destroy(key) {
      await redis.del(key);
    },

    // 设置过期时间
    async expire(key, time) {
      await redis.expire(key, time);
    },

    // 是否存在
    async exists(key) {

      return await redis.exists(key);
      
    },
    
  };

  // 初始化队列
  const queueConfigs = app.config.queue;
  app.queue = {};
  for(const queueConfig of queueConfigs) {
    const { name, ...redisConfig } = queueConfig;
    const queue = new Queue(name, {
      redis: { ...redisConfig},
      settings: {
        lockDuration: 60 * 1000,
        lockRenewTime: 30 * 1000,
        removeOnComplete: true, // 完成后删除,
        stalledInterval: 0,
      }
    })
    app.queue[name] = queue;
    console.log(`queue ${name} created by ${JSON.stringify(redisConfig)}`);
  }

  app.client = getClient();

  // 初始化中间件
  app.config.appMiddleware.push('interfaceMap');
  app.config.appMiddleware.push('range');
  // app.config.appMiddleware.push('portCache');
  app.config.appMiddleware.push('schoolModel');

  app.reStart = true;
  console.log('app 启动')

  /* app.beforeStart(async function () {
    // 保证应用启动监听端口前数据已经准备好了
    // 后续数据的更新由定时任务自动触发
    await app.runSchedule('update_pool');
  });*/
}