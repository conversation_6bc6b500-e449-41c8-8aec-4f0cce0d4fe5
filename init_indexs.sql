CREATE INDEX system_config_find_by_keys ON system_config (
    "key",
    deleted_at
);

CREATE INDEX user_find_by_username ON user (
    username,
    state,
    deleted_at
);

CREATE INDEX user_find_by_id ON user (
    id,
    state,
    deleted_at
);

CREATE INDEX team_find_by_schoolyear_team ON team (
    year,
    deleted_at
);

CREATE INDEX team_find_by_schoolyear_team_user ON team_user (
    team_id,
    deleted_at
);

CREATE INDEX users_find_by_team_id ON user (
    id,
    deleted_at
);

CREATE INDEX team_user_find_by_user_ids_and_not_in_team_id ON team_user (
    team_id,
    user_id,
    deleted_at
);

CREATE INDEX train_find_by_school_year_and_teacher_user_id ON train (
    year,
    create_user_id,
    deleted_at
);

CREATE INDEX train_find_by_school_year_and_teacher_user_id2 ON train (
    year,
    create_user_id,
    teachers,
    deleted_at
);

CREATE INDEX train_plan_find_by_id_json ON train_plan (
    id,
    deleted_at
);

CREATE INDEX train_plan_find_by_id_for_upload_json ON train_plan (
    id,
    end_time,
    uploaded_at,
    remote_id,
    deleted_at
);

CREATE INDEX train_by_train_plan_id_and_user_id_json ON train_through_train_plan (
    user_id,
    plan_id,
    deleted_at
);

CREATE INDEX train_by_train_plan_id_and_user_id_json2 ON train (
    id,
    deleted_at
);

CREATE INDEX train_user_record_find_json ON train_user_record (
    user_id,
    train_id,
    plan_id,
    deleted_at
);

CREATE INDEX train_plan_is_user_available_for_train_id ON train_through_train_plan (
    user_id,
    train_id,
    plan_id,
    deleted_at
);

CREATE INDEX train_user_record_by_train_plan_id ON train_user_record (
    user_id,
    plan_id,
    deleted_at
);

CREATE INDEX team_user_by_user_ids ON team_user (
    user_id,
    deleted_at
);

CREATE INDEX team_by_team_ids ON team (
    id,
    deleted_at
);

CREATE INDEX user_delete_by_id ON user (
    id,
    admin_authority
);

CREATE INDEX train_user_record_update_status_by_plan_id ON train_user_record (
    plan_id,
    deleted_at
);
