'use strict';

const { env } = process;
const { 
  MYJUPYTER_IMAGE = "registry-vpc.cn-hangzhou.aliyuncs.com/haixr/myjupyter",
  HXR_DEPLOYMEMT = 'hxr.iyopu.com',
  HXR_MAIN_STATION_PROTOCOL = 'https',
  MYSQL_SERVER_HOST = 'rm-bp1c38hq4h4s418xy.mysql.rds.aliyuncs.com',
} = env;

module.exports = appInfo => {
  const config = {};

  // 强制打印
  config.logger = {
    disableConsoleAfterReady: false,
    consoleLevel: 'DEBUG',
    level: 'DEBUG',
    allowDebugAtProd: true,
  };

  // 文件上传
  config.file = {
    hashFile: '/data/shared_files', // 散列文件上传（统一存放，不对外）
    trainFile: '/data/train',
    dir: '/data/school',
    url: 'file',
    tmpDir: '/data/tmp',
    tmpUrl: '/file/tmp',
    eliteCoursesDir: '/data/elite_courses'
  };

  // 大文件上传
  config.yuploader = {
    dir: '/data/file', // 存储路径，这里是本地调试路径
    chunkSize: 10 * 1024 * 1024, // 分块大小，必须与前端配置完全一致
    // 允许上传的文件格式类型
    fileExtensions: ['.xls', '.xlsx', '.rm', '.rmvb', '.mov', '.mtv', '.wmv', '.avi', '.3gp', '.amv', '.dmv', '.otf', '.flv', '.block', '.xml', '.csv', '.asf', '.asf', '.mpg', '.ra', '.qt', '.ipynb', '.pdf', '.in', '.out', '.jpg', '.png', '.gif', '.wmf', '.webp', '.tif', '.bmp', '.ogg', '.webm', '.c', '.cpp', '.py', '.txt', '.docx', '.dat', '.wav', '.mp4', '.css', '.gz', '.pkt', '.db', '.accdb', '.m4v', '.sb3', '.slide', '.code', '.onnx', '.zip'],
  };

  // 数据库配置
  config.sequelize = {
    dialect: 'mysql', // support: mysql, mariadb, postgres, mssql
    dialectOptions: {
      // 对于 mysql2 或 mariadb 的连接参数
      charset: 'utf8mb4'
    },
    baseDir: 'model/main',
    delegate: 'mainModel', // 引用时使用mainModel引用
    database: 'hxr',
    // 部署于阿里云的hxr.iyopu.com用RDS地址，否则用之前的PolarDB地址
    // host: HXR_DEPLOYMEMT === 'hxr.iyopu.com' ? 'rm-bp16wzgz88o361pwp.mysql.rds.aliyuncs.com' : 'pc-bp1p9gs7183455812.rwlb.rds.aliyuncs.com',
    host: MYSQL_SERVER_HOST,
    port: '3306',
    username: 'hxr',
    password: '1NKqvFxfdQrqDIbd',
    timezone: '+08:00',
    pool: {
      max: 1000,
      min: 100,
      acquire: 10 * 1000, // 5秒钟获取时间
    },
    retry: {
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /ETIMEDOUT/,
        /ESOCKETTIMEDOUT/,
        /EHOSTUNREACH/,
        /EPIPE/,
        /EAI_AGAIN/,
        /SequelizeConnectionError/,
        /SequelizeConnectionRefusedError/,
        /SequelizeHostNotFoundError/,
        /SequelizeHostNotReachableError/,
        /SequelizeInvalidConnectionError/,
        /SequelizeConnectionTimedOutError/,
        /OperationTimeout/
      ],
      max: 3
    }
  };

  const redisServerConfig = {
    // 部署于阿里云的hxr.iyopu.com用私有服务地址，否则用之前的阿里云Redis缓存地址
    host: HXR_DEPLOYMEMT === 'hxr.iyopu.com' ? 'hxr-redis.iyopu.com' : 'r-bp17ikznl37qzsyyyk.redis.rds.aliyuncs.com',
    port: '6379',
    password: '@8dacaixi',
  };

  // 增加Redis数据库连接，!!! 必须要注意不能和后续消息队列以及实时通讯db冲突 !!!
  config.redis = {
    clients: {
      // 会话
      session: {
        ...redisServerConfig,
        db: 0
      },

      // 缓存
      cache: {
        ...redisServerConfig,
        db: 1
      },

      // faye资源
      faye: {
        ...redisServerConfig,
        fayeHost: 'faye',
        db: 2
      },

      // 交互式课程的容器
      container: {
        ...redisServerConfig,
        db: 3
      },

      // 交互式课程的Kernel
      kernel: {
        ...redisServerConfig,
        db: 4
      },

      // 异步任务
      asyncTask: {
        ...redisServerConfig,
        db: 12
      },

      // OJ判决学生记录
      judge: {
        ...redisServerConfig,
        db: 10
      },
      train: {
        ...redisServerConfig,
        db: 14
      }
    },
  };

  // 消息队列，基于Redis !!! 必须要注意不能和前面Redis和SocketIO db冲突 !!!
  config.queue = [
    // {
    //   name: 'icourse-resource',
    //   ...redisServerConfig,
    //   db: 7,
    // },
    {
      name: 'runner',
      ...redisServerConfig,
      db: 8,
    },
    {
      name: 'judger',
      ...redisServerConfig,
      db: 9,
    },
    {
      name: 'oj-task',
      ...redisServerConfig,
      db: 11,
    },
    {
      name: 'oi-task',
      ...redisServerConfig,
      db: 13,
    },
    {
      name: 'course-task',
      ...redisServerConfig,
      db: 12,
    },
    {
      name: 'train-task',
      ...redisServerConfig,
      db: 12,
    },
  ];

  config.jupyter = {
    url: '',
    image: MYJUPYTER_IMAGE,
    cpuRequest: 2,
    memoryRequest: '8Gi',
    kernelLimit: 50
  };

  config.session = {
    secure: false,
    maxAge: 24 * 3600 * 1000, // 24小时
    renew: true,
    encrypt: false
  };

  config.sessionRedis = {
    name: 'session', // specific `session` as the session store
    secure: false,
  };

  config.sso = {
    'hxr.jsnje.cn': {
      name:'hxr.jsnje.cn',
      path: 'https://sso.nje.cn',
      AppCode: 'RNYWJXPT00003212',
      AppKey: '04140ecb-a971-4180-8d75-1e5b630421a1',

      DEFAULTCODECOUNT: 300,
    },
    'ai.ksedu.cn': {
      name:'ai.ksedu.cn',
      path: 'https://sso.ksedu.cn',
      AppCode: 'RGZNJXPT0003214511',
      AppKey: '6411d3a2-6a92-42a7-b309-ff827e10c754',

      AccountId: 'B17F8F89B68741E19ECCBC2E915E6AA4',
      AccountPassword: 'BECD548DD9A547FC952ED2D3F908865F',
      apiPath: 'https://api.ksedu.cn/edu/openapi/index',

      teacherPassword: '053f8a5d46c1cb8954cc53caab4cb1f02f9ad386',
      studentPassword: '7c4a8d09ca3762af61e59520943dc26494f8941b',
    },
    'hxr.i51cy.com': {
      name:'hxr.i51cy.com',
      path: 'https://sso.ksedu.cn',
      AppCode: 'RGZNJXPT0003214511',
      AppKey: '6411d3a2-6a92-42a7-b309-ff827e10c754',

      AccountId: 'B17F8F89B68741E19ECCBC2E915E6AA4',
      AccountPassword: 'BECD548DD9A547FC952ED2D3F908865F',
      apiPath: 'https://api.ksedu.cn/edu/openapi/index',

      teacherPassword: '053f8a5d46c1cb8954cc53caab4cb1f02f9ad386',
      studentPassword: '7c4a8d09ca3762af61e59520943dc26494f8941b',
    },
    'njyz': {
      name: 'njyz',
      appid: '635578760252622038723872',
      appkey: 'NMO4CpgZIAc6ZioRaClrFflfnyn3USFR',

      apiPath: 'https://zhxy.njyz.net:9840/school/v1',

      teacherLoginUrl: 'https://zhxy.njyz.net:9840/njyz/login',
      studentLoginUrl: 'https://smart.njyz.net:9854/njyz/login',

      teacherLoginPath: 'https://zhxy.njyz.net:9840/apis/sso/validToken.do',
      studentLoginPath: 'https://smart.njyz.net:9854/studentApis/sso/student/validToken.do',
    }
  }

  config.managerment = 'https://manager.hxr.iyopu.com';

  config.managermentAPI = {
    app_id: '****************',
    secret: 'hastd4g8f3h2a932',
  }

  config.platHxrMainAPI = {
    host: `${HXR_MAIN_STATION_PROTOCOL}://${HXR_DEPLOYMEMT}/api`,
    // 主站 app_id
    app_id: '9128737431120744',
    // 主站 secret
    secret: '5c5h7kg9edd2604fk4af4e1224548f80',
  }

  config.allSchoolDebug = false;

  config.thridParty = {
    2394820984575923948290: 'ja94hfao34949hfj9034uhf'
  }
  
  return config;
}