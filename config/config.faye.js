'use strict';

module.exports = appInfo => {
  const config = {};

  // 强制打印
  config.logger = {
    disableConsoleAfterReady: false,
    consoleLevel: 'DEBUG',
    level: 'DEBUG',
    allowDebugAtProd: true,
  };

  // 文件上传
  config.file = {
    hashFile: '/data/shared_files', // 散列文件上传（统一存放，不对外）
    trainFile: '/data/train',
    dir: '/data/school',
    url: 'file',
    demoDir: '/data/file',
    tmpDir: '/data/tmp',
    tmpUrl: '/file/tmp',
    eliteCoursesDir: '/data/elite_courses'
  };

  config.security = {
    csrf: {
      enable: true,
      useSession: true, // 默认为 false，当设置为 true 时，将会把 csrf token 保存到 Session 中
      sessionName: 'csrfToken', // Session 中的字段名，默认为 csrfToken
      ignore: (ctx) => {
        if(ctx.request.url.indexOf('faye') !== -1 || ctx.request.url.indexOf('thirdPart') !== -1) {
          return true
        }

        return false
      }
    },
  };

  // 大文件上传
  config.yuploader = {
    dir: '/data/file', // 存储路径，这里是本地调试路径
    chunkSize: 10 * 1024 * 1024, // 分块大小，必须与前端配置完全一致
    // 允许上传的文件格式类型
    fileExtensions: ['.xls', '.xlsx', '.rm', '.rmvb', '.mov', '.mtv', '.wmv', '.avi', '.3gp', '.amv', '.dmv', '.otf', '.flv', '.block', '.xml', '.csv', '.asf', '.asf', '.mpg', '.ra', '.qt', '.ipynb', '.pdf', '.in', '.out', '.jpg', '.png', '.gif', '.wmf', '.webp', '.tif', '.bmp', '.ogg', '.webm', '.c', '.cpp', '.py', '.txt', '.docx', '.dat', '.wav', '.mp4', '.css', '.gz', '.pkt', '.db', '.accdb', '.m4v', '.sb3', '.slide', '.code', '.onnx', '.zip'],
  };

  config.sequelize = {
    dialect: 'mysql', // support: mysql, mariadb, postgres, mssql
    dialectOptions: {
      // 对于 mysql2 或 mariadb 的连接参数
      charset: 'utf8mb4'
    },
    baseDir: 'model/main',
    delegate: 'mainModel', // 引用时使用mainModel引用
    database: 'hxr',
    host: '**************',
    port: '3306',
    username: 'hxr',
    password: '1NKqvFxfdQrqDIbd',
    timezone: '+08:00',
    pool: {
      max: 500,
      min: 50,
      acquire: 10 * 1000, // 5秒钟获取时间
    },
    retry: {
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /ETIMEDOUT/,
        /ESOCKETTIMEDOUT/,
        /EHOSTUNREACH/,
        /EPIPE/,
        /EAI_AGAIN/,
        /SequelizeConnectionError/,
        /SequelizeConnectionRefusedError/,
        /SequelizeHostNotFoundError/,
        /SequelizeHostNotReachableError/,
        /SequelizeInvalidConnectionError/,
        /SequelizeConnectionTimedOutError/,
        /OperationTimeout/
      ],
      max: Number.MAX_VALUE
    }
  };

  const redisServerConfig = {
    host: 'redis',
    port: '6379',
    password: '',
  };

  // 增加Redis数据库连接，!!! 必须要注意不能和后续消息队列以及实时通讯db冲突 !!!
  config.redis = {
    clients: {
      // 会话
      session: {
        ...redisServerConfig,
        db: 0
      },

      // 缓存
      cache: {
        ...redisServerConfig,
        db: 1
      },

      // faye资源
      faye: {
        ...redisServerConfig,
        fayeHost: 'faye',
        db: 2
      },

      // 交互式课程的容器
      container: {
        ...redisServerConfig,
        db: 3
      },

      // 交互式课程的Kernel
      kernel: {
        ...redisServerConfig,
        db: 4
      },

      // 异步任务
      asyncTask: {
        ...redisServerConfig,
        db: 12
      },

      // OJ判决学生记录
      judge: {
        ...redisServerConfig,
        db: 10
      },
      train: {
        ...redisServerConfig,
        db: 14
      }
    },
  };

  // 消息队列，基于Redis !!! 必须要注意不能和前面Redis和SocketIO db冲突 !!!
  config.queue = [
    // {
    //   name: 'icourse-resource',
    //   ...redisServerConfig,
    //   db: 7,
    // },
    {
      name: 'runner',
      ...redisServerConfig,
      db: 8,
    },
    {
      name: 'judger',
      ...redisServerConfig,
      db: 9,
    },
    {
      name: 'oj-task',
      ...redisServerConfig,
      db: 11,
    },
    {
      name: 'oi-task',
      ...redisServerConfig,
      db: 13,
    },
    {
      name: 'course-task',
      ...redisServerConfig,
      db: 12,
    },
    {
      name: 'train-task',
      ...redisServerConfig,
      db: 12,
    },
  ];

  config.session = {
    secure: false,
    maxAge: 2 * 3600 * 1000, // 2小时
    renew: true,
    encrypt: false
  };

  config.sessionRedis = {
    name: 'session', // specific `session` as the session store
    secure: false,
  };

  config.jupyter = {
    url: '',
    image: "registry.i51cy.com/hxr-myjupyter:faye",
    cpuRequest: 2,
    memoryRequest: '8Gi',
    kernelLimit: 50
  };

  config.managerment = 'http://manager.hxr.i51cy.com';

  config.managermentAPI = {
    app_id: '5432360931535683',
    secret: 'hastd4g8f3h2a932',
  }

  config.platHxrMainAPI = {
    host: `http://hxr.i51cy.com/api`,
    // 主站 app_id
    app_id: '9128737431120744',
    // 主站 secret
    secret: '5c5h7kg9edd2604fk4af4e1224548f80',
  }

  config.allSchoolDebug = true;

  config.thridParty = {
    2394820984575923948290: 'ja94hfao34949hfj9034uhf'
  }

  return config;
}