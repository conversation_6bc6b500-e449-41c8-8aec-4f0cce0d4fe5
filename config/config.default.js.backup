'use strict';

module.exports = appInfo => {
  const { env } = process;
  const { 
    HXR_DEPLOYMEMT = 'hxr.i51cy.com',
    ENABLE_TIMER_TASK,
    IP_WRONG_PASSWD_PER_5MIN,
    INITTRAIN = false,
    FAYE_SERVER_URL = 'http://faye',
  } = env;

  const config = {};

  config.fayeServerURL = FAYE_SERVER_URL;

  // nginx代理模式
  config.proxy = true;
  config.ipHeaders = 'X-Real-IP, x-forwarded-for';

  config.serverTimeout = 150000;

  config.ENABLE_TIMER_TASK = ENABLE_TIMER_TASK;

  // 5分钟容许密码出错次数
  config.ipWrongPasswd5Min = IP_WRONG_PASSWD_PER_5MIN ? parseInt(IP_WRONG_PASSWD_PER_5MIN, 10): 150;

  config.INITTRAIN = INITTRAIN;

  // 允许生产环境打印日志
  config.logger = {
    disableConsoleAfterReady: false,
    consoleLevel: 'DEBUG',
    level: 'DEBUG',
    allowDebugAtProd: true,
    // dir: 'C://logs',
    // file: 'logs'
  };

  // 大文件上传
  config.yuploader = {
    dir: './data/file', // 存储路径，这里是本地调试路径
    chunkSize: 10 * 1024 * 1024, // 分块大小，必须与前端配置完全一致
    // 允许上传的文件格式类型
    fileExtensions: ['.xls', '.xlsx', '.rm', '.rmvb', '.mov', '.mtv', '.wmv', '.avi', '.3gp', '.amv', '.dmv', '.otf', '.flv', '.block', '.xml', '.csv', '.asf', '.asf', '.mpg', '.ra', '.qt', '.ipynb', '.pdf', '.in', '.out', '.jpg', '.png', '.gif', '.wmf', '.webp', '.tif', '.bmp', '.ogg', '.webm', '.c', '.cpp', '.py', '.txt', '.docx', '.dat', '.wav', '.mp4', '.css', '.gz', '.pkt', '.db', '.accdb', '.m4v', '.sb3', '.slide', '.code', '.onnx'],
  };

  // 文件上传 !!! 本机调试专用
  // config.file = {
  //   hashFile: 'Y:/shared_files', // 散列文件上传（统一存放，不对外）
  //   dir: 'Y:/school',
  //   url: 'Y:/file',
  //   demoDir: 'Y:/file',
  //   tmpDir: 'Y:/tmp',
  //   tmpUrl: 'Y:/file/tmp',
  //   eliteCoursesDir: 'Y:/elite_courses',
  // };

  // MAC挂盘参数
  // sudo mkdir /Volumes/hxr-dev
  // sudo mount -t nfs -o resvport,rw,noowners **************:/mnt/yopu/hxr-dev /Volumes/hxr-dev
  
  // 文件上传 !!! 本机调试专用
  // config.file = {
  //   hashFile: '/Volumes/hxr-dev/shared_files', // 散列文件上传（统一存放，不对外）
  //   dir: '/Volumes/hxr-dev/school',
  //   url: 'file'
  // };

  // MAC挂盘参数
  // sudo mkdir /Volumes/hxr-faye
  // sudo mount -t nfs -o resvport,rw,noowners **************:/mnt/yopu/hxr-faye /Volumes/hxr-faye
  
  // 文件上传 !!! 本机调试专用
  // config.file = {
  //   hashFile: '/Volumes/hxr-faye/shared_files', // 散列文件上传（统一存放，不对外）
  //   dir: '/Volumes/hxr-faye/school',     
  //   url: 'file'
  // };

  // 文件上传
  // config.file = {
  //   hashFile: '/Users/<USER>/Desktop/Projects/hxr/api/data/shared_files', // 散列文件上传（统一存放，不对外）
  //   trainFile: '/Users/<USER>/Desktop/Projects/hxr/api/data/train',
  //   dir: '/Users/<USER>/Desktop/Projects/hxr/api/data/school',
  //   url: '/Users/<USER>/Desktop/Projects/hxr/apifile',
  //   demoDir: '/Users/<USER>/Desktop/Projects/hxr/api/data/file',
  //   tmpDir: '/Users/<USER>/Desktop/Projects/hxr/api/data/tmp',
  //   tmpUrl: '/Users/<USER>/Desktop/Projects/hxr/api/file/tmp',
  //   eliteCoursesDir: '/Users/<USER>/Desktop/Projects/hxr/api/data/elite_courses',
  // };
  config.file = {
    hashFile: './data/shared_files', // 散列文件上传（统一存放，不对外）
    trainFile: './data/train',
    dir: './data/school',
    url: 'file',
    demoDir: './data/file',
    tmpDir: './data/tmp',
    tmpUrl: '/file/tmp',
    eliteCoursesDir: './data/elite_courses'
  };

  config.multipart = {
    fileExtensions: ['.xls', '.xlsx', '.rm', '.rmvb', '.mov', '.mtv', '.wmv', '.avi', '.3gp', '.amv', '.dmv', '.otf', '.flv', '.block', '.xml', '.csv', '.asf', '.asf', '.mpg', '.ra', '.qt', '.ipynb', '.pdf', '.in', '.out', '.jpg', '.png', '.gif', '.wmf', '.webp', '.tif', '.bmp', '.ogg', '.webm', '.c', '.cpp', '.py', '.txt', '.docx', '.dat', '.wav', '.mp4', '.css', '.gz', '.pkt', '.db', '.accdb', '.m4v', '.sb3', '.slide', '.code', '.onnx', '.zip'],
    fileSize: '1024mb'
  };

  config.bodyParser = {
    jsonLimit: '128mb',
    formLimit: '128mb',
  }

  switch (process.platform) {
    // MAMP
    case 'darwin':
      // 增加默认本地调试环境数据库配置
      config.sequelize = {
        dialect: 'mysql', // support: mysql, mariadb, postgres, mssql
        dialectOptions: {
          // 对于 mysql2 或 mariadb 的连接参数
          charset: 'utf8mb4'
        },
        baseDir: 'model/main', // models in `app/${model}`
        delegate: 'mainModel', // 引用时使用mainModel引用
        database: 'hxr',
        // host: '**************',
        // host: '************',
        // host: '***********',
        host: '*************',
        port: '3306',
        // post: ' ',
        username: 'root',
        password: 'yopu123',
        timezone: '+08:00',
      };

      // // 正式题库
      // config.judge = {
      //   dir: '/data/judge',
      //   callback: 'http://**************:7001/api'
      // };

      break;
    // Docker
    case 'linux':
      config.sequelize = {
        dialect: 'mysql', // support: mysql, mariadb, postgres, mssql
        dialectOptions: {
          // 对于 mysql2 或 mariadb 的连接参数
          charset: 'utf8mb4'
        },
        baseDir: 'model/main', // models in `app/${model}`
        delegate: 'mainModel', // 引用时使用mainModel引用
        database: 'hxr',
        host: '**************',
        port: '3306',
        username: 'hxr',
        password: '1NKqvFxfdQrqDIbd',
        timezone: '+08:00'
      };
      break;
    // WAMP
    default:
      // 增加默认本地调试环境数据库配置
      config.sequelize = {
        dialect: 'mysql', // support: mysql, mariadb, postgres, mssql
        dialectOptions: {
          // 对于 mysql2 或 mariadb 的连接参数
          charset: 'utf8mb4'
        },
        baseDir: 'model/main', // models in `app/${model}`
        delegate: 'mainModel', // 引用时使用mainModel引用
        database: 'hxr',
        // host: '127.0.0.1', // *************  **************   
        // host: '**************',
        host: '*************',
        port: '3306', // 32713  3306
        username: 'hxr',
        password: '1NKqvFxfdQrqDIbd',
        timezone: '+08:00',
      };
      break;
  }

  config.keys = appInfo.name + '_1491792616161_7654';

  const redisServerConfig = {
    // host: '***********',
    // port: '31665',
    host: 'localhost',
    port: '6379',
    password: '',
  };

  // 增加Redis数据库连接，!!! 必须要注意不能和后续消息队列以及实时通讯db冲突 !!!
  config.redis = {
    clients: {
      // 会话
      session: {
        ...redisServerConfig,
        db: 0
      },

      // 缓存
      cache: {
        ...redisServerConfig,
        db: 1
      },

      // faye
      faye: {
        ...redisServerConfig,
        db: 2
      },

      // 交互式课程的容器
      container: {
        ...redisServerConfig,
        db: 3
      },

      // 交互式课程的Kernel
      kernel: {
        ...redisServerConfig,
        db: 4
      },

      // 异步任务
      asyncTask: {
        ...redisServerConfig,
        db: 12
      },

      // OJ判决学生记录
      judge: {
        ...redisServerConfig,
        db: 10
      },
      train: {
        ...redisServerConfig,
        db: 14
      }
    },
  };

  // 消息队列，基于Redis !!! 必须要注意不能和前面Redis和SocketIO db冲突 !!!
  config.queue = [
    // {
    //   name: 'icourse-resource',
    //   ...redisServerConfig,
    //   db: 7,
    // },
    {
      name: 'runner',
      ...redisServerConfig,
      db: 8,
    },
    {
      name: 'judger',
      ...redisServerConfig,
      db: 9,
    },
    {
      name: 'oj-task',
      ...redisServerConfig,
      db: 11,
    },
    {
      name: 'oi-task',
      ...redisServerConfig,
      db: 13,
    },
    {
      name: 'course-task',
      ...redisServerConfig,
      db: 12,
    },
    {
      name: 'train-task',
      ...redisServerConfig,
      db: 12,
    },
  ];

  config.session = {
    secure: false,
    maxAge: 2 * 3600 * 1000, // 2小时
    // maxAge: 60 * 1000,
    renew: true,
    encrypt: false
  };

  config.sessionRedis = {
    name: 'session', // specific `session` as the session store
    secure: false,
  };

  // 不进行全部操作记录搜集
  config.fullActionRecorder = false;

  // console.log(process.env, process.env.schoolSlug)
  config.jupyter = {
    url: 'hxr.i51cy.com/',
    image: "registry.i51cy.com/hxr-myjupyter:latest",
    cpuRequest: 2,
    memoryRequest: '4Gi',
    kernelLimit: 50
  };

  config.security = {
    csrf: {
      enable: true,
      useSession: true, // 默认为 false，当设置为 true 时，将会把 csrf token 保存到 Session 中
      sessionName: 'csrfToken', // Session 中的字段名，默认为 csrfToken
      ignore: (ctx) => {
        if(
          (ctx.request.url.indexOf('faye') !== -1) || 
          (ctx.request.url.indexOf('thirdPart') !== -1) || 
          (ctx.request.url.indexOf('oauth2') !== -1)
        ) {
          return true
        }

        return false
      }
    },
  };
 
  config.sso = {
    'hxr.jsnje.cn': {
      name:'hxr.jsnje.cn',
      path: 'https://sso.nje.cn',
      AppCode: 'RNYWJXPT00003212',
      AppKey: '04140ecb-a971-4180-8d75-1e5b630421a1',
      DEFAULTCODECOUNT: 300,
    },
    'ai.ksedu.cn': {
      name:'ai.ksedu.cn',
      path: 'https://sso.ksedu.cn',
      AppCode: 'RGZNJXPT0003214511',
      AppKey: '6411d3a2-6a92-42a7-b309-ff827e10c754',

      AccountId: 'B17F8F89B68741E19ECCBC2E915E6AA4',
      AccountPassword: 'BECD548DD9A547FC952ED2D3F908865F',
      apiPath: 'https://api.ksedu.cn/edu/openapi/index',

      teacherPassword: '053f8a5d46c1cb8954cc53caab4cb1f02f9ad386',
      studentPassword: '7c4a8d09ca3762af61e59520943dc26494f8941b',
    },
    'hxr.i51cy.com': {
      name:'hxr.i51cy.com',
      path: 'https://sso.ksedu.cn',
      AppCode: 'RGZNJXPT0003214511',
      AppKey: '6411d3a2-6a92-42a7-b309-ff827e10c754',

      AccountId: 'B17F8F89B68741E19ECCBC2E915E6AA4',
      AccountPassword: 'BECD548DD9A547FC952ED2D3F908865F',
      apiPath: 'https://api.ksedu.cn/edu/openapi/index',

      teacherPassword: '053f8a5d46c1cb8954cc53caab4cb1f02f9ad386',
      studentPassword: '7c4a8d09ca3762af61e59520943dc26494f8941b',
    },
    'njyz': {
      name: 'njyz',
      appid: '635578760252622038723872',
      appkey: 'NMO4CpgZIAc6ZioRaClrFflfnyn3USFR',

      apiPath: 'http://njry-dx.tpddns.cn:18090/school/v1',

      teacherLoginUrl: 'http://njry-dx.tpddns.cn:18090/njyz/login',
      studentLoginUrl: 'http://njry-dx.tpddns.cn:18082/njyz/login',

      teacherLoginPath: 'http://njry-dx.tpddns.cn:18090/apis/sso/validToken.do',
      studentLoginPath: 'http://njry-dx.tpddns.cn:18082/studentApis/sso/student/validToken.do',
    }
  }

  // 氦星人应用配置
  config.hxr = {
    deploymentSlug: HXR_DEPLOYMEMT, // 部署标志：氦星人测试环境
  };

  config.managerment = 'https://manager.hxr.i51cy.com';
  // config.managerment = 'http://127.0.0.1:7002';

  config.managermentAPI = {
    app_id: '5432360931535683',
    secret: 'hastd4g8f3h2a932',
  }

  config.platHxrMainAPI = {
    host: `http://${HXR_DEPLOYMEMT}/api`,
    // host: 'http://127.0.0.1:7002',
    // 主站 app_id
    app_id: '9128737431120744',
    // 主站 secret
    secret: '5c5h7kg9edd2604fk4af4e1224548f80',
  }

  config.allSchoolDebug = true;

  config.thridParty = {
    239482093948290: 'ja94hfao34949hfj9034uhf'
  }

  // OAuth2接口，仿制的是Github实现的OAuth2
  config.oauth2Provider = {
    codeExpire: 60 * 10, // code过期时间，单位秒
    accessTokenExpire: 60 * 60 * 10, // accessToken过期时间，单位秒
    clientMap: {
      // 江宁高级中学Dify
      "jnsms-dify": {
        clientID: 'jnsms-dify',
        clientSecret: 'asdjfiko!@#adLJjsfio;jqwaeio;fjio',
      }
    }
  };

  // 大语言模型配置
  config.llm = {
    "deepseek-chat": {
      apiKey: '***********************************',
      baseURL: 'https://api.deepseek.com'
    }
  }

  return config;
};

