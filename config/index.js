// 确定环境变量的配置
const { env } = process;
const { 
    WORK_MODE = 'dev'
} = env;

const appInfo = {
    name: '氦星人API',
};

// 加载合并
const defaultConfig = require('./config.default.js')(appInfo);
const targetConfig = require(`./config.${WORK_MODE}.js`)(appInfo);

const config = {
    name: '请在config.default.js中设置应用名称!!!',
    WORK_MODE,
    ...defaultConfig,
    ...targetConfig
};

// 打印
console.log(`${config.NAME} @ ${WORK_MODE}`);
console.log('==== Configuration Begin ====');
console.log(config);
console.log('==== Configuration End ====');

// 导出配置
for(const key in config) {
    const value = config[key];
    exports[key] = value;
}