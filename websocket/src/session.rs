use std::time::{Duration, Instant};

use actix::prelude::*;
use actix_web_actors::ws;
// use reqwest::Client;
// use post_course_download_course_zip;



use crate::server;


/// 心跳包发送的频率
const HEARTBEAT_INTERVAL: Duration = Duration::from_secs(15);

/// 客户端无响应导致超时的时间
const CLIENT_TIMEOUT: Duration = Duration::from_secs(30);

#[derive(Debug)]
pub struct WsChatSession {
    /// 唯一的会话ID
    pub id: usize,

    /// 客户端必须在10秒内（CLIENT_TIMEOUT）至少发送一次ping，
    /// 否则我们将断开连接。
    pub hb: Instant,

    /// 房间：学生student/教师teacher
    pub room: String,

    /// 连接时使用的用户IP地址，TODO: 禁止用户在同一台电脑开启两个教师端/学生端
    pub client_ip: String,

    /// ChatServer Actor地址
    pub addr: Addr<server::WSServer>,

    // pub downloading:i32,
}

impl WsChatSession {
    /// 每隔5秒（HEARTBEAT_INTERVAL）向客户端发送ping的辅助方法。
    ///
    /// 同时，此方法检查客户端的心跳
    /// 心跳实现
    fn hb(&self, ctx: &mut ws::WebsocketContext<Self>) {
        ctx.run_interval(HEARTBEAT_INTERVAL, |act, ctx| {
            // 检查客户端心跳
            // 5秒没有心跳就是断线了
            if Instant::now().duration_since(act.hb) > CLIENT_TIMEOUT {
                // 心跳超时
                // println!("Websocket Client heartbeat failed, disconnecting!");

                // 通知聊天服务器
                act.addr.do_send(server::Disconnect { 
                    ip_addr: act.client_ip.clone(),
                    room: act.room.clone(),
                    id: act.id 
                });

                // 停止actor
                ctx.stop();

                // 不要尝试发送ping
                return;
            }

            ctx.ping(b"");
        });
    }
    // fn downloading(&self, ctx: &mut ws::WebsocketContext<Self>, msg: String) {
    //     ctx.run_interval(HEARTBEAT_INTERVAL, |act, ctx| {
    //         // 检查客户端心跳
    //         // 5秒没有心跳就是断线了
    //         if Instant::now().duration_since(act.hb) > CLIENT_TIMEOUT {
    //             // 心跳超时
    //             // println!("Websocket Client heartbeat failed, disconnecting!");

    //             // 通知聊天服务器
    //             act.addr.do_send(server::Disconnect { 
    //                 ip_addr: act.client_ip.clone(),
    //                 room: act.room.clone(),
    //                 id: act.id 
    //             });

    //             // 停止actor
    //             ctx.stop();

    //             // 不要尝试发送ping
    //             return;
    //         }

    //         ctx.ping(b"");
    //     });
    // }
}

impl Actor for WsChatSession {
    type Context = ws::WebsocketContext<Self>;

    /// 方法在actor启动时调用。
    /// 我们向ChatServer注册ws会话
    fn started(&mut self, ctx: &mut Self::Context) {
        // 我们将在会话启动时开始心跳过程。
        self.hb(ctx);

        // 在聊天服务器中注册自己。`AsyncContext::wait`在上下文中注册
        // 未来，但上下文会等待此未来解析
        // 在处理任何其他事件之前。
        // HttpContext::state()是WsChatSessionState的实例，状态在
        // 应用程序中的所有路由之间共享
        let addr = ctx.address();

        self.addr
            .send(server::Connect {
                ip_addr: self.client_ip.clone(),
                room: self.room.clone(),
                addr: addr.recipient(),
            })
            .into_actor(self)
            .then(|res, act, ctx| {
                match res {
                    Ok(res) => act.id = res,
                    // 聊天服务器出现问题
                    _ => ctx.stop(),
                }

                fut::ready(())
            })
            .wait(ctx);
    }

    fn stopping(&mut self, _: &mut Self::Context) -> Running {
        // 通知聊天服务器
        self.addr.do_send(server::Disconnect { 
            ip_addr: self.client_ip.clone(),
            room: self.room.clone(),
            id: self.id 
        });
        Running::Stop
    }
}

// 处理来自聊天服务器的消息，我们只是将其发送到对等websocket
impl Handler<server::Message> for WsChatSession {
    type Result = ();

    fn handle(&mut self, msg: server::Message, ctx: &mut Self::Context) {
        ctx.text(msg.0);
    }
}

// WebSocket服务器端收到的消息处理
// 实际上只是实现了心跳检测、关闭连接，发送文本、二进制流都是空实现
impl StreamHandler<Result<ws::Message, ws::ProtocolError>> for WsChatSession {
    fn handle(&mut self, msg: Result<ws::Message, ws::ProtocolError>, ctx: &mut Self::Context) {
        let msg = match msg {
            Err(_) => {
                ctx.stop();
                return;
            }
            Ok(msg) => msg,
        };

        // log::debug!("WEBSOCKET MESSAGE: {msg:?}");
        match msg {
            ws::Message::Ping(msg) => {
                self.hb = Instant::now();
                ctx.pong(&msg);
            }
            ws::Message::Pong(_) => {
                self.hb = Instant::now();
            }
            ws::Message::Text(text) => {
                let m = text.trim();
                
                // // 我们检查/sss类型的消息
                // if m.starts_with('/') {
                //     let v: Vec<&str> = m.splitn(2, ' ').collect();
                //     match v[0] {
                //         // "/list" => {
                //         //     // 向聊天服务器发送ListRooms消息并等待
                //         //     // 响应
                //         //     println!("List rooms");
                //         //     self.addr
                //         //         .send(server::ListRooms)
                //         //         .into_actor(self)
                //         //         .then(|res, _, ctx| {
                //         //             match res {
                //         //                 Ok(rooms) => {
                //         //                     for room in rooms {
                //         //                         ctx.text(room);
                //         //                     }
                //         //                 }
                //         //                 _ => println!("Something is wrong"),
                //         //             }
                //         //             fut::ready(())
                //         //         })
                //         //         .wait(ctx)
                //         //     // .wait(ctx)暂停上下文中的所有事件，
                //         //     // 因此actor不会收到任何新消息，直到它获得房间列表
                //         //     // 返回
                //         // }
                //         "/downloadCourse" => {
                //             // 调用文件下载接口
                            
                //         }
                //         _ => ctx.text(format!("!!! unknown command: {m:?}")),
                //     }
                // } else {
                //     let msg = m.to_owned();
                //     // 向聊天服务器发送消息
                //     self.addr.do_send(server::ClientMessage {
                //         id: self.id,
                //         msg,
                //         room: self.room.clone(),
                //     })
                // }
                let msg = m.to_owned();
                self.addr.do_send(server::ClientMessage {
                    id: self.id,
                    msg,
                    room: self.room.clone(),
                })
            }
            ws::Message::Binary(_) => println!("Unexpected binary"),
            // 关闭连接
            ws::Message::Close(reason) => {
                ctx.close(reason);
                ctx.stop();
            }
            // 连续消息帧，这个应该是发错了，连接关了吧
            ws::Message::Continuation(_) => {
                ctx.stop();
            }
            ws::Message::Nop => (),
        }
    }
}
