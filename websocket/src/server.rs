//! `ChatServer` 是一个 actor。它维护着连接客户端会话的列表。
//! 并管理可用的房间。对等方通过 `ChatServer` 向同一房间中的其他对等方发送消息。

use std::collections::{HashMap, HashSet};
use actix::prelude::*;
use rand::{self, rngs::ThreadRng, Rng};
use std::sync::{Arc, Mutex};

/// 聊天服务器向会话发送此消息
#[derive(Message)]
#[rtype(result = "()")]
pub struct Message(pub String);

/// 聊天服务器通信的消息

/// 创建新的聊天会话
#[derive(Message)]
#[rtype(usize)]
pub struct Connect {
    pub ip_addr: String,
    pub room: String,
    pub addr: Recipient<Message>,
}

/// 会话断开连接
#[derive(Message)]
#[rtype(result = "()")]
pub struct Disconnect {
    pub ip_addr: String,
    pub room: String,
    pub id: usize,
}

/// 向特定房间发送消息
#[derive(Message,Debug)]
#[rtype(result = "()")]
pub struct ClientMessage {
    /// 客户端会话的ID
    pub id: usize,
    /// 对等方消息
    pub msg: String,
    /// 房间名称
    pub room: String,
}

/// 可用房间的列表
pub struct ListOnlineIPs;

impl actix::Message for ListOnlineIPs {
    type Result = HashSet<String>;
}

pub struct ListOnlineSessionCount;
impl actix::Message for ListOnlineSessionCount {
    type Result = usize;
}

/// 加入房间，如果房间不存在则创建新房间。
#[derive(Message)]
#[rtype(result = "()")]
pub struct Join {
    /// 客户端ID
    pub id: usize,

    /// 房间名称
    pub name: String,
}

/// `ChatServer` 管理聊天室并负责协调聊天会话。
///
/// 实现非常简单。

pub struct WSServer {
    session_ips: HashSet<String>, // 存储在线IP地址
    sessions: HashMap<usize, Recipient<Message>>,
    rooms: HashMap<String, HashSet<usize>>,
    // rng: Arc<Mutex<ThreadRng>>,
    on_client_message: Arc<dyn Fn(&ClientMessage, &WSServer) -> () + Send + Sync>,
    // visitor_count: Arc<AtomicUsize>,
}

impl std::fmt::Debug for WSServer {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("WSServer")
            .field("session_ips", &self.session_ips)
            .field("sessions", &self.sessions)
            .field("rooms", &self.rooms)
            // .field("rng", &self.rng)
            .field("on_client_message", &"Box<dyn Fn(&ClientMessage) -> ()>")
            .finish()
    }
}

impl WSServer {
    // visitor_count: Arc<AtomicUsize>
    pub fn new(
        on_client_message: Arc<dyn Fn(&ClientMessage, &WSServer) -> () + Send + Sync>
        
    ) -> WSServer {
        // 默认房间
        let mut rooms = HashMap::new();

        // 学生机房间
        rooms.insert("student".to_owned(), HashSet::new());

        // 教师机房间
        rooms.insert("teacher".to_owned(), HashSet::new());

        // 本地房间
        rooms.insert("local".to_owned(), HashSet::new());

        WSServer {
            session_ips: HashSet::new(),
            sessions: HashMap::new(),
            rooms,
            // rng: Arc::new(Mutex::new(rand::thread_rng())),
            on_client_message: Arc::from(on_client_message),
            // visitor_count,
        }
    }
}
impl Handler<ClientMessage> for WSServer {
    type Result = ();
    
    fn handle(&mut self, msg: ClientMessage, _: &mut Context<Self>){
        (self.on_client_message)(&msg, self);
    }
}

impl WSServer {
    /// 向房间中的所有用户发送消息
    pub fn send_message(&self, room: &str, message: &str, skip_id: usize) {
        // log::debug!("WEBSOCKET SEND TO {room:?}: {message:?}");
        if let Some(sessions) = self.rooms.get(room) {
            for id in sessions {
                if *id != skip_id {
                    if let Some(addr) = self.sessions.get(id) {
                        addr.do_send(Message(message.to_owned()));
                    }
                }
            }
        }
    }
}
impl Clone for WSServer {
    fn clone(&self) -> Self {
        WSServer {
            session_ips: self.session_ips.clone(),
            sessions: self.sessions.clone(),
            rooms: self.rooms.clone(),
            on_client_message: self.on_client_message.clone(),
        }
    }
}

/// 将 `ChatServer` 转换为 actor
impl Actor for WSServer {
    /// 我们将使用简单的 Context，我们只需要与其他 actor 通信的能力。
    type Context = Context<Self>;
}

/// 处理 Connect 消息的处理器。
///
/// 注册新会话并为此会话分配唯一 ID
impl Handler<Connect> for WSServer {
    type Result = usize;

    // 客户端连接逻辑
    fn handle(&mut self, msg: Connect, _: &mut Context<Self>) -> Self::Result {
        // 生成ID，加入会话
        // let id = self.rng.lock().unwrap().gen::<usize>();
        let id = rand::thread_rng().gen::<usize>();
        self.sessions.insert(id, msg.addr);

        // 加入默认房间
        self.rooms
            .entry(msg.room.to_owned())
            .or_insert_with(HashSet::new)
            .insert(id);

        // 当学生加入房间
        if msg.room == "student" {
            // 维护在线用户IP集合
            self.session_ips.insert(msg.ip_addr.clone());

            // 通知老师连接上了
            self.send_message("teacher", format!(r#"{{"type":"update","online":"在线","ip_addr":"{}"}}"#, msg.ip_addr).as_str(), 0);
        }

        // 返回ID
        id
    }
}

/// 处理 Disconnect 消息的处理器。
impl Handler<Disconnect> for WSServer {
    type Result = ();

    fn handle(&mut self, msg: Disconnect, _: &mut Context<Self>) {
        // 服务器删除用户ID
        if self.sessions.remove(&msg.id).is_none() {
            // 找不到这个ID，不要重复删除了
            return;
        }

        for (_name, sessions) in &mut self.rooms {
            sessions.remove(&msg.id);
        }

        // 学生退出
        if msg.room == "student" {
            // 删除离线用户IP地址
            self.session_ips.remove(&msg.ip_addr.clone());
    
            // 通知教师学生离开了
            self.send_message("teacher", format!(r#"{{"type":"update","online":"离线","ip_addr":"{}"}}"#, msg.ip_addr).as_str(), 0);
        }
    }
}

/// 处理 Message 消息的处理器。
// impl Handler<ClientMessage> for WSServer {
//     type Result = ();

//     fn handle(&mut self, msg: ClientMessage, _: &mut Context<Self>) {
//         self.send_message(&msg.room, msg.msg.as_str(), msg.id);
//     }
// }

/// 列举当前在线用户IP地址
impl Handler<ListOnlineIPs> for WSServer {
    type Result = MessageResult<ListOnlineIPs>;

    fn handle(&mut self, _: ListOnlineIPs, _: &mut Context<Self>) -> Self::Result {
        MessageResult(self.session_ips.clone())
    }
}

/// 列举当前在线用户IP地址
impl Handler<ListOnlineSessionCount> for WSServer {
    type Result = MessageResult<ListOnlineSessionCount>;

    fn handle(&mut self, _: ListOnlineSessionCount, _: &mut Context<Self>) -> Self::Result {
        if let Some(sessions) = self.rooms.get("student") {
            return MessageResult(sessions.len());
        }

        MessageResult(0)
    }
}