const sqlite3 = require('sqlite3').verbose();

// 打开数据库
const db = new sqlite3.Database('./init/hxr.db', (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
        return;
    }
    console.log('Connected to the SQLite database.');
});

// 查询所有以default开头的配置项
db.all("SELECT key, value FROM system_config WHERE key LIKE 'default%'", [], (err, rows) => {
    if (err) {
        console.error('Error querying database:', err.message);
        return;
    }
    
    console.log('Default configurations:');
    rows.forEach((row) => {
        console.log(`${row.key}: ${row.value}`);
    });
    
    // 关闭数据库连接
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err.message);
            return;
        }
        console.log('Database connection closed.');
    });
});
