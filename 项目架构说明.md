# 项目架构说明

## 📐 整体架构

```
┌─────────────────────────────────────┐
│   plt-teacher-app (桌面应用)        │
│   - <PERSON><PERSON> + <PERSON><PERSON>                   │
│   - 教师端界面                      │
└──────────────┬──────────────────────┘
               │ HTTP (127.0.0.1:46048)
               ↓
┌─────────────────────────────────────┐
│   plt-teacher-server (本地后端)     │
│   - Rust + Actix-web                │
│   - 端口: 46048                     │
│   - 数据库: SQLite (本地)           │
└──────────────┬──────────────────────┘
               │ HTTPS (需要时)
               ↓
┌─────────────────────────────────────┐
│   远程云平台 (hxr.iyopu.com)        │
│   - Node.js + Egg.js (api 项目)    │
│   - MySQL + Redis                   │
│   - 云端课程资源和用户管理          │
└─────────────────────────────────────┘
```

---

## 📁 项目目录说明

### 1. `plt-teacher-app/` - 桌面应用（前端）
- **技术栈**: <PERSON>ri + React + Umi.js
- **功能**: 
  - 教师端用户界面
  - 课程管理、班级管理
  - 学生终端管理
- **启动**: `pnpm tauri dev`

### 2. `plt-teacher-server/` - 本地后端
- **技术栈**: Rust + Actix-web + SeaORM
- **端口**: 46048
- **数据库**: SQLite (hxr.db)
- **功能**:
  - 本地 API 服务
  - 本地课程数据管理
  - 与远程云平台通信的中介
  - WebSocket 连接管理
- **启动**: `cargo run`

### 3. `api/` - 远程云平台代码（仅作参考）
- **技术栈**: Node.js + Egg.js
- **说明**: 
  - ⚠️ **这是远程云平台的代码仓库，不需要在本地运行！**
  - 部署在 `hxr.iyopu.com` 等云平台服务器上
  - 用于了解远程 API 接口规范
- **不要本地启动此项目！**

---

## 🔄 典型业务流程

### 下载课程流程

1. **用户点击"下载课程"按钮**
   ```
   plt-teacher-app
   ```

2. **调用本地后端获取登录凭证**
   ```
   POST http://127.0.0.1:46048/api/admin/course/teacher/remote/login
   Body: { site_url, username, password, lab, mac }
   ```

3. **本地后端调用远程平台验证账号**
   ```
   plt-teacher-server → 远程 API
   返回: { loginCode, csrfToken, cookies }
   ```

4. **前端打开远程平台页面进行课程下载**
   ```
   新窗口: ${site_url}/admin/#/trainAnalyzeLogin?code=${loginCode}&toUrl=course
   例如: http://hxr.iyopu.com/s/csxx/admin/#/trainAnalyzeLogin?code=xxx
   ```

5. **远程平台验证 loginCode，返回课程列表**

6. **用户选择课程，下载到本地**

---

## 🚀 启动顺序

### 首次启动（未绑定账号）

1. **启动本地 Rust 后端**
   ```bash
   cd plt-teacher-server
   
   # 首次运行需要复制配置文件
   copy server.env target\debug\server.env
   xcopy /E /I init target\debug\init
   
   # 启动服务
   cargo run
   ```
   
   看到 `氦星人教师机服务器 0.1.0 正在监听 0.0.0.0:46048` 表示成功

2. **启动前端应用**
   ```bash
   cd plt-teacher-app
   
   # 开发模式
   pnpm tauri dev
   ```

3. **完成启动向导**
   - 应用会自动检测到 `site_url = 'demo'`
   - 自动打开启动向导窗口
   - 填写你的氦星人云平台账号信息
   - 绑定成功后会更新本地配置

### 后续启动（已绑定账号）

1. 启动 Rust 后端
2. 启动前端应用
3. 自动登录，直接使用

---

## ❓ 常见问题

### Q1: 为什么有两个后端项目？

**A**: 
- `plt-teacher-server` (Rust) 是**本地后端**，运行在教师电脑上
- `api` (Node.js) 是**远程云平台**，运行在云服务器上
- 两者配合工作，本地后端作为中介和本地服务提供者

### Q2: 下载课程时报 400 错误："您访问的功能需要教师权限"

**A**: 这是因为 `site_url` 还是初始值 `'demo'`，需要：
1. 确保 Rust 后端已启动
2. 重启前端，完成启动向导绑定账号
3. 绑定后 `site_url` 会更新为正确的远程地址

### Q3: 为什么不能本地运行 api 项目？

**A**: 
- `api` 项目依赖云端的 MySQL 和 Redis
- 需要连接到特定的数据库服务器 (172.16.31.110)
- 缺少完整的数据和配置
- 它是给云平台管理员使用的，不是给教师端用户的

### Q4: 如何查看当前的 site_url？

**A**: 
```bash
# 查看 SQLite 数据库
cd plt-teacher-server/target/debug
sqlite3 hxr.db "SELECT * FROM system_config WHERE key='site_url';"
```

### Q5: 前端连接哪个后端？

**A**: 
- 前端**只连接本地 Rust 后端** (127.0.0.1:46048)
- Rust 后端再根据需要调用远程云平台
- 用户不需要直接访问远程 API

---

## 🛠️ 开发建议

### 修改前端代码
```bash
cd plt-teacher-app
# 修改代码后自动热重载
pnpm tauri dev
```

### 修改 Rust 后端代码
```bash
cd plt-teacher-server
# 修改代码后需要重新编译运行
cargo run
```

### 查看远程 API 接口
- 参考 `api/app/router.js` 查看所有接口路由
- 参考 `api/app/controller/` 查看接口实现
- **但不要在本地运行 api 项目！**

---

## 🎯 重要提示

1. ✅ **只需要启动 plt-teacher-server (Rust 后端)**
2. ❌ **不要启动 api 项目 (远程云平台代码)**
3. ✅ **前端默认连接本地 46048 端口**
4. ✅ **首次使用必须完成启动向导绑定账号**
5. ✅ **下载课程需要有效的远程平台账号**

---

## 📚 相关文档

- `plt-teacher-server/README.md` - Rust 后端详细说明
- `plt-teacher-app/README.md` - 前端应用说明
- `后端切换指南.md` - ~~废弃~~（基于错误理解）

**最后更新**: 2025-10-31


