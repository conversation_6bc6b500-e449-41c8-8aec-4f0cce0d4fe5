{"rustc": 7868289081541623310, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 5896785871467616221, "path": 8801143116370000475, "deps": [[11082282709338087849, "quote", false, 14408489220704795479], [11688815897905910532, "syn", false, 17177909104522095091], [13077543566650298139, "heck", false, 15874652646833368100], [14285738760999836560, "proc_macro2", false, 13461794086741022396]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_derive-e9b5ce98f6227baa\\dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}