{"rustc": 7868289081541623310, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 12237410465406213666, "deps": [[7312356825837975969, "crc32fast", false, 7343160864229621995], [7636735136738807108, "miniz_oxide", false, 14686669431436048356]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-111e41963f3bc927\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}