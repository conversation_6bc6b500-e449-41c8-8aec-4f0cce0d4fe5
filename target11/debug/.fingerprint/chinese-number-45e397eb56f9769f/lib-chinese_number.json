{"rustc": 7868289081541623310, "features": "[\"chinese-to-number\", \"default\", \"num-bigint\", \"num-traits\", \"number-to-chinese\", \"std\"]", "declared_features": "[\"chinese-to-number\", \"default\", \"num-bigint\", \"num-traits\", \"number-to-chinese\", \"std\"]", "target": 15946960765597942984, "profile": 15657897354478470176, "path": 3202725377534802822, "deps": [[5157631553186200874, "num_traits", false, 4817579227553181866], [7890276103475655350, "enum_ordinalize", false, 8978874520302380307], [12528732512569713347, "num_bigint", false, 7630959249455282023], [16908298234001246019, "chinese_variant", false, 15943469961056999399]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chinese-number-45e397eb56f9769f\\dep-lib-chinese_number", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}