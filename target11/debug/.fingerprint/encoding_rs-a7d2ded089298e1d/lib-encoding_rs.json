{"rustc": 7868289081541623310, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 15657897354478470176, "path": 14512764783672348509, "deps": [[7667230146095136825, "cfg_if", false, 11566262324257019426]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-a7d2ded089298e1d\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}