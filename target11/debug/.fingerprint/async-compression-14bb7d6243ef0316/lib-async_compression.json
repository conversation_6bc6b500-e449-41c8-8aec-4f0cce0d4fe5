{"rustc": 7868289081541623310, "features": "[\"brotli\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"futures-io\", \"gzip\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 1419271269840776899, "path": 17372783445541565025, "deps": [[1418514084193704528, "compression_codecs", false, 18428256113669291021], [1906322745568073236, "pin_project_lite", false, 14377486027600573708], [7620660491849607393, "futures_core", false, 10185287431925956518], [7720834239451334583, "tokio", false, 10194416700117889595], [13865646604939608930, "compression_core", false, 13068618596068578197]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-14bb7d6243ef0316\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}