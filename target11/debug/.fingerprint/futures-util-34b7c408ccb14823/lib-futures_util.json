{"rustc": 7868289081541623310, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 17510367019209554428, "deps": [[5103565458935487, "futures_io", false, 8524945502723996541], [198136567835728122, "memchr", false, 9086700952074288555], [1615478164327904835, "pin_utils", false, 2105454296310835692], [1811549171721445101, "futures_channel", false, 8897691913039395542], [1906322745568073236, "pin_project_lite", false, 14377486027600573708], [7013762810557009322, "futures_sink", false, 9365711432218614735], [7620660491849607393, "futures_core", false, 10185287431925956518], [10565019901765856648, "futures_macro", false, 10117527289965478548], [14767213526276824509, "slab", false, 8492086034571287643], [16240732885093539806, "futures_task", false, 9426041057328841975]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-34b7c408ccb14823\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}