{"rustc": 7868289081541623310, "features": "[\"aes-gcm\", \"base64\", \"hkdf\", \"hmac\", \"key-expansion\", \"percent-encode\", \"percent-encoding\", \"private\", \"rand\", \"secure\", \"sha2\", \"signed\", \"subtle\"]", "declared_features": "[\"aes-gcm\", \"base64\", \"hkdf\", \"hmac\", \"key-expansion\", \"percent-encode\", \"percent-encoding\", \"private\", \"rand\", \"secure\", \"sha2\", \"signed\", \"subtle\"]", "target": 678524939984925341, "profile": 15657897354478470176, "path": 12967369519669718123, "deps": [[3611029251930514425, "aes_gcm", false, 823041854158685713], [6803352382179706244, "percent_encoding", false, 14040111309557227212], [8010322816087218523, "build_script_build", false, 6834282727378424092], [9209347893430674936, "hmac", false, 9084078360293624021], [9857275760291862238, "sha2", false, 18319081935533318401], [12221344297584609106, "hkdf", false, 7019852246960311210], [13208667028893622512, "rand", false, 12210897855265911804], [13258367904422248074, "base64", false, 9966556479026295429], [17003143334332120809, "subtle", false, 13762848787013513800], [18360501799614255111, "time", false, 11325735960126687872]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie-af7b5bfd8c371ffb\\dep-lib-cookie", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}