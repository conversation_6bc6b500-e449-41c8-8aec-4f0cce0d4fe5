{"rustc": 7868289081541623310, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 13827760451848848284, "path": 376490646855568738, "deps": [[189982446159473706, "parking", false, 9588331305542712849], [1906322745568073236, "pin_project_lite", false, 15152602122021925890], [12100481297174703255, "concurrent_queue", false, 11690103271488090501]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\event-listener-d9b8da39592ca8e1\\dep-lib-event_listener", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}