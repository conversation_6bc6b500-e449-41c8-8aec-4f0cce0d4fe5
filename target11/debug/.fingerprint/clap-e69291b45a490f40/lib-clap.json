{"rustc": 7868289081541623310, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15221872889701672926, "path": 8604158156773628123, "deps": [[568339519768649955, "clap_derive", false, 614900648729675236], [10714286228985340213, "clap_builder", false, 11300169979010532426]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-e69291b45a490f40\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}