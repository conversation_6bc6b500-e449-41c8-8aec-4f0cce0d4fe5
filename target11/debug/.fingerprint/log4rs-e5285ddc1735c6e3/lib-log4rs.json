{"rustc": 7868289081541623310, "features": "[\"all_components\", \"ansi_writer\", \"chrono\", \"compound_policy\", \"config_parsing\", \"console_appender\", \"console_writer\", \"default\", \"delete_roller\", \"file_appender\", \"fixed_window_roller\", \"humantime\", \"json_encoder\", \"libc\", \"log-mdc\", \"onstartup_trigger\", \"parking_lot\", \"pattern_encoder\", \"rand\", \"rolling_file_appender\", \"serde\", \"serde-value\", \"serde_json\", \"serde_yaml\", \"simple_writer\", \"size_trigger\", \"thread-id\", \"threshold_filter\", \"time_trigger\", \"typemap-ors\", \"winapi\", \"yaml_format\"]", "declared_features": "[\"all_components\", \"ansi_writer\", \"background_rotation\", \"chrono\", \"compound_policy\", \"config_parsing\", \"console_appender\", \"console_writer\", \"default\", \"delete_roller\", \"file_appender\", \"fixed_window_roller\", \"flate2\", \"gzip\", \"humantime\", \"json_encoder\", \"json_format\", \"libc\", \"log-mdc\", \"log_kv\", \"onstartup_trigger\", \"parking_lot\", \"pattern_encoder\", \"rand\", \"rolling_file_appender\", \"serde\", \"serde-value\", \"serde_json\", \"serde_yaml\", \"simple_writer\", \"size_trigger\", \"thread-id\", \"threshold_filter\", \"time_trigger\", \"toml\", \"toml_format\", \"typemap-ors\", \"winapi\", \"yaml_format\", \"zstd\"]", "target": 10889050935924218042, "profile": 13766730378222015417, "path": 2557250862492009427, "deps": [[503842845364652431, "chrono", false, 18393235605901025352], [1232198224951696867, "unicode_segmentation", false, 13615854330085334395], [1345404220202658316, "fnv", false, 6010475098467100023], [1852463361802237065, "anyhow", false, 16058916671352019268], [2786110112969004522, "mock_instant", false, 10516795787523598114], [4255213592622242520, "typemap_ors", false, 4373931289016908862], [4336745513838352383, "thiserror", false, 14905986212407352756], [5385126873501015737, "serde_value", false, 7077450732482320793], [9614479274285663593, "serde_yaml", false, 3648467489916269289], [10020888071089587331, "<PERSON>ap<PERSON>", false, 1149649148307657031], [11293676373856528358, "derive_more", false, 776144449408508783], [11916940916964035392, "rand", false, 5861174268644945616], [12459942763388630573, "parking_lot", false, 14927329521493169185], [12832915883349295919, "serde_json", false, 3467306232928546916], [13066042571740262168, "log", false, 11379573784715200454], [13548984313718623784, "serde", false, 3541991186150599441], [14076699970472871123, "humantime", false, 2277874052183622229], [14217372430137802392, "thread_id", false, 8865323362553640780], [14306042979243042769, "arc_swap", false, 17277642395082722134], [16037874089270104281, "log_mdc", false, 13586234537923133230]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\log4rs-e5285ddc1735c6e3\\dep-lib-log4rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}