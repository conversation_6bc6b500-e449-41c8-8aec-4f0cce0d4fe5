{"rustc": 7868289081541623310, "features": "[\"chinese-to-number\", \"default\", \"num-bigint\", \"num-traits\", \"number-to-chinese\", \"std\"]", "declared_features": "[\"chinese-to-number\", \"default\", \"num-bigint\", \"num-traits\", \"number-to-chinese\", \"std\"]", "target": 15946960765597942984, "profile": 2241668132362809309, "path": 3202725377534802822, "deps": [[754835383512246836, "enum_ordinalize", false, 17949786331847003206], [5157631553186200874, "num_traits", false, 10352132260801240740], [12528732512569713347, "num_bigint", false, 5233865485256902097], [16908298234001246019, "chinese_variant", false, 8733408031629806377]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chinese-number-33771581a0ec5fb6\\dep-lib-chinese_number", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}