{"rustc": 7868289081541623310, "features": "[\"default\", \"display\", \"error\", \"from\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 16495044275104982277, "deps": [[11082282709338087849, "quote", false, 14408489220704795479], [11688815897905910532, "syn", false, 17177909104522095091], [14285738760999836560, "proc_macro2", false, 13461794086741022396], [16126285161989458480, "unicode_xid", false, 17150054561430191174]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-impl-6fc6c53ec7fc5a55\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}