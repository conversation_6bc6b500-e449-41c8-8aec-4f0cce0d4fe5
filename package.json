{"private": true, "author": "kismet <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@lumino/coreutils": "^2.2.0", "@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-dialog": "^2.0.0", "@tauri-apps/plugin-fs": "^2.0.0", "@tauri-apps/plugin-http": "^2.0.0", "@tauri-apps/plugin-os": "^2.0.0", "@tauri-apps/plugin-process": "^2.0.0", "@tauri-apps/plugin-shell": "^2.2.0", "@umijs/max": "^4.3.14", "antd": "^5.23.1", "echarts": "^5.5.1", "faye": "^1.4.0", "g": "^2.0.1", "jquery": "^3.7.1", "jssha": "^3.3.1", "md5": "^2.3.0", "moment": "^2.30.1", "query-string": "^9.1.0", "react": "^18.3.1", "react-joyride": "^2.9.3", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.14", "@tauri-apps/cli": "^2.0.0", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "tailwindcss": "^3.4.10", "typescript": "^5.0.3"}}