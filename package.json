{"name": "hxr-api", "version": "1.0.0", "description": "", "private": true, "dependencies": {"adm-zip": "^0.5.5", "archiver": "^5.3.0", "axios": "^1.4.0", "bull": "^3.22.7", "cheerio": "^1.0.0-rc.3", "cluster-reload": "^1.0.2", "cnchar": "^3.2.2", "del": "^5.1.0", "egg": "^2.34.0", "egg-bin": "^4.18.1", "egg-mysql": "^3.1.0", "egg-redis": "^2.4.0", "egg-schedule": "^3.6.6", "egg-scripts": "^2.15.3", "egg-sequelize": "^6.0.0", "egg-session": "^3.3.0", "egg-session-redis": "^2.1.0", "egg-validate": "^2.0.2", "events": "^3.3.0", "excel-export": "^0.5.1", "faye": "^1.4.0", "fetch-cookie": "^0.7.2", "file-type": "16.3.0", "fs-extra": "^10.0.1", "http": "^0.0.1-security", "https": "^1.0.0", "iconv-lite": "^0.6.3", "js-levenshtein": "^1.1.6", "json2xls": "^0.1.2", "jssha": "2.3.1", "koa-range": "^0.3.0", "md5": "^2.3.0", "memorystream": "^0.3.1", "mime": "^2.4.6", "moment": "^2.24.0", "mysql2": "^3.11.5", "mz": "^2.7.0", "node-fetch": "^2.6.1", "node-notifier": "^10.0.1", "node-uuid": "^1.4.8", "openai": "^4.103.0", "process": "^0.11.10", "querystring": "^0.2.0", "similarity": "^1.2.1", "stream": "^0.0.2", "stream-wormhole": "^1.1.0", "svg-captcha": "^1.4.0", "tunnel": "^0.0.6", "unzipper": "^0.10.3", "url": "^0.11.0", "xml2js": "^0.4.23", "zlib": "^1.0.5"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.0.0", "egg-mock": "^3.20.1", "eslint": "^5.8.0", "eslint-config-egg": "^7.1.0", "jest": "^29.3.1", "supertest": "^3.4.2", "webstorm-disable-index": "^1.2.0"}, "engines": {"node": ">=10.0.0"}, "scripts": {"start": "egg-scripts start --sticky --title=hxr-api", "stop": "egg-scripts stop --title=hxr-api", "dev": "egg-bin dev --sticky", "debug": "egg-bin debug --sticky", "test": "jest", "test-watch": "npm test --watch"}, "jest": {"verbose": true, "notify": true, "collectCoverage": true, "testEnvironment": "node", "testPathIgnorePatterns": ["__tests__/(fixtures|__mocks__)/"], "coverageReporters": ["html", "text", "text-summary"]}, "ci": {"version": "6"}, "repository": {"type": "git", "url": ""}, "author": "友谱", "license": "COMMERCIAL"}