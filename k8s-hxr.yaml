# API部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: hxr
  labels:
    app: api
spec:
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
#      annotations:
#        k8s.aliyun.com/eci-image-cache: "true" # 如果做了容器加速，就用一下
    spec:
      containers:
      - name: api
        image: registry-vpc.cn-hangzhou.aliyuncs.com/haixr/api
#        imagePullPolicy: IfNotPresent, # 如果做了容器加速，就用一下
        resources:
          requests:
            memory: 8Gi
            cpu: 4
        ports:
          - containerPort: 80
            name: api
        volumeMounts:
        - name: hxr-file-storage
          mountPath: /data/file
        - name: hxr-school-storage
          mountPath: /data/school
        - name: hxr-sharedfiles-storage
          mountPath: /data/shared_files
        env:
        - name: WORK_MODE
          value: prod
      imagePullSecrets:
      - name: yopu-aliyun-docker-login
      volumes:
      - name: hxr-file-storage
        flexVolume:
          driver: alicloud/nas
          options:
            server: 003ec711-1ta2.cn-hangzhou.extreme.nas.aliyuncs.com
            path: /share/hxr/file
            vers: "3.0"
      - name: hxr-school-storage
        flexVolume:
          driver: alicloud/nas
          options:
            server: 003ec711-1ta2.cn-hangzhou.extreme.nas.aliyuncs.com
            path: /share/hxr/school
            vers: "3.0"
      - name: hxr-sharedfiles-storage
        flexVolume:
          driver: alicloud/nas
          options:
            server: 003ec711-1ta2.cn-hangzhou.extreme.nas.aliyuncs.com
            path: /share/hxr/shared_files
            vers: "3.0"
---
# API服务
apiVersion: v1
kind: Service
metadata:
  name: api
  namespace: hxr
  labels:
    app: api
spec:
  ports:
    - port: 80
  selector:
    app: api
---
# API Ingress
# 使用ingress暴露phpmyadmin
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: hxr-api-ingress
  namespace: hxr
  annotations:
    # 配置使用指定的SLB实例（SLB ID）
    service.beta.kubernetes.io/alicloud-loadbalancer-id: lb-bp1iajim76qnsjr2s0u9z            ##替换为你的SLB ID
    service.beta.kubernetes.io/alicloud-loadbalancer-force-override-listeners: "true"
spec:
  rules:
  - host: "api.hxr.iyopu.com"
    http:
      paths:
      - path: /
        backend:
          serviceName: api
          servicePort: 80
---
# icourse控制与监控部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: icourse
  namespace: hxr
  labels:
    app: icourse
spec:
  selector:
    matchLabels:
      app: icourse
  template:
    metadata:
      labels:
        app: icourse
#      annotations:
#        k8s.aliyun.com/eci-image-cache: "true" # 如果做了容器加速，就用一下
    spec:
      containers:
      - name: icourse-worker
        image: registry-vpc.cn-hangzhou.aliyuncs.com/haixr/icourse-worker
#        imagePullPolicy: IfNotPresent, # 如果做了容器加速，就用一下
        resources:
          requests:
            memory: 1Gi
            cpu: 0.5
        volumeMounts:
        - name: hxr-kubeadmin-conf
          mountPath: /etc
        - name: hxr-school-storage
          mountPath: /school
        - name: hxr-yaml-storage
          mountPath: /yaml
        env:
        - name: WORK_MODE
          value: prod
      imagePullSecrets:
      - name: yopu-aliyun-docker-login
      volumes:
      - name: hxr-kubeadmin-conf
        configMap:
          name: kubeadmin
          items:
          - key: kubeadmin.conf
            path: kubeadmin.conf
      - name: hxr-school-storage
        flexVolume:
          driver: alicloud/nas
          options:
            server: 003ec711-1ta2.cn-hangzhou.extreme.nas.aliyuncs.com
            path: /share/hxr/school
            vers: "3.0"
      - name: hxr-yaml-storage
        flexVolume:
          driver: alicloud/nas
          options:
            server: 003ec711-1ta2.cn-hangzhou.extreme.nas.aliyuncs.com
            path: /share/hxr/yaml
            vers: "3.0"
