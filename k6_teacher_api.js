import http from 'k6/http';
import { sleep, check } from 'k6';
// import ws from 'k6/ws';  // 更新为正式的 ws 模块

const SERVER_URL = "http://127.0.0.1:46048";
const site_url = 'http://hxr.iyopu.com/s/csxx';
const username = 'datouxia';
const password = 'a3d375878e587ea81b399fe410a571c635d433cd';
const lab = 'ceshi';
function criticalCheck(response, successMessage) {
  // 首先检查请求是否成功发送
  if (response.error) {
      console.error(`[FATAL] 请求发送失败: ${response.error}`);
      console.error(`请求URL: ${response.url}`);
      console.error(`请求方法: ${response.request.method}`);
      throw new Error(`请求发送失败: ${response.error}`);
  }

  // 打印所有请求的URL和状态码
  console.log(`[REQUEST] ${response.request.method} ${response.url} - 状态码: ${response.status}`);
  
  // 如果请求失败(非200状态码)，打印详细错误信息
  if (response.status !== 200) {
      console.error(`[ERROR] 请求失败: ${successMessage}`);
      console.error(`状态码: ${response.status}`);
      console.error(`请求头: ${JSON.stringify(response.request.headers)}`);
      if (response.request.body) {
          console.error(`请求体: ${response.request.body}`);
      }
      console.error(`响应头: ${JSON.stringify(response.headers)}`);
      console.error(`响应内容: ${response.body}`);
  }
  if (!check(response, { [successMessage]: (r) => r.status === 200 || r.status === 400 || r.status === 403})) {
    console.error(`[FAIL] ${successMessage} - 状态码: ${response.status}`);
    console.error(`响应内容: ${response.body}`);
    return false;
  }
  return response;
  }


// 获取本机MAC地址的函数
function getMacAddress() {
    if (__ENV.MAC_ADDRESS) {
        return __ENV.MAC_ADDRESS;
    }
    
    try {
        const cmd = 'wmic nic where "NetConnectionStatus=2" get MACAddress /value';
        const result = __ENV.K6_EXEC ? __ENV.K6_EXEC(cmd, { output: 'string' }) : '';
        const match = result.match(/MACAddress=([0-9A-F]{2}(:[0-9A-F]{2}){5})/i);
        return match ? match[1] : '00:00:00:00:00:00';
    } catch (e) {
        return '00:00:00:00:00:00';
    }
}

const mac = getMacAddress();

export let options = {
    vus: 1,
    iterations: 1,
    // 设置更严格的阈值
    thresholds: {
      checks: ['rate==1'], // 所有检查必须100%通过
      http_req_failed: ['rate==0'], // 不允许任何HTTP请求失败
    },
    abortOnFail: true
};
// 通用请求头
const headers = {
  'User-Agent': 'tauri-plugin-http/2.0.1',
  'Content-Type': 'application/json',
};

export default function () {
  try {
    // 1. 获取登录配置
    let res = http.get(`${SERVER_URL}/api/admin/section/teacher/login/config`, {
      headers: headers,
      timeout: '30s'  // 设置超时时间
    });
    criticalCheck(res, '获取登录配置成功');
    sleep(1);

  // 2. 远程登录
  const remoteLoginPayload = JSON.stringify({
    site_url: site_url,
    username: username,
    password: password,
    lab: lab,
    mac: mac,
  });
  res = http.post(`${SERVER_URL}/api/admin/course/teacher/remote/login`, remoteLoginPayload, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '远程登录成功');
  sleep(1);

  // 3. 教师登录
  const teacherLoginPayload = JSON.stringify({
    username: username,
    password: password,
  });
  res = http.post(`${SERVER_URL}/api/admin/section/teacher/login`, teacherLoginPayload, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '教师登录成功');
  sleep(1);

  // 4. 获取所有课程
  res = http.get(`${SERVER_URL}/api/admin/course/all`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '获取所有课程成功');
  sleep(1);
  
  // 5. 再次获取登录配置
  res = http.get(`${SERVER_URL}/api/admin/section/teacher/login/config`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '再次获取登录配置成功');
  sleep(1);

  // 6. 停止IPython模块
  res = http.post(`${SERVER_URL}/api/web/course/block/ipython/stop/win10`, null, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '停止IPython模块成功');
  sleep(1);

  
   // 7. 获取所有班级
res = http.get(`${SERVER_URL}/api/admin/allTeams`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取所有班级成功');
sleep(1);

  // 8. 获取2024学年的所有班级
  res = http.get(`${SERVER_URL}/api/admin/course/teacher/school_year/2024%E5%AD%A6%E5%B9%B4/teams`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '获取2024学年班级列表成功');
  sleep(1);

  // 9. 获取指定班级的学生列表
  const teamId = 154; // 从日志中看到的班级ID
  res = http.get(`${SERVER_URL}/api/admin/course/teacher/team/${teamId}/users`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '获取班级学生列表成功');
  sleep(1);

  // 10. 远程获取2024学年的所有班级
  res = http.get(`${SERVER_URL}/api/admin/course/teacher/remote/school_year/2024%E5%AD%A6%E5%B9%B4/all_teams`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '远程获取2024学年班级列表成功');
  sleep(1);

  // 11. 远程获取指定班级的学生列表
  const remoteTeamId = 169; // 从日志中看到的远程班级ID
  res = http.get(`${SERVER_URL}/api/admin/course/teacher/remote/team/${remoteTeamId}/users`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '远程获取班级学生列表成功');
  sleep(1);

  // 12. 远程团队同步检查
  res = http.post(`${SERVER_URL}/api/admin/train/teacher/remote/team/sync/check?team_ids=169,154`, null, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '远程团队同步检查成功');
  sleep(1);

  // 13. 远程团队用户同步 (修改为包含两个团队ID)
  res = http.post(`${SERVER_URL}/api/admin/course/teacher/remote/team/sync/users?team_ids=169,154`, null, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '远程团队用户同步成功');
  sleep(1);

  // 14. 获取课程图片资源
  res = http.get(`${SERVER_URL}/file/course/pltdemo/assets/course.png?t=${new Date().getTime()}`, {
    headers: headers,
    timeout: '30s'  // 设置超时时间
  });
  criticalCheck(res, '获取课程图片资源成功');
  sleep(1);

// // 14.1 WebSocket下载测试
// const wsUrl = `${SERVER_URL.replace('http://', 'ws://')}/ `;
// const downloadUrl = "https://hxr.iyopu.com/s/csxx/file/tmp/氦星人机房教学系统演示课程_20250528140015.zip";

// res = ws.connect(wsUrl, null, function (socket) {
//     socket.on('open', () => {
//         console.log('WebSocket连接已建立');
//         const testMessage = JSON.stringify({
//             type: "courseDownload",
//             url: downloadUrl,
//             courseSlug: "pltdemo",
          
//         });
//         socket.send(testMessage);
        
//         // 设置1分钟等待定时器
//         socket.setTimeout(() => {
//             console.log('1分钟等待完成，关闭连接');
//             socket.close();
//         }, 60000);
//     });

//     socket.on('message', (data) => {
//       console.log(`收到WebSocket消息: ${data}`);
//       try {
//           const msg = JSON.parse(data);
          
//           if (msg.status === "error") {
//               console.error('下载错误详情:');
//               console.error('完整错误响应:', JSON.stringify(msg, null, 2));
//               console.error(`错误类型: ${msg.type || '未知'}`);
//               console.error(`错误消息: ${msg.error || '无详细错误信息'}`);
              
//               if (msg.errorDetails) {
//                   console.error('错误详情:', msg.errorDetails);
//               }
//           }
            
//             // 检查必要字段
//             if (!msg.courseSlug) {
//                 console.error('错误: 缺少课程标识');
//                 return;
//             }
            
//             // 进度更新处理
//             if (msg.percent !== undefined) {
//                 console.log(`下载进度: ${msg.percent}%`);
//                 // 如果进度达到100%，可以提前关闭连接
//                 if (parseInt(msg.percent) >= 100) {
//                     console.log('下载完成，关闭连接');
//                     socket.close();
//                 }
//             }
            
//             // 处理下载完成的情况
//             if (msg.status === "completed") {
//                 console.log('下载完成:', msg.downloadPath || '无路径信息');
//                 socket.close();
//             }
            
//         } catch (e) {
//             console.error('消息解析错误:', e);
//         }
//     });

//     socket.on('close', () => console.log('WebSocket连接已关闭'));
    
//     socket.on('error', (e) => {
//         console.error('WebSocket错误:', e.error);
//         // 更详细的错误处理
//         if (e.response) {
//             console.error('响应状态:', e.response.status);
//             console.error('响应头:', e.response.headers);
//         }
//         throw e.error;
//     });
// });

// check(res, {
//     'WebSocket连接成功': (r) => r && r.status === 101,
//     'WebSocket消息格式正确': (r) => {
//         // 这里可以添加对返回消息的验证逻辑
//         return true;
//     }
// });

// 15. 获取课程索引
res = http.get(`${SERVER_URL}/api/web/course/pltdemo/indices`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取课程索引成功');
sleep(1);

//16. 获取用户会话
res = http.get(`${SERVER_URL}/api/user/session`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取用户会话成功');
sleep(1);

// 17. 启动课程
const startCoursePayload = JSON.stringify({
  courseSlug: "pltdemo"
});
res = http.post(`${SERVER_URL}/api/admin/course/start`, startCoursePayload, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '启动课程成功');
sleep(1);

// 18. 获取课程目录内容
const chapterName = encodeURIComponent('演示课程');
const sectionName = encodeURIComponent('在线编程评测题');
res = http.get(`${SERVER_URL}/api/web/course/pltdemo/directory/${chapterName}/section/${sectionName}`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取课程目录内容成功');
sleep(1);

// 19 准备课程文件系统
res = http.post(`${SERVER_URL}/api/web/course/prepareFS/pltdemo/3013`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '准备课程文件系统成功');
sleep(1);

// 20 启动IPython模块
res = http.post(`${SERVER_URL}/api/web/course/block/ipython/start/win10/pltdemo`, {
  headers: headers,
  timeout: '60s'  // 设置超时时间
});
criticalCheck(res, '启动IPython模块成功');
sleep(1);

// 21 获取课程PPT资源
res = http.get(`${SERVER_URL}/file/course/pltdemo/assets/%E6%B0%A6%E6%98%9F%E4%BA%BA%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%95%99%E8%82%B2%E5%B9%B3%E5%8F%B0%E7%AE%80%E4%BB%8B.pdf`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取课程PDF资源成功');
sleep(1);

// 22. 获取PPT微应用页面
res = http.get(`${SERVER_URL}/microapps/ppt/index.html?microApp=1`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取PPT微应用页面成功');
sleep(1);

// 23. 获取PPT微应用JS资源
res = http.get(`${SERVER_URL}/microapps/ppt/static/js/main.a31f4196.js`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取PPT微应用JS资源成功');
sleep(1);

// 24. 获取PPT微应用CSS资源
res = http.get(`${SERVER_URL}/microapps/ppt/static/css/main.cfd54d2a.css`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取PPT微应用CSS资源成功');
sleep(1);

// 29. 查询章节内容和类型
res = http.get(`${SERVER_URL}/api/course/querySectionContentAndType?courseSlug=pltdemo&chapterName=${chapterName}&sectionName=${sectionName}&ifLoadCode=true&teamId=${teamId}`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '查询章节内容和类型成功');
sleep(1);


// 25.提交OI题目
const oiSubmitPayload = JSON.stringify({
    "courseSlug": "pltdemo",
    "chapterName": "演示课程",
    "sectionName": "客观题",
      "sectionId": 14146,
  
    "questions": {
      "938d0d62dd524c347d2b9a04cefc67a0": {
        "answer": "B",
        "questionType": "单选题"
      },
      "fa19c89efba1f1a8ac8d3affde2c94c2": {
        "answer": "B",
        "questionType": "单选题"
      },
      "027fa12f40f09e76889b286802bf67f0": {
        "answer": "C",
        "questionType": "单选题"
      },
      "af835ce14ec809f2211ff7377c54ce82": {
        "questionType": "文本"
      },
      "f590e5d790361412cf589762625f05c3": {
        "answer": [
          {
            "key": 1,
            "text": "世界1"
          }
        ],
        "questionType": "填空题"
      },
      "238c759f52e9ab124f0fcdd12c9e6533": {
        "answer": {
          "【填空1】": "A",
          "【填空2】": "B",
          "【填空3】": "C",
          "【填空4】": "D"
        },
        "questionType": "选择填空题"
      },
      "4bb71dc64dcb49c672a7dbf7f516904d":{
        "answer":["B","C"],
        "questionType":"多选题"
      }
    }
  }

);
res = http.post(`${SERVER_URL}/api/web/course/section/oi/submit`, oiSubmitPayload, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '提交OI题目成功');
sleep(1);


// 26 提交课程操作题
const operationPayload = JSON.stringify({
  "chapterName": "演示课程",
  "sectionName": "WPS表格操作题",
  "courseSlug": "pltdemo",
  "studentAnswer": {
      "steps": [
          {
              "status": "error"
          },
          {
              "status": "error"
          },
          {
              "status": "error"
          },
          {
              "status": "error"
          }
      ],
      "score": 0
  },
  "sectionId":14148
});
res = http.post(`${SERVER_URL}/api/web/course/section/operation/submit`, operationPayload, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '提交课程操作成功');
sleep(1);

// 27  OJ题目提交
const ojSubmitPayload = JSON.stringify({
  "source": "input_line = input()\r\na, b = map(int, input_line.split())\r\nresult = a + b\r\nprint(result)",
  
  "language": "Python",
  "state": {
    "courseSlug": "pltdemo",
    "chapterName": "演示课程",
    "sectionName": "在线编程评测题",
    "solutionType": "course"
  },
  "solutionType": "course",
  "sectionId":14149
});
res = http.post(`${SERVER_URL}/api/web/course/section/oj/submit`, ojSubmitPayload, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '提交OJ解决方案成功');
sleep(1);

// 28 提交编程填空题
const codeAnswerPayload = JSON.stringify({
  "courseSlug": "pltdemo",
  "chapterName": "演示课程",
  "sectionName": "编程填空题",
  "studentAnswer": {
    "【填空1】": "print"
  },
  "sectionId":14150,
});
res = http.post(`${SERVER_URL}/api/web/course/section/codeAnswer/submit`, codeAnswerPayload, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '提交代码答案成功');
sleep(1);


// 30 查询章节记录
res = http.get(`${SERVER_URL}/api/admin/section/getSectionRecord?courseSlug=pltdemo&chapterName=${chapterName}&sectionName=${sectionName}&teamID=${teamId}`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取章节记录成功');
sleep(1);

// 31. 获取章节结果 
const chapterResultPayload = JSON.stringify({
  courseSlug: "pltdemo",
  chapterName: "演示课程",
  classID: 154,
  selectState: "全部" 
});
res = http.post(`${SERVER_URL}/api/admin/result/chapter`, chapterResultPayload, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取章节结果成功');
sleep(1);

// 32. 获取微应用课程页面
res = http.get(`${SERVER_URL}/microapps/course/index.html`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取微应用课程页面成功');
sleep(1);

// 33. 获取微应用课程CSS
res = http.get(`${SERVER_URL}/microapps/course/umi.css`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取微应用课程CSS成功');
sleep(1);

// 34. 获取微应用课程JS
res = http.get(`${SERVER_URL}/microapps/course/umi.js`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取微应用课程JS成功');
sleep(1);

// 35. 获取微应用课程chunk CSS
res = http.get(`${SERVER_URL}/microapps/course/7133.chunk.css`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取微应用课程chunk CSS成功');
sleep(1);

// 36. 获取微应用课程async JS
res = http.get(`${SERVER_URL}/microapps/course/7133.async.js`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取微应用课程async JS成功');
sleep(1);

// 37. 获取微应用课程页面
res = http.get(`${SERVER_URL}/microapps/course/index.html`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取微应用课程页面成功');
sleep(1);

// 38. 获取spreadsheet微应用页面
res = http.get(`${SERVER_URL}/microapps/spreadsheet/static/js/main.48a121b1.chunk.js`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取spreadsheet微应用JS资源成功');
sleep(1);

// 39. 获取mind微应用页面
res = http.get(`${SERVER_URL}/microapps/mind/assets/zwicon.37e70fc7.ttf`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取mind微应用字体资源成功');
sleep(1);

// 40. 获取drawio微应用页面
res = http.get(`${SERVER_URL}/microapps/drawio/drawio/index.html`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取drawio微应用页面成功');
sleep(1);

//41. 获取networksimulator微应用页面
res = http.get(`${SERVER_URL}/microapps/networksimulator/static/media/ap.85d8dea0.png`, {
  headers: headers,
  timeout: '30s'  // 设置超时时间
});
criticalCheck(res, '获取networksimulator微应用图片资源成功');
sleep(1);

// 42. 重置课程计划
res = http.put(`${SERVER_URL}/api/admin/teacher/course/plan/reset`, null, {
  headers: headers,
});
criticalCheck(res, '重置课程计划成功');
sleep(1);

// 43. 获取终端信息
res = http.get(`${SERVER_URL}/api/admin/terminal`, {
  headers: headers,
});
criticalCheck(res, '获取终端信息成功');
sleep(1);

// 44. 获取在线终端数量
res = http.get(`${SERVER_URL}/api/admin/terminal/onlineCount`, {
  headers: headers,
});
criticalCheck(res, '获取在线终端数量成功');
sleep(1);

// 45. 删除终端
const deleteTerminalPayload = JSON.stringify({
  ips: ["127.0.0.1"],  // 添加必需的ips参数
  clear_user_record_by_ip: true  // 添加必需的参数
});
res = http.post(`${SERVER_URL}/api/admin/teacher/terminal/delete`, deleteTerminalPayload, {
  headers: headers,
});
criticalCheck(res, '删除终端成功');
sleep(1);

// 46. 清空终端
res = http.del(`${SERVER_URL}/api/admin/teacher/terminal/clear`, null, {
  headers: headers,
});
criticalCheck(res, '清空终端成功');
sleep(1);
}catch (e) {
  console.error(`[FATAL ERROR] 测试终止: ${e.message}`);
  // 强制退出脚本并返回非零状态码
  __VU = 0; // 终止当前VU
  __ITER = 0; // 终止当前迭代
  throw e; // 重新抛出异常以确保脚本失败
}
}
