.content {
  // background: url(../../assets/insBag.jpg);
  background-size: 100%;
  // height: 640px;
  // width: 1000px;
  overflow: hidden;
  display: flex;
  user-select: none;
}

.titleBar {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  line-height: 52px;
}

.title {
  padding-left: 12px;
  font-family: sans-serif;
  letter-spacing: 1px;
  font-weight: bold;
  font-size: 18px;
  color: white;
  flex: 1;
}

.close {
  font-family: system-ui;
  height: 40px;
  color: gray;
  margin-top: 6px;
  margin-right: 6px;
  padding: 4px;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.5s ease-in-out;
}

.close:hover {
  transition: all 0.5s ease-in-out;
  opacity: 1;
}

.formLeftContent {
  position: relative;
  height: 100vh;
  width: 51.4vw;
  background-size: cover;

  .footer {
    position: absolute;
    left: 10px;
    bottom: 10px;
    color: #ebe6df;
    font-size: 18px;
  }
}

.formContent {
  background: white;
  height: 100vh;
  margin-left: 45px;

  .forms {
    .header {
      font-family: sans-serif;
      letter-spacing: 1px;
      font-weight: bold;
      font-size: 20px;
      margin-top: 75px;
      margin-bottom: 36px;
    }

    .row {
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 1px;
    }
    // background: gray;
    // border-radius: 15px;
    flex: 1;
    width: 270px;
  }
}

.p1 {
  font-size: 1.4vw;
  font-weight: bold;
  color: black;
  margin-top: 1vw;
}

.logo {
  width: 30%;
  vertical-align: top;
  margin-right: 16px;
  margin-top: 20px;
}

.user {
  height: 42px;
}

:global {
  .ant-layout {
    height: 100%;
  }
}

.password {
  height: 42px;
}

.submit {
  height: 42px;
}

.reBind {
  position: fixed;
  bottom: 5px;
  right: 110px;
  user-select: none;
}

.offline {
  position: fixed;
  bottom: 5px;
  right: 30px;
  user-select: none;
}
