import React, { useState, useEffect } from 'react';
import {Form, Button, Input, Modal, Result, Checkbox} from 'antd';
import imgUrl from '@/assets/loginLeft.png';
import styles from './index.less';
import { alertError } from '../../utils/edittools';
import { checkNetworkStatus, getCookie } from '../../utils/utils.js';
import { getLoginConfig, login as loginSystem, checkTeacherVersion, downloadUpdateFiles, stopIpython } from '../../services/login.js';
import { invoke } from '@tauri-apps/api/core';
import { exit, relaunch } from "@tauri-apps/plugin-process";
import {getCurrentWebviewWindow, WebviewWindow} from "@tauri-apps/api/webviewWindow";
import {get_network_cards_info} from "@/utils/train";
import moment from "moment";
import {decrypt} from "@/utils/crypto";
import JsSHA from "jssha";
import globalVars from "../../utils/global";
import { writeLog } from '@/utils/log';

const { Item: FormItem } = Form;
export default function LoginPage() {
  const [form] = Form.useForm();
  const [orginPassword, setOrginPassword] = useState(null);
  const [schoolName, setSchoolName] = useState('');
  const [versionVisible, setVersionVisible] = useState(false);
  const [changelog, setChangelog] = useState('');
  const [updates, setUpdates] = useState([]);
  const [checkSavePassword, setCheckSavePassword] = useState(false); // 0: init, 1: not save, 2: save;

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const loginConfigResponse = await getLoginConfig();

        if (!loginConfigResponse || loginConfigResponse.code !== 0) {
          alertError("错误", `教师端服务器未正常启动，请检查原因！${loginConfigResponse && loginConfigResponse.message || ''}`);
          return;
        }

        const loginConfig = loginConfigResponse.data;
        console.log("loginConfig = ", loginConfig);

        if (loginConfig.site_url === 'demo') {
          // await handleLogin(loginConfig.default_username, loginConfig.default_password, false);
          if (window.__TAURI__) {
            const webview = new WebviewWindow('startUpWizard', {
              url: '/startUpWizard',
              title: `氦星人日常教学系统${globalVars.appVersion}`,
              width: 1280,
              height: 720,
              center: true,
              decorations: false,
            });

            await webview.once('tauri://created', function () {
              getCurrentWebviewWindow().close('main')
            });
          }
          return;
        }

        const envVar = await invoke("get_env_var",  { environmentName: "HXR-SKIP-LISCENSE" })
        const isSkipLiscense = (envVar === "1");

        const getNetWorkInfo = await get_network_cards_info();

        // 检查授权情况
        let nowIpString = '';

        if (isSkipLiscense) {
          nowIpString = getNetWorkInfo[0].ip;
          localStorage.nowIpString = nowIpString;
          localStorage.mac = getNetWorkInfo[0].mac;
        } else {
          try {
            const licenceText = decrypt(JSON.parse(loginConfig.licence))
            let licence = JSON.parse(licenceText)
            // console.log('licence:',licence);
            // console.log('getNetWorkInfo:',getNetWorkInfo);
            const netWorkIndex = getNetWorkInfo.findIndex(row => row.mac === licence.mac);

            if (netWorkIndex === -1) {
              alertError("错误", `请检查您的网卡是否有被禁用！您之前的mac地址为${licence.mac}。`)
              return;
            }

            const nowIpRow = getNetWorkInfo[netWorkIndex];
            nowIpString = nowIpRow.ip;

            localStorage.nowIpString = nowIpString;
            localStorage.mac = nowIpRow.mac;

            if(moment().isAfter(moment(licence.toDate).endOf('day')) || moment().isBefore(moment(licence.fromDate).startOf('day'))) {
              alertError("错误", '您的授权已到期，请联系025-83466679购买')
              return;
            }

            switch(licence.status) {
              case 'ok':
                // 通过不处理
                break;
              case 'overdue':
                alertError("错误", '您没有可用授权，请联系025-83466679购买')
                return;
              // break;
              case 'empty':
              default:
                if (licence.availableLicenseCount > 0) {
                  // 授权
                  // 通过不处理
                  // ctxBody = { licence: encrypt(JSON.stringify(permissionResult)), code: 0, message: "成功", schoolName: user.schoolName, displayName: user.name, csrfToken: session.csrfToken, user };
                  break;
                } else if (licence.totalLicenseCount) {
                  alertError("错误", '您购买的授权已用尽，请联系025-83466679购买')
                  return;
                } else {
                  alertError("错误", '您尚未购买过任何授权，请联系025-83466679购买')
                  return;
                }
            }
            // console.log('licence.status', licence.status)
          } catch(e) {
            console.log('e:',e)
            alertError("错误", e.message)
          }
        }

        console.log(checkSavePassword, loginConfig.save !== undefined, loginConfig.save === "false");
        if (loginConfig.save !== undefined && loginConfig.save === "false") {
          setCheckSavePassword(false);
        } else {
          if (loginConfig.save !== undefined) {
            setCheckSavePassword(JSON.parse(loginConfig.save));
          }
          // 在数据获取后
          form.setFieldsValue({
            username: loginConfig.default_username,
            password: loginConfig.default_password,
          });
          setOrginPassword(loginConfig.default_password);
        }
        console.log("#@@@@@@@@@", checkSavePassword);

        setSchoolName(loginConfig.school_name);
        localStorage.nowIpString = nowIpString;
      } catch (e) {
        alertError("错误", e.message)
      }
    };

    fetchInitialData();
  }, []);

  const handleLogin = async (username, password, encrypt = true) => {
    let encryptPassword = password;
    if (password !== orginPassword && encrypt) {
      const shaObj = new JsSHA('SHA-1', 'TEXT');
      shaObj.update(password);
      encryptPassword = shaObj.getHash('HEX');
    }

    try {
      const result = await loginSystem({ username, password: encryptPassword, save: checkSavePassword });

      if (!result || result.code) {
        alertError("错误", result.message || '接口错误');
        return;
      }

      localStorage.sessionResponse = JSON.stringify(result.data);

      if (window.__TAURI__) {
        const webview = new WebviewWindow('teacherView', {
          url: '/course/courseList',
          title: `氦星人日常教学系统(教师端) ${globalVars.appVersion}`,
          fullscreen: false,
          maximized: true,
          width: 1280,
          height: 720,
          decorations: false
        });

        await webview.once('tauri://created', function () {
          getCurrentWebviewWindow().close('main')
        });
      }
    } catch (error) {
      alertError("错误", error.message);
    }
  };

  const handleCheckUpdate = async () => {
    try {
      const result = await checkTeacherVersion(globalVars.appVersion);
      if (!result || result.code) {
        alertError("错误", result.message);
        return;
      }

      setChangelog(result.data.changelog);
      setUpdates(result.data.updates);
      setVersionVisible(true);
    } catch (error) {
      alertError("错误", error.message);
    }
  };

  const handleUpdateFiles = async () => {
    try {
      const result = await downloadUpdateFiles({ updates });
      if (!result || result.code) {
        alertError("错误", result.message);
        return;
      }

      if (result.data) {
        await invoke('upgrade');
      }

      setVersionVisible(false);
    } catch (error) {
      alertError("错误", error.message);
    }
  };

  const successResult = (
    <Result
      style={{ padding: 0 }}
      status="success"
      title="氦星人日常教学系统已是最新版本!"
      extra={
        <Button key="close" onClick={() => setVersionVisible(false)}>
          关闭
        </Button>
      }
    />
  );

  const needUpdateResult = (
    <Result
      style={{ padding: 0 }}
      title="有新版本"
      subTitle={<pre style={{ maxHeight: '200px', overflow: 'auto' }}>{changelog}</pre>}
      extra={[
        <Button type="primary" key="update" onClick={handleUpdateFiles}>
          更新
        </Button>,
        <Button key="close" onClick={() => setVersionVisible(false)}>
          关闭
        </Button>,
      ]}
    />
  );

  const onChange = (e) => {
    setCheckSavePassword(e.target.checked);
    console.log(`checked = ${e.target.checked}`);
  };

  return (
    <div style={{ height: '100%' }}>
      <div>
        <style>
          {`
            .${styles.row} .ant-input-affix-wrapper {
              border: none !important;
            }
            .${styles.row} .ant-input {
              border: none !important;
            }
            .${styles.row} .ant-form-item-row {
              border-bottom: 1px solid black !important;
            }
          `}
        </style>
        {/*<div className={styles.title}>氦星人日常教学系统 Version：{globalVars.appVersion}</div>*/}
        <div className={styles.content}>
          <div className={styles.formLeftContent} style={{backgroundImage: `url(${imgUrl})`}}>
            <div className={styles.footer}>授权给 {schoolName} 使用</div>
          </div>
          <div
            className={styles.titleBar}
            onMouseDown={(e) => {
              e.preventDefault();

              // 调用tauri的窗口拖拽功能
              getCurrentWebviewWindow().startDragging();
            }}
          >
            <h1 className={styles.title}>氦星人日常教学系统 V{globalVars.appVersion}</h1>
            <img
                className={styles.close}
                src={require("@/assets/minWindow.png")}
                onMouseDown={(e) => {
                  e.stopPropagation();
                }}
                onClick={async () => {
                  try {
                    await getCurrentWebviewWindow().minimize();
                  } catch (err) {
                    console.error('Minimize failed:', err);
                  }
                }}
            />
            <img
              className={styles.close}
              src={require("@/assets/close_gray.png")}
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
              onClick={async () => {
                // 退出程序
                await localStorage.removeItem('sessionResponse')
                await stopIpython(globalVars.winVersion);
                await invoke('stop_server');
                await exit(0);
              }}
            />
          </div>
          <div className={styles.formContent}>
            <div className={styles.forms}>
              <div className={styles.header}>教师登陆</div>
              <Form
                form={form}
                onFinish={(values) => {
                  handleLogin(values.username, values.password, true);
                }}
              >
                <div className={styles.row}>账号</div>
                <FormItem
                  className={styles.row}
                  name="username"
                  rules={[{required: true, message: '请输入您的账号！'}]}
                >
                  <Input autoComplete="off" placeholder="同步按钮可将账号同步至此"/>
                </FormItem>
                <div className={styles.row}>密码</div>
                <FormItem
                  className={styles.row}
                  name="password"
                  rules={[{required: true, message: '请输入您的密码！'}]}
                >
                  <Input.Password autoComplete="new-password" placeholder="请注意密码大小写和特殊符号"/>
                </FormItem>
                <FormItem>
                  <Checkbox checked={checkSavePassword} onChange={onChange}>保存账号密码</Checkbox>
                </FormItem>
                <FormItem>
                  <Button
                    htmlType="submit"
                    className={styles.submit}
                    style={{width: '100%'}}
                    type="primary"
                  >
                    系统登录
                  </Button>
                </FormItem>
                {/* <FormItem>
                    <Button
                      className={styles.submit}
                      style={{ width: '100%' }}
                      // type="primary"
                      onClick={() => handleCheckUpdate()}
                    >
                      检测更新
                    </Button>
                  </FormItem> */}
              </Form>
            </div>
          </div>
        </div>

        <Button
          type="link"
          className={styles.reBind}
          onClick={async () => {
            const networkStatus = await checkNetworkStatus();
            if (!networkStatus) {
              alertError('当前设备已离线，请使用离线同步');
              return;
            }

            if (window.__TAURI__) {
              const webview = new WebviewWindow('startUpWizard', {
                url: '/startUpWizard',
                title: `氦星人日常教学系统${globalVars.appVersion}`,
                width: 1280,
                height: 720,
                center: true,
                decorations: false,
              });

              await webview.once('tauri://created', function () {
                getCurrentWebviewWindow().close('main')
              });
            }
          }}
        >
          教师账号同步
        </Button>

        {/*<Button*/}
        {/*  type="link"*/}
        {/*  className={styles.offline}*/}
        {/*  onClick={() => {*/}
        {/*    if (window.__TAURI__) {*/}
        {/*      const webview = new WebviewWindow('offlineSync', {*/}
        {/*        url: '/offlineSyncGlobal',*/}
        {/*        width: 1280,*/}
        {/*        height: 720*/}
        {/*      });*/}

        {/*      webview.once('tauri://created', function () {*/}
        {/*        getCurrentWebviewWindow().close('main')*/}
        {/*      });*/}
        {/*    }*/}
        {/*  }}*/}
        {/*>*/}
        {/*  离线同步*/}
        {/*</Button>*/}

        <Modal open={versionVisible} footer={null} onCancel={() => setVersionVisible(false)}>
          {updates.length ? needUpdateResult : successResult}
        </Modal>
      </div>
    </div>
  );
};
