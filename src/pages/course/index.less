.sectionTitle {
  text-align: center;
  width: 100%;
  height: 26px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.answerSty {
  margin: 10px 0;
}

.renderCodeDiv {
  position: absolute;
  right: 24px;
  letter-spacing: 2px;
}

.renderText {
  padding: 0 5px;
  word-break: break-all;
  border: 0;
  background: none;
  margin: 0;
}

.renderError {
  width: 100%;
  overflow: auto;
  padding: 1px 5px;
  background: lightGrey !important;
}

.mask {
  position: absolute;
  top: 56px;
  background: red;
  width: 100%;
  height: 100%;
  opacity: 0;
}

//.win7Fix {
//  /* 防止 tree-shaking 的占位样式 */
//  /* 不能是注释，必须是真实 CSS */
//  /* display 或 position 不影响布局 */
//  position: relative;
//
//  //.ant-table-body {
//  //  overflow: scroll !important;
//  //}
//  //
//  //.ant-table-body::-webkit-scrollbar {
//  //  width: 12px;
//  //  height: 12px;
//  //}
//  //.ant-table-body::-webkit-scrollbar-track {
//  //  background: #f0f0f0;
//  //  border: 1px solid #ddd;
//  //}
//  //.ant-table-body::-webkit-scrollbar-thumb {
//  //  background: #aaa;
//  //  border-radius: 4px;
//  //}
//}

/* 显式写后代选择器 */
//.win7Fix .ant-table-body {
//  overflow: scroll !important;
//}
//
//.win7Fix .ant-table-body::-webkit-scrollbar {
//  width: 12px;
//  height: 12px;
//  background: #f0f0f0;
//}
//
//.win7Fix .ant-table-body::-webkit-scrollbar-track {
//  background: #f0f0f0;
//  border: 1px solid #ddd;
//}
//
//.win7Fix .ant-table-body::-webkit-scrollbar-thumb {
//  background: #aaa;
//  border-radius: 4px;
//}

//.win7Fix {
//  position: relative;
//
//  /* 在 .win7Fix 内部，用 :global 包裹 .ant-table-body */
//  :global(.ant-table-body) {
//    overflow: scroll !important;
//  }
//
//  :global(.ant-table-body)::-webkit-scrollbar {
//    width: 12px;
//    height: 12px;
//    background: #f0f0f0;
//  }
//
//  :global(.ant-table-body)::-webkit-scrollbar-track {
//    background: #f0f0f0;
//    border: 1px solid #ddd;
//  }
//
//  :global(.ant-table-body)::-webkit-scrollbar-thumb {
//    background: #aaa;
//    border-radius: 4px;
//  }
//}

.win7Fix {
  position: relative;

  :global(.ant-table-body) {
    /* 强制显示滚动条按钮 */
    overflow: scroll !important;
  }

  /* 纵向滚动条 */
  :global(.ant-table-body)::-webkit-scrollbar {
    width: 16px; /* 加宽以容纳箭头 */
    height: 16px; /* 加宽以容纳箭头 */
  }

  /* 滚动条轨道 */
  :global(.ant-table-body)::-webkit-scrollbar-track {
    background: #f0f0f0;
    border: 1px solid #ddd;
  }

  /* 滚动条滑块 */
  :global(.ant-table-body)::-webkit-scrollbar-thumb {
    background: #aaa;
    border-radius: 10px;
    /* 与轨道的间隔：用 border 模拟 */
    border: 4px solid transparent;
    background-clip: padding-box; /* 关键：让 border 不覆盖背景 */
  }

  /* 滚动条按钮（箭头） - 上/下/左/右 */
  :global(.ant-table-body)::-webkit-scrollbar-button {
    display: inline-block !important;
    background-color: #e0e0e0;
    height: 16px;
    width: 16px;
    background-repeat: no-repeat;   /* 👈 关键：禁止重复 */
    background-position: center;    /* 👈 居中 */
    background-size: 12px 12px;     /* 👈 固定尺寸 */
  }

  //:global(.ant-table-body)::-webkit-scrollbar-button:vertical {
  //  display: inline-block !important;
  //  width: 20px;
  //  height: 20px; /* 与滚动条高度一致 */
  //  background-color: #e0e0e0;
  //  background-repeat: no-repeat;   /* 👈 关键：禁止重复 */
  //  background-position: center;    /* 👈 居中 */
  //  background-size: 12px 12px;     /* 👈 固定尺寸 */
  //}

  /* 上箭头 */
  :global(.ant-table-body)::-webkit-scrollbar-button:vertical:start {
    /* 可用 background-image 自定义箭头 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24'%3E%3Cpath fill='%23666' d='M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z'/%3E%3C/svg%3E");
    //background-repeat: no-repeat;
    //background-position: center;
  }

  /* 下箭头 */
  :global(.ant-table-body)::-webkit-scrollbar-button:vertical:end {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24'%3E%3Cpath fill='%23666' d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z'/%3E%3C/svg%3E");
    //background-repeat: no-repeat;
    //background-position: center;
  }
  //:global(.ant-table-body)::-webkit-scrollbar-button:horizontal {
  //  display: inline-block !important;
  //  width: 20px;
  //  height: 20px; /* 与滚动条高度一致 */
  //  background-color: #e0e0e0;
  //  background-repeat: no-repeat;   /* 👈 关键：禁止重复 */
  //  background-position: center;    /* 👈 居中 */
  //  background-size: 12px 12px;     /* 👈 固定尺寸 */
  //}

  :global(.ant-table-body)::-webkit-scrollbar-button:horizontal:start {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24'%3E%3Cpath fill='%23666' d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E");
  }

  :global(.ant-table-body)::-webkit-scrollbar-button:horizontal:end {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24'%3E%3Cpath fill='%23666' d='M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z'/%3E%3C/svg%3E");
  }
}