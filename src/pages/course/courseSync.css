/* 课程同步相关样式 */

/* 同步控制卡片动画 */
.sync-control-card {
  transition: all 0.3s ease;
}

.sync-control-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* 状态徽章动画 */
.status-badge {
  transition: all 0.2s ease;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 同步按钮特效 */
.sync-button {
  position: relative;
  overflow: hidden;
}

.sync-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.sync-button:hover::before {
  width: 300px;
  height: 300px;
}

/* 状态标签悬停效果 */
.section-status-tag {
  transition: all 0.2s ease;
}

.section-status-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 加载中动画 */
.sync-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 进度条样式增强 */
.ant-progress-bg {
  transition: all 0.3s ease !important;
}

/* 检查更新按钮旋转动画 */
.checking-spin {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 成功提示动画 */
.sync-success {
  animation: bounceIn 0.5s ease;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .sync-control-card {
    max-width: 100% !important;
    margin: 1rem auto;
  }
  
  .status-badge {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .sync-control-card {
    background-color: #1f1f1f;
    border: 1px solid #333;
  }
  
  .section-status-tag {
    filter: brightness(1.2);
  }
}


