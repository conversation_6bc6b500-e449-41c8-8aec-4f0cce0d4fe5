import React, {useContext, useEffect, useRef, useState} from 'react';
import { useModel } from '@umijs/max';
import { Row, Col, Tooltip, Tree, Steps, Button, Dropdown, Space, Select, Radio, Table, Popover, Affix, Modal } from 'antd';
import MinusCircleOutlined from '@ant-design/icons/MinusCircleOutlined';
import CloseCircleOutlined from '@ant-design/icons/CloseCircleOutlined';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import { history } from '@umijs/max'
import styles from './index.less'
import { alertError } from '@/utils/edittools';
import {exportCourseProgressExcel, finishClass, getChapterProgress, getCourseDetail, getSectionProgress, startClass} from "@/services/course";
import {SessionContext} from "@/services/user";
import globalVars from '@/utils/global';
import { DownOutlined, FullscreenExitOutlined, FullscreenOutlined, LineChartOutlined, PoweroffOutlined, StarFilled } from '@ant-design/icons';
import BarChart from "@/components/Form/BarChart";
import {getClassList} from "@/services/team";
import moment from "moment/moment";
import ExportButton from "@/components/Form/ExportButton";
import requestServer from '@/utils/requestServer';
const { Option } = Select;

// 导出章
const exportChapterData = (dataSource, dataRes) => {
  const dataNew = [];
  for (const dataV of dataRes) {
    dataNew.push(dataV.sectionName);
  }
  let i = 1;

  const exportList = [];
  const datas = [];
  for (const row of dataSource) {
    for (const item in row) {
      if (datas.indexOf(item) !== -1) {
        continue;
      }
      if (typeof row[item] === 'object') {
        continue;
      }
      datas.push(item);
    }
  }
  for (const row of dataSource) {
    let value = {};
    for (const data of datas) {
      value = {
        ...value,
        serialNumber: i,
        [` ${data} `]: row[data] ? row[data] : 0,
      };
    }
    exportList.push(value);
    i += 1;
  }

  const titleValue = {
    serialNumber: '序号',
  };

  for (const v of datas) {
    if (v === 'id') {
      continue;
    }
    if (v === 'username') {
      titleValue[v] = '账号';
      continue;
    }
    if (v === 'name') {
      titleValue[v] = '用户名';
      continue;
    }
    titleValue[v] = v;
  }
  const valueRes = {
    serialNumber: '序号',
    ' username ': '账号',
    ' name ': '用户名',
  };
  for (const a of dataNew) {
    if (titleValue[a]) {
      valueRes[` ${a} `] = a;
    }
  }
  exportList.unshift(valueRes);

  return exportList;
};

// 导出节
const exportSectionData = (dataSource, keyMap, config) => {
  let i = 1;
  const exportList = [];

  // 遍历全部列，寻找可用题目
  const colNames = [];
  for (const row of dataSource) {
    for (const item in row) {
      if (colNames.indexOf(item) !== -1) {
        continue;
      }
      if (typeof row[item] === 'object') {
        continue;
      }

      if (['UUID', 'totalScore', 'passCount', 'codeCount', 'index'].includes(item)) {
        continue;
      }
      colNames.push(item);
    }
  }

  // 按照 keyMap 排序 colNames
  const sortedColNames = [];
  const basicColumns = ['id', 'name', 'sen', 'status', 'username'];
  const keyMapColumns = [];
  const otherColumns = [];

  // 分类列名
  for (const colName of colNames) {
    if (basicColumns.includes(colName)) {
      sortedColNames.push(colName);
    } else {
      // 检查是否是 keyMap 中的题目或对应的学生答案
      let isKeyMapColumn = false;
      for (const key in keyMap) {
        const codeKey = keyMap[key];
        const answerKey = `${codeKey}学生答案`;
        if (colName === codeKey || colName === answerKey) {
          isKeyMapColumn = true;
          break;
        }
      }

      if (isKeyMapColumn) {
        keyMapColumns.push(colName);
      } else {
        otherColumns.push(colName);
      }
    }
  }

  // 按照 keyMap 的顺序排列，每个代码题目后面紧跟对应的学生答案
  const sortedKeyMapColumns = [];
  // 获取 keyMap 的最大键值
  const maxKey = Math.max(...Object.keys(keyMap).map(k => parseInt(k)));

  for (let i = 1; i <= maxKey; i++) {
    const codeKey = keyMap[i.toString()];
    const answerKey = `${codeKey}学生答案`;

    // 先添加代码题目
    if (keyMapColumns.includes(codeKey)) {
      sortedKeyMapColumns.push(codeKey);
    }
    // 然后添加对应的学生答案
    if (keyMapColumns.includes(answerKey)) {
      sortedKeyMapColumns.push(answerKey);
    }
  }

  // 合并所有列：基础列 + 按顺序排列的 keyMap 列 + 其他列
  const finalColNames = [...sortedColNames, ...sortedKeyMapColumns, ...otherColumns];

  finalColNames.push('rightNumber', 'wrongNumber', 'notRunNumber');

  console.log('排序后的 colNames:', finalColNames);

  // 使用排序后的列名
  const colNamesOrdered = finalColNames;

  for (const row of dataSource) {
    let value = {};
    for (const colName of colNamesOrdered) {
      value = {
        ...value,
        serialNumber: i,
        [colName]: row[colName] ? row[colName] : '',
      };
    }

    exportList.push(value);
    i += 1;
  }

  for (const row of exportList) {
    const { id, name, username, serialNumber, status, ...values } = row;
    row.rightNumber = 0;
    row.wrongNumber = 0;
    row.notRunNumber = 0;

    for (const value in values) {
      switch (values[value]) {
        case '正确':
        case '运行通过':
        case 'AC':
          row.rightNumber++;
          break;
        case '错误':
        case '运行报错':
        case 'RE':
        case 'TL':
        case 'WA':
        case 'PE':
        case 'RI':
          row.wrongNumber++;
          break;
        case '未答题':
        case '未运行':
          row.notRunNumber++;
          break;
        default:
          break;
      }
    }
  }

  // 选择导出客观题答案
  if (config === 'OIAnswer') {
    for (const row of exportList) {
      const { id, name, username, serialNumber, rightNumber, wrongNumber, notRunNumber, ...values } = row;
      const currentRow = dataSource.find((r) => r.id === id);

      for (const data in values) {
        if (config === 'OIAnswer' && currentRow.answer) {
          row[data] = currentRow.answer[data] || currentRow[data];
        }
      }
    }
  }

  const titleValue = {
    serialNumber: '序号',
  };
  for (const v of colNamesOrdered) {
    if (v === 'id' || v === 'submitTime' || v === 'status') {
      continue;
    }
    if (v === 'username') {
      titleValue[v] = '账号';
      continue;
    }
    if (v === 'name') {
      titleValue[v] = '用户名';
      continue;
    }

    if (v === 'rightNumber') {
      titleValue[v] = '正确数';
      continue;
    }

    if (v === 'wrongNumber') {
      titleValue[v] = '错误数';
      continue;
    }

    if (v === 'notRunNumber') {
      titleValue[v] = '未答题数';
      continue;
    }
    if (typeof colNamesOrdered[v] === 'object') {
      continue;
    }

    titleValue[v] = v;
  }
  exportList.unshift(titleValue);
  return exportList;
};

// 导出课程
const exportCourseData = (dataSource, keyMap) => {
  const exportList = dataSource;
  const dataKeys = Object.keys(dataSource[0]);

  console.log('exportCourseData dataKeys:', dataKeys);
  console.log('exportCourseData keyMap:', keyMap);

  // 按照 keyMap 排序 dataKeys
  const sortedDataKeys = [];
  const basicColumns = ['id', 'name', 'serialNumber', 'username'];
  const keyMapColumns = [];
  const otherColumns = [];

  // 分类列名
  for (const colName of dataKeys) {
    if (basicColumns.includes(colName)) {
      sortedDataKeys.push(colName);
    } else if (['passSectionNumber', 'sectionNumbers', 'passNumber'].includes(colName)) {
      // 跳过这些列
      continue;
    } else {
      // 检查是否是 keyMap 中的章节
      let isKeyMapColumn = false;
      if (keyMap) {
        for (const key in keyMap) {
          const chapterKey = keyMap[key];
          if (colName === chapterKey) {
            isKeyMapColumn = true;
            break;
          }
        }
      }

      if (isKeyMapColumn) {
        keyMapColumns.push(colName);
      } else {
        otherColumns.push(colName);
      }
    }
  }

  // 按照 keyMap 的顺序排列章节列
  const sortedKeyMapColumns = [];

  if (keyMap) {
    // 获取 keyMap 的最大键值
    const maxKey = Math.max(...Object.keys(keyMap).map(k => parseInt(k)));

    for (let i = 1; i <= maxKey; i++) {
      const chapterKey = keyMap[i.toString()];

      if (keyMapColumns.includes(chapterKey)) {
        sortedKeyMapColumns.push(chapterKey);
      }
    }
  }

  // 合并所有列：基础列 + 按顺序排列的 keyMap 列 + 其他列
  const finalDataKeys = [...sortedDataKeys, ...sortedKeyMapColumns, ...otherColumns];

  console.log('排序后的 dataKeys:', finalDataKeys);

  const titleValue = {
    serialNumber: '序号',
  };

  for (const v of finalDataKeys) {
    if (['id', 'passSectionNumber', 'sectionNumbers', 'passNumber'].includes(v)) {
      continue;
    }
    if (v === 'serialNumber') {
      titleValue[v] = '序号';
      continue;
    }
    if (v === 'username') {
      titleValue[v] = '账号';
      continue;
    }
    if (v === 'name') {
      titleValue[v] = '用户名';
      continue;
    }
    titleValue[v] = v;
  }
  const valueRes = {
    serialNumber: '序号',
    username: '账号',
    name: '用户名',
  };
  for (const a of finalDataKeys) {
    if (titleValue[a]) {
      valueRes[`${a}`] = titleValue[a];
    }
  }

  exportList.unshift(valueRes);
  return exportList;
};

const currentStudyYear = () => {
  const month = moment().format('MM');
  if (parseInt(month) <= 7) {
    return `${moment().subtract(1, 'years').format('YYYY')}学年`;
  }

  return `${moment().format('YYYY')}学年`;
};

const renderSelectYear = () => {
  const currentYear = moment().format('YYYY');
  const currentMonth = moment().format('YYYYMM');

  // const currentYear = 2025;
  // const currentMonth = 202507;

  const year =
    currentMonth > `${moment().format('YYYY')}07`
      ? currentYear
      : currentYear - 1;
  let arr = [2020];
  if (parseInt(currentYear) > 2020) {
    for (let i = 2020; i <= year; i++) {
      if (!arr.includes(i)) {
        arr.push(i);
      }
    }
  }
  arr.sort((a, b) => b - a);
  return arr.map((i) => (
    <Option key={`${i}学年`} value={`${i}学年`}>
      {i}
      学年
    </Option>
  ));
};

// 完成将 toChineseNum， 可以将数字转换成中文大写的表示，处理到万级别，例如 toChineseNum(12345)，返回 一万二千三百四十五。
const toChinesNum = (num) => {
  const changeNum = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']; // changeNum[0] = "零"
  const unit = ['', '十', '百', '千', '万'];
  num = parseInt(num);
  const getWan = (temp) => {
    const strArr = temp.toString().split('').reverse();
    let newNum = '';
    for (let i = 0; i < strArr.length; i++) {
      newNum = (i === 0 && strArr[i] === 0 ? '' : (i > 0 && strArr[i] === 0 && strArr[i - 1] === 0 ? '' : changeNum[strArr[i]] + (strArr[i] === 0 ? unit[0] : unit[i]))) + newNum;
    }

    return newNum.replace(/^一十/, '十');
  };
  const overWan = Math.floor(num / 10000);
  let noWan = num % 10000;
  if (noWan.toString().length < 4) noWan = `0${noWan}`;
  return overWan ? `${getWan(overWan)}万${getWan(noWan)}` : getWan(num);
};

const { DirectoryTree } = Tree;
const imgMap = {
  AI: require('../../assets/courseDetail/ai.png'),
  OJ: require('../../assets/courseDetail/oj.png'),
  OI: require('../../assets/courseDetail/oi.png'),
  MicroBit: require('../../assets/courseDetail/microbit.png'),
  PPT: require('../../assets/courseDetail/powerpoint.png'),
  Scratch: require('../../assets/courseDetail/scratch.png'),
  Access: require('../../assets/courseDetail/mdb.png'),
  Excel: require('../../assets/courseDetail/excel.png'),
  CodeBlank: require('../../assets/courseDetail/codeBlank.png'),
};

const imgMapWhite = {
  AI: require('../../assets/courseDetail/ai_white.png'),
  OJ: require('../../assets/courseDetail/oj_white.png'),
  OI: require('../../assets/courseDetail/oi_white.png'),
  MicroBit: require('../../assets/courseDetail/microbit_select.png'),
  PPT: require('../../assets/courseDetail/powerpoint_select.png'),
  Scratch: require('../../assets/courseDetail/scratch_select.png'),
  Access: require('../../assets/courseDetail/mdb_select.png'),
  Excel: require('../../assets/courseDetail/excel_select.png'),
  CodeBlank: require('../../assets/courseDetail/codeBlank_select.png'),
};

const checkIsOpen = (tree) => {
  if (tree.isOpen === false || tree.isOpen) {
    return tree.isOpen;
  }
  const { children } = tree;
  if (!children || !Array.isArray(children) || !children.length) {
    return false;
  }

  return children.some((child) => checkIsOpen(child));
};

const colorMap = {
  运行通过: '#529652',
  已读: '#529652',
  运行报错: 'rgba(228, 42, 42, 0.83)',
  未答题: '#b9b8b8',
  未读: '#b9b8b8',
  未运行: '#ff9800',
  未评分: '#ff9800',
  已判分: '#ff9800',
  正确: '#529652',
  已提交: '#529652',
  错误: 'rgba(228, 42, 42, 0.83)',
  TL: '#ff9800',
};

const renderBackgroundColor = (data, total) => {
  if (!data && parseInt(data) !== 0) {
    return colorMap['未运行'];
  }
  if (data === 'WA' || data === 'RE' || data === 'PE' || data === 'RI') {
    return colorMap['运行报错'];
  }
  if (data === 'TL') {
    return colorMap['TL'];
  }
  if (data === 'AC') {
    return colorMap['运行通过'];
  }

  if (total && parseInt(data) >= total) {
    return colorMap['运行通过'];
  }

  if (total && parseInt(data) < total) {
    return colorMap['已判分'];
  }

  if (parseInt(data) < 100) {
    return colorMap['运行报错'];
  }
  if (parseInt(data) >= 100) {
    return colorMap['运行通过'];
  }

  return colorMap[data];
};

// 在组件顶部添加一个辅助函数来管理本地存储
const getStoredExpandedKeys = (courseSlug) => {
  const stored = localStorage.getItem(`expandedKeys_${courseSlug}`);
  return stored ? JSON.parse(stored) : [];
};

const setStoredExpandedKeys = (courseSlug, keys) => {
  localStorage.setItem(`expandedKeys_${courseSlug}`, JSON.stringify(keys));
};

export default function CourseDetailPage() {
  const [courseDetail, setCourseDetail] = useState(null);
  const { setNavName } = useModel('global');
  // 获取用户信息
  const session = useContext(SessionContext);
  // 获取地址栏参数
  const url = window.location.href;
  const parts = url.split('/');
  const courseSlug = parts[parts.length - 1];

  const rightColRef = useRef(null);
  const [fullScreen, setFullScreen] = useState(false);
  const [courseProgressParams, setCourseProgressParams] = useState({});
  const [selectedKey, setSelectedKey] = useState('');
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [classList, setClassList] = useState([]);
  const [selectSchoolYear, setSelectSchoolYear] = useState(currentStudyYear());
  const [selectClassID, setSelectClassID] = useState(null);
  const [selectVisual, setSelectVisual] = useState('综合统计');
  const [selectSort, setSelectSort] = useState('账号升序(仅数字排序)');
  const [selectState, setSelectState] = useState('全部');
  const [dataSource, setDataSource] = useState(null);
  const [staticsResult, setStaticsResult] = useState([]);
  const [dataSourceCount, setDataSourceCount] = useState(0);
  const [sectionType, setSectionType] = useState(null);
  const [AISectionKeyMap, setAISectionKeyMap] = useState({});
  const [sectionNames, setSectionNames] = useState([]);
  const [chartDataByQuestion, setChartDataByQuestion] = useState([]);
  const [chartDataByPeople, setChartDataByPeople] = useState([]);
  const [checkTeamType, setCheckTeamType] = useState(true);
  const [scoringAverageStart, setScoringAverageStart] = useState(0);
  const [scoringAverageEnd, setScoringAverageEnd] = useState(100);
  const exportRef = useRef();
  const [courseProgressDetailOpen, setCourseProgressDetailOpen] = useState(false);
  const [iFrameUrl, setIFrameUrl] = useState('');
  const [tableHeight, setTableHeight] = useState(0);
  const devRef = useRef(null);

  // 动态计算 Col 的高度
  useEffect(() => {
    const handleResize = () => {
      if (devRef.current) {
        const height = devRef.current.clientHeight;
        setTableHeight(height);
        if (fullScreen) {
          setTableHeight(height + 120);
        }
      }
    };

    // 初始计算高度
    handleResize();

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    // 清除监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [selectSchoolYear, selectClassID, selectedKey, courseProgressParams, fullScreen]);

  useEffect(() => {
    // 监听来自 iframe 的消息
    const messageHandler = (event) => {
      // 检查消息来源和类型
      if (event.data.type === 'iframe-ready') {
        console.log('Iframe is ready!');
        //http://hxr.iyopu.com/ok
        // 向 iframe 发送 Tauri 的数据或其他信息
        const iframe = document.getElementById('courseProgressDetailFrame');
        if (iframe) {
          iframe.contentWindow.postMessage({ type: 'iframe-ready', winVersion: globalVars.winVersion }, iFrameUrl);
        }
      }
      if (event.data.type === 'requestApi') {
        const { url, options, requestId } = event.data.data;

        // 这里可以调用你的 fetch 函数，比如 requestServer
        requestServer(url, options).then((response) => {
          // 将响应结果返回给 iframe
          const iframe = document.getElementById('courseProgressDetailFrame');
          if (iframe) {
            iframe.contentWindow.postMessage({ type: 'responseApi', response, requestId }, iFrameUrl);
          }
        }).catch((error) => {
          alertError("请求失败", error.message);
        });
      }
    };
    // 添加消息监听器
    window.addEventListener('message', messageHandler);

    // 移除监听器以避免内存泄漏
    return () => {
      window.removeEventListener('message', messageHandler);
    };
  }, [iFrameUrl]);

  // 加载课程详情
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 先获取班级列表
        const classRes = await getClassList({ schoolYear: selectSchoolYear });
        if (classRes.code !== 0) {
          alertError("获取班级列表失败", classRes.message);
          return;
        }

        setClassList(classRes.data);
        let tempClassID = null;
        if (classRes.data.length > 0) {
          setSelectClassID(classRes.data[0].id);
          tempClassID = classRes.data[0].id;
        }

        // 再获取课程详情
        const courseRes = await getCourseDetail(courseSlug);
        if (courseRes.code !== 0) {
          alertError("获取课程详情失败", courseRes.message);
          return;
        }

        const courseData = courseRes.data;
        setCourseDetail(courseData);
        setNavName(courseData.courseName + '——课程进度');
        setSelectedKey(courseData.indics[0].chapterName);

        // 确保两个异步操作都完成后再设置courseProgressParams
         if (tempClassID && courseData.indics[0]) {
           setCourseProgressParams({
             courseSlug,
             chapterName: courseData.indics[0].chapterName,
             classID: tempClassID,
             selectState: '全部',
             nodeType: 'chapter',
           });
         } else {
           console.warn('初始化参数不完整:', { tempClassID, hasIndics: !!courseData.indics[0] });
         }

        // 初始化时从本地存储加载展开状态
        const storedKeys = getStoredExpandedKeys(courseSlug);
        if (storedKeys.length > 0) {
          setExpandedKeys(storedKeys);
        } else {
          // 如果没有存储的状态，默认展开所有节点
          const allKeys = courseData.indics.reduce((keys, node) => {
            keys.push(node.key);
            if (node.children) {
              node.children.forEach(child => keys.push(child.key));
            }
            return keys;
          }, []);
          setExpandedKeys(allKeys);
        }
      } catch (error) {
        alertError("初始化数据失败", error.message);
      }
    };

    initializeData();
  }, [selectSchoolYear]);

  // 加载统计数据
  useEffect(() => {
    // 确保courseProgressParams有必要的参数后才加载数据
    if (!courseProgressParams || !courseProgressParams.nodeType || !courseProgressParams.courseSlug || !courseProgressParams.classID) {
      return;
    }
    let intervalId;

    const getCourseProgress = async () => {
      // 定义一个函数来处理重复的逻辑
      const processCourseData = (value) => {
        const data = value.list;
        const sectionNames = value.sectionNames || value.itemNames;
        const chartMapByQuestion = {};
        const updatedChartDataByQuestion = [];
        const updatedChartDataByPeople = [];

        data.forEach((row) => {
          const keysByQuestion = Object.keys(row);
          if (!keysByQuestion.length) return;

          const code = { key: row.name, value: 0 };
          keysByQuestion.forEach((key) => {
            if (sectionNames.length && !sectionNames.includes(key)) return;
            if (!row[key]) return;

            // 更新 chartMapByQuestion
            chartMapByQuestion[key] = chartMapByQuestion[key] || 0;

            // 处理不同的状态值
            if (['AC', '运行通过', '正确', '已读'].includes(row[key])) {
              chartMapByQuestion[key] += 100;
              code.value += 100;
              return;
            }

            if (typeof row[key] === 'number') {
              chartMapByQuestion[key] += row[key];
              code.value += row[key];
            }
          });

          // 保留两位小数
          code.value = parseFloat(code.value.toFixed(2));
          updatedChartDataByPeople.push(code);
        });

        Object.keys(chartMapByQuestion).forEach((key) => {
          // 保留两位小数
          const value = parseFloat(chartMapByQuestion[key].toFixed(2));
          updatedChartDataByQuestion.push({ key, value });
        });

        // 更新图表数据
        setChartDataByPeople(updatedChartDataByPeople);
        setChartDataByQuestion(updatedChartDataByQuestion);
      };

      // 根据不同 nodeType 获取不同的进度数据
      if (courseProgressParams.nodeType === 'chapter') {
        const res = await getChapterProgress(courseProgressParams);
        if (res && res.data && res.data.list) {
          processCourseData(res.data);
          listSort(res.data.list);
          setDataSource(res.data.list);
          setSectionNames(res.data.sectionNames);
          setStaticsResult(res.data.staticsResult);
        } else {
          alertError("获取课程进度失败", res && res.message ? res.message : '没有学生提交数据');
        }
      }

      if (courseProgressParams.nodeType === 'section') {
        const res = await getSectionProgress(courseProgressParams);
        if (res && res.data && res.data.list) {
          processCourseData(res.data);
          listSort(res.data.list);
          setDataSource(res.data.list);
          setDataSourceCount(res.data.count);
          setSectionType(res.data.sectionType);
          setAISectionKeyMap(res.data.keyMap);
          setSectionNames(res.data.itemNames);
          setStaticsResult(res.data.staticsResult);
        } else {
          alertError("获取课程进度失败", res && res.message ? res.message : '没有学生提交数据');
        }
      }
    };

    // 初始加载数据
    getCourseProgress();

    if (selectVisual === '综合统计') {
      // 设置定时器，每两秒刷新一次
      intervalId = setInterval(getCourseProgress, 5000);

      // 清除定时器
      return () => clearInterval(intervalId);
    }
  }, [courseProgressParams, selectedKey, selectVisual, selectSort, selectState]);

  // 更新展开状态时保存到本地存储
  const handleExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    setStoredExpandedKeys(courseSlug, expandedKeys);
  };

  if (!courseDetail) {
    return;
  }

  let { courseName, courseType, courseDescription, creater, indics: directory, sectionData = [] } = courseDetail;

  // let isAdmin = session.code === 0 ? session.data?.isAdmin : null;
  let isAdmin = 1;
  let count = 0;
  directory = directory.map((chapter, index) => {
    // 通过判断这个
    let inCount = 0;
    const { chapterIndex, chapterName, chapterTitle, sections, isOpen = true } = chapter;

    // 内容目录考虑对于折叠的支持，如5.练习_1.服装搭配.xml，5.练习_2.计算线段长度.xml；折叠为 练习/练习_1.服装搭配.xml
    // 查询出存在下划线的节
    const hasUnderlines = sections.filter((i) => i.sectionName.includes('_'));
    // 找出要折叠的节
    if (hasUnderlines && hasUnderlines.length) {
      for (const hasUnderline of hasUnderlines) {
        const foldArray = [];
        // 找出折叠名称
        const { sectionName } = hasUnderline;
        const [foldName] = sectionName.split('_');
        sections.forEach((section) => {
          const { sectionName: sn } = section;
          // 初始状态是未答题
          section.status = '未答题';
          const [name] = sn ? sn.split('_') : [null];
          if (foldName === name) {
            foldArray.push(section);
          }
          // 如果之前这个章没有答题记录
          if (!sectionData || !sectionData.length) {
            // 如果是必做题，记载count和inCount，如果没有必做题，两个都是0，一直到章节中有必做题，产生变化，在下一个章节的时候inCount赋值为0，两个不相等，后面的都会锁住
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          // 在答题记录中寻找当前节
          const sectionItem = sectionData.find((item) => item.chapterName === chapter.chapterName && item.sectionName === section.sectionName);
          // 如果当前节没有答
          if (!sectionItem || !sectionItem.sectionForeign || !sectionItem.sectionForeign.length) {
            section.status = '未答题';
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          // 如果有答题的话
          sectionItem.sectionForeign.forEach((foreignItem) => {
            const { record, totalScore, passCount } = foreignItem;
            if (!record) {
              section.status = '未答题';
              if (section.mustBeDone && !isAdmin) {
                count += 1;
                inCount += 1;
              }
              return;
            }
            if (totalScore !== passCount) {
              section.status = '未通过';
              if (section.mustBeDone && !isAdmin) {
                count += 1;
                inCount += 1;
              }
              return;
            }
            // 答对的时候不做任何处理
            section.status = '已通过';
          });
        });
        // 处理 sections
        if (foldArray && foldArray.length > 1) {
          foldArray.forEach((section, index) => {
            const sec = sections.findIndex((i) => i.sectionName === section.sectionName);
            if (index === 0 && sec > -1) {
              sections.splice(sec, 1, { foldArray, isOpen, foldName });
            } else if (sec > -1) {
              sections.splice(sec, 1);
            }
          });
        }
      }
    } else {
      // 逻辑参照上面分析
      sections.forEach((section) => {
        section.status = '未答题';
        if (!sectionData || !sectionData.length) {
          if (section.mustBeDone && !isAdmin) {
            count += 1;
            inCount += 1;
            return;
          }
        }
        const sectionItem = sectionData.find((item) => item.chapterName === chapter.chapterName && item.sectionName === section.sectionName);
        if (!sectionItem || !sectionItem.sectionForeign || !sectionItem.sectionForeign.length) {
          section.status = '未答题';
          if (section.mustBeDone && !isAdmin) {
            count += 1;
            inCount += 1;
          }
          return;
        }
        sectionItem.sectionForeign = sectionItem.sectionForeign.filter((item) => item.totalScore);
        if (!sectionItem.sectionForeign.length) {
          section.status = '未答题';
          if (section.mustBeDone && !isAdmin) {
            count += 1;
            inCount += 1;
          }
          return;
        }
        sectionItem.sectionForeign.forEach((foreignItem) => {
          const { record, totalScore, passCount } = foreignItem;
          if (!record) {
            section.status = '未答题';
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          if (totalScore !== passCount) {
            section.status = '未通过';
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          section.status = '已通过';
        });
      });
    }
    return {
      title: chapterTitle,
      key: chapterName,
      chapterName,
      index,
      nodeType: 'chapter',
      afterMustBeDone: count > 0 && inCount !== count,
      isOpen,
      children: sections.map((section, itemIndex) => {
        const {
          sectionIndex, sectionName: scn, sectionTitle, sectionType, foldArray: foldArr = [], foldName, mustBeDone, status,
        } = section;

        // 节 折叠的情况
        if (foldArr && foldArr.length) {
          return {
            title: foldName,
            key: `${chapterName}_${foldName}`,
            index: itemIndex,
            mustBeDone,
            chapterName,
            afterMustBeDone: count > 0 && inCount !== count,
            children: foldArr.map((sect, sectIndex) => {
              const { sectionName: sc, sectionTitle: st, sectionType: secType } = sect;
              return {
                title: st,
                sectionName: sc,
                chapterName,
                index: sectIndex,
                mustBeDone,
                key: `${chapterName}_${foldName}_${sc}`,
                nodeType: 'section',
                isOpen,
                sectionType: secType,
                status: sect.status,
              };
            }),
          };
        }
        return {
          title: sectionTitle,
          chapterName,
          sectionName: scn,
          index: itemIndex,
          key: `${chapterName}_${scn}`,
          afterMustBeDone: count > 0 && inCount !== count,
          mustBeDone,
          nodeType: 'section',
          isOpen,
          sectionType,
          status,
        };
      }),
    };
  });

  const menuProps = {
    items: [
      {
        label: '导出当前章/节进度',
        key: 'default',
      },
      {
        label: '导出当前班级课程进度',
        key: 'course4SingleClass',
      },
      {
        label: '导出全部班级课程进度',
        key: 'course4AllClass',
      },
    ],
    onClick: async (e)=> {
      if (e.key === 'default') {
        const config = e.key;
        const { chapterName, sectionName, nodeType } = courseProgressParams;

        // 获取选择班级的名称
        const selectClass = classList.find((i) => i.id === selectClassID);
        const exportClassName = selectClass.name;

        if (!dataSource) {
          alertError('暂无数据');
          return;
        }

        let exportList = [];
        let exportName = `课程进度-${exportClassName}-${chapterName}`;

        if (nodeType === 'chapter') {
          const { indics } = courseDetail;
          const dataValue = indics.find((v) => v.chapterName === chapterName);
          const dataRes = dataValue ? dataValue.sections : [];

          // 导出章分数统计内容
          exportList = exportChapterData(dataSource, dataRes);
        }

        if (nodeType === 'section') {
          // 导出节作题结果内容
          exportList = exportSectionData(dataSource, AISectionKeyMap, config);
          exportName = `课程进度-${exportClassName}-${chapterName}-${sectionName}`;
        }

        exportRef.current.exportExcel(exportList, [], exportName);
      }
      if (e.key === 'course4SingleClass' || e.key === 'course4AllClass') {
        const config = e.key;

        let exportList = [];
        let exportName = `课程进度-${courseSlug}`;
        let classIDs = [];

        if (config === 'course4SingleClass') {
          // 获取选择班级的名称
          const selectClass = classList.find((i) => i.id === selectClassID);
          const exportClassName = selectClass.name;
          exportName = `课程进度-${courseSlug}-${exportClassName}`;

          classIDs = [selectClassID];
          const response = await exportCourseProgressExcel({ courseSlug, classIDs: classIDs });

          if (response.code !== 0) {
            alertError("错误", response.message);
            return;
          }
          const { data, keyMap } = response;
          exportList = exportCourseData(data[selectClassID], data.keyMap);
        }

        let type = null;
        let classNames = [];

        if (config === 'course4AllClass') {
          type = 'multiSheets';
          classIDs = classList.map((i) => i.id);
          classNames = classList.map((i) => i.name);

          const response = await exportCourseProgressExcel({ courseSlug, classIDs: classIDs });
          if (response.code !== 0) {
            alertError("错误", response.message);
            return;
          }

          const { data } = response;
          for (const classId of classIDs) {
            const temp = exportCourseData(data[classId], data.keyMap);

            const selectClass = classList.find((i) => i.id === classId);
            const className = selectClass.name;
            exportList.push({ className, classList: temp });
          }
        }

        exportRef.current.exportExcel(exportList, [], exportName, type, classNames);
      }
    },
  };

  const columns = [
    {
      title: '账号',
      dataIndex: 'username',
      key: 'username',
      fixed: 'left',
      width: 100,
    },
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 100,
    }
  ];

  // 章的课程进度显示
  if (courseProgressParams.nodeType === 'chapter') {
    const indicsSelect = directory.find((i) => i.chapterName === courseProgressParams.chapterName) || [];
    const { children: sections } = indicsSelect;
    if (sections && sections.length) {
      for (const section of sections) {
        const { sectionName } = section;
        columns.push({
          title: (
            <Popover content={sectionName}>
              <div className={styles.sectionTitle}>
                {sectionName}
              </div>
            </Popover>
          ),
          width: 120,
          dataIndex: sectionName,
          key: sectionName,
          render: (row, r) => {
            let backgroundColor = renderBackgroundColor(row, r[`${sectionName}-total`]);
            return (
              <div
                style={{ textAlign: 'center', color: '#fff', fontSize: '13px', backgroundColor }}
                onClick={() => {
                  const chapterName = selectedKey;
                  setIFrameUrl(`${globalVars.baseURL}/microapps/course/index.html#/courseProgressDetail/${courseSlug}/${chapterName}/${sectionName}/${r.id}/${0}`);
                  // setIFrameUrl(`http://localhost:8001/courseProgressDetail/${courseSlug}/${chapterName}/${sectionName}/${r.id}/${0}`);
                  setCourseProgressDetailOpen(true);
                }}
              >
                {row}
              </div>
            );
          },
        });
      }
    }
  } else if (dataSourceCount) {
    // 节的课程进度显示
    let count = 0;
    for (let i = 1; i <= dataSourceCount; i++) {
      count += 1;
      const ind = count;
      let sectionTitle;
      switch (sectionType) {
        case 'OJ':
          sectionTitle = `数据${i}`;
          break;
        case 'OI':
          sectionTitle = `题目${i}`;
          break;
        case 'CodeBlank':
          sectionTitle = `填空${i}`;
          break;
        case 'AI':
          if (!AISectionKeyMap[i]) {
            continue;
          }
          sectionTitle = `${AISectionKeyMap[i]}`;
          break;
        case 'Access':
        case 'Excel':
        case 'Scratch':
          sectionTitle = `步骤${i}`;
          break;
        case 'PPT':
          sectionTitle = `页面${i}`;
          break;
        case 'MicroBit':
          sectionTitle = `项目${i}`;
          break;
        default:
          alertError(`不支持的节类型${sectionType}`);
          break;
      }
      columns.push({
        title: (
          <div className={styles.sectionTitle}>
            {sectionTitle}
          </div>
        ),
        width: 100,
        dataIndex: sectionTitle,
        key: sectionTitle,
        render: (row, r, index) => {
          let backgroundColor = renderBackgroundColor(row);
          // if (sectionType === 'OI') {
          //   let rowAnswer = r.answer ? r.answer[sectionTitle] : '未运行';
          //
          //   if (rowAnswer instanceof Object) {
          //     const answerList = [];
          //     for (const answerKey in rowAnswer) {
          //       answerList.push(`${answerKey}: ${rowAnswer[answerKey]}`);
          //     }
          //
          //     rowAnswer = answerList.join(' ');
          //   }
          //
          //   rowAnswer = !rowAnswer ? '暂无答案' : rowAnswer;
          //   if (checkTeamType) {
          //     return (
          //       <div style={{ textAlign: 'center', color: '#fff', fontSize: '13px', backgroundColor }}>
          //         {row}
          //       </div>
          //     );
          //   }
          //   return (
          //     <Tooltip
          //       title={(rowAnswer || '未填写答案')}
          //     >
          //       <div style={{ textAlign: 'center', color: '#fff', fontSize: '13px', backgroundColor }}>
          //         {row}
          //       </div>
          //     </Tooltip>
          //   );
          // }

          if (sectionType === 'Scratch') {
            // 检查 r.score 是否存在，避免 undefined 错误
            const scoreValue = r.score && r.score[`步骤${i}`] !== undefined ? r.score[`步骤${i}`] : null;
            backgroundColor = renderBackgroundColor(row, scoreValue);
          }
          return (
            <div
              style={{ textAlign: 'center', color: '#fff', fontSize: '13px', backgroundColor }}
              onClick={() => {
                // if (sectionType === 'OI' || sectionType === 'PPT') {
                //   return;
                // }
                // 获取下划线后面的内容 作为chapterName
                const chapterName = selectedKey.split('_')[0];
                const sectionName = selectedKey.split('_')[1];
                setIFrameUrl(`${globalVars.baseURL}/microapps/course/index.html#/courseProgressDetail/${courseSlug}/${chapterName}/${sectionName}/${r.id}/${i}`);
                // setIFrameUrl(`http://localhost:8001/courseProgressDetail/${courseSlug}/${chapterName}/${sectionName}/${r.id}/${i}`);
                setCourseProgressDetailOpen(true);
              }}
            >
              {row}
            </div>
          );
        },
      });
    }
  }

  const filterFunction = (row, total) => {
    return scoringAverageStart <= (row.value / total) * 100 && scoringAverageEnd >= (row.value / total) * 100;
  };

  const listSort = (data) => {
    if (selectSort) {
      switch (selectSort) {
        case '账号升序(字典排序)':
          // dataSource.sort(compare('id'));
          data.sort((a, b) => {
            // console.log('a.username', a.username, 'b.username', b.username)
            return a.username > b.username ? 1 : -1;
          });
          break;
        case '账号降序(字典排序)':
          // dataSource.sort(compareReverse('id'));
          data.sort((a, b) => {
            return a.username < b.username ? 1 : -1;
          });
          break;
        case '账号升序(仅数字排序)':
          // dataSource.sort(compare('id'));
          data.sort((a, b) => {
            const aNum = parseInt(a.username.replace(/\D/g, ''), 10);
            const bNum = parseInt(b.username.replace(/\D/g, ''), 10);
            // console.log('aNum', aNum, 'bNum', bNum)
            return aNum > bNum ? 1 : -1;
          });
          break;
        case '账号降序(仅数字排序)':
          // dataSource.sort(compareReverse('id'));
          data.sort((a, b) => {
            const aNum = parseInt(a.username.replace(/\D/g, ''), 10);
            const bNum = parseInt(b.username.replace(/\D/g, ''), 10);
            // console.log('a.username', a.username, 'b.username', b.username)
            return aNum < bNum ? 1 : -1;
          });
          break;
        case '提交时间升序':
          data.sort((a, b) => {
            return Date.parse(a['submitTime']) - Date.parse(b['submitTime']);
          });
          break;
        case '提交时间降序':
          data.sort((a, b) => {
            return Date.parse(b['submitTime']) - Date.parse(a['submitTime']);
          });
          break;
        case '课程进度升序':
          data.sort((a, b) => (a.scoreList?.length || 0) - (b.scoreList?.length || 0))
          break;
        case '课程进度降序':
          data.sort((a, b) => (b.scoreList?.length || 0) - (a.scoreList?.length || 0))
          break;
        default:
          break;
      }
    }
  }

  const isWin7 = navigator.userAgent.includes('Windows NT 6.1');
  console.log("styles = ", styles, "isWin7 = ", isWin7);

  return (
    <main className="flex justify-center flex-1">
      <div className="w-full mx-auto">
        <div style={{ userSelect: 'none' }}>
          <style>
            {`
        .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
          padding: 0;
          word-wrap: break-word;
          word-break: break-all;
        }

        .ant-switch-checked {
          background-color: #235A9B;
        }

        .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle {
          display: none;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:before {
          background-color: #1b487e;
        }
        
        .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:hover:before {
          background-color: #1b487e;
        }
        
        .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper:hover:before {
          background: rgba(27,72,126,0.8);
        }

        .ant-tree .ant-tree-switcher .ant-tree-switcher-icon , .ant-tree .ant-tree-switcher .ant-select-tree-switcher-icon{
          font-size: 20px;
          vertical-align: middle;
          line-height: 35px;
        }
        
        .ant-tree .ant-tree-switcher:before {
          height: 42px;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher {
          color: #1b487e;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher {
          color: #fff !important;
        }

        .ant-tree-treenode {
          align-items: center !important;
        }
        
        .ant-tree .ant-tree-checkbox {
          margin-top: 12px;
          margin-left: 5px;
        }
        
        .ant-table-wrapper .ant-table-pagination.ant-pagination {
          margin: 16px 0 0 0;
        }
        
        .ant-tree .ant-tree-indent {
          display: none;
        }

      `}
          </style>
          <div>
            <div className="bg-white rounded-lg w-full">
              <div
                className="flex justify-between items-center"
                style={{ marginTop: -10, paddingLeft: '20px' }}
              >
                <div className="flex justify-between items-center">
                  <div style={{ fontWeight: 'bold' }}>
                    学年：
                    <Select
                      showSearch
                      placeholder="请选择学年"
                      optionFilterProp="label"
                      style={{ width: 200 }}
                      value={selectSchoolYear}
                      onChange={(value) => {
                        setSelectSchoolYear(value);
                        setSelectClassID(null);
                        setClassList([]);
                        setCourseProgressParams({
                          ...courseProgressParams,
                          classID: null,
                        });
                        setDataSource([]);
                        setDataSourceCount(0);
                        setStaticsResult([]);
                      }}
                    >
                      {renderSelectYear()}
                    </Select>
                  </div>
                  <div style={{ fontWeight: 'bold', marginLeft: 20 }}>
                    班级：
                    <Select
                      showSearch
                      placeholder="请选择班级"
                      optionFilterProp="label"
                      style={{ width: 200 }}
                      value={selectClassID}
                      onChange={(value) => {
                        setSelectClassID(value);
                        setCourseProgressParams({
                          ...courseProgressParams,
                          classID: value,
                        });
                      }}
                      options={classList.map((v) => {
                        return {
                          value: v.id,
                          label: v.name,
                        };
                      })}
                    />
                  </div>
                </div>
                {
                  dataSource !== null ?
                      <>
                        <Dropdown menu={menuProps}>
                          <Button type='primary'>
                            <Space>
                              导出课程进度
                              <DownOutlined />
                            </Space>
                          </Button>
                        </Dropdown>
                        <ExportButton styles={{ display: 'none' }} ref={exportRef} />
                      </> :
                      <></>
                }
              </div>
              <Row gutter={20}>
                <Col xs={24} sm={24} md={6} lg={6} xl={6}>
                  <div style={{ height: 'calc(100vh - 150px)', overflowY: 'auto', backgroundColor: 'white' }} ref={devRef}>
                    {
                      directory && directory.length
                        ? (
                          <div style={{padding: '20px 10px 10px 10px', backgroundColor: 'white'}}>
                            <DirectoryTree
                              expandAction={false}  // 禁用点击节点展开
                              expandedKeys={expandedKeys}
                              onExpand={handleExpand}
                              selectedKeys={[selectedKey]}
                              showIcon={false}
                              titleRender={(treeNode) => {
                                return (
                                  <div className="flex items-center" style={{margin: '15px 0 10px 0'}}>
                                    {
                                      treeNode.mustBeDone ? (
                                          <Tooltip title="必做题做完之后才能开启下一个章节">
                                            <div className="flex items-center">
                                              {
                                                treeNode.nodeType === 'section' ? (
                                                  <img alt="" src={ selectedKey === treeNode.key ? imgMapWhite[treeNode.sectionType] : imgMap[treeNode.sectionType]}
                                                       style={{color: '#2f99d5', marginRight: '10px', height: 'auto', width: '24px', marginTop: '-7px'}}
                                                  />
                                                ) : ''
                                              }
                                              <span style={{color: (selectedKey === treeNode.key ? 'white' : 'black'), fontSize: treeNode.nodeType === 'chapter' ? '20px' : '16px', marginTop: '-7px'}}>
                                            {
                                              treeNode.mustBeDone ? (<span style={{color: 'red', marginRight: '2px'}}>*</span>) : ''
                                            }
                                                {
                                                  `${treeNode.nodeType === 'chapter' ? (`章节${toChinesNum(parseInt(treeNode.index, 10) + 1)}`) : `${parseInt(treeNode.index, 10) + 1}.`}`
                                                }
                                                {
                                                  `${treeNode.title}`}
                                                {treeNode.status === '未通过' && treeNode.sectionType !== 'AI' ? (
                                                  <CloseCircleOutlined style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'red',}}/>
                                                ) : ''}
                                                {treeNode.status === '未通过' && treeNode.sectionType === 'AI' ? (
                                                  <MinusCircleOutlined style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: '#d8d81c',}}/>
                                                ) : ''}
                                                {treeNode.status === '已通过' ? (
                                                  <CheckCircleOutlined style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'green',}}/>
                                                ) : ''}
                                                {((!checkIsOpen(treeNode) || treeNode.disabled) && treeNode.nodeType === 'chapter')
                                                  ? (
                                                    <img style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '5px', marginTop: '-7px',}}
                                                         src={selectedKey === treeNode.key ? require('../../assets/courseDetail/lock_white.png') : require('../../assets/courseDetail/lock_black.png')} alt=""
                                                    />
                                                  ) : ''}
                                            </span>
                                            </div>
                                          </Tooltip>
                                        )
                                        : (
                                          <>
                                            {
                                              treeNode.nodeType === 'section' ? (
                                                <img
                                                  alt=""
                                                  src={selectedKey === treeNode.key ? imgMapWhite[treeNode.sectionType] : imgMap[treeNode.sectionType]}
                                                  style={{color: '#2f99d5', marginRight: '12px', height: 'auto', width: '24px', marginTop: '-7px'}}
                                                />
                                              ) : ''
                                            }
                                            <span style={{color: (selectedKey === treeNode.key ? 'white' : 'black'), fontSize: treeNode.nodeType === 'chapter' ? '20px' : '16px', fontWeight: treeNode.nodeType === 'chapter' ? 'bolder' : 'normal', marginTop: '-7px'}}>
                                          {
                                            treeNode.mustBeDone ? (
                                              <span style={{color: 'red', marginRight: '1px'}}>*</span>
                                            ) : ''
                                          }
                                              {`${treeNode.nodeType === 'chapter' ? (`章节${toChinesNum(parseInt(treeNode.index, 10) + 1)}`) : `${parseInt(treeNode.index, 10) + 1}.`}`}
                                              &nbsp;&nbsp;
                                              {`${treeNode.title}`}
                                              {treeNode.status === '未通过' && treeNode.sectionType !== 'AI' ? (
                                                <CloseCircleOutlined style={{
                                                  display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'red',
                                                }}
                                                />
                                              ) : ''}
                                              {treeNode.status === '未通过' && treeNode.sectionType === 'AI' ? (
                                                <MinusCircleOutlined
                                                  src={require('../../assets/courseDetail/wrong.png')}
                                                  style={{
                                                    display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: '#d8d81c',
                                                  }}
                                                />
                                              ) : ''}
                                              {treeNode.status === '已通过' ? (
                                                <CheckCircleOutlined style={{
                                                  display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'green',
                                                }}
                                                />
                                              ) : ''}
                                              {((!checkIsOpen(treeNode) || treeNode.disabled || treeNode.afterMustBeDone) && treeNode.nodeType === 'chapter') ? (
                                                <img
                                                  style={{
                                                    display: 'inline-block', width: '20px', height: 'auto', marginLeft: '5px', marginTop: '-7px',
                                                  }}
                                                  src={selectedKey === treeNode.key ? require('../../assets/courseDetail/lock_white.png') : require('../../assets/courseDetail/lock_black.png')}
                                                  alt=""
                                                />
                                              ) : ''}
                                        </span>
                                          </>
                                        )
                                    }
                                  </div>
                                );
                              }}
                              treeData={directory}
                              onSelect={(e, {node}) => {
                                const { chapterName, key, sectionName, nodeType, sectionType } = node;
                                setSelectedKey(key);

                                const newParams = {
                                  nodeType,
                                  courseSlug,
                                  chapterName,
                                  sectionName,
                                  classID: selectClassID,
                                  selectState
                                };

                                console.log('树节点选择，设置新的courseProgressParams:', newParams);
                                setCourseProgressParams(newParams);
                              }}
                            />
                          </div>
                        )
                        : <div style={{fontSize: '20px', textAlign: 'center', marginTop: '30%'}}>暂无内容~</div>
                    }
                  </div>
                </Col>
                <Col xs={24} sm={24} md={18} lg={18} xl={18}
                     ref={rightColRef}
                     style={fullScreen ? {
                       position: 'fixed',
                       top: 0,
                       left: 0,
                       width: '100vw',
                       height: '100vh',
                       zIndex: '9999',
                       background: 'white',
                       overflow: 'auto',
                       maxWidth: '100%',
                     }: {}}
                >
                  <div style={{ paddingTop: 20 }}>
                    <Row gutter={20} style={{marginBottom: '10px'}}>
                      <Col span={4}>
                        {
                          classList && classList.length ? (
                          <Select
                            value={selectVisual}
                            style={{ width: '100%' }}
                            onChange={(v) => {
                              setSelectVisual(v);
                            }}
                          >
                            <Option value="综合统计"> 综合统计</Option>
                            <Option value="人员得分统计"> 人员得分统计</Option>
                            <Option value="题目得分统计"> 题目得分统计</Option>
                          </Select>
                          ) : null
                        }
                      </Col>
                      {
                        dataSource !== null ?
                            <Col span={5}>
                              <Select
                                  style={{width: '180px'}}
                                  value={selectSort}
                                  onChange={(value) => {
                                    setSelectSort(value);
                                    listSort(dataSource);
                                  }}
                              >
                                <Option value="账号升序(字典排序)">账号升序(字典排序)</Option>
                                <Option value="账号降序(字典排序)">账号降序(字典排序)</Option>
                                <Option value="账号升序(仅数字排序)">账号升序(仅数字排序)</Option>
                                <Option value="账号降序(仅数字排序)">账号降序(仅数字排序)</Option>
                                <Option value="提交时间升序">提交时间升序</Option>
                                <Option value="提交时间降序">提交时间降序</Option>
                                <Option value="课程进度升序">课程进度升序</Option>
                                <Option value="课程进度降序">课程进度降序</Option>
                              </Select>
                            </Col> :
                            <></>
                      }
                      <Col span={15} style={{textAlign: 'right'}}>
                        <a
                          onClick={() => {
                            setFullScreen(!fullScreen);
                          }}
                          style={{ cursor: 'pointer', fontSize: '16px', fontWeight: '550', marginRight: '20px' }}
                        >
                          {
                            dataSource !== null ? fullScreen ? <FullscreenExitOutlined /> : <FullscreenOutlined /> : <></>
                          }
                          {dataSource !== null ? fullScreen ? '还原' : '全屏' : ''}
                        </a>
                        {
                          classList && classList.length ? (
                          <Radio.Group
                            value={selectState}
                            onChange={(e) => {
                              const v = e.target.value;
                              setSelectState(v)
                              setCourseProgressParams({
                                ...courseProgressParams,
                                selectState: v,
                              })
                            }}
                          >
                            {
                              staticsResult.map((v, i) => (
                                <Radio.Button key={i+1} value={v.name}>
                                  {v.name}
                                  {' '}
                                  {v.value}
                                </Radio.Button>
                              ))
                            }
                          </Radio.Group>
                          ) : null
                        }
                      </Col>
                    </Row>
                    {/*{*/}
                    {/*  classList && classList.length && selectVisual !== '综合统计' ? (*/}
                    {/*    <div>*/}
                    {/*      得分率显示：&nbsp;*/}
                    {/*      <Button*/}
                    {/*        onClick={() => {*/}
                    {/*          dispatch({*/}
                    {/*            type: 'courseProgress/update',*/}
                    {/*            payload: {scoringAverageDisplay: true, scoringAverageValue: {start: scoringAverageStart, end: scoringAverageEnd}}*/}
                    {/*          });*/}
                    {/*        }}*/}
                    {/*      >*/}
                    {/*        {scoringAverageStart}*/}
                    {/*        % ~*/}
                    {/*        {scoringAverageEnd}*/}
                    {/*        %*/}
                    {/*        <DownOutlined />*/}
                    {/*      </Button>*/}
                    {/*    </div>*/}
                    {/*  ) : null*/}
                    {/*}*/}
                    {
                      selectVisual === '人员得分统计' ? (
                        <BarChart
                          key={`people-${JSON.stringify(courseProgressParams)}`}
                          chartTitle=""
                          tableData={chartDataByPeople.filter((row) => filterFunction(row, chartDataByQuestion.length * 100))}
                          max={chartDataByQuestion.length * 100}
                          sortFunction={(a, b) => b.value - a.value}
                        />
                      ) : null
                    }
                    {
                      selectVisual === '题目得分统计' ? (
                        // <BarChart chartTitle="" tableData={chartDataByQuestion.filter((row) => filterFunction(row, chartDataByPeople.length * 100))} max={chartDataByPeople.length * 100} left="30%" sortFunction={(a, b) => b.value - a.value} />
                        <BarChart
                          key={`question-${JSON.stringify(courseProgressParams)}`}
                          chartTitle=""
                          tableData={chartDataByQuestion.filter((row) => filterFunction(row, chartDataByPeople.length * 100))}
                          max={chartDataByPeople.length * 100}
                        />
                      ) : null
                    }
                    {
                      selectVisual === '综合统计' ? (
                        dataSource && dataSource.length > 0 ? (
                          <Table
                            className={isWin7 ? styles.win7Fix : ''}
                            columns={columns}
                            dataSource={dataSource}
                            size="middle"
                            scroll={{
                              y: tableHeight - 150,
                              x: 'max-content',
                            }}
                            rowKey={(record) => record.id}
                            pagination={{
                              pageSizeOptions: ['10', '25', '50', '100'], // 分页选项
                              showSizeChanger: true, // 显示分页大小切换器
                              defaultPageSize: 10, // 默认分页大小
                              showTotal: (total) => `共 ${total} 条`, // 显示总条数
                            }} // 使用自定义分页配置
                          />
                        ) : (
                          <div style={{textAlign: 'center', padding: '50px', fontSize: '16px', color: '#999'}}>
                            {/*{dataSource === null ? '数据加载中...' : '暂无数据'}*/}
                            {dataSource === null ? '没有选择班级' : '暂无数据'}
                          </div>
                        )
                      ) : null
                    }
                  </div>
                </Col>
              </Row>

              <Modal
                // title={currentRecord.title ? currentRecord.title : '代码记录'}
                title={'课程进度详情'}
                open={courseProgressDetailOpen}
                footer={null}
                width={'85vw'}
                onCancel={() => { setCourseProgressDetailOpen(false); }}
                destroyOnClose
                maskClosable={false}
              >
                <iframe
                  id="courseProgressDetailFrame"
                  // title={`123`}
                  // allow="usb;microphone;camera;midi;encrypted-media;"
                  // ref={this.containerRef}
                  src={iFrameUrl}
                  // frameBorder="0"
                  // onLoad={this.onLoaded}
                  referrerPolicy="no-referrer"
                  style={{width: '100%', height: '70vh', border: 0, overflow: 'hidden'}}
                  sandbox="allow-scripts allow-same-origin allow-popups allow-downloads"
                />
              </Modal>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
