import React, { useEffect, useState } from 'react';
import { Col, Row } from 'antd';
import { getCourseDetail, getSectionDetail } from '@/services/course';
import { useModel } from '@@/exports';
import requestServer from "@/utils/requestServer";
import { alertError } from '@/utils/edittools';

export default function SectionDetailPage() {

  const [courseDetail, setCourseDetail] = useState(null);
  const [sectionDetail, setSectionDetail] = useState(null);
  const [loading, setLoading] = useState(true);

  // 获取地址栏参数
  const url = window.location.href;
  const parts = url.split('/');
  const courseSlug = parts[parts.length - 3];
  const chapterName = decodeURIComponent(parts[parts.length - 2]);
  const sectionName = decodeURIComponent(parts[parts.length - 1]);
  const { setNavName } = useModel('global');

  const iFrameUrl = `${globalVars.baseURL}/microapps/course/index.html#/course/${courseSlug}/${chapterName}/${sectionName}`;
  // const iFrameUrl = `http://localhost:8000/course/${courseSlug}/${chapterName}/${sectionName}`;

  useEffect(() => {
    // 监听来自 iframe 的消息
    const messageHandler = (event) => {
      // 检查消息来源和类型
      if (event.data.type === 'iframe-ready') {
        console.log('Iframe is ready!');
        //http://hxr.iyopu.com/ok
        // 向 iframe 发送 Tauri 的数据或其他信息
        const iframe = document.getElementById('sectionFrame');
        if (iframe) {
          iframe.contentWindow.postMessage({ type: 'iframe-ready', winVersion: globalVars.winVersion }, iFrameUrl);
        }
      }
      if (event.data.type === 'requestApi') {
        const { url, options, requestId } = event.data.data;

        // 这里可以调用你的 fetch 函数，比如 requestServer
        requestServer(url, options).then((response) => {
          // 将响应结果返回给 iframe
          const iframe = document.getElementById('sectionFrame');
          if (iframe) {
            iframe.contentWindow.postMessage({ type: 'responseApi', response, requestId }, iFrameUrl);
          }
        }).catch((error) => {
          alertError("请求失败", error.message);
        });
      }
      // 处理链接点击事件
      if (event.data.type === 'LINK_CLICKED') {
        const { href } = event.data;
        if (href) {
          // 使用 Tauri 打开系统浏览器
          window.open(href)
        }
      }
    };
    // 添加消息监听器
    window.addEventListener('message', messageHandler);

    // 移除监听器以避免内存泄漏
    return () => {
      window.removeEventListener('message', messageHandler);
    };
  }, [courseSlug, chapterName, sectionName]);

  useEffect(() => {
    setNavName(sectionName.split('.')[1]);
  }, [sectionName, setNavName]);

  // 加载课程列表
  useEffect(() => {

    getCourseDetail(courseSlug)
      .then((res) => {
        const { code, message, data } = res;
        if (code !== 0) {
          alertError("获取课程详情失败", message);
          return;
        }

        setCourseDetail(data);
        setLoading(false);
      })
      .catch((error) => {
        alertError("获取课程详情失败", error.message);
        setLoading(false);
        return;
      });


    getSectionDetail({courseSlug, chapterName, sectionName})
      .then((res) => {
        const { code, message, data } = res;
        if (code !== 0) {
          alertError("获取课程节详情失败", message);
          return;
        }

        setSectionDetail(data);
        setLoading(false);
      })
      .catch((error) => {
        alertError("获取课程节详情失败", error.message);
        setLoading(false);
        return;
      });

  }, [sectionName]);

  if (!courseDetail) {
    return;
  }

  if (!sectionDetail) {
    return;
  }

  const directory = courseDetail && courseDetail.indics && courseDetail.indics.find((directoryData) => directoryData.chapterName === chapterName);

  // 获取当前章节课节列表
  const sectList = (directory && directory.sections && directory.sections) || [];
  const isExit = sectList.find((each) => each.sectionName === sectionName);

  if (!isExit) {
    alertError("错误", `当前课程${sectionName}不存在或未开放`);
    return;
  }
  return (
    <main className="flex justify-center flex-1">
      <style>
        {
          `
            .ant-pro-layout .ant-pro-layout-content {
              padding: 0 !important;
            }
          `
        }
      </style>
      <div className="w-full mx-auto">
        <Row>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <iframe
              id="sectionFrame"
              // title={`123`}
              allow="usb;microphone;camera;midi;encrypted-media;fullscreen;"
              // ref={this.containerRef}
              src={iFrameUrl}
              // frameBorder="0"
              // onLoad={this.onLoaded}
              referrerPolicy="no-referrer"
              style={{width: '100%', height: '92vh', border: 0, overflow: 'hidden'}}
              sandbox="allow-scripts allow-same-origin allow-popups allow-downloads allow-modals"
            />
          </Col>
        </Row>
      </div>
    </main>
  )
}
