import React, {useContext, useEffect, useState} from 'react';
import { useModel } from '@umijs/max';
import { Modal, Row, Col, Tooltip, Tree, Steps, Button, Affix, Checkbox, message, Progress } from 'antd';
import MinusCircleOutlined from '@ant-design/icons/MinusCircleOutlined';
import CloseCircleOutlined from '@ant-design/icons/CloseCircleOutlined';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import { history } from '@umijs/max'
import {finishClass, getCourseDetail, startClass, getRemoteCourseInfo, updateCourseContent} from "@/services/course";
import {SessionContext} from "@/services/user";
import globalVars from '@/utils/global';
import {LineChartOutlined, PoweroffOutlined, StarFilled} from "@ant-design/icons";
import { getLoginConfig, bindLogin } from '@/services/login';
import { checkNetworkStatus } from '@/utils/utils';
import { alertError, alertSuccess, formatCourseData } from '@/utils/edittools';
import { calculateAllSectionStatus } from '@/utils/courseStatus';
import courseWS from '@/utils/courseWebSocket';
import courseUpdateWS from '@/utils/courseUpdateWebSocket'; // 课程更新专用WebSocket（46046端口）
import './courseSync.css';

// 完成将 toChineseNum， 可以将数字转换成中文大写的表示，处理到万级别，例如 toChineseNum(12345)，返回 一万二千三百四十五。
const toChinesNum = (num) => {
  const changeNum = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']; // changeNum[0] = "零"
  const unit = ['', '十', '百', '千', '万'];
  num = parseInt(num);
  const getWan = (temp) => {
    const strArr = temp.toString().split('').reverse();
    let newNum = '';
    for (let i = 0; i < strArr.length; i++) {
      newNum = (i === 0 && strArr[i] === 0 ? '' : (i > 0 && strArr[i] === 0 && strArr[i - 1] === 0 ? '' : changeNum[strArr[i]] + (strArr[i] === 0 ? unit[0] : unit[i]))) + newNum;
    }

    return newNum.replace(/^一十/, '十');
  };
  const overWan = Math.floor(num / 10000);
  let noWan = num % 10000;
  if (noWan.toString().length < 4) noWan = `0${noWan}`;
  return overWan ? `${getWan(overWan)}万${getWan(noWan)}` : getWan(num);
};

const { DirectoryTree } = Tree;
const imgMap = {
  AI: require('../../assets/courseDetail/ai.png'),
  OJ: require('../../assets/courseDetail/oj.png'),
  OI: require('../../assets/courseDetail/oi.png'),
  MicroBit: require('../../assets/courseDetail/microbit.png'),
  PPT: require('../../assets/courseDetail/powerpoint.png'),
  Scratch: require('../../assets/courseDetail/scratch.png'),
  Access: require('../../assets/courseDetail/mdb.png'),
  Excel: require('../../assets/courseDetail/excel.png'),
  CodeBlank: require('../../assets/courseDetail/codeBlank.png'),
};

const imgMapWhite = {
  AI: require('../../assets/courseDetail/ai_white.png'),
  OJ: require('../../assets/courseDetail/oj_white.png'),
  OI: require('../../assets/courseDetail/oi_white.png'),
  MicroBit: require('../../assets/courseDetail/microbit_select.png'),
  PPT: require('../../assets/courseDetail/powerpoint_select.png'),
  Scratch: require('../../assets/courseDetail/scratch_select.png'),
  Access: require('../../assets/courseDetail/mdb_select.png'),
  Excel: require('../../assets/courseDetail/excel_select.png'),
  CodeBlank: require('../../assets/courseDetail/codeBlank_select.png'),
};

const checkIsOpen = (tree) => {
  if (tree.isOpen === false || tree.isOpen) {
    return tree.isOpen;
  }
  const { children } = tree;
  if (!children || !Array.isArray(children) || !children.length) {
    return false;
  }

  return children.some((child) => checkIsOpen(child));
};

export default function CourseDetailPage() {
  const [courseDetail, setCourseDetail] = useState(null);
  const { setNavName } = useModel('global');
  // 获取用户信息
  const session = useContext(SessionContext);
  // 获取地址栏参数
  const url = window.location.href;
  const parts = url.split('/');
  const courseSlug = parts[parts.length - 1];

  const [checkedKeys, setCheckedKeys] = useState([]);
  const [curStep, setCurStep] = useState(-1);
  const [currentCourse, setCurrentCourse] = useState(null);
  
  // 同步相关状态
  const [sectionStatus, setSectionStatus] = useState({});
  const [remoteCourseInfo, setRemoteCourseInfo] = useState(null);
  const [autoSync, setAutoSync] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [syncDetail, setSyncDetail] = useState(''); // 当前同步的小节
  const [downloadSpeed, setDownloadSpeed] = useState(''); // 下载速度
  const [wsConnected, setWsConnected] = useState(false); // WebSocket 连接状态
  const [hasAutoSynced, setHasAutoSynced] = useState(false); // 标记是否已执行过自动同步
  const [sectionDisabled, setSectionDisabled] = useState(false);

  // 加载课程详情
  useEffect(() => {
    getCourseDetail(courseSlug)
      .then((res) => {
        const { code, message, data } = res;
        if (code !== 0) {
          alertError("获取课程详情失败", message)
        }
        setCourseDetail(data);
        setNavName(data.courseName)
      })
      .catch((error) => {
        alertError("获取课程详情失败", error.message)
      });
    getLoginConfig()
      .then((res) => {
        setCurrentCourse(res.data.current_course_slug)
      });
  }, []);

  // 初始化所有小节状态为"已更新"
  const initializeSectionStatus = () => {
    if (!courseDetail) return;
    
    const statusMap = {};
    const localChapters = courseDetail.indics || [];
    
    // 初始化所有小节为"已更新"状态
    localChapters.forEach(chapter => {
      const { chapterName, sections = [] } = chapter;
      sections.forEach(section => {
        const { sectionName } = section;
        const key = `${chapterName}_${sectionName}`;
        // statusMap[key] = '已更新'; // 默认状态
      });
    });
    
    setSectionStatus(statusMap);
  };

  // 加载远程课程信息并提取状态
  // 后端已经在返回数据中包含 modify_status 字段
  const loadRemoteCourseInfo = async () => {
    try {
      const res = await getRemoteCourseInfo(courseSlug);
      if (res.code === 0) {
        setRemoteCourseInfo(res.data);
        
        // 直接从后端返回的数据中提取状态（后端已经计算好了）
        const statusMap = calculateAllSectionStatus(res.data);
        setSectionStatus(statusMap);
        
        console.log('📊 课程状态已加载：', statusMap);
      }
    } catch (error) {
      // 如果后端未实现，静默失败，使用默认状态
      console.log('远程课程信息获取失败，使用默认状态', error);
    }
  };

  // WebSocket 连接管理 - Teacher房间（用于教师端通知）
  useEffect(() => {
    console.log('[课程详情] 正在连接 Teacher WebSocket...');
    
    // 连接到 teacher 房间
    courseWS.connect('teacher');
    
    const handleConnected = () => {
      console.log('[课程详情] Teacher WebSocket 已连接');
    };
    
    const handleDisconnected = () => {
      console.log('[课程详情] Teacher WebSocket 已断开');
    };
    
    courseWS.on('connected', handleConnected);
    courseWS.on('disconnected', handleDisconnected);
    
    return () => {
      courseWS.off('connected', handleConnected);
      courseWS.off('disconnected', handleDisconnected);
    };
  }, []);

  // WebSocket 连接管理 - 课程更新（连接46046端口）
  useEffect(() => {
    console.log('[课程详情] 准备课程更新 WebSocket 监听器...');
    
    // 监听连接状态
    const handleConnected = () => {
      console.log('[课程详情] Update WebSocket 已连接（46046端口）');
      setWsConnected(true);
    };
    
    const handleDisconnected = () => {
      console.log('[课程详情] Update WebSocket 已断开');
      setWsConnected(false);
    };
    
    // 监听更新进度
    const handleUpdateProgress = (data) => {
      console.log('[课程详情] 收到更新进度:', data);
      const percent = data.percent || 0;
      setSyncProgress(Math.round(percent));
      
      // 根据进度阶段显示不同的提示
      if (percent < 85) {
        setSyncDetail(`正在下载课程... ${percent.toFixed(1)}%`);
      } else if (percent < 100) {
        setSyncDetail(`正在解压课程... ${percent.toFixed(1)}%`);
      } else {
        setSyncDetail('同步完成！');
      }
    };
    
    // 监听更新完成
    const handleUpdateCompleted = async (data) => {
      console.log('[课程详情] 更新完成:', data);
      setSyncing(false);
      setSyncProgress(100);
      message.success('课程同步完成！');
      setSectionDisabled( false)

      // 等待3秒后重新加载课程数据和状态
      setTimeout(async () => {
        try {
          console.log('[课程详情] 3秒后开始重新加载课程数据...');

          // 1. 重新加载课程详情
          const result = await getCourseDetail(courseSlug);
          if (result.code === 0) {
            setCourseDetail(result.data);
            setNavName(result.data.courseName);
            console.log('[课程详情] 课程详情已更新');
          }

          // 2. 重新加载远程课程信息和状态
          await loadRemoteCourseInfo();
          console.log('[课程详情] 课程状态已重新加载');

          // 3. 重置同步相关状态到初始状态
          setSyncProgress(0);
          setSyncDetail('');
          setDownloadSpeed('');
          setSectionDisabled(false); // 重新启用section点击

          console.log('[课程详情] 同步完成后的数据刷新已完成');

        } catch (error) {
          console.error('重新加载课程失败:', error);
          message.error('刷新课程数据失败: ' + (error.message || '未知错误'));
        }
      }, 3000); // 等待3秒
    };
    
    // 监听更新错误
    const handleUpdateError = (data) => {
      console.error('[课程详情] 更新失败:', data);
      setSyncing(false);
      setSectionDisabled(false); // 重新启用section点击
      message.error(`同步失败：${data.message || '未知错误'}`);
      setSyncProgress(0);
      setSyncDetail('');
    };
    
    // 注册事件监听
    courseUpdateWS.on('connected', handleConnected);
    courseUpdateWS.on('disconnected', handleDisconnected);
    courseUpdateWS.on('progress', handleUpdateProgress);
    courseUpdateWS.on('completed', handleUpdateCompleted);
    courseUpdateWS.on('error', handleUpdateError);
    
    // 组件卸载时清理
    return () => {
      console.log('[课程详情] 清理 Update WebSocket 监听器');
      courseUpdateWS.off('connected', handleConnected);
      courseUpdateWS.off('disconnected', handleDisconnected);
      courseUpdateWS.off('progress', handleUpdateProgress);
      courseUpdateWS.off('completed', handleUpdateCompleted);
      courseUpdateWS.off('error', handleUpdateError);
      // 注意：不要关闭连接，因为可能还在同步中
    };
  }, [courseSlug]);

  // 加载课程详情后，初始化状态
  useEffect(() => {
    if (courseDetail) {
      initializeSectionStatus();
      // 尝试加载远程信息（如果后端实现了）
      loadRemoteCourseInfo();
    }
  }, [courseDetail]);

  // 自动同步 - 仅在进入页面时执行一次
  useEffect(() => {
    let initialTimer;

    // 只有在开启自动同步、有课程数据、未同步中、且未执行过自动同步时才执行
    if (autoSync && courseDetail && !syncing && !hasAutoSynced) {
      console.log('[课程详情] 自动同步开启，3秒后将执行一次同步...');
      initialTimer = setTimeout(async () => {
        await checkAndSync();
        setHasAutoSynced(true); // 标记已执行过自动同步
      }, 3000);
    }

    return () => {
      if (initialTimer) clearTimeout(initialTimer);
    };
  }, [autoSync, courseDetail, syncing, hasAutoSynced]);

  // 检查并自动同步
  const checkAndSync = async () => {
    try {
      console.log('[课程详情] 开始检查课程更新状态...');
      await loadRemoteCourseInfo();

      // 检查是否有需要同步的内容
      const hasUpdates = Object.values(sectionStatus).some(
        status => status === '待下载' || status === '待更新' || status === '待删除'
      );

      if (hasUpdates) {
        console.log('[课程详情] 发现课程更新，开始自动同步...');
        message.info('发现课程更新，开始自动同步...');
        // 直接执行同步，跳过确认对话框
        await executeSyncCourse();
      } else {
        console.log('[课程详情] 课程已是最新版本，无需同步');
      }
    } catch (error) {
      console.error('[课程详情] 检查课程更新失败:', error);
      message.error('检查课程更新失败: ' + (error.message || '未知错误'));
    }
  };

  // 手动同步课程
  const handleSyncCourse = async () => {
    const loginStatusResult = await getLoginConfig()
                  if (!loginStatusResult || loginStatusResult.code) {
                    return alertError("错误", `${loginStatusResult && loginStatusResult.message ? loginStatusResult.message : "请检查教师端服务是否打开！"}`);
                  }

                  const netWorkInfo = await checkNetworkStatus();

                  if (!netWorkInfo) {
                    alertError('请检查您的网络环境')
                    return;
                  }

                  // 调取code
                  const result = await bindLogin({
                    username: loginStatusResult.data.default_username,
                    password: loginStatusResult.data.default_password,
                    site_url: loginStatusResult.data.site_url,
                    lab: loginStatusResult.data.lab,
                    mac: localStorage.mac,
                  })

                  if(!result || result.code) {
                    alertError("错误", result && result.message ? result.message : '请检查您的网络环境!')
                    return;
                  }
    // 统计待同步项
    const counts = { 待下载: 0, 待更新: 0, 待删除: 0 };
    Object.values(sectionStatus).forEach(status => {
      if (counts[status] !== undefined) {
        counts[status]++;
      }
    });
    
    const totalNeedSync = counts.待下载 + counts.待更新 + counts.待删除;
    
    if (totalNeedSync === 0) {
      message.info('课程已是最新，无需同步');
      return;
    }

    Modal.confirm({
      title: '确认同步课程',
      content: (
        <div>
          <p>检测到以下更新：</p>
          {counts.待下载 > 0 && <p>• 待下载: {counts.待下载} 个小节</p>}
          {counts.待更新 > 0 && <p>• 待更新: {counts.待更新} 个小节</p>}
          {counts.待删除 > 0 && <p>• 待删除: {counts.待删除} 个小节</p>}
          <p style={{ marginTop: 10, color: '#666' }}>
            是否立即同步？同步过程可能需要几分钟。
          </p>
        </div>
      ),
      okText: '立即同步',
      cancelText: '取消',
      onOk: async () => {
        setSectionDisabled(true)
        await executeSyncCourse();
      },
    });
  };

  // 执行同步操作（通过 PUT 接口触发）
  const executeSyncCourse = async () => {
    setSyncing(true);
    setSectionDisabled(true); // 禁用所有section点击
    setSyncProgress(0);
    setSyncDetail('正在启动同步任务...');
    
    const hide = message.loading('正在启动课程同步...', 0);
    
    try {
      // 调用后端接口触发课程更新
      console.log('[课程同步] 调用更新接口:', courseSlug);
      const result = await updateCourseContent(courseSlug);
      
      hide();
      
      if (result && result.code === 0) {
        message.success('同步任务已启动！');
        setSyncDetail('正在准备下载...');
        
        // 连接到46046端口接收进度
        console.log('[课程同步] 连接WebSocket接收进度（46046端口）');
        courseUpdateWS.connect();
        
        // 等待连接建立
        await new Promise(resolve => setTimeout(resolve, 500));
        
        if (!courseUpdateWS.isConnected()) {
          // 连接还未建立，但任务已经开始，进度会在连接建立后推送
          console.log('[课程同步] WebSocket连接中，等待进度推送...');
        }
      } else {
        message.error('启动同步任务失败: ' + (result?.message || '未知错误'));
        setSyncing(false);
        setSectionDisabled(false); // 重新启用section点击
        setSyncProgress(0);
        setSyncDetail('');
      }
      
    } catch (error) {
      hide();
      console.error('[课程同步] 同步课程失败:', error);
      message.error('同步过程中出现错误: ' + (error.message || '未知错误'));
      setSyncing(false);
      setSectionDisabled(false); // 重新启用section点击
      setSyncProgress(0);
      setSyncDetail('');
    }
  };

  if (!courseDetail) {
    return;
  }

  let { courseName, courseType, courseDescription, creater, indics: directory, sectionData = [], selectedKey = '' } = courseDetail;
  // let isAdmin = session.code === 0 ? session.data?.isAdmin : null;
  let isAdmin = 1;

  directory = formatCourseData(directory, sectionData, isAdmin);

  // 节跳转
  const gotoSection = (chapterName, sectionName) => {
    history.replace(`/course/sectionDetail/${courseSlug}/${chapterName}/${sectionName}`);
  };

  return (
    <main className="flex justify-center flex-1">
      <div className="w-full mx-auto max-w-screen-xl">
        <div style={{userSelect: 'none'}}>
          <style>
            {`
        .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
          padding: 0;
          word-wrap: break-word;
          word-break: break-all;
        }

        .ant-switch-checked {
          background-color: #235A9B;
        }

        .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle {
          display: none;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:before {
          background-color: #1b487e;
        }
        
        .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper:hover:before {
          background-color: #1b487e;
        }
        
        .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper:hover:before {
          background: rgba(27,72,126,0.8);
        }

        .ant-tree .ant-tree-switcher .ant-tree-switcher-icon , .ant-tree .ant-tree-switcher .ant-select-tree-switcher-icon{
          font-size: 20px;
          vertical-align: middle;
          line-height: 35px;
        }
        
        .ant-tree .ant-tree-switcher:before {
          height: 42px;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher {
          color: #1b487e;
        }

        .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher {
          color: #fff !important;
        }

        .ant-tree-treenode {
          align-items: center !important;
        }
        
        .ant-tree .ant-tree-checkbox {
          margin-top: 12px;
          margin-left: 5px;
        }

      `}
          </style>
          <div>
            <div className="bg-white rounded-lg w-full">
              <Row gutter={20}>
                <Col xs={24} sm={24} md={7} lg={7} xl={7}>
                  <Affix offsetTop={90}>
                    <div style={{overflowY: 'auto'}}>
                      <div className="bg-white rounded-lg mx-auto my-10 relative shadow-lg" style={{ maxHeight: '465px', maxWidth: '300px' }}>
                      <img className="h-240 w-350 block mx-auto select-none" src={`${globalVars.baseURL}/file/course/${courseSlug}/assets/course.png?t=${new Date().getTime()}`} alt=" " />

                      <div className="text-left p-2">

                        <div className="text-xl font-semibold overflow-hidden overflow-ellipsis whitespace-nowrap">
                          <Tooltip title={courseName || ''}>
                            {courseName}
                          </Tooltip>
                        </div>
                        <div className="mt-2.5 text-gray-400 text-lg overflow-hidden line-clamp-2">
                          {
                            courseDescription ? (
                              <Tooltip title={courseDescription} className="break-all">
                                简介:
                                {courseDescription}
                              </Tooltip>
                            ) : <div style={{height: '64px'}}/>
                          }
                        </div>
                      </div>
                      <div className="w-full mx-auto h-0.5 bg-gray-200 my-2"/>
                      <div className="p-2">
                        <div className="flex justify-between items-center">
                          <img className="w-7 h-7 rounded-full" src={creater && creater.avatar ? creater.avatar : require('@/assets/errorAvatar.png')} onError={(e) => { e.target.src = require('@/assets/errorAvatar.png'); }} alt=""/>
                          <span className="text-base font-bold align-top w-1/2">{creater && creater.name ? creater.name : ''}</span>
                          <div className="text-base" style={{color: courseType === '选修课' ? 'orange' : 'red'}}>{courseType}</div>
                        </div>
                      </div>
                    </div>

                    {/* 同步控制区域 */}
                    <div className="bg-white rounded-lg mx-auto mt-4 p-4 shadow-lg" style={{ maxWidth: '300px' }}>
                      {/* 自动同步开关 */}
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-sm">自动同步</span>
                        <Checkbox
                          checked={autoSync}
                          onChange={(e) => {
                            const checked = e.target.checked;
                            setAutoSync(checked);
                            // 如果重新开启自动同步，重置执行标记
                            if (checked) {
                              setHasAutoSynced(false);
                            }
                          }}
                        />
                      </div>

                      {/* 同步按钮 - 只在未开启自动同步时显示 */}
                      {!autoSync && (
                        <Button
                          type="primary"
                          block
                          onClick={handleSyncCourse}
                          loading={syncing}
                          disabled={syncing}
                          size="large"
                        >
                          {syncing ? `同步中 ${syncProgress}%` : '同步线上课程'}
                        </Button>
                      )}

                      {/* 自动同步状态提示 */}
                      {autoSync && !syncing && (
                        <div className="text-center p-3 bg-blue-50 rounded border border-blue-200">
                          <div className="text-sm text-blue-600 mb-1">🔄 自动同步已开启</div>
                          <div className="text-xs text-gray-500">
                            {hasAutoSynced ? '已完成自动同步检查' : '将在3秒后自动检查并同步'}
                          </div>
                        </div>
                      )}

                      {/* WebSocket 连接状态指示器 */}
                      <div className="mt-2 text-xs text-center" style={{ color: wsConnected ? '#52c41a' : '#999' }}>
                        {wsConnected ? '● 实时连接已建立' : '○ 等待连接...'}
                      </div>

                      {/* 进度条和详细信息 */}
                      {syncing && (
                        <div className="mt-3">
                          <Progress 
                            percent={syncProgress} 
                            size="small"
                            status="active"
                          />
                          
                          {/* WebSocket 实时信息 */}
                          {syncDetail && (
                            <div className="mt-2 text-sm text-gray-600">
                              <div>📦 正在处理: {syncDetail}</div>
                            </div>
                          )}
                          {downloadSpeed && (
                            <div className="mt-1 text-xs text-gray-500">
                              ⚡ 速度: {downloadSpeed}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    </div>
                  </Affix>
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                  <div>
                    {
                      directory && directory.length
                        ? (
                          <div style={{padding: '20px', backgroundColor: 'white'}}>
                            <DirectoryTree
                              expandAction={false}  // 禁用点击节点展开
                              defaultExpandAll
                              selectedKey={[selectedKey]}
                              showIcon={false}
                              titleRender={(treeNode) => {
                                // 获取当前节点的更新状态
                                const updateStatus = treeNode.nodeType === 'section' 
                                  ? sectionStatus[treeNode.key] 
                                  : null;
                                
                                const statusConfig = {
                                  '待下载': { color: '#ff4d4f', text: '待下载', bg: '#fff1f0', border: '#ffccc7' },
                                  '待更新': { color: '#faad14', text: '待更新', bg: '#fffbe6', border: '#ffe58f' },
                                  '待删除': { color: '#f5222d', text: '待删除', bg: '#fff1f0', border: '#ffa39e' },
                                  '已更新': { color: '#52c41a', text: '已更新', bg: '#f6ffed', border: '#b7eb8f' },
                                };
                                
                                const currentStatus = updateStatus ? statusConfig[updateStatus] : null;
                                
                                // 判断状态
                                const isDownloadPending = updateStatus === '待下载';
                                const isToBeDeleted = updateStatus === '待删除';
                                const isSectionDisabled = sectionDisabled && treeNode.nodeType === 'section';

                                // 根据状态设置样式
                                const textColor = (isDownloadPending || isToBeDeleted || isSectionDisabled) ? '#999' : (selectedKey === treeNode.key ? 'white' : 'black');
                                const textDecoration = isToBeDeleted ? 'line-through' : 'none';

                                return (
                                  <div className="flex items-center justify-between w-full" style={{
                                    margin: '15px 0 10px 0',
                                    opacity: (isDownloadPending || isSectionDisabled) ? 0.6 : 1,
                                    cursor: (isDownloadPending || isSectionDisabled) && treeNode.nodeType === 'section' ? 'not-allowed' : 'pointer'
                                  }}>
                                    <div className="flex items-center flex-1">
                                    {
                                      treeNode.mustBeDone ? (
                                          <Tooltip title="必做题做完之后才能开启下一个章节">
                                            <div className="flex items-center">
                                              {
                                                treeNode.nodeType === 'section' ? (
                                                  <img alt="" src={ selectedKey === treeNode.key ? imgMapWhite[treeNode.sectionType] : imgMap[treeNode.sectionType]}
                                                       style={{
                                                         color: '#2f99d5',
                                                         marginRight: '10px',
                                                         height: 'auto',
                                                         width: '24px',
                                                         marginTop: '-7px',
                                                       }}
                                                  />
                                                ) : ''
                                              }
                                              <span style={{
                                                color: textColor,
                                                fontSize: treeNode.nodeType === 'chapter' ? '22px' : '18px', 
                                                marginTop: '-7px',
                                                textDecoration: textDecoration
                                              }}>
                                            {
                                              treeNode.mustBeDone ? (<span style={{color: (isDownloadPending || isToBeDeleted || isSectionDisabled) ? '#999' : 'red', marginRight: '2px'}}>*</span>) : ''
                                            }
                                                {
                                                  `${treeNode.nodeType === 'chapter' ? (`章节${toChinesNum(parseInt(treeNode.index, 10) + 1)}`) : `${parseInt(treeNode.index, 10) + 1}.`}`
                                                }
                                                {
                                                  `${treeNode.title}`}
                                                {treeNode.status === '未通过' && treeNode.sectionType !== 'AI' ? (
                                                  <CloseCircleOutlined style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'red',}}/>
                                                ) : ''}
                                                {treeNode.status === '未通过' && treeNode.sectionType === 'AI' ? (
                                                  <MinusCircleOutlined style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: '#d8d81c',}}/>
                                                ) : ''}
                                                {treeNode.status === '已通过' ? (
                                                  <CheckCircleOutlined style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'green',}}/>
                                                ) : ''}
                                                {((!checkIsOpen(treeNode) || treeNode.disabled) && treeNode.nodeType === 'chapter')
                                                  ? (
                                                    <img style={{display: 'inline-block', width: '20px', height: 'auto', marginLeft: '5px', marginTop: '-7px',}}
                                                         src={selectedKey === treeNode.key ? require('../../assets/courseDetail/lock_white.png') : require('../../assets/courseDetail/lock_black.png')} alt=""
                                                    />
                                                  ) : ''}
                                            </span>
                                            </div>
                                          </Tooltip>
                                        )
                                        : (
                                          <>
                                            {
                                              treeNode.nodeType === 'section' ? (
                                                <img
                                                  alt=""
                                                  src={selectedKey === treeNode.key ? imgMapWhite[treeNode.sectionType] : imgMap[treeNode.sectionType]}
                                                  style={{
                                                    color: '#2f99d5',
                                                    marginRight: '12px',
                                                    height: 'auto',
                                                    width: '24px',
                                                    marginTop: '-7px',
                                                  }}
                                                />
                                              ) : ''
                                            }
                                            <span style={{
                                              color: textColor,
                                              fontSize: treeNode.nodeType === 'chapter' ? '22px' : '18px', 
                                              fontWeight: treeNode.nodeType === 'chapter' ? 'bolder' : 'normal', 
                                              marginTop: '-7px',
                                              textDecoration: textDecoration
                                            }}>
                                          {
                                            treeNode.mustBeDone ? (
                                              <span style={{color: (isDownloadPending || isToBeDeleted || isSectionDisabled) ? '#999' : 'red', marginRight: '1px'}}>*</span>
                                            ) : ''
                                          }
                                              {`${treeNode.nodeType === 'chapter' ? (`章节${toChinesNum(parseInt(treeNode.index, 10) + 1)}`) : `${parseInt(treeNode.index, 10) + 1}.`}`}
                                              &nbsp;&nbsp;
                                              {`${treeNode.title}`}
                                              {treeNode.status === '未通过' && treeNode.sectionType !== 'AI' ? (
                                                <CloseCircleOutlined style={{
                                                  display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'red',
                                                }}
                                                />
                                              ) : ''}
                                              {treeNode.status === '未通过' && treeNode.sectionType === 'AI' ? (
                                                <MinusCircleOutlined
                                                  src={require('../../assets/courseDetail/wrong.png')}
                                                  style={{
                                                    display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: '#d8d81c',
                                                  }}
                                                />
                                              ) : ''}
                                              {treeNode.status === '已通过' ? (
                                                <CheckCircleOutlined style={{
                                                  display: 'inline-block', width: '20px', height: 'auto', marginLeft: '8px', marginTop: '-5px', color: 'green',
                                                }}
                                                />
                                              ) : ''}
                                              {((!checkIsOpen(treeNode) || treeNode.disabled || treeNode.afterMustBeDone) && treeNode.nodeType === 'chapter') ? (
                                                <img
                                                  style={{
                                                    display: 'inline-block', width: '20px', height: 'auto', marginLeft: '5px', marginTop: '-7px',
                                                  }}
                                                  src={selectedKey === treeNode.key ? require('../../assets/courseDetail/lock_white.png') : require('../../assets/courseDetail/lock_black.png')}
                                                  alt=""
                                                />
                                              ) : ''}
                                        </span>
                                          </>
                                        )
                                    }
                                    </div>
                                    
                                    {/* 状态标签 */}
                                    {currentStatus && treeNode.nodeType === 'section' && (
                                      <span 
                                        style={{
                                          display: 'inline-block',
                                          fontSize: '12px',
                                          padding: '2px 10px',
                                          borderRadius: '8px',
                                          backgroundColor: currentStatus.bg,
                                          color: currentStatus.color,
                                          border: `1px solid ${currentStatus.border}`,
                                          marginLeft: '10px',
                                          whiteSpace: 'nowrap',
                                          fontWeight: '500',
                                          flexShrink: 0
                                        }}
                                      >
                                        {currentStatus.text}
                                      </span>
                                    )}
                                  </div>
                                );
                              }}
                              treeData={directory}
                              onSelect={(e, {node}) => {
                                const { isOpen, chapterName, key, sectionName, afterMustBeDone, nodeType } = node;

                                selectedKey = key;

                                if (nodeType === 'chapter') {
                                  return;
                                }

                                if (nodeType === 'section' && !sectionName) {
                                  alertError("课程不存在")
                                  return;
                                }

                                // 检查是否全局禁用section点击
                                if (nodeType === 'section' && sectionDisabled) {
                                  message.warning('课程同步中，请稍后再试');
                                  return;
                                }

                                // 检查是否为待下载状态
                                const updateStatus = sectionStatus[key];
                                if (updateStatus === '待下载') {
                                  message.warning('该小节尚未下载，请先同步课程');
                                  return;
                                }

                                if (isOpen === true && chapterName && sectionName) {
                                  gotoSection(chapterName, sectionName);
                                }
                              }}
                              onCheck={(e) => {
                                setCheckedKeys(e);
                                if (e.length > 0) {
                                  setCurStep(1)
                                } else {
                                  setCurStep(0)
                                }
                              }}
                              checkedKeys={checkedKeys}
                            />
                          </div>
                        )
                        : <div style={{fontSize: '20px', textAlign: 'center', marginTop: '30%'}}>暂无内容~</div>
                    }
                  </div>
                </Col>
                <Col xs={24} sm={24} md={5} lg={5} xl={5}>
                  <Affix offsetTop={90}>
                    {/*<div style={{ marginTop: -10, fontWeight: 'bold' }}>学生机在线人数：{ 0 }</div>*/}
                    <div style={{ marginTop: 20, width: 180 }}>
                    <Steps
                      direction="vertical"
                      current={curStep}
                      items={[
                        // {
                        //   title: '课程配置',
                        //   description: '请在左侧选择本次向学生开放的章节',
                        // },
                        {
                          title: '数据分发',
                          description: '推送课程包到学生机，并打开登录界面',
                        },
                        {
                          title: '学生登陆',
                          description: '学生输入密码登录，进入课程目录',
                        },
                        {
                          title: '过程反馈',
                          description: '学生操作后，展示成绩并汇总至教师机',
                        },
                        {
                          title: '数据上传',
                          description: '课后上传过程化评价及微课记录至云平台',
                        },
                      ]}
                    />
                    <Button
                      style={{ marginTop: 15, width: 180 }}
                      onClick={ async () => {
                        // if (checkedKeys.length === 0) {
                        //   notification.warning({
                        //     message: '请在左侧选择本次向学生开放的章节！',
                        //     placement: 'bottomRight'
                        //   });
                        //   return;
                        // }

                        if (currentCourse && currentCourse !== courseSlug) {
                          return Modal.confirm({
                            title: '已有进行中的课程',
                            content: '是否结束进行中的课程然后开始上课？',
                            okText: '确定',
                            cancelText: '取消',
                            onOk: async () => {
                              await finishClass();
                              const res = await startClass({courseSlug})
                              if (res.code === 0) {
                                setCurrentCourse(courseSlug);
                                alertSuccess('已开始上课！');
                                // setCurStep(2);
                              } else {
                                alertError('错误', res.message);
                              }
                            }
                          })
                        }

                        const res = await startClass({courseSlug})

                        if (res.code === 0) {
                          alertSuccess('已开始上课！');
                          // setCurStep(2);
                        } else {
                          alertError('错误', res.message);
                        }

                      }}
                      loading={false}
                      type="primary"
                    >
                      <StarFilled style={{ color: '#FADB14' }} />
                      开始上课
                    </Button>

                    <Button
                      style={{ marginTop: 15, width: 180 }}
                      onClick={ async () => {
                        await finishClass();
                        alertSuccess('已结束上课！');
                        // setCurStep(4);
                      }}
                      >
                      <PoweroffOutlined />
                      结束上课
                    </Button>

                    <Button
                      style={{ marginTop: 15, width: 180 }}
                      onClick={() => {
                        history.push(`/course/courseProgress/${courseSlug}`);
                      }}
                    >
                      <LineChartOutlined />
                      课程进度
                    </Button>
                  </div>
                  </Affix>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
