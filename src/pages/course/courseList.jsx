import React, { useEffect, useState } from 'react';
import Card from "@/components/Layout/Card";
import { Button, Checkbox, Tooltip, Modal } from 'antd';
import { history } from '@umijs/max';
import { deleteCourses, getCourseList } from '@/services/course';
import { createVirtualDisk } from '@/services/user';
import globalVars from '@/utils/global';
import { DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import { bindLogin, getLoginConfig } from '@/services/login';
import { checkNetworkStatus } from '@/utils/utils';
import { getCurrentWebviewWindow, WebviewWindow } from '@tauri-apps/api/webviewWindow';
import ReactJoyride from '@/components/ReactJoyride';
import { alertError, alertSuccess } from '@/utils/edittools';

export default function CoursePage() {
  let [courseList, setCourseList] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentCourse, setCurrentCourse] = useState(null);
  const [status, setStatus] = useState(localStorage['showJoyride'] === '1' ? true : false);
  const [checkedList, setCheckedList] = useState([]);

  const onSetStatus = (data) => {
    setStatus(data);
  };

  // 页面加载时检查是否有未关闭的窗口
  useEffect(() => {
    const checkExistingWindow = async () => {
      const lastLabel = localStorage.getItem("lastWindowLabel");
      if (!lastLabel) return;

      try {
        const existingWindow = await WebviewWindow.getByLabel(lastLabel);
        if (existingWindow) {
          await existingWindow.onCloseRequested(() => {
            localStorage.removeItem("lastWindowLabel");
            window.location.reload();
          });
        } else {
          localStorage.removeItem("lastWindowLabel"); // 窗口不存在
        }
      } catch (err) {
        console.error("获取窗口失败:", err);
        localStorage.removeItem("lastWindowLabel"); // 出错清理
      }
    };

    checkExistingWindow();
  }, []);

  // 加载课程列表
  useEffect(() => {
    getCourseList()
      .then((res) => {
        const { code, message, data } = res;
        if (code !== 0) {
          alertError("获取课程失败", message)
        }
        setCourseList(data);
        setLoading(false);
      })
      .catch((error) => {
        alertError("获取课程失败", error.message)
        setLoading(false);
      });

    getLoginConfig()
      .then((res) => {
        setCurrentCourse(res.data.current_course_slug)
      });
  }, [loading]);

  if (!courseList) {
    return;
  }

  function courseDetail(courseSlug) {
    // 创建虚拟盘
    createVirtualDisk(courseSlug, globalVars.userInfo.user_id)
      .then((res) => {
         if (res.code !== 0) {
          alertError("创建虚拟盘失败", res.data)
          return;
        }
      });

    // 跳转到课程详情
    history.push(`/course/courseDetail/${courseSlug}`);
  }

  return (
    <main className="flex justify-center flex-1 p-4">
      <div className="w-full mx-auto max-w-screen-xl relative">
        {/* 主内容区 */}
        {
          courseList && (courseList.ungrouped || courseList.grouped) ? (
            <div style={{ position: 'absolute', top: '-40px', right: '0' }} className={'flex justify-end'}>
              <Button
                type="primary"
                onClick={async() => {
                  const loginStatusResult = await getLoginConfig()
                  if (!loginStatusResult || loginStatusResult.code) {
                    return alertError("错误", `${loginStatusResult && loginStatusResult.message ? loginStatusResult.message : "请检查教师端服务是否打开！"}`);
                  }

                  const netWorkInfo = await checkNetworkStatus();

                  if (!netWorkInfo) {
                    alertError('请检查您的网络环境')
                    return;
                  }

                  // 调取code
                  const result = await bindLogin({
                    username: loginStatusResult.data.default_username,
                    password: loginStatusResult.data.default_password,
                    site_url: loginStatusResult.data.site_url,
                    lab: loginStatusResult.data.lab,
                    mac: localStorage.mac,
                  })

                  if(!result || result.code) {
                    alertError("错误", result && result.message ? result.message : '请检查您的网络环境!')
                    return;
                  }

                  // 替换https为http，这样才能连接本地的ws服务
                  if (loginStatusResult.data.site_url.startsWith('https')) {
                    loginStatusResult.data.site_url = loginStatusResult.data.site_url.replace('https', 'http')
                  }

                  const urlStr = `${loginStatusResult.data.site_url}/admin/#/trainAnalyzeLogin?code=${result.data.loginCode}&toUrl=course`
                  const mainWindow = getCurrentWebviewWindow();
                  try {
                    const webview = new WebviewWindow('downloadCourse', {
                      url: urlStr,
                      title: "氦星人日常教学系统",
                      resizable: true,
                      width: 1280,
                      height: 720,
                      center: true,
                      decorations: true,
                      parent: mainWindow,
                    });
                    localStorage.setItem("lastWindowLabel", "downloadCourse"); // 存储 label
                    await webview.onCloseRequested(() => {
                      localStorage.removeItem("lastWindowLabel");
                      window.location.reload();
                    });
                  }
                  catch(e) {
                    // 用默认浏览器打开
                    window.open(urlStr);
                  }
                }}
                icon={<LinkOutlined />}
              >
                下载课程
              </Button>

              {
                checkedList.length > 0 ?  (
                  <Button
                    style={{ marginLeft: '15px' }}
                    type="primary"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      // 确认框
                      Modal.confirm({
                        title: '删除课程',
                        content: '确认删除所选课程？',
                        okText: '确认',
                        cancelText: '取消',
                        onOk: async () => {
                          setLoading(true);
                          const response = await deleteCourses({
                            courseSlugList: checkedList
                          });

                          if (!response || response.code !== 0) {
                            alertError("错误", response.message);
                            return;
                          }
                          alertSuccess('删除成功');
                          setLoading(false);
                          setCheckedList([]);
                        }
                      })
                    }}
                  >
                    删除课程
                  </Button>
                ) : null
              }
            </div>
          ) : null
        }
        {
          courseList && (courseList.ungrouped || courseList.grouped) ?
            (
              <div>
                {/* 未分组或单独课程 */}
                {courseList.ungrouped && courseList.ungrouped.length > 0 && (
                  <div className="mb-8">
                    <h2 className="text-xl font-bold mb-4">
                      未分组课程
                      <Checkbox
                        style={{ marginLeft: '10px' }}
                        indeterminate={checkedList.filter(courseSlug => courseList.ungrouped.map(item => item.courseSlug).includes(courseSlug)).length > 0 && checkedList.filter(courseSlug => courseList.ungrouped.map(item => item.courseSlug).includes(courseSlug)).length < courseList.ungrouped.length}
                        checked={checkedList.filter(courseSlug => courseList.ungrouped.map(item => item.courseSlug).includes(courseSlug)).length === courseList.ungrouped.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setCheckedList([...new Set([...checkedList, ...courseList.ungrouped.map(item => item.courseSlug)])]);
                          } else {
                            setCheckedList(checkedList.filter(courseSlug => !courseList.ungrouped.map(item => item.courseSlug).includes(courseSlug)));
                          }
                        }}
                      >
                        全选
                      </Checkbox>
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {courseList.ungrouped.map((item, index) => {
                        const isCurrentCourse = item.courseSlug === currentCourse;
                        return (
                          <Card className={`relative cursor-pointer flex flex-col ${isCurrentCourse ? 'border-2 border-[#1677FF]' : ''}`} key={`ungrouped-${index}`}>
                            <Checkbox
                              style={{ position: 'absolute', top: '2px', right: '5px' }}
                              checked={checkedList.includes(item.courseSlug)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setCheckedList([...checkedList, item.courseSlug]);
                                } else {
                                  setCheckedList(checkedList.filter(v => v !== item.courseSlug));
                                }
                              }}
                            />
                            <img
                              className="h-auto w-full block mx-auto select-none"
                              src={`${globalVars.baseURL}/file/course/${item.courseSlug}/assets/course.png?t=${item.updated_at}`} alt=" "
                              onError={(e) => {e.target.src = require('@/assets/courseExample.png');}}
                              onClick={() => courseDetail(item.courseSlug)}
                            />
                            <div className="py-2.5 flex flex-col flex-1" onClick={() => courseDetail(item.courseSlug)}>
                              <div className="text-lg font-semibold overflow-hidden whitespace-nowrap overflow-ellipsis">
                                <Tooltip title={item && item.courseName ? item.courseName : ''}>
                                  {item.courseName}
                                </Tooltip>
                              </div>
                              <div className="mt-2.5 text-gray-400 overflow-hidden text-ellipsis whitespace-normal break-words leading-normal block lg:inline-block lg:max-w-[calc(100%-10px)]">
                                {
                                  item.courseDescription ? (
                                    <Tooltip className="break-all line-clamp-2" title={item.courseDescription}>
                                      简介:
                                      {item.courseDescription}
                                    </Tooltip>
                                  ) : <div style={{ height: '40px' }}></div>
                                }
                              </div>
                            </div>
                            <div className="w-full mx-auto h-0.5 bg-gray-200" />
                            <div className="pt-2.5 flex justify-between items-center mt-auto" onClick={() => courseDetail(item.courseSlug)}>
                              <img className="w-7 h-7 rounded-full" src={item.creater && item.creater.avatar ? item.creater.avatar : require('@/assets/errorAvatar.png')} onError={(e) => { e.target.src = require('@/assets/errorAvatar.png'); }} alt=""/>
                              <span className="text-base font-bold align-top w-1/2">{item.creater && item.creater.name ? item.creater.name : ''}</span>
                              <div className="inline-block align-bottom text-base" style={{ color: item.courseType === '选修课' ? 'orange' : 'red' }}>
                                {item.courseType}
                              </div>
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* 分组课程 */}
                {courseList.grouped && Object.keys(courseList.grouped).map(groupName => (
                  <div key={groupName} className="mb-8">
                    <h2 className="text-xl font-bold mb-4">
                      {groupName}
                      <Checkbox
                        style={{ marginLeft: '10px' }}
                        indeterminate={checkedList.filter(courseSlug => courseList.grouped[groupName].map(item => item.courseSlug).includes(courseSlug)).length > 0 && checkedList.filter(courseSlug => courseList.grouped[groupName].map(item => item.courseSlug).includes(courseSlug)).length < courseList.grouped[groupName].map(item => item.courseSlug).length}
                        checked={checkedList.filter(courseSlug => courseList.grouped[groupName].map(item => item.courseSlug).includes(courseSlug)).length === courseList.grouped[groupName].map(item => item.courseSlug).length && courseList.grouped[groupName].map(item => item.courseSlug).length > 0}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setCheckedList([...new Set([...checkedList, ...courseList.grouped[groupName].map(item => item.courseSlug)])]);
                          } else {
                            setCheckedList(checkedList.filter(courseSlug => !courseList.grouped[groupName].map(item => item.courseSlug).includes(courseSlug)));
                          }
                        }}
                      >
                        全选
                      </Checkbox>
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {courseList.grouped[groupName].map((item, index) => {
                        const isCurrentCourse = item.courseSlug === currentCourse;
                        return (
                          <Card className={`relative cursor-pointer flex flex-col ${isCurrentCourse ? 'border-2 border-[#1677FF]' : ''}`} key={`${groupName}-${index}`}>
                            <Checkbox
                              style={{ position: 'absolute', top: '2px', right: '5px' }}
                              checked={checkedList.includes(item.courseSlug)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setCheckedList([...checkedList, item.courseSlug]);
                                } else {
                                  setCheckedList(checkedList.filter(v => v !== item.courseSlug));
                                }
                              }}
                            />
                            <img
                              className="h-auto w-full block mx-auto select-none"
                              src={`${globalVars.baseURL}/file/course/${item.courseSlug}/assets/course.png?t=${item.updated_at}`} alt=" "
                              onError={(e) => {e.target.src = require('@/assets/courseExample.png');}}
                              onClick={() => courseDetail(item.courseSlug)}
                            />
                            <div className="py-2.5 flex flex-col flex-1" onClick={() => courseDetail(item.courseSlug)}>
                              <div className="text-lg font-semibold overflow-hidden whitespace-nowrap overflow-ellipsis">
                                <Tooltip title={item && item.displayName ? item.displayName : ''}>
                                  {item.displayName}
                                </Tooltip>
                              </div>
                              <div className="mt-2.5 text-gray-400 overflow-hidden text-ellipsis whitespace-normal break-words leading-normal block lg:inline-block lg:max-w-[calc(100%-10px)]">
                                {
                                  item.courseDescription ? (
                                    <Tooltip className="break-all line-clamp-2" title={item.courseDescription}>
                                      简介:
                                      {item.courseDescription}
                                    </Tooltip>
                                  ) : <div style={{ height: '40px' }}></div>
                                }
                              </div>
                            </div>
                            <div className="w-full mx-auto h-0.5 bg-gray-200" />
                            <div className="pt-2.5 flex justify-between items-center mt-auto" onClick={() => courseDetail(item.courseSlug)}>
                              <img className="w-7 h-7 rounded-full" src={item.creater && item.creater.avatar ? item.creater.avatar : require('@/assets/errorAvatar.png')} onError={(e) => { e.target.src = require('@/assets/errorAvatar.png'); }} alt=""/>
                              <span className="text-base font-bold align-top w-1/2">{item.creater && item.creater.name ? item.creater.name : ''}</span>
                              <div className="inline-block align-bottom text-base" style={{ color: item.courseType === '选修课' ? 'orange' : 'red' }}>
                                {item.courseType}
                              </div>
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            )
            : loading
              ? <div className="text-center mt-10% text-2xl w-full">加载中....</div>
              : (
                <div className="w-full select-none">
                  <img className="block mx-auto" src={require('@/assets/rocket.png')} alt=""/>
                  <div className="text-center mt-5 text-gray-400 text-xl">
                    暂无课程，请先下载课程
                  </div>
                  <Button
                    type="primary"
                    onClick={async() => {
                      const loginStatusResult = await getLoginConfig()
                      if (!loginStatusResult || loginStatusResult.code) {
                        return alertError("错误", loginStatusResult && loginStatusResult.message ? loginStatusResult.message : "请检查教师端服务是否打开！");
                      }

                      const netWorkInfo = await checkNetworkStatus();

                      if (!netWorkInfo) {
                        alertError('请检查您的网络环境')
                        return;
                      }

                      // 调取code
                      const result = await bindLogin({
                        username: loginStatusResult.data.default_username,
                        password: loginStatusResult.data.default_password,
                        site_url: loginStatusResult.data.site_url,
                        lab: loginStatusResult.data.lab,
                        mac: localStorage.mac,
                      })

                      if(!result || result.code) {
                        alertError("错误", result && result.message ? result.message : '请检查您的网络环境!')
                        return;
                      }

                      // 替换https为http，这样才能连接本地的ws服务
                      if (loginStatusResult.data.site_url.startsWith('https')) {
                        loginStatusResult.data.site_url = loginStatusResult.data.site_url.replace('https', 'http')
                      }

                      const urlStr = `${loginStatusResult.data.site_url}/admin/#/trainAnalyzeLogin?code=${result.data.loginCode}&toUrl=course`
                      const mainWindow = getCurrentWebviewWindow();
                      try {
                        const webview = new WebviewWindow('downloadCourse', {
                          url: urlStr,
                          title: "氦星人日常教学系统",
                          resizable: true,
                          width: 1280,
                          height: 720,
                          center: true,
                          decorations: true,
                          parent: mainWindow // 设置父窗口
                        });
                        localStorage.setItem("lastWindowLabel", "downloadCourse"); // 存储 label
                        await webview.onCloseRequested(() => {
                          localStorage.removeItem("lastWindowLabel");
                          window.location.reload();
                        });
                      }
                      catch(e) {
                        // 用默认浏览器打开
                        window.open(urlStr);
                      }
                    }}
                    style={{ margin: '20px auto', display: 'flex' }}
                    icon={<LinkOutlined />}
                  >
                    下载课程
                  </Button>
                </div>
              )
        }
      </div>
      <ReactJoyride run={status} onSetStatus={onSetStatus} />
    </main>
  );
}
