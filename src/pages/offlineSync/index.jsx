import React, { useEffect } from 'react';
import { getVersion } from "@tauri-apps/api/app";
import { getCurrentWebviewWindow, WebviewWindow } from "@tauri-apps/api/webviewWindow";
import { history } from '@umijs/max';
import OfflineSync from '../../components/OfflineSyncPage'; // Assuming this is a regular import

function OfflineSyncPage() {

    useEffect(() => {
        const setAppVersion = async () => {
            try {
                const appVersion = await getVersion();
                const currentWindow = getCurrentWebviewWindow();

                if (currentWindow && appVersion) {
                    currentWindow.setTitle(`氦星人日常教学系统(教师端) ${appVersion}`);
                }
            } catch (e) {
                console.error(e);
            }
        };

        setAppVersion();
    }, []); // Empty dependency array ensures this runs once after the component mounts

    const gotoLogin = () => {
        if (window.__TAURI__) {
            const webview = new WebviewWindow('login', {
                url: '/login',
                fullscreen: false,
                decorations: false,
                center: true,
                focus: true,
                width: 730,
                height: 480,
                shadow: true
            });

            webview.once('tauri://created', function () {
                getCurrentWebviewWindow().close('main');
            });
        } else {
            history.push('/login');
        }
    };

    return <OfflineSync gotoLogin={gotoLogin} />;
}

export default OfflineSyncPage;
