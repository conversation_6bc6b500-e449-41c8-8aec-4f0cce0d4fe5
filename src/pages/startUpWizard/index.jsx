import React, {useEffect, useState} from 'react';
import { Result, Button, Steps, Card, Form, Modal, Input, Select, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import styles from './index.css';
import { checkNetworkStatus } from "@/utils/utils";
import { bindLogin, getLoginConfig, stopIpython } from '@/services/login';
import {get_network_cards_info} from "@/utils/train";
import {getCurrentWebviewWindow, WebviewWindow} from "@tauri-apps/api/webviewWindow";
import moment from "moment/moment";
import JsSHA from "jssha";
import {alertError, alertSuccess, debounce} from "@/utils/edittools";
import CheckCircleOutlined from "@ant-design/icons/CheckCircleOutlined";
import globalVars from '@/utils/global';
import { invoke } from '@tauri-apps/api/core';
import { exit } from '@tauri-apps/plugin-process';
import ReactJoyride from '@/components/ReactJoyride';

const { Step } = Steps;
const { Option } = Select;


const currentStudyYear = () => {
  const month = moment().format('MM');
  if (parseInt(month) <= 7) {
    return `${moment().subtract(1, 'years').format('YYYY')}学年`;
  }

  return `${moment().format('YYYY')}学年`;
};

export default function StartUpWizardPage() {

  const [isLoading, setIsLoading] = useState(true);
  const [form] = Form.useForm();
  const [defaultValue, setDefaultValue] = useState({});
  const [pcInfo, setPcInfo] = useState([]);
  const [orginPassword, setOrginPassword] = useState(null);
  const [ifNetwork, setIfNetwork] = useState(false);
  const [resultNetworkTitle, setResultNetworkTitle] = useState('');
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    // 调用异步函数
    initData(currentStep);
  }, [currentStep]);

  const initData = async (currentStep) => {

    // 通信检测阶段
    if (currentStep === 0) {
      setIsLoading(true);
      setIfNetwork(false);
      setResultNetworkTitle('正在检测，请稍候');
      try {
        const result = await checkNetworkStatus();
        if (result) {
          setTimeout(() => {
            setResultNetworkTitle('您的网络正常！');
            setIfNetwork(true);
          }, 1500)

          setTimeout(() => {
            setCurrentStep(1);
          }, 2000)
        } else {
          setTimeout(() => {
            setResultNetworkTitle('您的网络环境不佳，请调试后再试');
          }, 1500)
        }
      } catch (error) {
        console.error('网络检测错误:', error);
      } finally {
        setIsLoading(false);
      }
    }

    // 绑定学校阶段
    if (currentStep === 1) {
      setIsLoading(true);
      try {
        let mac = null;

        // 获取登录配置
        const result = await getLoginConfig();

        // 获取网络卡信息
        const res = await get_network_cards_info();
        mac = res[0]?.mac || null;

        // 检查本地存储的 mac 地址
        if (localStorage.mac) {
          const findIndex = res.findIndex(row => row.mac === localStorage.mac);
          if (findIndex !== -1) {
            mac = localStorage.mac;
          }
        }

        if (!result || result.code || !result.data.default_username || result.data.site_url === 'demo') {
          if (localStorage.bindInfo) {
            setDefaultValue(JSON.parse(localStorage.bindInfo));
          } else {
            setDefaultValue({
              mac,
              site_url: '127.0.0.1:7001'
            });
          }
          setPcInfo(res)
          return;
        }

        if (localStorage.bindInfo) {
          setDefaultValue(JSON.parse(localStorage.bindInfo));
        } else {
          setDefaultValue({
            site_url: result.data.site_url.replace('http://', '').replace('https://', ''),
            mac,
            lab: result.data.lab, // 机房名称
          });
        }
        setPcInfo(res);
        setOrginPassword(result.data.default_password);
      } catch (error) {
        console.error('获取信息时发生错误:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // 绑定并登录
  const bind = async (data) => {
    const netWorkResult = await checkNetworkStatus();

    if(!netWorkResult || netWorkResult.code) {
      alertError('请保证您的网络连接通畅')
      return;
    }

    let encryptPassword = data.password;

    if (data.password !== orginPassword) {
      // 密码加密
      const shaObj = new JsSHA('SHA-1', 'TEXT');
      shaObj.update(data.password);
      encryptPassword = shaObj.getHash('HEX');
    }

    // 如果HOST有端口号则HTTP协议，否则默认HTTPS通信
    let site_url_str = `https://${data.site_url}`;
    if (site_url_str.match(/:(\d+)/)) {
      site_url_str = `http://${data.site_url}`;
    }

    const result = await bindLogin({
      username: data.username,
      password: encryptPassword,
      site_url: site_url_str,
      lab: data.lab,
      mac: data.mac,
    });

    if (result && result.code && result.message === '出现了账号错误 Error:请检查网址是否正确，通信是否正常，当前无法获取服务器Token！') {
      Modal.confirm({
        autoFocusButton: null,
        title: `请检查网址（${site_url_str}）是否正确？无法找到该学校网址`,
        onCancel: () => {
          new WebviewWindow('register', {
            url: 'https://hxr.iyopu.com/register',
            title: `氦星人日常教学系统`,
            resizable: true,
            width: 1280,
            height: 720,
            maximized: true,
          });
        },
        okText: '重新输入',
        cancelText: '注册新学校',
      });
      return;
    }

    if(!result || result.code) {
      alertError("错误", result && result.message ? result.message : '接口错误')
      return;
    }

    // 记录入localStorage
    localStorage.sessionResponse = JSON.stringify(result.data.user)
    localStorage.mac = data.mac;
    localStorage.bindInfo = JSON.stringify(data);
    localStorage.showJoyride = '1';

    alertSuccess("绑定成功！")
    setCurrentStep(2);
    setTimeout(() => {
      localStorage.removeItem('bindInfo')
      // 如果没有绑定信息则直接退出程序
      const webview = new WebviewWindow('main', {
        url: '/login',
        title: "教师端登录",
        fullscreen: false,
        decorations: false,
        center: true,
        focus: true,
        width: 730,
        height: 480,
        shadow: true,
        alwaysOnTop: true
      });
      webview.once('tauri://created', function () {
        getCurrentWebviewWindow().close('startUpWizard');
      });
    }, 1500)
  }

  if (isLoading) {
    return <Spin/>;
  }

  // 根据 currentStep 选择渲染内容
  const renderContent = () => {
    switch (currentStep) {
      case 0:
        return renderStep0();
      case 1:
        return renderStep1();
      default:
        return null;
    }
  };

  const renderStep0  = () => {
    return (
      <div className={styles.body}>
        <Result
          status={ifNetwork ? 'success': 'info'}
          title={resultNetworkTitle}
          icon={ifNetwork ? <CheckCircleOutlined /> : <LoadingOutlined />}
          extra={[
            <Button
              type="primary"
              key="console"
              onClick={async () => {
                setIfNetwork(false);
                const result = await checkNetworkStatus(); // 假设 checkNetworkStatus 是个 Promise 函数
                if (result) {
                  setTimeout(() => {
                    setResultNetworkTitle('您的网络正常！');
                    setIfNetwork(true);
                  }, 1500)
                  setTimeout(() => {
                    setCurrentStep(1);
                  }, 2000)
                } else {
                  setTimeout(() => {
                    setResultNetworkTitle('您的网络环境不佳，请调试后再试');
                  }, 1500)
                }
              }}
            >
              重试
            </Button>,
            <Button
              key="buy"
              onClick={() => setCurrentStep(1)}
            >
              {ifNetwork ? '下一步' : '跳过'}
            </Button>,
          ]}
        />
        </div>
    );
  }

  const renderStep1  = () => (
    <Card
      style={{
        // boxShadow: '0 0px 7px 6px rgb(165 165 165 / 10%)',
        border: 0,
        width: '60%',
        marginLeft: '20%',
        marginTop: '30px'
      }}
    >
      <Form
        name="basic"
        form={form}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        initialValues={defaultValue}
        onFinish={(e) => {
          if (defaultValue && defaultValue.site_url !== e.site_url && defaultValue.site_url !== '127.0.0.1:7001') {
            Modal.confirm({
              autoFocusButton: null,
              title: '您的学校网址已更改，是否确认？',
              content: '学校网址更改后，会将您本地数据库清空，请您核实后再进行操作。',
              onOk: () => { bind(e) },
              okText: '确定',
              cancelText: '取消'
            })
            return;
          }
          debounce(bind(e), 1500);
        }}
        autoComplete="off"
      >
        <Form.Item
          label="学校网址"
          name="site_url"
          rules={[
            { required: true, message: '请填写您学校的网址！' },
            {
              validator:(rule, value, callback) => {
                // 去除http://或https://
                const cleanedValue = value.replace('https://', '').replace('http://', '');

                // 检查是否匹配hxr.iyopu.com/s/csxx格式
                const matchFormat1 = cleanedValue.match(/^hxr\.iyopu\.com\/s\/[a-zA-Z0-9-]+$/);

                // 检查是否匹配csxx.hxr.i51cy.com格式 测试环境专用！！！
                const matchFormat2 = cleanedValue.match(/^([a-zA-Z0-9-]+\.)*hxr\.i51cy\.com$/);

                // 检查域名是否为hxr.iyopu.com
                const matchDomain1 = cleanedValue.match(/^hxr\.iyopu\.com$/);

                // 检查域名是否为csxx.hxr.i51cy.com 测试环境专用！！！
                const matchDomain2 = cleanedValue.match(/^([a-zA-Z0-9-]+\.)*hxr\.i51cy\.com$/);

                // console.log(matchFormat1, matchFormat2, matchDomain1, matchDomain2, 'matchFormat1, matchFormat2, matchDomain1, matchDomain2'  )

                // 如果填入的域名不是 hxr.iyopu.com，也不是 csxx.hxr.i51cy.com，则不检查，因为可能是单独的域名
                if (!matchDomain1 && !matchDomain2) {
                  return Promise.resolve();
                }

                // 如果填入的域名是 hxr.iyopu.com，但是格式不对，则提示错误
                if(matchDomain1 && !matchFormat1) {
                  return Promise.reject('请输入正确的学校网址，如：hxr.iyopu.com/s/csxx, 其中csxx是根据不同学校变化的');
                }

                if (matchFormat1 || matchFormat2) {
                  return Promise.resolve();
                }

                return Promise.reject('请输入正确的学校网址，如：hxr.iyopu.com/s/csxx');
              }
            },
          ]}
        >
          <Input
            addonBefore="http://"
            placeholder="127.0.0.1:7001"
            onChange={(e) => {
              // 去除http://或者https://
              let value = e.target.value.replace('https://', '').replace('http://', '');

              // 只保留127.0.0.1:7001，去除后面的内容
              const match = value.match(/^127\.0\.0\.1:7001/);
              if (match) {
                value = match[0]; // 提取匹配到的部分
              }

              // 转为小写
              value = value.toLowerCase();

              form.setFieldsValue({ site_url: value });
            }}
          />
        </Form.Item>

        <Form.Item
          label="账号"
          name="username"
          placeholder="请填写您在氦星人在线训练系统使用的账号"
          rules={[{ required: true, message: '请填写您的账号！' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="密码"
          name="password"
          placeholder="请填写您在氦星人在线训练系统使用的密码"
          rules={[{ required: true, message: '请填写您的密码！' }]}
        >
          <Input.Password />
        </Form.Item>

        <Form.Item
          label="绑定网卡"
          name="mac"
          rules={[{ required: true, message: '请选择需要绑定的网卡' }]}
        >
          <Select
            onChange={(e) => {
              const value = e.target ? e.target.value : e;
              setDefaultValue({...defaultValue, mac: value})
              localStorage.mac = value;
            }}
          >
            {
              pcInfo && pcInfo.map(row => (
                <Option value={row.mac} key={row.mac}>{row.ip}({row.name})</Option>
              ))
            }
          </Select>
        </Form.Item>

        <Form.Item
          label="机房名称"
          name="lab"
          rules={[{ required: true, message: '请填写您的机房名称！' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item label="操作">
          <Button type="primary" htmlType="submit" style={{ width: '10vw' }}>
            确认绑定
          </Button>
          <Button
            style={{ width: '10vw', marginLeft: '20px' }}
            onClick={() => {
              const webview = new WebviewWindow('register', {
                url: 'https://hxr.iyopu.com/register',
                title: `氦星人日常教学系统`,
                resizable: true,
                width: 1280,
                height: 720,
                maximized: true,
              });
            }}
          >
            注册新学校
          </Button>
        </Form.Item>

      </Form>
    </Card>
  )

  return (
    <div className={styles.startUpWizardContent}>
      <div className={styles.startUpWizardTitle}>教师账号同步</div>
      <img
        className={styles.exitButton}
        src={require("@/assets/close_gray.png")}
        onMouseDown={(e) => {
          e.stopPropagation();
        }}
        onClick={async () => {
          localStorage.removeItem('bindInfo')

          // 如果没有绑定信息则直接退出程序
          const loginConfigResponse = await getLoginConfig();
          const loginConfig = loginConfigResponse.data || null;
          if (loginConfig === null || loginConfig.site_url === 'demo') {
            // 退出程序
            await localStorage.removeItem('sessionResponse')
            await stopIpython(globalVars.winVersion);
            await invoke('stop_server');
            await exit(0);
            return;
          }
          const webview = new WebviewWindow('main', {
            url: '/login',
            title: "教师端登录",
            fullscreen: false,
            decorations: false,
            center: true,
            focus: true,
            width: 730,
            height: 480,
            shadow: true,
            alwaysOnTop: true
          });
          webview.once('tauri://created', function () {
            getCurrentWebviewWindow().close('startUpWizard');
          });
        }}
      />
      <Steps current={currentStep}>
        <Step title="通信检测" description={'检测通信是否正常'}/>
        <Step title="绑定学校" description={'通过站点完成登录或手工填入信息登录'}/>
        <Step title="完成" description={'账号信息绑定成功返回登陆'}/>
      </Steps>
      <div>{renderContent()}</div>
    </div>
  );
}
