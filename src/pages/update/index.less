.content {
  // background: url(../../assets/insBag.jpg);
  background-size: 100%;
  // height: 640px;
  // width: 1000px;
  overflow: hidden;
  display: flex;
  user-select: none;
}

.formLeftContent {
  position: relative;
  height: 640px;
  margin: 0;
  width: 500px;
  background-size: contain;

  .title {
    margin-left: 20px;
    font-size: 22px;
    height: 72px;
    line-height: 72px;
    color: white;
  }

  .footer {
    position: absolute;
    left: 10px;
    bottom: 10px;
    color: #ebe6df;
    font-size: 18px;
  }
}

.formContent {
  background: white;
  height: 640px;
  margin: 0;
  width: 500px;

  .forms {
    .header {
      height: 128px;
      line-height: 128px;
      font-size: 30px;
      font-weight: 700;
    }

    .row {
      font-size: 20px;
      height: 32px;
      line-height: 32px;
    }
    // background: gray;
    // border-radius: 15px;
    width: 400px;
    height: 384px;
    // box-shadow: 0 0 16px -6px rgb(0 0 0 / 50%);
    margin: 128px 50px;
  }
}

.p1 {
  font-size: 1.4vw;
  font-weight: bold;
  color: black;
  margin-top: 1vw;
}

.logo {
  width: 30%;
  vertical-align: top;
  margin-right: 16px;
  margin-top: 20px;
}

.user {
  height: 42px;
}

:global {
  .ant-layout {
    height: 100%;
  }
}

.password {
  height: 42px;
}

.submit {
  height: 42px;
}

.close {
  position: fixed;
  top: 7px;
  left: 980px;
  font-size: 15px;
  color: black;
}

.reBind {
  position: fixed;
  top: 610px;
  left: 510px;
  user-select: none;
}
