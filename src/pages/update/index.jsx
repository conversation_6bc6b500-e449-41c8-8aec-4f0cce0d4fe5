import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Result, <PERSON><PERSON>, Spin } from 'antd';
import { LoadingOutlined, CheckOutlined } from '@ant-design/icons';
import { checkTeacherVersion, downloadUpdateFiles } from '../../services/login.js';
import { getVersion } from '@tauri-apps/api/app';
import { alertError } from '../../utils/edittools';
import { invoke } from '@tauri-apps/api/core';
import { checkNetworkStatus } from '../../utils/utils.js';
import { getSystemConfig, updateSystemConfig } from '../../services/terminal.js';

const UPGRAGE_COUNT = 'upgrade_count';

export default function UpdatePage() {
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [changelog, setChangelog] = useState('');
  const [updates, setUpdates] = useState(null);
  const [networkStatus, setNetworkStatus] = useState(true);
  const [currentUpgradeCount, setCurrentUpgradeCount] = useState(0);

  useEffect(() => {
    checkUpdate();
  }, []);

  const checkUpdate = async () => {
    setLoading(true);

    const appVersion = await getVersion();
    const networkStatus = await checkNetworkStatus();
    setNetworkStatus(networkStatus);

    if (networkStatus) {
      const result = await checkTeacherVersion(appVersion);
      if (!result || result.code) {
        alertError("错误", result.message);
        setLoading(false);
        return;
      }

      const downloadResponse = await getSystemConfig({ key: UPGRAGE_COUNT });
      if (!downloadResponse || downloadResponse.code) {
        alertError("错误", downloadResponse?.message || '获取当前学生端下载并发次数失败');
        setLoading(false);
        return;
      }

      setChangelog(result.data.changelog);
      setUpdates(result.data.updates);
      setCurrentUpgradeCount(parseInt(downloadResponse.data || '0', 10));
    }

    setLoading(false);
  };

  const handleUpdateFiles = async () => {
    setUpdateLoading(true);

    const result = await downloadUpdateFiles({ updates });
    if (!result || result.code) {
      alertError("错误", result.message);
      setUpdateLoading(false);
      return;
    }

    if (result.data) {
      await invoke('upgrade');
    }

    checkUpdate();
    setUpdateLoading(false);
  };

  const handleResetDownloadCount = async () => {
    const downloadResponse = await updateSystemConfig({ key: UPGRAGE_COUNT, value: '0' });
    if (!downloadResponse || downloadResponse.code) {
      alertError("错误", downloadResponse?.message || '重置当前学生端下载并发次数失败');
      return;
    }

    checkUpdate();
    setCurrentUpgradeCount(0);
  };

  if (loading) {
    return <Spin />;
  }

  if (!networkStatus) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '60vh',
          fontSize: '26px',
          userSelect: 'text',
          margin: '20px',
        }}
      >
        <div>
          <div>当前设备已离线，您可以选择：</div>
          <div>1. 重新连接网络后再次点击检测更新</div>
          <div>2. 前往 https://hxr.iyopu.com/pclab2 下载最新版本安装包</div>
        </div>
      </div>
    );
  }

  if (!updates) {
    return (
      <div>
        <Result
          icon={<LoadingOutlined />}
          title="正在检测，请稍候"
        />
      </div>
    );
  }

  return (
    <>
      {updates && updates.length ? (
        <Result
          title="有新版本"
          subTitle={
            <pre
              style={{
                textAlign: 'left',
                wordBreak: 'break-all',
                marginTop: '30px',
                fontSize: '18px',
                width: '100%',
                whiteSpace: 'pre-wrap',
              }}
            >
              {changelog}
            </pre>
          }
          extra={
            <Button
              style={{ marginTop: '30px' }}
              type="primary"
              key="update"
              loading={updateLoading}
              icon={<CheckOutlined />}
              onClick={() => {
                Modal.confirm({
                  autoFocusButton: null,
                  title: '确认开始升级到最新版本？',
                  content: (
                    <ol>
                      <li>
                        建议在课后而非课前升级，升级后请及时验证可用性，出现问题烦请通过企业微信和技术支持联系。
                      </li>
                      <li>
                        升级前请注意关闭杀毒软件和"Windows 安全中心"应用中"Microsoft Defender防病毒"，以防更新失败。
                      </li>
                      <li>
                        因为学生端升级会在启动学生端后自动进行，所以系统还原场景下也可实现更新，但是会减慢学生机开启速度。
                      </li>
                      <li>
                        学生端下载并发次数数值较高会造成部分学生机无法宛城升级，可点击“重置下载并发数”按钮重置后再试。
                      </li>
                    </ol>
                  ),
                  okText: '开始升级',
                  cancelText: '取消',
                  onOk: handleUpdateFiles,
                });
              }}
              size="large"
            >
              更新
            </Button>
          }
        />
      ) : (
        <Result
          status="success"
          title="氦星人日常教学系统已是最新版本!"
        />
      )}

      <div
        style={{
          textAlign: 'center',
          marginTop: '30px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
        }}
      >
        <div>学生端升级下载并发计数器：{currentUpgradeCount || 0}</div>
        <Button onClick={handleResetDownloadCount} style={{ marginLeft: '20px' }}>
          重置
        </Button>
      </div>
    </>
  );
};
