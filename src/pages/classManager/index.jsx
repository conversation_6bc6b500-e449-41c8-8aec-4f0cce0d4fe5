import React, {useEffect, useState} from 'react';
import {checkNetworkStatus} from "../../utils/utils";
import { bindLogin, getLoginConfig } from '../../services/login';
import { alertError, alertSuccess } from '../../utils/edittools';
import {changBulkStudentsPassword, checkRepeatStu, deleteClass, getClassList, getRemoteClassList, getRemoteUserList, getUserList, syncClassAndStudents} from "../../services/team";
import moment from "moment";
import JsSHA from "jssha";
import {Button, Col, Modal, Progress, Row, Input, Select, Spin, Table} from "antd";
import {ArrowLeftOutlined, CheckOutlined, CloudDownloadOutlined} from "@ant-design/icons";
import styles from './index.css';
import {getCurrentWebviewWindow, WebviewWindow} from "@tauri-apps/api/webviewWindow";

const { Option } = Select;

const currentStudyYear = () => {
  const month = moment().format('MM');
  if (parseInt(month) <= 7) {
    return `${moment().subtract(1, 'years').format('YYYY')}学年`;
  }

  return `${moment().format('YYYY')}学年`;
};

export default function ClassManagerPage() {
  const sessionResponse = JSON.parse(localStorage.sessionResponse)
  const [networkStatus, setNetworkStatus] = useState(false);
  const [loading, setLoading] = useState(true);
  const [ifExport, setIfExport] = useState(false);
  const [postClassLoading , setPostClassLoading] = useState(false);
  const [classList , setClassList ] = useState([]);
  const [classID , setClassID ] = useState('');
  const [currentProgress , setCurrentProgress] = useState(0);
  const [currentProgressMessage  , setCurrentProgressMessage] = useState('');
  const [selectedYear, setSelectedYear] = useState(currentStudyYear());
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedStudents , setSelectedStudents] = useState([]);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [changePassWord, setChangePassWord] = useState('');
  const [changePassWordSub, setChangePassWordSub] = useState(null);
  const [repeatStuModalOpen, setRepeatStuModalOpen] = useState(false);
  const [repeatStu, setRepeatStu] = useState([]);

  const nonOfficial = sessionResponse.site_url === 'demo';

  // 班级用户列表表格处理
  const columnsClass = [
    {
      title: '序号',
      key: 'index',
      width: '60px',
      render: (row, text, index) => index + 1
    },
    {
      key: '账号',
      title: '账号',
      dataIndex: 'username'
    },
    {
      key: '姓名',
      title: '姓名',
      dataIndex: 'name'
    },
    {
      key: '学籍号',
      title: '学籍号',
      width: 200,
      dataIndex: 'sen'
    },
  ];

  // 班级列表
  const classColumns = [
    {
      title: '序号',
      key: 'index',
      width: '60px',
      render: (row, text, index) => index + 1
    },
    {
      title: '班级名称',
      key: 'className',
      // dataIndex: 'name',
      // width: 120,
      render: (classRow) => {
        return (
          <div
            key={classRow.id}
            style={{ textAlign: 'center' }}
          >
            {classRow.name}
            {
              !nonOfficial && classRow.ifInRemote === false ? (
                <span style={{ color: 'red' }}>（被删除）</span>
              ) : null
            }
          </div>
        );
      },
    },
    {
      title: '学年',
      key: 'year',
      // width: 80,
      // dataIndex: 'year',
      render: (row) => row.year ? row.year : selectedYear
    },
    {
      title: '班级人数',
      key: 'count',
      // width: 100,
      render: (row) => row.count ? row.count : (row.user_count ? row.user_count : 0)
    },
  ];

  if(!ifExport && sessionResponse.site_url !== 'demo') {
    classColumns.push({
      title: '操作',
      key: 'action',
      render: (row) => {
        return <a onClick={() => {
          Modal.confirm({
            autoFocusButton: null,
            title: `是否删除${row.name}？`,
            onOk: () => {
              delClass(row);
            }
          })
        }}>删除</a>
      }
    })
  }

  const rowSelection = {
    type: 'Checkbox',
    selectedRowKeys,
    onChange: (e) => {
      setSelectedRowKeys(e)
    },
  };

  useEffect(() => {
    // 调用异步函数
    initData();
  }, []);

  const initData = async (ifExport = false, schoolYear = currentStudyYear(), classID = null) => {
    setLoading(true);
    setPostClassLoading(true);
    try {
      // 检查网络连接状态
      const networkResult = await checkNetworkStatus();
      setNetworkStatus(networkResult);

      let result = {};

      if (networkResult && ifExport) {
        // 获取登录配置
        const loginConfig = await getLoginConfig();
        if (!loginConfig || loginConfig.code) {
          alertError("错误", loginConfig.message)
          return;
        }

        // 先登录远程
        const bindResult = await bindLogin({
          username: loginConfig.data.default_username,
          password: loginConfig.data.default_password,
          site_url: loginConfig.data.site_url,
          lab: loginConfig.data.lab,
          mac: localStorage.mac,
        })
        if(!bindResult || bindResult.code) {
          alertError("错误", bindResult && bindResult.message ? bindResult.message : '接口错误')
          return;
        }
        // 获取班级列表
        result = await getRemoteClassList({ schoolYear });

        if (result.data && result.data.length && result.data && result.data.length) {
          // 循环每一个班级，标记在远程是否有该班级
          for (const classItem of result.data) {
            const classID = classItem.id;
            const classIDInRemote = result.data.find((item) => item.id === classID);
            if (classIDInRemote) {
              classItem.ifInRemote = true;
            } else {
              classItem.ifInRemote = false;
            }
          }
        }
      } else {
        result = await getClassList({ schoolYear });
      }

      let tempClassID = classID ? classID
        : result.data && result.data.length
          ? result.data[0].id
          : null;
      const name =
        result.data && result.data.length
          ? result.data.find((data) => data.id === tempClassID)
          : null;
      const classNameData =  name ? name.name : null;

      let userListRes = {};

      if (tempClassID) {
        if (ifExport) {
          userListRes = await getRemoteUserList({ classID: tempClassID });
        } else {
          userListRes = await getUserList({ classID: tempClassID });
        }
        if (!userListRes || userListRes.code !== 0) {
          setPostClassLoading(false);
          setLoading(false);
          return;
        }
      } else {
        userListRes = { data: [] }
      }

      if (!networkResult) {
        setIfExport(false);
      }

      setClassList({
        classList: result.data,
        selectedYear: schoolYear ? schoolYear : currentStudyYear(),
        classNameData,
        tempClassID,
        userList: userListRes.data ? userListRes.data : [],
        userTotal: userListRes.data ? userListRes.data.total : 0,
      });
      setClassID(tempClassID);
      setPostClassLoading(false);
      setLoading(false);
      setNetworkStatus(networkResult);
    } catch (error) {
      console.error('获取信息时发生错误:', error);
    }
  };

  // 同步选中班级
  const sync = async () => {
    setPostClassLoading(true);
    if (!selectedRowKeys || !selectedRowKeys.length) {
      alertError('请在左侧列表中选择要同步的班级');
      setPostClassLoading(false);
      return;
    }

    // 校验重复学生
    const res = await checkRepeatStu({selectClasses: selectedRowKeys});

    if (res.data.length > 0) {
      setPostClassLoading(false);
      setRepeatStuModalOpen(true);
      setRepeatStu(res.data);
      return;
    }

    setCurrentProgress(0.01);

    // const { classPageSize, classPageNum } = classList;

    let index = 0;
    for(const selectClass of selectedRowKeys) {
      index += 1;
      const response = await syncClassAndStudents({
        selectClasses: [selectClass],
      });
      if (response.code !== 0) {
        setPostClassLoading(false);
        setCurrentProgressMessage(response.message);
        return;
      }
      setCurrentProgress(parseInt(index / selectedRowKeys.length * 10000 + 0.5, 10) / 100);
    }

    setCurrentProgress(100);
    setCurrentProgressMessage('同步成功');
    setPostClassLoading(false);
  }

  // 删除本地班级
  const delClass = async (data) => {
    const result = await deleteClass({ id: data.id });
    if (result.code !== 0) {
      return alertError(`错误！${result.message}`);
    }
    initData(ifExport, selectedYear)
  }

  // 管理平台
  const gotoManagement = async () => {
    const loginStatusResult = await getLoginConfig();
    if (!loginStatusResult || loginStatusResult.code) {
      return alertError(`错误！${loginStatusResult.message}`);
    }
    const netWorkInfo = await checkNetworkStatus();
    if (!netWorkInfo) {
      alertError('请检查您的网络环境')
      return;
    }
    // 调取code
    const result = await bindLogin({
      username: loginStatusResult.data.default_username,
      password: loginStatusResult.data.default_password,
      site_url: loginStatusResult.data.site_url,
      lab: loginStatusResult.data.lab,
      mac: localStorage.mac,
    })
    if(!result || result.code) {
      alertError("错误", result && result.message ? result.message : '请检查您的网络环境!')
      return;
    }
    const urlStr = `${loginStatusResult.data.site_url}/admin#/trainAnalyzeLogin?code=${result.data.loginCode}&toUrl=team`
    const mainWindow = getCurrentWebviewWindow();
    const webview = new WebviewWindow('management', {
      url: urlStr,
      title: "氦星人日常教学系统",
      resizable: true,
      width: 1280,
      height: 720,
      maximized: true,
      parent: mainWindow,
    });

    webview.once('tauri://close-requested', function (e) {});
  }

  // 重置密码
  const resetPassword = async () => {
    // 密码加密
    const shaObj = new JsSHA('SHA-1', 'TEXT');
    shaObj.update(changePassWord);
    const encryptPassword = shaObj.getHash('HEX');

    const result = await changBulkStudentsPassword({
      ids: selectedStudents,
      password: encryptPassword
    });
    // console.log('result:',result)
    if (result.code !== 0) {
      return alertError("错误", result.message);
    }

    alertSuccess('修改完成');

    setPasswordVisible(false);
    setSelectedStudents([]);
  }

  const renderSelectYear = () => {
    const currentYear = moment().format('YYYY');
    const currentMonth = moment().format('YYYYMM');

    // const currentYear = 2025;
    // const currentMonth = 202507;

    const year =
      currentMonth > `${moment().format('YYYY')}07`
        ? currentYear
        : currentYear - 1;
    let arr = [2020];
    if (parseInt(currentYear) > 2020) {
      for (let i = 2020; i <= year; i++) {
        if (!arr.includes(i)) {
          arr.push(i);
        }
      }
    }
    arr.sort((a, b) => b - a);
    return arr.map((i) => (
      <Option key={`${i}学年`} value={`${i}学年`}>
        {i}
        学年
      </Option>
    ));
  };

  const downLoadModal = (
    <Modal
      open={!!currentProgress}
      closable={false}
      footer={
        currentProgressMessage ? (<Button
          onClick={() => {
            setCurrentProgressMessage(null);
            setCurrentProgress(0);
          }}
        >关闭</Button>) : null
      }
    >
      <div className={styles.uploadMessage}>{currentProgressMessage ? currentProgressMessage : '正在同步，请稍候'}</div>
      <Progress percent={currentProgress} status="active" strokeColor={{ from: '#108ee9', to: '#87d068' }} />
    </Modal>
  )

  const repeatStuModal = (
    <Modal
      title="存在重复学生"
      open={repeatStuModalOpen}
      width={800}
      closable={false}
      footer={
        <Button
          onClick={() => {
            setRepeatStuModalOpen(false);
            setRepeatStu([]);
          }}
        >
          关闭
        </Button>
      }
    >
      <div style={{marginBottom: 8}}>以下列表中的学生学号与本地班级的学生号存在重复的数据，请先打开管理平台完成对学生或者学生班级的校验</div>
      <Table
        scroll={{y: 300}}
        rowKey={(record) => record.user.username}
        columns={[
          {
            title: '班级',
            dataIndex: 'team_name',
            key: 'team_name',
          },
          {
            title: '学年',
            dataIndex: 'school_year',
            key: 'school_year',
          },
          {
            title: '账号',
            key: 'username',
            render: ({user}) => {
              return <div>{user.username}</div>;
            },
          },
          {
            title: '姓名',
            key: 'name',
            render: ({user}) => {
              return <div>{user.name}</div>;
            },
          },
          {
            title: '学籍号',
            key: 'sen',
            width: 200,
            render: ({user}) => {
              return <div>{user.sen}</div>;
            },
          },
        ]}
        dataSource={repeatStu}
      />
    </Modal>
  );

  return (
    <div>
      <style>
        {`
            .userTable td {
              padding: 5px !important;
              text-align: center;
            }
            .userTable th {
              text-align: center !important;
            }
          `}
        </style>

        <div className={styles.top}>
          {
            sessionResponse.site_url === 'demo' ? (
              <Row>
                <Col span={3} className={styles.classTitle}>
                  班级信息
                </Col>
                <Col span={19}>
                  <div className={styles.topSubTitle}>
                    下列学生可登陆进行学习，学生若只使用过单点登录，则请使用123456作为密码登录。
                  </div>
                </Col>
              </Row>
            ) : (
              ifExport ? (
                <Row>
                  <Col span={3} className={styles.classTitle}>
                    云端班级
                  </Col>
                  <Col span={19}>
                    <div className={styles.topSubTitle}>
                      在此选择需要从管理平同步的班级，再点击右侧按钮同步所选班级。
                    </div>
                  </Col>
                </Row>
              ) : (
                <Row>
                  <Col span={3} className={styles.classTitle}>
                    本地班级
                  </Col>
                  <Col span={19}>
                    <div className={styles.topSubTitle}>
                      下列学生可登陆进行学习，学生若只使用过单点登录，则请使用123456作为密码登录。如需要更多班级，请点击同步班级按钮。
                    </div>
                  </Col>
                </Row>
              )
            )
          }
        </div>
        <div className={styles.classContent}>
          <Row gutter={30}>
            <Col span={12}>
              <Row>
                <Col span={8}>
                  <Select
                    defaultValue={'2024学年'}
                    className={styles.filterOption}
                    value={selectedYear || moment().format('YYYY学年')}
                    style={{width: '100%'}}
                    onChange={(value) => {
                      setSelectedYear(value);
                      initData(ifExport, value);
                    }}
                  >
                    {renderSelectYear()}
                  </Select>
                </Col>

                <Col span={8}>
                  {ifExport ? (
                    <Button
                      style={{width: 'calc(100% - 10px)', marginLeft: '10px'}}
                      onClick={() => {
                        setIfExport(false);
                        setSelectedRowKeys([]);
                        initData(false, selectedYear);
                      }}
                      loading={postClassLoading}
                    >
                      {postClassLoading ? null : (<ArrowLeftOutlined/>)}
                      返回本地班级
                    </Button>
                  ) : null}
                </Col>

                {ifExport ? (
                  <Col span={8}>
                    <Button
                      style={{width: 'calc(100% - 10px)', marginLeft: '10px'}}
                      type="primary"
                      onClick={() => {
                        sync();
                      }}
                      loading={postClassLoading}
                    >
                      {postClassLoading ? null : (<CheckOutlined/>)}
                      同步选中班级
                    </Button>
                  </Col>
                ) : (
                  <Col span={8}>
                    {
                      sessionResponse.site_url !== 'demo' ? (
                        <Button
                          style={{width: 'calc(100% - 10px)', marginLeft: '10px'}}
                          type="primary"
                          onClick={() => {
                            setIfExport(true);
                            initData(true, selectedYear);
                          }}
                          disabled={!networkStatus}
                          loading={postClassLoading}
                        >
                          {postClassLoading ? null : (<CloudDownloadOutlined/>)}
                          同步云端班级
                        </Button>
                      ) : null
                    }
                  </Col>
                )}
              </Row>

              <Table
                scroll={{
                  y: 444,
                }}
                className={`${styles.tableSty} userTable`}
                style={{
                  boxShadow: '0 0px 7px 6px rgb(165 165 165 / 10%)'
                }}
                dataSource={classList.classList}
                columns={classColumns}
                // pagination={paginationClass}
                // pagination={{
                //   showSizeChanger: true
                // }}
                pagination={false}
                showHeader
                rowKey={(record) => record.id}
                rowSelection={ifExport ? rowSelection : false}
                rowClassName={(row) => {
                  if (row.id === classID) {
                    // row.id === classList.classID
                    return `${styles.actionClass} userTable`;
                  }
                  return `${styles.normalClass} userTable`;
                }}
                onRow={(record) => {
                  return {
                    onClick: () => {
                      setClassID(record.id);
                      initData(ifExport, selectedYear, record.id)
                    },
                  };
                }}
              />
            </Col>
            <Col span={12}>
              <Row style={{height: '30px', lineHeight: '30px'}}>
                <Col span={19} className={styles.classNameData}>
                  {classList && classList.classNameData
                    ? classList.classNameData
                    : ''} 学生名单
                </Col>
                <Col span={5}>
                  {
                    !ifExport ? (
                      <Button
                        onClick={() => {
                          if (!selectedStudents || !selectedStudents.length) {
                            alertError('请选择需要重置密码的学生！');
                            return;
                          }
                          setPasswordVisible(true);
                          setChangePassWord(null);
                          setChangePassWordSub(null);
                        }}
                      >重置选定学生密码</Button>
                    ) : null
                  }

                  {
                    ifExport ? (
                      <Button
                        onClick={() => {
                          gotoManagement();
                        }}
                        // style={{width: '150px'}}
                        disabled={!networkStatus}
                      >打开管理平台</Button>
                    ) : null
                  }

                </Col>

              </Row>
              {loading ? (
                <Spin>正在加载</Spin>
              ) : (
                <div>
                  {classList && classList.classNameData ? (
                    <div>
                      <div>
                        <style>
                          {`
                            .userTable td {
                              padding: 5px !important;
                              text-align: center;
                            }
                            .userTable th {
                              text-align: center !important;
                            }
                          `}
                        </style>
                        {classList.userList && classList.userList.length ? (
                          <Table
                            scroll={{
                              y: 444,
                            }}
                            dataSource={
                              classList && classList.classNameData
                                ? classList.userList
                                : []
                            }
                            style={{
                              boxShadow: '0 0px 7px 6px rgb(165 165 165 / 10%)'
                            }}
                            className={`${styles.tableEnd} userTable`}
                            columns={columnsClass}
                            showHeader
                            rowSelection={ifExport ? false : {
                              type: 'Checkbox',
                              selectedRowKeys: selectedStudents,
                              onChange: (e) => {
                                setSelectedStudents(e);
                              },
                            }}
                            rowKey={(record) => record.id}
                            rowClassName={() => {
                              return `${styles.nextReplyBtn} userTable`;
                            }}
                            pagination={false}
                          />
                        ) : (
                          <div
                            style={{
                              textAlign: 'center',
                              marginTop: '20%',
                              fontSize: '20px',
                            }}
                          >
                            暂无人员~
                          </div>
                        )}
                      </div>
                    </div>
                  ) : null}
                </div>
              )}
            </Col>
          </Row>
        </div>
        {downLoadModal}
        {repeatStuModal}
        {/*<div className={styles.blank}></div>*/}
        <Modal
          open={passwordVisible}
          title="是否重置选定学生的密码？"
          onCancel={() => {
            setPasswordVisible(false);
          }}
          onOk={() => {
            if (!changePassWord || !changePassWordSub || changePassWord !== changePassWordSub) {
              alertError('请填写需要重置的密码并确认两次填写一致');
              return;
            }
            resetPassword();
          }}
        >
          <Row style={{lineHeight: '32px'}}>
            <Col span={6}>重置密码为：</Col>
            <Col span={18}>
              <Input.Password value={changePassWord} onChange={(e) => {
                const value = e && e.target ? e.target.value : e;
                setChangePassWord(value);
              }}/>
              {
                !changePassWord ? (
                  <div className={styles.warning}>请填写需要重置的密码</div>
                ) : (
                  <div></div>
                )
              }
            </Col>
          </Row>
          <Row style={{marginTop: '10px', lineHeight: '32px'}}>
            <Col span={6}>确认密码为：</Col>
            <Col span={18}>
              <Input.Password value={changePassWordSub} onChange={(e) => {
                const value = e && e.target ? e.target.value : e;
                setChangePassWordSub(value);
              }}/>
              {
                !changePassWordSub || changePassWordSub !== changePassWord ? (
                  <div className={styles.warning}>请确认需要重置的密码，并保证填写一致</div>
                ) : (
                  <div></div>
                )
              }
            </Col>
          </Row>
        </Modal>
      </div>
  );
}
