.content {
  width: 1000px;
  height: 640px;
  padding: 20px;
  background-color: white;
}

.title {
  width: 100%;
  text-align: center;
  height: 100px;
  line-height: 100px;
  font-size: 30px;
}

.body {
  margin-top: 50px;
}

.startUpWizardContent {
  /* width: calc(100vw - 40px); */
  padding: 20px;
  background-color: white;
}

.startUpWizardTitle {
  width: 100%;
  text-align: center;
  height: 80px;
  line-height: 80px;
  font-size: 30px;
}

.homeTitle {
  height: 8vh;
  line-height: 8vh;
  width: 100%;
  background-color: white;
  font-size: 25px;
  font-weight: bold;
  padding-left: 2.5%;
  display: flex;
}

.actionClass {
  background-color: #d2e9ff !important;
  /* line-height: 20px; */
  cursor: pointer;
}

.normalClass {
  /* line-height: 20px; */
  cursor: pointer;
}

.listClass {
  font-size: 1.2vw;
  font-weight: bold;
  padding-left: 10px;
  padding-top: 4px;
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: space-between;
}

.left {
  background: white;
}

.display {
  display: flex;
  justify-content: space-between;
}

.selectAll {
  margin-top: 25px;
  margin-left: 20px;
}

.extraContentSearch {
  width: 100%;
  height: 35px;
}

.tableSty {
  margin-top: 20px;
}

.linesRig {
  background: #e4e4e4;
  width: 96%;
  margin-left: 2%;
  height: 1px;
}

.tableEnd {
  width: 100%;
  margin-top: 20px;
}

.classNameData {
  font-size: 23px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.uploadBtn {
  text-align: center;
  margin-left: 5px;
  margin-right: 5px;
}

.top {
  height: 40px;
  line-height: 30px;
  padding-left: 20px;
  border-bottom: 1px solid #fafafa;
}

.classContent {
  margin-top: 10px;
  padding-left: 20px;
  padding-right: 20px;
}

.topSubTitle {
  color: #00000073;
}

.classTitle {
  font-size: 20px;
  font-weight: 600;
}

.blank {
  height: 40px;
  width: 100%;
}

.uploadMessage {
  font-weight: 600;
  margin: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 22px;
  word-wrap: break-word;
}

.warning {
  color: red;
  min-height: 32px;
  min-width: 1px;
}

