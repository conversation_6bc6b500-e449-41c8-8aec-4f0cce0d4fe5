.top {
    height: 40px;
    line-height: 30px;
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #fafafa;
}

.content {
    padding-left: 20px;
    padding-right: 20px;
}

.filterOption {
    width: calc(100% - 5px);
}

.tableTitleRow {
    height: 60px;
    line-height: 60px;
}

.title {
    font-size: 20px;
    font-weight: 600;
}

.trainPlanNameTitle {
    text-align: center;
    font-size: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.steps {
    margin-top: 15px;
}

.buttonText {
    text-align: center;
}

.fontbox {
    width: 100%;
    height: 50px;
    font-size: 45px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #1a4880;
    line-height: 26px;
    text-align: center;
    margin: 50px auto;
}

.titleStep {
    font-weight: 600;
    font-size: 24px;
}

.popover>div {
    transform: translateX(-19px);
}

.tableTrainSelect {
    width: 100%;
    text-align: center;
    height: 40px;
    font-size: 20px;
    line-height: 40px
}

.tableTrainSelect>span {
    margin-top: -10px;
}

.blank {
    height: 20px;
    width: 100%;
}

.checkInTitle {
    margin-top: -2px;
}
