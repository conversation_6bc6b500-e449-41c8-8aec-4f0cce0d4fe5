import React, { useEffect, useState } from 'react';
import styles from './index.css';
import { Button, Table, Row, Col, Spin, Modal, List, Switch, Select } from 'antd';
import { get_network_cards_info } from '@/utils/train';
import { get_server_url } from '@/utils/train';
import {
  changeNoPasswordLogin,
  changeTerminalIPBind,
  clearTerminal,
  deleteTerminal,
  getTerminalList,
  getTerminalOnlineCount,
  resetTerminalBatch,
  transferTerminal,
} from '@/services/terminal';
import { alertError, alertSuccess } from '@/utils/edittools';
import { wsListen } from '@/utils/requestServer';

export default function TerminalPage() {
  const [loading, setLoading] = useState(false);
  const [terminal, setTerminal] = useState({});
  const [terminalList, setTerminalList] = useState([]);
  const [onlineCount, setOnlineCount] = useState(0);
  const [port, setPort] = useState(':46048');
  const [getNetWorkInfo, setGetNetWorkInfo] = useState([]);
  const [deleteVisible, setDeleteVisible] = useState(false);
  const [resetTerminalVisible, setResetTerminalVisible] = useState(false);
  const [transferTerminalVisible, setTransferTerminalVisible] = useState(false);
  const [selectTerminalIPs, setSelectTerminalIPs] = useState([]);
  const [transferToIP, setTransferToIP] = useState(null);
  const [clear_user_record_by_ip, setClear_user_record_by_ip] = useState(null);
  const [enableTerminalIpBind, setEnableTerminalIpBind] = useState(null);
  const [enableNoPasswordLogin, setEnableNoPasswordLogin] = useState(null);

  const fetchData = async () => {
    try {
      const res = await getTerminalList();
      const { code, message, data } = res;
      if (code !== 0) {
        alertError("获取终端列表失败", message)
      }
      setTerminal(data);
      setTerminalList(data.terminalList);
      setClear_user_record_by_ip(data.clear_user_record_by_ip);
      setEnableTerminalIpBind(data.enableTerminalIpBind);
      setEnableNoPasswordLogin(data.enableNoPasswordLogin);
    } catch (error) {
      alertError('获取终端列表失败', error.message)
    }

    try {
      const res = await getTerminalOnlineCount();
      const { code, message, data } = res;
      if (code !== 0) {
        alertError("获取终端在线人数失败", message)
      }
      setOnlineCount(data);
    } catch (error) {
      alertError('获取终端在线人数失败', error.message)
    }
  };

  // 监听ws
  useEffect(() => {
    const { pathname } = location;
    if (pathname !== "/terminal") {
      return;
    }

    wsListen({
      onTerminalRefresh: async () => {
        // 刷新终端列表
        await fetchData();
      },
    });
  }, []);

  // 加载课程详情
  useEffect( () => {
    // 首次加载时获取终端列表
    fetchData();

    // 获取服务器端口
    get_server_url()
      .then((res) => {
        if (res) {
          const portMatch = res && res.match(/(:\d+)$/);
          if (portMatch && portMatch[1]) {
            setPort(portMatch[1]);
          }
        }
      });

    // 获取本机IP地址
    get_network_cards_info()
      .then((res) => {
        console.log(res)
        setGetNetWorkInfo(res);
      })
  }, []);

  if (loading) {
    return <Spin />;
  }

  const columns = [
    {
      title: '序号',
      key: 'index',
      align: 'center',
      render: (row, text, index) => index + 1
    },
    {
      title: '终端IP',
      dataIndex: 'ip',
      key: 'ip',
      align: 'center',
      sorter: {
        compare: (a, b) => a?.ip?.localeCompare(b?.ip)
      },
    },
    {
      title: '班级',
      dataIndex: 'teamName',
      key: 'teamName',
      align: 'center',
      sorter: {
        compare: (a, b) => a?.teamName?.localeCompare(b?.teamName)
      },
    },
    {
      title: '账号',
      dataIndex: 'userName',
      key: 'userName',
      align: 'center',
      sorter: {
        compare: (a, b) => a?.userName?.localeCompare(b?.userName)
      },
    },
    {
      title: '姓名',
      dataIndex: 'userNickname',
      key: 'userNickname',
      align: 'center',
      sorter: {
        compare: (a, b) => a?.userNickname?.localeCompare(b?.userNickname)
      },
    },
    {
      title: '登录状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      sorter: {
        compare: (a, b) => a?.status?.localeCompare(b?.status)
      },
      render: (text, record) => (
        <span>
          {record.status === '在线' ? '在线' : '离线'}
        </span>
      ),
    },
    // {
    //   title: '试卷名称',
    //   dataIndex: 'trainName',
    //   key: 'trainName',
    //   align: 'center',
    //   sorter: {
    //     compare: (a, b) => a?.trainName?.localeCompare(b?.trainName)
    //   },
    //   render: (text, record) => (
    //     <span>
    //       {record.trainName || '无'}
    //     </span>
    //   ),
    // },
  ];

  const selectTerminalList = () => {
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 3 }}
        size="small"
        bordered
        dataSource={selectTerminalIPs}
        renderItem={item => <List.Item>{item}</List.Item>}
      />
    )
  }

  return (
    <div style={{ height: '100%' }}>
      <Modal
        title="删除终端"
        open={deleteVisible}
        onOk={async () => {
          const response = await deleteTerminal({
            ips: selectTerminalIPs,
          });

          if (!response || response.code !== 0) {
            alertError("错误", response.message);
            return;
          }
          setDeleteVisible(false);
          await fetchData();
          alertSuccess('删除成功');
        }}
        onCancel={() => setDeleteVisible(false)}
      >
        <div>本功能用于删除不再使用的终端设备记录，如果设备下次启动还是会自动出现。确认删除以下终端设备？</div>
        {selectTerminalList()}
      </Modal>

      <Modal
        title="迁移终端"
        open={transferTerminalVisible}
        onOk={async () => {
          if(!transferToIP) {
            alertError('请选择目标终端设备');
            return;
          }

          const response = await transferTerminal({
            from_ip: selectTerminalIPs[0],
            to_ip: transferToIP,
          });

          if (!response || response.code !== 0) {
            alertError("错误", response.message);
            return;
          }
          await fetchData();
          alertSuccess("迁移成功", `学生可以在${transferToIP}上登录答题`)

        }}
        onCancel={() => setTransferTerminalVisible(false)}
      >
        <div style={{marginBottom: 10}}>通过迁移源头终端上的已暂存考试信息到目标终端，使得学生可在目标终端登录答题，确认要进行该操作么？</div>
        <div>源头终端：{selectTerminalIPs[0]}</div>
        <div>目标终端：
          <Select
            style={{ width: 200 }} placeholder="请选择可用终端"
            onChange={(value) => {
              setTransferToIP(value);
            }}
          >
            {
              terminalList
                .filter(row => (row.ip !== selectTerminalIPs[0]))
                .map(row => (<Select.Option key={row.ip} value={row.ip}>{row.ip}</Select.Option>))
            }
          </Select>
        </div>
      </Modal>

      <Modal
        title="初始化终端"
        open={resetTerminalVisible}
        onOk={async () => {
          dispatch({
            type: 'terminal/resetTerminal',
          })
          const response = await resetTerminalBatch({
            ips: selectTerminalIPs,
            clear_user_record_by_ip,
          });

          if (!response || response.code !== 0) {
            alertError("错误", response.message);
            return;
          }
          await fetchData();
        }}
        onCancel={() => setResetTerminalVisible(false)}
      >
        <div>确认初始化以下终端设备绑定的账号信息？清除后可允许该终端设备登录其他账号。</div>
        <div style={{ marginBottom: 10, marginTop: 10 }}>
          <span style={{ marginRight: '5px' }}>同时清除旧终端在该账号的提交记录</span>
            <Switch
              checkedChildren="开启"
              unCheckedChildren="关闭"
              checked={clear_user_record_by_ip}
              onChange={(checked) => {
                setClear_user_record_by_ip(checked)
              }}
            />

        </div>

        {selectTerminalList()}
      </Modal>

      <div className={styles.top}>
        <Row>
            <Col span={2} className={styles.title}>
              终端管理
            </Col>
            <Col span={2} style={{textAlign: 'right'}}>
              在线人数：
              {onlineCount} 人
            </Col>
            <Col span={20} style={{textAlign: 'right'}}>
              服务器地址：
              {
                getNetWorkInfo && getNetWorkInfo.length ? getNetWorkInfo.map(row => (<span key={row?.ip} style={{marginRight: 10}}>
                  {row?.ip}{port}
                </span>)) : null
              }
            </Col>
        </Row>
      </div>

      <div style={{ margin: '0 20px 10px 10px', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>

        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          {/*<Button*/}
          {/*  style={{ margin: '10px 0 10px 10px' }}*/}
          {/*  onClick={() => setResetTerminalVisible(true)}*/}
          {/*  disabled={!selectTerminalIPs.length > 0}*/}
          {/*>*/}
          {/*  初始化*/}
          {/*</Button>*/}

          {/*<Button*/}
          {/*  style={{ margin: '10px 0 10px 10px' }}*/}
          {/*  onClick={() => setTransferTerminalVisible(true)}*/}
          {/*  disabled={selectTerminalIPs.length !== 1}*/}
          {/*>*/}
          {/*  迁移*/}
          {/*</Button>*/}


          <Button
            style={{ margin: '10px 0 10px 10px' }}
            onClick={() => setDeleteVisible(true)}
            disabled={!selectTerminalIPs.length > 0}
          >
            删除
          </Button>

          <Button
            danger
            style={{ margin: '10px 0 10px 10px' }}
            onClick={() => {
              Modal.confirm({
                title: '清空终端',
                content: '本功能用于清空终端设备绑定的账号信息，清除后可允许终端设备登录其他账号。确认清空终端设备绑定的账号信息？',
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  const response = await clearTerminal();

                  if (!response || response.code !== 0) {
                    alertError("错误", response.message);
                    return;
                  }

                  await fetchData();
                  alertSuccess('清空成功');
                }
              });
            }}
          >
            清空
          </Button>
        </div>

        <div>
          <span style={{ marginRight: '10px' }}>终端设备绑定</span>
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={enableTerminalIpBind === "1"}
            onChange={async (checked) => {
              setEnableTerminalIpBind(checked ? "1" : "0")
              await changeTerminalIPBind({ enableTerminalIpBind: checked ? "1" : "0" })
            }}
          />

          <span style={{ margin: '0 10px 0 20px' }}>免密登录</span>
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={enableNoPasswordLogin === "1"}
            onChange={async (checked) => {
              setEnableNoPasswordLogin(checked ? "1" : "0")
              await changeNoPasswordLogin({ enableNoPasswordLogin: checked ? "1" : "0" })
            }}
          />

          {/*<span style={{ margin: '0 5px 0 20px' }}>学生端界面风格</span>*/}
          {/*<Switch*/}
          {/*  checkedChildren="现代版"*/}
          {/*  unCheckedChildren="经典版"*/}
          {/*  checked={enable_modern_style === "1"}*/}
          {/*  onChange={() => {*/}
          {/*    dispatch({*/}
          {/*      type: 'terminal/changeStudentStyle',*/}
          {/*      payload: { enable_modern_style: checked ? "1" : "0" },*/}
          {/*    })*/}
          {/*  }}*/}
          {/*/>*/}

          {/*<span style={{ margin: '0 5px 0 20px' }}>错题训练模式</span>*/}
          {/*<Switch*/}
          {/*  checkedChildren="开启"*/}
          {/*  unCheckedChildren="关闭"*/}
          {/*  checked={enable_correction_mode === "1"}*/}
          {/*  onChange={() => {*/}
          {/*    dispatch({*/}
          {/*      type: 'terminal/changeTrainMode',*/}
          {/*      payload: { enable_correction_mode: checked ? "1" : "0" },*/}
          {/*    })*/}
          {/*  }}*/}
          {/*/>*/}
        </div>

      </div>

      <div className={styles.content}>
        <Row>
          <Col span={24}>
            <Table
              scroll={{ y: '61vh' }}
              columns={columns}
              dataSource={terminalList}
              rowSelection={{
                selectTerminalIPs,
                onChange: (newSelectedRowKeys) => {
                  setSelectTerminalIPs(newSelectedRowKeys);
                  },
              }}
              pagination={false}
              rowKey={record => record.ip}
            />
          </Col>
        </Row>
      </div>
    </div>
  );
}
