import { createContext } from 'react';
import requestServer from '../utils/requestServer';

// 创建一个存储用户会话的上下文
export const SessionContext = createContext(null);

// 获取session
export async function getSession() {
    return requestServer(`/api/web/user/session`);
}

// 创建虚拟盘
export async function createVirtualDisk(courseSlug, userId) {
  return requestServer(`/api/web/course/prepareFS/${courseSlug}/${userId}`, {
    method: 'POST',
  });
}

// 打开虚拟盘
export async function openVirtualDisk(courseSlug) {
  return requestServer(`/api/web/course/openFS/${courseSlug}`, {
    method: 'POST',
  });
}

// 关闭虚拟盘
export async function closeVirtualDisk(userId) {
  return requestServer(`/api/web/course/closeVirtualDisk/${userId}`, {
    method: 'POST',
  });
}
