import requestServer from '../utils/requestServer';

export async function getTerminalList() {
    return requestServer(`/api/admin/terminal`);
}

export async function getTerminalOnlineCount() {
    return requestServer(`/api/admin/terminal/onlineCount`);
}

// 清除终端设备绑定用户信息
export async function resetTerminalBatch(params) {
    return requestServer(`/api/admin/teacher/terminal/reset/batch`, {
        method: 'POST',
        body: params,
    });
}

// 迁移终端
export async function transferTerminal(params) {
    return requestServer(`/api/admin/teacher/terminal/transfer`, {
        method: 'POST',
        body: params,
    });
}

// 删除终端设备记录
export async function deleteTerminal(params) {
    return requestServer(`/api/admin/teacher/terminal/delete`, {
        method: 'POST',
        body: params,
    });
}

// 清除终端设备绑定用户信息
export async function clearTerminal() {
    return requestServer(`/api/admin/teacher/terminal/clear`, {
        method: 'DELETE'
    });
}

// 修改绑定终端设备IP配置
export async function changeTerminalIPBind(params) {
    return requestServer(`/api/admin/teacher/config/terminal_ip_bind`, {
        method: 'PUT',
        body: params,
    });
}

export async function changeNoPasswordLogin(params) {
    return requestServer(`/api/admin/teacher/config/enable_no_password_login`, {
        method: 'PUT',
        body: params,
    });
}

export async function changeStudentStyle(params) {
    return requestServer(`/api/admin/teacher/config/enable_modern_style`, {
        method: 'PUT',
        body: params,
    });
}

export async function changeTrainMode(params) {
    return requestServer(`/api/admin/teacher/config/enable_correction_mode`, {
        method: 'PUT',
        body: params,
    });
}

export async function getStudentAnswer(params) {
    return requestServer(`/api/admin/teacher/train/student_answer`, {
        method: 'POST',
        body: params,
    });
}


export async function getSystemConfig(params) {
    return requestServer(`/api/admin/teacher/get/config/${params.key}`, {
        method: 'GET',
    });
}

export async function updateSystemConfig(params) {
    return requestServer(`/api/admin/teacher/put/config/${params.key}/${params.value}`, {
        method: 'PUT',
        body: params,
    });
}
