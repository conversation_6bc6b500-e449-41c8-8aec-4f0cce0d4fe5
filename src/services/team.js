import requestServer from '../utils/requestServer';

// 获取班级列表(远程)
export async function getRemoteClassList(params) {
  return requestServer(`/api/admin/course/teacher/remote/school_year/${params.schoolYear}/all_teams`);
}

// 获取用户列表(远程)
export async function getRemoteUserList(params) {
  return requestServer(
    `/api/admin/course/teacher/remote/team/${params.classID}/users`,
  );
}

// 获取班级列表（内网）
export async function getClassList(params) {
  return requestServer(`/api/admin/course/teacher/school_year/${params.schoolYear}/teams`);
}

// 获取用户列表（内网）
export async function getUserList(params) {
  return requestServer(
    `/api/admin/course/teacher/team/${params.classID}/users`,
  );
}

// 校验重复学生
export async function checkRepeatStu(params) {
  return requestServer(`/api/admin/train/teacher/remote/team/sync/check?team_ids=${params.selectClasses.join(',')}`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

export async function syncClassAndStudents(params) {
  return requestServer(`/api/admin/course/teacher/remote/team/sync/users?team_ids=${params.selectClasses.join(',')}`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

export async function deleteClass(params) {
  return requestServer(`/api/admin/course/delete/teacher/team/${params.id}`, {
    method: 'POST',
  });
}

// 批量修改学生密码
export async function changBulkStudentsPassword(params) {
  return requestServer(`/api/admin/user/password/by_ids`, {
    method: 'PUT',
    body: {
      ...params,
    },
  });
}
