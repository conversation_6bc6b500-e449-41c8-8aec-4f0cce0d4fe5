import requestServer from '../utils/requestServer';

let sessionResponse = null;

export function clearSession() {
  sessionResponse = null;
}

export async function logout() {
  localStorage.sessionResponse = null;
  return requestServer('/api/admin/user/adminLogoff');
}

export async function checkTeacherVersion(version) {
  return requestServer(`/api/admin/train/remote/update/check/teacher_version/${version}`, {
    method: 'POST',
    // body: {
    //   ...params,
    // },
  });
}

export async function downloadUpdateFiles(params) {
  return requestServer(`/api/admin/train/remote/update/download`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

export async function getLoginConfig() {
  // console.log('window', window.location.pathname)
  return requestServer('/api/admin/section/teacher/login/config')
}
// 用户登录
export async function bindLogin(params) {
  sessionResponse = null;
  // console.log('bindLogin', params)

  // return requestServer('/api/faye/online', {
  return requestServer('/api/admin/course/teacher/remote/login', {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 用户登录
export async function login(params) {
  sessionResponse = null;

  const result = requestServer('/api/admin/section/teacher/login', {
    method: 'POST',
    body: params,
  });
  // return requestServer('/api/faye/online', {
  return result
}

// 获取session
export async function getSession() {
  if (sessionResponse) {
    return sessionResponse;
  }

  sessionResponse = requestServer('/api/user/session');

  return sessionResponse;
}

export async function sendStudentMessage(params) {
  return requestServer(`/api/admin/train/teacher/message`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 初始化ipython
export function initIpython(config_name) {
  return requestServer(`/api/web/course/block/ipython/init/${config_name}`, {
    method: 'POST',
  });
}

// 检查ipython
export function checkIpython(config_name) {
  return requestServer(`/api/web/course/block/ipython/check/${config_name}`, {
    method: 'GET',
  });
}

// 关闭ipython
export function stopIpython(config_name) {
  return requestServer(`/api/web/course/block/ipython/stop/${config_name}`, {
    method: 'POST',
  });
}
