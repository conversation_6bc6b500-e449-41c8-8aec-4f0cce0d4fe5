import requestServer from '../utils/requestServer';

export async function getCourseList() {
    return requestServer('/api/admin/course/all');
}

export async function getCourseDetail(params) {
    return requestServer(`/api/web/course/${params}/indices`)
}

export async function getSectionDetail(params) {
    return requestServer(`/api/web/course/${params.courseSlug}/directory/${params.chapterName}/section/${params.sectionName}`)
}

// 删除课程
export async function deleteCourses(params) {
    return requestServer('/api/admin/course/batchDelete', {
        method: 'POST',
        body: params,
    })
}

// 开始上课
export async function startClass(params) {
    return requestServer('/api/admin/course/start', {
        method: 'POST',
        body: params,
    })
}

// 结束上课
export async function finishClass(params) {
    return requestServer('/api/admin/teacher/course/plan/reset', {
        method: 'PUT',
        body: params,
    })
}

// 获取课程进度(章)
export async function getChapterProgress(params) {
    return requestServer(`/api/admin/result/chapter`, {
        method: 'POST',
        body: params,
    })
}

// 获取课程进度(节)
export async function getSectionProgress(params) {
    return requestServer(`/api/admin/result/section`, {
        method: 'POST',
        body: params,
    })
}

// 导出班级课程进度
export async function exportCourseProgressExcel(params) {
    return requestServer(`/api/admin/course/progress/exportCourseProgressExcel`, {
        method: 'POST',
        body: params,
    })
}

// 获取远程课程信息（用于对比）
// 后端已经在返回数据中包含 modify_status 字段（删除、无变化、新增、更新）
export async function getRemoteCourseInfo(courseSlug) {
    return requestServer(`/api/web/course/${courseSlug}/indices`)
}

// 更新课程内容（触发后端下载和更新）
// 后端会通过 ws://localhost:46046 推送下载进度
export async function updateCourseContent(courseSlug) {
    return requestServer(`/api/web/course/${courseSlug}`, {
        method: 'PUT'
    })
}

// // 获取课程同步状态（轮询方式，作为备用）
// export async function getCourseSyncStatus(taskId) {
//     return requestServer(`/api/admin/course/sync/status/${taskId}`)
// }