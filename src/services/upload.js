import requestServer, { fileFetch } from '../utils/requestServer';

export async function uploadOfflineFile(params) {
    return requestServer(`/api/admin/offline_sync/upload`, {
        method: 'POST',
        body: params,
    });
}

export async function uploadFile(form) {
    return fileFetch(`/api/admin/upload/train/file`, {
        method: 'POST',
        body: form,
    });
}

// 上传学校配置文件
export async function uploadSchoolConfig(params) {
    return requestServer(`/api/admin/upload/school_config`, {
        method: 'POST',
        body: params,
    });
}

// 上传班级和学生
export async function uploadClassAndStudents(params) {
    return requestServer(`/api/admin/upload/class_and_students`, {
        method: 'POST',
        body: params,
    });
}

// 上传用户
export async function uploadUsers(params) {
    return requestServer(`/api/admin/upload/users`, {
        method: 'POST',
        body: params,
    });
}

// 上传试卷
export async function uploadTrain(params) {
    return requestServer(`/api/admin/upload/train`, {
        method: 'POST',
        body: params,
    });
}

// 上传试卷系列
export async function uploadTrainSeries(params) {
    return requestServer(`/api/admin/upload/train_series`, {
        method: 'POST',
        body: params,
    });
}
