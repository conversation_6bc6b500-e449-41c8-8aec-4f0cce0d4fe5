import {history, useModel} from '@umijs/max';
import {Avatar, Button, Dropdown, Modal, notification} from 'antd';
import {BorderOutlined, CloseOutlined, LineOutlined, LoginOutlined, UserOutlined} from '@ant-design/icons';
import {getCurrentWebviewWindow, WebviewWindow} from '@tauri-apps/api/webviewWindow';
import LeftOutlined from '@ant-design/icons/LeftOutlined';
import {invoke} from "@tauri-apps/api/core";
import {stopIpython} from '@/services/login';
import {closeVirtualDisk} from '@/services/user';
import globalVars from '@/utils/global';
import React from 'react';
import {newWSConnect} from '@/utils/requestServer';
import './global.css';

// 运行时配置
notification.config({
  placement: 'bottomRight',
  duration: 3,
  maxCount: 2
});

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState() {
  // 初始化全局WebSocket监听
  if (window.__TAURI__) {
    newWSConnect();
  }
  return {};
}

const displayMenuList = [
  '/login',
  '/offlineSync',
  '/startUpWizard'
]

export const layout = () => {
  const { navName } = useModel('global');

  return {
    title: '机房教学系统',
    logo: require('@/assets/logo.png'),
    menu: {
      locale: false,
    },
    layout: 'top',
    contentStyle: {
      backgroundColor: 'white',
    },
    fixSiderbar: true,

    headerRender: (props, defaultDom) => {
      if (displayMenuList.includes(props.location.pathname)) {
        return (
          <style>
            {
              `
                .ant-pro-layout .ant-layout-header {
                    display: none;
                }
                .ant-pro-layout .ant-pro-layout-content {
                    padding-block: 0;
                    padding-inline: 0;
                }
              `
            }
          </style>
        )
      }

      const dropdownClick = ({ key }) => {

        if (key === '1') {
          history.push('/classManager');
        }

        if (key === '2') {
          Modal.confirm({
            title: '注销',
            content: '你确定要注销账号吗？',
            onOk: async () => {
              try {
                await localStorage.removeItem('sessionResponse')
                await stopIpython(globalVars.winVersion);
                await closeVirtualDisk(globalVars.userInfo.user_id);
                if (window.__TAURI__) {
                  const webview = new WebviewWindow('login', {
                    url: '/login',
                    title: "教师端登录",
                    fullscreen: false,
                    decorations: false,
                    center: true,
                    focus: true,
                    width: 730,
                    height: 480,
                    shadow: true,
                    alwaysOnTop: true
                  });

                  await webview.once('tauri://created', function () {
                    getCurrentWebviewWindow().close('main')
                  });
                }
              } catch (error) {
                console.error('Failed to close the application:', error);
              }
            },
          });
        }

        if (key === '3') {
          Modal.confirm({
            title: '退出',
            content: '你确定要退出应用吗？',
            onOk: async () => {
              try {
                await localStorage.removeItem('sessionResponse')
                await stopIpython(globalVars.winVersion);
                await closeVirtualDisk(globalVars.userInfo.user_id);
                await invoke('stop_server');
                await getCurrentWebviewWindow().close();
              } catch (error) {
                console.error('Failed to close the application:', error);
              }
            },
          });
        }
      }

      const curMenu = props.menuData?.filter(item => !item.hideInMenu)
      const showNavName = props.menuData?.filter(item => item.key === props.matchMenuKeys[0])[0].showNavName;
      const showBack = props.menuData?.filter(item => item.key === props.matchMenuKeys[0])[0].showBack;

      const goBack = (key, pathname) => {
        // 判断页面是否包含 iframe
        const iframe = document.querySelector('iframe');

        // 判断当前页面是否包含动态参数
        const pathParts = pathname.split('/');

        if (iframe) {
          history.replace(`/course/courseDetail/${pathname.split('/')[3]}`);
        } else if (pathParts.length === 4 && pathParts[1] === 'course' && pathParts[2] === 'courseDetail') {
          history.replace(`/course/courseList`);
        } else {
          history.go(-1);
        }

        // const curId = props.menuData?.filter(item => item.key === key[0])[0]?.id;
        // if (curId) {
        //   const curIndex = parseInt(curId) - 2;
        //   const toPath = props.menuData[curIndex - 1];
        //
        //   if (toPath && toPath.path) {
        //     if (toPath.path.includes(':')) {
        //
        //       const currentSegments = pathname.split('/').filter(segment => segment);
        //       const keySegments = key[0].split('/').slice(1);
        //
        //       let resolvedPath = toPath.path;
        //       const dynamicSegments = toPath.path.match(/:(\w+)/g);
        //
        //       dynamicSegments?.forEach((segment) => {
        //         const segIndex = keySegments.findIndex(v => v === segment);
        //         resolvedPath = resolvedPath.replace(segment, currentSegments[segIndex]);
        //       });
        //
        //       history.push(resolvedPath);
        //     } else {
        //       history.push(`${toPath.path}`);
        //     }
        //   }
        // }
      };


      return (
        <div
          data-tauri-drag-region
          style={{
            backgroundColor: '#294879',
            padding: '0 24px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
          // onMouseDown={(e) => {
          //   // 调用tauri的窗口拖拽功能开启后无法点击按钮
          //   // 如果点击的不是按钮区域，则开启拖拽
          //   if (!e.target.closest('button') && !e.target.closest('dropdown') && !e.target.closest('ul')) {
          //     getCurrentWebviewWindow().startDragging();
          //   }
          // }}
          onDoubleClick={async (e) => {
            // 如果双击的是按钮、下拉菜单或其他交互元素，则不触发全屏
            if (e.target.closest('button') || e.target.closest('.ant-dropdown') || e.target.closest('.ant-avatar') || e.target.closest('img')) {
              return;
            }
            if (window.__TAURI__) {
              try {
                const win = getCurrentWebviewWindow();
                const isMaximized = await win.isMaximized();
                if (isMaximized) {
                  // await win.setSize(new LogicalSize(1280, 720));
                  await win.unmaximize();
                } else {
                  await win.maximize()
                }
              } catch (error) {
                console.error('Failed to toggle fullscreen:', error);
              }
            }
          }}
        >
          {/* Logo */}
          <Button type="link" style={{ height: '54px', display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={()=> history.push('/course/courseList')}>
            <img style={{ height: '44px' }} src={props.logo} alt="logo" />
            <div style={{ fontWeight: 'bold', color: 'white', fontSize: '24px', marginLeft: '24px' }}>{props.title}</div>
          </Button>

          {
            showNavName ? (
              <div style={{ fontWeight: 'bold', color: 'white', fontSize: '24px' }}>{navName}</div>
            ) :
              (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ul style={{ listStyle: 'none', margin: 0, padding: 0, display: 'inline-flex', color: 'white' }}>
                  {curMenu.slice(1).map(item =>
                    <li key={item.path} style={{ margin: '0 5px' }} >
                      <Button
                        style={{
                          borderColor: props.matchMenuKeys.includes(item.key) ? '#3db2e2' : '#fff',
                          color: props.matchMenuKeys.includes(item.key) ? '#3db2e2' : '#000'
                        }}
                        onClick={() => {history.push(`${item.path}`) }}
                        className={item.name === '课程' ? 'tour-first' : item.name === '班级' ? 'tour-second' : item.name === '终端' ? 'tour-third' : ''}
                      >
                        {item.name}
                      </Button>
                    </li>,
                  )}
                </ul>
              </div>
            )
          }

          {/* Right Side Buttons */}
          <div style={{ display: 'flex', alignItems: 'center', width: '284px', justifyContent: 'flex-end' }}>
            {
              showBack ? (
                <Button
                  ghost
                  style={{ marginRight: '20px' }}
                  icon={<LeftOutlined />}
                  onClick={() => {goBack(props.matchMenuKeys, props.location.pathname)}}
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  返回
                </Button>
              ) : null
            }
            {/*<Button*/}
            {/*  style={{*/}
            {/*    marginRight: '10px',*/}
            {/*    borderColor: props.matchMenuKeys[0] === '/operate' ? '#3db2e2' : '#fff',*/}
            {/*    color: props.matchMenuKeys[0] === '/operate' ? '#3db2e2' : '#000'*/}
            {/*  }}*/}
            {/*  icon={<SettingOutlined />}*/}
            {/*  onClick={() => {history.push('/operate') }}*/}
            {/*  onMouseDown={(e) => e.stopPropagation()}*/}
            {/*>*/}
            {/*  操作*/}
            {/*</Button>*/}
            {/*<Button*/}
            {/*  style={{*/}
            {/*    marginRight: '10px',*/}
            {/*    borderColor: props.matchMenuKeys[0] === '/demonstrate' ? '#3db2e2' : '#fff',*/}
            {/*    color: props.matchMenuKeys[0] === '/demonstrate' ? '#3db2e2' : '#000'*/}
            {/*  }}*/}
            {/*  icon={<PlayCircleOutlined />}*/}
            {/*  onClick={() => {history.push('/demonstrate') }}*/}
            {/*  onMouseDown={(e) => e.stopPropagation()}*/}
            {/*>*/}
            {/*  演示*/}
            {/*</Button>*/}
            {/*<Button*/}
            {/*  icon={<LogoutOutlined />}*/}
            {/*  onClick={handleExit}*/}
            {/*  onMouseDown={(e) => e.stopPropagation()}*/}
            {/*>*/}
            {/*  退出*/}
            {/*</Button>*/}
            {/*/!* 头像和用户名 *!/*/}
            <Dropdown
              menu={{
                items: [
                  // {
                  //   key: '1',
                  //   label: '班级',
                  //   icon: <TeamOutlined />,
                  // },
                  {
                    key: '1',
                    label: globalVars.userInfo && globalVars.userInfo.display_name,
                    icon: <UserOutlined />,
                    disabled: true,
                  },
                  {
                    key: '2',
                    label: '注销',
                    icon: <LoginOutlined />,
                  },
                  // {
                  //   key: '3',
                  //   label: '退出',
                  //   icon: <LogoutOutlined />,
                  // }
                ],
                onClick: (e) => dropdownClick(e)
              }}
              trigger={['hover']}
              placement="bottomRight"
              onMouseDown={(e) => e.stopPropagation()}
            >
              <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', marginRight: '15px' }} onMouseDown={(e) => e.stopPropagation()}>
                <Avatar
                  style={{ marginRight: '10px' }}
                  icon={<UserOutlined />}
                  src={globalVars.userInfo && globalVars.userInfo.avata_path ? `https://statichxr.iyopu.com/school/csxx/${globalVars.userInfo.avata_path}` : require('@/assets/errorAvatar.png')}
                />
                {/*<span style={{ color: '#fff', fontWeight: 'bold' }}>{globalVars.userInfo && globalVars.userInfo.display_name}</span>*/}
              </div>
            </Dropdown>
            <div
              onClick={async () => {
                await getCurrentWebviewWindow().minimize()
              }}
              style={{
                color: '#fff',
                fontSize: '16px',
                paddingRight: '12px',
                paddingLeft: '12px',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              title="最小化"
            >
              <LineOutlined />
            </div>
            <div
              onClick={async () => {
                if (window.__TAURI__) {
                  try {
                    const win = getCurrentWebviewWindow();
                    const isMaximized = await win.isMaximized();
                    console.log(isMaximized);
                    if (isMaximized) {
                      // await win.setSize(new LogicalSize(1280, 720));
                      await win.unmaximize();
                    } else {
                      await win.maximize()
                    }
                  } catch (error) {
                    console.error('Failed to toggle fullscreen:', error);
                  }
                }
              }}
              style={{
                color: '#fff',
                fontSize: '16px',
                paddingRight: '12px',
                paddingLeft: '12px',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              title="最大化/还原"
            >
              <BorderOutlined />
            </div>
            <div
              onClick={async () => {
                Modal.confirm({
                  title: '退出',
                  content: '你确定要退出应用吗？',
                  onOk: async () => {
                    try {
                      await localStorage.removeItem('sessionResponse')
                      await stopIpython(globalVars.winVersion);
                      await closeVirtualDisk(globalVars.userInfo.user_id);
                      await invoke('stop_server');
                      await getCurrentWebviewWindow().close();
                    } catch (error) {
                      console.error('Failed to close the application:', error);
                    }
                  },
                });
              }}
              style={{
                color: '#fff',
                fontSize: '16px',
                paddingRight: '12px',
                paddingLeft: '12px',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(231, 76, 60, 0.8)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              title="关闭"
            >
              <CloseOutlined />
            </div>
          </div>
        </div>
      );
    },
  };
};
