import React from 'react';
import Joyride from 'react-joyride';

const ReactJoyride = (props) => {
  const { run = true, onSetStatus } = props;
  const steps = [
    {
      target: '.tour-first',
      content: '在这里教师可以下载课程！',
      disableBeacon: true,
      hideCloseButton: true,
    },
    {
      target: '.tour-second',
      content: '在这里教师可以同步班级！',
      disableBeacon: true,
      hideCloseButton: true,
    },
    {
      target: '.tour-third',
      content: '在这里教师可以管理终端！',
      disableBeacon: true,
      hideCloseButton: true,
    },
  ];

  const handleJoyrideCallback = (data) => {
    const { action } = data;
    if (action === 'reset') {
      onSetStatus(false);
      localStorage.setItem('showJoyride', '0')
    }
  };

  return (
    <Joyride
      steps={steps}
      scrollOffset={500}
      scrollDuration={100}
      scrollToFirstStep
      continuous
      locale={{ back: '返回', close: '关闭', last: '结束', next: '下一步', skip: '跳过' }}
      styles={{
        options: {
          primaryColor: '#1890ff',
          textColor: '#000',
        },
      }}
      showSkipButton
      disableOverlayClose
      run={run}
      callback={handleJoyrideCallback}
    />
  );
};

export default ReactJoyride;
