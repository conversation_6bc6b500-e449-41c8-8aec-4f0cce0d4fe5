import { memo } from "react";

export default memo(function Card({ title, centerTitle, left, right, children, className = "", onClick }) {
    return (
        <div className={`w-full bg-white mb-0 lg:mb-4 rounded shadow-lg px-4 pb-4 ${className}`} onClick={onClick}>
            <div className="pt-0 md:pt-4 flex">
                {
                    left && (
                        <div className="text-sm text-gray-400">
                            {left}
                        </div>
                    )
                }

                <h1 className={`flex-1 text-lg md:text-xl md:mb-2 ${centerTitle ? 'text-center': ''}`}>{title}</h1>

                {
                    right && (
                        <div className="text-sm text-gray-400">
                            {right}
                        </div>
                    )
                }
            </div>
            {children}
        </div>
    )
});
