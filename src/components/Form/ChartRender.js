import React, { Component } from 'react';
import LineC<PERSON>AndList from './LineChartAndList';
import Bar<PERSON>hartAndList from './BarChartAndList';
import BarChart from './BarChart';

export default class ChartRender extends Component {
  constructor(props) {
    super(props);
    this.state = {
      allDatas: props.allDatas ? props.allDatas.concat([]) : [],
    };
  }

  // 处理数据更新
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { allDatas } = this.state;
    let updateCounter = 0;
    const nextState = {};

    if (allDatas !== nextProps.allDatas) {
      updateCounter += 1;
      nextState.allDatas = nextProps.allDatas.concat([]);
    }

    if (!updateCounter) {
      return;
    }

    this.setState(nextState);
  }

  render() {
    const { allDatas } = this.state;
    const contentRender = (result) => {
      // console.log('result:', result);
      return (
        <div key={`${result[0].chartTitle}_group`} style={{ width: '100%' }}>
          {
            result.map((row, index) => (
              <div key={index} style={row.style ? row.style : { width: '100%', float: 'left', paddingRight: '20px', marginBottom: '20px', minHeight: '500px', position: 'relative' }}>
                {
                  row.type === 'lineChartAndList' ? (
                    <LineChartAndList
                      tableData={row.tableData}
                      chartTitle={row.chartTitle}
                      listTitle={row.listTitle}
                      listSubTitle={row.listSubTitle}
                    />
                  ) : null
                }
                {
                  row.type === 'barChartAndList' ? (
                    <BarChartAndList
                      tableData={row.tableData}
                      chartTitle={row.chartTitle}
                      listTitle={row.listTitle}
                      unit={row.unit}
                      listSubTitle={row.listSubTitle}
                    />
                  ) : null
                }
                {
                  row.type === 'barChart' ? (
                    <BarChart
                      tableData={row.tableData}
                      chartTitle={row.chartTitle}
                      max={row.max}
                      left={row.left}
                      sortFunction={row.sortFunction}
                    />
                  ) : null
                }
              </div>
            ),
            )
          }
        </div>
      );
    };

    // let result = [];
    // const results = [];
    // console.log('allDatas:',allDatas);
    // for (const row of allDatas) {
    //   result.push(row);
    //   if (result.length === 4) {
    //     results.push(contentRender(result));
    //     result = [];
    //   }
    // }
    // if (result.length) {
    //   results.push(contentRender(result));
    // }
    // return results;
    return contentRender(allDatas);
  }
}
