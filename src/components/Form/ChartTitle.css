.tip {
  width: 11px !important;
  height: 11px !important;
  box-sizing: border-box !important;
  border-radius: 50% !important;
  display: inline-block !important;
  margin-right: 10px;
  -webkit-transform-origin-x: 0 !important;
  -webkit-transform: scale(0.90) !important;
}

.title{
  padding: 0px 10px 20px 10px; 
  margin: 10px; 
  font-size: 19px; 
  letter-spacing: 3px;
  color: #026ed3;
  font-weight: 600;
}

.title1{
  padding: 0px 0px 20px 10px; 
  font-size: 19px; 
  letter-spacing: 3px;
  color: #026ed3;
  font-weight: 600;
}

.titleCenter{
  text-align: center;
  font-size: 18px; 
  letter-spacing: 3px;
  color: #026ed3;
  font-weight: 600;
  height: 50px;
  line-height: 50px;
}

.areaTitle {
  font-size: 15px;
  font-weight: bold;
  padding: 6px 15px;
  color: black;
}

.excleButton {
  height: 40px;
  margin: 5px;
  float: right;
}

.level{
  text-align : center;
  font-size: 0.6vw; 
  letter-spacing: 1px;
}

.number{
  margin-left: 5px; 
  height: 35px;
  width: 35px;
  text-align: center; 
  background: #055280; 
  color: #d7c82a; 
  font-size: 22px;
}

.line{
  margin-left: 48%; 
  height: 85px; 
  width: 0px; 
  border: 0.5px solid lightGrey;
}

/* .thTitle {
  position: relative;
}

.thTitle:after {
    content: "";
    position: absolute;
    width: 1px;
    height: 120%;
    top: 0;
    left: 0;
    background-color: lightgray;
    display: block;
    transform: rotate(-30deg);
    transform-origin: top;
    -ms-transform: rotate(-50deg);
    -ms-transform-origin: top;
}*/

/* .Tabletitle1{
  float: right;
  margin-top: 12px;
}

.Tabletitle2{
  float: left;
}  */

.th1{
  border: 1px solid #e8e9e8; 
  font-weight: bold;
  color: rgba(0,0,0,.85);
  font-size: 15px;
  position: relative;
  padding-left: 2PX;
  padding-right: 2PX;
}

/* .th2{
  border: 1px solid #e8e9e8; 
  font-weight: 500; 
  color: rgba(0,0,0,.85);
  font-size: 15px;
  text-align: center; 
  background: url('../../assets/img/th2.png') no-repeat 25% 45%;
  background-size: 110% 120%;
  padding-left: 2PX;
  padding-right: 2PX;
} */

.th3{
  border: 1px solid #e8e9e8; 
  color: rgba(0,0,0,.85);
  font-size: 15px;
  font-weight: bold;
  text-align: center;
  padding-left: 2PX;
  padding-right: 2PX;
}

.th4{
  border: 1px solid #e8e9e8; 
  color: rgba(0, 0, 0, 0.55);
  font-size: 11px;
  text-align: center;
  padding-left: 2PX;
  padding-right: 2PX;
}

.canvasDiv {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
}

.canvasLine {
  width: 100%;
  height: 100%;
  position: absolute;
}
  
.canvasTextTop {
  top: 2PX;
  right: 2PX;
  position: absolute;
  max-width: 50%;
  text-align: right;
}
  
.canvasTextBottom {
  bottom: 2PX;
  left: 2PX;
  position: absolute;
  max-width: 50%;
  text-align: left;
}

.excleButton {
  height: 40px;
  margin: 5px;
  float: right;
}
