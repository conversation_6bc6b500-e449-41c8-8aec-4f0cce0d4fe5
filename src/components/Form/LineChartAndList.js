/* eslint-disable */
import React, { Component } from 'react';
import ReactECharts from '../ReactECharts/ECharts.js';
import { Row, Col, Tooltip } from 'antd';
// import $ from 'jquery';

const colorArr = ['#da0d68', '#975e6d', '#e0719c', '#f99e1c', '#ef5a78', '#f7f1bd', '#da1d23', '#dd4c51', '#3e0317', '#e62969', '#6569b0', '#ef2d36', '#c94a44', '#b53b54', '#a5446f', '#dd4c51', '#f2684b', '#e73451', '#e65656', '#f89a1c', '#aeb92c', '#4eb849', '#f68a5c', '#baa635', '#f7a128', '#f26355', '#e2631e', '#fde404', '#7eb138', '#ebb40f', '#e1c315', '#9ea718', '#94a76f', '#d0b24f', '#8eb646', '#faef07', '#c1ba07', '#b09733', '#8f1c53', '#b34039', '#ba9232', '#8b6439', '#187a2f', '#a2b029', '#718933', '#3aa255', '#a2bb2b', '#62aa3c', '#03a653', '#038549', '#28b44b', '#a3a830', '#7ac141', '#5e9a80', '#0aa3b5', '#9db2b7', '#8b8c90', '#beb276', '#fefef4', '#744e03', '#a3a36f', '#c9b583', '#978847', '#9d977f', '#cc7b6a', '#db646a', '#76c0cb', '#80a89d', '#def2fd', '#7a9bae', '#039fb8', '#5e777b', '#120c0c', '#c94930', '#caa465', '#dfbd7e', '#be8663', '#b9a449', '#899893', '#a1743b', '#894810', '#ddaf61', '#b7906f', '#eb9d5f', '#ad213e', '#794752', '#cc3d41', '#b14d57', '#c78936', '#8c292c', '#e5762e', '#a16c5a', '#a87b64', '#c78869', '#d4ad12', '#9d5433', '#c89f83', '#bb764c', '#692a19', '#470604', '#e65832', '#d45a59', '#310d0f', '#ae341f', '#d78823', '#da5c1f', '#f89a80', '#f37674', '#e75b68', '#d0545f'];

export default class LineChartAndList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tableData: props.tableData ? props.tableData.concat([]) : [],
      chartTitle: props.chartTitle,
      listTitle: props.listTitle,
      listSubTitle: props.listSubTitle,
      selected: null,
    };

    this.className = new Date().getTime();

    // this.show = this.show.bind(this);
  }

  // 处理数据更新
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { tableData, chartTitle, listTitle, listSubTitle } = this.state;
    let updateCounter = 0;
    const nextState = {};

    if (tableData !== nextProps.tableData) {
      updateCounter += 1;
      nextState.tableData = nextProps.tableData ? nextProps.tableData.concat([]) : [];
    }

    if (chartTitle !== nextProps.chartTitle) {
      updateCounter += 1;
      nextState.chartTitle = nextProps.chartTitle;
    }

    if (listTitle !== nextProps.listTitle) {
      updateCounter += 1;
      nextState.listTitle = nextProps.listTitle;
    }

    if (listSubTitle !== nextProps.listSubTitle) {
      updateCounter += 1;
      nextState.listSubTitle = nextProps.listSubTitle;
    }

    if (!updateCounter) {
      return;
    }

    this.setState(nextState);
  }

  render() {
    const { tableData, chartTitle, listTitle, listSubTitle, selected } = this.state; //, listTitle
    const chartOptions = {
      title: {
        text: chartTitle
      },
      tooltip : {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        // type: 'scroll',
        // data:[],
        // orient: 'vertical',
        // right: 10,
        // top: 0,
        show: false,
        selected: {}
      },
      // toolbox: {
      //   feature: {
      //     saveAsImage: {}
      //   }
      // },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis : [
        {
          type : 'category',
          boundaryGap : false,
          data : []
        }
      ],
      yAxis : [
        {
          type : 'value'
        }
      ],
      series : []
    };

    for (const row of tableData[0].data) {
      chartOptions.xAxis[0].data.push(row.key)
    }

    let allTotal = 0;
    let listData = [];

    let i = -1;

    for (const row of tableData) {
      i += parseInt(50 / tableData.length);
      // chartOptions.legend.data.push(row.name);
      let code = {
        name: row.name,
        type:'line',
        stack: row.name,
        lineStyle: {
          color: colorArr[i]
        },
        itemStyle: {
          color: colorArr[i]
        },
        data:[]
      }
      let total = 0;
      for (const rowData of row.data) {
        code.data.push(rowData.value);
        total += rowData.value;
      }

      allTotal += total;
      chartOptions.series.push(code);
      listData.push({
        label: code.name,
        value: total,
        color: colorArr[i],
      })
    }

    listData.sort((a, b) => b.value - a.value);

    if (!selected) {
      listData.forEach((row, index) => {
        listData[index].select = index < 3;
        chartOptions.legend.selected[row.label] = index < 3;
        return;
      });
    } else {
      listData.forEach((row, index) => {
        listData[index].select = selected[row.label];
        chartOptions.legend.selected[row.label] = selected[row.label];
        return;
      });
    }

    const changeSelect = (field) => {
      const newSelect = {
        ...chartOptions.legend.selected,
        [field]: !chartOptions.legend.selected[field]
      }

      this.setState({selected: newSelect})
    }

    return (
      <div
        style={{ background: '#fff', width: '100%', height: '100%', padding: '20px', color: 'rgba(0, 0, 0, 0.65)', position: 'absolute' }}
      >
        <Row style={{ height: '100%' }}>
          <Col span={16} style={{ height: '100%' }}>
            <ReactECharts
              option={chartOptions}
              notMerge={false}
              style={{ width: '100%', float: 'left', height: '100%', zIndex: '100' }}
            />
          </Col>
          <Col span={8} style={{ height: '100%', paddingTop: '20px' }}>
            <div style={{ width: '100%', height: '90%', float: 'left' }} >
              <div style={{ widht: '100%', height: '20%', border: '1px solid #ccc', padding: '5px' }}>
                <div style={{ width: '100%', textAlign: 'center', fontWeight: 'bold', fontSize: '17px', marginTop: '4px' }}>{listTitle}</div>
                <div style={{ width: '100%', textAlign: 'center', fontWeight: 'bolder', fontSize: '23px', color: 'red', marginTop: '4px' }}>{allTotal}元</div>
              </div>
              <div
                style={{
                  widht: '100%',
                  height: '85%',
                  borderLeft: '1px solid #ccc',
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  padding: '5px'
                }}
              >
                <div style={{ width: '100%', textAlign: 'center', fontWeight: 'bold', fontSize: '17px', marginTop: '4px' }}>{listSubTitle}</div>
                <table style={{ width: '100%', height: '85%', marginLeft: '5px' }} >
                  <tbody style={{ width: '100%' }} >
                    {
                      listData.map((row, index) =>
                        <tr
                          key={row.label}
                          style={{ cursor: 'pointer' }}
                          onClick={() => changeSelect(row.label)}
                        >
                          <td
                            style={{ width: '10%', fontSize: '17px', padding: 0, color: !row.select ? row.color : 'white' }}
                          >
                            <div
                              style={{
                                width: '20px',
                                height: '20px',
                                borderRadius: '50%',
                                background: row.select ? row.color : 'none',
                                textAlign: 'center',
                                fontSize: '13px',
                                lineHeight: '18px',
                                border: `1px solid ${row.color}`
                              }}
                            >{index + 1}</div>
                          </td>
                          <td style={{ width: '60%', fontSize: '14px', padding: 0 }}>
                            <Tooltip
                              // style={{
                              //   height: '100%',
                              //   LineHeight: '100%'
                              // }}
                              title={row.label}
                            >
                              <div
                                style={{
                                  display: '-webkit-box',
                                  WebkitBoxOrient: 'vertical',
                                  WebkitLineClamp: '1',
                                  overflow: 'hidden',
                                  wordBreak: 'break-all',
                                  height: '30px',
                                  lineHeight: '30px',
                                }}
                                key={`${row.label}_name`}
                              >{row.label}</div>
                            </Tooltip>
                          </td>
                          <td style={{ width: '30%', fontSize: '14px', padding: 0 }}>{row.value}元</td>
                        </tr>,
                      )
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </Col>
        </Row>
        
        
      </div>
    );
  }
}
