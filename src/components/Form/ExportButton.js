import React, { Component } from 'react';
import { Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import * as XLSX from 'xlsx';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';
import styles from './ChartTitle.css';

const s2ab = (s) => { // 字符串转字符流
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i !== s.length; i += 1) {
    view[i] = s.charCodeAt(i) & 0xFF;
  }
  return buf;
};

// 将指定的自然数转换为26进制表示。映射关系：[0-25] -> [A-Z]。
function getCharCol(n) {
  // let temCol = '',
  let s = '';
  let m = 0;
  while (n > 0) {
    m = (n % 26) + 1;
    s = String.fromCharCode(m + 64) + s;
    n = (n - m) / 26;
  }
  return s;
}

function getCharLength(str) {
  if (!str) {
    return 0;
  }
  const datas = str.split('');
  let resultLength = 0;
  for (const data of datas) {
    if (data.match(/[a-zA-Z0-9]/)) {
      resultLength += 1;
    } else {
      resultLength += 2;
    }
  }

  return resultLength * 8;
}

export default class ExportButton extends Component {
  constructor(props) {
    super(props);

    this.state = {
      exportDatas: props.exportDatas,
      merges: props.merges,
    };

    this.exportExcel = this.exportExcel.bind(this);
    this.createExcelData = this.createExcelData.bind(this);
  }

  // 处理数据更新
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { exportDatas, merges } = this.state;
    let updateCounter = 0;
    const nextState = {};

    if (exportDatas !== nextProps.exportDatas) {
      updateCounter += 1;
      nextState.exportDatas = nextProps.exportDatas;
    }

    if (merges !== nextProps.merges) {
      updateCounter += 1;
      nextState.merges = nextProps.merges;
    }

    if (!updateCounter) {
      return;
    }

    this.setState(nextState);
  }

  createExcelData(exportDatas) {
    const tmpDown = exportDatas[0];

    exportDatas.unshift({});

    const keyMap = []; // 获取键
    const cols = [];
    for (const k in tmpDown) {
      if (!{}.hasOwnProperty.call(tmpDown, k)) {
        continue;
      }
      keyMap.push(k);
      cols.push({ wpx: 45 });
      exportDatas[0][k] = k.indexOf('th') !== -1 ? '' : k;
    }

    exportDatas.splice(0, 1);
    const tmpdata = {}; // 用来保存转换好的exportDatas
    exportDatas
      .map((v, i) => keyMap.map((k, j) => ({ v: v[k],
        position: (j > 25 ? getCharCol(j) : String.fromCharCode(65 + j)) + (i + 1),
        i,
        j })))
      .reduce((prev, next) => prev.concat(next))
      .forEach((v) => {
        const widthLength = getCharLength(`${v.v}`);
        cols[v.j].wpx = cols[v.j].wpx < widthLength ? widthLength : cols[v.j].wpx;
        if (typeof v.v === 'number') {
          tmpdata[v.position] = {
            v: v.v,
            t: 'n',
          };
          return;
        }
        tmpdata[v.position] = {
          v: `${v.v}`,
          t: 's',
          z: '@',
        };
      });
    return { tmpdata, cols, tmpDown };
  }

  // 导出excel
  async exportExcel(exportDatas, merges = [], exportTitle, type, classNames) {
    let tmpWB = {
      Sheets: {},
    };
    let tmpDown = {};

    if (type === 'multiSheets') {
      for (const data of exportDatas) {
        const { tmpdata, cols, tmpDown: down } = this.createExcelData(data.classList);
        tmpDown = down;
        const outputPos = Object.keys(tmpdata);

        tmpWB['SheetNames'] = classNames;
        tmpWB['Sheets'][data.className] = {
          ...tmpdata, // 内容
          '!ref': `${outputPos[0]}:${outputPos[outputPos.length - 1]}`, // 设置填充区域
          '!cols': cols,
          '!merges': merges,
        };
      }
    } else {
      const { tmpdata, cols, tmpDown: down } = this.createExcelData(exportDatas);
      tmpDown = down;
      const outputPos = Object.keys(tmpdata);
      tmpWB = {
        SheetNames: ['sheet1'], // 保存的表标题
        Sheets: {
          sheet1: {
            ...tmpdata, // 内容
            '!ref': `${outputPos[0]}:${outputPos[outputPos.length - 1]}`, // 设置填充区域
            '!cols': cols,
            '!merges': merges,
          },
        },
      };
    }

    try {
      // 使用 Tauri 的文件保存对话框
      const filePath = await save({
        defaultPath: exportTitle ? `${exportTitle}.xlsx` : '文件下载.xlsx',
        filters: [{
          name: 'Excel 文件',
          extensions: ['xlsx']
        }]
      });

      if (filePath) {
        // 生成 Excel 文件的二进制数据
        const excelBuffer = XLSX.write(tmpWB, {
          bookType: 'xlsx',
          bookSST: true,
          type: 'array'
        });

        // 使用 Tauri 的文件系统 API 写入文件
        await writeFile(filePath, new Uint8Array(excelBuffer));

        console.log('文件保存成功:', filePath);
        return true;
      }
    } catch (error) {
      console.error('文件保存失败:', error);
      return false;
    }

    return false;
  }

  handleExportClick = async () => {
    const { exportDatas, merges } = this.state;
    const { exportTitle, type, classNames } = this.props;

    if (exportDatas && exportDatas.length > 0) {
      await this.exportExcel(exportDatas, merges, exportTitle, type, classNames);
    }
  }

  render() {
    const { noExport = false } = this.state;
    const { excleButtonClassName } = this.props;

    if (noExport) {
      return null;
    }

    return (
      <Button
        type="primary"
        className={excleButtonClassName || styles.excleButton}
        style={this.props.styles}
        onClick={this.handleExportClick}
      >
        <DownloadOutlined />
        导出Excel
      </Button>
    );
  }
}
