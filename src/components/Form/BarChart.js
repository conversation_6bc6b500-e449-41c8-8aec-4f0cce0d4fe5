/* eslint-disable */
import React, { Component } from 'react';
// import echarts from 'echarts';
// import echarts from 'echarts/lib/echarts';
import ReactECharts from '../ReactECharts/ECharts.js';
import { Row, Col } from 'antd';
import $ from 'jquery';

import styles from  './BarChart.css';

// const colorArr = ['#da0d68', '#975e6d', '#e0719c', '#f99e1c', '#ef5a78', '#f7f1bd', '#da1d23', '#dd4c51', '#3e0317', '#e62969', '#6569b0', '#ef2d36', '#c94a44', '#b53b54', '#a5446f', '#dd4c51', '#f2684b', '#e73451', '#e65656', '#f89a1c', '#aeb92c', '#4eb849', '#f68a5c', '#baa635', '#f7a128', '#f26355', '#e2631e', '#fde404', '#7eb138', '#ebb40f', '#e1c315', '#9ea718', '#94a76f', '#d0b24f', '#8eb646', '#faef07', '#c1ba07', '#b09733', '#8f1c53', '#b34039', '#ba9232', '#8b6439', '#187a2f', '#a2b029', '#718933', '#3aa255', '#a2bb2b', '#62aa3c', '#03a653', '#038549', '#28b44b', '#a3a830', '#7ac141', '#5e9a80', '#0aa3b5', '#9db2b7', '#8b8c90', '#beb276', '#fefef4', '#744e03', '#a3a36f', '#c9b583', '#978847', '#9d977f', '#cc7b6a', '#db646a', '#76c0cb', '#80a89d', '#def2fd', '#7a9bae', '#039fb8', '#5e777b', '#120c0c', '#c94930', '#caa465', '#dfbd7e', '#be8663', '#b9a449', '#899893', '#a1743b', '#894810', '#ddaf61', '#b7906f', '#eb9d5f', '#ad213e', '#794752', '#cc3d41', '#b14d57', '#c78936', '#8c292c', '#e5762e', '#a16c5a', '#a87b64', '#c78869', '#d4ad12', '#9d5433', '#c89f83', '#bb764c', '#692a19', '#470604', '#e65832', '#d45a59', '#310d0f', '#ae341f', '#d78823', '#da5c1f', '#f89a80', '#f37674', '#e75b68', '#d0545f'];

export default class BarChart extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tableData: props.tableData ? props.tableData.concat([]) : [],
      chartTitle: props.chartTitle,
      max: props.max,
      left: props.left,
      selected: null,
    };

    this.className = new Date().getTime();

    // this.show = this.show.bind(this);
  }

  // 处理数据更新
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { tableData, chartTitle, max, left } = this.state;
    let updateCounter = 0;
    const nextState = {};

    if (tableData !== nextProps.tableData) {
      updateCounter += 1;
      nextState.tableData = nextProps.tableData ? nextProps.tableData.concat([]) : [];
      $(this.contentRef).css('height', `${nextState.tableData.length * 50 + 50}px`)
    }

    if (chartTitle !== nextProps.chartTitle) {
      updateCounter += 1;
      nextState.chartTitle = nextProps.chartTitle;
    }

    if (max !== nextProps.max) {
      updateCounter += 1;
      nextState.max = nextProps.max;
    }

    if (left !== nextProps.left) {
      updateCounter += 1;
      nextState.left = nextProps.left;
    }

    if (!updateCounter) {
      return;
    }

    this.setState(nextState);
  }
  // shouldComponentUpdate(nextProps, nextState) {
  //   // console.log('shouldComponentUpdate', JSON.parse(JSON.stringify(nextProps)), JSON.parse(JSON.stringify(nextState)))
  //   const { tableData, chartTitle, max, left } = this.state;
  //   let updateCounter = 0;
  //   // const nextState = {};

  //   if (tableData !== nextProps.tableData) {
  //     updateCounter += 1;
  //     // nextProps.tableData = nextProps.tableData ? nextProps.tableData.concat([]) : [];
  //     // $(this.contentRef).css('height', `${nextProps.tableData.length * 50 + 100}px`)
  //   }

  //   if (chartTitle !== nextProps.chartTitle) {
  //     updateCounter += 1;
  //     // nextProps.chartTitle = nextProps.chartTitle;
  //   }

  //   if (max !== nextProps.max) {
  //     updateCounter += 1;
  //     // nextProps.max = nextProps.max;
  //   }

  //   if (left !== nextProps.left) {
  //     updateCounter += 1;
  //     // nextProps.left = nextProps.left;
  //   }

  //   if (!updateCounter) {
  //     return  false;
  //   }
  //   console.log('should shouldComponentUpdate')

  //   // this.setState(nextState);
  //   return true
  // }

  render() {
    const { tableData, chartTitle, max, left } = this.state; //, listTitle
    const { sortFunction } = this.props;

    const chartOptions = {
      title: {
        text: chartTitle,
        // subtext: 'Feature Sample: Gradient Color, Shadow, Click Zoom'
      },
      grid: {
        left: left ? left : '12%',
        top: chartTitle ? '10%' : 0,
        bottom: 50,
        height: tableData.length * 50
      },
      yAxis: {
        data: [],
        axisLabel: {
          // textStyle: {
          //   color: '#999',
          //   // align: 'left'
          // },
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        z: 10
      },
      xAxis: {
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          // textStyle: {
          //   color: '#999',
          // },
        }
      },
      legend: {
        selected: {},
        show: false
      },
      // dataZoom: [
      //   {
      //     type: 'inside'
      //   }
      // ],
      series: [
        // { // For shadow
        //   type: 'bar',
        //   itemStyle: {
        //     normal: {color: 'rgba(0,0,0,0.05)'}
        //   },
        //   barGap:'-100%',
        //   barCategoryGap:'40%',
        //   data: dataShadow,
        //   animation: false
        // },
        {
          type: 'bar',
          barWidth: 25,
          label: {
            show: true,
            // formatter: '{c}',
            formatter: (params) => {
              // console.log('params:',params);

              if (max) {
                const percentage = ((params.data / max) * 100).toFixed(2);
                return `${params.data}分  ${percentage}%`
              }

              return params.data
            },
            // color: '#000000',
            position: 'right'
          },
          itemStyle: {
            normal: {
              // color: new echarts.graphic.LinearGradient(
              //   0, 0, 0, 1,
              //   [
              //     {offset: 0, color: '#83bff6'},
              //     {offset: 0.5, color: '#188df0'},
              //     {offset: 1, color: '#188df0'}
              //   ]
              // )
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#83bff6' // 0% 处的颜色
                }, {
                  offset: 0.5, color: '#188df0' // 0% 处的颜色
                }, {
                  offset: 1, color: '#188df0' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            emphasis: {
              // color: new echarts.graphic.LinearGradient(
              //   0, 0, 0, 1,
              //   [
              //     {offset: 0, color: '#2378f7'},
              //     {offset: 0.7, color: '#2378f7'},
              //     {offset: 1, color: '#83bff6'}
              //   ]
              // )
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#2378f7' // 0% 处的颜色
                }, {
                  offset: 0.5, color: '#2378f7' // 0% 处的颜色
                }, {
                  offset: 1, color: '#83bff6' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            }
          },
          data: []
        }
      ]
    };

    if (max) {
      chartOptions.xAxis.max = max;
    }

    if (sortFunction) {
      tableData.sort(sortFunction);
    }
    // console.log('tableData:',tableData)

    for(let i = tableData.length - 1; i >= 0; i -=  1) {
      const row = tableData[i];
      chartOptions.series[0].data.push(row.value);
      chartOptions.yAxis.data.push(row.key)
    }

    // for (const row of tableData) {
    //   chartOptions.series[0].data.push(row.value);
    // }

    // for (const row of tableData) {
    //   chartOptions.yAxis.data.push(row.key)
    // }

    return (
      <div
        style={{ background: '#fff', width: '100%', margin: '20px', color: 'rgba(0, 0, 0, 0.65)', position: 'relative' }}
        ref={ref => {
          if (!ref) {
            return;
          }
          this.contentRef = ref;
          // console.log('height', `${tableData.length * 50 + 100}px`)
          $(this.contentRef).css('height', `${tableData.length * 50 + 100}px`);
          $('.chart > div > canvas').attr('height', `${tableData.length * 50 + 100}`)
        }}
      >
        <Row style={{ height: '100%' }}>
          <Col span={24} style={{ height: '100%' }}>
            <ReactECharts
              className={`${styles.chart} chart`}
              option={chartOptions}
              notMerge={false}
              style={{ width: '100%', float: 'left', height: '100%', zIndex: '100' }}
            />
          </Col>
        </Row>
      </div>
    );
  }
}
