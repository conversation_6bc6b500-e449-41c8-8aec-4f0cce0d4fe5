/* eslint-disable */
import React, { Component } from 'react';
// import echarts from 'echarts';
// import echarts from 'echarts/lib/echarts';
import ReactECharts from '../ReactECharts/ECharts.js';
import $ from 'jquery';
 
export default class Bar<PERSON>hart extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tableData: props.tableData ? props.tableData.concat([]) : [],
      chartTitle: props.chartTitle,
      max: props.max,
      left: props.left,
      selected: null,
    };

    this.className = new Date().getTime();

    // this.show = this.show.bind(this);
  }

  // 处理数据更新
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { tableData, chartTitle, max, left } = this.state;
    let updateCounter = 0;
    const nextState = {};

    if (tableData !== nextProps.tableData) {
      updateCounter += 1;
      nextState.tableData = nextProps.tableData ? nextProps.tableData.concat([]) : [];
      $(this.contentRef).css('height', `${nextState.tableData.length * 50 + 50}px`)
    }

    if (chartTitle !== nextProps.chartTitle) {
      updateCounter += 1;
      nextState.chartTitle = nextProps.chartTitle;
    }

    if (max !== nextProps.max) {
      updateCounter += 1;
      nextState.max = nextProps.max;
    }

    if (left !== nextProps.left) {
      updateCounter += 1;
      nextState.left = nextProps.left;
    }

    if (!updateCounter) {
      return;
    }

    this.setState(nextState);
  }

  render() {
    const { value } = this.props;

    const chartOptions = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
      },
      yAxis: {
        type: 'value',
        min: 0,
        minInterval: 1,
        axisLabel: {
          formatter: '{value}',
        },
      },
      series: [
        {
          data: [],
          type: 'bar',
          tooltip: {
            valueFormatter(value) {
              return `${value} 个`;
            },
          },
        }
      ],
    };
    
    if(value){
      chartOptions.xAxis.data = Object.keys(value);
      chartOptions.series[0].data = Object.values(value);
    }

    return (
      <div
        style={{ width: '90%', color: 'rgba(0, 0, 0, 0.65)', height: '200px' }}
      >
        <ReactECharts
          option={chartOptions}
          notMerge={false}
          style={{ width: '100%', float: 'left', height: '100%', zIndex: '100' }}
        />
      </div>
    );
  }
}
