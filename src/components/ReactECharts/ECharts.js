import React from 'react';
// import * as echarts from 'echarts/lib/echarts';
import * as echarts from 'echarts';
// console.log('echarts:',echarts);
class ECharts extends React.Component {
  // static defaultProps = {
  //   notMerge: false,
  //   notRefreshImmediately: false,
  //   style: {},
  // };

  constructor(props) {
    super(props);
    this.state = {};
    this.init = this.init.bind(this);
    this.dispose = this.dispose.bind(this);
  }

  componentDidMount() {
    this.init();
  }

  componentDidUpdate() {
    this.setOption();
  }

  componentWillUnmount() {
    this.dispose();
  }

  getInstance() {
    return this.chart;
  }

  setOption() {
    const {
      option,
      notMerge,
      notRefreshImmediately,
    } = this.props;
    if (option && this.chart) {
      this.chart.showLoading();
      this.chart.setOption(option, notMerge, notRefreshImmediately);
      this.chart.hideLoading();
    }
  }

  init(container) {
    if (!container) {
      return;
    }
    this.container = container;

    setTimeout(() => {
      const { selectChart } = this.props;
      this.chart = echarts.init(container);
      this.setOption();
      // console.log('this.chart:',this.chart);
      if (selectChart) {
        this.chart.on('click', selectChart);
      }
      window.addEventListener('resize', this.chart.resize);
      // this.chart.on('click', (params) => {
      //   console.log(params);
      // });
    }, 1000);
  }

  dispose() {
    if (this.chart) {
      window.removeEventListener('resize', this.chart.resize);
      this.chart.dispose();
      this.chart = null;
    }
  }

  render() {
    const {
      style,
      className,
    } = this.props;

    const newStyle = { width: '100%',
      height: '100%',
      ...style };

    return (
      <div ref={this.init} className={className} style={newStyle} />
    );
  }
}

export default ECharts;
