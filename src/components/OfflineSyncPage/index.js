import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Typo<PERSON>, Tree, Modal, Upload, List } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { get_network_cards_info } from '../../utils/train';
import styles from './index.css';
import { uploadSchoolConfig, uploadClassAndStudents, uploadUsers, uploadTrain, uploadFile as uploadZipFile, uploadTrainSeries } from '../../services/upload';
import { alertError, alertSuccess } from '../../utils/edittools';
import { decrypt } from '../../utils/crypto';
import { open } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import { readAsBuffer, readDirRecursive, readJsonFileContent } from '../../utils/file';
import { addLog } from '../../utils/log';

const { Paragraph } = Typography;
const { DirectoryTree } = Tree;

function OfflineSync(props = {}) {
    const [mac, setMac] = React.useState(null);
    const [loading, setLoading] = React.useState(false);
    const [treeData, setTreeData] = React.useState([]);
    const [openUploadModal, setOpenUploadModal] = React.useState(false);

    const { gotoLogin } = props;

    const isGlobal = window.location.pathname === '/offlineSyncGlobal';

    let isTrial = false;
    try {
        const sessionResponse = localStorage.sessionResponse ? JSON.parse(localStorage.sessionResponse) : {};
        isTrial = sessionResponse.site_url === 'demo';
    } catch (e) {
        console.error(e);
    }


    // 获取本机MAC地址
    const getMac = async () => {
        const pcInfo = await get_network_cards_info();
        let mac = pcInfo[0] && pcInfo[0].mac ? pcInfo[0].mac : null;

        if(localStorage.mac) {
            const findIndex = pcInfo.find(row => row.mac === localStorage.mac);
            if (findIndex !== -1) {
                mac = localStorage.mac;
            }
        }

        return mac;
    }

    // 上传授权文件
    const uploadPermissionFile = async (data) => {
        // 检查MAC地址与本机是否一致
        const mac = await getMac();
        if (!mac) {
            throw new Error('获取本机MAC地址失败');
        }

        data.licence = JSON.stringify(data.licence);
        data.school_name = data.schoolName;

        let licence = {};
        try {
            let licenceObj = JSON.parse(data.licence);
            const licenceStr = decrypt(licenceObj);
            if (!licenceStr) {
                throw new Error('授权文件解密失败');
            }

            licence = JSON.parse(licenceStr);
        } catch (e) {
            console.error(e);
            throw new Error('授权文件格式错误');
        }

        const uploadMac = licence.mac;
        if (mac !== uploadMac) {
            throw new Error('MAC地址不一致');
        }

        const response = await uploadSchoolConfig(data);
        if (!response || response.code !== 0) {
            throw new Error(response?.message || '上传授权文件失败');
        }
    }

    // 上传班级和学生信息
    const uploadClassAndStudentsFile = async (data) => {
        const { allClassIds, classList } = data;
        const response = await uploadClassAndStudents({ all_class_ids: allClassIds, class_list: classList});
        if (!response || response.code !== 0) {
            throw new Error(response?.message || '上传班级和学生信息失败');
        }
    }

    // 上传教师信息
    const uploadUsersFile = async (data) => {
        const newData = data.map(i => { return { ...i, admin_authority: i.adminAuthority } });
        const response = await uploadUsers(newData);
        if (!response || response.code !== 0) {
            throw new Error(response?.message || '上传用户信息失败');
        }
    }

    // 上传训练试卷
    const uploadTrainFile = async (data) => {
        const { allTrainIds, trainList } = data;
        const response = await uploadTrain({ all_train_ids: allTrainIds, train_list: trainList });
        if (!response || response.code !== 0) {
            throw new Error(response?.message || '上传训练试卷失败');
        }
    }

    const uploadSeriesFile = async (data) => {
        const response = await uploadTrainSeries(data);
        if (!response || response.code !== 0) {
            throw new Error(response?.message || '上传试卷系列文件失败');
        }
    }

    // 上传训练试卷压缩包文件
    const uploadTrainZipFile = async ({ fileName, filePath }) => {
        console.debug(filePath, 'filePath');

        let response = null;
        try {
            response = await invoke("upload_file", {
                filePath: filePath,
                fileName: fileName,
            });
        } catch (e) {
            console.error(e);
            addLog({ err: e })
            throw new Error('上传训练试卷压缩包失败');
        }

        if (!response || response !== "上传成功") {
            throw new Error(response || '上传训练试卷压缩包失败');
        }
    }

    async function uploadJson(name, data) {
        switch (name) {
            case 'permission.json':
                await uploadPermissionFile(data);
                break;
            case 'class.json':
                await uploadClassAndStudentsFile(data);
                break;
            case 'teacher.json':
                await uploadUsersFile(data);
                break;
            case 'train.json':
                await uploadTrainFile(data);
                break;
            case 'series.json':
                await uploadSeriesFile(data);
                break;
            default:
                console.error(`${name} 文件不在上传范围内`);
                break;
        }
    }

    const onClickUpload = async() => {
        const selected = await open({
            multiple: false,
            directory: true,
            filters: [{
                name: 'json',
                extensions: ['json']
            }, {
                name: 'zip',
                extensions: ['zip']
            }]
        });

        // 解析文件夹
        let dirs = [];
        try {
            dirs = await readDirRecursive(selected) || [];
        } catch (e) {
            console.error(e);
            alertError('读取文件夹失败');
            return;
        }

        let fileTree = [{
            title: 'train',
            key: 'train',
            type: 'folder',
            children: [],
        }];

        for (const file of dirs) {
            // 解析文件夹，解析json文件和压缩文件
            const { children, name, path } = file;
            const ext = name.split('.').pop();

            if (name === 'train' && children && children.length) {
                const currentDir = fileTree.find(i => i.title === 'train' && i.type === 'folder')

                const newChildren = [];
                for (const child of children) {
                    const { name, path } = child;
                    const ext = name.split('.').pop();

                    if (ext !== 'zip') {
                        continue;
                    }

                    newChildren.push({
                        title: name,
                        key: path,
                        isLeaf: true,
                        path: path,
                        type: 'zip',
                    });
                }

                currentDir.children = newChildren;
            } else {
                // 检查文件后缀
               if (ext === 'json') {
                    // 解析json文件
                    const data = await readJsonFileContent(path);
                    fileTree.push({
                        title: name,
                        key: path,
                        path: path,
                        file: data,
                        isLeaf: true,
                        type: 'json',
                    });
                }

            }
        }

        setTreeData([...fileTree]);
        setOpenUploadModal(true);
    };

    const uploadFile = async () => {
        if (!treeData || !treeData.length) {
            alertError('请先选择文件夹');
            return;
        }

        setLoading(true);
        try {
            for (const tree of treeData) {
                const { children, title, file, type } = tree;
                if (type === 'json') {
                    addLog({ err: `正在上传 ${title}` });
                    await uploadJson(title, file);
                    addLog({ err: `${title} 上传成功` });
                    continue;
                }

                if (type === 'folder' && title === 'train' && children && children.length) {
                    for (const child of children) {
                        const { title: subTitle, type, path } = child;
                        if (type !== 'zip') {
                            continue;
                        }

                        addLog({ err: `正在上传 ${subTitle}` });
                        await uploadTrainZipFile({ fileName: subTitle, filePath: path });
                        addLog({ err: `${subTitle} 上传成功` });
                    }
                }
            }
        } catch (error) {
            console.error(error);
            alertError("错误", error.message);
            setLoading(false);
            addLog({ err: error })
            return;
        }

        alertSuccess('同步成功')
        setLoading(false);
        setOpenUploadModal(false);

        // 如果是试用版，在导入成功后跳转登录页面
        if (isTrial) {
            gotoLogin();
        }
    }

    const listData = [
        {
            title: '1.获取MAC地址',
            content: '请点击获取本机MAC地址按钮，获取教师端的MAC地址'
        }, {
            title: '2.下载同步文件',
            content: '从在线训练系统的 训练管理-离线同步 菜单中选择需要同步的班级、试卷，将教师端的MAC地址输入到机房信息中，将离线文件导出'
        }, {
            title: '3.上传同步文件夹',
            content: '将导出的文件解压缩，点击上传同步文件夹，选择文件夹进行上传'
        }
    ]

    return (
        <div>
             <div className={styles.top}>
                <Row>
                    <Col span={3} className={styles.title}>
                        离线同步
                    </Col>
                </Row>
            </div>

            <Row style={{ margin: '20px' }}>
                <div>
                    支持教师端无需联网即可对接云平台或同步班级和试卷
                </div>
            </Row>

            <div style={{ margin: '20px' }}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                    <List.Item>
                        <List.Item.Meta
                            title={<a style={{ fontWeight: 600 }}>{item.title}</a>}
                            description={item.content}
                        />
                    </List.Item>
                    )}
                />
            </div>

            <Row justify="center" style={{ marginTop: '20px' }}>
                <Button
                    onClick={async() => {
                        const currentMac = await getMac();
                        if (!currentMac) {
                            alertError('获取本机MAC地址失败');
                            return;
                        }
                        setMac(currentMac);
                    }}
                >
                    点击获取本机MAC地址
                </Button>
            </Row>

            <Row justify="center" style={{ marginTop: '20px', fontSize: '20px' }}>
                {
                    mac && (
                        <Paragraph copyable>{mac}</Paragraph>
                    )
                }
            </Row>

            <Row justify="center" style={{ marginTop: '20px' }}>
                <Button icon={<UploadOutlined />} loading={loading} type={isGlobal ? "default" : "primary"} onClick={onClickUpload}>上传同步文件夹</Button>
            </Row>

            {/* <Row justify="center" style={{ marginTop: '20px' }}>
                <Upload
                    accept=".zip"
                    showUploadList={false}
                    beforeUpload={async (file) => {
                        const { name } = file;
                        const ext = name.split('.').pop();
                        if (ext !== 'zip') {
                            alertError('请上传zip文件');
                            return false;
                        }

                        const buffer = await readAsBuffer(file);
                        const response = await uploadZipFile({
                            fileData: {
                                file: new Uint8Array(buffer),
                                fileName: name,
                                mime: file.type,
                            }
                        })
                        if (!response || response.code !== 0) {
                            alertError(response?.message || '上传失败');
                            return false;
                        }

                        alertSuccess('上传成功');
                        return false;
                    }}
                >
                    <Button
                        style={{ marginTop: '20px' }}
                    >
                        上传离线文件
                    </Button>
                </Upload>
            </Row> */}


            <Modal
                title="上传文件"
                open={openUploadModal}
                onCancel={() => setOpenUploadModal(false)}
                footer={null}
            >
                <div>
                    <Row justify="center">
                        确认上传以下文件？
                    </Row>
                    <Row justify="center" style={{ marginTop: '20px', height: '280px', overflow: 'auto' }}>
                        <DirectoryTree
                            defaultExpandAll={true}
                            treeData={treeData}
                        />
                    </Row>
                    <Row justify="center" style={{ marginTop: '20px' }}>
                        <Button
                            type="primary"
                            onClick={uploadFile}
                            loading={loading}
                            style={{ width: '200px'}}
                        >
                            确认上传
                        </Button>
                    </Row>
                </div>
            </Modal>

            {
                isGlobal ?
                    <Row justify="center" style={{ marginTop: '50px' }}>
                        <Button
                            type="primary"
                            onClick={gotoLogin}
                        >
                            返回登录
                        </Button>
                    </Row>
                    : null
            }
        </div>
    )
}

export default OfflineSync;
