import { fetch as tauriFetch } from '@tauri-apps/plugin-http';

// Scan the User Agent for a specific string
function scanUA(str) {
  // If the string is contained in the User Agent return true
  return window.navigator.userAgent.indexOf(str) > -1;
}

export function getDeviceType() {
  // set a variable for the browser's reported architecture
  const platform = window.navigator.platform;
  // Set the className variable based on what their User Agent reported
  let className = '';
  // if the User Agent has any of the following strings it's 64-Bit
  if (
    scanUA('x86_64')
    || scanUA('x86-64')
    || scanUA('Win64')
    || scanUA('x64;')
    || scanUA('amd64')
    || scanUA('AMD64')
    || scanUA('WOW64')
    || scanUA('x64_64')
    || platform === 'MacIntel'
    || platform === 'Linux x86_64'
  ) {
    className = 'arch64';
    // Otherwise if it's got any of these it's a mobile device
  } else if (
    platform === 'Linux armv7l'
    || platform === 'iPad'
    || platform === 'iPhone'
    || platform === 'Android'
    || platform === 'iPod'
    || platform === 'BlackBerry'
  ) {
    className = 'mobile';
    // This one doesn't report accurately, set it to unknown
  } else if (platform === 'Linux i686') {
    className = 'unknown';
    // Anything else will just be lumped into 32-bit
  } else {
    className = 'arch32';
  }

  return className;
}

export async function checkNetworkStatus() {
  if (!navigator.onLine) {
    return false;
  }

  const url = "http://hxr.iyopu.com/ok";
  const timeout = 5000; // 超时时间设置为5秒

  // 使用AbortController来处理超时
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await tauriFetch(url, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const text = await response.text();
      // Optional: you can log or process the response text if needed
      console.log(text);
      return true;
    } else {
      return false;
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Fetch timeout');
    } else {
      console.error('Fetch error:', error);
    }
    return false;
  }
}


