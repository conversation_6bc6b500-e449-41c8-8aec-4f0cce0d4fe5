import {
    exists,
    readTextFile,
    readDir,
} from "@tauri-apps/plugin-fs";


// 读取文件
export function readAsText(file) {
    return new Promise(((resolve, reject) => {
        try {
            const reader = new FileReader();

            // 等待文件加载到位
            reader.onload = function (e) {
                const data = e.target.result;
                resolve(data);
            };

            reader.readAsText(file);
        } catch (e) {
            reject(e);
        }
    }));
}

export function readAsBuffer(file) {
    return new Promise(((resolve, reject) => {
        try {
            const reader = new FileReader();

            // 等待文件加载到位
            reader.onload = function (e) {
                const data = e.target.result;
                resolve(data);
            };

            reader.readAsArrayBuffer(file);
        } catch (e) {
            reject(e);
        }
    }));
}

// 绝对路径读取文件内容
export async function readJsonFileContent(filePath) {
    let exist = false;
    try {
        exist = await exists(filePath);
    } catch (e) {
        console.error(e);
        throw new Error(`${filePath} 不存在`)
    }

    if (!exist) {
        throw new Error(`${filePath} 不存在`)
    }

    let fileContent = "";
    try {
        fileContent = await readTextFile(filePath);
    } catch (e) {
        console.error(e);
        throw new Error(`${filePath} 读取失败`)
    }

    let json = {};
    try {
        json = JSON.parse(fileContent);
    } catch (e) {
        console.error(e);
        throw new Error(`${filePath} 解析失败`)
    }

    return json;
}

// 读取文件夹
export async function readDirRecursive(dirPath) {
    let exist = false;
    try {
        exist = await exists(dirPath);
    } catch (e) {
        console.error(e);
        throw new Error(`${dirPath} 不存在`)
    }

    if (!exist) {
        throw new Error(`${dirPath} 不存在`)
    }

    let dirs = [];
    try {
        dirs = await readDir(dirPath, { recursive: true });
    } catch (e) {
        console.error(e);
        throw new Error(`${dirPath} 读取失败`)
    }

    return dirs;
}
