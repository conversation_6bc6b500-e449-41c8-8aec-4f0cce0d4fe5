/**
 * 课程同步 WebSocket 客户端
 * 用于实时接收课程同步进度和状态更新
 */
class CourseWebSocket {
  constructor() {
    this.ws = null;
    this.listeners = {};
    this.reconnectTimer = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.room = 'teacher'; // 默认连接到教师房间
    this.isManualClose = false; // 是否手动关闭
  }

  /**
   * 连接 WebSocket
   * @param {string} room - 房间名称 (teacher/student/local)
   */
  connect(room = 'teacher') {
    this.room = room;
    this.isManualClose = false;

    // 根据当前协议选择 ws 或 wss
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}/ws/${room}`;
    
    console.log(`[WebSocket] 正在连接到: ${wsUrl}`);
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('[WebSocket] 连接已建立');
        this.reconnectAttempts = 0;
        this.emit('connected', { room });
      };
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('[WebSocket] 收到消息:', data);
          
          // 根据消息类型触发不同的事件
          if (data.type === 'sync_progress') {
            // 同步进度更新
            this.emit('syncProgress', data);
          } else if (data.type === 'sync_completed') {
            // 同步完成
            this.emit('syncCompleted', data);
          } else if (data.type === 'sync_error') {
            // 同步错误
            this.emit('syncError', data);
          } else if (data.type === 'sync_started') {
            // 同步开始
            this.emit('syncStarted', data);
          } else {
            // 其他消息类型
            this.emit('message', data);
          }
        } catch (error) {
          console.error('[WebSocket] 解析消息失败:', error, event.data);
        }
      };
      
      this.ws.onerror = (error) => {
        console.error('[WebSocket] 连接错误:', error);
        this.emit('error', error);
      };
      
      this.ws.onclose = (event) => {
        console.log('[WebSocket] 连接已关闭', event.code, event.reason);
        this.emit('disconnected', { code: event.code, reason: event.reason });
        
        // 如果不是手动关闭，尝试重连
        if (!this.isManualClose && this.reconnectAttempts < this.maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // 指数退避，最多30秒
          console.log(`[WebSocket] 将在 ${delay}ms 后尝试重连 (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
          
          this.reconnectTimer = setTimeout(() => {
            this.reconnectAttempts++;
            this.connect(this.room);
          }, delay);
        } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.error('[WebSocket] 已达到最大重连次数，停止重连');
          this.emit('reconnectFailed');
        }
      };
    } catch (error) {
      console.error('[WebSocket] 创建连接失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 发送消息到服务器
   * @param {string} type - 消息类型
   * @param {object} data - 消息数据
   */
  send(type, data = {}) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = JSON.stringify({ type, ...data });
      this.ws.send(message);
      console.log('[WebSocket] 发送消息:', { type, ...data });
    } else {
      console.warn('[WebSocket] 未连接，无法发送消息');
      return false;
    }
    return true;
  }

  /**
   * 订阅事件
   * @param {string} event - 事件名称
   * @param {function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  /**
   * 取消订阅事件
   * @param {string} event - 事件名称
   * @param {function} callback - 回调函数
   */
  off(event, callback) {
    if (!this.listeners[event]) return;
    if (callback) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    } else {
      delete this.listeners[event];
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`[WebSocket] 事件 ${event} 的回调执行出错:`, error);
      }
    });
  }

  /**
   * 检查连接状态
   * @returns {boolean}
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 获取连接状态
   * @returns {string}
   */
  getState() {
    if (!this.ws) return 'CLOSED';
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'OPEN';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'CLOSED';
      default:
        return 'UNKNOWN';
    }
  }

  /**
   * 关闭连接
   */
  close() {
    console.log('[WebSocket] 手动关闭连接');
    this.isManualClose = true;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.listeners = {};
    this.reconnectAttempts = 0;
  }

  /**
   * 重置重连尝试次数
   */
  resetReconnectAttempts() {
    this.reconnectAttempts = 0;
  }
}

// 导出单例
const courseWebSocket = new CourseWebSocket();
export default courseWebSocket;


