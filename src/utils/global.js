import { invoke } from '@tauri-apps/api/core';
import { version } from '@tauri-apps/plugin-os';
import { getVersion } from '@tauri-apps/api/app'

const globalVars = {
  // 以下四个根目录启动时赋值
  baseURL: null,
  userInfo: localStorage.sessionResponse ? JSON.parse(localStorage.sessionResponse) : null,
  appVersion: null,
  winVersion: null
};

const appVersion=  await getVersion()
globalVars.appVersion = appVersion;

const osVersion = version();
let winVersion = 'win10';
// 提取主要版本号和次版本号
const [major, minor, build] = osVersion.split('.').map(Number);

if (major === 10) {
  if (build >= 22000) {
    winVersion = 'win11';
  } else {
    winVersion = 'win10';
  }
} else {
  winVersion = 'win10';
}
globalVars.winVersion = winVersion;

window.globalVars = globalVars;

// 当tauri应用的时候重写地址
if (window.__TAURI__) {
  const get_server_url = function() {
    return invoke("get_server_url", {});
  };
  const serverUrl = await get_server_url();
  globalVars.baseURL = serverUrl;
} else {
  globalVars.baseURL = `${location.protocol}//${location.host}`;
}
export default globalVars;
