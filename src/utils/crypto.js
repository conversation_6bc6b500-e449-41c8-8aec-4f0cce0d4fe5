const crypto = require('crypto');

const algorithm = 'aes-256-ctr';
const secretKey = 's1283904iujoidsgvjskSDKUHJK!@#@$';

const encrypt = (text) => {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
    const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);

    return {
        iv: iv.toString('hex'),
        content: encrypted.toString('hex')
    };
};

const decrypt = (hash) => {
    const decipher = crypto.createDecipheriv(algorithm, secretKey, Buffer.from(hash.iv, 'hex'));
    const decrpyted = Buffer.concat([decipher.update(Buffer.from(hash.content, 'hex')), decipher.final()]);
    return decrpyted.toString();
};

const Hmac = (key, data) => {
    if (!key) {
        throw new Error(`请提供密钥`);
    }

    if (!data) {
        throw new Error(`请提供加密内容`);
    }

    const sign = crypto.createHmac('md5', key).update(data).digest('hex').toUpperCase();
    return sign;
}

module.exports = {
    encrypt,
    decrypt,
    Hmac,
};