import { notification } from 'antd';
import globalVars from '@/utils/global';
import { invoke } from '@tauri-apps/api/core';
import { fetch as tauriFetch } from '@tauri-apps/plugin-http';
import { addLog } from '@/utils/log';
import { listen } from '@tauri-apps/api/event';
import { alertError } from '@/utils/edittools';

// 默认服务器地址
export const baseURL = '';

function checkStatus(url, newOptions, options, response) {
  if (response.status >= 200 && response.status < 300) {
    return response;
  }

  if (response && response.status === 403) {
    // 正常调用一次fetch
    return fetch(url, newOptions);
  }

  if (response.status === 502) {
    notification.destroy();
    alertError(`${response.status}错误`, '服务器正在重新启动，请您稍候再试！')
    return;
  }

  return response;
}

export const get_server_url = function () {
  return invoke("get_server_url", {});
};

export async function getTauriClient(url, newOptions) {
  let allUrl = '';

  const serverUrl = await get_server_url();

  allUrl = `${serverUrl}${url}`.replace('netWork-api', 'api')

  const requestCode = { method: 'GET', ...newOptions };

  requestCode.headers = { ...newOptions.headers, credentials: 'include'};

  if (newOptions.body) {
    requestCode.body = JSON.stringify(newOptions.body);
  }

  // 把记录的cookie发出来，就要一个id
  if (localStorage.cookie) {
    requestCode.headers.Cookie = localStorage.cookie
  }

  const result = await tauriFetch(allUrl, requestCode);
  if(result.status === 500) {
    const errorMessage = result?.data?.message || result?.data || result.statusText;
    alertError('请求' + allUrl + '错误', errorMessage)
    return Promise.reject(errorMessage);
  }

  // 记录下cookie
  try {
    const cookieStr = result.headers['set-cookie'];
    const cookieResult = cookieStr.match(/(id=.+?);/);

    if (cookieResult && cookieResult[1]) {
      localStorage.cookie = cookieResult[1]
    }
  } catch (e) {}

  const jsonData = await result.json();

  const response = jsonData;
  console.debug('网络请求响应', response);
  return response;
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */
export default function requestServer(rawURL, options) {
  // 拼接完整URL，避免被CDN污染
  let url = `${baseURL}${rawURL}`;

  const defaultOptions = {
    credentials: 'include',
  };
  const newOptions = { ...defaultOptions, ...options };
  if (newOptions.method === 'POST' || newOptions.method === 'PUT' || newOptions.method === 'DELETE') {
    const method = newOptions.method.toLowerCase();
    if (!options.body) {
      options.body = {};
    }

    // 没有禁用CSRF并且是POST/PUT/PATCH
    if (['post', 'put', 'patch', 'delete'].indexOf(method) !== -1) {
      // 防火墙限制PUT、DELETE模式，此时所有的PUT、DELETE请求都会被转换为POST请求
      if (window.methodOverrideMode && (['put', 'delete'].indexOf(method) !== -1)) {
        newOptions.method = 'POST';

        // 路径部分前面加上/_method/${method}
        url = `${globalVars.baseURL}${rawURL.replace("/api", `/api/_method/${method}`)}`;
      }
    }

    newOptions.headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json; charset=utf-8',
      ...newOptions.headers,
    };
    if (!window.__TAURI__) {
      newOptions.body = JSON.stringify(options.body);
    }
  }

  if (window.__TAURI__) {
    console.debug("网络请求", url, newOptions);
    return getTauriClient(url, newOptions)
      .catch((error) => {
        console.log(error)
        alertError('请求错误', error)
        // umiHistory.replace('/login');
        return;
      })
  }

  return fetch(url, newOptions)
    .then((response) => checkStatus(url, newOptions, { ...options }, response))
    .then((response) => {
      try {
        return response.json();
      } catch (e) {
        console.log(e);
        return response;
      }
    })
}

// 文件上传
export async function fileFetch(url, options) {
  const serve_url = await get_server_url();

  const { body: formData = {} } = options;

  const path = `${serve_url}${url}`;
  const newOptions = {
    method: 'POST',
    body: JSON.stringify(formData),
    headers:{"Content-Type": "multipart/form-data"},
    // credentials: 'same-origin',
    // enctype: 'multipart/form-data',
    // responseType: ResponseType.JSON,
  };

  // console.debug(path, newOptions, 'fileFetch')

  let result;
  try {
    result = await tauriFetch(path, newOptions);
  } catch (e) {
    console.error(e, 'fileFetch error')
  }

  // console.debug(result, 'fileFetch result')

  return result && result.data ? result.data : result;
}

// 调用WebSocket客户端，会按照配置文件连接服务器，并在服务器收到消息时，通过plt-ws-server-notice全局消息送至TS
// 注意，这个函数不要await啊，可能永远不会把控制权交回来
let reconnectTimer = null;

export function newWSConnect() {
  console.log("connecting");

  invoke("ws_client")
    .then((s) => {
      console.log("ws_client", s);

      if (s === '与服务器连接已关闭' || (s && s.includes('连接错误'))) {
        scheduleReconnect();
      }
    })
    .catch((e) => {
      console.error("ws_client连接失败", e);
      scheduleReconnect();
    });
}

// 定时重新连接
function scheduleReconnect() {
  if (reconnectTimer) {
    return;
  }

  reconnectTimer = setTimeout(() => {
    reconnectTimer = null;
    newWSConnect();
  }, 3000);
}

export function wsListen({ onTerminalRefresh }) {
  const task = listen(
    "plt-ws-server-notice",
    (event) => {
      const { payload: { code, message: rawMessage = "" } = {} } = event;
      console.log(event)

      if (code !== 0) {
        alertError("WebSocket重新连接中...");
        scheduleReconnect();
        return;
      }

      const message = JSON.parse(rawMessage);
      console.log(message, "收到message");

      switch (message.type) {
        case "terminalRefresh":
          if (onTerminalRefresh) {
            onTerminalRefresh(message);
          }
          break;
        default:
          throw new Error(`异常的WS请求${message.type}`);
      }
    }
  );

  let closed = false;
  let unlisten_got = null;

  return {
    open() {
      console.log("open");
      task.then((unlisten) => {
        if (closed) {
          console.log("closed1");
          unlisten();
          return;
        }

        unlisten_got = unlisten;
      });
    },

    close() {
      closed = true;
      if (unlisten_got) {
        console.log("closed2");
        unlisten_got();
      }
    },
  };
}

