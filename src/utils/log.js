import { invoke } from "@tauri-apps/api/core";

export const writeLog = async ({ content }) => {
  try {
    await invoke("write_log", { content });
  } catch (e) {
    console.error(e);
  }
};

export const addLog = async ({ err, request }) => {
  let fileContent =
    err && JSON.stringify(err, Object.getOwnPropertyNames(err), 2);

  let requestContent = request && JSON.stringify(request, null, 2);

  let content = err ? fileContent : requestContent;

  if (!content) {
    return;
  }

  await writeLog({ content: content });
};
