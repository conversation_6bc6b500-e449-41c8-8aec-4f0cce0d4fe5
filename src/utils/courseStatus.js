// 将后端返回的 modify_status 映射为前端显示的状态
// @param {String} modifyStatus - 后端返回的状态：删除、无变化、新增、更新
// @returns {String} - 前端显示状态: '待删除' | '已更新' | '待下载' | '待更新'

export function mapModifyStatus(modifyStatus) {
  const statusMap = {
    '删除': '待删除',
    '无变化': '已更新',
    '新增': '待下载',
    '更新': '待更新',
  };
  
  return statusMap[modifyStatus] || '已更新'; // 默认返回"已更新"
}

// 从后端返回的课程数据中提取所有小节的状态
// 后端已经在每个 section 中包含了 modify_status 字段
// @param {Object} remoteCourseInfo - 远程课程详情对象（包含 modify_status）
// @returns {Object} - 课程所有小节的状态映射 { "章节名_小节名": "状态" }

export function calculateAllSectionStatus(remoteCourseInfo) {
  const statusMap = {}; // 用来存储所有小节的状态

  // 如果没有数据，直接返回空对象
  if (!remoteCourseInfo) {
    return statusMap;
  }

  // 获取远程课程的章节列表
  const remoteChapters = remoteCourseInfo.indics || [];

  // 遍历所有章节
  remoteChapters.forEach(chapter => {
    const { chapterName, sections = [] } = chapter;

    // 遍历章节下的每个小节
    sections.forEach(section => {
      const { sectionName, modify_status } = section;
      
      // 生成唯一的key：章节名_小节名
      const key = `${chapterName}_${sectionName}`;

      // 将后端的 modify_status 映射为前端状态
      statusMap[key] = mapModifyStatus(modify_status);
    });
  });

  return statusMap;
}

// 统计各状态的数量
// @param {Object} statusMap - 状态映射对象
// @returns {Object} - 各状态的统计 { 待下载: 0, 待更新: 0, 待删除: 0 }

export function countStatus(statusMap) {
  const counts = {
    待下载: 0,
    待更新: 0,
    待删除: 0,
    已更新: 0,
  };

  Object.values(statusMap).forEach(status => {
    if (counts[status] !== undefined) {
      counts[status]++;
    }
  });

  return counts;
}

