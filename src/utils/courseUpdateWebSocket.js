/**
 * 课程更新 WebSocket 客户端
 * 连接到本地 TCP 端口 46046，接收课程下载和解压进度
 */
class CourseUpdateWebSocket {
  constructor() {
    this.ws = null;
    this.listeners = {};
    this.reconnectTimer = null;
    this.isManualClose = false;
  }

  /**
   * 连接 WebSocket（连接到 localhost:46046）
   */
  connect() {
    this.isManualClose = false;

    // 固定连接到本地46046端口
    const wsUrl = `ws://localhost:46046`;
    
    console.log(`[课程更新WS] 正在连接到: ${wsUrl}`);
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('[课程更新WS] 连接已建立');
        this.emit('connected');
      };
      
      this.ws.onmessage = (event) => {
        try {
          // 后端直接发送进度数字（0-100）
          const progress = parseInt(event.data, 10);
          
          console.log('[课程更新WS] 收到进度:', progress);
          
          // 触发进度事件
          this.emit('progress', {
            percent: progress,
            status: progress >= 100 ? 'completed' : 'process'
          });
          
          // 如果是100%，表示完成
          if (progress >= 100) {
            this.emit('completed', { percent: 100 });
            // 自动关闭连接
            this.close();
          }
        } catch (error) {
          console.error('[课程更新WS] 解析消息失败:', error);
        }
      };
      
      this.ws.onerror = (error) => {
        console.error('[课程更新WS] WebSocket错误:', error);
        this.emit('error', { message: '连接错误' });
      };
      
      this.ws.onclose = () => {
        console.log('[课程更新WS] 连接已关闭');
        this.emit('disconnected');
        
        // 如果不是手动关闭，尝试重连（但对于46046端口，通常不需要重连）
        if (!this.isManualClose) {
          console.log('[课程更新WS] 连接意外关闭');
        }
        
        this.ws = null;
      };
      
    } catch (error) {
      console.error('[课程更新WS] 创建连接失败:', error);
      this.emit('error', { message: '创建连接失败: ' + error.message });
    }
  }

  /**
   * 检查连接状态
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 手动关闭连接
   */
  close() {
    this.isManualClose = true;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    console.log('[课程更新WS] 已手动关闭连接');
  }

  /**
   * 注册事件监听器
   * @param {string} event - 事件名称：'connected', 'disconnected', 'progress', 'completed', 'error'
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (!this.listeners[event]) return;
    
    if (callback) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    } else {
      delete this.listeners[event];
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (!this.listeners[event]) return;
    
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`[课程更新WS] 事件处理器错误 (${event}):`, error);
      }
    });
  }

  /**
   * 清理所有监听器
   */
  removeAllListeners() {
    this.listeners = {};
  }
}

// 导出单例
const courseUpdateWS = new CourseUpdateWebSocket();
export default courseUpdateWS;

