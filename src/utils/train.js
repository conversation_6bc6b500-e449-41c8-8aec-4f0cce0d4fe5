/* eslint-disable no-useless-escape */
// const { Buffer: NodeBuffer } = require("buffer/");
// import <PERSON><PERSON>eader from "mdb-reader";
// import XLSX from "@/utils/xlsx";
// import { exists, readTextFile, readBinaryFile } from "@tauri-apps/api/fs";
// import { judge as judgeWPS } from "@/utils/judge/wps_table";
// import { judge as judgeAccess } from "@/utils/judge/access";
// import { readDir /* BaseDirectory */ } from "@tauri-apps/api/fs";
// import { invoke } from "@tauri-apps/api";
// import { resourceDir } from "@tauri-apps/api/path";
// import { alertInfo } from "./edittools";
// import { history } from "umi";
import { invoke } from '@tauri-apps/api/core';
// console.log('teacherUrl:',teacherUrl)

export const get_server_url = function () {
  // console.log('--------------------------------------')
  return invoke("get_server_url", {});
};

// 获取本机网卡信息
export const get_network_cards_info = function () {
  return invoke("get_network_cards_info", {}).then(JSON.parse).catch((e) => {
    console.error(e);
  });
};

export async function wsConnect(mode, callBack) {
  // if (!window.__TAURI_IPC__) {
  //   return
  // }
  // const mode = "student";
  const baseUrl = await get_server_url();
  const wsUri = `${baseUrl.replace("http", "ws")}/ws/${mode}`;
  console.log(wsUri);
  console.log("Connecting...");
  let socket = new WebSocket(wsUri);

  socket.onopen = () => {
    console.log("Connected");
  };

  socket.onmessage = (ev) => {
    console.log("Received: " + ev.data, "message");

    if (callBack) {
      callBack(ev.data)
    }
  };

  socket.onclose = () => {
    console.log("Disconnected");
    socket = null;
  };

  return socket
}

// 点击滚动到对应位置
export const scrollToAnchor = (anchorname, questionDom) => {
  if (anchorname) {
    const anchorElement = document.getElementById(anchorname);

    if (anchorElement) {
      let top = anchorElement.offsetTop - window.innerHeight / 3;
      let isMobile = window.matchMedia(
        "only screen and (max-width: 600px)"
      ).matches;

      if (isMobile) {
        window.scrollTo({
          top,
          behavior: "smooth",
        });
      } else if (questionDom && questionDom.current) {
        questionDom.current.scrollTo({
          top,
          behavior: "smooth",
        });
      }
    }
  }
};

export function getScore(score) {
  return score ? Number(score).toFixed(2) : "0.00";
}

// 资源根目录路径
export const getBaseDir = async () => {
  return invoke("get_resource_dir", {});
};

// access wps 转换表述 下载文件 => 打开"Z:\id"文件夹中的文件xx
export function transformTrainQuestion(content) {
  if (!content || !content.length) {
    return;
  }

  for (const block of content) {
    if (!block) {
      continue;
    }

    const { questions } = block;
    if (!questions || !questions.length) {
      continue;
    }

    for (const question of questions) {
      const { id, questionType, questionDetail } = question;

      if (!questionDetail) {
        continue;
      }

      switch (questionType) {
        case "Access操作题":
        case "WPS表格操作题":
          {
            let { content: questionContent, instructions = [] } =
              questionDetail;
            const targetFileMatchResult = questionContent.match(
              /下载<a href=\"\.\.\/assets\/([^\"]+)\">/
            );

            questionDetail.content = questionContent.replace(
              /下载<a href=\"[^\"]+\">[^<]+<\/a>[，|,]/,
              ""
            );

            if (
              targetFileMatchResult &&
              targetFileMatchResult.length &&
              targetFileMatchResult[1]
            ) {
              const fileName = targetFileMatchResult[1];
              questionDetail.content = `打开“${id}”文件夹中的文件“${fileName}”，${questionDetail.content.replace(
                "并提交",
                ""
              )}`;
            }

            if (!instructions || !instructions.length) {
              continue;
            }

            let i = -1;
            for (const instruction of instructions) {
              i++;
              let { description } = instruction;
              if (!description) {
                continue;
              }

              const targetFileMatchResult = description.match(
                /下载<a href=\"\.\.\/assets\/([^\"]+)\">/
              );

              const replaceResult = description.replace(
                /下载<a href=\"[^\"]+\">[^<]+<\/a>[，|,]/,
                ""
              );

              if (
                targetFileMatchResult &&
                targetFileMatchResult.length &&
                targetFileMatchResult[1]
              ) {
                const fileName = targetFileMatchResult[1];
                // console.log(fileName, 'fileName')
                instructions[
                  i
                ].description = `打开“${id}”文件夹中的文件“${fileName}”，${replaceResult}`;
              }
            }
          }
          break;
        case "编程填空题":
          {
            // console.log(questionDetail, "questionDetail");
            questionDetail.content = `打开“${id}”文件夹中的文件“${questionDetail.extraCodePath}”，${questionDetail.content}`;
          }
          break;
        default:
          break;
      }
    }
  }
}

export function getPauseTime({
  pauseRecords,
  currentTime,
  studentStartTimeStamp,
}) {
  let pauseTime = 0;
  let studentPauseTime = 0;

  if (pauseRecords && pauseRecords.length) {
    for (const pauseRecord of pauseRecords) {
      const { start_time: startTime, end_time: endTime } = pauseRecord;
      const pauseStartTime = Date.parse(startTime);
      const pauseEndTime = endTime ? Date.parse(endTime) : currentTime;
      const duration = pauseEndTime - pauseStartTime;
      pauseTime += duration;

      // 如果是将学生开始答题时间作为开始时间，那么计算暂停时长时，需要从学生答题时间之后的时间开始算起
      if (pauseStartTime < studentStartTimeStamp) {
        continue;
      }

      studentPauseTime += duration;
    }
  }

  return { pauseTime, studentPauseTime };
}

export function getStudentItemScore({ content, studentAnswer }) {
  let scoreStat = [];

  for (const block of content) {
    let currentScore = 0;
    const { questions = [], name } = block;
    if (studentAnswer) {
      for (const question of questions) {
        const { id, questionType, questionDetail } = question;

        if (questionType === "综合题") {
          for (const smallQuestion of questionDetail) {
            const { UUID } = smallQuestion;
            if (
              studentAnswer[id] &&
              studentAnswer[id][UUID] &&
              studentAnswer[id][UUID].score
            ) {
              currentScore += Number(studentAnswer[id][UUID].score);
            }
          }
        } else {
          if (studentAnswer[id] && studentAnswer[id].score) {
            currentScore += Number(studentAnswer[id].score);
          }
        }
      }
    }

    scoreStat.push({ name, score: currentScore });
  }

  return scoreStat;
}
