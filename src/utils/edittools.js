import { notification } from 'antd';

export function alertError(message, description, placement = 'bottomRight') {
  notification.destroy();
  notification.error({
    message,
    description,
    placement
  });
}

// 显示成功信息
export function alertSuccess(message, description, placement = 'bottomRight') {
  notification.success({
    message,
    description,
    placement
  });
}

// 显示提示信息
export function alertInfo(message, description, placement = 'bottomRight') {
  notification.info({
    message,
    description,
    placement
  });
}

// 显示警告信息
export function alertWarning(message, description, placement = 'bottomRight') {
  notification.warning({
    message,
    description,
    placement
  });
}

// 休眠
export function sleep(time) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

let timerID = null;

export const cancelDebounce = () => {
  console.log("cancel debounce", timerID);
  if (timerID) {
    clearTimeout(timerID);
    timerID = null;
  }
};

export function debounce(action, delay) {
  let timer = null;
  return function () {
    const self = this;
    // eslint-disable-next-line
    let args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => { action.apply(self, args); }, delay);
  };
}

// 整理课程目录：考虑课程开放情况
export const formatCourseData = (directory, sectionData, isAdmin) => {
  let count = 0;

  const formDirectory = directory.map((chapter, chapterIndex) => {
    // 必做题节计数器
    let inCount = 0;
    const {
      chapterName, chapterTitle, sections, isOpen = true,
    } = chapter;

    // 内容目录考虑对于折叠的支持，如5.练习_1.服装搭配.xml，5.练习_2.计算线段长度.xml；折叠为 练习/练习_1.服装搭配.xml
    // 查询出存在下划线的节
    // 检查 sectionName 是否存在，并且是字符串
    const hasUnderlines = sections.filter((i) => i.sectionName && typeof i.sectionName === 'string' && i.sectionName.includes('_'));

    // 找出要折叠的节
    if (hasUnderlines && hasUnderlines.length) {
      for (const hasUnderline of hasUnderlines) {
        const foldArray = [];

        // 找出折叠名称
        const { sectionName } = hasUnderline;
        const [foldName] = sectionName.split('_');

        sections.forEach((section) => {
          const { sectionName: sn } = section;
          // 初始状态是未答题
          section.status = '未答题';
          const [name] = sn ? sn.split('_') : [null];
          if (foldName === name) {
            foldArray.push(section);
          }

          // 如果之前这个章没有答题记录
          if (!sectionData || !sectionData.length) {
            // 如果是必做题，记载count和inCount，如果没有必做题，两个都是0，一直到章节中有必做题，产生变化，在下一个章节的时候inCount赋值为0，两个不相等，后面的都会锁住
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }

          // 在答题记录中寻找当前节
          const sectionItem = sectionData.find((item) => item.chapterName === chapter.chapterName && item.sectionName === section.sectionName);
          // 如果当前节没有答
          if (!sectionItem || !sectionItem.sectionForeign || !sectionItem.sectionForeign.length) {
            section.status = '未答题';
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          // 如果有答题的话
          sectionItem.sectionForeign.forEach((foreignItem) => {
            const { record, totalScore, passCount } = foreignItem;
            if (!record) {
              section.status = '未答题';
              if (section.mustBeDone && !isAdmin) {
                count += 1;
                inCount += 1;
              }
              return;
            }
            if (totalScore !== passCount) {
              section.status = '未通过';
              if (section.mustBeDone && !isAdmin) {
                count += 1;
                inCount += 1;
              }
              return;
            }

            // 答对的时候不做任何处理
            section.status = '已通过';
          });
        });

        // 处理 sections
        if (foldArray && foldArray.length > 1) {
          foldArray.forEach((section, index) => {
            const sec = sections.findIndex((i) => i.sectionName === section.sectionName);
            if (index === 0 && sec > -1) {
              sections.splice(sec, 1, { foldArray, isOpen, foldName });
            } else if (sec > -1) {
              sections.splice(sec, 1);
            }
          });
        }
      }
    } else {
      // 逻辑参照上面分析
      sections.forEach((section) => {
        section.status = '未答题';
        if (!sectionData || !sectionData.length) {
          if (section.mustBeDone && !isAdmin) {
            count += 1;
            inCount += 1;
            return;
          }
        }
        const sectionItem = sectionData.find((item) => item.chapterName === chapter.chapterName && item.sectionName === section.sectionName);
        if (!sectionItem || !sectionItem.sectionForeign || !sectionItem.sectionForeign.length) {
          section.status = '未答题';
          if (section.mustBeDone && !isAdmin) {
            count += 1;
            inCount += 1;
          }
          return;
        }
        sectionItem.sectionForeign = sectionItem.sectionForeign.filter((item) => item.totalScore);
        if (!sectionItem.sectionForeign.length) {
          section.status = '未答题';
          if (section.mustBeDone && !isAdmin) {
            count += 1;
            inCount += 1;
          }
          return;
        }
        sectionItem.sectionForeign.forEach((foreignItem) => {
          const { record, totalScore, passCount } = foreignItem;
          if (!record) {
            section.status = '未答题';
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          if (totalScore !== passCount) {
            section.status = '未通过';
            if (section.mustBeDone && !isAdmin) {
              count += 1;
              inCount += 1;
            }
            return;
          }
          section.status = '已通过';
        });
      });
    }

    return {
      title: chapterTitle,
      key: chapterName,
      index: chapterIndex,
      nodeType: 'chapter',
      afterMustBeDone: count > 0 && inCount !== count,
      isOpen,
      children: sections.map((section, sectionIndex) => {
        const {
          sectionName: scn, sectionTitle, sectionType, foldArray: foldArr = [], foldName, mustBeDone, status,
        } = section;

        // 节 折叠的情况
        if (foldArr && foldArr.length) {
          return {
            title: foldName,
            key: `${chapterName}_${foldName}`,
            index: sectionIndex,
            mustBeDone,
            afterMustBeDone: count > 0 && inCount !== count,
            children: foldArr.map((sect, sectIndex) => {
              const { sectionName: sc, sectionTitle: st, sectionType: secType } = sect;
              return {
                title: st,
                sectionName: sc,
                chapterName,
                index: sectIndex,
                mustBeDone,
                key: `${chapterName}_${foldName}_${sc}`,
                nodeType: 'section',
                isOpen,
                sectionType: secType,
                status: sect.status,
              };
            }),
          };
        }
        return {
          title: sectionTitle,
          chapterName,
          sectionName: scn,
          index: sectionIndex,
          key: `${chapterName}_${scn}`,
          afterMustBeDone: count > 0 && inCount !== count,
          mustBeDone,
          nodeType: 'section',
          isOpen,
          sectionType,
          status,
        };
      }),
    };
  });

  return formDirectory;
};
