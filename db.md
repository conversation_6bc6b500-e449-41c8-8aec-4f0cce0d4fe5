# 数据字典

## card 卡片
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
cardLink|卡片链接|TEXT(32)|
cardUrl|卡片地址|TEXT(32)|
cardType|卡片类型|STRING(64)|
typeID|类型对应ID|INTEGER|
introduction|卡片简介|TEXT(32)|
frame|边框|STRING(64)|
cardCode|卡片编号|STRING(64)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## chat 聊天
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
senduser|发送者|STRING(32)|
receiveuser|接收者|STRING(32)|
sendtime|发送时间|STRING(32)|
receivetime|接收时间|STRING(32)|
message|发送消息内容|TEXT(long)|
messageState|消息发送状态|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## class 班级
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
name|班级名|STRING(128)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## class_user 用户班级关联
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|id|INTEGER|主键，自动增长
classID|班级ID|INTEGER|
userID|用户ID|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## course 课程
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
courseName|课程名称|STRING(64)|
picture|封面|TEXT(32)|
discription|简介|TEXT(32)|
number|用户数组|TEXT(long)|
draft|草稿|INTEGER|
discussDraft|开放讨论|INTEGER|
lectureCount|课程开放老师|TEXT(long)|
fixedLanguage|固定语言|TEXT(32)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## directory 章节
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
courseID|课程id|INTEGER|
chapterName|章名|STRING(64)|
cardID|卡片id|INTEGER|
indexNo|序号|INTEGER|
classCount|开放班级|TEXT(long)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## exam 考试
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
examName|考试名称|STRING(64)|
status|状态|STRING(64)|
gameType|赛制|STRING(64)|
startTime|开始时间|DECIMAL(13, 0)|
score|得分|INTEGER|
passArray|通过数据|TEXT(long)|
duration|考试时长|INTEGER|
submited|总提交|INTEGER|
waiting|待记录|INTEGER|
questionArray|已选题目|TEXT(long)|
examDiscription|考试说明|TEXT(long)|
cardID|卡片id|INTEGER|
examCord|考试码|STRING(64)|唯一索引
allowBreakIn|是否允许不报名中途加入考试|INTEGER|
adminRemark|管理员备注|TEXT(long)|
fixedLanguage|固定语言|TEXT(32)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## exam_user 用户考试关联
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|id|INTEGER|主键，自动增长
examID|考试id|INTEGER|
userID|用户id|INTEGER|
totalScore|总分|INTEGER|
submitTimes|提交次数|INTEGER|
spendTime|总耗时|DECIMAL(11, 0)|
questionRecord|题目记录|TEXT(long)|
rank|排名|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## forums 评论与题解
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
userID|用户id|INTEGER|
discussContent|评论内容|TEXT(long)|
forumType|所在类型(关卡|课程)|STRING(16)|
typeID|类型对应ID|INTEGER|
replyArray|回复内容(数组)|TEXT(long)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## level 关卡
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
level|级别|STRING(64)|
orderIndex|排序的ID|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## mission 任务
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
levelID|级别ID|INTEGER|
title|标题|STRING(64)|
startStandard|开启标准|STRING(64)|
startPlan|开启进度|INTEGER|
link|课程链接|STRING(64)|
taskDiscription|任务说明|TEXT(32)|
questionArray|已选题目|TEXT(long)|
pass|通过数目|DECIMAL(10, 0)|
submit|提交数目|DECIMAL(10, 0)|
cardID|卡片id|INTEGER|
adminRemark|管理员备注|TEXT(32)|
orderIndex|排序的ID|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## questions 题库
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
serial|题号|STRING(64)|
title|标题|STRING(128)|
submit|提交次数|DECIMAL(10, 0)|
success|通过次数|DECIMAL(10, 0)|
memoryLimit|内存限制|DECIMAL(10, 0)|
timeLimit|时间限制|DECIMAL(10, 0)|
content|内容|TEXT(long)|
inOutExample|输入输出样例|TEXT(long)|
questionType|模式|STRING(32)|
studentLabel|学生标签|TEXT(long)|
sampleCount|数据统计|INTEGER|
inFormat|输入格式|TEXT(long)|
outFormat|输出格式|TEXT(long)|
dataRangePrompts|数据范围提示|TEXT(long)|
adminRemark|管理员备注|TEXT(long)|
hidden|隐藏题|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## question_tag 题目标签
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|id|INTEGER|主键，自动增长
questionID|题目ID|INTEGER|
tagID|标签ID|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## section 节
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
directoryID|章id|INTEGER|
sectionName|节名|STRING(64)|
type|类型|STRING(64)|
content|课程内容|TEXT(long)|
recordData|录制内容|TEXT(long)|
recordUser|录制人|STRING(64)|
indexNo|序号|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## solution 操作记录
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
questionID|题号|INTEGER|
userID|用户id|INTEGER|
solutionType|做题类别|STRING(64)|
typeID|类型对应ID|INTEGER|
score|分数|DECIMAL(10, 2)|
usedTime|执行时间|INTEGER|
ram|内存|INTEGER|
record|记录|TEXT(long)|
codeRecord|代码记录|TEXT(long)|
codeLanguage|代码语言|STRING(64)|
rank|排名|INTEGER|
punishmentTime|总用时|DECIMAL(11, 0)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## system_config 
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
key|自定义配置名称|STRING(128)|唯一索引
value|自定义配置内容|TEXT(long)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## systemlogs 题目修改日志
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|id|INTEGER|主键，自动增长
type|类型|STRING(16)|
typeID|类型ID|INTEGER|
value|日志内容||
userID|修改人|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## tag 标签
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
tagName|标签名|STRING(128)|
tagType|标签类型|STRING(64)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## users 用户
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
username|用户名|STRING(32)|
password|密码|STRING(64)|
isAdmin|是否管理员|INTEGER|
nickName|昵称|STRING(64)|
email|电子邮箱|STRING(128)|
avatar|头像|TEXT(32)|
state|状态|STRING(32)|
submit|提交|DECIMAL(10, 0)|
check|正确|DECIMAL(10, 0)|
formatError|格式错误|DECIMAL(10, 0)|
wrongAnswer|答案错误|DECIMAL(10, 0)|
timeOut|时间超限|DECIMAL(10, 0)|
intOut|输出超限|DECIMAL(10, 0)|
runError|运行错误|DECIMAL(10, 0)|
compileError|编译错误|DECIMAL(10, 0)|
timeRecord|时间统计记录|TEXT(long)|
coursePlan|课程进度|TEXT(long)|
examRecord|考试记录|TEXT(long)|
levelPlan|关卡进度|TEXT(long)|
levelRecord|全部关卡使用记录|TEXT(long)|
authority|权限管理|TEXT(32)|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|

## user_card 用户卡片
字段名 | 含义 | 数据类型 | 备注
---|---|---|---
id|主键|INTEGER|主键，自动增长
userID|用户id|INTEGER|
cardID|卡片id|INTEGER|
created_at|创建时间|DATETIME|
updated_at|最后更新时间|DATETIME|
deleted_at|删除时间|DATETIME|