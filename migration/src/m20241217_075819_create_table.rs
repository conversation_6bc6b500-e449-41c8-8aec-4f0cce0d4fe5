use std::ptr::null;

use sea_orm::Statement;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 建立用户表
        manager
            .create_table(
                Table::create()
                    .table(User::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(User::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(User::Username).string().not_null())
                    .col(ColumnDef::new(User::Password).string().not_null())
                    .col(ColumnDef::new(User::Name).string().not_null())
                    .col(ColumnDef::new(User::Sen).string())
                    .col(ColumnDef::new(User::Avatar).string())
                    .col(ColumnDef::new(User::State).string())
                    .col(ColumnDef::new(User::AdminAuthority).json())
                    .col(ColumnDef::new(User::School).string())
                    .col(ColumnDef::new(User::LastActiveTime).date_time())
                    .col(ColumnDef::new(User::LastActiveIp).string())
                    .col(ColumnDef::new(User::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(User::UpdatedAt).date_time().not_null())
                    .col(ColumnDef::new(User::DeletedAt).date_time())
                    .to_owned(),
            )
            .await?;
        // 建立课程表
        manager
            .create_table(
                Table::create()
                    .table(Course::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Course::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key()
                            .unique_key(),
                    )
                    .col(ColumnDef::new(Course::CourseName).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::CourseSlug).string().not_null().unique_key())
                    .col(ColumnDef::new(Course::CourseDescription).text().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::Publish).integer().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::Statist).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::Indics).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::ContainerInfo).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::Creater).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::CourseType).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::SaveCode).integer().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::SaveRunResult).integer().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::AllowPaste).integer().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::AllowCopy).integer().default(1))
                    .col(ColumnDef::new(Course::QuestionAnswer).integer().default(0))
                    .col(ColumnDef::new(Course::ProgramLanguage).string().default("Python"))
                    .col(ColumnDef::new(Course::Teams).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::HistoryTeams).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::Teachers).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Course::UploadCount).integer().default(0))
                    .col(ColumnDef::new(Course::DownloadCount).integer().default(0))
                    .col(ColumnDef::new(Course::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(Course::UpdatedAt).date_time())
                    .col(ColumnDef::new(Course::DeletedAt).date_time())
                    .to_owned(),
            )
            .await?;
        // 建立章节表
        manager
            .create_table(
                Table::create()
                    .table(Section::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Section::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key()
                            .unique_key(),
                    )
                    .col(ColumnDef::new(Section::CourseId).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::ChapterName).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::SectionName).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::SectionType).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::Ext).string().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::Record).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::HistoryRecords).json().default(Value::Json(None)))
                    .col(ColumnDef::new(Section::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(Section::UpdatedAt).date_time())
                    .col(ColumnDef::new(Section::DeletedAt).date_time().default(Value::Json(None)))
                    .to_owned(),
            )
            .await?;
        // 建立章节记录表
        manager
            .create_table(
                Table::create()
                    .table(SectionRecord::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(SectionRecord::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(SectionRecord::UserId).integer().default(Value::Json(None)))
                    .col(ColumnDef::new(SectionRecord::SectionId).integer().default(Value::Json(None)))
                    .col(ColumnDef::new(SectionRecord::StayTime).string().default(Value::Json(None)))
                    .col(ColumnDef::new(SectionRecord::Record).string().default(Value::Json(None)))
                    .col(ColumnDef::new(SectionRecord::TotalScore).integer().default(0))
                    .col(ColumnDef::new(SectionRecord::PassCount).integer().default(0))
                    .col(ColumnDef::new(SectionRecord::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(SectionRecord::UpdatedAt).date_time())
                    .col(ColumnDef::new(SectionRecord::DeletedAt).date_time().default(Value::Json(None)))
                    .to_owned(),
            )
            .await?;
        // 建立班级表
        manager
            .create_table(
                Table::create()
                    .table(Team::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Team::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Team::Name).string().not_null())
                    .col(ColumnDef::new(Team::Year).string().not_null())
                    .col(ColumnDef::new(Team::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(Team::UpdatedAt).date_time().not_null())
                    .col(ColumnDef::new(Team::DeletedAt).date_time())
                    .to_owned(),
            )
            .await?;

        // 建立用户班级关联表
        manager
            .create_table(
                Table::create()
                    .table(TeamUser::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(TeamUser::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(TeamUser::TeamID).integer().not_null())
                    .col(ColumnDef::new(TeamUser::UserID).integer().not_null())
                    .col(ColumnDef::new(TeamUser::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(TeamUser::UpdatedAt).date_time().not_null())
                    .col(ColumnDef::new(TeamUser::DeletedAt).date_time())
                    .to_owned(),
            )
            .await?;

            // 建立系统配置表
            manager
                .create_table(
                    Table::create()
                        .table(SystemConfig::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(SystemConfig::Id)
                                .integer()
                                .not_null()
                                .auto_increment()
                                .primary_key(),
                        )
                        .col(ColumnDef::new(SystemConfig::Key).string().not_null())
                        .col(ColumnDef::new(SystemConfig::Value).string())
                        .col(ColumnDef::new(SystemConfig::CreatedAt).date_time().not_null())
                        .col(ColumnDef::new(SystemConfig::UpdatedAt).date_time().not_null())
                        .col(ColumnDef::new(SystemConfig::DeletedAt).date_time())
                        .to_owned(),
                )
                .await?;
            
            manager
            .create_table(
                Table::create()
                    .table(Terminal::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(Terminal::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(Terminal::IP).string().not_null())
                    .col(ColumnDef::new(Terminal::UserID).integer())
                    .col(ColumnDef::new(Terminal::TrainID).integer())
                    .col(ColumnDef::new(Terminal::Status).string())
                    .col(ColumnDef::new(Terminal::CreatedAt).date_time().not_null())
                    .col(ColumnDef::new(Terminal::UpdatedAt).date_time().not_null())
                    .col(ColumnDef::new(Terminal::DeletedAt).date_time())
                    .to_owned(),
            )
            .await?;
            
            // 检索system_config表的site_url字段的值中的hxr.jsnje.cn替换为hxr.iyopu.com
            let db = manager.get_connection();

            let sql_stmt = format!(
                r#"
                INSERT INTO system_config (key, value, created_at, updated_at)
                VALUES 
                    ('site_url', 'demo', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);"#,
            );
            // let sql_stmt = format!(
            //     r#"
            //     INSERT INTO system_config (key, value, created_at, updated_at)
            //     VALUES
            //         ('default_username', 'datouxia', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            //         ('default_password', '2fdb3be15236116c02966b50d4a3277a9fc60d7f', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            //         ('school_name', '试用', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            //         ('site_url', 'demo', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);"#,
            // );
            let stmt = Statement::from_sql_and_values(
                manager.get_database_backend(),
                &sql_stmt,
                []
            );

            db.execute(stmt).await?;

            Ok(()) // Wrap the unit type in the Ok variant of the Result enum

    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 删除用户表
        manager
            .drop_table(Table::drop().table(User::Table).to_owned())
            .await?;
        // 建立课程表
        manager
        .drop_table(Table::drop().table(Course::Table).to_owned())
        .await?;
        // 建立章节表
        manager
        .drop_table(Table::drop().table(Section::Table).to_owned())
        .await?;
        // 建立章节记录表
        manager
        .drop_table(Table::drop().table(SectionRecord::Table).to_owned())
        .await?;
        // 删除班级表
        manager
            .drop_table(Table::drop().table(Team::Table).to_owned())
            .await?;

        // 删除用户班级关联表
        manager
            .drop_table(Table::drop().table(TeamUser::Table).to_owned())
            .await?;

        // 删除系统配置
        manager
            .drop_table(Table::drop().table(SystemConfig::Table).to_owned())
            .await?;

        manager
            .drop_table(Table::drop().table(Terminal::Table).to_owned())
            .await
    }
}

/// Learn more at https://docs.rs/sea-query#iden
#[derive(Iden)]
enum User {
    Table,
    Id,
    Username,
    Password,
    Name,
    Sen,
    Avatar,
    State,
    AdminAuthority,
    School,
    LastActiveTime,
    LastActiveIp,
    CreatedAt,
    UpdatedAt,
    DeletedAt,
}

#[derive(Iden)]
enum Course {
    Table,
    Id,
    CourseName,
    CourseSlug ,
    CourseDescription,
    Publish,
    Statist,
    Indics,
    ContainerInfo,
    Creater,
    CourseType,
    SaveCode,
    SaveRunResult,
    AllowPaste,
    AllowCopy,
    QuestionAnswer,
    ProgramLanguage,
    Teams, 
    HistoryTeams,
    Teachers,
    UploadCount,
    DownloadCount,
    CreatedAt,
    UpdatedAt,
    DeletedAt,
}

#[derive(Iden)]
enum SectionRecord {
    Table,
    Id,
    UserId ,
    SectionId ,
    StayTime,
    Record,
    TotalScore,
    PassCount,
    CreatedAt,
    UpdatedAt,
    DeletedAt
}

#[derive(Iden)]
enum Section {
    Table,
    Id,
    CourseId,
    ChapterName,
    SectionName,
    SectionType,
    Ext,
    Record,
    HistoryRecords,
    CreatedAt,
    UpdatedAt,
    DeletedAt,

}

#[derive(Iden)]
enum Team {
    Table,
    Id,
    Name,
    Year,
    CreatedAt,
    UpdatedAt,
    DeletedAt,
}

#[derive(Iden)]
enum TeamUser {
    Table,
    Id,
    TeamID,
    UserID,
    CreatedAt,
    UpdatedAt,
    DeletedAt,
}

#[derive(Iden)]
enum SystemConfig {
    Table,
    Id,
    Key,
    Value,
    CreatedAt,
    UpdatedAt,
    DeletedAt,
}

#[derive(Iden)]
enum Terminal {
    Table,
    Id,
    IP,
    UserID,
    TrainID,
    Status,
    CreatedAt,
    UpdatedAt,
    DeletedAt,
}