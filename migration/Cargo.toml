[package]
name = "migration"
version = "0.1.0"
edition = "2021"
publish = false

[lib]
name = "migration"
path = "src/lib.rs"

[dependencies]
async-std = { version = "1", features = ["attributes", "tokio1"] }
chrono = { version = "0.4", features = ["serde"] }
service = { path = "../service" }
regex = "1.8.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.64"
zip = "2.2.0"

[dependencies.sea-orm-migration]
version = "1.0.1" # sea-orm-migration version
features = [
  # Enable following runtime and db backend features if you want to run migration via CLI
  # "runtime-actix-native-tls",
  # "sqlx-mysql",
]
