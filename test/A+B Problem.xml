<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/css" href="book.css"?>
<fps version="1.2" url="https://github.com/zhblue/freeproblemset/">
<generator name="NFLSOJ" url="http://www.nfls.com.cn:10443/"/>
<item>
<title><![CDATA[A+B Problem]]></title>
<serial><![CDATA[1002]]></serial>
<time_limit unit="s"><![CDATA[1.00]]></time_limit>
<memory_limit unit="mb"><![CDATA[256]]></memory_limit>
<time_limit unit="s" language="Python"><![CDATA[2.00]]></time_limit>
<memory_limit unit="mb" language="Python"><![CDATA[512]]></memory_limit>
<description><![CDATA[<p>输入两个数字，计算两个数字的和， a+b问题。</p>]]></description>
<input><![CDATA[<p>一行，两个整数 a,b ，用空格隔开，保证0&lt;=a,b&lt;=10^9。</p>]]></input>
<output><![CDATA[<p>一行，一个数字，即a+b的和</p>]]></output>
<question_type><![CDATA[编程题]]></question_type>
<sample_input><![CDATA[1 2]]></sample_input>
<sample_output><![CDATA[3]]></sample_output>
<test_input hide="C++" src="/data/shared_files/2020/09/28/sdjfidasjfiojdsfi.in" />
<test_output src="/data/shared_files/2020/09/28/sdjfidasjfiojdsfi.out" />
<test_input src="/data/shared_files/2020/09/28/sdjfidasjfiojdsfi2.in" />
<test_output src="/data/shared_files/2020/09/28/sdjfidasjfiojdsfi2.out" />
<spj language="C++" src="/data/shared_files/2020/09/28/sdjfidasjfiojdsfi2.c" />
<hint><![CDATA[<p>(0&lt;=a,b&lt;=10^9)</p>]]></hint>
<source><![CDATA[初级组-入门题]]></source>
</item>
</fps>