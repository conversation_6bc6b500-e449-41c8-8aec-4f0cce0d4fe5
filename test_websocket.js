const WebSocket = require('ws');

// 测试WebSocket连接到端口46046
const ws = new WebSocket('ws://localhost:46046');

ws.on('open', function open() {
    console.log('WebSocket连接已建立');
});

ws.on('message', function message(data) {
    console.log('收到消息:', data.toString());
});

ws.on('error', function error(err) {
    console.log('WebSocket错误:', err.message);
});

ws.on('close', function close() {
    console.log('WebSocket连接已关闭');
});

// 保持连接5秒钟
setTimeout(() => {
    ws.close();
}, 5000);
